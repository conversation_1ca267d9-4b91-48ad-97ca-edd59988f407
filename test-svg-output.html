<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG 中文字体测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimHei', 'SimSun', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .url-input {
            width: 100%;
            padding: 10px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .svg-container {
            border: 2px solid #007bff;
            padding: 10px;
            margin: 10px 0;
            background: white;
            text-align: center;
        }
        .font-test {
            font-size: 18px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            background: #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SVG 中文字体显示测试工具</h1>

        <div class="test-section">
            <h2>1. 系统字体测试</h2>
            <p>以下文本应该正确显示中文字符：</p>
            <div class="font-test" style="font-family: 'Microsoft YaHei';">
                Microsoft YaHei: 2025年1-4月首次稽核违规率趋势
            </div>
            <div class="font-test" style="font-family: 'PingFang SC';">
                PingFang SC: 2025年1-4月首次稽核违规率趋势
            </div>
            <div class="font-test" style="font-family: 'SimHei';">
                SimHei: 2025年1-4月首次稽核违规率趋势
            </div>
            <div class="font-test" style="font-family: sans-serif;">
                sans-serif: 2025年1-4月首次稽核违规率趋势
            </div>
        </div>

        <div class="test-section">
            <h2>2. SVG API 测试</h2>
            <p>输入您的 SVG API URL：</p>
            <input type="text" class="url-input" id="apiUrl"
                   placeholder="http://localhost:39049/graph-rest-api/api/chart/generate?data=...&format=svg"
                   value="http://localhost:39049/graph-rest-api/api/chart/generate?data=eyJ0eXBlIjoibGluZSIsInRpdGxlIjoiMjAyNeW5tDEtNOaciOmmluasoeeoveaguOi/neinhOeOh+i2i+WKvyIsInhBeGlzTGFiZWwiOiLmnIjku70iLCJ5QXhpc0xhYmVsIjoi6L+d6KeE546HKCUpIiwid2lkdGgiOjgwMCwiaGVpZ2h0Ijo1MDAsIm9wdGlvbnMiOnsiZHBpIjoxNTB9LCJzZXJpZXMiOlt7Im5hbWUiOiLov53op4TnjociLCJkYXRhIjpbMzMuNjEsNjIuMDQsMTkuMTMsMzIuMDhdLCJsYWJlbHMiOlsiMjAyNS0wMSIsIjIwMjUtMDIiLCIyMDI1LTAzIiwiMjAyNS0wNCJdLCJjb2xvciI6IiNGRjZCNkIifV19&format=svg">
            <br>
            <button class="btn" onclick="loadSVG()">加载 SVG</button>
            <button class="btn" onclick="testPNG()">测试 PNG 格式</button>
            <button class="btn" onclick="testTransparent()">测试透明背景</button>
            <button class="btn" onclick="testWhiteBackground()">测试白色背景</button>

            <div id="result" class="result" style="display: none;">
                <h3>SVG 内容：</h3>
                <div id="svgContainer" class="svg-container"></div>

                <h3>调试信息：</h3>
                <div id="debugInfo"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>3. 手动 SVG 测试</h2>
            <p>这是一个包含中文的测试 SVG：</p>
            <div class="svg-container">
                <svg width="400" height="200" xmlns="http://www.w3.org/2000/svg">
                    <style type="text/css"><![CDATA[
                        text {
                            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimHei', 'SimSun', sans-serif !important;
                        }
                    ]]></style>
                    <rect width="400" height="200" fill="#f0f0f0" stroke="#ccc"/>
                    <text x="200" y="100" text-anchor="middle" font-size="16" fill="black">
                        2025年1-4月首次稽核违规率趋势
                    </text>
                    <text x="200" y="130" text-anchor="middle" font-size="14" fill="blue">
                        月份 - 违规率(%)
                    </text>
                </svg>
            </div>
        </div>
    </div>

    <script>
        function loadSVG() {
            const url = document.getElementById('apiUrl').value;
            const resultDiv = document.getElementById('result');
            const svgContainer = document.getElementById('svgContainer');
            const debugInfo = document.getElementById('debugInfo');

            if (!url) {
                alert('请输入 API URL');
                return;
            }

            resultDiv.style.display = 'block';
            svgContainer.innerHTML = '加载中...';
            debugInfo.innerHTML = '加载中...';

            fetch(url)
                .then(response => {
                    const contentType = response.headers.get('content-type');
                    debugInfo.innerHTML = `
                        <strong>HTTP 状态:</strong> ${response.status}<br>
                        <strong>Content-Type:</strong> ${contentType}<br>
                        <strong>响应大小:</strong> ${response.headers.get('content-length') || '未知'} 字节
                    `;
                    return response.text();
                })
                .then(svgContent => {
                    svgContainer.innerHTML = svgContent;

                    const hasChineseChars = /[\u4e00-\u9fff]/.test(svgContent);
                    const hasFontFamily = svgContent.includes('font-family');
                    const hasStyle = svgContent.includes('<style');
                    const hasEncoding = svgContent.includes('UTF-8');

                    debugInfo.innerHTML += `<br>
                        <strong>包含中文字符:</strong> ${hasChineseChars ? '是' : '否'}<br>
                        <strong>包含字体设置:</strong> ${hasFontFamily ? '是' : '否'}<br>
                        <strong>包含样式标签:</strong> ${hasStyle ? '是' : '否'}<br>
                        <strong>包含UTF-8编码:</strong> ${hasEncoding ? '是' : '否'}<br>
                        <strong>内容长度:</strong> ${svgContent.length} 字符
                    `;
                })
                .catch(error => {
                    svgContainer.innerHTML = '加载失败: ' + error.message;
                    debugInfo.innerHTML = '错误: ' + error.message;
                });
        }

        function testPNG() {
            const url = document.getElementById('apiUrl').value.replace('format=svg', 'format=png');
            window.open(url, '_blank');
        }

        function testTransparent() {
            // 创建包含透明背景的测试数据
            const testData = {
                "type": "bar",
                "title": "透明背景测试图表",
                "xAxisLabel": "类别",
                "yAxisLabel": "数值",
                "width": 800,
                "height": 600,
                "options": {
                    "backgroundColor": "transparent"
                },
                "series": [{
                    "name": "测试数据",
                    "data": [10, 20, 30, 40, 50],
                    "labels": ["A", "B", "C", "D", "E"],
                    "color": "#FF4500"
                }]
            };

            const base64Data = btoa(JSON.stringify(testData));
            const baseUrl = document.getElementById('apiUrl').value.split('?')[0];
            const testUrl = `${baseUrl}?data=${base64Data}&format=svg`;

            document.getElementById('apiUrl').value = testUrl;
            loadSVG();
        }

        function testWhiteBackground() {
            // 创建包含白色背景的测试数据
            const testData = {
                "type": "line",
                "title": "白色背景测试图表",
                "xAxisLabel": "时间",
                "yAxisLabel": "数值",
                "width": 800,
                "height": 600,
                "options": {
                    "backgroundColor": "white"
                },
                "series": [{
                    "name": "测试数据",
                    "data": [15, 25, 35, 45, 55],
                    "labels": ["1月", "2月", "3月", "4月", "5月"],
                    "color": "#4169E1"
                }]
            };

            const base64Data = btoa(JSON.stringify(testData));
            const baseUrl = document.getElementById('apiUrl').value.split('?')[0];
            const testUrl = `${baseUrl}?data=${base64Data}&format=svg`;

            document.getElementById('apiUrl').value = testUrl;
            loadSVG();
        }
    </script>
</body>
</html>
