# SVG 图表背景颜色设置指南

## 功能概述

现在 ChartController 支持设置图表背景颜色，包括：
- **白色背景**（默认）
- **透明背景**

## 使用方法

### 1. 透明背景

在图表数据的 `options` 中设置 `backgroundColor` 为 `"transparent"` 或 `"none"`：

```json
{
  "type": "bar",
  "title": "透明背景图表",
  "xAxisLabel": "类别",
  "yAxisLabel": "数值",
  "width": 800,
  "height": 600,
  "options": {
    "backgroundColor": "transparent"
  },
  "series": [
    {
      "name": "数据系列",
      "data": [10, 20, 30, 40],
      "labels": ["A", "B", "C", "D"],
      "color": "#FF4500"
    }
  ]
}
```

### 2. 白色背景

在图表数据的 `options` 中设置 `backgroundColor` 为 `"white"`（或不设置，默认为白色）：

```json
{
  "type": "line",
  "title": "白色背景图表",
  "xAxisLabel": "时间",
  "yAxisLabel": "数值",
  "width": 800,
  "height": 600,
  "options": {
    "backgroundColor": "white"
  },
  "series": [
    {
      "name": "数据系列",
      "data": [15, 25, 35, 45],
      "labels": ["1月", "2月", "3月", "4月"],
      "color": "#4169E1"
    }
  ]
}
```

## API 调用示例

### 透明背景 SVG
```bash
# Base64 编码包含 backgroundColor: "transparent" 的数据
curl "http://localhost:39049/graph-rest-api/api/chart/generate?data={base64_data}&format=svg"
```

### 白色背景 SVG
```bash
# Base64 编码包含 backgroundColor: "white" 的数据（或不设置）
curl "http://localhost:39049/graph-rest-api/api/chart/generate?data={base64_data}&format=svg"
```

## 实际效果

### 透明背景
- ✅ 移除所有白色背景填充
- ✅ 图表可以与任何背景色融合
- ✅ 适合嵌入到有色背景的网页中
- ✅ SVG 中不包含 `fill="white"` 等白色填充属性

### 白色背景
- ✅ 保持传统的白色背景
- ✅ 适合打印和文档使用
- ✅ 提供清晰的对比度

## 技术实现

### 1. Java 层面
- `setChartBackground()` 方法根据配置设置 JFreeChart 背景
- 支持 CategoryPlot、XYPlot、PiePlot 等不同图表类型

### 2. SVG 后处理
- `processSVGBackground()` 方法处理 SVG 内容
- 透明背景：移除白色填充属性，设置 `fill="none"`
- 白色背景：确保白色填充属性存在

### 3. 配置选项
```java
// 检查背景颜色设置
String backgroundColor = "white"; // 默认白色
if (chartData.getOptions() != null && chartData.getOptions().containsKey("backgroundColor")) {
    backgroundColor = chartData.getOptions().get("backgroundColor").toString().toLowerCase();
}
```

## 测试验证

已通过以下测试：
- ✅ `testTransparentBackgroundSVG()` - 透明背景测试
- ✅ `testWhiteBackgroundSVG()` - 白色背景测试
- ✅ 验证 SVG 内容不包含白色填充（透明模式）
- ✅ 验证 SVG 生成成功且格式正确

## 兼容性

- ✅ **向后兼容**：不设置 backgroundColor 时默认为白色背景
- ✅ **格式支持**：同时支持 SVG 和位图格式（PNG、JPG、GIF）
- ✅ **浏览器支持**：所有现代浏览器都支持透明 SVG

## 使用建议

### 透明背景适用场景
- 网页嵌入（与页面背景融合）
- 深色主题界面
- 需要自定义背景的场景
- 移动应用界面

### 白色背景适用场景
- 文档和报告
- 打印输出
- 传统的图表展示
- 需要高对比度的场景

现在您可以通过设置 `backgroundColor` 选项来控制图表的背景颜色了！
