# IDCAuditTools 新增方法说明

## 功能概述

为 IDCAuditTools 新增了 `generateAuditQueryJson` 方法，该方法接收多个参数（设备IP、端口、人员姓名等），生成标准化的JSON报文格式（只包含英文字段名）。

## 方法签名

```java
@MCPTool(description = "根据输入参数生成审计查询JSON报文")
public String generateAuditQueryJson(String deviceIp, String portName, String userName,
                                   String startDate, String endDate, String regionNames)
```

## 参数说明

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| deviceIp | String | 设备IP地址（可选） | "*************" |
| portName | String | 端口名称（可选） | "GE0/0/1" |
| userName | String | 用户名称（可选） | "张三" |
| startDate | String | 开始时间（可选） | "2025-05-06" 或 "2025-05-06 00:00:00" |
| endDate | String | 结束时间（可选） | "2025-06-06" 或 "2025-06-06 23:59:59" |
| regionNames | String | 地市名称，多个用逗号分隔（可选） | "南京,苏州,无锡" |

## 返回格式

方法返回标准化的JSON字符串，只包含英文字段名：

```json
{
  "deviceIp": "*************",
  "portName": "GE0/0/1",
  "userName": "张三",
  "startDate": "2025-05-06 00:00:00",
  "endDate": "2025-06-06 00:00:00",
  "regionNames": ["南京", "苏州", "无锡"],
  "code": "0"
}
```

## 使用示例

### 1. 完整参数调用

```java
IDCAuditTools tools = new IDCAuditTools();
String result = tools.generateAuditQueryJson(
    "*************",     // 设备IP
    "GE0/0/1",           // 端口名称
    "张三",               // 用户名称
    "2025-05-06",        // 开始时间
    "2025-06-06",        // 结束时间
    "南京,苏州,无锡"      // 地市列表
);
```

### 2. 部分参数调用

```java
String result = tools.generateAuditQueryJson(
    "********",          // 只提供设备IP
    null,                // 端口为空
    "李四",               // 用户名称
    null,                // 使用默认开始时间
    null,                // 使用默认结束时间
    "徐州"               // 单个地市
);
```

### 3. 空参数调用

```java
String result = tools.generateAuditQueryJson("", "", "", "", "", "");
// 返回包含默认值的JSON
```

## 特殊处理逻辑

### 1. 时间格式处理

支持多种时间格式输入：
- `"2025-05-06"` → `"2025-05-06 00:00:00"`
- `"2025-05-06 10:30:00"` → `"2025-05-06 10:30:00"`
- `"2025年5月6日"` → `"2025-05-06 00:00:00"`

如果未提供时间参数，使用默认时间范围（最近一个月）。

### 2. 地市名称处理

支持多种分隔符：
- `"南京,苏州,无锡"` （逗号分隔）
- `"南京，苏州，无锡"` （中文逗号）
- `"南京;苏州;无锡"` （分号分隔）
- `"南京 苏州 无锡"` （空格分隔）
- `"南京市,苏州市,无锡市"` （自动识别地市名称）

支持的地市列表：
```
南京、徐州、无锡、连云港、宿迁、苏州、常州、镇江、盐城、淮安、扬州、泰州、南通
```

如果未提供地市参数，返回所有支持的地市列表。

### 3. 空值处理

- `null` 参数会被转换为空字符串 `""`
- 空字符串参数保持为空字符串
- 无效的时间格式会使用当前时间
- 无效的地市名称会被忽略

## 测试验证

已通过以下测试用例：

1. **完整参数测试** - 验证所有参数正确处理
2. **部分参数测试** - 验证默认值生成
3. **空参数测试** - 验证空值处理
4. **null参数测试** - 验证null值处理
5. **时间格式测试** - 验证多种时间格式转换
6. **地市格式测试** - 验证多种分隔符支持
7. **格式匹配测试** - 验证JSON结构完整性

## 日志输出

方法执行时会输出详细的日志信息：

```
INFO : Generating audit query JSON with parameters: deviceIp=*************, portName=GE0/0/1, userName=张三, startDate=2025-05-06, endDate=2025-06-06, regionNames=南京,苏州,无锡
INFO : Successfully generated audit query JSON: {"deviceIp":"*************",...}
```

## 错误处理

如果在处理过程中发生异常，方法会：
1. 记录错误日志
2. 返回包含默认值的JSON报文
3. 确保返回的JSON格式始终有效

## 应用场景

该方法适用于：
- 审计查询参数标准化
- 多系统间数据格式统一
- API接口参数转换
- 报表查询条件生成
- 标准化英文字段名输出

通过这个方法，可以将分散的查询参数统一转换为标准的JSON格式（纯英文字段），便于后续的数据处理和系统对接。
