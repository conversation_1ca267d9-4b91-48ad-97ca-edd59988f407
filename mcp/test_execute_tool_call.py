import asyncio
import json
import os
from dotenv import load_dotenv
import sys
import importlib.util
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 加载环境变量
load_dotenv()

# 创建stdio服务器参数
server_params = StdioServerParameters(
    command="python",  # 可执行文件
    args=["mcp/mcp_idc_sql_server_debug.py"],  # 命令行参数
    env=os.environ  # 传递当前环境变量
)

async def execute_tool_call(tool_call: dict, session) -> str:
    """
    执行工具调用
    
    Args:
        tool_call: 工具调用参数，包含name和input
        session: MCP会话
        
    Returns:
        工具执行结果
    """
    if "error" in tool_call:
        return tool_call["error"]
    
    if not tool_call.get('input'):
        tool_call["input"] = {}
    
    if not all(k in tool_call for k in ("name", "input")):
        return "工具调用缺少必要字段'name'或'input'"
    
    try:
        print(f"调用工具: {tool_call['name']}")
        print(f"参数: {json.dumps(tool_call['input'], ensure_ascii=False)}")
        
        result = await session.call_tool(
            name=tool_call["name"],
            arguments=tool_call["input"]
        )
        
        return str(result)
    except Exception as e:
        import traceback
        print(f"工具执行失败: {str(e)}")
        traceback.print_exc()
        return f"工具执行失败: {str(e)}"

async def format_and_print_results(results_str):
    """格式化并打印查询结果"""
    print("\n查询结果:")
    print("-" * 50)
    
    try:
        # 尝试解析结果字符串为JSON
        results = json.loads(results_str)
        
        if not results:
            print("无结果")
            return
        
        if isinstance(results, list) and len(results) > 0 and isinstance(results[0], dict):
            if "error" in results[0]:
                print(f"错误: {results[0]['error']}")
                return
            
            # 尝试以表格形式打印结果
            try:
                # 获取所有列名
                columns = list(results[0].keys())
                
                # 计算每列的最大宽度
                col_widths = {}
                for col in columns:
                    col_widths[col] = max(len(str(col)), max(len(str(row.get(col, ""))) for row in results))
                
                # 打印表头
                header = " | ".join(f"{col:{col_widths[col]}}" for col in columns)
                print(header)
                print("-" * len(header))
                
                # 打印数据行
                for row in results:
                    row_str = " | ".join(f"{str(row.get(col, '')):{col_widths[col]}}" for col in columns)
                    print(row_str)
                
                print(f"\n共 {len(results)} 条记录")
            except Exception as e:
                # 如果表格格式化失败，直接打印JSON
                print(f"表格格式化失败: {str(e)}")
                print(json.dumps(results, ensure_ascii=False, indent=2))
        else:
            # 如果不是预期的格式，直接打印
            print(json.dumps(results, ensure_ascii=False, indent=2))
    except json.JSONDecodeError:
        # 如果不是JSON格式，直接打印原始字符串
        print(results_str)
    
    print("-" * 50)

async def test_sql_query(session, sql):
    """测试SQL查询工具调用"""
    tool_call = {
        "name": "sql_query",
        "input": {
            "sql": sql
        }
    }
    
    result = await execute_tool_call(tool_call, session)
    return result

async def test_get_table_schema(session, table_name):
    """测试获取表结构工具调用"""
    tool_call = {
        "name": "get_table_schema",
        "input": {
            "table_name": table_name
        }
    }
    
    result = await execute_tool_call(tool_call, session)
    return result

async def main():
    """主函数，测试execute_tool_call函数"""
    print("测试execute_tool_call函数")
    print("=" * 50)
    
    print(f"[1/4] 连接到MCP服务器...")
    async with stdio_client(server_params) as (read, write):
        print(f"[2/4] 创建ClientSession...")
        async with ClientSession(read, write) as session:
            print(f"[3/4] 初始化会话...")
            await session.initialize()
            print(f"[4/4] 初始化完成")
            
            # 获取工具列表
            list_tool_response = await session.list_tools()
            tools_data = list_tool_response.model_dump()
            tools = json.dumps(tools_data['tools'], ensure_ascii=False, indent=2)
            print(f"\n可用工具列表:\n{tools}")
            
            # 测试SQL查询 - 查询异常流量数据
            print("\n\n测试1: 执行SQL查询 - 查询异常流量数据")
            sql = """
            select 1
            """
            result = await test_sql_query(session, sql)
            await format_and_print_results(result)
            
            # 测试获取表结构
            print("\n\n测试2: 获取表结构")
            result = await test_get_table_schema(session, "port_abnormal_traffic")
            await format_and_print_results(result)
            
            # 交互式测试
            while True:
                print("\n请选择要测试的工具 (输入数字或'exit'退出):")
                print("1. sql_query - 执行SQL查询")
                print("2. get_table_schema - 获取表结构")
                choice = input("> ")
                
                if choice.lower() == 'exit':
                    print("测试结束")
                    break
                
                if choice == '1':
                    print("\n请输入SQL查询语句:")
                    sql = input("> ")
                    result = await test_sql_query(session, sql)
                    await format_and_print_results(result)
                elif choice == '2':
                    print("\n请输入表名:")
                    table_name = input("> ")
                    result = await test_get_table_schema(session, table_name)
                    await format_and_print_results(result)
                else:
                    print("无效的选择，请重试")

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
