import json
from dotenv import load_dotenv
import os
import asyncio
import aiohttp
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

load_dotenv()

# 本地大模型服务配置
LLM_API_URL = os.getenv("LLM_API_URL", "http://openapi.telecomjs.com:80/eop/AIGLKFPT/DeepSeekR1_w8a8_AIPlatform/completions")
LLM_API_MODEL = os.getenv("LLM_API_MODEL", "DeepSeek-R1-Q-w8a8")
LLM_API_APP_ID = os.getenv("LLM_API_APP_ID", "a18f86ca710840688ff0fe023ea431f2")
LLM_API_APP_KEY = os.getenv("LLM_API_APP_KEY", "a6c5eebd88fc4330a1ea18e5276faa8a")

# 创建stdio服务器参数
server_params = StdioServerParameters(
    command="python",  # 可执行文件
    args=["mcp/mcp_idc_sql_server.py"],  # 命令行参数
    env=os.environ  # 传递当前环境变量
)

async def run():
    import time
    start_time = time.time()
    print("开始运行。。。")

    print(f"[{time.time() - start_time:.2f}s] 启动MCP服务器并连接...")
    async with stdio_client(server_params) as (read, write):
        print(f"[{time.time() - start_time:.2f}s] 连接成功，创建ClientSession...")
        async with ClientSession(read, write) as session:
            print(f"[{time.time() - start_time:.2f}s] ClientSession已创建，开始初始化...")
            await session.initialize()
            print(f"[{time.time() - start_time:.2f}s] 初始化完成")

            # 获取工具列表
            print(f"[{time.time() - start_time:.2f}s] 获取工具列表...")
            list_tool_response = await session.list_tools()
            tools_data = list_tool_response.model_dump()
            tools = json.dumps(tools_data['tools'], ensure_ascii=False)
            print(f"[{time.time() - start_time:.2f}s] 工具列表获取完成")

            # 构造系统提示
            system_prompt = fr"""你是一个IDC审核助手，通过查询PostgreSQL数据库来分析IDC操作、流量和速率配置的异常。
            你可以使用以下工具：
            ```json
            {tools}
            ```
            请按REACT模式执行：
            [思考] 分析问题
            [行动] 调用工具（JSON数组格式，用markdown代码标志包围），工具名称key是name，工具参数key是input
            [观察] 工具结果
            [答案] 最终结论

            请注意：
            1. 如果需要调用工具时，返回[思考]和[行动]，不要给出[观察]和[答案]，等待用户输入观察结果。
            2. 当不需要调用工具时，返回[思考]和[答案]。
            3. 请不要修改输入参数，当调用工具的入参无法返回数据或错误结果，请如实给出结果。
            4. 行动执行的SQL只能是查询SQL，不允许删除、修改、新增等任何数据修改操作。
            """

            print(system_prompt)

            # 初始化系统消息
            messages = [
                {"role": "system", "content": system_prompt}
            ]

            # 设置最大保留消息数量，防止上下文长度超限
            MAX_MESSAGES = 10  # 根据实际情况调整
            MAX_MESSAGE_LENGTH = 50000  # 单条消息最大长度

            print("\n请输入您的问题 (输入'exit'退出):")

            while True:
                # 从标准输入读取用户命令
                user_input = input("> ")

                # 检查是否退出
                if user_input.lower() == 'exit':
                    print("程序已退出")
                    break

                # 截断过长的输入
                if len(user_input) > MAX_MESSAGE_LENGTH:
                    print(f"警告: 输入过长，已截断至{MAX_MESSAGE_LENGTH}字符")
                    user_input = user_input[:MAX_MESSAGE_LENGTH]

                # 添加用户输入到消息历史
                messages.append({"role": "user", "content": user_input})

                max_iterations = 6
                final_answer = None
                context_error = False

                for _ in range(max_iterations):
                    # 查询本地大模型
                    api_result = await query_deepseek(messages)
                    if not api_result['success']:
                        error_msg = api_result.get('error', '')
                        print(f"API错误: {error_msg}")

                        # 检测是否是上下文长度错误
                        if "maximum context length" in error_msg and "tokens" in error_msg:
                            print("检测到上下文长度超限，正在减少历史消息...")
                            context_error = True

                            # 保留系统消息和最新的用户消息
                            if len(messages) > 2:
                                # 只保留系统消息和最新的用户消息
                                messages = [messages[0], messages[-1]]
                                print("已重置对话历史，仅保留当前问题")

                                # 重试API调用
                                continue
                        break

                    response_text = api_result['response']
                    print(f"\n模型完整响应: {response_text}")

                    # 解析工具调用
                    if '[行动]' in response_text:
                        tool_calls = extract_tool_call(response_text)
                        print(f"解析到工具调用: {tool_calls}")

                        # 确保tool_calls是一个列表
                        if not isinstance(tool_calls, list):
                            print("警告: 工具调用不是列表格式，正在转换...")
                            tool_calls = [tool_calls]

                        for tool_call in tool_calls:
                            # 执行工具
                            tool_result = await execute_tool_call(tool_call, session)

                            # 截断过长的工具结果
                            if len(str(tool_result)) > MAX_MESSAGE_LENGTH:
                                print(f"警告: 工具结果过长，已截断至{MAX_MESSAGE_LENGTH}字符")
                                tool_result = str(tool_result)[:MAX_MESSAGE_LENGTH] + "...[结果已截断]"

                            print(f"工具执行结果: {tool_result}")

                            # 更新消息历史
                            messages.extend([
                                {"role": "assistant", "content": response_text},
                                {"role": "user", "content": f"[观察] {tool_result}"}
                            ])

                            # 如果消息历史过长，保留最近的几条
                            if len(messages) > MAX_MESSAGES:
                                # 始终保留系统消息
                                messages = [messages[0]] + messages[-(MAX_MESSAGES-1):]
                                print(f"警告: 对话历史过长，已保留最近{MAX_MESSAGES-1}条消息")
                    else:
                        final_answer = response_text.split('[答案]')[-1].strip()
                        break

                if context_error and not final_answer:
                    print("\n由于上下文长度限制，请尝试简化您的问题或分多次提问")
                elif final_answer:
                    print(f"\n最终答案: {final_answer}")
                else:
                    print("\n未找到明确答案")

                # 保留系统消息，但清除之前的对话历史，以便开始新的对话
                messages = [messages[0]]
                print("\n请输入您的下一个问题 (输入'exit'退出):")

def extract_tool_call(response: str) -> list:
    """提取工具调用参数，支持多行JSON块"""
    import re
    import traceback

    # 尝试匹配JSON块
    json_pattern = r"\[行动\].*?```json(.*?)```"
    match = re.search(json_pattern, response, re.DOTALL)

    if not match:
        print("未找到JSON参数块")
        return [{"error": "未找到JSON参数块"}]

    # 提取JSON文本并清理
    json_text = match.group(1).strip()
    print(f"提取到的JSON文本: {json_text[:100]}...")  # 只打印前100个字符

    try:
        # 尝试解析JSON
        parsed_json = json.loads(json_text)

        # 确保返回的是一个列表
        if isinstance(parsed_json, list):
            print(f"成功解析为JSON数组，包含 {len(parsed_json)} 个元素")
            return parsed_json
        else:
            # 如果是单个对象，将其包装成列表
            print("成功解析为单个JSON对象，已转换为数组")
            return [parsed_json]

    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        print(f"错误位置附近的文本: {json_text[max(0, e.pos-30):min(len(json_text), e.pos+30)]}")
        traceback.print_exc()

        # 尝试修复常见的JSON格式问题
        try:
            # 尝试修复缺少引号的键
            fixed_json = re.sub(r'(\s*)(\w+)(\s*):', r'\1"\2"\3:', json_text)
            parsed_json = json.loads(fixed_json)
            print("修复JSON格式后成功解析")

            if isinstance(parsed_json, list):
                return parsed_json
            else:
                return [parsed_json]

        except Exception:
            # 如果修复失败，返回错误
            return [{"error": f"无效的JSON格式: {str(e)}"}]

    except Exception as e:
        print(f"解析JSON时发生未知错误: {str(e)}")
        traceback.print_exc()
        return [{"error": f"解析JSON时发生错误: {str(e)}"}]

async def execute_tool_call(tool_call: dict, session) -> str:
    if "error" in tool_call:
        return tool_call["error"]
    if (not tool_call.get('input')):
        tool_call["input"] = {}
    if not all(k in tool_call for k in ("name", "input")):
        return "工具调用缺少必要字段'name'或'input'"
    try:
        result = await session.call_tool(
            name=tool_call["name"],
            arguments=tool_call["input"]
        )
        return str(result)
    except Exception as e:
        return f"工具执行失败: {str(e)}"

async def query_deepseek(messages, model=LLM_API_MODEL, temperature=0, max_tokens=1024):
    """通过HTTP流式访问本地部署的大模型服务"""

    try:
        # 估算token数量，防止超出限制
        estimated_tokens = estimate_tokens(messages)
        if estimated_tokens > 60000:  # 预留一些空间，避免接近限制
            print(f"警告: 估计消息长度约为 {estimated_tokens} tokens，接近模型限制")

            # 如果系统消息太长，可以考虑缩短它
            if len(messages) > 1 and len(messages[0]["content"]) > 10000:
                print("系统消息过长，考虑在代码中缩短系统提示")

            # 如果历史消息太多，只保留最新的几条
            if len(messages) > 3:
                # 保留系统消息和最新的用户消息
                messages = [messages[0], messages[-1]]
                print("已自动裁剪历史消息以适应上下文限制")

        # 构建请求参数
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": True
        }

        # 构建请求头
        headers = {
            "Content-Type": "application/json"
        }

        # 如果有API认证信息，添加到请求头
        if LLM_API_APP_ID and LLM_API_APP_KEY:
            headers["X-APP-ID"] = LLM_API_APP_ID
            headers["X-APP-KEY"] = LLM_API_APP_KEY

        full_content = ""
        print("大模型响应流开始：")

        async with aiohttp.ClientSession() as session:
            async with session.post(LLM_API_URL, json=payload, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"API请求错误: HTTP {response.status} - {error_text}",
                        "is_complete": True
                    }

                # 处理流式响应
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue

                    # 处理数据结束标记
                    if line.startswith("data: [DONE]"):
                        break

                    # 处理数据行
                    if line.startswith("data: "):
                        try:
                            json_data = json.loads(line[6:])  # 去掉 "data: " 前缀
                            if "choices" in json_data and len(json_data["choices"]) > 0:
                                delta = json_data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    content_chunk = delta["content"]
                                    full_content += content_chunk
                                    print(content_chunk, end="", flush=True)
                        except json.JSONDecodeError:
                            print(f"\n警告: 无法解析JSON: {line}")
                            continue

        print("\n\n流处理已完成")
        return {
            "success": True,
            "response": full_content,
            "is_complete": True
        }
    except Exception as e:
        import traceback
        print(f"发生异常: {str(e)}")
        traceback.print_exc()
        return {
            "success": False,
            "error": f"API请求错误: {str(e)}",
            "is_complete": True
        }

def estimate_tokens(messages):
    """粗略估计消息中的token数量"""
    # 一个简单的估算方法：中文约1.5个字符/token，英文约4个字符/token
    # 这只是一个粗略估计，实际token数会根据分词器而有所不同
    total_tokens = 0
    for msg in messages:
        content = msg.get("content", "")
        # 计算中文字符数
        chinese_chars = sum(1 for c in content if '\u4e00' <= c <= '\u9fff')
        # 计算非中文字符数
        non_chinese_chars = len(content) - chinese_chars

        # 估算token数：中文字符/1.5 + 非中文字符/4
        estimated_tokens = chinese_chars / 1.5 + non_chinese_chars / 4

        # 每条消息额外添加一些token开销
        total_tokens += estimated_tokens + 4

    return int(total_tokens)

if __name__ == "__main__":
    import asyncio
    asyncio.run(run())
