import json
from dotenv import load_dotenv

import os
import asyncio
import aiohttp
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

load_dotenv()

# Create server parameters for stdio connection
server_params = StdioServerParameters(
    command="python",  # 可执行文件
    args=["mcp/mcp_idc_sql_server.py"],  # 命令行参数
    env=os.environ  # 传递当前环境变量
)


# 移除缓存机制，不再需要全局变量


# 主函数
async def run():
    import time
    start_time = time.time()
    print("开始运行。。。")

    print(f"[{time.time() - start_time:.2f}s] 启动 stdio_client...")
    async with stdio_client(server_params) as (read, write):
        print(f"[{time.time() - start_time:.2f}s] stdio_client 已启动，创建 ClientSession...")
        async with ClientSession(read, write) as session:
            print(f"[{time.time() - start_time:.2f}s] ClientSession 已创建，开始初始化...")
            await session.initialize()
            print(f"[{time.time() - start_time:.2f}s] stdio 初始化完成。。。")

            # 获取工具列表
            print(f"[{time.time() - start_time:.2f}s] 开始获取工具列表...")
            list_tool_response = await session.list_tools()
            tools_data = list_tool_response.model_dump()
            tools = json.dumps(tools_data['tools'], ensure_ascii=False)
            print(f"[{time.time() - start_time:.2f}s] 工具列表获取完成")

            # 构造系统提示
            system_prompt = fr"""你是一个IDC审核助手,通过查询postgresql数据库来分析IDC操作、流量和速率配置的异常,数据库表结构如下：

1.IDC 风险操作类型（idc_audit_risk_dictvalue）表结构：
- id (numeric) 主键
- exist_order (integer)  是否存在工单
- role_id (numeric)  操作角色ID  10100000611 表示割接前端口  10100000612 表示割接后端口
- group_id (integer) 分母ID
- target_risk_id (numeric) 目标风险类型
- port_status (character varying) 端口状态
- operation_type (character varying) 操作类型
- pso_type (character varying) 定单类型
- suggest (character varying) 处置建议
- risk (character varying) 是否有风险 有风险|无风险
- risk_analysis (character varying) 风险描述
- risk_level (character varying) 风险等级：高|中|低

2.pm_port_network_operation_audit表结构：
- id (numeric) 主键
- review_risk_id (numeric) 复核后风险ID
- operation_date (timestamp without time zone) 操作时间
- role_id (numeric)  操作角色
- dispatch_status (integer)  是否派单
- dispatch_date (timestamp without time zone) 派单时间
- create_date (timestamp without time zone) 采集时间
- device_id (numeric) 设备ID
- port_id (numeric) 端口ID
- risk_id (numeric) 风险类型ID，关联idc_audit_risk_dictvalue
- operator_full_dept (character varying) 操作人所属部门
- operator_name (character varying) 操作人姓名
- operator_account (character varying) 操作人工号
- dt (character varying) 日期
- region_name (character varying) 所属区域
- review_risk (character varying) 复核风险
- inspeed (character varying) 入带宽，打开关闭端口不需要配置
- outspeed (character varying) 出带宽，打开关闭端口不需要配置
- inspeedmode (character varying) 入带宽模式，打开关闭端口不需要配置
- outspeedmode (character varying) 出带宽模式，打开关闭端口不需要配置
- device_ip (character varying) 设备IP
- device_name (character varying) 设备名称
- device_code (character varying) 设备编码
- port_name (character varying) 端口名称
- port_code (character varying) 端口编码
- port_status (character varying) 端口状态
- port_business_code (character varying) 端口业务编码
- operation_instruct (character varying) 操作指令
- operation_notes (character varying) 操作备注
- operation_type (character varying) 操作类型 打开端口/关闭端口
- crm_cust_order_code (character varying) 客户订单
- pso_type (character varying) 业务定单类型
- order_business_code (character varying) 定单业务编码
- order_status (character varying) 定单状态

3.流量异常表(port_abnormal_traffic)。dev_code和port_name相同的端口表示同一个端口，我们将“有资源异常”作为异常流量分析，“无资源有流量异常”暂忽略，
。表结构如下
- id (numeric) 主键
- out_val (numeric) 出流量,单位bps
- in_val (numeric) 入流量,单位bps
- create_date (timestamp without time zone) 采集时间
- room_name (text) 机房
- dev_code (text) 设备编码
- dev_name (text) 设备名称
- dev_addr (text) 设备IP
- port_name (text) 端口名称
- port_code (text) 端口编码
- usingstate (text) 端口占用状态
- business_code (text) 业务编码
- cus_name (text) 客户
- crm_cust_order_code (text) 客户订单
- gom_order_state (text) 定单类型
- is_archived (text) 定单是否归档
- time_for (text) 时间
- ip (text) IP
- tagkey (text) tagkey
- abnormal_type (text) 异常类型 有资源异常|无资源有流量异常
- shijian (text) 流量产生时间
- area_name (text) 地区
- station_name (text) 数据中心
            ,可以使用以下工具：
            ```json
            {tools}
            ```
            请严格按REACT模式返回：
            [思考] 分析问题
            [行动] 调用工具（JSON数组格式，用markdown代码标志包围，注意逃逸特殊字符），工具名称key是name，工具参数key是input
            [观察] 工具结果
            [答案] 最终结论
            请注意：
            1. 如果需要调用工具时，返回[行动]，不要给出[观察]和[答案]，等待用户输入观察结果。当不需要调用工具时,返回[答案]，请不要修改输入参数，当调用工具的入参无法返回数据或错误结果，请如实给出结果。
            2. 尽量1个SQL查询所需要的数据，避免多次查询过程数据造成token的浪费
            3. port_abnormal_traffic存放每天的流速，需要根据设备和端口分组汇聚(group by)，分析其流量产生最早时间,最晚时间,持续时间(最晚时间-最早时间)，最大流速（入流量+出流量），最小流速（入流量+出流量），返回按最大流速从大到小排列，返回前10条数据
            4. 将dev_code和port_name识别成一个端口,请不要漏返回端口
            5. 流速单位是bps,请根据实际大小转成kbps、mbps、gbps
            6. 如果流量需要和操作结合分析，则port_abnormal_traffic 关联 pm_port_network_operation_audit 仅考虑设备端口，无需考虑时间范围。
            7. 如果不需要关联分析，只需要对单张表查询统计。
            8. 拼接生成的SQL用一行表达,不要包含换行字符
            9. 严格返回REACT模式
            10. 请对最后答案结果进行总结，输出清晰报告,如果没有数据直接告知，不要给示例数据
            11. ”shijian“ 字段需要转成 timestamp 参与计算

            """

            print(system_prompt)

            # 初始化系统消息
            messages = [
                {"role": "system", "content": system_prompt}
            ]

            # 设置最大保留消息数量，防止上下文长度超限
            MAX_MESSAGES = 3  # 根据实际情况调整
            MAX_MESSAGE_LENGTH = 50000  # 单条消息最大长度

            print("\n请输入您的问题 (输入'exit'退出):")

            while True:
                # 从标准输入读取用户命令
                user_input = input("> ")

                # 检查是否退出
                if user_input.lower() == 'exit':
                    print("程序已退出")
                    break

                # 截断过长的输入
                if len(user_input) > MAX_MESSAGE_LENGTH:
                    print(f"警告: 输入过长，已截断至{MAX_MESSAGE_LENGTH}字符")
                    user_input = user_input[:MAX_MESSAGE_LENGTH]

                # 添加用户输入到消息历史
                messages.append({"role": "user", "content": user_input})

                max_iterations = 6
                final_answer = None
                context_error = False

                for _ in range(max_iterations):
                    # 查询本地大模型
                    api_result = await query_deepseek(messages)
                    if not api_result['success']:
                        error_msg = api_result.get('error', '')
                        print(f"API错误: {error_msg}")

                        # 检测是否是上下文长度错误
                        if "maximum" in error_msg:
                            print("检测到上下文长度超限，正在减少历史消息...")
                            context_error = True

                            # 保留系统消息和最新的用户消息
                            if len(messages) > 2:
                                # 只保留系统消息和最新的用户消息
                                messages = [messages[0], messages[-1]]
                                print("已重置对话历史，仅保留当前问题")

                                # 重试API调用
                                continue
                        break

                    response_text = api_result['response']
                    print(f"\n模型完整响应: {response_text}")

                    # 解析工具调用
                    if '[行动]' in response_text:
                        tool_calls = extract_tool_call(response_text)
                        print(f"解析到工具调用: {tool_calls}")

                        # 确保tool_calls是一个列表
                        if not isinstance(tool_calls, list):
                            print("警告: 工具调用不是列表格式，正在转换...")
                            tool_calls = [tool_calls]

                        for tool_call in tool_calls:
                            # 执行工具
                            tool_result = await execute_tool_call(tool_call, session)
                            print ("工具调用结果",tool_result)

                            # 更新消息历史
                            messages.extend([
                                {"role": "assistant", "content": response_text},
                                {"role": "user", "content": f"[观察] {tool_result}"}
                            ])

                            # 如果消息历史过长，保留最近的几条
                            if len(messages) > MAX_MESSAGES:
                                # 始终保留系统消息
                                messages = [messages[0]] + messages[-(MAX_MESSAGES-1):]
                                print(f"警告: 对话历史过长，已保留最近{MAX_MESSAGES-1}条消息")
                    elif '[答案]' in response_text:
                        final_answer = response_text.split('[答案]')[-1].strip()
                        break

                if context_error and not final_answer:
                    print("\n由于上下文长度限制，请尝试简化您的问题或分多次提问")
                elif final_answer:
                    print(f"\n最终答案: {final_answer}")
                else:
                    print("\n未找到明确答案")

                # 保留系统消息，但清除之前的对话历史，以便开始新的对话
                messages = [messages[0]]
                print("\n请输入您的下一个问题 (输入'exit'退出):")



def extract_tool_call(response: str) -> list:
    """提取工具调用参数，支持多行JSON块"""
    import re
    import traceback

    # 尝试匹配JSON块
    json_pattern = r"\[行动\].*?```json(.*?)```"
    match = re.search(json_pattern, response, re.DOTALL)

    if not match:
        print("未找到JSON参数块")
        return [{"error": "未找到JSON参数块"}]

    # 提取JSON文本并清理
    json_text = match.group(1).strip()
    print(f"提取到的JSON文本: {json_text[:100]}...")  # 只打印前100个字符

    try:
        # 尝试解析JSON
        parsed_json = json.loads(json_text)

        # 确保返回的是一个列表
        if isinstance(parsed_json, list):
            print(f"成功解析为JSON数组，包含 {len(parsed_json)} 个元素")
            return parsed_json
        else:
            # 如果是单个对象，将其包装成列表
            print("成功解析为单个JSON对象，已转换为数组")
            return [parsed_json]

    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        print(f"错误位置附近的文本: {json_text[max(0, e.pos-30):min(len(json_text), e.pos+30)]}")
        traceback.print_exc()

        # 尝试修复常见的JSON格式问题
        try:
            # 尝试修复缺少引号的键
            fixed_json = re.sub(r'(\s*)(\w+)(\s*):', r'\1"\2"\3:', json_text)
            parsed_json = json.loads(fixed_json)
            print("修复JSON格式后成功解析")

            if isinstance(parsed_json, list):
                return parsed_json
            else:
                return [parsed_json]

        except Exception:
            # 如果修复失败，返回错误
            return [{"error": f"无效的JSON格式: {str(e)}"}]

    except Exception as e:
        print(f"解析JSON时发生未知错误: {str(e)}")
        traceback.print_exc()
        return [{"error": f"解析JSON时发生错误: {str(e)}"}]

async def execute_tool_call(tool_call: dict, session) -> str:
    if "error" in tool_call:
        return tool_call["error"]
    if (not tool_call.get('input')):tool_call["input"] = {}
    if not all(k in tool_call for k in ("name", "input")):
        return "工具调用缺少必要字段'name'或'input'"
    try:
        print("调用工具：")
        result = await session.call_tool(
            name=tool_call["name"],
            arguments=tool_call["input"]
        )

        return str(result)
    except Exception as e:
        return f"工具执行失败: {str(e)}"

# 本地大模型服务配置
LLM_API_URL = os.getenv("LLM_API_URL", "")
LLM_API_MODEL = os.getenv("LLM_API_MODEL", "")
LLM_API_APP_ID = os.getenv("LLM_API_APP_ID", "")
LLM_API_APP_KEY = os.getenv("LLM_API_APP_KEY", "")

async def query_deepseek(messages, model=LLM_API_MODEL, temperature=0, max_tokens=1024):
    """通过HTTP流式访问本地部署的大模型服务"""

    try:
        # 估算token数量，防止超出限制
        estimated_tokens = estimate_tokens(messages)
        if estimated_tokens > 60000:  # 预留一些空间，避免接近限制
            print(f"警告: 估计消息长度约为 {estimated_tokens} tokens，接近模型限制")

            # 如果系统消息太长，可以考虑缩短它
            if len(messages) > 1 and len(messages[0]["content"]) > 10000:
                print("系统消息过长，考虑在代码中缩短系统提示")

            # 如果历史消息太多，只保留最新的几条
            if len(messages) > 3:
                # 保留系统消息和最新的用户消息
                messages = [messages[0], messages[-1]]
                print("已自动裁剪历史消息以适应上下文限制")

        # 构建请求参数
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": True
        }

        # 构建请求头
        headers = {
            "Content-Type": "application/json"
        }

        # 如果有API认证信息，添加到请求头
        if LLM_API_APP_ID and LLM_API_APP_KEY:
            headers["X-APP-ID"] = LLM_API_APP_ID
            headers["X-APP-KEY"] = LLM_API_APP_KEY

        full_content = ""
        print("大模型响应流开始：")

        async with aiohttp.ClientSession() as session:
            async with session.post(LLM_API_URL, json=payload, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"API请求错误: HTTP {response.status} - {error_text}",
                        "is_complete": True
                    }

                # 处理流式响应
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue

                    # 处理数据结束标记
                    if line.startswith("data: [DONE]"):
                        print("结束：",line)
                        break

                    # 处理数据行
                    if line.startswith("data: "):
                        try:
                            json_data = json.loads(line[6:])  # 去掉 "data: " 前缀
                            if "choices" in json_data and len(json_data["choices"]) > 0:
                                delta = json_data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    content_chunk = delta["content"]
                                    full_content += content_chunk
                                    print(content_chunk, end="", flush=True)
                        except json.JSONDecodeError:
                            print(f"\n警告: 无法解析JSON: {line}")
                            continue

        print("\n\n流处理已完成")
        return {
            "success": True,
            "response": full_content,
            "is_complete": True
        }
    except Exception as e:
        import traceback
        print(f"发生异常: {str(e)}")
        traceback.print_exc()
        return {
            "success": False,
            "error": f"API请求错误: {str(e)}",
            "is_complete": True
        }

def estimate_tokens(messages):
    """粗略估计消息中的token数量"""
    # 一个简单的估算方法：中文约1.5个字符/token，英文约4个字符/token
    # 这只是一个粗略估计，实际token数会根据分词器而有所不同
    total_tokens = 0
    for msg in messages:
        content = msg.get("content", "")
        # 计算中文字符数
        chinese_chars = sum(1 for c in content if '\u4e00' <= c <= '\u9fff')
        # 计算非中文字符数
        non_chinese_chars = len(content) - chinese_chars

        # 估算token数：中文字符/1.5 + 非中文字符/4
        estimated_tokens = chinese_chars / 1.5 + non_chinese_chars / 4

        # 每条消息额外添加一些token开销
        total_tokens += estimated_tokens + 4

    return int(total_tokens)



if __name__ == "__main__":
    import asyncio
    asyncio.run(run())