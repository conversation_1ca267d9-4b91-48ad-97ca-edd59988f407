import aiohttp
import json
import os
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置API访问信息
API_BASE_URL = os.getenv("IDC_API_BASE_URL", "http://localhost:1084")
SQL_QUERY_ENDPOINT = os.getenv("SQL_QUERY_ENDPOINT", "/api/idc-query/sql_query")
API_URL = f"{API_BASE_URL}{SQL_QUERY_ENDPOINT}"

# 可选的API认证信息
API_APP_ID = os.getenv("API_APP_ID", "")
API_APP_KEY = os.getenv("API_APP_KEY", "")

async def execute_sql_query(sql: str):
    """
    执行SQL查询并返回结果
    
    Args:
        sql: 要执行的SQL查询语句
        
    Returns:
        查询结果的列表
    """
    if not sql:
        print("SQL查询语句为空")
        return []
    
    print(f"执行SQL查询: {sql}")
    
    # 构建请求参数
    payload = {
        "sql": sql
    }
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    # 如果有API认证信息，添加到请求头
    if API_APP_ID and API_APP_KEY:
        headers["X-APP-ID"] = API_APP_ID
        headers["X-APP-KEY"] = API_APP_KEY
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"发送请求到: {API_URL}")
            print(f"请求头: {headers}")
            print(f"请求体: {payload}")
            
            async with session.post(API_URL, json=payload, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"API请求错误: HTTP {response.status} - {error_text}")
                    return [{"error": f"API请求错误: HTTP {response.status} - {error_text}"}]
                
                result = await response.json()
                print("返回结果:", result)
                
                if "data" in result and isinstance(result["data"], list):
                    return result["data"]
                if "message" in result:
                    return [{"message": result["message"], "error": "执行异常"}]
                else:
                    return [{"message": "查询成功但未返回数据"}]
    except Exception as e:
        import traceback
        print(f"发生异常: {str(e)}")
        traceback.print_exc()
        return [{"error": f"API请求错误: {str(e)}"}]

async def main():
    """
    主函数，用于测试SQL查询功能
    """
    print("SQL查询测试工具")
    print(f"API URL: {API_URL}")
    print("=" * 50)
    
    while True:
        # 获取用户输入的SQL查询
        print("\n请输入SQL查询语句 (输入'exit'退出):")
        sql = input("> ")
        
        # 检查是否退出
        if sql.lower() == 'exit':
            print("程序已退出")
            break
        
        # 执行查询
        result = await execute_sql_query(sql)
        
        # 格式化输出结果
        print("\n查询结果:")
        print("-" * 50)
        
        if not result:
            print("无结果")
        elif "error" in result[0]:
            print(f"错误: {result[0]['error']}")
        else:
            # 尝试以表格形式打印结果
            try:
                # 获取所有列名
                if result:
                    columns = list(result[0].keys())
                    
                    # 计算每列的最大宽度
                    col_widths = {}
                    for col in columns:
                        col_widths[col] = max(len(str(col)), max(len(str(row.get(col, ""))) for row in result))
                    
                    # 打印表头
                    header = " | ".join(f"{col:{col_widths[col]}}" for col in columns)
                    print(header)
                    print("-" * len(header))
                    
                    # 打印数据行
                    for row in result:
                        row_str = " | ".join(f"{str(row.get(col, '')):{col_widths[col]}}" for col in columns)
                        print(row_str)
                    
                    print(f"\n共 {len(result)} 条记录")
                else:
                    print("查询结果为空")
            except Exception as e:
                # 如果表格格式化失败，直接打印JSON
                print(json.dumps(result, ensure_ascii=False, indent=2))
        
        print("-" * 50)

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
