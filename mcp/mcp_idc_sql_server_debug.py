from mcp.server.fastmcp import FastMCP
from typing import Dict, Any, List
import requests
import json
import os
import time
import threading
import traceback
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 创建MCP服务器
mcp = FastMCP("idc_sql_query_debug")

# 配置API访问信息
API_BASE_URL = os.getenv("IDC_API_BASE_URL", "http://localhost:8080")
SQL_QUERY_ENDPOINT = os.getenv("SQL_QUERY_ENDPOINT", "/api/idc-query/sql_query")
API_URL = f"{API_BASE_URL}{SQL_QUERY_ENDPOINT}"

# 可选的API认证信息
API_APP_ID = os.getenv("API_APP_ID", "")
API_APP_KEY = os.getenv("API_APP_KEY", "")

# 调试日志文件
DEBUG_LOG_FILE = "mcp_sql_debug.log"

def log_debug(message):
    """写入调试日志"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    with open(DEBUG_LOG_FILE, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")

# 工具1: 执行SQL查询
@mcp.tool()
def sql_query(sql: str) -> List[Dict[str, Any]]:
    """
    根据指定的SQL使用PostgreSQL执行查询语句，返回查询结果
    
    Args:
        sql: 要执行的SQL查询语句
        
    Returns:
        查询结果的列表
    """
    log_debug(f"开始执行SQL查询: {sql}")
    
    if not sql:
        log_debug("SQL查询语句为空")
        return []

    # 验证SQL语法（简单检查）
    if not sql.strip().lower().startswith(('select', 'with')):
        error_msg = "SQL语法错误: 只允许SELECT查询"
        log_debug(error_msg)
        return [{"error": error_msg}]

    # 构建请求参数
    payload = {
        "sql": sql
    }
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    # 如果有API认证信息，添加到请求头
    if API_APP_ID and API_APP_KEY:
        headers["X-APP-ID"] = API_APP_ID
        headers["X-APP-KEY"] = API_APP_KEY

    log_debug(f"API URL: {API_URL}")
    log_debug(f"请求头: {headers}")
    log_debug(f"请求体: {payload}")
    
    # 使用线程执行HTTP请求，避免阻塞MCP服务器
    def execute_request():
        try:
            log_debug("开始发送HTTP请求")
            # 设置超时时间为10秒
            response = requests.post(API_URL, json=payload, headers=headers, timeout=10)
            log_debug(f"HTTP请求完成，状态码: {response.status_code}")
            
            # 检查响应状态码
            if response.status_code != 200:
                error_text = response.text
                log_debug(f"API请求错误: HTTP {response.status_code} - {error_text}")
                return [{"error": f"API请求错误: HTTP {response.status_code} - {error_text}"}]
            
            # 解析JSON响应
            result = response.json()
            log_debug(f"返回结果: {result}")
            
            # 处理响应数据
            if "data" in result and isinstance(result["data"], list):
                return result["data"]
            if "message" in result:
                error_msg = result.get("message", "未知错误")
                log_debug(f"API返回错误: {error_msg}")
                return [{"error": error_msg}]
            else:
                log_debug("查询成功但未返回数据")
                return [{"error": "查询成功但未返回数据"}]
                
        except requests.exceptions.ConnectionError as e:
            log_debug(f"连接错误: {str(e)}")
            return [{"error": f"无法连接到API服务器: {str(e)}"}]
        except requests.exceptions.Timeout as e:
            log_debug(f"请求超时: {str(e)}")
            return [{"error": "API请求超时，请检查SQL语句或稍后重试"}]
        except requests.exceptions.RequestException as e:
            log_debug(f"请求错误: {str(e)}")
            return [{"error": f"API请求错误: {str(e)}"}]
        except json.JSONDecodeError as e:
            log_debug(f"JSON解析错误: {str(e)}")
            return [{"error": f"无法解析API响应: {str(e)}"}]
        except Exception as e:
            log_debug(f"执行查询时发生异常: {str(e)}")
            log_debug(traceback.format_exc())
            return [{"error": f"执行查询时发生异常: {str(e)}"}]
    
    try:
        # 设置最大超时时间
        timeout_seconds = 15
        log_debug(f"设置最大超时时间: {timeout_seconds}秒")
        
        # 创建线程执行请求
        result = [{"error": "请求超时"}]  # 默认结果
        
        def thread_func():
            nonlocal result
            result = execute_request()
        
        thread = threading.Thread(target=thread_func)
        thread.daemon = True  # 设置为守护线程，主线程结束时自动结束
        
        log_debug("启动线程执行请求")
        start_time = time.time()
        thread.start()
        
        # 等待线程完成或超时
        thread.join(timeout_seconds)
        elapsed_time = time.time() - start_time
        
        if thread.is_alive():
            log_debug(f"请求超时 ({timeout_seconds}秒)")
            return [{"error": f"请求超时 ({timeout_seconds}秒)，请检查SQL语句或稍后重试"}]
        else:
            log_debug(f"请求完成，耗时: {elapsed_time:.2f}秒")
            return result
            
    except Exception as e:
        log_debug(f"执行线程时发生异常: {str(e)}")
        log_debug(traceback.format_exc())
        return [{"error": f"执行线程时发生异常: {str(e)}"}]

# 工具2: 获取表结构信息
@mcp.tool()
def get_table_schema(table_name: str) -> List[Dict[str, Any]]:
    """
    获取指定表的结构信息
    
    Args:
        table_name: 表名
        
    Returns:
        表结构信息的列表
    """
    log_debug(f"获取表结构: {table_name}")
    
    if not table_name:
        log_debug("表名不能为空")
        return [{"error": "表名不能为空"}]
    
    # 构建SQL查询表结构的语句
    schema_sql = f"""
    SELECT 
        column_name as "列名", 
        data_type as "数据类型", 
        is_nullable as "是否可空",
        column_default as "默认值"
    FROM 
        information_schema.columns 
    WHERE 
        table_name = '{table_name}'
    ORDER BY 
        ordinal_position;
    """
    
    # 调用sql_query函数执行查询
    return sql_query(schema_sql)

# 工具3: 测试连接
@mcp.tool()
def test_connection() -> List[Dict[str, Any]]:
    """
    测试与API服务器的连接
    
    Returns:
        连接测试结果
    """
    log_debug("测试连接")
    
    try:
        # 设置超时时间为5秒
        response = requests.get(API_BASE_URL, timeout=5)
        log_debug(f"连接测试完成，状态码: {response.status_code}")
        
        return [{"status": "success", "message": f"连接成功，状态码: {response.status_code}"}]
    except Exception as e:
        log_debug(f"连接测试失败: {str(e)}")
        return [{"status": "error", "message": f"连接失败: {str(e)}"}]

if __name__ == "__main__":
    # 清空日志文件
    with open(DEBUG_LOG_FILE, "w", encoding="utf-8") as f:
        f.write(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 启动MCP服务器\n")
    
    # 启动服务器
    print(f"启动MCP服务器，使用stdio协议")
    print(f"API URL: {API_URL}")
    print(f"调试日志: {DEBUG_LOG_FILE}")
    mcp.run(transport='stdio')
