#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接使用 requests 库远程执行 SQL 查询的独立脚本
不依赖 MCP 框架，可以直接与后端 SQL 查询服务通信
"""

import requests
import json
import os
import time
import argparse
from dotenv import load_dotenv
from typing import Dict, Any, List, Optional, Union

# 加载环境变量
load_dotenv()

# 配置API访问信息
API_BASE_URL = os.getenv("IDC_API_BASE_URL", "http://localhost:8080")
SQL_QUERY_ENDPOINT = os.getenv("SQL_QUERY_ENDPOINT", "/api/idc-query/sql_query")
API_URL = f"{API_BASE_URL}{SQL_QUERY_ENDPOINT}"

# 可选的API认证信息
API_APP_ID = os.getenv("API_APP_ID", "")
API_APP_KEY = os.getenv("API_APP_KEY", "")

def execute_sql_query(sql: str, timeout: int = 10) -> List[Dict[str, Any]]:
    """
    执行SQL查询并返回结果
    
    Args:
        sql: 要执行的SQL查询语句
        timeout: 请求超时时间（秒）
        
    Returns:
        查询结果的列表
    """
    if not sql:
        print("SQL查询语句为空")
        return []

    # 验证SQL语法（简单检查）
    if not sql.strip().lower().startswith(('select', 'with')):
        print(f"SQL语法错误: 只允许SELECT查询")
        return [{"error": "SQL语法错误: 只允许SELECT查询"}]

    # 构建请求参数
    payload = {
        "sql": sql
    }
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    # 如果有API认证信息，添加到请求头
    if API_APP_ID and API_APP_KEY:
        headers["X-APP-ID"] = API_APP_ID
        headers["X-APP-KEY"] = API_APP_KEY

    print(f"执行SQL查询: {sql}")
    print(f"API URL: {API_URL}")
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 发送请求
        response = requests.post(API_URL, json=payload, headers=headers, timeout=timeout)
        
        # 计算请求耗时
        elapsed_time = time.time() - start_time
        print(f"请求耗时: {elapsed_time:.2f}秒")
        
        # 检查响应状态码
        if response.status_code != 200:
            error_text = response.text
            print(f"API请求错误: HTTP {response.status_code} - {error_text}")
            return [{"error": f"API请求错误: HTTP {response.status_code} - {error_text}"}]
        
        # 解析JSON响应
        result = response.json()
        
        # 处理响应数据
        if "data" in result and isinstance(result["data"], list):
            return result["data"]
        if "message" in result:
            error_msg = result.get("message", "未知错误")
            print(f"API返回错误: {error_msg}")
            return [{"error": error_msg}]
        else:
            return [{"error": "查询成功但未返回数据"}]
            
    except requests.exceptions.ConnectionError as e:
        print(f"连接错误: {str(e)}")
        return [{"error": f"无法连接到API服务器: {str(e)}"}]
    except requests.exceptions.Timeout as e:
        print(f"请求超时: {str(e)}")
        return [{"error": "API请求超时，请检查SQL语句或稍后重试"}]
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {str(e)}")
        return [{"error": f"API请求错误: {str(e)}"}]
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {str(e)}")
        return [{"error": f"无法解析API响应: {str(e)}"}]
    except Exception as e:
        import traceback
        print(f"执行查询时发生异常: {str(e)}")
        traceback.print_exc()
        return [{"error": f"执行查询时发生异常: {str(e)}"}]

def get_table_schema(table_name: str) -> List[Dict[str, Any]]:
    """
    获取指定表的结构信息
    
    Args:
        table_name: 表名
        
    Returns:
        表结构信息的列表
    """
    if not table_name:
        return [{"error": "表名不能为空"}]
    
    # 构建SQL查询表结构的语句
    schema_sql = f"""
    SELECT 
        column_name as "列名", 
        data_type as "数据类型", 
        is_nullable as "是否可空",
        column_default as "默认值"
    FROM 
        information_schema.columns 
    WHERE 
        table_name = '{table_name}'
    ORDER BY 
        ordinal_position;
    """
    
    # 调用execute_sql_query函数执行查询
    return execute_sql_query(schema_sql)

def format_and_print_results(results: List[Dict[str, Any]]) -> None:
    """
    格式化并打印查询结果
    
    Args:
        results: 查询结果列表
    """
    print("\n查询结果:")
    print("-" * 50)
    
    if not results:
        print("无结果")
        return
    
    if "error" in results[0]:
        print(f"错误: {results[0]['error']}")
        return
    
    # 尝试以表格形式打印结果
    try:
        # 获取所有列名
        columns = list(results[0].keys())
        
        # 计算每列的最大宽度
        col_widths = {}
        for col in columns:
            col_widths[col] = max(len(str(col)), max(len(str(row.get(col, ""))) for row in results))
        
        # 打印表头
        header = " | ".join(f"{col:{col_widths[col]}}" for col in columns)
        print(header)
        print("-" * len(header))
        
        # 打印数据行
        for row in results:
            row_str = " | ".join(f"{str(row.get(col, '')):{col_widths[col]}}" for col in columns)
            print(row_str)
        
        print(f"\n共 {len(results)} 条记录")
    except Exception as e:
        # 如果表格格式化失败，直接打印JSON
        print(f"表格格式化失败: {str(e)}")
        print(json.dumps(results, ensure_ascii=False, indent=2))

def interactive_mode() -> None:
    """
    交互式模式，允许用户输入SQL查询
    """
    print("SQL查询执行器 - 交互式模式")
    print(f"API URL: {API_URL}")
    print("=" * 50)
    
    while True:
        # 获取用户输入的SQL查询
        print("\n请输入SQL查询语句 (输入'exit'退出, 'schema 表名'获取表结构):")
        user_input = input("> ")
        
        # 检查是否退出
        if user_input.lower() == 'exit':
            print("程序已退出")
            break
        
        # 检查是否获取表结构
        if user_input.lower().startswith('schema '):
            table_name = user_input[7:].strip()
            if table_name:
                results = get_table_schema(table_name)
                format_and_print_results(results)
            else:
                print("错误: 请指定表名")
            continue
        
        # 执行SQL查询
        results = execute_sql_query(user_input)
        format_and_print_results(results)

def file_mode(sql_file: str) -> None:
    """
    文件模式，从文件读取SQL查询
    
    Args:
        sql_file: SQL文件路径
    """
    print(f"SQL查询执行器 - 文件模式: {sql_file}")
    print(f"API URL: {API_URL}")
    print("=" * 50)
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql = f.read()
        
        if not sql.strip():
            print("错误: SQL文件为空")
            return
        
        print(f"从文件读取SQL查询: {sql_file}")
        results = execute_sql_query(sql)
        format_and_print_results(results)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {sql_file}")
    except Exception as e:
        print(f"读取文件时发生错误: {str(e)}")

def main() -> None:
    """
    主函数，处理命令行参数并执行相应的操作
    """
    parser = argparse.ArgumentParser(description='SQL查询执行器')
    parser.add_argument('-f', '--file', help='SQL文件路径')
    parser.add_argument('-q', '--query', help='SQL查询语句')
    parser.add_argument('-s', '--schema', help='获取表结构')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间（秒）')
    
    args = parser.parse_args()
    
    if args.schema:
        # 获取表结构
        results = get_table_schema(args.schema)
        format_and_print_results(results)
    elif args.query:
        # 执行单个SQL查询
        results = execute_sql_query(args.query, args.timeout)
        format_and_print_results(results)
    elif args.file:
        # 从文件读取SQL查询
        file_mode(args.file)
    else:
        # 交互式模式
        interactive_mode()

if __name__ == "__main__":
    main()
