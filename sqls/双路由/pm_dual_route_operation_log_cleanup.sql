// pm_dual_route_operation_log_cleanup.sql
// 双路由管理操作日志清理API - 简化版本
// limit 1

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};

// 基本参数验证
if (collect.isEmpty(p) || collect.isEmpty(p.retention_days)) {
    return 0;
}

// 直接删除，所有业务逻辑在Java层处理
var deleteFun = @@sql(p)<%
    DELETE FROM pm_dual_route_operation_log
    WHERE operation_time < CURRENT_TIMESTAMP - INTERVAL '#{p.retention_days} days'
%>;

return deleteFun(p);

//limit 1
