// pm_dual_route_operation_log_statistics.sql
// 双路由管理操作日志统计查询API - 简化版本

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        SELECT
            operation_type,
            object_type,
            area_code,
            operator_id,
            operator_name,
            operation_result,
            DATE(operation_time) as operation_date,
            EXTRACT(HOUR FROM operation_time) as operation_hour,
            COUNT(*) as count
        FROM pm_dual_route_operation_log
        WHERE 1 = 1

        <if test="p.start_time != null and p.start_time != ''">
            AND operation_time &gt; #{p.start_time}::timestamp
        </if>
        <if test="p.end_time != null and p.end_time != ''">
            AND operation_time &lt; #{p.end_time}::timestamp
        </if>
        <if test="p.area_code != null and p.area_code != ''">
            AND area_code = #{p.area_code}
        </if>
        <if test="p.operator_id != null and p.operator_id != ''">
            AND operator_id = #{p.operator_id}
        </if>
        <if test="p.operation_type != null and p.operation_type != ''">
            AND operation_type = #{p.operation_type}
        </if>
        <if test="p.object_type != null and p.object_type != ''">
            AND object_type = #{p.object_type}
        </if>

        GROUP BY operation_type, object_type, area_code, operator_id, operator_name, operation_result, DATE(operation_time), EXTRACT(HOUR FROM operation_time)
        ORDER BY MAX(operation_time) DESC

    </select>
%>;


var pageQuery = queryFun(${param});

run pageQuery.setPageInfo({
    "pageSize": #{pageSize},
    "currentPage": #{currentPage}
});

var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();

return {
    "success": true,
    "message": "双路由管理操作日志log_statistics查询成功",
    "pageInfo": pageInfo,
    "data": data
};
