// dual_route_trend_analysis.sql
// 双路由光路检测历史趋势分析

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;



// 月度检测频次和问题发现率趋势
var monthlyTrendFun = @@sql(p)<%
    SELECT 
        DATE_TRUNC('month', his_date) as stat_month,
        COUNT(*) as total_detections,
        COUNT(CASE WHEN check_result != '正常' THEN 1 END) as problem_detections,
        COUNT(DISTINCT object_id) as unique_objects,
        ROUND(COUNT(CASE WHEN check_result != '正常' THEN 1 END) * 100.0 / COUNT(*), 2) as problem_rate
    FROM pm_risk_check_his
    WHERE his_date >= #{p.startDate}::timestamp
        AND his_date <= #{p.endDate}::timestamp
        AND check_result IS NOT NULL
    GROUP BY DATE_TRUNC('month', his_date)
    ORDER BY stat_month
    LIMIT 24
%>;

// 状态变化事件趋势分析
var statusChangeTrendFun = @@sql(p)<%
    WITH object_status_timeline AS (
        SELECT 
            object_id,
            check_result,
            his_date,
            LAG(check_result) OVER (PARTITION BY object_id ORDER BY his_date) as prev_status,
            LAG(his_date) OVER (PARTITION BY object_id ORDER BY his_date) as prev_date
        FROM pm_risk_check_his
        WHERE his_date >= #{p.startDate}::timestamp
            AND his_date <= #{p.endDate}::timestamp
            AND check_result IS NOT NULL
    ),
    
    status_change_events AS (
        SELECT 
            object_id,
            check_result as current_status,
            prev_status as previous_status,
            his_date as change_date,
            -- 状态变化类型
            CASE 
                WHEN prev_status IS NULL THEN 'initial'
                WHEN prev_status = check_result THEN 'stable'
                WHEN prev_status IN ('缺管道', '无光路', '异常') 
                     AND check_result = '正常' THEN 'fixed'
                WHEN prev_status = '正常' 
                     AND check_result IN ('缺管道', '无光路', '异常', '同管道', '同光缆') THEN 'degraded'
                WHEN prev_status IN ('同管道', '同光缆', '局内光路', '同局前井') 
                     AND check_result = '正常' THEN 'improved'
                ELSE 'other_change'
            END as change_type
        FROM object_status_timeline
        WHERE prev_status IS NOT NULL
    )
    
    SELECT 
        DATE_TRUNC('month', change_date) as stat_month,
        COUNT(CASE WHEN change_type = 'fixed' THEN 1 END) as fixed_count,
        COUNT(CASE WHEN change_type = 'degraded' THEN 1 END) as degraded_count,
        COUNT(CASE WHEN change_type = 'improved' THEN 1 END) as improved_count,
        COUNT(CASE WHEN change_type = 'stable' THEN 1 END) as stable_count,
        -- 修复效率指标
        ROUND(COUNT(CASE WHEN change_type = 'fixed' THEN 1 END) * 100.0 / 
              NULLIF(COUNT(CASE WHEN change_type IN ('degraded', 'other_change') THEN 1 END), 0), 2) as fix_rate
    FROM status_change_events
    GROUP BY DATE_TRUNC('month', change_date)
    ORDER BY stat_month
    LIMIT 24
%>;

// 问题持续时间分析
var problemDurationFun = @@sql(p)<%
    WITH object_status_timeline AS (
        SELECT 
            object_id,
            check_result,
            his_date,
            LAG(check_result) OVER (PARTITION BY object_id ORDER BY his_date) as prev_status,
            LAG(his_date) OVER (PARTITION BY object_id ORDER BY his_date) as prev_date
        FROM pm_risk_check_his
        WHERE his_date >= #{p.startDate}::timestamp
            AND his_date <= #{p.endDate}::timestamp
            AND check_result IS NOT NULL
    ),
    
    problem_duration_analysis AS (
        SELECT 
            object_id,
            check_result as current_status,
            his_date as change_date,
            -- 计算状态持续时间（天）
            CASE 
                WHEN prev_date IS NOT NULL THEN 
                    EXTRACT(EPOCH FROM (his_date - prev_date)) / 86400
                ELSE NULL
            END as status_duration_days,
            -- 问题严重程度分类
            CASE 
                WHEN check_result IN ('缺管道', '无光路', '异常') THEN 'severe'
                WHEN check_result IN ('同管道', '同光缆', '局内光路', '同局前井') THEN 'moderate'
                ELSE 'normal'
            END as problem_severity
        FROM object_status_timeline
        WHERE prev_status IS NOT NULL
            AND check_result != '正常'
    )
    
    SELECT 
        problem_severity,
        COUNT(*) as problem_count,
        AVG(status_duration_days) as avg_duration_days,
        MIN(status_duration_days) as min_duration_days,
        MAX(status_duration_days) as max_duration_days,
        -- 持续时间分级统计
        COUNT(CASE WHEN status_duration_days <= 7 THEN 1 END) as short_term_count,
        COUNT(CASE WHEN status_duration_days > 7 AND status_duration_days <= 30 THEN 1 END) as medium_term_count,
        COUNT(CASE WHEN status_duration_days > 30 AND status_duration_days <= 90 THEN 1 END) as long_term_count,
        COUNT(CASE WHEN status_duration_days > 90 THEN 1 END) as chronic_count
    FROM problem_duration_analysis
    WHERE status_duration_days IS NOT NULL
    GROUP BY problem_severity
    ORDER BY 
        CASE problem_severity 
            WHEN 'severe' THEN 1 
            WHEN 'moderate' THEN 2 
            ELSE 3 
        END
%>;

// 高风险光路对象识别
var riskObjectsFun = @@sql(p)<%
    WITH object_risk_profile AS (
        SELECT 
            object_id,
            COUNT(*) as total_checks,
            COUNT(CASE WHEN check_result != '正常' THEN 1 END) as problem_count,
            MAX(his_date) as last_check_date,
            MIN(his_date) as first_check_date,
            ROUND(COUNT(CASE WHEN check_result != '正常' THEN 1 END) * 100.0 / COUNT(*), 2) as problem_frequency,
            
            -- 最近状态
            (SELECT check_result FROM pm_risk_check_his h2 
             WHERE h2.object_id = pm_risk_check_his.object_id 
             ORDER BY his_date DESC LIMIT 1) as current_status
             
        FROM pm_risk_check_his
        WHERE his_date > #{p.startDate}::timestamp
            AND his_date < #{p.endDate}::timestamp
        GROUP BY object_id
        HAVING COUNT(*) > 3  -- 至少检测3次以上
    )
    
    SELECT 
        risk_level,
        COUNT(*) as object_count,
        AVG(problem_frequency) as avg_problem_frequency,
        COUNT(CASE WHEN current_status != '正常' THEN 1 END) as current_problem_objects
    FROM (
        SELECT 
            CASE 
                WHEN problem_frequency > 50 THEN 'high_risk'
                WHEN problem_frequency > 20 THEN 'medium_risk'
                ELSE 'low_risk'
            END as risk_level,
            problem_frequency,
            current_status
        FROM object_risk_profile
    ) t
    GROUP BY risk_level
    ORDER BY 
        CASE risk_level
            WHEN 'high_risk' THEN 1
            WHEN 'medium_risk' THEN 2 
            ELSE 3 
        END
%>;


// 执行查询
var monthlyTrend = monthlyTrendFun(${param});
var statusChangeTrend = statusChangeTrendFun(${param});
var problemDuration = problemDurationFun(${param});
var riskObjects = riskObjectsFun(${param});

return {
    "success": true,
    "message": "双路由趋势分析查询成功",
    "data": {
        "queryParams": {
            "startDate": startDate,
            "endDate": endDate
        },
        "monthlyTrend": monthlyTrend,
        "statusChangeTrend": statusChangeTrend,
        "problemDuration": problemDuration,
        "riskObjects": riskObjects
    }
};

//limit 1000