CREATE TABLE "wlyy_bc_sch"."pm_risk_check_his" (
  "id" numeric(24),
  "object_type" varchar(1000) COLLATE "pg_catalog"."default",
  "check_result" varchar(10000) COLLATE "pg_catalog"."default",
  "previous_check_result" varchar(10000) COLLATE "pg_catalog"."default",
  "deal_result" varchar(10000) COLLATE "pg_catalog"."default",
  "exception_info" text COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "deal_time" timestamp(6),
  "confirm_status" varchar(10000) COLLATE "pg_catalog"."default",
  "confirm_op" varchar(10000) COLLATE "pg_catalog"."default",
  "confirm_time" timestamp(6),
  "snapshot" text COLLATE "pg_catalog"."default",
  "his_date" timestamp(6),
  "object_id" numeric(24)
)
;

ALTER TABLE "wlyy_bc_sch"."pm_risk_check_his"
  OWNER TO "wlyy_bc";