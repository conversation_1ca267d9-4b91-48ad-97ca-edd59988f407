CREATE TABLE "wlyy_bc_sch"."pm_risk_check" (
  "id" numeric(24),
  "object_type" varchar(1000) COLLATE "pg_catalog"."default",
  "check_result" varchar(10000) COLLATE "pg_catalog"."default",
  "previous_check_result" varchar(10000) COLLATE "pg_catalog"."default",
  "deal_result" varchar(10000) COLLATE "pg_catalog"."default",
  "exception_info" text COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "deal_time" timestamp(6),
  "confirm_status" varchar(10000) COLLATE "pg_catalog"."default",
  "confirm_op" varchar(10000) COLLATE "pg_catalog"."default",
  "confirm_time" timestamp(6),
  "snapshot" text COLLATE "pg_catalog"."default",
  "object_id" numeric(24),
  "check_time" timestamp(6),
  "route_num" varchar(10) COLLATE "pg_catalog"."default",
  CONSTRAINT "obj" UNIQUE ("object_type", "object_id"),
  CONSTRAINT "object" UNIQUE ("object_id", "object_type")
)
;

ALTER TABLE "wlyy_bc_sch"."pm_risk_check"
  OWNER TO "wlyy_bc";

CREATE INDEX "pm_risk_check_his_obj" ON "wlyy_bc_sch"."pm_risk_check" USING btree (
  "object_id" "pg_catalog"."numeric_ops" ASC NULLS LAST,
  "object_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "pm_risk_check_obj" ON "wlyy_bc_sch"."pm_risk_check" USING btree (
  "object_id" "pg_catalog"."numeric_ops" ASC NULLS LAST,
  "object_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);