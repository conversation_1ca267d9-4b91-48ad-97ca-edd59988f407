-- 双路由管理操作日志表
-- 用于记录用户在光路组管理页面的所有操作行为

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS seq_pm_dual_route_operation_log START WITH 1;

-- 双路由管理操作日志主表
DROP TABLE IF EXISTS pm_dual_route_operation_log CASCADE;
CREATE TABLE pm_dual_route_operation_log (
    id NUMERIC(24) PRIMARY KEY DEFAULT nextval('seq_pm_dual_route_operation_log'),
    
    -- 基本信息
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 操作时间
    operator_id VARCHAR(100) NOT NULL, -- 操作人ID
    operator_name VARCHAR(100) NOT NULL, -- 操作人姓名
    operator_ip VARCHAR(50), -- 操作人IP地址
    session_id VARCHAR(200), -- 会话ID
    
    -- 操作对象信息
    object_type VARCHAR(50) NOT NULL, -- 操作对象类型：opt_group(光路组), opt_element(光路元素), risk_check(风险检测)
    object_id NUMERIC(24), -- 操作对象ID
    object_code VARCHAR(200), -- 操作对象编码
    object_name VARCHAR(500), -- 操作对象名称
    
    -- 操作信息
    operation_type VARCHAR(50) NOT NULL, -- 操作类型：view(查看), create(新增), update(修改), delete(删除), risk_detect(风险检测), export(导出), import(导入), ignore_risk(忽略风险), restore_risk(恢复风险)
    operation_module VARCHAR(100) NOT NULL, -- 操作模块：光路组管理
    operation_description TEXT, -- 操作描述
    
    -- 业务信息
    area_code VARCHAR(50), -- 地市编码
    area_name VARCHAR(100), -- 地市名称
    speciality VARCHAR(100), -- 专业类型
    
    -- 修改前后数据对比（JSON格式存储）
    before_data TEXT, -- 修改前数据（JSON字符串）
    after_data TEXT, -- 修改后数据（JSON字符串）
    changed_fields TEXT, -- 变更字段列表（JSON数组）
    
    -- 操作结果
    operation_result VARCHAR(20) DEFAULT 'success', -- 操作结果：success(成功), failed(失败), partial(部分成功)
    error_message TEXT, -- 错误信息
    
    -- 扩展信息
    request_params TEXT, -- 请求参数（JSON字符串）
    response_data TEXT, -- 响应数据（JSON字符串）
    execution_time_ms INTEGER, -- 执行耗时（毫秒）
    
    -- 审计信息
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark TEXT -- 备注信息
);

-- 创建索引
CREATE INDEX idx_pm_dual_route_operation_log_time ON pm_dual_route_operation_log(operation_time DESC);
CREATE INDEX idx_pm_dual_route_operation_log_operator ON pm_dual_route_operation_log(operator_id, operation_time DESC);
CREATE INDEX idx_pm_dual_route_operation_log_object ON pm_dual_route_operation_log(object_type, object_id);
CREATE INDEX idx_pm_dual_route_operation_log_operation ON pm_dual_route_operation_log(operation_type, operation_time DESC);
CREATE INDEX idx_pm_dual_route_operation_log_area ON pm_dual_route_operation_log(area_code, operation_time DESC);

-- 添加表注释
COMMENT ON TABLE pm_dual_route_operation_log IS '双路由管理操作日志表';
COMMENT ON COLUMN pm_dual_route_operation_log.id IS '主键ID';
COMMENT ON COLUMN pm_dual_route_operation_log.operation_time IS '操作时间';
COMMENT ON COLUMN pm_dual_route_operation_log.operator_id IS '操作人ID';
COMMENT ON COLUMN pm_dual_route_operation_log.operator_name IS '操作人姓名';
COMMENT ON COLUMN pm_dual_route_operation_log.object_type IS '操作对象类型：opt_group(光路组), opt_element(光路元素), risk_check(风险检测)';
COMMENT ON COLUMN pm_dual_route_operation_log.operation_type IS '操作类型：view(查看), create(新增), update(修改), delete(删除), risk_detect(风险检测), export(导出), import(导入), ignore_risk(忽略风险), restore_risk(恢复风险)';
COMMENT ON COLUMN pm_dual_route_operation_log.before_data IS '修改前数据（JSON字符串）';
COMMENT ON COLUMN pm_dual_route_operation_log.after_data IS '修改后数据（JSON字符串）';
COMMENT ON COLUMN pm_dual_route_operation_log.changed_fields IS '变更字段列表（JSON数组）';

-- 操作类型枚举说明
/*
操作类型 (operation_type) 定义：
1. view - 查看详情：点击详情按钮、打开详细信息抽屉
2. create - 新增：新增光路组、新增光路元素
3. update - 修改：修改光路组信息、修改光路元素信息
4. delete - 删除：删除光路组、删除光路元素
5. risk_detect - 风险检测：单个检测、一键检测
6. export - 导出：导出数据
7. import - 导入：导入光路组
8. ignore_risk - 忽略风险：忽略异常
9. restore_risk - 恢复风险：恢复异常
10. query - 查询：筛选查询操作
11. statistics - 统计分析：跳转统计分析页面

对象类型 (object_type) 定义：
1. opt_group - 光路组
2. opt_element - 光路元素
3. risk_check - 风险检测记录
4. batch_operation - 批量操作
*/

-- 创建分区表（按月分区，提高查询性能）
-- 注意：PostgreSQL 10+ 支持声明式分区
-- CREATE TABLE pm_dual_route_operation_log_y2024m01 PARTITION OF pm_dual_route_operation_log
-- FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 示例数据插入（用于测试）
INSERT INTO pm_dual_route_operation_log (
    operator_id, operator_name, operator_ip,
    object_type, object_id, object_code, object_name,
    operation_type, operation_module, operation_description,
    area_code, area_name, speciality,
    before_data, after_data, changed_fields,
    operation_result, request_params
) VALUES (
    'admin', '系统管理员', '*************',
    'opt_group', 12345, 'OPT_GROUP_001', '测试光路组',
    'update', '光路组管理', '修改光路组备注信息',
    'wx', '无锡', '传输',
    '{"note": "原备注信息", "name": "测试光路组"}',
    '{"note": "修改后的备注信息", "name": "测试光路组"}',
    '["note"]',
    'success', '{"id": 12345, "note": "修改后的备注信息"}'
);
