
--光路隐患整改数量，分库统计（人工整理）
select
s.*
from
(
select
*,
row_number() over (
partition by temp1.object_id
order by
temp1.his_date
) as group_idx
from
(
select
grp.id,
pc.check_result,
pch.check_result,
pch.object_id,
pc.create_time,
pch.his_date
from
pm_risk_check pc
left join pm_risk_check_his pch on pc.object_id = pch.object_id
and pc.check_result & lt;& gt; pch.check_result
and pch.check_result in (
'缺管道', '无光路', '同管道',
'同光缆', '异常'
)
and pc.check_result not in (
'缺管道', '无光路', '同管道',
'同光缆', '异常'
)
and pc.check_result is not null
and pch.check_result is not null
and pch.his_date & gt; to_timestamp (
'2024-12-01 00:00:00', 'yyyy-MM-dd hh24:mi'
)
and pch.his_date & lt; to_timestamp (
'2025-1-01 00:00:00', 'yyyy-MM-dd hh24:mi'
)
inner join pm_opt_road_group grp on pc.object_id = grp.id
and grp.source_type_id = '0'
) temp1
) s
where
s.group_idx = 1


样例数据：

202167	正常	缺管道	202167	2025-06-14 00:00:00	2024-12-05 00:00:00	1
202173	正常	缺管道	202173	2025-05-28 00:00:00	2024-12-05 00:00:00	1
202179	正常	缺管道	202179	2025-06-14 00:00:00	2024-12-06 00:00:00	1
202191	正常	缺管道	202191	2025-06-14 00:00:00	2024-12-27 00:00:00	1
202207	正常	缺管道	202207	2025-06-14 00:00:00	2024-12-27 00:00:00	1
202221	正常	同光缆	202221	2025-06-15 00:00:00	2024-12-10 00:00:00	1