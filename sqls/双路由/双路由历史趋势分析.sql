-- 双路由光路检测历史趋势分析
-- 基于 pm_risk_check_his 表进行历史数据分析和趋势预测

-- =====================================================
-- 1. 历史检测频次分析
-- =====================================================

-- 按月统计检测频次和问题发现率
WITH monthly_detection_stats AS (
    SELECT 
        DATE_TRUNC('month', his_date) as stat_month,
        COUNT(*) as total_detections,
        COUNT(CASE WHEN check_result != '正常' THEN 1 END) as problem_detections,
        COUNT(DISTINCT object_id) as unique_objects,
        ROUND(COUNT(CASE WHEN check_result != '正常' THEN 1 END) * 100.0 / COUNT(*), 2) as problem_rate
    FROM pm_risk_check_his
    WHERE his_date >= :start_date
        AND his_date < :end_date
    GROUP BY DATE_TRUNC('month', his_date)
),

-- =====================================================
-- 2. 光路对象状态变化轨迹分析
-- =====================================================

object_status_timeline AS (
    SELECT 
        object_id,
        check_result,
        his_date,
        LAG(check_result) OVER (PARTITION BY object_id ORDER BY his_date) as prev_status,
        LAG(his_date) OVER (PARTITION BY object_id ORDER BY his_date) as prev_date,
        ROW_NUMBER() OVER (PARTITION BY object_id ORDER BY his_date) as check_sequence
    FROM pm_risk_check_his
    WHERE his_date >= :start_date
        AND his_date < :end_date
        AND check_result IS NOT NULL
),

-- 状态变化事件识别
status_change_events AS (
    SELECT 
        object_id,
        check_result as current_status,
        prev_status as previous_status,
        his_date as change_date,
        prev_date,
        check_sequence,
        -- 计算状态持续时间（天）
        CASE 
            WHEN prev_date IS NOT NULL THEN 
                EXTRACT(EPOCH FROM (his_date - prev_date)) / 86400
            ELSE NULL
        END as status_duration_days,
        -- 状态变化类型
        CASE 
            WHEN prev_status IS NULL THEN 'initial'
            WHEN prev_status = check_result THEN 'stable'
            WHEN prev_status IN ('缺管道', '无光路', '异常') 
                 AND check_result = '正常' THEN 'fixed'
            WHEN prev_status = '正常' 
                 AND check_result IN ('缺管道', '无光路', '异常', '同管道', '同光缆') THEN 'degraded'
            WHEN prev_status IN ('同管道', '同光缆', '局内光路', '同局前井') 
                 AND check_result = '正常' THEN 'improved'
            ELSE 'other_change'
        END as change_type
    FROM object_status_timeline
    WHERE prev_status IS NOT NULL
),

-- =====================================================
-- 3. 问题持续时间分析
-- =====================================================

problem_duration_analysis AS (
    SELECT 
        object_id,
        current_status,
        change_date,
        status_duration_days,
        -- 问题严重程度分类
        CASE 
            WHEN current_status IN ('缺管道', '无光路', '异常') THEN 'severe'
            WHEN current_status IN ('同管道', '同光缆', '局内光路', '同局前井') THEN 'moderate'
            ELSE 'normal'
        END as problem_severity,
        -- 持续时间分级
        CASE 
            WHEN status_duration_days <= 7 THEN 'short_term'
            WHEN status_duration_days <= 30 THEN 'medium_term'
            WHEN status_duration_days <= 90 THEN 'long_term'
            ELSE 'chronic'
        END as duration_category
    FROM status_change_events
    WHERE change_type IN ('degraded', 'other_change')
        AND current_status != '正常'
)

-- =====================================================
-- 4. 趋势分析主查询
-- =====================================================

SELECT 
    -- 月度检测统计
    mds.stat_month,
    mds.total_detections,
    mds.problem_detections,
    mds.unique_objects,
    mds.problem_rate,
    
    -- 状态变化统计
    COUNT(CASE WHEN sce.change_type = 'fixed' THEN 1 END) as fixed_count,
    COUNT(CASE WHEN sce.change_type = 'degraded' THEN 1 END) as degraded_count,
    COUNT(CASE WHEN sce.change_type = 'improved' THEN 1 END) as improved_count,
    
    -- 问题持续时间统计
    AVG(CASE WHEN pda.problem_severity = 'severe' THEN pda.status_duration_days END) as avg_severe_duration,
    AVG(CASE WHEN pda.problem_severity = 'moderate' THEN pda.status_duration_days END) as avg_moderate_duration,
    
    -- 慢性问题统计
    COUNT(CASE WHEN pda.duration_category = 'chronic' THEN 1 END) as chronic_problems,
    
    -- 修复效率指标
    ROUND(COUNT(CASE WHEN sce.change_type = 'fixed' THEN 1 END) * 100.0 / 
          NULLIF(COUNT(CASE WHEN sce.change_type IN ('degraded', 'other_change') THEN 1 END), 0), 2) as fix_rate

FROM monthly_detection_stats mds
LEFT JOIN status_change_events sce ON DATE_TRUNC('month', sce.change_date) = mds.stat_month
LEFT JOIN problem_duration_analysis pda ON DATE_TRUNC('month', pda.change_date) = mds.stat_month
GROUP BY mds.stat_month, mds.total_detections, mds.problem_detections, mds.unique_objects, mds.problem_rate
ORDER BY mds.stat_month DESC;

-- =====================================================
-- 5. 光路对象风险评估
-- =====================================================

-- 识别高风险光路对象（频繁出现问题或问题持续时间长）
/*
WITH object_risk_profile AS (
    SELECT 
        object_id,
        COUNT(*) as total_checks,
        COUNT(CASE WHEN check_result != '正常' THEN 1 END) as problem_count,
        MAX(his_date) as last_check_date,
        MIN(his_date) as first_check_date,
        ROUND(COUNT(CASE WHEN check_result != '正常' THEN 1 END) * 100.0 / COUNT(*), 2) as problem_frequency,
        
        -- 最近状态
        FIRST_VALUE(check_result) OVER (PARTITION BY object_id ORDER BY his_date DESC) as current_status,
        
        -- 风险评分
        CASE 
            WHEN COUNT(CASE WHEN check_result != '正常' THEN 1 END) * 100.0 / COUNT(*) > 50 THEN 'high_risk'
            WHEN COUNT(CASE WHEN check_result != '正常' THEN 1 END) * 100.0 / COUNT(*) > 20 THEN 'medium_risk'
            ELSE 'low_risk'
        END as risk_level
        
    FROM pm_risk_check_his
    WHERE his_date >= :start_date
    GROUP BY object_id
)

SELECT 
    risk_level,
    COUNT(*) as object_count,
    AVG(problem_frequency) as avg_problem_frequency,
    COUNT(CASE WHEN current_status != '正常' THEN 1 END) as current_problem_objects
FROM object_risk_profile
GROUP BY risk_level
ORDER BY 
    CASE risk_level 
        WHEN 'high_risk' THEN 1 
        WHEN 'medium_risk' THEN 2 
        ELSE 3 
    END;
*/
