// pm_dual_route_operation_log_query.sql
// 双路由管理操作日志查询API - 简化版本

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        SELECT
            id,
            operation_time,
            operator_id,
            operator_name,
            operator_ip,
            object_type,
            object_id,
            object_code,
            object_name,
            operation_type,
            operation_module,
            operation_description,
            area_code,
            area_name,
            before_data,
            after_data,
            changed_fields,
            operation_result,
            error_message,
            remark
        FROM pm_dual_route_operation_log
        WHERE 1 = 1

        <if test="p.start_time != null and p.start_time != ''">
            AND operation_time &gt; #{p.start_time}::timestamp
        </if>
        <if test="p.end_time != null and p.end_time != ''">
            AND operation_time &lt; #{p.end_time}::timestamp
        </if>
        <if test="p.operator_id != null and p.operator_id != ''">
            AND operator_id = #{p.operator_id}
        </if>
        <if test="p.operation_type != null and p.operation_type != ''">
            AND operation_type = #{p.operation_type}
        </if>
        <if test="p.object_type != null and p.object_type != ''">
            AND object_type = #{p.object_type}
        </if>
        <if test="p.area_code != null and p.area_code != ''">
            AND area_code = #{p.area_code}
        </if>
        <if test="p.keyword != null and p.keyword != ''">
            AND (
                operation_description LIKE '%' || #{p.keyword} || '%'
                OR object_name LIKE '%' || #{p.keyword} || '%'
                OR object_code LIKE '%' || #{p.keyword} || '%'
            )
        </if>

        ORDER BY operation_time DESC
    </select>
%>;

var pageQuery = queryFun(${param});

run pageQuery.setPageInfo({
    "pageSize": #{pageSize},
    "currentPage": #{currentPage}
});

var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();

return {
    "success": true,
    "message": "双路由管理操作日志查询API查询成功",
    "pageInfo": pageInfo,
    "data": data
};
//limit 1000