hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


// 光路隐患整改统计查询
var hazardRemediationQueryFun = @@MyBatis(p)<%
    <select>

    SELECT
        s.*
    FROM (
        SELECT
            *,
            row_number() OVER (
                PARTITION BY temp1.光路组id
                ORDER BY temp1.历史检测时间 DESC
            ) as group_idx
        FROM (
            SELECT
                grp.id as 光路组id,
                grp.area_name as 地市名称,
                grp.leaf_region as 区县,
                grp.speciality as 专业,
                grp.code as 保护组编码,
                grp.name as 保护组名称,
                pc.object_type as 保护组类型,
                pc.check_result as 最新检测结果,
                pch.check_result as 上一次检测结果,
                pc.exception_info as 最新异常信息,
                pch.exception_info as 上一次检测异常信息,
                pc.check_time as 最新检测时间,
                pch.his_date as 历史检测时间
            FROM pm_risk_check pc
            LEFT JOIN pm_risk_check_his pch ON pc.object_id = pch.object_id
                AND pc.check_result &lt;&gt;  pch.check_result
                AND pc.check_result IS NOT NULL
                AND pch.check_result IS NOT NULL
                <if test="p.startDate != null and p.startDate != ''">
                    AND pch.his_date &gt; #{p.startDate}::timestamp
                </if>
                <if test="p.endDate != null and p.endDate != ''">
                    AND pch.his_date &lt;  #{p.endDate}::timestamp
                </if>
            INNER JOIN pm_opt_road_group grp ON pc.object_id = grp.id
                AND grp.source_type_id = '0'
                <if test="p.groupId != null and p.groupId != ''">
                    AND grp.id = #{p.groupId}::numeric
                </if>
                <if test="p.objectType != null and p.objectType != ''">
                    AND pc.object_type = #{p.objectType}
                </if>
                <if test="p.fromStatus != null and p.fromStatus != ''">
                    AND pch.check_result = #{p.fromStatus}
                </if>
                <if test="p.toStatus != null and p.toStatus != ''">
                    AND pc.check_result = #{p.toStatus}
                </if>
                <if test="p.transitionType != null and p.transitionType != ''">
                    <if test="p.transitionType == 'improved'">
                        AND pc.check_result = '正常' AND pch.check_result != '正常'
                    </if>
                    <if test="p.transitionType == 'deteriorated'">
                        AND pc.check_result != '正常' AND pch.check_result = '正常'
                    </if>
                    <if test="p.transitionType == 'maintained'">
                        AND pc.check_result = pch.check_result
                    </if>
                    <if test="p.transitionType == 'changed'">
                        AND pc.check_result != pch.check_result
                    </if>
                </if>
        ) temp1
    ) s
    WHERE 1=1 
                <if test="p.historyLimit != null and p.historyLimit != ''">
                    AND s.group_idx &lt; #{p.historyLimit}::integer

                </if>

    ORDER BY s.光路组id, s.group_idx

</select>
%>;

// 执行查询
var pageQuery = hazardRemediationQueryFun(${param});

run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();

return {
    "success": true,
    "message": "双路由光路隐患整改统计查询成功",
    "pageInfo": pageInfo,
    "data": data
};