-- 隐患整改相关表结构
-- 创建时间: 2024-12-05
-- 说明: 用于存储隐患整改表单数据，包括主表、光路关联表和附件关联表

-- 1. 创建序列
CREATE SEQUENCE IF NOT EXISTS seq_hazard_remediation START WITH 1;
CREATE SEQUENCE IF NOT EXISTS seq_hazard_remediation_optical_path START WITH 1;
CREATE SEQUENCE IF NOT EXISTS seq_hazard_remediation_attachment START WITH 1;

-- 2. 隐患整改主表
CREATE TABLE hazard_remediation (
    id NUMERIC(24) PRIMARY KEY DEFAULT nextval('seq_hazard_remediation'),
    
    -- 状态信息
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- 状态：draft-发起, inProgress-整改中, completed-整改完
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 最后更新时间
    
    -- 基本信息
    city_code VARCHAR(20) NOT NULL, -- 地市代码
    district VARCHAR(100), -- 区县
    speciality VARCHAR(50) DEFAULT '数据', -- 专业：数据/传输/接入/核心/综合
    hazard_source VARCHAR(50) NOT NULL, -- 隐患来源：selfDiscovered/businessGroup/deviceGroup
    hazard_type VARCHAR(50) NOT NULL, -- 隐患类型
    hazard_level VARCHAR(20) NOT NULL, -- 隐患等级：high/medium/low
    title VARCHAR(500) NOT NULL, -- 整改标题
    remediation_description TEXT NOT NULL, -- 整改描述及措施
    expected_completion_date DATE NOT NULL, -- 预计完成时间
    actual_completion_date DATE, -- 实际完成时间
    completion_description TEXT, -- 完成说明（用户填写）
    user_remaining_issues TEXT, -- 用户填写的遗留问题描述
    exemption_reason TEXT, -- 豁免理由（用户填写）
    completion_operator VARCHAR(100), -- 完成操作人
    completion_time TIMESTAMP, -- 完成操作时间
    responsible_person VARCHAR(100) NOT NULL, -- 责任人
    responsible_person_contact VARCHAR(100), -- 责任人联系方式
    remediation_person VARCHAR(100) NOT NULL, -- 整改人
    remediation_person_contact VARCHAR(100), -- 整改人联系方式
    
    -- 保护组信息（设备保护组）
    protection_scenario VARCHAR(100), -- 保护组场景
    custom_protection_scenario VARCHAR(200), -- 自定义保护组场景
    device_type VARCHAR(100), -- 设备类型
    custom_device_type VARCHAR(200), -- 自定义设备类型
    selected_protection_group_id VARCHAR(100), -- 选择的保护组ID
    
    -- 业务保护组信息
    optical_path_group_code VARCHAR(200), -- 光路组编码
    optical_path_group_name VARCHAR(500), -- 光路组名称
    
    -- 生命线业务信息
    is_lifeline VARCHAR(10), -- 是否生命线业务：0-是，1-否
    lifeline_business_id VARCHAR(100), -- 生命线业务ID
    lifeline_business_name VARCHAR(500), -- 生命线业务名称
    
    -- 业务信息（业务保护组）
    circuit_code VARCHAR(200), -- 电路编码
    circuit_name VARCHAR(500), -- 电路名称
    
    -- 客户信息（业务保护组）
    customer_name VARCHAR(500), -- 客户名称
    customer_account VARCHAR(200), -- 客户账号
    customer_manager VARCHAR(100), -- 客户经理姓名
    customer_manager_department VARCHAR(200), -- 客户经理部门
    customer_level VARCHAR(50), -- 客户等级
    differentiated_service_level VARCHAR(10), -- 差异化服务等级（2A-5A）
    
    -- 自行发现相关字段
    self_discovered_description TEXT, -- 自行发现说明
    discovered_by VARCHAR(100), -- 发现人
    discovered_date DATE, -- 发现时间
    
    -- 创建人信息
    creator VARCHAR(100), -- 创建人
    creator_id VARCHAR(100), -- 创建人ID

    -- 检测关联信息
    latest_detection_id NUMERIC(24), -- 最后一次检测记录ID
    initial_detection_id NUMERIC(24), -- 初始检测记录ID
    completion_detection_id NUMERIC(24), -- 完成检测记录ID
    detection_count INTEGER DEFAULT 0, -- 检测次数
    has_remaining_issues BOOLEAN DEFAULT FALSE, -- 是否有遗留问题（基于检测结果）

    -- 备注
    remark TEXT -- 备注信息
);

-- 创建索引
CREATE INDEX idx_hazard_remediation_status ON hazard_remediation(status);
CREATE INDEX idx_hazard_remediation_city_code ON hazard_remediation(city_code);
CREATE INDEX idx_hazard_remediation_district ON hazard_remediation(district);
CREATE INDEX idx_hazard_remediation_speciality ON hazard_remediation(speciality);
CREATE INDEX idx_hazard_remediation_hazard_source ON hazard_remediation(hazard_source);
CREATE INDEX idx_hazard_remediation_create_time ON hazard_remediation(create_time);
CREATE INDEX idx_hazard_remediation_creator ON hazard_remediation(creator);

-- 添加表注释
COMMENT ON TABLE hazard_remediation IS '隐患整改主表';
COMMENT ON COLUMN hazard_remediation.id IS '主键ID';
COMMENT ON COLUMN hazard_remediation.status IS '状态：draft-发起, inProgress-整改中, completed-整改完';
COMMENT ON COLUMN hazard_remediation.city_code IS '地市代码';
COMMENT ON COLUMN hazard_remediation.district IS '区县';
COMMENT ON COLUMN hazard_remediation.speciality IS '专业：数据/传输/接入/核心/综合';
COMMENT ON COLUMN hazard_remediation.hazard_source IS '隐患来源：selfDiscovered-自行发现/businessGroup-业务保护组/deviceGroup-设备保护组';
COMMENT ON COLUMN hazard_remediation.hazard_type IS '隐患类型';
COMMENT ON COLUMN hazard_remediation.hazard_level IS '隐患等级：high-高风险/medium-中风险/low-低风险';
COMMENT ON COLUMN hazard_remediation.actual_completion_date IS '实际完成时间';
COMMENT ON COLUMN hazard_remediation.completion_description IS '完成说明（用户填写）';
COMMENT ON COLUMN hazard_remediation.user_remaining_issues IS '用户填写的遗留问题描述';
COMMENT ON COLUMN hazard_remediation.exemption_reason IS '豁免理由（用户填写）';
COMMENT ON COLUMN hazard_remediation.completion_operator IS '完成操作人';
COMMENT ON COLUMN hazard_remediation.completion_time IS '完成操作时间';
COMMENT ON COLUMN hazard_remediation.latest_detection_id IS '最后一次检测记录ID';
COMMENT ON COLUMN hazard_remediation.initial_detection_id IS '初始检测记录ID';
COMMENT ON COLUMN hazard_remediation.completion_detection_id IS '完成检测记录ID';
COMMENT ON COLUMN hazard_remediation.detection_count IS '检测次数';
COMMENT ON COLUMN hazard_remediation.has_remaining_issues IS '是否有遗留问题（基于检测结果）';

-- 3. 隐患整改光路关联表
CREATE TABLE hazard_remediation_optical_path (
    id NUMERIC(24) PRIMARY KEY DEFAULT nextval('seq_hazard_remediation_optical_path'),
    remediation_id NUMERIC(24) NOT NULL, -- 关联隐患整改主表ID
    optical_path_code VARCHAR(200) NOT NULL, -- 光路编码
    sort_order INTEGER DEFAULT 1, -- 排序序号
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    
    CONSTRAINT fk_hazard_remediation_optical_path_remediation_id 
        FOREIGN KEY (remediation_id) REFERENCES hazard_remediation(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_hazard_remediation_optical_path_remediation_id ON hazard_remediation_optical_path(remediation_id);
CREATE INDEX idx_hazard_remediation_optical_path_code ON hazard_remediation_optical_path(optical_path_code);

-- 添加表注释
COMMENT ON TABLE hazard_remediation_optical_path IS '隐患整改光路关联表';
COMMENT ON COLUMN hazard_remediation_optical_path.remediation_id IS '关联隐患整改主表ID';
COMMENT ON COLUMN hazard_remediation_optical_path.optical_path_code IS '光路编码';
COMMENT ON COLUMN hazard_remediation_optical_path.sort_order IS '排序序号';

-- 4. 隐患整改附件表
CREATE TABLE hazard_remediation_attachment (
    id NUMERIC(24) PRIMARY KEY DEFAULT nextval('seq_hazard_remediation_attachment'),
    remediation_id NUMERIC(24) NOT NULL, -- 关联隐患整改主表ID
    original_filename VARCHAR(500) NOT NULL, -- 原始文件名
    stored_filename VARCHAR(200) NOT NULL, -- 存储在S3的文件名（UUID格式）
    file_size NUMERIC(20), -- 文件大小（字节）
    file_type VARCHAR(100), -- 文件类型/MIME类型
    s3_bucket VARCHAR(100) DEFAULT 'yuanfeng', -- S3存储桶名称
    s3_key VARCHAR(1000) NOT NULL, -- S3存储路径/键名
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 上传时间
    uploader VARCHAR(100), -- 上传人
    
    CONSTRAINT fk_hazard_remediation_attachment_remediation_id 
        FOREIGN KEY (remediation_id) REFERENCES hazard_remediation(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_hazard_remediation_attachment_remediation_id ON hazard_remediation_attachment(remediation_id);
CREATE INDEX idx_hazard_remediation_attachment_stored_filename ON hazard_remediation_attachment(stored_filename);
CREATE INDEX idx_hazard_remediation_attachment_upload_time ON hazard_remediation_attachment(upload_time);

-- 添加表注释
COMMENT ON TABLE hazard_remediation_attachment IS '隐患整改附件表';
COMMENT ON COLUMN hazard_remediation_attachment.remediation_id IS '关联隐患整改主表ID';
COMMENT ON COLUMN hazard_remediation_attachment.original_filename IS '原始文件名';
COMMENT ON COLUMN hazard_remediation_attachment.stored_filename IS '存储在S3的文件名（UUID格式）';
COMMENT ON COLUMN hazard_remediation_attachment.file_size IS '文件大小（字节）';
COMMENT ON COLUMN hazard_remediation_attachment.file_type IS '文件类型/MIME类型';
COMMENT ON COLUMN hazard_remediation_attachment.s3_bucket IS 'S3存储桶名称';
COMMENT ON COLUMN hazard_remediation_attachment.s3_key IS 'S3存储路径/键名';
COMMENT ON COLUMN hazard_remediation_attachment.upload_time IS '上传时间';
COMMENT ON COLUMN hazard_remediation_attachment.uploader IS '上传人';

-- 5. 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. 为主表创建更新时间触发器
CREATE TRIGGER update_hazard_remediation_modtime
    BEFORE UPDATE ON hazard_remediation
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- 7. 插入一些示例数据（可选，用于测试）
/*
INSERT INTO hazard_remediation (
    city_code, hazard_source, hazard_type, hazard_level, title,
    remediation_description, expected_completion_date, responsible_person,
    remediation_person, creator
) VALUES (
    'xz', 'selfDiscovered', 'singlePath', 'high', '徐州地区单路由隐患整改',
    '发现某光路存在单路由风险，需要进行双路由改造', '2024-12-31', '张三', '李四', '管理员'
);

-- 插入光路数据
INSERT INTO hazard_remediation_optical_path (remediation_id, optical_path_code, sort_order)
VALUES
    (1, 'OPT001', 1),
    (1, 'OPT002', 2);

-- 插入附件数据
INSERT INTO hazard_remediation_attachment (
    remediation_id, original_filename, stored_filename, file_size, file_type, s3_key, uploader
) VALUES (
    1, '整改方案.pdf', 'uuid-12345-67890.pdf', 1024000, 'application/pdf',
    'shuangluyou/hazard-remediation/uuid-12345-67890.pdf', '管理员'
);
*/

-- 8. 常用查询语句示例

-- 查询隐患整改单列表（带分页）
/*
SELECT
    hr.id,
    hr.title,
    hr.status,
    hr.city_code,
    hr.hazard_source,
    hr.hazard_type,
    hr.hazard_level,
    hr.responsible_person,
    hr.remediation_person,
    hr.expected_completion_date,
    hr.actual_completion_date,
    hr.create_time,
    hr.creator,
    COUNT(hrop.id) as optical_path_count,
    COUNT(hra.id) as attachment_count
FROM hazard_remediation hr
LEFT JOIN hazard_remediation_optical_path hrop ON hr.id = hrop.remediation_id
LEFT JOIN hazard_remediation_attachment hra ON hr.id = hra.remediation_id
WHERE hr.city_code = 'xz'  -- 可选的城市过滤
GROUP BY hr.id, hr.title, hr.status, hr.city_code, hr.hazard_source, hr.hazard_type,
         hr.hazard_level, hr.responsible_person, hr.remediation_person,
         hr.expected_completion_date, hr.actual_completion_date, hr.create_time, hr.creator
ORDER BY hr.create_time DESC
LIMIT 20 OFFSET 0;
*/

-- 查询单个隐患整改单详情（包含光路和附件）
/*
SELECT
    hr.*,
    COALESCE(
        json_agg(
            json_build_object(
                'id', hrop.id,
                'optical_path_code', hrop.optical_path_code,
                'sort_order', hrop.sort_order
            ) ORDER BY hrop.sort_order
        ) FILTER (WHERE hrop.id IS NOT NULL),
        '[]'::json
    ) as optical_paths,
    COALESCE(
        json_agg(
            json_build_object(
                'id', hra.id,
                'original_filename', hra.original_filename,
                'stored_filename', hra.stored_filename,
                'file_size', hra.file_size,
                'file_type', hra.file_type,
                's3_key', hra.s3_key,
                'upload_time', hra.upload_time,
                'uploader', hra.uploader
            ) ORDER BY hra.upload_time
        ) FILTER (WHERE hra.id IS NOT NULL),
        '[]'::json
    ) as attachments
FROM hazard_remediation hr
LEFT JOIN hazard_remediation_optical_path hrop ON hr.id = hrop.remediation_id
LEFT JOIN hazard_remediation_attachment hra ON hr.id = hra.remediation_id
WHERE hr.id = ?
GROUP BY hr.id;
*/

-- 统计查询：按状态统计隐患整改单数量
/*
SELECT
    status,
    COUNT(*) as count,
    COUNT(CASE WHEN hazard_level = 'high' THEN 1 END) as high_risk_count,
    COUNT(CASE WHEN hazard_level = 'medium' THEN 1 END) as medium_risk_count,
    COUNT(CASE WHEN hazard_level = 'low' THEN 1 END) as low_risk_count
FROM hazard_remediation
GROUP BY status
ORDER BY
    CASE status
        WHEN 'draft' THEN 1
        WHEN 'inProgress' THEN 2
        WHEN 'completed' THEN 3
        ELSE 4
    END;
*/

-- 统计查询：按地市统计隐患整改单数量
/*
SELECT
    city_code,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
    COUNT(CASE WHEN status = 'inProgress' THEN 1 END) as in_progress_count,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
    AVG(CASE WHEN actual_completion_date IS NOT NULL AND expected_completion_date IS NOT NULL
        THEN EXTRACT(DAY FROM actual_completion_date - expected_completion_date) END) as avg_delay_days
FROM hazard_remediation
GROUP BY city_code
ORDER BY total_count DESC;
*/
