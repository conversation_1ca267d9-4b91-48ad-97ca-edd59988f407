hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
import 'net.hasor.dataql.fx.basic.JsonUdfSource' as json;

var queryFun1 = @@mybatis(p)<%
<select>

	with t_node as (

			<if test="p.cbl_sect != null  and p.cbl_sect != ''">
			select id from  cm_cable where 1=1 and spec_id =1121000002
				   and  code = 	#{p.cbl_sect}
			</if>
			<if test="p.net_code != null  and p.net_code != ''">
				select entity_id id from cm_net  net
				join cr_net_entity cr on net.id = cr.net_id
				where net.code =  	#{p.net_code}
			</if>

			<if test="p.pipe_code != null  and p.pipe_code != ''">

			with t_edge
			as (
			select  t1.id, t5.id a_id,t5.code a_code, t5.spec_id a_spec_id, c.id b_id,c.spec_id b_spec_id
			from cm_cable c
			inner join cr_pipeline_cable t1 on c.id = t1.cable_id
			inner join   CM_PIPELINE t2 on t2.id = t1.pipeline_id
			inner join   CM_PIPELINE t5 on t5.id = t2.parent_id
			where t5.parent_id is null
			union all
			select  t1.id , t5.id a_id,t5.code a_code, t5.spec_id a_spec_id, c.id b_id,c.spec_id b_spec_id
			from  cm_cable c
			inner join cr_pipeline_cable t1 on c.id = t1.cable_id
			inner join   CM_PIPELINE t2 on t2.id = pipeline_id
			inner join   CM_PIPELINE t6 on t6.id =t2.parent_id
			inner join   CM_PIPELINE t5 on t5.id = t6.parent_id
			where t5.parent_id is null
			UNION all
			select  t1.id , t2.id a_id,t2.code a_code, t2.spec_id  a_spec_id, c.id b_id,c.spec_id b_spec_id
			from  cm_cable c
			inner join cr_pipeline_cable t1 on c.id = t1.cable_id
			inner join CM_PIPELINE t2 on t2.id = pipeline_id
			where t2.parent_id is null
			)
			select b_id as id from t_edge where 1=1
						and a_code  = #{p.pipe_code}
			</if>

						<if test="p.pipe_id != null  and p.pipe_id != ''">

			with t_edge
			as (
			select  t1.id, t5.id a_id,t5.code a_code, t5.spec_id a_spec_id, c.id b_id,c.spec_id b_spec_id
			from cm_cable c
			inner join cr_pipeline_cable t1 on c.id = t1.cable_id
			inner join   CM_PIPELINE t2 on t2.id = t1.pipeline_id
			inner join   CM_PIPELINE t5 on t5.id = t2.parent_id
			where t5.parent_id is null
			union all
			select  t1.id , t5.id a_id,t5.code a_code, t5.spec_id a_spec_id, c.id b_id,c.spec_id b_spec_id
			from  cm_cable c
			inner join cr_pipeline_cable t1 on c.id = t1.cable_id
			inner join   CM_PIPELINE t2 on t2.id = pipeline_id
			inner join   CM_PIPELINE t6 on t6.id =t2.parent_id
			inner join   CM_PIPELINE t5 on t5.id = t6.parent_id
			where t5.parent_id is null
			UNION all
			select  t1.id , t2.id a_id,t2.code a_code, t2.spec_id  a_spec_id, c.id b_id,c.spec_id b_spec_id
			from  cm_cable c
			inner join cr_pipeline_cable t1 on c.id = t1.cable_id
			inner join CM_PIPELINE t2 on t2.id = pipeline_id
			where t2.parent_id is null
			)
			select b_id as id from t_edge where 1=1
						and a_id  = #{p.pipe_id} ::numeric
			</if>





			<if test="p.cbl_sect_id != null  and p.cbl_sect_id != ''">
			select id from  cm_cable where 1=1 and spec_id =1121000002
				   and  id = 	#{p.cbl_sect_id}  ::numeric
			</if>

	),t_node2 as (
	select prod_id::varchar crm_prod_id,line_seq,line_spec_id,line_no,route.code route_code,route.name route_name
	from  PROD_CARRIER_CBL_SECTION_DAILY carrier
	left join cr_link_cable lc on lc.cable_id=carrier.line_seq::numeric
	left join cm_link jx on jx.id=lc.link_id
    left join cr_link_link ll on lc.link_id=ll.lower_link_id
    left join cm_link route on ll.upper_link_id=route.id
    left join cr_link_link road2route on road2route.lower_link_id=route.id
    left join cm_link opt_road on opt_road.id=road2route.upper_link_id
	where 1=1
	and  carrier.CBL_SECT_ID =  	(select id  from t_node limit 1)
	union
	select carrier.crm_product_id::varchar as crm_prod_id, fiber.id::varchar line_seq,fiber.spec_id line_spec_id,fiber.code::numeric line_no, opt_road.code as route_code, opt_road.name as route_name
	from cm_cable cable
	inner join cm_cable fiber on fiber.parent_id=cable.id
	inner join cr_link_cable lc on lc.cable_id=fiber.id
    inner join cm_link jx on jx.id=lc.link_id
    inner join cr_link_link ll on lc.link_id=ll.lower_link_id
    inner join cm_link route on ll.upper_link_id=route.id
    inner join cr_link_link road2route on road2route.lower_link_id=route.id
    inner join cm_link opt_road on opt_road.id=road2route.upper_link_id
    inner join pm_opt_road_carry_cfs carrier on carrier.opt_road_code=opt_road.code
    inner join t_node on cable.id=t_node.id
	where 1=1 and cable.id = (select id from t_node limit 1)
)select
       DISTINCT (prod.crm_prod_id,prod.cust_id ),
       prod.crm_prod_id,
       prod.ACCS_NBR_NO ACCS_NBR_NO,
       prod.instl_addr_full_name address,
       tp.product_name || '-' || prod.accs_nbr_no service_name,
       tp.product_name service_spec_name,
       cust.nm cust_name,
       custge.khjl_name,
       custge.khjl_phone,
       custge.p_name,
       custge.customer_level_name,
       ''  scene_name,
       line_seq,line_spec_id,line_no,route_code,route_name,
       t_node2.*
  from  PROD_USE_RES_SERV_DAILY_NEW prod
  inner join t_node2 on t_node2.crm_prod_id=prod.crm_prod_id
  left JOIN cust_daily_info cust
    on prod.cust_id ::numeric = cust.cust_id
  left join cust_ge_info_daily custge
    on  prod.cust_id  = custge.cust_id
  left join PM_SRV_PRODUCT tp
    on prod.crm_prod_spec_id = tp.product_no
  AND TP.product_type_id = '12000024'
where 1=1


 limit 100000


</select>
%>;



var pageQuery1 = queryFun1(${param});


return  {
	  'cfs_influence' : pageQuery1
}