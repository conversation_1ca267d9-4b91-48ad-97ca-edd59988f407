// nrm.pm_project_insert

hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var saveFun = @@sql(p)<%
insert into pm_project
(id, area_code , name, project_type_id, create_op, create_time, remark)
values
(
    #{p.id},
    #{p.area_code},
    #{p.name},
    #{p.project_type_id},
    #{p.create_op},
    now(),
    #{p.remark}
) ON CONFLICT(id) do update set name=#{p.name},area_code=#{p.area_code},project_type_id=#{p.project_type_id},remark=#{p.remark}
%>;


var generateId = (data) -> {
    var newMap = collect.newMap(data);
	if (data['id'] == null || data['id'] == '' ) {
		var newId = nextId('seq_pm_project',24,'32100002' )
		run newMap.put('id',newId.shortId);
    }
    return newMap.data()
};

var p = generateId(${param});
var result =  saveFun(p);
return {
	"result": result
};