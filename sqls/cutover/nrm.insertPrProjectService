// nrm.insertPrProjectService

hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var saveFun = @@sql[](p)<%
insert into pr_project_service
(project_id, action, batch_no, crm_product_id, access_code, service_type, service_level, customer, customer_manager, customer_manager_phone, customer_manager_org, customer_manager_post, is_lifecycle, address, entity_id,
 entity_code, entity_name, customer_type,row_status,line_no,route_code,route_name,a_road_device_name,z_road_device_name,a_road_port_code,z_road_port_code,a_cable_device_name,z_cable_device_name,a_cable_port_code,z_cable_port_code)
values
(
    #{p.project_id}::numeric,
    #{p.action},
    #{p.batch_no},
    #{p.crm_prod_id}::numeric,
    #{p.accs_nbr_no},
    #{p.service_spec_name},
    #{p.customer_level_name},
    #{p.cust_name},
    #{p.khjl_name},
    #{p.khjl_phone},
    #{p.khjl_org_name},
    #{p.khjl_post_type},
    #{p.is_lifecycle},
    #{p.address},
    #{p.entity_id}::numeric,
    #{p.entity_code},
    #{p.entity_name},
    #{p.customer_type},
    #{p.row_status}::int,
    #{p.line_no},
    #{p.route_code},
    #{p.route_name},
    #{p.a_road_device_name},
    #{p.z_road_device_name},
    #{p.a_road_port_code},
    #{p.z_road_port_code},
    #{p.a_cable_device_name},
    #{p.z_cable_device_name},
    #{p.a_cable_port_code},
    #{p.z_cable_port_code}
)
%>;

var result =  saveFun(${param}.prod_info);
return result ;