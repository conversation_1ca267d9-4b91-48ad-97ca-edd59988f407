// nrm.query_node_facility.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        SELECT  a.phy_port_no pon_code, dev.ip_addr ip,a.prod_id,dev.phy_eqp_id,dev.name dev_name,dev.no dev_no,
        obd.name obd_name,obd.no obd_code,obd_port.code obd_port_code FROM "prod_carrier_dev_daily" a
        inner JOIN res_phy_dev_daily dev on dev.phy_eqp_id::numeric = a.phy_eqp_id::numeric
        and a.phy_port_spec_id::numeric = 1041100004
        inner join prod_carrier_dev_daily t1 on a.prod_id::numeric=t1.prod_id::numeric
        and t1.phy_res_spec_id=1020200006
        inner JOIN res_phy_dev_daily obd on obd.phy_eqp_id::numeric = t1.phy_eqp_id::numeric
        inner join RES_PHY_OBD_DAILY last_obd  on t1.phy_eqp_id::numeric=last_obd.phy_eqp_id::numeric and last_obd.is_last_obd='1'
        inner join cm_port obd_port on t1.phy_port_id::numeric=obd_port.id and obd_port.up_down_id=100366
		<if test="p.prod_ids != null">
			and a.prod_id::numeric in
			<foreach collection="p.prod_ids" item="prod_id" open="(" close=")" separator=",">
				#{prod_id}::numeric
			</foreach>
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return {
	"pageInfo": pageInfo,
	"data": data
};

// ===================================================================================