hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var dataSetFun = @@mybatis(p)<%
    <select >
        SELECT * FROM pr_project_route a
		WHERE 1 = 1 and a.row_status=1
		<if test="p.project_id != null and p.project_id != ''">
			and a.project_id= #{p.project_id}::numeric           
		</if>
		ORDER BY net_code,cable_code,line_code,jx_fiber_code ASC
    </select>
 %>;

var pageQuery =  dataSetFun(${param});

run pageQuery.setPageInfo({
  "pageSize"    : #{pageSize}, // 页大小
  "currentPage" : #{currentPage}// 第n页
});

var pageInfo= pageQuery.pageInfo();
var data=pageQuery.data();

return {
'pageInfo': pageInfo,
'data': data
}