// nrm.insert_pr_project_opt_group

hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var saveFun = @@sql[](p)<%
insert into pr_project_opt_group
(id,project_id, action, batch_no, opt_group_id,opt_group_code,opt_group_name,opt_codes,effect_opt_codes,opt_code_num,effect_opt_code_num,risk_check_result,cutover_check_result,row_status)
values
(
		nextval('seq_pm_project'),
    #{p.project_id}::numeric,
    #{p.action},
    #{p.batch_no},
    #{p.opt_group_id}::numeric,
		#{p.opt_group_code},
		#{p.opt_group_name},
		#{p.opt_codes},
		#{p.effect_opt_codes},
		#{p.opt_code_num}::numeric,
		#{p.effect_opt_code_num}::numeric,
		#{p.risk_check_result},
		#{p.cutover_check_result},
		#{p.row_status}
)
%>;

var result =  saveFun(${param}.list);
return result ;