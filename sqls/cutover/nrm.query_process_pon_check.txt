// nrm.query_process_pon_check

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
var queryFun = @@mybatis(p)<%
<select>

select  coalesce(a.access_code,b.crm_product_id||'(超出影响范围)') access_code,a.is_lifecycle,a.entity_id,a.entity_code,a.entity_name,b.crm_product_id,b.olt_ip pre_olt_ip, b.olt_nm_ip pre_olt_nm_ip,b.olt_port_code pre_olt_port_code,c.olt_ip,c.olt_nm_ip,c.olt_port_code,b.access_device_code,b.access_device_code_loid,
b.obd_name,b.obd_code,b.obd_port_code,a.line_no,a.route_name,a.route_code,b.online_status as pre_online_status,c.online_status as process_online_status,b.rx_power pre_rx_power,c.rx_power process_rx_power
from pr_project_pon_check b
left join pr_project_service a on a.row_status=1 and a.action = '1' and a.project_id=b.project_id  and a.crm_product_id=b.crm_product_id and b.row_status=1 and b.online_status in ('online','offline','unknown') and b.action='2'
left join pr_project_pon_check c on b.project_id=c.project_id and c.action=#{p.action} and b.crm_product_id=c.crm_product_id and c.row_status=1 and c.online_status in ('online','offline','unknown')
and coalesce(b.obd_code, ' ')=coalesce(c.obd_code,' ') and coalesce(b.obd_port_code, ' ')=coalesce(c.obd_port_code, ' ')
where  b.row_status=1 and b.action='2' and b.online_status in ('online','offline','unknown')
<if test="p.project_id != null and p.project_id != ''">
	and b.project_id = #{p.project_id}::numeric
</if>
</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};