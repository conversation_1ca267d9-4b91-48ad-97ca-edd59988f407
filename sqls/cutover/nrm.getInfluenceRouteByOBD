hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var dataSetFun = @@mybatis(p)<%

    <!--查询指定行数据-->
    <select >
         SELECT #{p.code} source_code, net.id cable_id,  net.code net_code, a.name cable_name, a.code cable_code, b.code::numeric line_code, jxgx.code jx_fiber_code, cl.code route_code, cl.name route_name, cdajx.code jx_a_device_code, cdajx.name jx_a_device_name, cpajx.code jx_a_port_code, cdzjx.code jx_z_device_code, cdzjx.name jx_z_device_name, cpzjx.code jx_z_port_code, adt.res_type a_device_type, cda.code a_device_code, cda.name a_device_name, avrn.code a_device_ip, cpa.code a_port_code, apt.res_type a_port_type, zdt.res_type z_device_type, cdz.code z_device_code, cdz.name z_device_name, zvrn.code z_device_ip, cpz.code z_port_code, zpt.res_type z_port_type FROM cm_device cmd
		INNER JOIN cm_link cl ON (cl.a_physic_device_id = cmd.id or cl.z_physic_device_id = cmd.id) and cl.spec_id = 1131200002
		LEFT JOIN cr_link_link crll ON crll.upper_link_id = cl.id and crll.lower_link_spec_id = 1131100003
		LEFT JOIN cm_link jxgx ON jxgx.id = crll.lower_link_id
		LEFT JOIN cr_link_cable c on c.link_id = jxgx.id and c.cable_spec_id = 1121100002
		LEFT JOIN cm_cable b ON b.id = c.cable_id
		LEFT JOIN cm_cable a on a.id = b.parent_id
		LEFT JOIN cr_net_entity crne ON crne.entity_id = a.id and crne.net_spec_id = 1211200002
		LEFT JOIN cm_net net on net.id = crne.net_id
		LEFT JOIN cm_device cda ON cda.id = cl.a_physic_device_id
		LEFT JOIN pm_pub_res_type adt on adt.res_type_id = cda.spec_id
		LEFT JOIN rr_number_entity arne on arne.entity_id = cda.id and arne.number_spec_id = 2020400004
		LEFT JOIN v_rm_number avrn on avrn.id = arne.number_id
		LEFT JOIN cm_port cpa on cpa.id = cl.a_port_id
		LEFT JOIN pm_pub_res_type apt on apt.res_type_id = cpa.spec_id
		LEFT JOIN cm_device cdz ON cdz.id = cl.z_physic_device_id
		LEFT JOIN pm_pub_res_type zdt on zdt.res_type_id = cdz.spec_id
		LEFT JOIN rr_number_entity zrne on zrne.entity_id = cdz.id and zrne.number_spec_id = 2020400004
		LEFT JOIN v_rm_number zvrn on zvrn.id = zrne.number_id
		LEFT JOIN cm_port cpz on cpz.id = cl.z_port_id
		LEFT JOIN pm_pub_res_type zpt on zpt.res_type_id = cpz.spec_id
		LEFT JOIN cm_device cdajx ON cdajx.id = jxgx.a_physic_device_id
		LEFT JOIN cm_device cdzjx ON cdzjx.id = jxgx.z_physic_device_id
		LEFT JOIN cm_port cpajx ON cpajx.id = jxgx.a_port_id
		LEFT JOIN cm_port cpzjx ON cpzjx.id = jxgx.z_port_id
		WHERE 1 = 1  and cmd.code = #{p.code}
		ORDER BY line_code ASC
    </select>
 %>;

var pageQuery =  dataSetFun(${param});

run pageQuery.setPageInfo({
  "pageSize"    : #{pageSize}, // 页大小
  "currentPage" : #{currentPage}// 第n页
});

var pageInfo= pageQuery.pageInfo();
var data=pageQuery.data();

return {
'pageInfo': pageInfo,
'data': data
}