// nrm.summary_project_service

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
var queryFun = @@mybatis(p)<%
<select>
select a.customer_type,replace(service_type,'接入端','') service_type, count(distinct access_code) as row_count
from pr_project_service a
where row_status=1 and action = #{p.action}
and a.project_id = #{p.project_id}::numeric
group by a.customer_type,replace(service_type,'接入端','')
</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};