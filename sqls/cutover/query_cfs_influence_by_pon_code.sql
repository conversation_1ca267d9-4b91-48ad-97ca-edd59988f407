// query_cfs_influence_by_pon_code

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
import 'net.hasor.dataql.fx.basic.JsonUdfSource' as json;

var queryFun1 = @@mybatis(p)<%
<select>

with t_node as (
	<if test="p.codes != null  and p.codes != ''">
		select id from  cm_device where 1=1
				   and  code in  	
		<foreach collection="p.codes" item="code" open="(" close=")" separator=",">
			#{code}
		</foreach>
	</if>
	<if test="p.code != null  and p.code != ''">
		select id from  cm_device where   code = 	#{p.code}
	</if>
	<if test="p.olt_id != null  and p.olt_id != ''">
		select id from  cm_device where  id = 	#{p.olt_id} ::numeric
	</if>
	<if test="p.ip_addr != null  and p.ip_addr != ''">
		select PHY_EQP_ID id from  RES_PHY_OLT_DAILY where 1=1 and  MNG_IP_ADDR = 	#{p.ip_addr}
	</if>
),t_node2 as (
	select prod_id::varchar crm_prod_id
	from  PROD_CARRIER_DEV_DAILY carrier
	where 1=1 and  carrier.PHY_EQP_ID in  	(select id::varchar from t_node)
	<if test="p.pon_code != null  and p.pon_code != ''">
		and  carrier.phy_port_no = 	#{p.pon_code}
	</if>
)select 
       DISTINCT (prod.crm_prod_id,prod.cust_id ),
       prod.crm_prod_id,
       prod.ACCS_NBR_NO ACCS_NBR_NO,
			 prod.instl_addr_full_name address,
       tp.product_name || '-' || prod.accs_nbr_no service_name,
       tp.product_name service_spec_name,
       cust.nm cust_name,
       custge.khjl_name,
       custge.khjl_phone,
       custge.p_name,
       custge.customer_level_name,
''  scene_name
  from  PROD_USE_RES_SERV_DAILY_NEW prod
  left JOIN cust_daily_info cust
    on prod.cust_id ::numeric = cust.cust_id 
  left join cust_ge_info_daily custge
    on  prod.cust_id  = custge.cust_id
  left join PM_SRV_PRODUCT tp
    on prod.crm_prod_spec_id = tp.product_no 
  AND TP.product_type_id = '12000024'
where 1=1 
and  prod.crm_prod_id in  	(select crm_prod_id  from t_node2)
		

 limit 10000


</select>
%>;



var pageQuery1 = queryFun1(${param});


return  {
	  'cfs_influence' : pageQuery1
}