// nrm.query_pon_manufactor

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun= @@mybatis(p)<%
<select>
select n.code ip,d.id,d.name,d.code,f.code manufactor_code,f.name manufactor_name from v_rm_number n
inner join rr_number_entity n2d on n.id=n2d.number_id
inner join cm_device d on n2d.entity_id=d.id and n2d.entity_spec_id=d.spec_id
left join dm_model m on d.model_id=m.id
left join dm_manufactor f on m.manufactor_id=f.id
where 1=1
<if test="p.loopback_ips != null and p.loopback_ips != ''">
	 and n.code in
	<foreach collection="p.loopback_ips" item="loopback_ip" open="(" close=")" separator=",">
		#{loopback_ip}
	</foreach>
</if>

</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();

return {
	"pageInfo": pageInfo,
	"data": data
};