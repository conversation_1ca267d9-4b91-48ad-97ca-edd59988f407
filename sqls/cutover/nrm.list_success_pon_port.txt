// nrm.list_success_pon_port

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var listFun = @@sql(p)<%
select distinct olt_ip,olt_port_code from pr_project_pon_check where project_id=#{p.project_id}::numeric and action=#{p.action} and row_status=1 and online_status='online' limit 1000
%>;


var result =  listFun(${param});

return result;