// nrm.summary_project_pon_check

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
var queryFun = @@mybatis(p)<%
<select>
select a.online_status,count(1) as row_count
from pr_project_pon_check a
where row_status=1 and action = '2'
and a.project_id = #{p.project_id}::numeric
and exists (
select 1 from pr_project_service b where a.project_id=b.project_id and a.crm_product_id=b.crm_product_id and b.row_status=1 and b.action='1'
)
group by a.online_status
</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};