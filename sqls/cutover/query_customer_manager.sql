// nrm.query_customer_manager.sql

// tygh_all_staff_include_logoff_jtjm_day
// TYGH_ORG_DAY
// TYGH_STAFF_POST_DAY


hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
import 'net.hasor.dataql.fx.basic.JsonUdfSource' as json;

var queryFun1 = @@mybatis(p)<%
<select>

select t1.pd_inst_id crm_prod_id,t2.name khjl_name,t2.rela_mobile khjl_phone,t4.org_name,t4.post_type_name from css_pd_day_info t1
inner join v_manager_staff_info_daily t2 on t1.mana_id=t2.manager_id
left join tygh_all_staff_include_logoff_jtjm_day t3 on trim(t2.ty_staff_code)=trim(t3.staff_code)
left join TYGH_STAFF_POST_DAY t4 on t3.staff_id=t4.staff_id
where t2.manager_id is not null and t1.pd_inst_id in
<foreach collection="p.crmProductIds" item="id" open="(" close=")" separator=",">
	#{id}::numeric
</foreach>

limit 100000
</select>
%>;



var pageQuery1 = queryFun1(${param});


return  pageQuery1;