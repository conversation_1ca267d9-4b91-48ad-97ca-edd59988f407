// nrm.save_project_res

hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var deleteFun = @@sql(p)<%
delete from pr_project_res where project_id = #{p.project_id}::numeric
%>;


var saveFun = @@sql[](p)<%
insert into pr_project_res
(id, project_id, entity_id,entity_spec_id, entity_name, entity_spec_name, entity_spec_code,create_time,entity_code,dev_ip,res_role)
values
(
    #{p.id},
    #{p.project_id}::numeric,
    #{p.entity_id}::numeric,
    #{p.entity_spec_id}::numeric,
    #{p.entity_name},
    #{p.entity_spec_name},
    #{p.entity_spec_code},
    now(),
    #{p.entity_code},
    #{p.dev_ip},
    #{p.res_role}
)
%>;


var generateId = (data) -> {
    var newMap = collect.newMap(data);
	if (data['id'] == null || data['id'] == '' ) {
		var newId = nextId('seq_pm_project',24,'32100002' )
		run newMap.put('id',newId.shortId);
    }
    return newMap.data()
};

var p = ${param};

var result01 =  deleteFun(p);

var p2 = ${param}.resList => [
generateId(#)
];

var result02 =  saveFun(p2);

return p;