----获取同一设备编码的最新白名单数据。
DROP TABLE MID_NOC_WHITE_LIST_PHY ;
CREATE TABLE MID_NOC_WHITE_LIST_PHY
AS
SELECT *
  FROM order_monitor.MID_NOC_WHITE_LIST_PHY m
 WHERE M.CREATE_DATE = (SELECT MAX(CREATE_DATE)
                          FROM order_monitor.MID_NOC_WHITE_LIST_PHY
                         WHERE M.phy_EQP_NO = phy_EQP_NO
                           AND WHITE_TYPE = '设备白名单')
 AND M.LGC_EQP_SPEC_NO NOT IN ('SDH')
   UNION
 SELECT *
   FROM order_monitor.MID_NOC_WHITE_LIST_PHY m
  WHERE M.CREATE_DATE = (SELECT MAX(CREATE_DATE)
                           FROM order_monitor.MID_NOC_WHITE_LIST_PHY
                          WHERE M.phy_EQP_NO = phy_EQP_NO
                            AND WHITE_TYPE = '双路由白名单')
    AND M.LGC_EQP_SPEC_NO NOT IN ('SDH')
   UNION
 SELECT *
   FROM order_monitor.MID_NOC_WHITE_LIST_PHY m
  WHERE M.CREATE_DATE = (SELECT MAX(CREATE_DATE)
                           FROM order_monitor.MID_NOC_WHITE_LIST_PHY
                          WHERE M.phy_EQP_NO = phy_EQP_NO
                            AND WHITE_TYPE = '同板卡白名单')
    AND M.LGC_EQP_SPEC_NO NOT IN ('SDH');
--剔除白名单数据
delete from  sly_deivice_tj_info t where  t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
insert into sly_deivice_tj_info (city_name,area_name,device_spec,device_num,create_time)
select t.city_name ,area_name,device_spec,count(1),to_char(CURRENT_DATE,'yyyy-mm-dd')  from (select * from sly_device_base_info t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')) t
group by t.city_name ,t.area_name,t.device_spec;


update sly_deivice_tj_info t set port_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t
where error = '缺端口' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set port_err = 0 where port_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set gl_err1 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t
where error = '缺光路' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set gl_err1 = 0 where gl_err1 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set gl_err2 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t 
where error = '光路异常' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set gl_err2 = 0 where gl_err2 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set bk_err1 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_gl_bk_err  t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t 
where error = '无板卡' group by CITY_NAME, AREA_NAME, device_spec)n
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set bk_err1 = 0 where bk_err1 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set bk_err2 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_gl_bk_err  t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'同卡板白名单') and is_white='申请白名单成功')) t 
where error = '同板卡' group by CITY_NAME, AREA_NAME, device_spec)n  
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set bk_err2 = 0 where bk_err2 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set cs_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单')and is_white='申请白名单成功' )) t 
where error = '同光缆' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set cs_err = 0 where cs_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set zc_null = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in ('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t 
where error = '无穿管' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set zc_null = 0 where zc_null is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set zc_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t 
where error = '同管道' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set zc_err = 0 where zc_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info t set Dual_routing = n.num
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM 
(select CITY_NAME, AREA_NAME,device_id,device_spec,create_date from (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')) t
UNION
select CITY_NAME, AREA_NAME,device_id,device_spec,create_date from (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in ('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t) a
 group by a.CITY_NAME, a.AREA_NAME, a.device_spec)n
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set Dual_routing = 0 where Dual_routing is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');




