drop table IF EXISTS sly_pair_device_list_1 ;--select * from sly_pair_device_list_1
create table sly_pair_device_list_1 as
select distinct --t2.name city_name,t3.name area_name,
t.b_pair,k.ip device_ip,--rr.entity_id ,
--cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,
k.eqp_spec
from mid_noc_sly_eqp_list k 
join mid_noc_sly_eqp_pair t 
on k.ip = t.b1_ip 
	and k.area_name = t.p_area 
	and k.eqp_spec = t.eqp_spec
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
--left join  ${o3_res_schema}.cm_device cd on cd.id = rr.entity_id 
--left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
--left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('B设备' , 'ITV设备')
union
select distinct --t2.name city_name,t3.name area_name,
t.b_pair,k.ip device_ip,--rr.entity_id ,
--cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,
k.eqp_spec
from mid_noc_sly_eqp_list k 
join mid_noc_sly_eqp_pair t 
on k.ip = t.b2_ip 
	and k.area_name = t.p_area 
	and k.eqp_spec = t.eqp_spec
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
--left join  ${o3_res_schema}.cm_device cd on cd.id = rr.entity_id 
--left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
--left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('B设备' , 'ITV设备');
 create index device_ip on sly_pair_device_list_1(device_ip);

drop table if exists sly_pair_device_list_2;
create table sly_pair_device_list_2 as
select distinct t.b_pair,t.device_ip,t.eqp_spec,rr.phy_eqp_id entity_id  from sly_pair_device_list_1 t
left join ${o3_odso_schema}.res_phy_dev_daily rr on t.device_ip =rr.ip_addr;
--left join  ${o3_res_schema}.rm_number r on t.device_ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id;


 create index entity_id11 on sly_pair_device_list_2(entity_id);


drop table if exists sly_pair_device_list;
create table sly_pair_device_list as
select distinct t2.name city_name,t3.name area_name,t.b_pair,t.device_ip,cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,t.eqp_spec from sly_pair_device_list_2 t
left join  ${o3_res_schema}.cm_device cd on cd.id = t.entity_id 
left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id ;



--南京额外添加几台设备
<#if areaCode= 'nj'> 
insert into sly_pair_device_list
select '南京'as city_name,area_name,b_pair,device_ip,t1.id as device_id,device_code,device_name,t.create_date,eqp_spec from sly_pair_device_list_tmp t left join res_nj_sch.cm_device t1 on t.device_code=t1.code ;
 </#if>
insert into sly_pair_device_list
select distinct t2.name city_name,t3.name area_name,K.EQP_SPEC || '：' || K.IP b_pair,k.ip device_ip,cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,k.eqp_spec
from mid_noc_sly_eqp_list k 
left join ${o3_odso_schema}.res_phy_dev_daily rr on k.ip =rr.ip_addr
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
left join  ${o3_res_schema}.cm_device cd on cd.id = rr.phy_eqp_id 
left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('B设备' , 'ITV设备')
and not exists (select 1
   from mid_noc_sly_eqp_pair
   where (k.ip = b1_ip or k.ip = b2_ip)
   and CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = p_area
	 and eqp_spec = k.eqp_spec);
commit;


------------DR设备--------------------

insert into sly_pair_device_list
select distinct t2.name city_name,t3.name area_name,t.b_pair,k.ip device_ip,cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,k.eqp_spec
from mid_noc_sly_eqp_list k 
join mid_noc_sly_eqp_pair t 
on k.ip = t.b1_ip 
	and k.area_name = t.p_area 
left join ${o3_odso_schema}.res_phy_dev_daily rr on k.ip =rr.ip_addr
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
left join  ${o3_res_schema}.cm_device cd on cd.id = rr.phy_eqp_id
left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('DR设备','DSW设备','BSC CE')
union
select distinct t2.name city_name,t3.name area_name,t.b_pair,k.ip device_ip,cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,k.eqp_spec
from mid_noc_sly_eqp_list k 
join mid_noc_sly_eqp_pair t 
on k.ip = t.b2_ip 
	and k.area_name = t.p_area 
left join ${o3_odso_schema}.res_phy_dev_daily rr on k.ip =rr.ip_addr
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
left join  ${o3_res_schema}.cm_device cd on cd.id = rr.phy_eqp_id
left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('DR设备','DSW设备','BSC CE');


-- 去掉无效数据
DELETE FROM sly_pair_device_list T
 WHERE T.device_code IS NULL
   AND EXISTS (SELECT 1
          FROM sly_pair_device_list
         WHERE device_code IS NOT NULL
           AND T.device_ip = device_ip);
COMMIT;

------------ 设备到光路 100362：上联 100366：下联 100363： 上下联 ---------------------------
drop table IF EXISTS sly_pair_gl_info;
create table sly_pair_gl_info as
select t.*,t1.id port_id,t1.code port_code,t2.id gl_id,t2.code gl_code from sly_pair_device_list t 
left join ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id 
left join ${o3_res_schema}.cm_link t2 on t2.a_physic_device_id = t.device_id and t2.a_port_id = t1.id  and t1.up_down_id = 100362 and t2.spec_id = 1132400006 
where   (t2.notes not like '%逃生通道%' or t2.notes is null)
union
select t.*,t1.id port_id,t1.code port_code,t2.id gl_id,t2.code gl_code from sly_pair_device_list t 
left join ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id 
left join ${o3_res_schema}.cm_link t2 on t2.z_physic_device_id = t.device_id and t2.z_port_id = t1.id and t1.up_down_id = 100362 and t2.spec_id = 1132400006
where  (t2.notes not like '%逃生通道%' or t2.notes is null);

drop table if exists sly_pair_gl_info_2;
create table sly_pair_gl_info_2 as select * from sly_pair_gl_info;


drop table IF EXISTS sly_pair_gl_info1;
create table sly_pair_gl_info1 as
select t.*,t1.id port_id,t1.code port_code,t2.id gl_id,t2.code gl_code,t2.a_physic_device_id,t2.z_physic_device_id from sly_pair_device_list t 
left join  ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id 
left join  ${o3_res_schema}.cm_link t2 on t2.a_physic_device_id = t.device_id and t2.a_port_id = t1.id and t1.up_down_id = 100363 and t2.spec_id = 1132400006

union
select t.*,t1.id port_id,t1.code port_code,t2.id gl_id,t2.code gl_code,t2.a_physic_device_id,t2.z_physic_device_id from sly_pair_device_list t 
left join  ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id 
left join  ${o3_res_schema}.cm_link t2 on t2.z_physic_device_id = t.device_id and t2.z_port_id = t1.id and t1.up_down_id = 100363 and t2.spec_id = 1132400006
;

drop table IF EXISTS sly_pair_gl_info2;
create table sly_pair_gl_info2 as
select * from sly_pair_gl_info1 t1 where t1.device_id = t1.a_physic_device_id 
and  exists(select 1 from sly_pair_device_list t where t.b_pair = t1.b_pair and t.device_id = t1.z_physic_device_id)
union
select * from sly_pair_gl_info1 t1 where t1.device_id = t1.z_physic_device_id 
and  exists(select 1 from sly_pair_device_list t where t.b_pair = t1.b_pair and t.device_id = t1.a_physic_device_id);

insert into sly_pair_gl_info
select distinct t.city_name,t.area_name,t.b_pair,t.device_ip,t.device_id,t.device_code,t.device_name,t.create_date,t.eqp_spec,t.port_id,t.port_code,t.gl_id,t.gl_code from sly_pair_gl_info2 t;


drop table IF EXISTS sly_pair_device_netsource;
create table sly_pair_device_netsource as
select T.* ,'缺端口' ERROR from sly_pair_gl_info T
where t.device_id in 
(select t1.device_id from sly_pair_gl_info t1 
group by t1.device_id having count(distinct t1.port_code)<2);


insert into sly_pair_device_netsource
select T.* ,'缺光路' ERROR from sly_pair_gl_info T
where t.device_id in(select t1.device_id from sly_pair_gl_info t1 
group by t1.device_id having count (distinct t1.gl_code)<2 ) and t.device_id in(select t1.device_id from SLY_CN_BASE_INFO t1 
group by t1.device_id having count (distinct t1.port_code)>=2 );
commit;

--同板卡
drop table IF EXISTS sly_pair_gl_bk ;
create table sly_pair_gl_bk as
select cd.*,
(case when cdw2.code='' then '' else cdw2.code || '/' end)||
(case when cdw1.code='' then '' else cdw1.code || '/' end)||
(case when pcdw.code='' then '' else pcdw.code end) AS bk_code
from sly_pair_gl_info cd
left join  ${o3_res_schema}.cm_port cm on cm."id" = cd.port_id
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	 ${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN  ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) pcdw on pcdw.child_id = cm.ware_id 
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	 ${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN  ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) cdw1 on cdw1.child_id = pcdw.parent_id and cdw1.child_id IS NOT NULL
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	 ${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN  ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) cdw2 on cdw2.child_id = cdw1.parent_id and cdw2.child_id IS NOT NULL;

drop table IF EXISTS sly_pair_gl_bk_err ;
create table sly_pair_gl_bk_err as
select t.* from sly_pair_gl_bk t 
where t.device_id in(
select sdgi.device_id  from sly_pair_gl_bk sdgi 
group by sdgi.device_id having count(distinct sdgi.bk_code) = 1) and  t.device_id in  (select device_id from sly_pair_device_netsource t  where t.error = '缺光路') ;

delete from sly_three_gl_group_info t where t.group_type = '设备对';
insert into sly_three_gl_group_info (group_code,device_id,device_code,device_name,group_type,gl_id,gl_code,eqp_spec)
select t.b_pair,t.device_id,t.device_code,t.device_name,'设备对',t.gl_id,t.gl_code,t.eqp_spec from sly_pair_gl_info t;
-----------------------------------------------路由资源分析---------------------------------------------------------

drop table IF EXISTS sly_dwdm_gl_no_1_pair ;--select * from sly_dwdm_gl_no_1_pair limit 100
create table sly_dwdm_gl_no_1_pair as
select distinct t1.* ,  t3.id bf_id , t3.code bf_no   --, t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec
from sly_pair_gl_info t1 
inner join ${o3_res_schema}.cm_link gl on gl.code = t1.gl_code and gl.spec_id = 1131200002
inner join ${o3_res_schema}.cr_link_link t2 on t2.lower_link_id = gl.id and t2.upper_link_spec_id = 1132100021 -- 以太网链路

inner join ${o3_res_schema}.cr_link_link t4 on t4.upper_link_id = t2.upper_link_id and t4.lower_link_spec_id = 1132300020 -- client链路
inner join ${o3_res_schema}.cm_link t3 on t3.id = t4.lower_link_id
join ${o3_res_schema}.cv_link tt on tt.link_id=t3.id and pkey='P_OPT_ROUTE_MODEL'and value='100172'
; 
--select * from sly_dwdm_gl_no_1_dcsw limit 100;
create index sly_dwdm_gl_no_1_pairsy on sly_dwdm_gl_no_1_pair(bf_id);

drop table  if exists  sly_dwdm_gl_no_pair;--select * from sly_dwdm_gl_no_pair
create table sly_dwdm_gl_no_pair as select  DiSTINCT  t.*,t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec from sly_dwdm_gl_no_1_pair t 
join ${o3_res_schema}.cr_link_link t5 on t.bf_id=t5.upper_link_id and t5.lower_link_spec_id = 1132300005 
inner join ${o3_res_schema}.cm_link t3 on t3.id = t5.lower_link_id

join ${o3_res_schema}.cr_link_link t6 on t5.lower_link_id=t6.upper_link_id and t6.lower_link_spec_id = 1132200002
join ${o3_res_schema}.cr_link_link t7 on t6.lower_link_id=t7.upper_link_id and t7.lower_link_spec_id = 1132300002
join ${o3_res_schema}.cr_link_link t8 on t7.lower_link_id=t8.upper_link_id and t8.lower_link_spec_id = 1132100010
join ${o3_res_schema}.cr_link_link t9 on t8.lower_link_id=t9.upper_link_id  and t9.lower_link_spec_id = 1131200002
join ${o3_res_schema}.cr_link_link t10 on t9.lower_link_id=t10.lower_link_id  and t10.upper_link_spec_id = 1132400006
join ${o3_res_schema}.cm_link t11 on t10.upper_link_id=t11.id ;

--select * from sly_dwdm_gl_no_dcsw limit 100;







--select DISTINCT * from sly_dwdm_gl_no_1_dcsw 
drop table if EXISTS cm_link_gl;
drop table if EXISTS cr_link_link_t4 ;
drop table if EXISTS cm_link_t13;
drop table if EXISTS cr_link_link_t2;
----整合同属一个波分的
insert into sly_dwdm_gl_no_pair 
select t.*,null,t.gl_code||'波分',t.gl_id,t.gl_code from sly_pair_gl_info t ;
drop table IF EXISTS sly_dwdm_gl_cable_pair ;
create table sly_dwdm_gl_cable_pair as
select distinct  t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name,cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id ,ppr.desc_china cable_level
from sly_dwdm_gl_no_pair t
left join    ${o3_res_schema}.cr_link_link cll1  on cll1.upper_link_id = t.gl_id_bf and cll1.spec_id=1133111310000
left join    ${o3_res_schema}.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id and cll2.spec_id=1133511310000
left join   ${o3_res_schema}.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
left join   ${o3_res_schema}.cm_CABLE fiber on fiber.id = clc.cable_id
left join   ${o3_res_schema}.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002  --光缆段
left join   ${o3_res_schema}.cr_net_entity cne on cs.id=cne.entity_ID
left join   ${o3_res_schema}.cm_net cn on cn.id = cne.NET_iD
left join   ${o3_res_schema}.pm_pub_restriction ppr on ppr.serial_no = cn.net_type_id;

delete from sly_dwdm_gl_cable_pair t where t.cs_code is null and exists(select 1 from sly_dwdm_gl_cable_pair t1 where t1.device_code = t.device_code and t1.gl_code = t.gl_code and  t.cs_code is not null);

--select * from sly_dwdm_gl_cable_pair limit 100;
drop table IF EXISTS temp_sly_dwdm_num2_pair ;
create table temp_sly_dwdm_num2_pair as
select  b_pair,cs_id,count(distinct t.gl_code) num from sly_dwdm_gl_cable_pair t where t.cable_level <> '局内'and t.cable_level <> '联络'
group by  b_pair,cs_id ;

drop table IF EXISTS temp_sly_cable_err_pair ;
create table temp_sly_cable_err_pair as--select * from temp_sly_cable_err_pair
select t.* from temp_sly_dwdm_num2_pair t 
where num>=2 ;
--select * from temp_sly_cable_err_dcsw limit 100
--这里的是通过光缆段--管孔--管道段，但是目前有两种特殊情况1.光缆段--子管孔--管孔--管道段 2.光缆段--管道段
drop table IF EXISTS sly_dwdm_gl_zc_pair ;--select DISTINCT * from sly_dwdm_gl_zc_dcsw where gl_code='F2109020394'limit 100
create table  sly_dwdm_gl_zc_pair as
select distinct t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable_pair t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001 --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
union 
select distinct t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable_pair t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id  --子管孔
left join   ${o3_res_schema}.CM_PIPELINE t6 on t6.id =t2.parent_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id !=1111100001  --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id 
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
UNION
select distinct t.*,t2.id zc_section_id, null as code,t2.code zc_section_code,t2.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable_pair t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id and t2.spec_id !=1111100001  --去掉管孔
--left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t2.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t2.z_facility_id;




create index sly_dwdm_gl_zc_pairys1 on sly_dwdm_gl_zc_pair(device_id);
create index sly_dwdm_gl_zc_pairsy2 on sly_dwdm_gl_zc_pair(cs_id);
create index sly_dwdm_gl_zc_pairsy3 on sly_dwdm_gl_zc_pair(zc_section_id);

delete from sly_dwdm_gl_zc_pair t where cs_id is null;
delete from sly_dwdm_gl_zc_pair t where z_zc_eqp_code is null AND a_zc_eqp_code is null AND  zc_section_id  IS NOT NULL ;

delete from sly_dwdm_gl_zc_pair t where T.zc_section_id is null and exists
(select 1 from sly_dwdm_gl_zc_pair T1 where T.device_id = T1.device_id  and T1.cs_id = T.cs_id and T1.zc_section_id is not null);


drop table IF EXISTS sly_dwdm_gl_route_err1_pair ; --select * from sly_dwdm_gl_route_err1_pair
create table  sly_dwdm_gl_route_err1_pair as
select T.* , '无穿管' ERROR from sly_dwdm_gl_zc_pair T 
where T.zc_section_id is null and T.Cable_Level<> '局内' and T.Cable_Level<> '联络' and not exists
(select 1 from sly_dwdm_gl_zc_pair T1 where T.device_id = T1.device_id  and T1.cs_id = T.cs_id and T1.zc_section_id is not null);

--发现存在光缆属性不是局内，但光缆段属性是局内。所以在此做一个剔除；
delete from sly_dwdm_gl_route_err1_pair t where error='无穿管' and exists (select 1 from ${o3_res_schema}.cm_cable t1 where t.cs_id=t1.id  and t1.long_local_id =108439   );

--删除光缆级别为楼间的无穿管
delete from sly_dwdm_gl_route_err1_pair       where cable_level='楼间'; 


DELETE FROM sly_dwdm_gl_zc_pair WHERE zc_section_id is null;

delete from sly_dwdm_gl_zc_pair t where z_zc_eqp_code is null or a_zc_eqp_code is null;

drop table IF EXISTS temp_sly_dwdm_num3_pair ;
create table temp_sly_dwdm_num3_pair as
select  t.b_pair, t.zc_section_code,count(distinct t.gl_code) num from sly_dwdm_gl_zc_pair t
where t.cable_level <> '局内'
group by  t.b_pair, t.zc_section_code;

drop table IF EXISTS temp_sly_zc_err_pair ;
create table temp_sly_zc_err_pair as
select distinct t.* from temp_sly_dwdm_num3_pair t
where  num>=2 ;

insert into sly_dwdm_gl_route_err1_pair 
select t.*,'同管道'  from sly_dwdm_gl_zc_pair t
where EXISTS (select 1 from temp_sly_zc_err_pair t1 where t1.b_pair= t.b_pair and t1.zc_section_code = t.zc_section_code) 
and not EXISTS (select 1 from temp_sly_cable_err_pair t1 where t1.b_pair = t.b_pair and t1.cs_id = t.cs_id) ;

insert into sly_dwdm_gl_route_err1_pair (city_name,area_name,b_pair,device_ip,device_id,device_code,device_name,create_date,eqp_spec,port_id,port_code,gl_id,gl_code,bf_id,bf_no,gl_id_bf,gl_code_bf,gl_spec,cs_id,cs_code,cs_name,cable_id,cable_code,cable_name,spec_id,cable_level,error)
select t.*,'同光缆'  from sly_dwdm_gl_cable_pair t
where EXISTS (select 1 from temp_sly_cable_err_pair t1 where t1.b_pair= t.b_pair and t1.cs_id = t.cs_id) ;


DROP TABLE if exists TMP_PHYPAIR_ZC_EQP_CXR_1 ;
COMMIT;
CREATE TABLE TMP_PHYPAIR_ZC_EQP_CXR_1 --select * from TMP_PHYPAIR_ZC_EQP_CXR_1
AS
 SELECT DISTINCT t3.device_id LGC_EQP_ID,t.gl_code GL_NO2,t2.id BSE_EQP_ID,T.a_zc_eqp_code BEQ_NO,t4.res_type BSE_SPEC,T3.B_PAIR,zc_section_code BSE_SECT_ID

   FROM sly_dwdm_gl_zc_pair  T--管道人井表
	 join  sly_pair_gl_info  t3 on t.b_pair=t3.b_pair and t.gl_code=t3.gl_code 
	 --join ${o3_res_schema}.cm_device t1 on t1.code=t.b_pair 
	 join ${o3_res_schema}.cm_facility t2 on t.a_zc_eqp_code=t2.code and t2.notes like'%单路由局前井%'
	 join ${o3_res_schema}.pm_pub_res_type t4 on t4.res_type_id=t2.spec_id
 union
  SELECT DISTINCT t3.device_id LGC_EQP_ID,t.gl_code GL_NO2,t2.id BSE_EQP_ID,T.z_zc_eqp_code BEQ_NO,t4.res_type BSE_SPEC,T3.B_PAIR,zc_section_code BSE_SECT_ID
   FROM sly_dwdm_gl_zc_pair  T--管道人井表
	 join  sly_pair_gl_info  t3 on t.b_pair=t3.b_pair and t.gl_code=t3.gl_code
	 --join ${o3_res_schema}.cm_device t1 on t1.code=t.b_pair 
	 join ${o3_res_schema}.cm_facility t2 on t.z_zc_eqp_code=t2.code and t2.notes like'%单路由局前井%'
	 join ${o3_res_schema}.pm_pub_res_type t4 on t4.res_type_id=t2.spec_id;
	 DROP TABLE if exists TMP_PHYPAIR_ZC_EQP_CXR ;
COMMIT;
CREATE TABLE TMP_PHYPAIR_ZC_EQP_CXR --select * from TMP_PHYPAIR_ZC_EQP_CXR_1
AS
SELECT DISTINCT LGC_EQP_ID,GL_NO2,BSE_EQP_ID, BEQ_NO, BSE_SPEC,B_PAIR

   FROM TMP_PHYPAIR_ZC_EQP_CXR_1 T
  GROUP BY LGC_EQP_ID, GL_NO2, BSE_EQP_ID, BSE_SPEC,T.B_PAIR,T.BEQ_NO
 HAVING COUNT(DISTINCT BSE_SECT_ID) = 1;
 COMMIT;


drop table IF EXISTS white;
create table white as
select distinct t.group_name b_pair,t.bse_eqp_no eqp1 from sly_sys_white_config t where t.function_type = '设备对'
union
select  distinct  B_PAIR,BEQ_NO from TMP_PHYPAIR_ZC_EQP_CXR ;;

drop table IF EXISTS white1;
create table white1 as
select distinct t.b_pair,t.eqp1,t1.z_zc_eqp_code eqp2,t1.zc_section_code 
from white t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp1 = t1.a_zc_eqp_code and t.b_pair = t1.b_pair
union
select distinct t.b_pair,t.eqp1,t1.a_zc_eqp_code eqp2,t1.zc_section_code
from white t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp1 = t1.z_zc_eqp_code  and t.b_pair = t1.b_pair;

drop table IF EXISTS white2;
create table white2 as
select distinct t.b_pair,t.eqp2,t1.z_zc_eqp_code eqp3,t1.zc_section_code 
from white1 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp2 = t1.a_zc_eqp_code and t.eqp1 <> t1.z_zc_eqp_code and t.b_pair = t1.b_pair 
where t1.z_zc_eqp_code is not null
union
select distinct t.b_pair,t.eqp2,t1.a_zc_eqp_code eqp3,t1.zc_section_code
from white1 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp2 = t1.z_zc_eqp_code and t.eqp1 <> t1.a_zc_eqp_code and t.b_pair = t1.b_pair where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white3;
create table white3 as
select distinct t.b_pair,t.eqp3,t1.z_zc_eqp_code eqp4,t1.zc_section_code 
from white2 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp3 = t1.a_zc_eqp_code and t.eqp2 <> t1.z_zc_eqp_code and t.b_pair = t1.b_pair 
where t1.z_zc_eqp_code is not null
union
select distinct t.b_pair,t.eqp3,t1.a_zc_eqp_code eqp4,t1.zc_section_code
from white2 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp3 = t1.z_zc_eqp_code and t.eqp2 <> t1.a_zc_eqp_code and t.b_pair = t1.b_pair where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white4;
create table white4 as
select distinct t.b_pair,t.eqp4,t1.z_zc_eqp_code eqp5,t1.zc_section_code 
from white3 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp4 = t1.a_zc_eqp_code and t.eqp3 <> t1.z_zc_eqp_code and t.b_pair = t1.b_pair 
where t1.z_zc_eqp_code is not null
union
select distinct t.b_pair,t.eqp4,t1.a_zc_eqp_code eqp5,t1.zc_section_code
from white3 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp4 = t1.z_zc_eqp_code and t.eqp3 <> t1.a_zc_eqp_code and t.b_pair = t1.b_pair where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white5;
create table white5 as
select distinct t.b_pair,t.eqp5,t1.z_zc_eqp_code eqp6,t1.zc_section_code 
from white4 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp5 = t1.a_zc_eqp_code and t.eqp4 <> t1.z_zc_eqp_code and t.b_pair = t1.b_pair 
where t1.z_zc_eqp_code is not null
union
select distinct t.b_pair,t.eqp5,t1.a_zc_eqp_code eqp6,t1.zc_section_code
from white4 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp5 = t1.z_zc_eqp_code and t.eqp4 <> t1.a_zc_eqp_code and t.b_pair = t1.b_pair where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white6;
create table white6 as
select distinct t.b_pair,t.eqp6,t1.z_zc_eqp_code eqp7,t1.zc_section_code 
from white5 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp6 = t1.a_zc_eqp_code and t.eqp5 <> t1.z_zc_eqp_code and t.b_pair = t1.b_pair 
where t1.z_zc_eqp_code is not null
union
select distinct t.b_pair,t.eqp6,t1.a_zc_eqp_code eqp7,t1.zc_section_code
from white5 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp6 = t1.z_zc_eqp_code and t.eqp5 <> t1.a_zc_eqp_code and t.b_pair = t1.b_pair where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white7;
create table white7 as
select distinct t.b_pair,t.eqp7,t1.z_zc_eqp_code eqp8,t1.zc_section_code 
from white6 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp7 = t1.a_zc_eqp_code and t.eqp6 <> t1.z_zc_eqp_code and t.b_pair = t1.b_pair 
where t1.z_zc_eqp_code is not null
union
select distinct t.b_pair,t.eqp7,t1.a_zc_eqp_code eqp8,t1.zc_section_code
from white6 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp7 = t1.z_zc_eqp_code and t.eqp6 <> t1.a_zc_eqp_code and t.b_pair = t1.b_pair where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white8;
create table white8 as
select distinct t.b_pair,t.eqp8,t1.z_zc_eqp_code eqp9,t1.zc_section_code 
from white7 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp8 = t1.a_zc_eqp_code and t.eqp7 <> t1.z_zc_eqp_code and t.b_pair = t1.b_pair 
where t1.z_zc_eqp_code is not null
union
select distinct t.b_pair,t.eqp8,t1.a_zc_eqp_code eqp9,t1.zc_section_code
from white7 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp8 = t1.z_zc_eqp_code and t.eqp7 <> t1.a_zc_eqp_code and t.b_pair = t1.b_pair where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white9;
create table white9 as
select distinct t.b_pair,t.eqp9,t1.z_zc_eqp_code eqp10,t1.zc_section_code 
from white8 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp9 = t1.a_zc_eqp_code and t.eqp8 <> t1.z_zc_eqp_code and t.b_pair = t1.b_pair 
where t1.z_zc_eqp_code is not null
union
select distinct t.b_pair,t.eqp9,t1.a_zc_eqp_code eqp10,t1.zc_section_code
from white8 t 
left join sly_dwdm_gl_zc_pair  t1 on t.eqp9 = t1.z_zc_eqp_code and t.eqp8 <> t1.a_zc_eqp_code and t.b_pair = t1.b_pair where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white_list;
create table white_list as
select t.b_pair,t.eqp1 zc_eqp_no from white t
union 
select t1.b_pair,t1.eqp2 from white1 t1
union 
select t2.b_pair,t2.eqp3 from white2 t2
union 
select t3.b_pair,t3.eqp4 from white3 t3
union 
select t4.b_pair,t4.eqp5 from white4 t4
union 
select t5.b_pair,t5.eqp6 from white5 t5
union 
select t6.b_pair,t6.eqp7 from white6 t6
union 
select t7.b_pair,t7.eqp8 from white7 t7
union 
select t8.b_pair,t8.eqp9 from white8 t8
union 
select t9.b_pair,t9.eqp10 from white9 t9;


 
delete from sly_dwdm_gl_route_err1_pair  t where exists(select 1 from white_list t1 where t.b_pair = t1.b_pair and(t1.zc_eqp_no = t.a_zc_eqp_code or t1.zc_eqp_no = t.z_zc_eqp_code));
		delete from sly_dwdm_gl_route_err1_pair  t1 where EXISTS (select 1 from  sly_dwdm_gl_route_err1_pair   t where t.b_pair =t1.b_pair and t.error='同光缆') and t1.error='同管道';
delete from sly_dwdm_gl_route_err1_pair t where error in('无穿管','同光缆','同管道') and exists (select 1 from ${o3_res_schema}.cm_cable t1 where t.cs_id=t1.id  and t1.long_local_id =108439   );
  --select * from  sly_dwdm_gl_route_err1_pair limit 100
delete from SLY_DEVICE_PAIR_CHECK_INFO_gzdl t where t.create_time = CURRENT_DATE;

insert into SLY_DEVICE_PAIR_CHECK_INFO_gzdl (city_name,area_name,eqp_spec,eqp_num)
select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) from sly_pair_gl_info t group by t.city_name,t.area_name,t.eqp_spec;

update SLY_DEVICE_PAIR_CHECK_INFO_gzdl a set port_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_pair_device_netsource t where t.error = '缺端口' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl set port_err = 0 where port_err is null and create_time = CURRENT_DATE;

update SLY_DEVICE_PAIR_CHECK_INFO_gzdl a set gl_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_pair_device_netsource t where t.error = '缺光路' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl set gl_err = 0 where gl_err is null and create_time = CURRENT_DATE;
 
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl a set bk_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_pair_gl_bk_err t  group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl set bk_err = 0 where bk_err is null and create_time = CURRENT_DATE;
 

update SLY_DEVICE_PAIR_CHECK_INFO_gzdl a set cable_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_dwdm_gl_route_err1_pair t where t.error = '同光缆' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl set cable_err = 0 where cable_err is null and create_time = CURRENT_DATE;

update SLY_DEVICE_PAIR_CHECK_INFO_gzdl a set zc_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_dwdm_gl_route_err1_pair t where t.error = '同管道' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl set zc_err = 0 where zc_err is null and create_time = CURRENT_DATE;

update SLY_DEVICE_PAIR_CHECK_INFO_gzdl a set none_zc = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_dwdm_gl_route_err1_pair t where t.error = '无穿管' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl set none_zc = 0 where none_zc is null and create_time = CURRENT_DATE;
drop table if EXISTS SLY_pair_ERR_4_DATE_gzdl;
create table SLY_pair_ERR_4_DATE_gzdl AS
SELECT *,CURRENT_DATE as sj,NULL::date AS SJ_his FROM sly_dwdm_gl_route_err1_pair     ;
insert into SLY_pair_ERR_4_DATE_gzdl (city_name,area_name,b_pair,device_id,device_code,device_name,create_date,eqp_spec,port_code,gl_id,gl_code,error,sj)
select city_name,area_name,b_pair,device_id,device_code,device_name,create_date,eqp_spec,port_code,gl_id,gl_code,error,CURRENT_DATE as sj from sly_pair_device_netsource  ;
update SLY_pair_ERR_4_DATE_gzdl t set SJ_his=t1.SJ_his from SLY_pair_ERR_4_DATE_his_gzdl t1 where t1.b_pair=t.b_pair and t1.error=t.error;
update SLY_pair_ERR_4_DATE_gzdl t set sj_his=sj where sj_his is null;
drop table if EXISTS  SLY_pair_ERR_4_DATE_his_gzdl;
create table SLY_pair_ERR_4_DATE_his_gzdl as 
select * ,SJ-SJ_his as err_sj from SLY_pair_ERR_4_DATE_gzdl;
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl a set day_err= n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from SLY_pair_ERR_4_DATE_his_gzdl  t where t.err_sj>= 4  group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO_gzdl set day_err= 0 where day_err is null and create_time = CURRENT_DATE;