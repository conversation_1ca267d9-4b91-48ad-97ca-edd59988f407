CREATE OR REPLACE PROCEDURE "wlyy_cz_sch"."sly_three_route"("three_group_type" varchar)
 AS $BODY$
DECLARE
v2_group_type 	VARCHAR(255);
BEGIN
v2_group_type := three_group_type;
delete from sly_three_gl_group_cable sg where sg.group_type = v2_group_type;
delete from sly_three_section_glnum sc where sc.group_type = v2_group_type;
delete from SLY_THREE_SECTION_ERR_TMP sc where sc.group_type = v2_group_type;
delete from SLY_THREE_SECTION_ERR sc where sc.group_type = v2_group_type;
insert into sly_three_gl_group_cable
select distinct t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name,fiber.id fiber_id,fiber.code fiber_code ,fiber.name fiber_name, cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id,cn.net_type_id,pp.desc_china cable_level from sly_three_gl_group_info t
left join res_cz_sch.cr_link_link cll1  on cll1.upper_link_id = t.gl_id and cll1.spec_id=1133111310000
left join res_cz_sch.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id and cll2.spec_id=1133511310000
left join res_cz_sch.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
left join res_cz_sch.cm_CABLE fiber on fiber.id = clc.cable_id
left join res_cz_sch.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002  
left join res_cz_sch.cr_net_entity cne on cs.id=cne.entity_ID
left join res_cz_sch.cm_net cn on cn.id = cne.NET_iD
left join res_cz_sch.pm_pub_restriction pp on pp.serial_no = cn.net_type_id
where t.group_type = v2_group_type;
delete from sly_three_gl_group_cable T
where T.Fiber_Id is null
and T.group_type = v2_group_type
and exists (select 1 from sly_three_gl_group_cable T1 where T1.gl_code =T.gl_code and T1.Fiber_Id is not null and T.group_type = v2_group_type);
commit;
insert into sly_three_section_glnum
select t.group_type,t.group_code ,t.cs_id, count(distinct t.gl_code) glnum from sly_three_gl_group_cable t
where t.cable_level <> '局内' and t.cable_level <> '联络'
and t.group_type = v2_group_type
group by t.group_type,t.group_code ,t.cs_id;
insert into SLY_THREE_SECTION_ERR_TMP
select t.* from sly_three_section_glnum t
where t.group_type = v2_group_type and t.glnum >= 2;
insert into SLY_THREE_SECTION_ERR
select T.*,'同光缆' ERR from sly_three_gl_group_cable T
where T.group_type = v2_group_type
and  exists (select 1 from SLY_THREE_SECTION_ERR_TMP
where T.group_code = group_code and T.cs_id = cs_id and T.group_type = v2_group_type);
delete from sly_three_cable_zc_section sc where sc.group_type = v2_group_type;
delete from SLY_THREE_ROUTE sc where sc.group_type = v2_group_type;
delete from sly_THREE_zc_section_glnum sc where sc.group_type = v2_group_type;
delete from SLY_THREE_ZC_ERROR sc where sc.group_type = v2_group_type;
insert into sly_three_cable_zc_section
select distinct t.*,t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from sly_three_gl_group_cable t
left join res_cz_sch.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join res_cz_sch.CM_PIPELINE t2 on t2.id = pipeline_id
left join res_cz_sch.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id in (1111000001,1111000002,1111000003,1111000004)
left join res_cz_sch.cm_facility t3 on t3.id = t5.a_facility_id
left join res_cz_sch.cm_facility t4 on t4.id = t5.z_facility_id
where t.group_type = v2_group_type
union
select distinct t.*,t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from sly_three_gl_group_cable t
left join res_cz_sch.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join res_cz_sch.CM_PIPELINE t2 on t2.id = pipeline_id
left join res_cz_sch.CM_PIPELINE t6 on t6.id= t2.parent_id
left join res_cz_sch.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id in (1111000001,1111000002,1111000003,1111000004)
left join res_cz_sch.cm_facility t3 on t3.id = t5.a_facility_id
left join res_cz_sch.cm_facility t4 on t4.id = t5.z_facility_id
where t.group_type = v2_group_type
union
select distinct t.*,t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from sly_three_gl_group_cable t
left join res_cz_sch.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join res_cz_sch.CM_PIPELINE t5 on t5.id = pipeline_id and t5.spec_id in (1111000001,1111000002,1111000003,1111000004)
left join res_cz_sch.cm_facility t3 on t3.id = t5.a_facility_id
left join res_cz_sch.cm_facility t4 on t4.id = t5.z_facility_id
where t.group_type = v2_group_type;
delete from sly_three_cable_zc_section T
where T.zc_section_id is null
and T.group_type = v2_group_type
and exists (select 1 from sly_three_cable_zc_section T1 where T1.cs_id  =T.cs_id  and T1.zc_section_id is not null and T.group_type = v2_group_type);
commit;
insert into SLY_THREE_ROUTE
select T.* , '无穿管' ERROR from sly_three_cable_zc_section T
where T.zc_section_id is null
and T.Cable_Level<> '局内'
and T.Cable_Level<> '联络' and not exists
(select 1 from sly_three_cable_zc_section T1 where T.group_code = T1.group_code
and T1.cs_id = T.cs_id and T1.zc_section_id is not null)
and T.group_type = v2_group_type;
delete from sly_three_cable_zc_section T
where exists (select 1 from SLY_THREE_ROUTE T1 where T1.group_code = T.group_code
and T1.Fiber_Id = T.Fiber_Id and T1.Error = '无穿管' and T1.group_type = v2_group_type) and T.group_type = v2_group_type;
commit;
insert into sly_THREE_zc_section_glnum
select t.group_code , t.zc_section_code,t.group_type,count(distinct t.gl_code) glnum from sly_three_cable_zc_section t
where t.cable_level <> '局内' and t.group_type = v2_group_type
group by t.group_code , t.zc_section_code,t.group_type;
insert into SLY_THREE_ZC_ERROR
select t.* from sly_THREE_zc_section_glnum t
where t.group_type = v2_group_type and t.glnum >= 2;
insert into  SLY_THREE_ROUTE
select T.*,'同管道' error from sly_three_cable_zc_section T
where T.Cable_Level <> '局内' and T.group_type = v2_group_type
and exists(select 1 from SLY_THREE_ZC_ERROR T1 where T1.group_code = T.group_code
and T1.zc_section_code = T.zc_section_code and T1.group_type = v2_group_type
);
insert into SLY_THREE_ROUTE(group_id,group_code,group_name,device_id,device_code,device_name,eqp_spec,group_type,gl_id,gl_code,cs_id,cs_code,cs_name,fiber_id,fiber_code,fiber_name,cable_id,cable_code,cable_name,spec_id,net_type_id,cable_level,error)
select T.*,'同光缆' from sly_three_gl_group_cable T
where exists(select 1 from SLY_THREE_SECTION_ERR_TMP T1 where T1.group_code = T.group_code and T1.cs_id =
t.cs_id);
commit;
END$BODY$
  LANGUAGE plpgsql