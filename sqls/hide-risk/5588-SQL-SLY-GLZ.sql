---sr_gl_code 输入的光路编码
drop table if exists SLY_GL_GROUP_GL_INFO ;--select * from SLY_GL_GROUP_GL_INFO
create table SLY_GL_GROUP_GL_INFO as 
select t.*,t1.id as id,null sr_gl_code from gl_group_sly_detection_new t left join ${o3_res_schema}.cm_link t1 on t.gl_no = t1.code  and  t1.spec_id in ( 1132400006,1132300020 ) ;


drop table if exists SLY_GL_GROUP_GL_ERR ;--select * from SLY_GL_GROUP_GL_ERR
create table SLY_GL_GROUP_GL_ERR as--select * from SLY_GL_GROUP_GL_ERR
select t.*,'无光路' error  from SLY_GL_GROUP_GL_INFO t where t.id is null;

DELETE FROM SLY_GL_GROUP_GL_INFO t WHERE exists(SELECT 1 FROM SLY_GL_GROUP_GL_ERR t1 where t.gl_group_name = t1.gl_group_name);
commit;

drop table sly_gl_group_gl_info_1;
create table sly_gl_group_gl_info_1 as
select distinct t1.* ,  t3.id bf_id , t3.code bf_no
from sly_gl_group_gl_info t1 
inner join ${o3_res_schema}.cm_link gl on gl.code = t1.gl_no and gl.spec_id = 1132300020 
inner join ${o3_res_schema}.cm_link t3 on t3.id =gl.id
--join ${o3_res_schema}.cv_link tt on tt.link_id=t3.id and pkey='P_OPT_ROUTE_MODEL'and value='100172' order by t1.gl_group_name
;

drop table sly_gl_group_gl_info_2;
create table sly_gl_group_gl_info_2 as 
select distinct t.*,t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec from sly_gl_group_gl_info_1 t 
inner join ${o3_res_schema}.cr_link_link t5 on t.bf_id=t5.upper_link_id and t5.lower_link_spec_id = 1132300005 
inner join ${o3_res_schema}.cm_link t3 on t3.id = t5.lower_link_id
join ${o3_res_schema}.cr_link_link t6 on t5.lower_link_id=t6.upper_link_id and t6.lower_link_spec_id = 1132200002
join ${o3_res_schema}.cr_link_link t7 on t6.lower_link_id=t7.upper_link_id and t7.lower_link_spec_id = 1132300002
join ${o3_res_schema}.cr_link_link t8 on t7.lower_link_id=t8.upper_link_id and t8.lower_link_spec_id = 1132100010
join ${o3_res_schema}.cr_link_link t9 on t8.lower_link_id=t9.upper_link_id  and t9.lower_link_spec_id = 1131200002
join ${o3_res_schema}.cr_link_link t10 on t9.lower_link_id=t10.lower_link_id  and t10.upper_link_spec_id = 1132400006
join ${o3_res_schema}.cm_link t11 on t10.upper_link_id=t11.id ;
DELETE FROM SLY_GL_GROUP_GL_INFO T WHERE EXISTS (SELECT 1 FROM ${o3_res_schema}.cm_link t1 WHERE t.gl_no = t1.code  and  t1.spec_id in ( 1132300020 ) 
);
UPDATE SLY_GL_GROUP_GL_INFO T SET SR_GL_CODE=GL_NO;
insert into SLY_GL_GROUP_GL_INFO
select distinct t.city_name,t.gl_group_name,t.business_type,t.user_name,t.business_no,t.gl_code_bf,CURRENT_DATE,t.gl_id_bf,gl_no from sly_gl_group_gl_info_2 t where t.gl_id_bf is not null;


/*--送oss
delete from REPORTDEV.gl_group_data@REPORTDEV t 
where t.area_code = decode('${areaName}','${areaName}','ha.js.cn','连云港','lyg.js.cn','常州','cz.js.cn','泰州','tz.js.cn','宿迁','sq.js.cn','南京','nj.js.cn','镇江','zj.js.cn',
'无锡','wx.js.cn','苏州','sz.js.cn','南通','nt.js.cn','扬州','yz.js.cn','盐城','yc.js.cn','徐州','xz.js.cn') and t.eqp_spec = '光路组';
commit;
insert into REPORTDEV.gl_group_data@REPORTDEV
select distinct decode('${areaName}','${areaName}','ha.js.cn','连云港','lyg.js.cn','常州','cz.js.cn','泰州','tz.js.cn','宿迁','sq.js.cn','南京','nj.js.cn','镇江','zj.js.cn',
'无锡','wx.js.cn','苏州','sz.js.cn','南通','nt.js.cn','扬州','yz.js.cn','盐城','yc.js.cn','徐州','xz.js.cn')  area_code,
 '' ,t.gl_group_name , t.gl_no , sysdate,'光路组' from gl_group_sly_detection_new t;
commit;
--送集约化
delete from REPORTDEV.sly_group_data_circuit@REPORTDEV t 
where t.city_name = '${areaName}' and t.group_type = '光路组';
commit;
insert into REPORTDEV.sly_group_data_circuit@REPORTDEV (city_name,gl_group_name,gl_no，long_circuit_name,group_type,create_time)
select distinct '${areaName}', t.gl_group_name , t.gl_no ,t2.value ,'光路组', sysdate from gl_group_sly_detection_new t
left join lnk_business_link t1 on t1.no = t.gl_no and t1.res_spec_id = 1008
left join lnk_business_link_item t2 on t2.busi_link_id = t1.busi_link_id and t2.prop_spec_id = 51197;
commit;
*/
--select * from SLY_GL_GROUP_GL_INFO
-------------------------------------同光缆稽核begin------------------------------------
drop table if exists SLY_GL_GROUP_CABLE_INFO ; --select * from SLY_GL_GROUP_CABLE_INFO
create table SLY_GL_GROUP_CABLE_INFO as
select t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name,fiber.id fiber_id,fiber.code fiber_code ,fiber.name fiber_name, cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id,cn.net_type_id,pp.desc_china cable_level
from SLY_GL_GROUP_GL_INFO t

left join ${o3_res_schema}.cr_link_link cll1  on cll1.upper_link_id = t.id and cll1.spec_id=1133111310000
left join ${o3_res_schema}.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id and cll2.spec_id=1133511310000
left join ${o3_res_schema}.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
left join ${o3_res_schema}.cm_CABLE fiber on fiber.id = clc.cable_id
left join ${o3_res_schema}.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002  --光缆段
left join ${o3_res_schema}.cr_net_entity cne on cs.id=cne.entity_ID
left join ${o3_res_schema}.cm_net cn on cn.id = cne.NET_iD
left join ${o3_res_schema}.pm_pub_restriction pp on pp.serial_no = cn.net_type_id;

------计算光缆段内设备的光路数（去除局内光缆和联络光缆）
drop table if exists sly_gl_group_section_glnum ;--select * from sly_gl_group_section_glnum
create table sly_gl_group_section_glnum as
select t.gl_group_name ,t.cs_id, count(distinct t.SR_GL_CODE) glnum from SLY_GL_GROUP_CABLE_INFO t
where t.cable_level <> '局内' and t.cable_level <> '联络'
group by t.gl_group_name ,t.cs_id;

------计算导入的光路组数量
drop table if exists gl_group_sly_detection_glnum ;--select * from gl_group_sly_detection_glnum
create table gl_group_sly_detection_glnum as
select g.gl_group_name,count(distinct g.SR_GL_CODE) glnum from SLY_GL_GROUP_GL_INFO g group by g.gl_group_name;


-------比较数据的大小
drop table if exists SLY_GL_GROUP_SECTION_ERR_TMP ;--select * from SLY_GL_GROUP_SECTION_ERR_TMP
create table SLY_GL_GROUP_SECTION_ERR_TMP as
select t.* from sly_gl_group_section_glnum t
inner join gl_group_sly_detection_glnum t1 on t1.gl_group_name = t.gl_group_name and t1.glnum = t.glnum ;

-------同光缆数据
drop table if exists SLY_GL_GROUP_SECTION_ERR ;--select * from SLY_GL_GROUP_SECTION_ERR
create table SLY_GL_GROUP_SECTION_ERR as
select distinct T.*,'同光缆' ERR from SLY_GL_GROUP_CABLE_INFO T 
where exists (select 1 from SLY_GL_GROUP_SECTION_ERR_TMP 
where T.gl_group_name = gl_group_name and T.cs_id = cs_id);

------------------------------------同光缆end-----------------------------------------

----------------------------------检测同管道begin--------------------------------------
drop table if exists SLY_GL_GROUP_ZC_INFO ;--select * from SLY_GL_GROUP_ZC_INFO
create table SLY_GL_GROUP_ZC_INFO as
select distinct  T.*,--t1.cable_id,t1.pipeline_id,t2.parent_id,
t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from SLY_GL_GROUP_CABLE_INFO T
left join ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join ${o3_res_schema}.CM_PIPELINE t2 on t2.id = t1.pipeline_id
left join ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001  --去掉管孔
left join ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
union
select distinct  T.*,--t1.cable_id,t1.pipeline_id,t2.parent_id,
t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from SLY_GL_GROUP_CABLE_INFO T
left join ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join ${o3_res_schema}.CM_PIPELINE t2 on t2.id = t1.pipeline_id
left join ${o3_res_schema}.CM_PIPELINE t6 on t6.id = t2.parent_id
left join ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id !=1111100001  --去掉管孔
left join ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
union
select distinct  T.*,--t1.cable_id,t1.pipeline_id,t2.parent_id,
t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from SLY_GL_GROUP_CABLE_INFO T
left join ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
--left join ${o3_res_schema}.CM_PIPELINE t2 on t2.id = t1.pipeline_id
--left join ${o3_res_schema}.CM_PIPELINE t6 on t6.id = t2.parent_id
left join ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t1.pipeline_id and t5.spec_id !=1111100001  --去掉管孔
left join ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id

;
delete from SLY_GL_GROUP_ZC_INFO where (a_zc_eqp_code is null  or  z_zc_eqp_code is null) and zc_section_id is not null;
    
drop table if exists SLY_GL_GROUP_ROUTE  ;--select * from SLY_GL_GROUP_ROUTE;select * from SLY_GL_GROUP_ZC_INFO
create table SLY_GL_GROUP_ROUTE as
select T.* , '无穿管' ERROR from SLY_GL_GROUP_ZC_INFO T 
where T.zc_section_id is null 
and T.Cable_Level<> '局内'
and T.Cable_Level<> '联络' and not exists
(select 1 from SLY_GL_GROUP_ZC_INFO T1 where T.gl_group_name = T1.gl_group_name 
and T1.cs_id = T.cs_id and T1.zc_section_id is not null);

delete from SLY_GL_GROUP_ZC_INFO T 
where exists (select 1 from SLY_GL_GROUP_ROUTE T1 where T1.gl_group_name = T.gl_group_name
and T1.Fiber_Id = T.Fiber_Id and T1.Error = '无穿管');
commit;

-------删除单设备中包含的select * from ${o3_res_schema}.CM_FACILITY where notes LIKE '%单路由%' limit 100

--删除前10个人井，并标注为单路由的管道(必须是人井且还打了白名单)
DELETE FROM SLY_GL_GROUP_ZC_INFO T
 WHERE EXISTS (SELECT 1 FROM TMP_XHJ_PHY_ZC_EQPALL T2
         WHERE T.GL_NO = T2.GL_NO2 AND (T.a_zc_eqp_code = T2.BSE_EQP_code or T.z_zc_eqp_code = T2.BSE_EQP_code) )
   AND EXISTS (SELECT 1 FROM ${o3_res_schema}.CM_FACILITY T3
         WHERE (T.a_zc_eqp_code = T3.code or T.z_zc_eqp_code = T3.code ) AND T3.NOTES LIKE '%单路由%');
COMMIT;
           
------计算管道段内设备的光路数（去除局内光缆和联络光缆）
drop table if exists sly_GL_GROUP_zc_section_glnum ;
create table sly_GL_GROUP_zc_section_glnum as
select t.gl_group_name , t.zc_section_code,count(distinct t.SR_GL_CODE) glnum from SLY_GL_GROUP_ZC_INFO t
where t.cable_level <> '局内' and zc_section_code is not null
group by t.gl_group_name , t.zc_section_code;
-------比较数据大小
drop table if exists SLY_GL_GROUP_ZC_ERROR_TMP ;--select * from SLY_GL_GROUP_ZC_ERROR_TMP
create table SLY_GL_GROUP_ZC_ERROR_TMP as
select t.* from sly_GL_GROUP_zc_section_glnum t
inner join gl_group_sly_detection_glnum t1 on t1.gl_group_name = t.gl_group_name and t1.glnum = t.glnum ;

-------同管道数据
drop table if exists SLY_GL_GROUP_ZC_ERROR ;
create table SLY_GL_GROUP_ZC_ERROR as
select distinct T.*,'同管道' ERR from SLY_GL_GROUP_ZC_INFO T 
where exists (select 1 from SLY_GL_GROUP_ZC_ERROR_TMP t1
where T.gl_group_name = t1.gl_group_name and T.zc_section_code = t1.zc_section_code);
--/**/
-----同管道删除同光缆的位置
DELETE FROM SLY_GL_GROUP_ZC_ERROR T  --select * from SLY_GL_GROUP_ZC_ERROR
 WHERE EXISTS (SELECT 1  FROM SLY_GL_GROUP_SECTION_ERR
               WHERE T.gl_group_name = gl_group_name AND T.cs_id = cs_id);
COMMIT;

DELETE FROM SLY_GL_GROUP_ZC_ERROR T
 WHERE EXISTS (SELECT *
          FROM sly_sys_white_gl_group t1
         WHERE T.city_name = t1.city_name
           AND T.gl_group_name = t1.group_name
           AND T.zc_section_code = t1.zc_section_no
           AND t1.function_type = '光路组检测');
COMMIT;--select * from sly_sys_white_config
-----------------------------------检测同管道end---------------------------------------

insert into SLY_GL_GROUP_ROUTE (city_name,gl_group_name,business_type,user_name,business_no,gl_no,create_date,id,sr_gl_code,cs_id,cs_code,cs_name,fiber_id,fiber_code,fiber_name,cable_id,cable_code,cable_name,spec_id,net_type_id,cable_level,error)
select *--select * from SLY_GL_GROUP_ROUTE
--a.city_name,a.gl_group_name,a.business_type,a.user_name,a.business_no,a.gl_no,a.create_date,a.id,a.cs_id,a.cs_code,a.cs_name,a.fiber_id,a.fiber_code,a.fiber_name,a.cable_id,a.cable_code,cable_name,spec_id,net_type_id,cable_level,'','','','','',a.err 
from SLY_GL_GROUP_SECTION_ERR a;--select * from SLY_GL_GROUP_SECTION_ERR
commit;
--/**/
insert into SLY_GL_GROUP_ROUTE

select b.* from SLY_GL_GROUP_ZC_ERROR b;--select * from SLY_GL_GROUP_ZC_ERROR
commit;

----光路异常信息汇总到江苏库select * from SLY_GL_GROUP_GL_ERR_js
delete from SLY_GL_GROUP_GL_ERR_js t where t.city_name = '${areaName}' and t.update_time = CURRENT_DATE;
commit;
--/**/
 insert into SLY_GL_GROUP_GL_ERR_JS (city_name,gl_group_name,business_type,user_name,business_no,gl_no,create_date,id,sr_gl_code,error,update_time,is_type) SELECT t.*,CURRENT_DATE update_time,''is_type FROM SLY_GL_GROUP_GL_ERR t;
commit;
--/**/
----异常信息汇总到江苏库
delete from SLY_GL_GROUP_ROUTE_js t where t.city_name = '${areaName}' and t.update_time = CURRENT_DATE;
commit;

insert into SLY_GL_GROUP_ROUTE_JS(city_name,gl_group_name,business_type,user_name,business_no,gl_no,create_date,id,sr_gl_code,cs_id,cs_code,cs_name,fiber_id,fiber_code,fiber_name,cable_ID,cable_code,cable_name,spec_id,net_type_id,cable_level,zc_section_id,zc_section_code,zc_section_name,a_zc_eqp_code,z_zc_eqp_code,error,update_time,is_type)

SELECT t.*,CURRENT_DATE update_time,'' is_type FROM SLY_GL_GROUP_ROUTE t;
commit;
--/**/
-----设备汇总到江苏库 select * from gl_group_sly_detection_new_js
delete from gl_group_sly_detection_new_js t where t.city_name = '${areaName}';
commit;
insert into gl_group_sly_detection_new_js

SELECT t.*,CURRENT_DATE update_time FROM gl_group_sly_detection_new t;
commit;

delete FROM SLY_GL_GROUP_ROUTE_JS T WHERE T.Update_Time < CURRENT_DATE - 7;
commit;
 ANALYZE SLY_GL_GROUP_ROUTE_JS;
update SLY_GL_GROUP_ROUTE_JS t--select * from SLY_GL_GROUP_ROUTE_JS
   set is_type = '新增'
 where t.update_time = CURRENT_DATE and  not exists (SELECT 1
          FROM SLY_GL_GROUP_ROUTE_JS t1
         where t.gl_group_name = t1.gl_group_name
           and t.gl_no = t1.gl_no
           and t.error = t1.error
           and t1.update_time = CURRENT_DATE-1);
commit;

delete FROM SLY_GL_GROUP_GL_ERR_JS T WHERE T.Update_Time < CURRENT_DATE - 7;
commit;

--/**/
update SLY_GL_GROUP_GL_ERR_JS t
   set is_type = '新增'
 where t.update_time = CURRENT_DATE and  not exists (SELECT 1
          FROM SLY_GL_GROUP_GL_ERR_JS t1
         where t.gl_group_name = t1.gl_group_name
           and t.gl_no = t1.gl_no
           and t.error = t.error
           and t1.update_time = CURRENT_DATE-1);
commit;
--select * from gl_group_sly_detection_new_js
drop table if exists gl_group_sly_detection_new_num ;--select * from gl_group_sly_detection_new_num
create table gl_group_sly_detection_new_num as
SELECT t.city_name,t.business_type,count(distinct t.gl_group_name) num0
  FROM gl_group_sly_detection_new_js t--select * from gl_group_sly_detection_new_js
 group by t.city_name, t.business_type;

drop table if exists SLY_GL_GROUP_GL_ERR_JS_num1 ;--select * from SLY_GL_GROUP_GL_ERR_JS_num1
create table SLY_GL_GROUP_GL_ERR_JS_num1 as
SELECT t.city_name,t.business_type, count(distinct t.gl_group_name) num1
  FROM SLY_GL_GROUP_GL_ERR_JS t--select * from SLY_GL_GROUP_GL_ERR_JS
 where t.update_time = CURRENT_DATE
 group by t.city_name,t.business_type;

drop table if exists SLY_GL_GROUP_GL_ERR_JS_num2 ;
create table SLY_GL_GROUP_GL_ERR_JS_num2 as
SELECT t.city_name,t.business_type, count(distinct t.gl_group_name) num2
  FROM SLY_GL_GROUP_GL_ERR_JS t
 where t.update_time = CURRENT_DATE
 and t.is_type = '新增'
 group by t.city_name,t.business_type;
 
drop table if exists SLY_GL_GROUP_ROUTE_num3 ;
create table SLY_GL_GROUP_ROUTE_num3 as
select t.city_name,t.business_type, count(distinct t.gl_group_name) num3
  from SLY_GL_GROUP_ROUTE_JS t
  where t.update_time = CURRENT_DATE
 group by t.city_name,t.business_type;

drop table if exists SLY_GL_GROUP_ROUTE_num4 ;
create table SLY_GL_GROUP_ROUTE_num4 as
select t.city_name,t.business_type, count(distinct t.gl_group_name) num4
  from SLY_GL_GROUP_ROUTE_JS t
  where t.update_time = CURRENT_DATE
  and t.is_type = '新增'
 group by t.city_name,t.business_type;

--/**/

delete from SLY_GL_GROUP_ROUTE_TJ where update_time = CURRENT_DATE;
commit;


insert into SLY_GL_GROUP_ROUTE_TJ
select distinct a.city_name,a.business_type,
case when a.num0 is null then 0 else a.num0 end num0,
case when b.num1 is null then 0 else b.num1 end num1,
case when d.num2 is null then 0 else d.num2 end num2,
case when e.num3 is null then 0 else e.num3 end num3,
case when f.num4 is null then 0 else f.num4 end num4,
                CURRENT_DATE update_time
  from gl_group_sly_detection_new_num a
  left join SLY_GL_GROUP_GL_ERR_JS_num1 b
  on a.city_name = b.city_name
  and a.business_type = b.business_type
  left join SLY_GL_GROUP_GL_ERR_JS_num2 d
  on a.city_name = d.city_name
  and a.business_type = d.business_type
  left join SLY_GL_GROUP_ROUTE_num3 e
  on a.city_name = e.city_name
  and a.business_type = e.business_type
  left join SLY_GL_GROUP_ROUTE_num4 f
  on a.city_name = f.city_name
  and a.business_type = f.business_type
  where a.city_name is not null;
commit;


/*drop table if exists SLY_GL_GROUP_GL_INFO ;
drop table if exists SLY_GL_GROUP_GL_ERR ;

drop table if exists sly_gl_group_section_glnum ;
drop table if exists gl_group_sly_detection_glnum ;
drop table if exists SLY_GL_GROUP_SECTION_ERR_TMP ;
drop table if exists SLY_GL_GROUP_SECTION_ERR ;
drop table if exists SLY_GL_GROUP_ZC_INFO ;
drop table if exists SLY_GL_GROUP_ROUTE  ;
drop table if exists sly_GL_GROUP_zc_section_glnum ;
drop table if exists SLY_GL_GROUP_ZC_ERROR_TMP ;
drop table if exists SLY_GL_GROUP_ZC_ERROR ;
drop table if exists gl_group_sly_detection_new_num ;
drop table if exists SLY_GL_GROUP_GL_ERR_JS_num1 ;
drop table if exists SLY_GL_GROUP_ROUTE_num3 ;
drop table if exists SLY_GL_GROUP_GL_ERR_JS_num2 ;
drop table if exists SLY_GL_GROUP_ROUTE_num4 ;*/

