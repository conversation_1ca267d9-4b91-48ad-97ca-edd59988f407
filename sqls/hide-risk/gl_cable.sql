CREATE OR REPLACE PROCEDURE "wlyy_cz_sch"."gl_cable"("v_group_type" varchar)
 AS $BODY$
DECLARE
v1_group_type 	VARCHAR(255);
BEGIN
v1_group_type := v_group_type;
delete from sly_two_gl_group_cable sg where sg.group_type = v_group_type;
delete from sly_two_eqp_glnum sc where sc.group_type = v_group_type;
delete from sly_two_section_glnum sc where sc.group_type = v_group_type;
delete from SLY_TWO_SECTION_ERR_TMP sc where sc.group_type = v_group_type;
delete from SLY_TWO_SECTION_ERR sc where sc.group_type = v_group_type;
insert into sly_two_gl_group_cable
select t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name,fiber.id fiber_id,fiber.code fiber_code ,fiber.name fiber_name, cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id,cn.net_type_id,pp.desc_china cable_level from sly_gl_group_info t
left join res_cz_sch.cr_link_link cll1  on cll1.upper_link_id = t.gl_id and cll1.spec_id=1133111310000
left join res_cz_sch.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id and cll2.spec_id=1133511310000
left join res_cz_sch.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
left join res_cz_sch.cm_CABLE fiber on fiber.id = clc.cable_id
left join res_cz_sch.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002
left join res_cz_sch.cr_net_entity cne on cs.id=cne.entity_ID
left join res_cz_sch.cm_net cn on cn.id = cne.NET_iD
left join res_cz_sch.pm_pub_restriction pp on pp.serial_no = cn.net_type_id
where t.group_type = v1_group_type;
delete from sly_two_gl_group_cable T
where T.Fiber_Id is null
and T.group_type = v1_group_type
and exists (select 1 from sly_two_gl_group_cable T1 where T1.gl_code =T.gl_code and T1.Fiber_Id is not null and T.group_type = v1_group_type);
commit;
insert into sly_two_eqp_glnum
select t.group_code ,group_type, count(distinct t.gl_code) glnum from sly_two_gl_group_cable t
where  t.group_type = v1_group_type
group by t.group_type,t.group_code;
insert into sly_two_section_glnum
select t.group_type,t.group_code ,t.cs_id, count(distinct t.gl_code) glnum from sly_two_gl_group_cable t
where t.cable_level <> '局内' and t.cable_level <> '联络'
and t.group_type = v1_group_type
group by t.group_type,t.group_code ,t.cs_id;
insert into SLY_TWO_SECTION_ERR_TMP
select t.* from sly_two_section_glnum t
inner join sly_two_eqp_glnum t1 on t1.group_code = t.group_code and t1.glnum = t.glnum
where t.group_type = v1_group_type;
insert into SLY_TWO_SECTION_ERR
select T.*,'同光缆' ERR from sly_two_gl_group_cable T
where T.group_type = v1_group_type and
exists (select 1 from SLY_two_SECTION_ERR_TMP
where T.group_code = group_code and T.cs_id = cs_id and T.group_type = v1_group_type);
END$BODY$
  LANGUAGE plpgsql