
--select * from ${o3_res_schema}.pm_pub_restriction where old_SERIAL_NO in (10010401, 10010411, 10010412)
--select * from ${o3_res_schema}.cm_net T limit 100
DROP TABLE if exists TMP_SLY_SDHTRANS_INFO ;--select * from TMP_SLY_SDHTRANS_INFO where TRANS_NAME='越溪MSAP-吴中区检察院通信机房双光板34M'
CREATE TABLE TMP_SLY_SDHTRANS_INFO
AS 
  SELECT DISTINCT '${areaName}' city_name,
	T.id TRANS_SYSTEM_ID,
                  SA.id AREA_ID,
                  SA.NAME AS AREA_NAME,
                  SA.PARENT_ID PARENT_AREA_ID,
                  T.NAME AS TRANS_NAME,
									 T.code AS TRANS_NO,
                  PR.DESC_CHINA AS PROTECT_TYPE,
                PR2.DESC_CHINA AS NET_LEVEL
    FROM ${o3_res_schema}.cm_net T
    LEFT JOIN ${o3_res_schema}.RM_AREA SA ON T.LEAF_REGION_ID = SA.id
    LEFT JOIN ${o3_res_schema}.pm_pub_restriction PR ON T.PROTECT_WAY_ID = PR.SERIAL_NO
    LEFT JOIN ${o3_res_schema}.pm_pub_restriction PR2 ON T.NETWORK_LAYER_ID = PR2.SERIAL_NO
   WHERE T.SPEC_ID = 1212000003
     AND T.PROTECT_WAY_ID NOT IN (81720253, 101611, 81705057) AND  (T.NOTES NOT LIKE '%退网%' OR T.NOTES IS NULL) ;

CREATE INDEX TMP_SLY_SDHTRANS_INFOID ON TMP_SLY_SDHTRANS_INFO(TRANS_SYSTEM_ID);


--找到SDH传输系统的传输段、光路号
DROP TABLE  if exists TMP_SLY_SDHTRANS_2_GL_1 ;--select * from TMP_SLY_SDHTRANS_2_GL
CREATE TABLE TMP_SLY_SDHTRANS_2_GL_1
AS
  SELECT DISTINCT T.TRANS_SYSTEM_ID,
                  LBL1.code            AS DUAN_NO,
                  LLC.id AS GL_ID,
                  LLC.code            AS GL_NO
                  /*PEA.code             AS PEA_NO,
                  PMA.NAME           AS PEA_MODEL,
                  PEUA.code            AS PEA_BK,
                  PTPA.code            AS PEA_PORT,
                  PEZ.code             AS PEZ_NO,
                  PMZ.NAME           AS PEZ_MODEL,
                  PEUZ.code            AS PEZ_BK,
                  PTPZ.code            AS PEZ_PORT*/
									 
    FROM TMP_SLY_SDHTRANS_INFO T
  left  JOIN ${o3_res_schema}.CR_NET_ENTITY LTS ON T.TRANS_SYSTEM_ID = LTS.net_id  
  left   JOIN ${o3_res_schema}.cm_link LBL1  ON LTS.entity_id = LBL1.id 
   LEFT  JOIN ${o3_res_schema}.cr_link_link  LB2L1 ON LB2L1.upper_link_id = LBL1.id
  LEFT   JOIN ${o3_res_schema}.cm_link  LLC  ON LB2L1.lower_link_id = LLC.id  ;
	CREATE INDEX TMP_SLY_SDHTRANS_INFOIDsy ON TMP_SLY_SDHTRANS_2_GL_1(GL_ID);

	DROP TABLE  if exists TMP_SLY_SDHTRANS_2_GL_2 ;--select * from TMP_SLY_SDHTRANS_2_GL
CREATE TABLE TMP_SLY_SDHTRANS_2_GL_2
AS
   select t.*, PEA.code             AS PEA_NO,
                  PMA.NAME           AS PEA_MODEL,PEZ.code             AS PEZ_NO,
                  PMZ.NAME           AS PEZ_MODEL from TMP_SLY_SDHTRANS_2_GL_1 t  left  join  ${o3_res_schema}.cm_link llc on llc.id=t.gl_id
    LEFT JOIN ${o3_res_schema}.CM_DEVICE PEA ON LLC.a_physic_device_id = PEA.ID
    LEFT JOIN ${o3_res_schema}.DM_MODEL PMA ON PEA.MODEL_ID = PMA.ID
    LEFT JOIN ${o3_res_schema}.CM_DEVICE PEZ ON LLC.z_physic_device_id = PEZ.ID
    LEFT JOIN ${o3_res_schema}.DM_MODEL PMZ ON PEZ.MODEL_ID = PMZ.ID;
			CREATE INDEX TMP_SLY_SDHTRANS_2_GL_2sy ON TMP_SLY_SDHTRANS_2_GL_2(GL_ID);

		DROP TABLE  if exists TMP_SLY_SDHTRANS_2_GL ;--select * from TMP_SLY_SDHTRANS_2_GL
CREATE TABLE TMP_SLY_SDHTRANS_2_GL
AS select t.* , PEUA.code            AS PEA_BK,
                  PTPA.code            AS PEA_PORT,PEUZ.code            AS PEZ_BK,
                  PTPZ.code            AS PEZ_PORT from TMP_SLY_SDHTRANS_2_GL_2 t  left   join  ${o3_res_schema}.cm_link llc on llc.id=t.gl_id
    LEFT JOIN ${o3_res_schema}.cm_port PTPA  ON PTPA.ID = LLC.A_PORT_ID
    LEFT JOIN ${o3_res_schema}.CM_WARE PEUA ON PEUA.ID = PTPA.WARE_ID AND PEUA.SPEC_ID IN (1030400001)
    LEFT JOIN ${o3_res_schema}.cm_port PTPZ ON PTPZ.ID = LLC.Z_PORT_ID
    LEFT JOIN ${o3_res_schema}.CM_WARE PEUZ ON PEUZ.ID = PTPZ.WARE_ID AND PEUZ.SPEC_ID IN (1030400001);
UPDATE TMP_SLY_SDHTRANS_2_GL T SET PEA_PORT = REPLACE(T.PEA_PORT, '收', '');
COMMIT;
UPDATE TMP_SLY_SDHTRANS_2_GL T SET PEA_PORT = REPLACE(T.PEA_PORT, '发', '');
COMMIT;      
UPDATE TMP_SLY_SDHTRANS_2_GL T SET PEZ_PORT = REPLACE(T.PEZ_PORT, '收', '');
COMMIT;
UPDATE TMP_SLY_SDHTRANS_2_GL T SET PEZ_PORT = REPLACE(T.PEZ_PORT, '发', '');
COMMIT;

DROP TABLE  if exists TMP_SLY_SDHTRANS_2_GL_1 ;
CREATE TABLE TMP_SLY_SDHTRANS_2_GL_1
AS
SELECT DISTINCT T.* FROM TMP_SLY_SDHTRANS_2_GL T;

DROP TABLE  if exists TMP_SLY_SDHTRANS_2_GL ;
ALTER TABLE TMP_SLY_SDHTRANS_2_GL_1 RENAME TO TMP_SLY_SDHTRANS_2_GL;
CREATE INDEX TMP_SLY_SDHTRANS_2_GLID ON TMP_SLY_SDHTRANS_2_GL(TRANS_SYSTEM_ID);
CREATE INDEX TMP_SLY_SDHTRANS_2_GLNO ON TMP_SLY_SDHTRANS_2_GL(GL_NO);
  
---20180910 剔除sdh传输段有光路，但是统计到 无光路的情况。
DELETE FROM   TMP_SLY_SDHTRANS_2_GL t1 WHERE t1.gl_id IS NULL AND  EXISTS(
SELECT 1 FROM TMP_SLY_SDHTRANS_2_GL t2 WHERE t1.trans_system_id=t2.trans_system_id 
AND t1.duan_no=t2.duan_no AND t2.gl_id IS NOT NULL);  
COMMIT;
delete from TMP_SLY_SDHTRANS_2_GL t1 where t1.DUAN_NO is null and exists(SELECT 1 FROM TMP_SLY_SDHTRANS_2_GL t2 WHERE t1.trans_system_id=t2.trans_system_id 
 AND t2.DUAN_NO IS NOT NULL);

--插入省级传输系统全量清单
drop table if exists RLT_SLY_SDHTRANS_GL_KPI;--select * from RLT_SLY_SDHTRANS_GL_KPI
create table RLT_SLY_SDHTRANS_GL_KPI as
  SELECT '${areaName}' city_name,
         M.TRANS_SYSTEM_ID,
         M.AREA_ID,
         M.AREA_NAME,
         M.PARENT_AREA_ID,
         M.TRANS_NAME,
         M.TRANS_NO,
         M.PROTECT_TYPE,
         M.NET_LEVEL,
         T.DUAN_NO,
         T.GL_NO,
         T.PEA_NO,
         T.PEA_MODEL,
         T.PEZ_NO,
         T.PEZ_MODEL
    FROM TMP_SLY_SDHTRANS_INFO M
    JOIN TMP_SLY_SDHTRANS_2_GL T  ON M.TRANS_SYSTEM_ID = T.TRANS_SYSTEM_ID;
COMMIT;
--创建临时表(重复支撑管道率)--找到单光路、无光路
DROP TABLE   if exists TMP_SLY_SDHTRANS_ID ;--select * from TMP_SLY_SDHTRANS_ID
CREATE TABLE TMP_SLY_SDHTRANS_ID
AS
SELECT DISTINCT TRANS_SYSTEM_ID, ERR
  FROM (SELECT TRANS_SYSTEM_ID, '系统内光路数为1' AS ERR
          FROM TMP_SLY_SDHTRANS_2_GL T
         WHERE GL_ID IS NOT NULL
           AND NOT EXISTS
         (SELECT TRANS_SYSTEM_ID, '' AS ERRf
                  FROM TMP_SLY_SDHTRANS_2_GL
                 WHERE GL_ID IS NULL
                   AND DUAN_NO IS NOT NULL
                   AND T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID)
         GROUP BY TRANS_SYSTEM_ID
        HAVING COUNT(DISTINCT GL_ID) <= 1
        UNION
        SELECT TRANS_SYSTEM_ID, '' AS ERR
          FROM TMP_SLY_SDHTRANS_2_GL
         WHERE GL_ID IS NULL  AND DUAN_NO IS NOT NULL
        UNION
        SELECT TRANS_SYSTEM_ID, '系统无传输段' AS ERR
          FROM TMP_SLY_SDHTRANS_2_GL
         WHERE DUAN_NO IS NULL) a;
     
--单光路、无光路结果表

DROP TABLE  if exists RLT_SLY_SDHTRANS_NOGL_KPI ;
CREATE TABLE RLT_SLY_SDHTRANS_NOGL_KPI
AS
  SELECT '${areaName}' city_name, M.TRANS_SYSTEM_ID,
                  M.AREA_ID,
                  M.AREA_NAME,
                  M.PARENT_AREA_ID,
                  M.TRANS_NAME,
                  M.TRANS_NO,
                  M.PROTECT_TYPE,
                  M.NET_LEVEL,
                  T.DUAN_NO,
                  T.GL_NO,
                  T.PEA_NO,
                  T.PEA_MODEL,
                  T.PEZ_NO,
                  T.PEZ_MODEL,
                  MID.ERR
    FROM TMP_SLY_SDHTRANS_INFO M
    JOIN TMP_SLY_SDHTRANS_2_GL T
      ON M.TRANS_SYSTEM_ID = T.TRANS_SYSTEM_ID
    JOIN TMP_SLY_SDHTRANS_ID MID
      ON M.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID;
UPDATE RLT_SLY_SDHTRANS_NOGL_KPI T--select * from RLT_SLY_SDHTRANS_NOGL_KPI
   SET ERR = '传输段缺光路'
 WHERE T.GL_NO IS NULL
   AND T.DUAN_NO IS NOT NULL;
COMMIT;

DROP TABLE  if exists TMP_SLY_SDHTRANS_ID ;  

-- 去除异常系统的影响
DELETE FROM TMP_SLY_SDHTRANS_2_GL T WHERE T.DUAN_NO IS NULL;
COMMIT;
DELETE FROM TMP_SLY_SDHTRANS_2_GL T WHERE T.GL_NO IS NULL;
COMMIT;
DELETE FROM TMP_SLY_SDHTRANS_2_GL T
 WHERE T.GL_NO IS NOT NULL
   AND EXISTS (SELECT 1
          FROM RLT_SLY_SDHTRANS_NOGL_KPI
         WHERE TRANS_SYSTEM_ID = T.TRANS_SYSTEM_ID
           AND T.GL_NO = GL_NO
           AND ERR IS NOT NULL);
COMMIT;

--同板卡，同一系统内同一设备的出入两条光路不可以在同一板卡上（传输段异常的不算做在内）
--同一传输系统内出现的设备，不应该有同一块板卡使用多个端口的情况
DROP TABLE  if exists TMP_SLY_SDHTRANS_BK ;
CREATE TABLE TMP_SLY_SDHTRANS_BK
AS 
SELECT TMP.TRANS_SYSTEM_ID,
       TMP.PE_NO,
       TMP.PE_BK,
       COUNT(DISTINCT PE_PORT) AS PORT_NUM
  FROM (SELECT T.TRANS_SYSTEM_ID,
               T.PEA_NO          AS PE_NO,
               T.PEA_BK          AS PE_BK,
               T.PEA_PORT        AS PE_PORT
          FROM TMP_SLY_SDHTRANS_2_GL T
         WHERE T.PEA_BK IS NOT NULL
        UNION
        SELECT T.TRANS_SYSTEM_ID,
               T.PEZ_NO          AS PE_NO,
               T.PEZ_BK          AS PE_BK,
               T.PEZ_PORT        AS PE_PORT
          FROM TMP_SLY_SDHTRANS_2_GL T
         WHERE T.PEZ_BK IS NOT NULL) TMP
 WHERE TMP.PE_BK IS NOT NULL
 GROUP BY TMP.TRANS_SYSTEM_ID, TMP.PE_NO, TMP.PE_BK
HAVING COUNT(DISTINCT PE_PORT) > 1;


DROP TABLE  if exists RLT_SLY_SDHTRANS_BK ;
CREATE TABLE RLT_SLY_SDHTRANS_BK
AS 
SELECT '${areaName}' city_name,T.TRANS_SYSTEM_ID,
       T.AREA_ID,
       T.AREA_NAME,
       T.PARENT_AREA_ID,
       T.TRANS_NAME,
       T.TRANS_NO,
       T.PROTECT_TYPE,
       T.NET_LEVEL,
       M.DUAN_NO,
       M.GL_ID,
       M.GL_NO,
       M.PEA_NO,
       M.PEA_MODEL,
       M.PEA_BK,
       M.PEA_PORT,
       M.PEZ_NO,
       M.PEZ_MODEL,
       M.PEZ_BK,
       M.PEZ_PORT,
       M.PEA_NO||'设备同板卡' AS ERR
  FROM TMP_SLY_SDHTRANS_INFO T
  JOIN TMP_SLY_SDHTRANS_2_GL M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
 WHERE EXISTS (SELECT 1
          FROM TMP_SLY_SDHTRANS_BK
         WHERE M.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
           AND M.PEA_NO = PE_NO
           AND M.PEA_BK = PE_BK)
UNION
SELECT '${areaName}' city_name,T.TRANS_SYSTEM_ID,
       T.AREA_ID,
       T.AREA_NAME,
       T.PARENT_AREA_ID,
       T.TRANS_NAME,
       T.TRANS_NO,
       T.PROTECT_TYPE,
       T.NET_LEVEL,
       M.DUAN_NO,
       M.GL_ID,
       M.GL_NO,
       M.PEA_NO,
       M.PEA_MODEL,
       M.PEA_BK,
       M.PEA_PORT,
       M.PEZ_NO,
       M.PEZ_MODEL,
       M.PEZ_BK,
       M.PEZ_PORT,
       M.PEZ_NO||'设备同板卡' AS ERR
  FROM TMP_SLY_SDHTRANS_INFO T
  JOIN TMP_SLY_SDHTRANS_2_GL M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
 WHERE EXISTS (SELECT 1
          FROM TMP_SLY_SDHTRANS_BK
         WHERE M.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
           AND M.PEZ_NO = PE_NO
           AND M.PEZ_BK = PE_BK);
INSERT INTO RLT_SLY_SDHTRANS_BK   --select * from RLT_SLY_SDHTRANS_BK 
SELECT '${areaName}' city_name,T.TRANS_SYSTEM_ID,
       T.AREA_ID,
       T.AREA_NAME,
       T.PARENT_AREA_ID,
       T.TRANS_NAME,
       T.TRANS_NO,
       T.PROTECT_TYPE,
       T.NET_LEVEL,
       M.DUAN_NO,
       M.GL_ID,
       M.GL_NO,
       M.PEA_NO,
       M.PEA_MODEL,
       M.PEA_BK,
       M.PEA_PORT,
       M.PEZ_NO,
       M.PEZ_MODEL,
       M.PEZ_BK,
       M.PEZ_PORT,
       '端口未放置于板卡、子卡' AS ERR     
  FROM TMP_SLY_SDHTRANS_INFO T
  JOIN TMP_SLY_SDHTRANS_2_GL M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID 
    AND (M.PEA_BK IS NULL OR M.PEZ_BK IS NULL);
COMMIT;
           
/*--去除同板卡白名单                   
DELETE FROM RLT_SLY_SDHTRANS_BK T
 WHERE EXISTS (SELECT 1
          FROM REPORTDEV.MID_NOC_SDH_WHITE_EQPMODEL@REPORTDEV 
         WHERE T.Pea_Model = MODEL_NAME
           AND AREA =  '${areaName}');
COMMIT;
DELETE FROM RLT_SLY_SDHTRANS_BK T
 WHERE EXISTS (SELECT 1
          FROM REPORTDEV.MID_NOC_SDH_WHITE_EQPMODEL@REPORTDEV 
         WHERE T.Pez_Model = MODEL_NAME
           AND AREA =  '${areaName}');
COMMIT;*/
DROP TABLE if exists  TMP_SLY_SDHTRANS_BK ;
 
--找到光路经过的光缆
--找到光路经过的光缆
	 DROP TABLE if exists  TMP_SLY_SDHTRANS_2_SECTION_1 ;
CREATE TABLE TMP_SLY_SDHTRANS_2_SECTION_1
AS
SELECT DISTINCT T.TRANS_SYSTEM_ID,
                T.DUAN_NO,
                T.GL_ID,
                T.GL_NO,
                CS.id  CBL_SECT_ID,
                CS.code             AS SECTION_NO
               -- CC.code             AS CABLE_NO,
               -- PR.DESC_CHINA     AS CABLE_LEVEL
								
  FROM TMP_SLY_SDHTRANS_2_GL T
 LEFT JOIN ${o3_res_schema}.cm_link LBL
    ON T.GL_NO = LBL.code and spec_id = 1131200002
  LEFT JOIN ${o3_res_schema}.cr_link_link  LBL2L
    ON LBL.id = LBL2L.upper_link_id 
LEFT JOIN ${o3_res_schema}.CR_LINK_CABLE  LPLC
    ON LBL2L.lower_link_id = LPLC.link_id 
  LEFT JOIN ${o3_res_schema}.cm_CABLE  CF
    ON LPLC.cable_id = CF.id 
  LEFT JOIN ${o3_res_schema}.cm_CABLE  CS
    ON CF.parent_id  = CS.id  and cs.spec_id = 1121000002;  --光缆段
		CREATE INDEX TMP_SLY_SDHTRANS_2_SECTION_1sy ON TMP_SLY_SDHTRANS_2_SECTION_1(CBL_SECT_ID);
			 DROP TABLE if exists  TMP_SLY_SDHTRANS_2_SECTION ;
CREATE TABLE TMP_SLY_SDHTRANS_2_SECTION
AS select  cs.*, CC.code             AS CABLE_NO,
                PR.DESC_CHINA     AS CABLE_LEVEL from TMP_SLY_SDHTRANS_2_SECTION_1 cs
  LEFT JOIN ${o3_res_schema}.cr_net_entity  CC2S
    ON CC2S.entity_ID = CS.CBL_SECT_ID
  LEFT JOIN ${o3_res_schema}.cm_net  CC
    ON CC2S.NET_iD = CC.id 
  LEFT JOIN ${o3_res_schema}.pm_pub_restriction  PR
    ON CC.net_type_id = PR.serial_no ;
CREATE INDEX TMP_SLY_SDHTRANS_2_SECTION_ID ON TMP_SLY_SDHTRANS_2_SECTION(TRANS_SYSTEM_ID); 
CREATE INDEX TMP_SLY_SDHTRANS_2_SECTID ON TMP_SLY_SDHTRANS_2_SECTION(CBL_SECT_ID); 

--去掉无效数据
DELETE FROM TMP_SLY_SDHTRANS_2_SECTION T
 WHERE T.SECTION_NO IS NULL
   AND EXISTS (SELECT 1
          FROM TMP_SLY_SDHTRANS_2_SECTION
         WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
           AND GL_ID = T.GL_ID
           AND SECTION_NO IS NOT NULL);
COMMIT;

DROP TABLE  if exists MID_SLY_SDHTRANS_2_SECTION ;
CREATE TABLE MID_SLY_SDHTRANS_2_SECTION
AS      
SELECT *
  FROM TMP_SLY_SDHTRANS_2_SECTION T
 WHERE T.SECTION_NO IS NOT NULL;
DELETE FROM MID_SLY_SDHTRANS_2_SECTION T
 WHERE T.CABLE_LEVEL IN ('局内', '联络');
COMMIT;
--计算出现的光路数量
DROP TABLE  if exists TMP_SLY_SDHTRANS_2_SECTNUM ;
CREATE TABLE TMP_SLY_SDHTRANS_2_SECTNUM
AS 
SELECT T.TRANS_SYSTEM_ID, COUNT(DISTINCT GL_NO) AS GL_NUM
  FROM MID_SLY_SDHTRANS_2_SECTION T
 GROUP BY T.TRANS_SYSTEM_ID;

--计算光缆段上出现的光路数量
DROP TABLE if exists  MID_SLY_SDHTRANS_2_SECTNUM ;
CREATE TABLE MID_SLY_SDHTRANS_2_SECTNUM
AS 
SELECT T.TRANS_SYSTEM_ID, T.SECTION_NO, COUNT(DISTINCT GL_NO) AS GL_NUM
  FROM MID_SLY_SDHTRANS_2_SECTION T
 GROUP BY T.TRANS_SYSTEM_ID, T.SECTION_NO;

--缆段重复、光路无光缆的结果表

DROP TABLE if exists  RLT_SLY_SDHTRANS_2_SECTION ;
CREATE TABLE RLT_SLY_SDHTRANS_2_SECTION
AS             
SELECT '${areaName}' city_name,T.TRANS_SYSTEM_ID,
       T.AREA_ID,
       T.AREA_NAME,
       T.PARENT_AREA_ID,
       T.TRANS_NAME,
       T.TRANS_NO,
       T.PROTECT_TYPE,
       T.NET_LEVEL,
       M.DUAN_NO,
       M.GL_ID,
       M.GL_NO,
       M.CBL_SECT_ID,
       M.SECTION_NO,
       M.CABLE_NO,
       M.CABLE_LEVEL,
       '光缆段重复' AS ERR
  FROM TMP_SLY_SDHTRANS_INFO T
  JOIN TMP_SLY_SDHTRANS_2_SECTION M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
  JOIN MID_SLY_SDHTRANS_2_SECTNUM X
    ON M.TRANS_SYSTEM_ID = X.TRANS_SYSTEM_ID
   AND M.SECTION_NO = X.SECTION_NO
   AND X.GL_NUM  >=2;
INSERT INTO RLT_SLY_SDHTRANS_2_SECTION
SELECT '${areaName}' city_name,T.TRANS_SYSTEM_ID,
       T.AREA_ID,
       T.AREA_NAME,
       T.PARENT_AREA_ID,
       T.TRANS_NAME,
       T.TRANS_NO,
       T.PROTECT_TYPE,
       T.NET_LEVEL,
       M.DUAN_NO,
       M.GL_ID,
       M.GL_NO,
       M.CBL_SECT_ID,
       M.SECTION_NO,
       M.CABLE_NO,
       M.CABLE_LEVEL,
       '光路无光缆' AS ERR
  FROM TMP_SLY_SDHTRANS_INFO T
  JOIN TMP_SLY_SDHTRANS_2_SECTION M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   AND M.SECTION_NO IS NULL; 
COMMIT;
DROP TABLE if exists  MID_SLY_SDHTRANS_2_SECTNUM ;
DROP TABLE if exists  TMP_SLY_SDHTRANS_2_SECTNUM ;
<#if areaCode = 'nj'>
delete from RLT_SLY_SDHTRANS_2_SECTION t where exists(select 1 from sly_sdh_cable_section_white a where t.city_name = a.city_name and t.trans_no = a.trans_no ) and t.err = '光路无光缆';
</#if>


--去除光路光缆重复对于管线的影响
DELETE FROM MID_SLY_SDHTRANS_2_SECTION T
 WHERE EXISTS (SELECT 1
          FROM RLT_SLY_SDHTRANS_2_SECTION
         WHERE TRANS_SYSTEM_ID = T.TRANS_SYSTEM_ID
           AND T.SECTION_NO = SECTION_NO);
COMMIT;

--找到管线
DROP TABLE  if exists TMP_SLY_SDHTRANS_2_ZC ;
CREATE TABLE TMP_SLY_SDHTRANS_2_ZC
AS
SELECT DISTINCT T.TRANS_SYSTEM_ID,
                T.DUAN_NO,
                T.GL_NO,
                T.CBL_SECT_ID,
                BS.id BSE_SECT_ID
  FROM MID_SLY_SDHTRANS_2_SECTION T
left join  ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.CBL_SECT_ID --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE BS on BS.id = t2.parent_id and BS.spec_id not in(1111100001 ,1111100000 ,1111100004)--去掉管孔
union
SELECT DISTINCT T.TRANS_SYSTEM_ID,
                T.DUAN_NO,
                T.GL_NO,
                T.CBL_SECT_ID,
                BS.id BSE_SECT_ID
  FROM MID_SLY_SDHTRANS_2_SECTION T
left join  ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.CBL_SECT_ID --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t6 on t6.id =t2.parent_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE BS on BS.id = t6.parent_id and BS.spec_id not in(1111100001 ,1111100000 ,1111100004) --去掉管孔
union
SELECT DISTINCT T.TRANS_SYSTEM_ID,
                T.DUAN_NO,
                T.GL_NO,
                T.CBL_SECT_ID,
                BS.id BSE_SECT_ID
  FROM MID_SLY_SDHTRANS_2_SECTION T
left join  ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.CBL_SECT_ID --光缆段
left join   ${o3_res_schema}.CM_PIPELINE BS on BS.id = pipeline_id  and BS.spec_id not in(1111100001 ,1111100000 ,1111100004) --去掉管孔
;
CREATE INDEX TMP_SLY_SDHTRANS_2_ZCsy ON TMP_SLY_SDHTRANS_2_ZC(BSE_SECT_ID);

--去除无效数据 
DELETE FROM TMP_SLY_SDHTRANS_2_ZC T
 WHERE T.BSE_SECT_ID IS NULL
   AND EXISTS (SELECT 1
          FROM TMP_SLY_SDHTRANS_2_ZC
         WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
           AND T.GL_NO = GL_NO
           AND T.CBL_SECT_ID = CBL_SECT_ID
           AND BSE_SECT_ID IS NOT NULL);
COMMIT;

--获取管道两端支撑信息
DROP TABLE if exists  TMP_SLY_SDHTRANS_2_ZCEQP ;
CREATE TABLE TMP_SLY_SDHTRANS_2_ZCEQP
AS
SELECT BS.id BSE_SECT_ID, BEQ.id BSE_EQP_ID
  FROM ${o3_res_schema}.CM_PIPELINE BS
  left join   ${o3_res_schema}.cm_facility BEQ on BEQ.id = BS.a_facility_id
 WHERE EXISTS (SELECT 1
          FROM TMP_SLY_SDHTRANS_2_ZC
         WHERE BS.id  = BSE_SECT_ID
           AND BSE_SECT_ID IS NOT NULL)
   
 GROUP BY BS.id,BEQ.id
 union
 SELECT BS.id BSE_SECT_ID, BEQ.id BSE_EQP_ID
  FROM ${o3_res_schema}.CM_PIPELINE BS
  left join   ${o3_res_schema}.cm_facility BEQ on BEQ.id = BS.z_facility_id
 WHERE EXISTS (SELECT 1
          FROM TMP_SLY_SDHTRANS_2_ZC
         WHERE BS.id  = BSE_SECT_ID
           AND BSE_SECT_ID IS NOT NULL)
   
 GROUP BY BS.id,BEQ.id
 
 
 ;
 
/*--去掉单路由局前井白名单
DROP TABLE TMP_SLY_SDHTRANS_2_ZCEQP1 purge;
CREATE TABLE TMP_SLY_SDHTRANS_2_ZCEQP1
AS
SELECT DISTINCT T.BSE_SECT_ID
  FROM TMP_SLY_SDHTRANS_2_ZCEQP T
 WHERE EXISTS
 (SELECT 1 FROM TMP_XHJ_RJ_KPI WHERE T.BSE_EQP_ID = BSE_EQP_ID);
 
  <#if areaCode= 'WX'> 
--20170731 BY CHUXINRONG 特殊处理白名单（TMP_XHJ_RJ_KPI_CXR）手动添加
DROP TABLE TMP_SLY_SDHTRANS_2_ZCEQP1_CXR purge;
CREATE TABLE TMP_SLY_SDHTRANS_2_ZCEQP1_CXR
AS
SELECT DISTINCT T.BSE_SECT_ID
  FROM TMP_SLY_SDHTRANS_2_ZCEQP T
 WHERE EXISTS
 (SELECT 1 FROM TMP_XHJ_RJ_KPI_CXR WHERE T.BSE_EQP_ID = BSE_EQP_ID);
 INSERT INTO TMP_SLY_SDHTRANS_2_ZCEQP1 SELECT * FROM TMP_SLY_SDHTRANS_2_ZCEQP1_CXR;
 COMMIT;
  </#if>
 

DELETE FROM TMP_SLY_SDHTRANS_2_ZCEQP T
 WHERE EXISTS
 (SELECT 1 FROM TMP_SLY_SDHTRANS_2_ZCEQP1 WHERE T.BSE_SECT_ID = BSE_SECT_ID);
COMMIT;
DELETE FROM TMP_SLY_SDHTRANS_2_ZC T
 WHERE EXISTS
 (SELECT 1 FROM TMP_SLY_SDHTRANS_2_ZCEQP1 WHERE T.BSE_SECT_ID = BSE_SECT_ID);
COMMIT;
DROP TABLE TMP_SLY_SDHTRANS_2_ZCEQP1 purge;
*/

 --20170822 by chuxinrong 修改sdh白名单规则 增加局前10个人井屏蔽;
 --**BEGIN--
 --设备对应的管线，人井信息
DROP TABLE  if exists TMP_SLY_SDHTRANS_2_ZC_CXR ;
CREATE TABLE TMP_SLY_SDHTRANS_2_ZC_CXR AS
SELECT ZC.*,BE.id BSE_EQP_ID,BE.code AS BSE_NO,BE.NOTES,PRS.res_type AS BSE_SPEC  FROM TMP_SLY_SDHTRANS_2_ZC  ZC
       LEFT JOIN ${o3_res_schema}.CM_PIPELINE  BEF  --支撑设施展开面
       ON ZC.BSE_SECT_ID=BEF.id
       LEFT JOIN ${o3_res_schema}.cm_facility  BE  --支撑设施
       ON BEF.a_facility_id=BE.id
       LEFT JOIN ${o3_res_schema}.pm_pub_res_type PRS--select * from ${o3_res_schema}.cm_facility limit 100
       ON BE.SPEC_ID = PRS.res_type_id
union			 
	SELECT ZC.*,BE.id BSE_EQP_ID,BE.code AS BSE_NO,BE.NOTES,PRS.res_type AS BSE_SPEC  FROM TMP_SLY_SDHTRANS_2_ZC  ZC
       LEFT JOIN ${o3_res_schema}.CM_PIPELINE  BEF  --支撑设施展开面
       ON ZC.BSE_SECT_ID=BEF.id
       LEFT JOIN ${o3_res_schema}.cm_facility  BE  --支撑设施
       ON BEF.z_facility_id=BE.id
       LEFT JOIN ${o3_res_schema}.pm_pub_res_type PRS--select * from ${o3_res_schema}.cm_facility limit 100
       ON BE.SPEC_ID = PRS.res_type_id		 
			 
			 
			 ;
--找到局端用户端人井白名单
DROP TABLE  if exists  TMP_SLY_SDHTRANS_2_ZC_CXR_1 ;
CREATE TABLE TMP_SLY_SDHTRANS_2_ZC_CXR_1
AS
 SELECT DISTINCT TRANS_SYSTEM_ID, GL_NO, BSE_EQP_ID,T.BSE_NO
   FROM TMP_SLY_SDHTRANS_2_ZC_CXR T
  /*where t.NOTES Like '%单路由%'*/
  GROUP BY TRANS_SYSTEM_ID, GL_NO, BSE_EQP_ID, BSE_SPEC,T.BSE_NO
 HAVING COUNT(DISTINCT BSE_SECT_ID) = 1;

 <#if areaCode = 'nj'>
insert into TMP_SLY_SDHTRANS_2_ZC_CXR_1
select t.phy_eqp_id,null gl_no,t.bse_eqp_id,t.bse_eqp_no from sly_sys_white_config t where t.white_type = '局前井白名单' and t.function_type = 'SDH传输系统' ;
</#if>

--拼接10次路由设施
DROP TABLE  if exists  TMP_SLY_SDHTRANS_2_ZC_CXR_2 ;
CREATE TABLE TMP_SLY_SDHTRANS_2_ZC_CXR_2
AS 
SELECT * FROM  TMP_SLY_SDHTRANS_2_ZC_CXR T1 WHERE EXISTS (
SELECT 1 FROM TMP_SLY_SDHTRANS_2_ZC_CXR_1 T2 WHERE T1.TRANS_SYSTEM_ID =T2.TRANS_SYSTEM_ID 
AND T1.BSE_EQP_ID =T2.BSE_EQP_ID ) and  t1.NOTES Like '%单路由%' ;
--SELECT * FROM TMP_SLY_SDHTRANS_2_ZC_CXR_2;
---第一个
CREATE INDEX TMP_SLY_SDHTRANS_2_ZC_CXRsy ON TMP_SLY_SDHTRANS_2_ZC_CXR(TRANS_SYSTEM_ID);
CREATE INDEX TMP_SLY_SDHTRANS_2_ZC_CXRsy1 ON TMP_SLY_SDHTRANS_2_ZC_CXR(GL_NO);
CREATE INDEX TMP_SLY_SDHTRANS_2_ZC_CXRsy2 ON TMP_SLY_SDHTRANS_2_ZC_CXR(BSE_SECT_ID);
CREATE INDEX TMP_SLY_SDHTRANS_2_ZC_CXRsy3 ON TMP_SLY_SDHTRANS_2_ZC_CXR(BSE_EQP_ID);

CREATE INDEX TMP_SLY_SDHTRANS_2_ZC_CXR_2ys1 ON TMP_SLY_SDHTRANS_2_ZC_CXR_2 (GL_NO);
CREATE INDEX TMP_SLY_SDHTRANS_2_ZC_CXR_2ys2 ON TMP_SLY_SDHTRANS_2_ZC_CXR_2 (BSE_SECT_ID);
CREATE INDEX TMP_SLY_SDHTRANS_2_ZC_CXR_2ys3 ON TMP_SLY_SDHTRANS_2_ZC_CXR_2 (BSE_EQP_ID);
CREATE INDEX TMP_SLY_SDHTRANS_2_ZC_CXR_2ys4 ON TMP_SLY_SDHTRANS_2_ZC_CXR_2 (TRANS_SYSTEM_ID);
drop table TMP_SDH_ZC_EQPALL_1_1_1;
CREATE TABLE TMP_SDH_ZC_EQPALL_1_1_1 AS SELECT T.TRANS_SYSTEM_ID, T.GL_NO, M.BSE_EQP_ID AS T1_BSE, M.BSE_NO AS T1_BSE_NO,t.BSE_SECT_ID --, MID.BSE_SECT_ID 
FROM TMP_SLY_SDHTRANS_2_ZC_CXR_2 T 
LEFT JOIN TMP_SLY_SDHTRANS_2_ZC_CXR M ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID AND T.GL_NO = M.GL_NO AND T.BSE_SECT_ID = M.BSE_SECT_ID AND T.BSE_EQP_ID <> M.BSE_EQP_ID ;
drop table TMP_SDH_ZC_EQPALL_1_1;
CREATE TABLE TMP_SDH_ZC_EQPALL_1_1 as select T.TRANS_SYSTEM_ID, T.GL_NO, t.T1_BSE, t.T1_BSE_NO,MID.BSE_SECT_ID  from TMP_SDH_ZC_EQPALL_1_1_1 t
LEFT JOIN TMP_SLY_SDHTRANS_2_ZC_CXR MID ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID AND T.GL_NO = MID.GL_NO AND T.BSE_SECT_ID <> MID.BSE_SECT_ID AND t.T1_BSE = MID.BSE_EQP_ID;
drop table TMP_SDH_ZC_EQPALL_1_1_1;	 
	 drop table  if exists   TMP_SDH_ZC_EQPALL_1;
	 create table TMP_SDH_ZC_EQPALL_1 AS 
	 select TRANS_SYSTEM_ID,
                GL_NO,
           
                 T1_BSE,
                 T1_BSE_NO,
								
                BSE_SECT_ID
								from TMP_SDH_ZC_EQPALL_1_1
	 group by TRANS_SYSTEM_ID,
                GL_NO,
           
                 T1_BSE,
                 T1_BSE_NO,
								
                BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_1ys1 ON TMP_SDH_ZC_EQPALL_1 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_1ys2 ON TMP_SDH_ZC_EQPALL_1 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_1ys3 ON TMP_SDH_ZC_EQPALL_1 (T1_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_1ys4 ON TMP_SDH_ZC_EQPALL_1 (TRANS_SYSTEM_ID);

--第二个   
DROP TABLE  if exists TMP_SDH_ZC_EQPALL_2 ;---select count(*) from TMP_SDH_ZC_EQPALL_2
CREATE TABLE TMP_SDH_ZC_EQPALL_2
AS 
SELECT  T.TRANS_SYSTEM_ID,
                T.GL_NO,
                
                M.BSE_EQP_ID AS T2_BSE,
                M.BSE_NO     AS T2_BSE_NO,
                MID.BSE_SECT_ID
  FROM TMP_SDH_ZC_EQPALL_1 T
 LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
     AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID = M.BSE_SECT_ID
   AND T.T1_BSE <> M.BSE_EQP_ID
  LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) MID
    ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID
   AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID <> MID.BSE_SECT_ID
   AND M.BSE_EQP_ID = MID.BSE_EQP_ID	 group by T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID ,
                M.BSE_NO ,
								
                MID.BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_2ys1 ON TMP_SDH_ZC_EQPALL_2 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_2ys2 ON TMP_SDH_ZC_EQPALL_2 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_2ys3 ON TMP_SDH_ZC_EQPALL_2 (T2_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_2ys4 ON TMP_SDH_ZC_EQPALL_2 (TRANS_SYSTEM_ID);
--第三个   
DROP TABLE  if exists TMP_SDH_ZC_EQPALL_3 ;
CREATE TABLE TMP_SDH_ZC_EQPALL_3
AS 
SELECT  T.TRANS_SYSTEM_ID,
                T.GL_NO,
                
                M.BSE_EQP_ID AS T3_BSE,
                M.BSE_NO     AS T3_BSE_NO,
                MID.BSE_SECT_ID
  FROM TMP_SDH_ZC_EQPALL_2 T
 LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID = M.BSE_SECT_ID
   AND T.T2_BSE <> M.BSE_EQP_ID
  LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) MID
    ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID
   AND T.GL_NO = MID.GL_NO
   AND T.BSE_SECT_ID <> MID.BSE_SECT_ID
   AND M.BSE_EQP_ID = MID.BSE_EQP_ID	 group by T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID ,
                M.BSE_NO ,
								
                MID.BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_3ys1 ON TMP_SDH_ZC_EQPALL_3 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_3ys2 ON TMP_SDH_ZC_EQPALL_3 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_3ys3 ON TMP_SDH_ZC_EQPALL_3 (T3_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_3ys4 ON TMP_SDH_ZC_EQPALL_3 (TRANS_SYSTEM_ID);
--第四个   
DROP TABLE  if exists TMP_SDH_ZC_EQPALL_4 ;
CREATE TABLE TMP_SDH_ZC_EQPALL_4
AS 
SELECT  T.TRANS_SYSTEM_ID,
                T.GL_NO,
               
                M.BSE_EQP_ID AS T4_BSE,
                M.BSE_NO     AS T4_BSE_NO,
                MID.BSE_SECT_ID
  FROM TMP_SDH_ZC_EQPALL_3 T
 LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID = M.BSE_SECT_ID
   AND T.T3_BSE <> M.BSE_EQP_ID
  LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) MID
    ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID
   AND T.GL_NO = MID.GL_NO
   AND T.BSE_SECT_ID <> MID.BSE_SECT_ID
   AND M.BSE_EQP_ID = MID.BSE_EQP_ID	 group by T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID ,
                M.BSE_NO ,
								
                MID.BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_4ys1 ON TMP_SDH_ZC_EQPALL_4 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_4ys2 ON TMP_SDH_ZC_EQPALL_4 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_4ys3 ON TMP_SDH_ZC_EQPALL_4 (T4_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_4ys4 ON TMP_SDH_ZC_EQPALL_4 (TRANS_SYSTEM_ID);
   --第五个   
DROP TABLE  if exists  TMP_SDH_ZC_EQPALL_5 ;
CREATE TABLE TMP_SDH_ZC_EQPALL_5
AS 
SELECT    T.TRANS_SYSTEM_ID,
                T.GL_NO,
                
                M.BSE_EQP_ID AS T5_BSE,
                M.BSE_NO     AS T5_BSE_NO,
                MID.BSE_SECT_ID
  FROM TMP_SDH_ZC_EQPALL_4 T
 LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID = M.BSE_SECT_ID
   AND T.T4_BSE <> M.BSE_EQP_ID
  LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) MID
    ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID
   AND T.GL_NO = MID.GL_NO
   AND T.BSE_SECT_ID <> MID.BSE_SECT_ID
   AND M.BSE_EQP_ID = MID.BSE_EQP_ID	 group by T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID ,
                M.BSE_NO ,
								
                MID.BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_5ys1 ON TMP_SDH_ZC_EQPALL_5 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_5ys2 ON TMP_SDH_ZC_EQPALL_5 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_5ys3 ON TMP_SDH_ZC_EQPALL_5 (T5_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_5ys4 ON TMP_SDH_ZC_EQPALL_5 (TRANS_SYSTEM_ID);
  --第六个   
DROP TABLE  if exists  TMP_SDH_ZC_EQPALL_6 ;
CREATE TABLE TMP_SDH_ZC_EQPALL_6
AS 
SELECT   T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID AS T6_BSE,
                M.BSE_NO     AS T6_BSE_NO,
                MID.BSE_SECT_ID
  FROM TMP_SDH_ZC_EQPALL_5 T
 LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID = M.BSE_SECT_ID
   AND T.T5_BSE <> M.BSE_EQP_ID
  LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) MID
    ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID
   AND T.GL_NO = MID.GL_NO
   AND T.BSE_SECT_ID <> MID.BSE_SECT_ID
   AND M.BSE_EQP_ID = MID.BSE_EQP_ID	 group by T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID ,
                M.BSE_NO ,
								
                MID.BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_6ys1 ON TMP_SDH_ZC_EQPALL_6 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_6ys2 ON TMP_SDH_ZC_EQPALL_6 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_6ys3 ON TMP_SDH_ZC_EQPALL_6 (T6_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_6ys4 ON TMP_SDH_ZC_EQPALL_6 (TRANS_SYSTEM_ID);

 --第七个   
DROP TABLE if exists TMP_SDH_ZC_EQPALL_7 ;
CREATE TABLE TMP_SDH_ZC_EQPALL_7
AS 
SELECT    T.TRANS_SYSTEM_ID,
                T.GL_NO,
                
                M.BSE_EQP_ID AS T7_BSE,
                M.BSE_NO     AS T7_BSE_NO,
                MID.BSE_SECT_ID
  FROM TMP_SDH_ZC_EQPALL_6 T
 LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID = M.BSE_SECT_ID
   AND T.T6_BSE <> M.BSE_EQP_ID
  LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) MID
    ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID
   AND T.GL_NO = MID.GL_NO
   AND T.BSE_SECT_ID <> MID.BSE_SECT_ID
   AND M.BSE_EQP_ID = MID.BSE_EQP_ID	 group by T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID ,
                M.BSE_NO ,
								
                MID.BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_7ys1 ON TMP_SDH_ZC_EQPALL_7 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_7ys2 ON TMP_SDH_ZC_EQPALL_7 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_7ys3 ON TMP_SDH_ZC_EQPALL_7 (T7_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_7ys4 ON TMP_SDH_ZC_EQPALL_7 (TRANS_SYSTEM_ID);
--第八个   
DROP TABLE  if exists  TMP_SDH_ZC_EQPALL_8 ;
CREATE TABLE TMP_SDH_ZC_EQPALL_8
AS 
SELECT    T.TRANS_SYSTEM_ID,
                T.GL_NO,
               
                M.BSE_EQP_ID AS T8_BSE,
                M.BSE_NO     AS T8_BSE_NO,
                MID.BSE_SECT_ID
  FROM TMP_SDH_ZC_EQPALL_7 T
 LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID = M.BSE_SECT_ID
   AND T.T7_BSE <> M.BSE_EQP_ID
  LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) MID
    ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID
   AND T.GL_NO = MID.GL_NO
   AND T.BSE_SECT_ID <> MID.BSE_SECT_ID
   AND M.BSE_EQP_ID = MID.BSE_EQP_ID	 group by T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID ,
                M.BSE_NO ,
								
                MID.BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_8ys1 ON TMP_SDH_ZC_EQPALL_8 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_8ys2 ON TMP_SDH_ZC_EQPALL_8 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_8ys3 ON TMP_SDH_ZC_EQPALL_8 (T8_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_8ys4 ON TMP_SDH_ZC_EQPALL_8 (TRANS_SYSTEM_ID);
 --第九个   
DROP TABLE if exists  TMP_SDH_ZC_EQPALL_9 ;
CREATE TABLE TMP_SDH_ZC_EQPALL_9
AS 
SELECT   T.TRANS_SYSTEM_ID,
                T.GL_NO,
                
                M.BSE_EQP_ID AS T9_BSE,
                M.BSE_NO     AS T9_BSE_NO,
                MID.BSE_SECT_ID
  FROM TMP_SDH_ZC_EQPALL_8 T
 LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   AND T.GL_NO = M.GL_NO
   AND T.BSE_SECT_ID = M.BSE_SECT_ID
   AND T.T8_BSE <> M.BSE_EQP_ID
  LEFT JOIN (SELECT TRANS_SYSTEM_ID, GL_NO, BSE_SECT_ID, BSE_EQP_ID, BSE_NO FROM TMP_SLY_SDHTRANS_2_ZC_CXR) MID
    ON T.TRANS_SYSTEM_ID = MID.TRANS_SYSTEM_ID
   AND T.GL_NO = MID.GL_NO
   AND T.BSE_SECT_ID <> MID.BSE_SECT_ID
   AND M.BSE_EQP_ID = MID.BSE_EQP_ID	 group by T.TRANS_SYSTEM_ID,
                T.GL_NO,
           
                M.BSE_EQP_ID ,
                M.BSE_NO ,
								
                MID.BSE_SECT_ID;
CREATE INDEX TMP_SDH_ZC_EQPALL_9ys1 ON TMP_SDH_ZC_EQPALL_9 (GL_NO);
CREATE INDEX TMP_SDH_ZC_EQPALL_9ys2 ON TMP_SDH_ZC_EQPALL_9 (BSE_SECT_ID);
CREATE INDEX TMP_SDH_ZC_EQPALL_9ys3 ON TMP_SDH_ZC_EQPALL_9 (T9_BSE);
CREATE INDEX TMP_SDH_ZC_EQPALL_9ys4 ON TMP_SDH_ZC_EQPALL_9 (TRANS_SYSTEM_ID);


--这里存放的是光路终端出去的十个人井撑点，白名单只能在这里产生
DROP TABLE  if exists TMP_SDH_ZC_EQPALL ;
CREATE TABLE TMP_SDH_ZC_EQPALL
AS
SELECT * FROM (
SELECT TRANS_SYSTEM_ID,GL_NO,BSE_EQP_ID AS  BSE_EQP_ID FROM TMP_SLY_SDHTRANS_2_ZC_CXR_2
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T1_BSE FROM TMP_SDH_ZC_EQPALL_1
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T2_BSE FROM TMP_SDH_ZC_EQPALL_2
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T3_BSE FROM TMP_SDH_ZC_EQPALL_3
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T4_BSE FROM TMP_SDH_ZC_EQPALL_4 
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T5_BSE FROM TMP_SDH_ZC_EQPALL_5
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T6_BSE FROM TMP_SDH_ZC_EQPALL_6
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T7_BSE FROM TMP_SDH_ZC_EQPALL_7
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T8_BSE FROM TMP_SDH_ZC_EQPALL_8
UNION
SELECT TRANS_SYSTEM_ID,GL_NO,T9_BSE FROM TMP_SDH_ZC_EQPALL_9 ) TMP
WHERE TMP.BSE_EQP_ID IS NOT NULL;

  drop table  if exists  TMP_SDH_ZC_EQPALL_1 ;
   drop table if exists  TMP_SDH_ZC_EQPALL_2 ;
   drop table if exists  TMP_SDH_ZC_EQPALL_3 ;
   drop table if exists  TMP_SDH_ZC_EQPALL_4 ;
   drop table if exists  TMP_SDH_ZC_EQPALL_5 ;
   drop table  if exists TMP_SDH_ZC_EQPALL_6 ;
   drop table if exists  TMP_SDH_ZC_EQPALL_7 ;
   drop table if exists  TMP_SDH_ZC_EQPALL_8 ;
   drop table if exists  TMP_SDH_ZC_EQPALL_9 ;
--删除单路由局前井白名单
DELETE FROM   TMP_SLY_SDHTRANS_2_ZC_CXR t WHERE EXISTS(SELECT 1 FROM TMP_SDH_ZC_EQPALL 
WHERE t.trans_system_id=trans_system_id AND t.bse_eqp_id =bse_eqp_id )AND  EXISTS (SELECT 1
          FROM ${o3_res_schema}.cm_facility
         WHERE T.BSE_EQP_ID = id
           AND NOTES LIKE '%单路由%');
COMMIT;
----20171128  by chuxinrong 地下进线室剔除 （暂用）
DELETE FROM TMP_SLY_SDHTRANS_2_ZC_CXR T where t.bse_spec = '地下进线室';
commit;

 ----***END---

--获取管道两端支撑信息
DROP TABLE  if exists TMP_SLY_SDHTRANS_2_ZCEQP ;
CREATE TABLE TMP_SLY_SDHTRANS_2_ZCEQP
AS
SELECT DISTINCT BS.id BSE_SECT_ID,
       BS.code AS ZC_EQP_NO,
       PRS.res_type AS ZC_EQP_SPECNAME,
       beq.code AS ZC_EQPPORT_NO,
       PRS2.res_type AS ZC_EQPPORT_SPECNAME
  FROM ${o3_res_schema}.CM_PIPELINE  BS--select * from ${o3_res_schema}.pm_pub_res_type limit 100
  LEFT JOIN ${o3_res_schema}.pm_pub_res_type PRS
    ON BS.spec_id = PRS.res_type_id
  
  LEFT JOIN ${o3_res_schema}.cm_facility  BEQ
    ON bs.a_facility_id = BEQ.id
  LEFT JOIN ${o3_res_schema}.pm_pub_res_type PRS2
    ON BEQ.spec_id = PRS2.res_type_id
 WHERE EXISTS (SELECT 1
          FROM TMP_SLY_SDHTRANS_2_ZC_CXR
         WHERE BS.id = BSE_SECT_ID
           AND BSE_SECT_ID IS NOT NULL)
   

 union
 SELECT DISTINCT BS.id BSE_SECT_ID,
       BS.code AS ZC_EQP_NO,
       PRS.res_type AS ZC_EQP_SPECNAME,
       beq.code AS ZC_EQPPORT_NO,
       PRS2.res_type AS ZC_EQPPORT_SPECNAME
  FROM ${o3_res_schema}.CM_PIPELINE  BS--select * from ${o3_res_schema}.pm_pub_res_type limit 100
  LEFT JOIN ${o3_res_schema}.pm_pub_res_type PRS
    ON BS.spec_id = PRS.res_type_id
  
  LEFT JOIN ${o3_res_schema}.cm_facility  BEQ
    ON bs.z_facility_id = BEQ.id
  LEFT JOIN ${o3_res_schema}.pm_pub_res_type PRS2
    ON BEQ.spec_id = PRS2.res_type_id
 WHERE EXISTS (SELECT 1
          FROM TMP_SLY_SDHTRANS_2_ZC_CXR
         WHERE BS.id = BSE_SECT_ID
           AND BSE_SECT_ID IS NOT NULL)
   

 
 ;
         
---20171128 by chuxinrong  删除地下进线室的影响
DELETE FROM TMP_SLY_SDHTRANS_2_ZCEQP T WHERE T.ZC_EQPPORT_SPECNAME  like '%地下进线室%';
commit;    



--同一支撑段不能有承载2条以上的光路中的光缆段
DROP TABLE  if exists MID_SLY_SDHTRANS_2_ZC ;
CREATE TABLE MID_SLY_SDHTRANS_2_ZC
AS
SELECT TMP.TRANS_SYSTEM_ID, TMP.BSE_SECT_ID
  FROM (SELECT DISTINCT T.TRANS_SYSTEM_ID, T.GL_NO, T.BSE_SECT_ID
          FROM TMP_SLY_SDHTRANS_2_ZC_CXR T) TMP
 WHERE TMP.BSE_SECT_ID IS NOT NULL
 GROUP BY TMP.TRANS_SYSTEM_ID, TMP.BSE_SECT_ID
HAVING COUNT(DISTINCT TMP.GL_NO) > 1;
--管线重复的结果表

DROP TABLE  if exists RLT_SLY_SDHTRANS_2_ZC ;
CREATE TABLE RLT_SLY_SDHTRANS_2_ZC
AS
SELECT '${areaName}' city_name, T.TRANS_SYSTEM_ID,
       T.AREA_ID,
       T.AREA_NAME,
       T.PARENT_AREA_ID,
       T.TRANS_NAME,
       T.TRANS_NO,
       T.PROTECT_TYPE,
       T.NET_LEVEL,
       P.DUAN_NO,
       P.GL_NO,
       P.CABLE_NO,
       P.CABLE_LEVEL,
       P.SECTION_NO,
       X.ZC_EQP_NO,
       X.ZC_EQP_SPECNAME,
       X.ZC_EQPPORT_NO,
       X.ZC_EQPPORT_SPECNAME,
       '光缆段管道重复' AS ERR
  FROM TMP_SLY_SDHTRANS_INFO T
  JOIN MID_SLY_SDHTRANS_2_ZC M
    ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
  JOIN TMP_SLY_SDHTRANS_2_ZC S
    ON M.TRANS_SYSTEM_ID = S.TRANS_SYSTEM_ID
   AND S.BSE_SECT_ID = M.BSE_SECT_ID
  JOIN MID_SLY_SDHTRANS_2_SECTION P
    ON S.TRANS_SYSTEM_ID = P.TRANS_SYSTEM_ID
   AND S.GL_NO = P.GL_NO
   AND S.CBL_SECT_ID = P.CBL_SECT_ID
  JOIN TMP_SLY_SDHTRANS_2_ZCEQP X
    ON S.BSE_SECT_ID = X.BSE_SECT_ID;

INSERT INTO RLT_SLY_SDHTRANS_2_ZC
  ( city_name,TRANS_SYSTEM_ID,
   AREA_ID,
   AREA_NAME,
   PARENT_AREA_ID,
   TRANS_NAME,
   TRANS_NO,
   PROTECT_TYPE,
   NET_LEVEL,
   DUAN_NO,
   GL_NO,
   CABLE_NO,
   CABLE_LEVEL,
   SECTION_NO,
   ERR)
 SELECT '${areaName}' city_name,T.TRANS_SYSTEM_ID,
        T.AREA_ID,
        T.AREA_NAME,
        T.PARENT_AREA_ID,
        T.TRANS_NAME,
        T.TRANS_NO,
        T.PROTECT_TYPE,
        T.NET_LEVEL,
        P.DUAN_NO,
        P.GL_NO,
        P.CABLE_NO,
        P.CABLE_LEVEL,
        P.SECTION_NO,
        '光缆段无管线' 
   FROM TMP_SLY_SDHTRANS_INFO T
   JOIN TMP_SLY_SDHTRANS_2_ZC M
     ON T.TRANS_SYSTEM_ID = M.TRANS_SYSTEM_ID
   JOIN MID_SLY_SDHTRANS_2_SECTION P
     ON M.TRANS_SYSTEM_ID = P.TRANS_SYSTEM_ID
    AND M.GL_NO = P.GL_NO
    AND M.CBL_SECT_ID = P.CBL_SECT_ID
  WHERE M.BSE_SECT_ID IS NULL;
COMMIT;
--80000254 为缆段 敷设方式 为'杆路架空'  字典表（pub_restriction） 无穿管不考虑光缆段为杆路架空 的敷设方式
-- 20170421 by chuxinrong
delete from RLT_SLY_SDHTRANS_2_ZC t  where t.section_no in (SELECT distinct code FROM ${o3_res_schema}.CM_CABLE t where t.LAYING_WAY_ID ='81701444')
 and t.err ='光缆段无管线';--select * from ${o3_res_schema}.CM_CABLE  where LAYING_WAY_ID ='81701444' limit 100
commit ;



delete from  sly_sdh_tj_info t where  t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
insert into sly_sdh_tj_info (city_name,AREA_NAME,device_num,create_time)
select t.city_name,AREA_NAME ,count(DISTINCT TRANS_SYSTEM_ID),to_char(CURRENT_DATE,'yyyy-mm-dd')  from (select * from  RLT_SLY_SDHTRANS_GL_KPI  t ) t
group by t.city_name,AREA_NAME ;

update sly_sdh_tj_info t set gl_err1=n.num from (select city_name,AREA_NAME,count(DISTINCT TRANS_SYSTEM_ID)  num from RLT_SLY_SDHTRANS_NOGL_KPI group by city_name,AREA_NAME ) n where t.city_name=n.city_name and t.AREA_NAME=n.AREA_NAME and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_sdh_tj_info t set gl_err1=0 where gl_err1 is null and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_sdh_tj_info t set cs_err=n.num from (select city_name,AREA_NAME,count(DISTINCT TRANS_SYSTEM_ID)  num from RLT_SLY_SDHTRANS_2_SECTION group by city_name,AREA_NAME ) n where t.city_name=n.city_name and t.AREA_NAME=n.AREA_NAME and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_sdh_tj_info t set cs_err=0 where cs_err is null and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_sdh_tj_info t set zc_err=n.num from (select city_name,AREA_NAME,count(DISTINCT TRANS_SYSTEM_ID)  num from RLT_SLY_SDHTRANS_2_ZC where err='光缆段管道重复' group by city_name,AREA_NAME ) n where t.city_name=n.city_name and t.AREA_NAME=n.AREA_NAME and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_sdh_tj_info t set zc_err=0 where zc_err is null and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');


update sly_sdh_tj_info t set cg_err=n.num from (select city_name,AREA_NAME,count(DISTINCT TRANS_SYSTEM_ID)  num from RLT_SLY_SDHTRANS_2_ZC where err='光缆段无管线' group by city_name,AREA_NAME ) n where t.city_name=n.city_name and t.AREA_NAME=n.AREA_NAME and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_sdh_tj_info t set cg_err=0 where cg_err is null and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_sdh_tj_info t set bk_err=n.num from (select city_name,AREA_NAME,count(DISTINCT TRANS_SYSTEM_ID)  num from RLT_SLY_SDHTRANS_BK group by city_name,AREA_NAME ) n where t.city_name=n.city_name and t.AREA_NAME=n.AREA_NAME and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_sdh_tj_info t set bk_err=0 where bk_err is null and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');


DROP TABLE  if exists TMP_SLY_SDHTRANS_2_GL_1 ;
DROP TABLE  if exists TMP_SLY_SDHTRANS_2_GL_2 ;

	DROP TABLE  if exists TMP_SLY_SDHTRANS_2_GL_1 ;
	

	DROP TABLE   if exists TMP_SLY_SDHTRANS_ID ;

	DROP TABLE  if exists TMP_SLY_SDHTRANS_ID ; 
	DROP TABLE  if exists TMP_SLY_SDHTRANS_BK ;

	DROP TABLE if exists  TMP_SLY_SDHTRANS_BK ;
		 DROP TABLE if exists  TMP_SLY_SDHTRANS_2_SECTION_1 ;
	
				 DROP TABLE  if exists MID_SLY_SDHTRANS_2_SECTION ;
				 DROP TABLE  if exists TMP_SLY_SDHTRANS_2_SECTNUM ;
				 DROP TABLE if exists  MID_SLY_SDHTRANS_2_SECTNUM ;

				 DROP TABLE if exists  MID_SLY_SDHTRANS_2_SECTNUM ;
				 DROP TABLE if exists  TMP_SLY_SDHTRANS_2_SECTNUM ;
	
				 DROP TABLE if exists  TMP_SLY_SDHTRANS_2_ZCEQP ;
				 DROP TABLE  if exists TMP_SLY_SDHTRANS_2_ZC_CXR ;
				 DROP TABLE  if exists  TMP_SLY_SDHTRANS_2_ZC_CXR_1 ;
				 DROP TABLE  if exists  TMP_SLY_SDHTRANS_2_ZC_CXR_2 ;
				 DROP TABLE  if exists  TMP_SDH_ZC_EQPALL_1 ;
				 DROP TABLE  if exists TMP_SDH_ZC_EQPALL_2 ;
				 DROP TABLE  if exists TMP_SDH_ZC_EQPALL_3 ;
				 DROP TABLE  if exists TMP_SDH_ZC_EQPALL_4 ;
				 DROP TABLE  if exists  TMP_SDH_ZC_EQPALL_5 ;
				 DROP TABLE  if exists  TMP_SDH_ZC_EQPALL_6 ;
				 DROP TABLE if exists TMP_SDH_ZC_EQPALL_7 ;
				 DROP TABLE  if exists  TMP_SDH_ZC_EQPALL_8 ;
				 DROP TABLE if exists  TMP_SDH_ZC_EQPALL_9 ;
				 DROP TABLE  if exists TMP_SDH_ZC_EQPALL ;
				   drop table  if exists  TMP_SDH_ZC_EQPALL_1 ;
					 DROP TABLE  if exists TMP_SLY_SDHTRANS_2_ZCEQP ;
					 DROP TABLE  if exists MID_SLY_SDHTRANS_2_ZC ;
