--------------------------------------cn2交换机检测过程--------------------------------------
----------------基础信息包括：设备、端口、链路、逻辑光路
DROP TABLE IF EXISTS SLY_CN_BASE_INFO ;
CREATE table SLY_CN_BASE_INFO
as
select distinct ra.name city_name,raa.name area_name,t.id device_id ,t.code device_code ,t.name device_name,t1.id port_id,t1.code port_code,t1.name port_name,'CN2' spec_no, t2.id gl_id,t2.code gl_code
from ${o3_res_schema}.cm_device t 
inner join ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.id and t1.up_down_id = 100362 
inner join ${o3_res_schema}.cm_link t2 on ((t2.a_physic_device_id = t.id and t2.a_port_id = t1.id)
or (t2.z_physic_device_id = t.id and t2.z_port_id = t1.id)) and t2.spec_id = 1132400006
left join ${o3_res_schema}.pm_pub_res_type pp on pp.res_type_id = t.spec_id
left join ${o3_res_schema}.rm_area ra on t.region_id = ra.id
left join ${o3_res_schema}.rm_area raa on raa.id = t.leaf_region_id
where t.code like '%CN2%'   and 
 ( position('-A-' in t.code) > 0 or position('-C-' in t.code) > 0 or position('-G-' in t.code)>0 or position('-S-' in t.code)>0 or position('-V-' in t.code)>0 or position('-W-' in t.code)>0 )
 and position('虚拟' in t.code)=0 and pp.res_type != '光电转换器(以太网)' and  pp.res_type != '光电转换设备' 
 union
select distinct ra.name city_name,raa.name area_name,t.id device_id ,t.code device_code ,t.name device_name,t1.id port_id,t1.code port_code,t1.name port_name,'CN2' spec_no, t2.id link_id,t2.code gl_code
from ${o3_res_schema}.cm_device t 
inner join ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.id and t1.up_down_id = 100362
inner join ${o3_res_schema}.cm_link t2 on ((t2.a_physic_device_id = t.id and t2.a_port_id = t1.id)
or (t2.z_physic_device_id = t.id and t2.z_port_id = t1.id)) and t2.spec_id = 1132400006 
left join ${o3_res_schema}.pm_pub_res_type pp on pp.res_type_id = t.spec_id
left join ${o3_res_schema}.rm_area ra on t.region_id = ra.id
left join ${o3_res_schema}.rm_area raa on raa.id = t.leaf_region_id
where t.code like '%.CN%'  and 
 ( position('-A-' in t.code) > 0 or position('-C-' in t.code) > 0 or position('-G-' in t.code)>0 or position('-S-' in t.code)>0 or position('-V-' in t.code)>0 or position('-W-' in t.code)>0 )
 and position('虚拟' in t.code)=0 and pp.res_type != '光电转换器(以太网)' and  pp.res_type != '光电转换设备'  ;


----加入sdh网元
-- insert into SLY_CN_BASE_INFO
-- select distinct '${areaName}' city_name, sa.name area_name, T.Lgc_Eqp_Id,T.NAME lgc_eqp_name,T.No lgc_eqp_NO,'SDH网元' lgc_spec_no,
-- pe.phy_eqp_id , pe.name phy_eqp_name , pe.no phy_eqp_no ,t1.phy_port_id ,  t1.no phy_port_no ,t2.link_id phy_link_id , lbl2.no phy_gl_no
-- from zhzz_wy_eqp z
-- inner join lgc_equipment t on t.lgc_eqp_id = z.lgc_eqp_id
-- left join SPC_AREA sa on sa.area_id  = T.AREA_ID
-- inner join LGC_PHYEQP_2_LGCEQP lp2l on T.LGC_EQP_ID = lp2l.LGC_EQP_ID
-- inner join PHY_EQUIPMENT pe on  pe.phy_eqp_id = lp2l.phy_eqp_id
-- left join phy_term_port t1 on t1.phy_eqp_id = pe.phy_eqp_id and t1.up_or_down_id = 80000407
-- left join lnk_phy_link_component t2 on (t2.a_phy_eqp_id = pe.phy_eqp_id and t2.a_phy_port_id = t1.phy_port_id) or (t2.z_phy_eqp_id = pe.phy_eqp_id and t2.z_phy_port_id = t1.phy_port_id)
-- left join LNK_BUSI_LINK_2_LINK lbl2l2 on lbl2l2.link_id = t2.link_id
-- left join LNK_BUSINESS_LINK lbl2 on lbl2.busi_link_id = lbl2l2.busi_link_id ;
-- commit;
-- ]

delete from SLY_CN_BASE_INFO t 
where t.gl_code is null and exists 
(select 1 from SLY_CN_BASE_INFO t1 
where t1.gl_code is not null and t1.device_id = t.device_id);
commit;

---------------网络资源检测----------------
DROP TABLE IF EXISTS SLY_CN_NETRESOURCE;
create table SLY_CN_NETRESOURCE as
select T.* ,'缺端口' ERROR from SLY_CN_BASE_INFO T
where t.device_id in 
(select t1.device_id from SLY_CN_BASE_INFO t1 
group by t1.device_id having count(distinct t1.port_code)<2);


insert into SLY_CN_NETRESOURCE
select T.* ,'缺光路' ERROR from SLY_CN_BASE_INFO T
where t.device_id in(select t1.device_id from SLY_CN_BASE_INFO t1 
group by t1.device_id having count (distinct t1.gl_code)<2 ) and t.device_id in(select t1.device_id from SLY_CN_BASE_INFO t1 
group by t1.device_id having count (distinct t1.port_code)>=2 );
commit;




/*--设备清单表
DROP TABLE IF EXISTS SLY_CN_GROUP_INFO ;
create table  SLY_CN_GROUP_INFO as
SELECT distinct T.city_name,T.area_name,t.lgc_eqp_id,T.LGC_EQP_NO,T.LGC_EQP_NAME,T.LGC_SPEC_NO,t.phy_eqp_no,t.phy_eqp_name,sysdate create_time
FROM SLY_CN_BASE_INFO T ;
 
DELETE FROM REPORTDEV.SLY_CN_GROUP_INFO@REPORTDEV
 WHERE CITY_NAME = '${areaName}';
COMMIT;

INSERT INTO REPORTDEV.SLY_CN_GROUP_INFO@REPORTDEV
SELECT T.* FROM SLY_CN_GROUP_INFO T where t.lgc_spec_no = 'CN2';
COMMIT;


delete from SLY_CN_BASE_INFO T 
where exists(select 1 from SLY_CN_NETRESOURCE T1 
where T1.Lgc_Eqp_Id = T.Lgc_Eqp_Id);
commit;

----汇总cn2设备和光路数据清单到江苏库
delete from REPORTDEV.gl_group_data@REPORTDEV t 
where t.area_code = decode('${areaName}','淮安','ha.js.cn','连云港','lyg.js.cn','常州','cz.js.cn','泰州','tz.js.cn','宿迁','sq.js.cn','南京','nj.js.cn','镇江','zj.js.cn',
'无锡','wx.js.cn','苏州','sz.js.cn','南通','nt.js.cn','扬州','yz.js.cn','盐城','yc.js.cn','徐州','xz.js.cn') and t.eqp_spec = 'cn2';
commit;
insert into REPORTDEV.gl_group_data@REPORTDEV
select distinct decode('${areaName}','淮安','ha.js.cn','连云港','lyg.js.cn','常州','cz.js.cn','泰州','tz.js.cn','宿迁','sq.js.cn','南京','nj.js.cn','镇江','zj.js.cn',
'无锡','wx.js.cn','苏州','sz.js.cn','南通','nt.js.cn','扬州','yz.js.cn','盐城','yc.js.cn','徐州','xz.js.cn')  area_code,
 t.lgc_eqp_id ,t.lgc_eqp_no , t.phy_gl_no , sysdate,'cn2' from SLY_CN_BASE_INFO t where t.lgc_spec_no  ='CN2';
commit;*/

------检测同板卡
DROP TABLE IF EXISTS SLY_CN_BK;
commit;
CREATE table SLY_CN_BK
as
select cd.*,
(case when cdw2.code='' then '' else cdw2.code || '/' end)||
(case when cdw1.code='' then '' else cdw1.code || '/' end)||
(case when pcdw.code='' then '' else pcdw.code end) AS BK_NO
from SLY_CN_BASE_INFO cd
left join ${o3_res_schema}.cm_port cm on cm."id" = cd.port_id
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) pcdw on pcdw.child_id = cm.ware_id 
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) cdw1 on cdw1.child_id = pcdw.parent_id and cdw1.child_id IS NOT NULL
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) cdw2 on cdw2.child_id = cdw1.parent_id and cdw2.child_id IS NOT NULL;


DROP TABLE IF EXISTS SLY_temp_CN_BK_ERROR;
commit;
create table SLY_temp_CN_BK_ERROR as
select T.device_id , count(distinct T.Bk_No) bk_num from SLY_CN_BK T 
where T.gl_code is not null and T.Bk_No is not null 
group by T.device_id having count(distinct T.Bk_No) = 1;

DROP TABLE IF EXISTS SLY_CN_BK_ERROR;
create table SLY_CN_BK_ERROR as
select distinct  T.City_Name,t.area_name,t.spec_no,
t.device_name,t.device_code,t.port_code,t.gl_code,t.bk_no,'同板卡'  ERR,now() create_time
 from SLY_CN_BK T
where exists(select 1 from SLY_temp_CN_BK_ERROR T1 where T.device_id = T1.device_id);


delete from sly_gl_group_info sg where sg.group_type = 'CN2';
insert into sly_gl_group_info 
select sc.device_id,sc.device_code,sc.device_name,sc.spec_no,sc.gl_id,sc.gl_code from SLY_CN_BASE_INFO sc;

call gl_cable('CN2');


-- ----汇总cn2设备经过的光缆段和光路数据清单到江苏库
-- delete from REPORTDEV.cbl_gl_group_data@REPORTDEV t 
-- where t.area_code = decode('${areaName}','淮安','ha.js.cn','连云港','lyg.js.cn','常州','cz.js.cn','泰州','tz.js.cn','宿迁','sq.js.cn','南京','nj.js.cn','镇江','zj.js.cn',
-- '无锡','wx.js.cn','苏州','sz.js.cn','南通','nt.js.cn','扬州','yz.js.cn','盐城','yc.js.cn','徐州','xz.js.cn') and t.eqp_spec = 'cn2';
-- commit;
-- insert into REPORTDEV.cbl_gl_group_data@REPORTDEV
-- select distinct decode('${areaName}','淮安','ha.js.cn','连云港','lyg.js.cn','常州','cz.js.cn','泰州','tz.js.cn','宿迁','sq.js.cn','南京','nj.js.cn','镇江','zj.js.cn',
-- '无锡','wx.js.cn','苏州','sz.js.cn','南通','nt.js.cn','扬州','yz.js.cn','盐城','yc.js.cn','徐州','xz.js.cn')  area_code,
--  t.section_no , t.phy_gl_no , sysdate,'cn2' from SLY_CN_CABLE_INFO t where t.lgc_spec_no  ='CN2';
-- commit;



--DROP TABLE IF EXISTS SLY_CN_SECTION_ERR_TMP purge;
--create table   SLY_CN_SECTION_ERR_TMP as
--select T.Lgc_Eqp_Id,T.Section_Id from SLY_CN_CABLE_INFO T 
--where T.Cable_Level <> '局内' and T.Cable_Level <> '联络'
--group by T.Lgc_Eqp_Id,T.Section_Id having count(distinct T.Phy_Gl_No)>=2;



-----------------------检测同管道----------------------
call cable_zc_section('CN2');
----汇总cn2经过的管道段和光路数据清单到江苏库
-- delete from REPORTDEV.BSE_GL_GROUP_DATA@REPORTDEV t 
-- where t.area_code = decode('${areaName}','淮安','ha.js.cn','连云港','lyg.js.cn','常州','cz.js.cn','泰州','tz.js.cn','宿迁','sq.js.cn','南京','nj.js.cn','镇江','zj.js.cn',
-- '无锡','wx.js.cn','苏州','sz.js.cn','南通','nt.js.cn','扬州','yz.js.cn','盐城','yc.js.cn','徐州','xz.js.cn') and t.eqp_spec = 'cn2';
-- commit;
-- insert into REPORTDEV.BSE_GL_GROUP_DATA@REPORTDEV
-- select distinct decode('${areaName}','淮安','ha.js.cn','连云港','lyg.js.cn','常州','cz.js.cn','泰州','tz.js.cn','宿迁','sq.js.cn','南京','nj.js.cn','镇江','zj.js.cn',
-- '无锡','wx.js.cn','苏州','sz.js.cn','南通','nt.js.cn','扬州','yz.js.cn','盐城','yc.js.cn','徐州','xz.js.cn')  area_code,
--  t.zc_section_no , t.phy_gl_no , sysdate,'cn2' from sly_cable_zc_section t where t.lgc_spec_no  ='CN2';
-- commit;




-- ----网络资源异常表
-- DELETE FROM REPORTDEV.SLY_CN_NETRESOURCE@REPORTDEV
--  WHERE CITY_NAME = '${areaName}';
-- COMMIT;
-- INSERT INTO REPORTDEV.SLY_CN_NETRESOURCE@REPORTDEV
-- SELECT T.*,sysdate FROM SLY_CN_NETRESOURCE T where t.lgc_spec_no = 'CN2';
-- COMMIT;
-- 
-- ----板卡资源异常表
-- DELETE FROM REPORTDEV.SLY_CN_BK_ERROR@REPORTDEV
--  WHERE CITY_NAME = '${areaName}';
-- COMMIT;
-- INSERT INTO REPORTDEV.SLY_CN_BK_ERROR@REPORTDEV
-- SELECT T.* FROM SLY_CN_BK_ERROR T where t.lgc_spec_no = 'CN2';
-- COMMIT;
-- 
-- 
-- ----路由资源异常表
-- DELETE FROM REPORTDEV.SLY_CN_ROUTE@REPORTDEV
--  WHERE CITY_NAME = '${areaName}';
-- COMMIT;
-- INSERT INTO REPORTDEV.SLY_CN_ROUTE@REPORTDEV
-- SELECT T.*,sysdate
--   FROM SLY_CN_ROUTE T where t.lgc_spec_no = 'CN2';
-- COMMIT;
-- 
-- ----SDH网元路由资源异常表
-- DELETE FROM REPORTDEV.SLY_SDH_ROUTE@REPORTDEV
--  WHERE CITY_NAME = '${areaName}';
-- COMMIT;
-- INSERT INTO REPORTDEV.SLY_SDH_ROUTE@REPORTDEV
-- SELECT T.*,sysdate
--   FROM SLY_CN_ROUTE T where t.lgc_spec_no = 'SDH网元';
-- COMMIT;
-- 
DROP TABLE IF EXISTS SLY_CN_ROUTE;
create table SLY_CN_ROUTE as
select t.city_name,t.area_name,t1.*,clock_timestamp()::timestamp(0) without time zone create_date from SLY_CN_BASE_INFO t left join SLY_TWO_ROUTE t1 on t.device_code = group_code and t.gl_code = t1.gl_code where t1.group_type = 'cn2';
--------------------------------统计脚本----------------
delete from SLY_CN_CHECK_INFO t where t.create_time = CURRENT_DATE;

insert into SLY_CN_CHECK_INFO (city_name,eqp_num)
select t.city_name,count(distinct t.device_code) from SLY_CN_BASE_INFO t group by t.city_name;

update SLY_CN_CHECK_INFO set port_err = (select count(distinct t.device_code) count1 from SLY_CN_NETRESOURCE t where t.error = '缺端口' group by t.city_name) where create_time = CURRENT_DATE;
update SLY_CN_CHECK_INFO set port_err = 0 where port_err is null and create_time = CURRENT_DATE;
 
update SLY_CN_CHECK_INFO set gl_err = (select count(distinct t.device_code) count1 from SLY_CN_NETRESOURCE t where t.error = '缺光路' group by t.city_name) where create_time = CURRENT_DATE;
update SLY_CN_CHECK_INFO set gl_err = 0 where gl_err is null and create_time = CURRENT_DATE;


update SLY_CN_CHECK_INFO set bk_err = (select count(distinct t.device_code) count1 from SLY_CN_BK_ERROR t where t.err = '同板卡' group by t.city_name) where create_time = CURRENT_DATE;
update SLY_CN_CHECK_INFO set bk_err = 0 where bk_err is null and create_time = CURRENT_DATE;


update SLY_CN_CHECK_INFO set cable_err = (select count(distinct t.group_code) count1 from SLY_CN_ROUTE t where t.error = '同光缆') where create_time = CURRENT_DATE;
update SLY_CN_CHECK_INFO set cable_err = 0 where cable_err is null and create_time = CURRENT_DATE;

update SLY_CN_CHECK_INFO set zc_err = (select count(distinct t.group_code) count1 from SLY_CN_ROUTE t where t.error = '同管道') where create_time = CURRENT_DATE;
update SLY_CN_CHECK_INFO set zc_err = 0 where zc_err is null and create_time = CURRENT_DATE;

update SLY_CN_CHECK_INFO set none_zc = (select count(distinct t.group_code) count1 from SLY_CN_ROUTE t where t.error = '无穿管') where create_time = CURRENT_DATE;
update SLY_CN_CHECK_INFO set none_zc = 0 where none_zc is null and create_time = CURRENT_DATE;






