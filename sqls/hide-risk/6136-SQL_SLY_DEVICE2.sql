-----------------------------------------------路由资源分析---------------------------------------------------------
 drop table IF EXISTS sly_dwdm_gl_cable_1 ;
create table sly_dwdm_gl_cable_1 as
select   t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name--,cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id ,ppr.desc_china cable_level
from sly_dwdm_gl_no t
left join    ${o3_res_schema}.cr_link_link cll1  on cll1.upper_link_id = t.gl_id_bf and cll1.spec_id=1133111310000
left join    ${o3_res_schema}.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id and cll2.spec_id=1133511310000
left join   ${o3_res_schema}.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
left join   ${o3_res_schema}.cm_CABLE fiber on fiber.id = clc.cable_id
left join   ${o3_res_schema}.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002;  --光缆段
create index sly_dwdm_gl_cable_1sy_1 on sly_dwdm_gl_cable_1(cs_id);
drop table IF EXISTS sly_dwdm_gl_cable ;
create table sly_dwdm_gl_cable as
select t.*,cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id ,ppr.desc_china cable_level from sly_dwdm_gl_cable_1 t
left join   ${o3_res_schema}.cr_net_entity cne on t.cs_id=cne.entity_ID
left join   ${o3_res_schema}.cm_net cn on cn.id = cne.NET_iD
left join   ${o3_res_schema}.pm_pub_restriction ppr on ppr.serial_no = cn.net_type_id ;
delete from sly_dwdm_gl_cable t where t.cs_code is null and exists(select 1 from sly_dwdm_gl_cable t1 where t1.device_code = t.device_code and t1.gl_code = t.gl_code and  t.cs_code is not null);
drop table IF EXISTS temp_sly_dwdm_num ;
create table temp_sly_dwdm_num as
select device_id,count(distinct t.bf_no) num from sly_dwdm_gl_cable t 
group by device_id ;
 drop table IF EXISTS temp_sly_dwdm_num2 ;
create table temp_sly_dwdm_num2 as
select device_id,cs_id,count(distinct t.bf_no) num from sly_dwdm_gl_cable t where t.cable_level <> '局内'and t.cable_level <> '联络'
group by device_id,cs_id ;
drop table IF EXISTS temp_sly_cable_err ;
create table temp_sly_cable_err as
select t.* from temp_sly_dwdm_num2 t 
inner join temp_sly_dwdm_num t1 on t1.device_id = t.device_id and t1.num = t.num ;
--这里的是通过光缆段--管孔--管道段，但是目前有两种特殊情况1.光缆段--子管孔--管孔--管道段 2.光缆段--管道段
drop table IF EXISTS sly_dwdm_gl_zc ;
create table  sly_dwdm_gl_zc as
select  t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001 --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
union 
select  t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id  --子管孔
left join   ${o3_res_schema}.CM_PIPELINE t6 on t6.id =t2.parent_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id !=1111100001  --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id 
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
UNION
select  t.*,t2.id zc_section_id, '' code,t2.code zc_section_code,t2.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id and t2.spec_id !=1111100001  --去掉管孔
--left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t2.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t2.z_facility_id;
create index device_id99 on sly_dwdm_gl_zc(device_id);
create index cs_id99 on sly_dwdm_gl_zc(cs_id);
create index zc_section_id99 on sly_dwdm_gl_zc(zc_section_id);
delete from sly_dwdm_gl_zc t where cs_id is null;
delete from sly_dwdm_gl_zc t where z_zc_eqp_code is null AND a_zc_eqp_code is null AND  zc_section_id  IS NOT NULL ;
delete from sly_dwdm_gl_zc t where T.zc_section_id is null and exists
(select 1 from sly_dwdm_gl_zc T1 where T.device_id = T1.device_id  and T1.cs_id = T.cs_id and T1.zc_section_id is not null);
drop table IF EXISTS sly_dwdm_gl_route_err ; --SELECT DEVICE_CODE FROM sly_dwdm_gl_route_err GROUP BY DEVICE_CODE
create table  sly_dwdm_gl_route_err as
select T.* , '无穿管' ERROR from sly_dwdm_gl_zc T 
where T.zc_section_id is null and T.Cable_Level<> '局内' and T.Cable_Level<> '联络' and not exists
(select 1 from sly_dwdm_gl_zc T1 where T.device_id = T1.device_id  and T1.cs_id = T.cs_id and T1.zc_section_id is not null);
delete from sly_dwdm_gl_route_err where DEVICE_CODE IN  (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in ('设备白名单' ,'双路由白名单') and is_white='申请白名单成功') ;
--删除光缆级别为楼间的无穿管。
delete from sly_dwdm_gl_route_err where cable_level='楼间'; 
--发现存在光缆属性不是局内，但光缆段属性是局内。所以在此做一个剔除；
delete from sly_dwdm_gl_route_err t where error='无穿管' and exists (select 1 from ${o3_res_schema}.cm_cable t1 where t.cs_id=t1.id  and t1.long_local_id =108439   );
DELETE FROM sly_dwdm_gl_zc WHERE zc_section_id is null;
delete from sly_dwdm_gl_zc t where z_zc_eqp_code is null or a_zc_eqp_code is null;
drop table IF EXISTS temp_sly_dwdm_num3 ;
create table temp_sly_dwdm_num3 as
select t.device_id , t.zc_section_code,count(distinct t.gl_code) num from sly_dwdm_gl_zc t
where t.cable_level <> '局内'
group by t.device_id , t.zc_section_code;
drop table IF EXISTS temp_sly_zc_err ;
create table temp_sly_zc_err as
select  t.* from temp_sly_dwdm_num3 t
inner join temp_sly_dwdm_num t1 on t1.device_id = t.device_id and t1.num = t.num  ;
insert into sly_dwdm_gl_route_err 
select t.*,'同管道'  from sly_dwdm_gl_zc t
where EXISTS (select 1 from temp_sly_zc_err t1 where t1.device_id = t.device_id and t1.zc_section_code = t.zc_section_code) 
and not EXISTS (select 1 from temp_sly_cable_err t1 where t1.device_id = t.device_id and t1.cs_id = t.cs_id) and t.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单') and is_white='申请白名单成功') ;
insert into sly_dwdm_gl_route_err (ip,city_name,area_name,device_id,device_code,device_name,device_spec,create_date,port_id,port_code,gl_id,gl_code,bf_id,bf_no,gl_id_bf,gl_code_bf,gl_spec,cs_id,cs_code,cs_name,cable_id,cable_code,cable_name,spec_id,cable_level,error)
select t.*,'同光缆'  from sly_dwdm_gl_cable t
where EXISTS (select 1 from temp_sly_cable_err t1 where t1.device_id = t.device_id and t1.cs_id = t.cs_id) and t.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单')and is_white='申请白名单成功' );
drop table IF EXISTS device_white;
create table device_white as
select  t.group_name group_code,t.bse_eqp_no eqp1 from sly_sys_white_config t where t.function_type = '单设备';
drop table IF EXISTS device_white1;
create table device_white1 as
select  t.group_code,t.eqp1,t1.z_zc_eqp_code eqp2,t1.zc_section_code 
from device_white t 
left join sly_dwdm_gl_zc t1 on t.eqp1 = t1.a_zc_eqp_code and t.group_code = t1.device_code
union
select  t.group_code,t.eqp1,t1.a_zc_eqp_code eqp2,t1.zc_section_code
from device_white t 
left join sly_dwdm_gl_zc t1 on t.eqp1 = t1.z_zc_eqp_code  and t.group_code = t1.device_code;
drop table IF EXISTS device_white2;
create table device_white2 as
select  t.group_code,t.eqp2,t1.z_zc_eqp_code eqp3,t1.zc_section_code 
from device_white1 t 
left join sly_dwdm_gl_zc t1 on t.eqp2 = t1.a_zc_eqp_code and t.eqp1 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp2,t1.a_zc_eqp_code eqp3,t1.zc_section_code
from device_white1 t 
left join sly_dwdm_gl_zc t1 on t.eqp2 = t1.z_zc_eqp_code and t.eqp1 <> t1.a_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white3;
create table device_white3 as
select  t.group_code,t.eqp3,t1.z_zc_eqp_code eqp4,t1.zc_section_code 
from device_white2 t 
left join sly_dwdm_gl_zc t1 on t.eqp3 = t1.a_zc_eqp_code and t.eqp2 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp3,t1.a_zc_eqp_code eqp4,t1.zc_section_code
from device_white2 t 
left join sly_dwdm_gl_zc t1 on t.eqp3 = t1.z_zc_eqp_code and t.eqp2 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white4;
create table device_white4 as
select  t.group_code,t.eqp4,t1.z_zc_eqp_code eqp5,t1.zc_section_code 
from device_white3 t 
left join sly_dwdm_gl_zc t1 on t.eqp4 = t1.a_zc_eqp_code and t.eqp3 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp4,t1.a_zc_eqp_code eqp5,t1.zc_section_code
from device_white3 t 
left join sly_dwdm_gl_zc t1 on t.eqp4 = t1.z_zc_eqp_code and t.eqp3 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white5;
create table device_white5 as
select  t.group_code,t.eqp5,t1.z_zc_eqp_code eqp6,t1.zc_section_code 
from device_white4 t 
left join sly_dwdm_gl_zc t1 on t.eqp5 = t1.a_zc_eqp_code and t.eqp4 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp5,t1.a_zc_eqp_code eqp6,t1.zc_section_code
from device_white4 t 
left join sly_dwdm_gl_zc t1 on t.eqp5 = t1.z_zc_eqp_code and t.eqp4 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white6;
create table device_white6 as
select  t.group_code,t.eqp6,t1.z_zc_eqp_code eqp7,t1.zc_section_code 
from device_white5 t 
left join sly_dwdm_gl_zc t1 on t.eqp6 = t1.a_zc_eqp_code and t.eqp5 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp6,t1.a_zc_eqp_code eqp7,t1.zc_section_code
from device_white5 t 
left join sly_dwdm_gl_zc t1 on t.eqp6 = t1.z_zc_eqp_code and t.eqp5 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white7;
create table device_white7 as
select  t.group_code,t.eqp7,t1.z_zc_eqp_code eqp8,t1.zc_section_code 
from device_white6 t 
left join sly_dwdm_gl_zc t1 on t.eqp7 = t1.a_zc_eqp_code and t.eqp6 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp7,t1.a_zc_eqp_code eqp8,t1.zc_section_code
from device_white6 t 
left join sly_dwdm_gl_zc t1 on t.eqp7 = t1.z_zc_eqp_code and t.eqp6 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white8;
create table device_white8 as
select  t.group_code,t.eqp8,t1.z_zc_eqp_code eqp9,t1.zc_section_code 
from device_white7 t 
left join sly_dwdm_gl_zc t1 on t.eqp8 = t1.a_zc_eqp_code and t.eqp7 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp8,t1.a_zc_eqp_code eqp9,t1.zc_section_code
from device_white7 t 
left join sly_dwdm_gl_zc t1 on t.eqp8 = t1.z_zc_eqp_code and t.eqp7 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;
--/**/
drop table IF EXISTS device_white9;
create table device_white9 as
select  t.group_code,t.eqp9,t1.z_zc_eqp_code eqp10,t1.zc_section_code 
from device_white8 t 
left join sly_dwdm_gl_zc t1 on t.eqp9 = t1.a_zc_eqp_code and t.eqp8 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp9,t1.a_zc_eqp_code eqp10,t1.zc_section_code
from device_white8 t 
left join sly_dwdm_gl_zc t1 on t.eqp9 = t1.z_zc_eqp_code and t.eqp8 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;
drop table IF EXISTS device_white10;
create table device_white10 as
select  t.group_code,t.eqp10,t1.z_zc_eqp_code eqp11,t1.zc_section_code 
from device_white9 t 
left join sly_dwdm_gl_zc t1 on t.eqp10 = t1.a_zc_eqp_code and t.eqp9 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp10,t1.a_zc_eqp_code eqp11,t1.zc_section_code
from device_white9 t 
left join sly_dwdm_gl_zc t1 on t.eqp10 = t1.z_zc_eqp_code and t.eqp9 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;drop table IF EXISTS device_white11;
create table device_white11 as
select  t.group_code,t.eqp11,t1.z_zc_eqp_code eqp12,t1.zc_section_code 
from device_white10 t 
left join sly_dwdm_gl_zc t1 on t.eqp11 = t1.a_zc_eqp_code and t.eqp10 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp11,t1.a_zc_eqp_code eqp12,t1.zc_section_code
from device_white10 t 
left join sly_dwdm_gl_zc t1 on t.eqp11 = t1.z_zc_eqp_code and t.eqp10 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;
--/**/
drop table IF EXISTS device_white_list;
create table device_white_list as
select t.group_code,t.eqp1 zc_eqp_no from device_white t
union 
select t1.group_code,t1.eqp2 from device_white1 t1
union 
select t2.group_code,t2.eqp3 from device_white2 t2
union 
select t3.group_code,t3.eqp4 from device_white3 t3
union 
select t4.group_code,t4.eqp5 from device_white4 t4
union 
select t5.group_code,t5.eqp6 from device_white5 t5
union 
select t6.group_code,t6.eqp7 from device_white6 t6
union 
select t7.group_code,t7.eqp8 from device_white7 t7
union 
select t8.group_code,t8.eqp9 from device_white8 t8
union 
select t9.group_code,t9.eqp10 from device_white9 t9
union 
select t10.group_code,t10.eqp11 from device_white10 t10
union 
select t11.group_code,t11.eqp12 from device_white11 t11;

delete from sly_dwdm_gl_route_err t where exists(select 1 from device_white_list t1 where t.device_code = t1.group_code and (t1.zc_eqp_no = t.a_zc_eqp_code or t1.zc_eqp_no = t.z_zc_eqp_code)) and exists (select 1 from ${o3_res_schema}.cm_facility t1  where notes like '%单路由%' and (t1.code = t.a_zc_eqp_code or t1.code = t.z_zc_eqp_code) );

---之前逻辑需要在稽核时删除进线室，但常州删除进线室会导致部分局前井失效，所以在检测之后将出现同管道的进线室删除掉。
delete from sly_dwdm_gl_route_err t WHERE (z_zc_eqp_code LIKE '%进线室%' OR A_zc_eqp_code LIKE '%进线室%') AND ERROR ='同管道';
-----------------------------------统计数据----------------------------------------剔除白名单
delete from  sly_deivice_tj_info t where  t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
insert into sly_deivice_tj_info (city_name,area_name,device_spec,device_num,create_time)
select t.city_name ,area_name,device_spec,count(1),to_char(CURRENT_DATE,'yyyy-mm-dd')  from (select * from sly_device_base_info t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')) t
group by t.city_name ,t.area_name,t.device_spec;

--/**/
update sly_deivice_tj_info t set port_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t
where error = '缺端口' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set port_err = 0 where port_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set gl_err1 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t
where error = '缺光路' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set gl_err1 = 0 where gl_err1 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set gl_err2 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t 
where error = '光路异常' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set gl_err2 = 0 where gl_err2 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set bk_err1 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_gl_bk_err  t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t 
where error = '无板卡' group by CITY_NAME, AREA_NAME, device_spec)n
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set bk_err1 = 0 where bk_err1 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
--/**/
update sly_deivice_tj_info t set bk_err2 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_gl_bk_err  t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'同卡板白名单') and is_white='申请白名单成功')) t 
where error = '同板卡' group by CITY_NAME, AREA_NAME, device_spec)n  
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set bk_err2 = 0 where bk_err2 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set cs_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单')and is_white='申请白名单成功' )) t 
where error = '同光缆' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set cs_err = 0 where cs_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set zc_null = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in ('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t 
where error = '无穿管' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set zc_null = 0 where zc_null is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

--/**/

update sly_deivice_tj_info t set zc_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t 
where error = '同管道' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set zc_err = 0 where zc_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info t set Dual_routing = n.num
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM 
(select CITY_NAME, AREA_NAME,device_id,device_spec,create_date from (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')) t
UNION
select CITY_NAME, AREA_NAME,device_id,device_spec,create_date from (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in ('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t) a
 group by a.CITY_NAME, a.AREA_NAME, a.device_spec)n
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set Dual_routing = 0 where Dual_routing is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
--这个sql里面用到两个函数，里面的表数据在运行完清空防止因为数据量过大影响其他脚本。
delete from sly_two_gl_group_cable sg where sg.group_type = 'OLP';
delete from sly_two_eqp_glnum sc where sc.group_type = 'OLP';
delete from sly_two_section_glnum sc where sc.group_type = 'OLP';
delete from SLY_TWO_SECTION_ERR_TMP sc where sc.group_type = 'OLP';
delete from SLY_TWO_SECTION_ERR sc where sc.group_type = 'OLP';
delete from sly_two_cable_zc_section sc where sc.group_type = 'OLP';
delete from SLY_TWO_ROUTE sc where sc.group_type = 'OLP';
delete from sly_two_eqp_zc_glnum sc where sc.group_type = 'OLP';
delete from sly_two_zc_section_glnum sc where sc.group_type = 'OLP';
delete from SLY_TWO_ZC_ERROR sc where sc.group_type = 'OLP';


delete from sly_two_gl_group_cable sg where sg.group_type = 'BFGL';
delete from sly_two_eqp_glnum sc where sc.group_type = 'BFGL';
delete from sly_two_section_glnum sc where sc.group_type = 'BFGL';
delete from SLY_TWO_SECTION_ERR_TMP sc where sc.group_type = 'BFGL';
delete from SLY_TWO_SECTION_ERR sc where sc.group_type = 'BFGL';
delete from sly_two_cable_zc_section sc where sc.group_type = 'BFGL';
delete from SLY_TWO_ROUTE sc where sc.group_type = 'BFGL';
delete from sly_two_eqp_zc_glnum sc where sc.group_type = 'BFGL';
delete from sly_two_zc_section_glnum sc where sc.group_type = 'BFGL';
delete from SLY_TWO_ZC_ERROR sc where sc.group_type = 'BFGL';
drop table if exists cm_link_t2;
drop table IF EXISTS sly_device_gl_info_1 ;

drop table sly_device_netsource_1;

drop table IF EXISTS sly_device_gl_bk_1 ;
drop table IF EXISTS CR_DEVICE_WARE_z;
drop table IF EXISTS sly_device_gl_bk;


drop table if EXISTS cm_link_gl;

drop table if EXISTS cr_link_link_t2;
drop table if EXISTS cr_link_link_t4;
drop table if EXISTS cm_link_t13;
 drop table IF EXISTS sly_dwdm_gl_no_1 ;
 drop table  if exists  sly_dwdm_gl_no;
 drop table IF EXISTS sly_dwdm_gl_no_1 ;
 drop table IF EXISTS sly_dwdm_gl_no_room ;
 drop table IF EXISTS sly_dwdm_protect;
 drop table IF EXISTS sly_trans_system_gl ;
 drop table IF EXISTS sly_trans_system_gl_correct ;
 drop table IF EXISTS sly_trans_system_gl_bk_1 ;
 drop table IF EXISTS sly_trans_system_gl_bk;
 drop table IF EXISTS sly_dwdm_gl_olp_protect ;
 drop table IF EXISTS sly_olp_protect_gl_check ;
 DROP TABLE IF EXISTS sly_dwdm_gl_no_2;
 DROP TABLE IF EXISTS sly_dwdm_gl_no_1;
drop table if EXISTS cm_link_gl;
drop table if EXISTS cr_link_link_t4 ;
drop table if EXISTS cm_link_t13;
drop table if EXISTS cr_link_link_t2;
drop table IF EXISTS sly_dwdm_gl_cable_1 ;
--drop table IF EXISTS sly_dwdm_gl_cable ;
drop table IF EXISTS temp_sly_dwdm_num ;
drop table IF EXISTS temp_sly_dwdm_num2 ;
drop table IF EXISTS temp_sly_cable_err ;

drop table IF EXISTS temp_sly_dwdm_num3 ;
drop table IF EXISTS temp_sly_zc_err ;
drop table IF EXISTS device_white;
drop table IF EXISTS device_white1;
drop table IF EXISTS device_white2;
drop table IF EXISTS device_white3;
drop table IF EXISTS device_white4;
drop table IF EXISTS device_white5;
drop table IF EXISTS device_white6;
drop table IF EXISTS device_white7;
drop table IF EXISTS device_white8;
drop table IF EXISTS device_white9;
drop table IF EXISTS device_white10;
drop table IF EXISTS device_white11;
drop table IF EXISTS device_white_list;