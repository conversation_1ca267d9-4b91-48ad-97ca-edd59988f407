CREATE OR REPLACE PROCEDURE "wlyy_cz_sch"."cable_zc_section"("group_type" varchar)
 AS $BODY$
DECLARE
v1_group_type 	VARCHAR(255);
BEGIN
v1_group_type := group_type;
delete from sly_two_cable_zc_section sc where sc.group_type = v1_group_type;
delete from SLY_TWO_ROUTE sc where sc.group_type = v1_group_type;
delete from sly_two_eqp_zc_glnum sc where sc.group_type = v1_group_type;
delete from sly_two_zc_section_glnum sc where sc.group_type = v1_group_type;
delete from SLY_TWO_ZC_ERROR sc where sc.group_type = v1_group_type;
insert into sly_two_cable_zc_section
select  t.*,t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from sly_two_gl_group_cable t
left join res_cz_sch.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join res_cz_sch.CM_PIPELINE t2 on t2.id = pipeline_id
left join res_cz_sch.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id in (1111000001,1111000002,1111000003,1111000004)
left join res_cz_sch.cm_facility t3 on t3.id = t5.a_facility_id
left join res_cz_sch.cm_facility t4 on t4.id = t5.z_facility_id
where t.group_type = v1_group_type
union
select  t.*,t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from sly_two_gl_group_cable t
left join res_cz_sch.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join res_cz_sch.CM_PIPELINE t2 on t2.id = pipeline_id
left join res_cz_sch.CM_PIPELINE t6 on t6.id= t2.parent_id
left join res_cz_sch.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id in (1111000001,1111000002,1111000003,1111000004)
left join res_cz_sch.cm_facility t3 on t3.id = t5.a_facility_id
left join res_cz_sch.cm_facility t4 on t4.id = t5.z_facility_id
where t.group_type = v1_group_type
union
select  t.*,t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from sly_two_gl_group_cable t
left join res_cz_sch.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join res_cz_sch.CM_PIPELINE t5 on t5.id = pipeline_id and t5.spec_id in (1111000001,1111000002,1111000003,1111000004)
left join res_cz_sch.cm_facility t3 on t3.id = t5.a_facility_id
left join res_cz_sch.cm_facility t4 on t4.id = t5.z_facility_id
where t.group_type = v1_group_type;
ANALYZE sly_two_cable_zc_section;
insert into SLY_TWO_ROUTE
select T.* , '无穿管' ERROR from sly_two_cable_zc_section T
where T.zc_section_id is null
and T.Cable_Level<> '局内'
and T.Cable_Level<> '联络' and not exists
(select 1 from sly_two_cable_zc_section T1 where T.group_code = T1.group_code
and T1.cs_id = T.cs_id and T1.zc_section_id is not null)
and T.group_type = v1_group_type;
ANALYZE SLY_TWO_ROUTE;
delete from sly_two_cable_zc_section T
where exists (select 1 from SLY_TWO_ROUTE T1 where T1.group_code = T.group_code
and T1.Fiber_Id = T.Fiber_Id and T1.Error = '无穿管' and T1.group_type = v1_group_type) and T.group_type = v1_group_type;
ANALYZE sly_two_cable_zc_section;
commit;
insert into sly_two_eqp_zc_glnum
select t.group_code,t.group_type,count(distinct t.gl_code) glnum from sly_two_cable_zc_section t
where t.group_type = v1_group_type
group by t.group_code ,t.group_type;
ANALYZE sly_two_eqp_zc_glnum;
insert into sly_two_zc_section_glnum
select t.group_code , t.zc_section_code,t.group_type,count(distinct t.gl_code) glnum from sly_two_cable_zc_section t
where t.cable_level <> '局内'
group by t.group_code , t.zc_section_code,t.group_type;
ANALYZE sly_two_zc_section_glnum;
insert into SLY_TWO_ZC_ERROR
select t.* from sly_two_zc_section_glnum t
inner join sly_two_eqp_zc_glnum t1 on t1.group_code = t.group_code and t1.glnum = t.glnum
where t.group_type = v1_group_type ;
ANALYZE SLY_TWO_ZC_ERROR;
insert into SLY_TWO_ROUTE
select T.*,'同管道' from sly_two_cable_zc_section T
where T.Cable_Level <> '局内' and T.group_type = v1_group_type and exists(select 1 from SLY_TWO_ZC_ERROR T1 where T1.group_code = T.group_code
and T1.zc_section_code = T.zc_section_code and T1.group_type = v1_group_type);
ANALYZE SLY_TWO_ROUTE;
insert into SLY_TWO_ROUTE(group_id,group_code,group_name,group_type,gl_id,gl_code,cs_id,cs_code,cs_name,fiber_id,fiber_code,fiber_name,cable_id,cable_code,cable_name,spec_id,net_type_id,cable_level,error)
select T.*,'同光缆' from sly_two_gl_group_cable T
where exists(select 1 from SLY_TWO_SECTION_ERR_TMP T1 where T1.group_code = T.group_code and T1.cs_id = T.cs_id);
ANALYZE SLY_TWO_ROUTE;
commit;
END$BODY$
  LANGUAGE plpgsql