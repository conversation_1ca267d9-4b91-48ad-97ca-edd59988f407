// graph_query_device.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select d.id,d.name,d.code,d.spec_id,d.device_type_id,d.model_id,d.manufactor_id,s.res_type spec_name,dic.desc_china device_type, m.name model_name,f."name" manufactor_name,d.longitude,d.latitude,d.facility_id,coalesce(addr.full_name, d.address_desc) address,r.name facility_name,pos.pos_x,pos.pos_y,pos.ip_addr from cm_device d
		inner join pm_pub_res_type s on d.spec_id=s.res_type_id
		left join pm_pub_restriction dic on dic.serial_no=d.device_type_id
		left join dm_model m on d.model_id=m.id
		left join dm_manufactor f on d.manufactor_id=f.id
		left join rm_address addr on addr.id = d.address_id
		left join cm_facility r on d.facility_id=r.id
		left join public.res_phy_dev_daily pos on d.id=pos.phy_eqp_id
		where 1=1
		<if test="p.ids != null">
			and d.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.name != null and p.name != ''">
			and d.name = #{p.name}
		</if>
		<if test="p.code != null and p.code != ''">
			and d.code = #{p.code}
		</if>
		<if test="p.spec_id != null and p.spec_id != ''">
			and d.spec_id = #{p.spec_id}
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================