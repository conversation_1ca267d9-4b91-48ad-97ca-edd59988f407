// nrm.query_edge_otn_link_carry_link

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
		<if test="p.b != null">
			with recursive t_edge as (
			select 
			ll.route_id id,ll.route_obj_id a_id,ll.snc_id b_id,
			ll.snc_id upper_link_id,ll.route_obj_id lower_link_id,
			seg_no,grp_no,order_no,direction,a_ne_id,a_ptp_id,a_port_id,
			z_ne_id,z_ptp_id,z_port_id,data_domain_id,route_type,
			way_type,connect_direct,trace_id,route_obj_state,
			cast(1 as int) layer  from ct_otn_schema.t_snc_route ll
			where ll.a_ne_id!=ll.z_ne_id and  ll.snc_id in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				#{previous.id}::varchar
			</foreach>
			union
			select
			ll.route_id id,ll.route_obj_id a_id,t_edge.b_id b_id,
            ll.snc_id upper_link_id,ll.route_obj_id lower_link_id,
            ll.seg_no,ll.grp_no,ll.order_no,ll.direction,ll.a_ne_id,ll.a_ptp_id,ll.a_port_id,
            ll.z_ne_id,ll.z_ptp_id,ll.z_port_id,ll.data_domain_id,ll.route_type,
            ll.way_type,ll.connect_direct,ll.trace_id,ll.route_obj_state, t_edge.layer+1 layer
            from ct_otn_schema.t_snc_route ll
			inner join t_edge on ll.snc_id=t_edge.a_id where  ll.a_ne_id!=ll.z_ne_id and t_edge.layer&lt;=20
			)
			select * from t_edge
			union
			select
			ll.snc_id id,ll.snc_id a_id,ll.snc_id b_id,
            ll.snc_id upper_link_id,ll.snc_id lower_link_id,
            null,null,null,null,ll.a_ne_id,ll.a_ptp_id,ll.a_port_id,
            ll.z_ne_id,ll.z_ptp_id,ll.z_port_id,null,null,
            null,null ,ll.trace_id,null, 0 layer
			from ct_otn_schema.t_snc ll where ll.snc_id in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
            	#{previous.id}::varchar
            </foreach>

		</if>
		<if test="p.a != null">
			with recursive t_edge as (
			select
            	ll.route_id id,ll.route_obj_id a_id,ll.snc_id b_id,
            	ll.snc_id upper_link_id,ll.route_obj_id lower_link_id,
            	seg_no,grp_no,order_no,direction,a_ne_id,a_ptp_id,a_port_id,
            	z_ne_id,z_ptp_id,z_port_id,data_domain_id,route_type,
            	way_type,connect_direct,trace_id,route_obj_state,
            	cast(1 as int) layer  from ct_otn_schema.t_snc_route ll
            where ll.route_obj_id in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
			union all
			select
                ll.route_id id,ll.route_obj_id a_id,ll.snc_id b_id,
                ll.snc_id upper_link_id,ll.route_obj_id lower_link_id,
                seg_no,grp_no,order_no,direction,a_ne_id,a_ptp_id,a_port_id,
                z_ne_id,z_ptp_id,z_port_id,data_domain_id,route_type,
                way_type,connect_direct,trace_id,route_obj_state,
                cast(1 as int) layer  from ct_otn_schema.t_snc_route ll
			inner join t_edge on ll.route_obj_id=t_edge.b_id  where t_edge.layer&lt;=20
			)
			select * from t_edge
			union all
            	select
            		ll.snc_id id,ll.snc_id a_id,ll.snc_id b_id,
                    ll.snc_id upper_link_id,ll.snc_id lower_link_id,
                    null,null,null,null,ll.a_ne_id,ll.a_ptp_id,ll.a_port_id,
                    ll.z_ne_id,ll.z_ptp_id,ll.z_port_id,null,null,
                    null,null ,ll.trace_id,null, 0 layer
            	    from ct_otn_schema.t_snc ll where ll.snc_id in
            	<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
                    #{previous.id}::varchar
                </foreach>
			
		</if>
		<if test="p.a == null and p.b == null">
			select
                ll.snc_id id,ll.snc_id a_id,ll.snc_id b_id,
                ll.snc_id upper_link_id,ll.snc_id lower_link_id,
                null,null,null,null,ll.a_ne_id,ll.a_ptp_id,ll.a_port_id,
                ll.z_ne_id,ll.z_ptp_id,ll.z_port_id,null,null,
                null,null ,ll.trace_id,null, 0 layer
            from ct_otn_schema.t_snc ll
			where 1=2
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================