// nrm.query_node_jxgx.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select l.id,l.spec_id,l.a_physic_device_id,l.z_physic_device_id,'' as name,'' as code,l.a_port_id,l.z_port_id,ad.name a_device_name,zd.name z_device_name from cm_link l
		left join cm_device ad on l.a_physic_device_id=ad.id
		left join cm_device zd on l.z_physic_device_id=zd.id
		where 1=1 and l.spec_id in (1131100003,1131100001)
		<if test="p.ids != null">
			and l.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================