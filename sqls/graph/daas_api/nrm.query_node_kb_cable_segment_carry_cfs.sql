// nrm.query_node_kb_cable_segment_carry_cfs

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
with t_node 
as (
select t1.prod_id ,t1.cbl_sect_id ,t1.cbl_sect_spec_id,t4.id as service_id,t4.spec_id service_spec_id,tp.res_type service_spec_name,t4.access_code,t4.name||'-'||t4.access_code service_name,cl.code service_code,route.id route_id,route.name route_name,route.code route_code,cl.code link_code,cl.name link_name from public.PROD_CARRIER_CBL_SECTION_DAILY t1 
left join rm_product t2 on t1.prod_id=t2.crm_product_id
left join rr_service_product t3 on t3.product_id=t2.id
left join rm_service t4 on t3.service_id=t4.id
left join cr_link_cable lc on lc.cable_id=t1.line_seq::numeric
left join cr_link_link ll on lc.link_id=ll.lower_link_id
left join cm_link route on ll.upper_link_id=route.id
left join rr_service_entity rse on t4.id = rse.service_id and rse.spec_id='2311611310000'
left join cm_link cl on cl.id=rse.entity_id
left join pm_pub_res_type tp on tp.res_type_id=t4.spec_id
)
select * from t_node where 1=1
		<if test="p.ids != null">
			and t_node.cbl_sect_id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		
		<if test="p.is_empty">
			and 1 = 2 
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================