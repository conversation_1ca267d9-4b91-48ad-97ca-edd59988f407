// nrm.query_node_pon_subport.sql

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select id,name,code,spec_id,port_type_id,physic_device_id,logic_device_id from cm_port
		where 1=1 and spec_id=1041200007
		<if test="p.ids != null">
			and id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.name != null and p.name != ''">
			and name = #{p.name}
		</if>
		<if test="p.code != null and p.code != ''">
			and code = #{p.code}
		</if>
		<if test="p.spec_id != null and p.spec_id != ''">
			and spec_id = #{p.spec_id}
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================