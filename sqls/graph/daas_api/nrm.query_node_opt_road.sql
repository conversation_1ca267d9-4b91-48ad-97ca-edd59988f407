// nrm.query_node_opt_road



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
<select>
SELECT   
    cl.id,  
    cl.spec_id,  
    cl.a_physic_device_id,  
    cl.z_physic_device_id,  
    cl.name,  
    cl.code,  
    cl.a_port_id,  
    cl.z_port_id  ,
    zcp.code  as z_port_code,
    acp.code  as a_port_code,
		af.id as a_facility_id,
		zf.id as z_facility_id,
		af.code as a_facility_code,
		zf.code as z_facility_code,
		af.parent_id as a_site_id,
		zf.parent_id as z_site_id
FROM    cm_link cl
left join  cm_port zcp   on zcp.id =cl.z_port_id 
left join  cm_port acp   on acp.id =cl.a_port_id
left join  cm_device ad on ad.id = cl.a_physic_device_id
left join  cm_device zd on zd.id = cl.z_physic_device_id
left join  cm_facility af on ad.facility_id = af.id
left join  cm_facility zf on zd.facility_id = zf.id 

where 1=1 and cl.spec_id=1132400006

    <if test="p.id  == null and p.ids  == null and p.codes == null and p.code == null">
			and 1=2
		</if>

	    <if test="p.codes != null">
			and cl.code in
			<foreach collection="p.codes" item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
		</if>
		<if test="p.code != null">
        and cl.code = #{p.code}
    </if>
    <if test="p.id != null">
        and cl.id = #{p.id}::numeric
    </if>
		<if test="p.ids != null">
			and cl.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		
</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================