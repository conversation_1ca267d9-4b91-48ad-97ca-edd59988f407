// nrm.query_node_pipe_segment.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select pl.id,pl.spec_id,pl.name,pl.code,pl.a_facility_id,pl.z_facility_id,af.name a_name,zf.name z_name,af.pos_x a_pos_x,af.pos_y a_pos_y,zf.pos_x z_pos_x,zf.pos_y z_pos_y from cm_pipeline pl
		inner join public.res_bse_equipment_daily af on pl.a_facility_id=af.bse_eqp_id
		inner join public.res_bse_equipment_daily zf on pl.z_facility_id=zf.bse_eqp_id
		where  pl.parent_id is null
		<if test="p.ids != null">
			and pl.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.codes != null">
			and pl.code in
			<foreach collection="p.codes" item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
		</if>
		
		<if test="p.code != null and p.code != ''">
			and pl.code = #{p.code}
		</if>
		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize}, #{currentPage});
return data;

// ===================================================================================