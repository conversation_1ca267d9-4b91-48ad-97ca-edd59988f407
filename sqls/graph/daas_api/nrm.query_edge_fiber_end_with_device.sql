// nrm.query_edge_fiber_end_with_device

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
		with t_edge as (
        select l.id , l.id a_id,l.spec_id a_spec_id,d.id b_id,d.spec_id b_spec_id from cm_cable l
		inner join cm_cable c on l.parent_id=c.id
		inner join cm_device d on c.a_device_id=d.id
		union all
		select l.id , l.id a_id,l.spec_id a_spec_id,d.id b_id,d.spec_id b_spec_id from cm_cable l
		inner join cm_cable c on l.parent_id=c.id
		inner join cm_device d on c.z_device_id=d.id
		)
		select * from t_edge
		where 1=1
		<if test="p.a == null and p.b == null">
			and 1=2
		</if>
		<if test="p.a != null">
			and (a_id,a_spec_id) in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		<if test="p.b != null">
			and (b_id,b_spec_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize}, #{currentPage});
return data;

// ===================================================================================