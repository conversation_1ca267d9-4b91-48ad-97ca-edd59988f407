// nrm.query_edge_pon_carry_subport

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
		with t_edge as (
        select l.id , p.id a_id,p.spec_id a_spec_id,sp.id b_id,sp.spec_id b_spec_id from cm_link l 
		inner join cm_port sp on l.a_port_id=sp.id 
		inner join cm_port p on l.z_port_id=p.id
		where l.spec_id=1132100001 and sp.spec_id=1041200007 and p.spec_id=1041100004
		union all
		select l.id , p.id a_id,p.spec_id a_spec_id,sp.id b_id,sp.spec_id b_spec_id from cm_link l 
		inner join cm_port sp on l.z_port_id=sp.id 
		inner join cm_port p on l.a_port_id=p.id
		where l.spec_id=1132100001 and sp.spec_id=1041200007 and p.spec_id=1041100004
		)
		select * from t_edge
		where 1=1
		<if test="p.a == null and p.b == null">
			and 1=2
		</if>
		<if test="p.a != null">
			and (a_id,a_spec_id) in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		<if test="p.b != null">
			and (b_id,b_spec_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================