// nrm.query_node_cable_segment.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select l.id,l.name,l.code,l.a_device_id,l.z_device_id,ad.name a_name,zd.name z_name,l.spec_id,
        l.a_device_id as a_physic_device_id,l.z_device_id as z_physic_device_id
        from cm_cable l
		left join cm_device ad on ad.id=l.a_device_id
		left join cm_device zd on zd.id=l.z_device_id
		where 1=1 and l.spec_id=1121000002
		<if test="p.ids != null">
			and l.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.code != null and p.code != ''">
			and l.code = #{p.code}
		</if>
		<if test="p.codes != null">
			and l.code  in
			<foreach collection="p.codes " item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
		</if>

		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize}, #{currentPage});
return data;

// ===================================================================================