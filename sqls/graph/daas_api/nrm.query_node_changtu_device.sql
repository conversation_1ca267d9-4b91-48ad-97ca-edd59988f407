// nrm.query_node_changtu_device



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select d.id,d.name,d.code,d.spec_id,d.device_type_id,d.model_id,d.manufactor_id,s.name spec_name,dic.name device_type, m.name model_name,
        f."name" manufactor_name,
        d.longitude,d.latitude,d.facility_id,coalesce(addr.full_name, d.address_desc) address,r.name facility_name from ct_changtu_schema.cm_device d
		inner join ct_meta_schema.mm_entity_spec s on d.spec_id=s.id
		left join ct_meta_schema.mm_dictvalue dic on dic.id=d.device_type_id
		left join ct_changtu_schema.dm_model m on d.model_id=m.id
		left join ct_changtu_schema.dm_manufactor f on d.manufactor_id=f.id
		left join ct_changtu_schema.rm_address addr on addr.id = d.address_id
		left join ct_changtu_schema.cm_facility r on d.facility_id=r.id
		where 1=1
		<if test="p.ids != null">
			and d.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.name != null and p.name != ''">
			and d.name = #{p.name}
		</if>
		<if test="p.code != null and p.code != ''">
			and d.code = #{p.code}
		</if>
		<if test="p.spec_id != null and p.spec_id != ''">
			and d.spec_id = #{p.spec_id}
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================