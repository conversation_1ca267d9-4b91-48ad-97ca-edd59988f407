// nrm.query_edge_opt_road_carry_link

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
with t_edge 
as (
        select road.id as a_id,road.spec_id  as a_spec_id,l.id as b_id,l.spec_id  as b_spec_id
		from cm_link l ,cr_link_link rll1 ,cr_link_link road2Route ,cm_link road 
		 where road.id = road2Route.upper_link_id  and road.spec_id = 1132400006
		 and road2Route.lower_link_id  = rll1.lower_link_id 
		 and rll1.upper_link_id  = l.id
		 union all
		 select road.id as a_id,road.spec_id  as a_spec_id,route.id as b_id,route.spec_id  as b_spec_id
		from cm_link route ,cr_link_link road2Route ,cm_link road 
		 where road.id = road2Route.upper_link_id  and road.spec_id = 1132400006
		 and road2Route.lower_link_id  = route.id 
		 union all
		 select a_id,a_spec_id,b_id,b_spec_id from public.vw_pm_opt_road_carry_logic_link
		 
)
		select distinct * from t_edge
		where 1=1
		<if test="p.a == null and p.b == null">
			and 1=2
		</if>
		<if test="p.a != null">
			and (a_id,a_spec_id) in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		<if test="p.b != null">
			and (b_id,b_spec_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================