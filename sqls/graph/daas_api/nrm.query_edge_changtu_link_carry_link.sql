// nrm.query_edge_changtu_link_carry_link

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
		<if test="p.b != null">
			with recursive t_edge as (
			select 
			ll.id,ll.spec_id,lower_link_id a_id,ll.lower_link_spec_id a_spec_id,ll.upper_link_id b_id,ll.upper_link_spec_id b_spec_id,cast(ll.seq as text) a_seq, 
			ll.upper_link_id, ll.upper_link_spec_id,ll.lower_link_id,ll.lower_link_spec_id, cast(1 as int) layer  from ct_changtu_schema.cr_link_link ll
			where   (upper_link_id,upper_link_spec_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
			union all
			select
			ll.id,ll.spec_id,ll.lower_link_id a_id,ll.lower_link_spec_id a_spec_id,t_edge.b_id,t_edge.b_spec_id,t_edge.a_seq||'-'||ll.seq a_seq,
			ll.upper_link_id, ll.upper_link_spec_id,ll.lower_link_id,ll.lower_link_spec_id, t_edge.layer+1 layer  from ct_changtu_schema.cr_link_link ll
			inner join t_edge on ll.upper_link_id=t_edge.a_id and ll.upper_link_spec_id=t_edge.a_spec_id where t_edge.layer&lt;=10
			)
			select id,spec_id,a_id,a_spec_id,b_id,b_spec_id,a_seq,upper_link_id,upper_link_spec_id,lower_link_id,lower_link_spec_id,min(layer) layer from t_edge
			group by id,spec_id,a_id,a_spec_id,b_id,b_spec_id,a_seq,upper_link_id,upper_link_spec_id,lower_link_id,lower_link_spec_id
			union all
			select d.id,0,d.id a_id,d.spec_id a_spec_id,d.id b_id,d.spec_id b_spec_id,'0',null,null,null,null,0 from ct_changtu_schema.cm_link d
			where (d.id,d.spec_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		<if test="p.a != null">
			with recursive t_edge as (
			select ll.id,ll.spec_id,lower_link_id a_id,ll.lower_link_spec_id a_spec_id,ll.upper_link_id b_id,ll.upper_link_spec_id b_spec_id,cast(ll.seq as text) a_seq, 
			ll.upper_link_id, ll.upper_link_spec_id,ll.lower_link_id,ll.lower_link_spec_id, cast(1 as int) layer from ct_changtu_schema.cr_link_link ll
			where    (ll.lower_link_id,ll.lower_link_spec_id) in 
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
			union all
			select
			ll.id,ll.spec_id,ll.lower_link_id a_id,ll.lower_link_spec_id a_spec_id,t_edge.b_id,t_edge.b_spec_id,t_edge.a_seq||'-'||ll.seq a_seq,
			ll.upper_link_id, ll.upper_link_spec_id,ll.lower_link_id,ll.lower_link_spec_id, t_edge.layer+1 layer  from ct_changtu_schema.cr_link_link ll
			inner join t_edge on ll.lower_link_id=t_edge.b_id and ll.lower_link_spec_id=t_edge.b_spec_id where t_edge.layer&lt;=10 
			)
			select * from t_edge
			
		</if>
		<if test="p.a == null and p.b == null">
			select ll.id,ll.spec_id,lower_link_id a_id,ll.lower_link_spec_id a_spec_id,ll.upper_link_id b_id,ll.upper_link_spec_id b_spec_id,cast(ll.seq as text) a_seq, 
			ll.upper_link_id, ll.upper_link_spec_id,ll.lower_link_id,ll.lower_link_spec_id from ct_changtu_schema.cr_link_link ll
			where 1=2
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================