// nrm.query_edge_otn_link_end_with_device

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
		with t_edge as (
    select l.snc_id , l.snc_id a_id,d.ne_id b_id
    from ct_otn_schema.t_snc l inner join ct_otn_schema.t_trans_ne d on l.a_ne_id=d.ne_id
		union all
		select l.snc_id , l.snc_id a_id,d.ne_id b_id
		from ct_otn_schema.t_snc l inner join ct_otn_schema.t_trans_ne d on l.z_ne_id=d.ne_id
		)
		select * from t_edge
		where 1=1
		<if test="p.a == null and p.b == null">
			and 1=2
		</if>
		<if test="p.a != null">
			and (a_id) in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id})
			</foreach>
		</if>
		<if test="p.b != null">
			and (b_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id})
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize}, #{currentPage});
return data;

// ===================================================================================