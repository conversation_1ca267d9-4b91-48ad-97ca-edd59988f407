// nrm.query_node_ware.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select w.id,w.name,w.code,w.spec_id,w.model_id,w.manufactor_id,s.res_type spec_name,
        m.name model_name,f."name" manufactor_name,w.facility_id,
        r.name facility_name,d.id device_id,d.name device_name,d.code device_code from cm_ware w
		inner join pm_pub_res_type s on w.spec_id=s.res_type_id
		left join dm_model m on w.model_id=m.id
		left join dm_manufactor f on w.manufactor_id=f.id
		left join rm_address addr on addr.id = w.address_id
		left join cm_facility r on w.facility_id=r.id
        left join cr_device_ware dw on dw.spec_id='1020110310000' and dw.child_id=w.id
        left join cm_device d on d.id=dw.parent_id
		where 1=1
		<if test="p.ids != null">
			and w.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.name != null and p.name != ''">
			and d.name = #{p.name}
		</if>
		<if test="p.code != null and p.code != ''">
			and d.code = #{p.code}
		</if>
		<if test="p.spec_id != null and p.spec_id != ''">
			and d.spec_id = #{p.spec_id}
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================