// nrm.query_edge_pipe_carry_cable

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
with t_edge 
as (
/*select pc.id,ppl.id a_id,ppl.spec_id a_spec_id,c.id b_id,c.spec_id b_spec_id from cm_cable c
inner join cr_pipeline_cable pc on c.id=pc.cable_id
inner join cm_pipeline pl on pc.pipeline_id=pl.id
inner join cm_pipeline ppl on pl.parent_id = ppl.id


union all

select pc.id,ppl.id a_id,ppl.spec_id a_spec_id,c.id b_id,c.spec_id b_spec_id from cm_cable c
inner join cr_pipeline_cable pc on c.id=pc.cable_id
inner join cm_pipeline pl on pc.pipeline_id=pl.id
inner join cm_pipeline ppl on pl.parent_id = ppl.id
inner join cm_pipeline pppl on ppl.parent_id=pppl.id*/

select  t1.id, t5.id a_id, t5.spec_id a_spec_id, c.id b_id,c.spec_id b_spec_id 
from cm_cable c
inner join cr_pipeline_cable t1 on c.id = t1.cable_id
inner join   CM_PIPELINE t2 on t2.id = t1.pipeline_id 
inner join   CM_PIPELINE t5 on t5.id = t2.parent_id 
where t5.parent_id is null
union all
select  t1.id , t5.id a_id, t5.spec_id a_spec_id, c.id b_id,c.spec_id b_spec_id 
from  cm_cable c
inner join cr_pipeline_cable t1 on c.id = t1.cable_id
inner join   CM_PIPELINE t2 on t2.id = pipeline_id  
inner join   CM_PIPELINE t6 on t6.id =t2.parent_id 
inner join   CM_PIPELINE t5 on t5.id = t6.parent_id
where t5.parent_id is null
UNION all
select  t1.id , t2.id a_id, t2.spec_id  a_spec_id, c.id b_id,c.spec_id b_spec_id 
from  cm_cable c
inner join cr_pipeline_cable t1 on c.id = t1.cable_id
inner join CM_PIPELINE t2 on t2.id = pipeline_id
where t2.parent_id is null
)
select * from t_edge where 1=1
		<if test="p.a == null and p.b == null">
			and 1=2
		</if>
		<if test="p.a != null">
			and (a_id,a_spec_id) in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		<if test="p.b != null">
			and (b_id,b_spec_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================