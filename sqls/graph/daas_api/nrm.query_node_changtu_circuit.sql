// nrm.query_node_changtu_circuit.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
		with t_link as (
		select l.id,l.name,l.code,l.spec_id,l.a_physic_device_id,l.z_physic_device_id,l.a_port_id,l.z_port_id,s.access_code from ct_changtu_schema.cm_link l
		inner join rr_service_entity s2l on s2l.entity_id=l.id and s2l.entity_spec_id=l.spec_id
		inner join rm_service s on s2l.service_id=s.id
		)
		select * from t_link l where 1=1
		<if test="p.ids != null">
			and l.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		
		<if test="p.codes != null">
			and l.code in
			<foreach collection="p.codes" item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
		</if>
		
		<if test="p.code != null and p.code != ''">
			and l.code = #{p.code}
		</if>
		<if test="p.access_code != null and p.access_code != ''">
			and l.access_code = #{p.access_code}
		</if>
		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================