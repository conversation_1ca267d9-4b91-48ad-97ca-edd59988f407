// nrm.query_edge_facility_contain_reel

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
with t_edge 
as (
    select assistance.id,assistance.facility_id a_id,assistance.id b_id from CM_ASSISTANCE  assistance
    where assistance.spec_id=1051100006
)
select * from t_edge where 1=1
		<if test="p.a == null and p.b == null">
			and 1=2
		</if>
		<if test="p.a != null">
			and a_id in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				#{previous.id}::numeric
			</foreach>
		</if>
		<if test="p.b != null">
			and b_id in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				#{previous.id}::numeric
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================