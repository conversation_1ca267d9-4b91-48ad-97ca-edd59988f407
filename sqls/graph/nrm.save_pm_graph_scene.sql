hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.DateTimeUdfSource' as time;
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
var saveFun = @@sql(p)<%
INSERT INTO pm_graph_scene(id,name,code,create_time,start_node,edges)
values (
#{p.id}::numeric,#{p.name},#{p.code},#{p.create_time}::timestamp,#{p.start_node},#{p.edges}
)
on conflict (id) 
do update set name = #{p.name}, code=#{p.code},  create_time=#{p.create_time}::timestamp,start_node=#{p.start_node},edges=#{p.edges}
;
%>;

var setDefaultData = (p) -> {
    var newMap = collect.newMap(p);
	if (p.id==null) {
		var newId = nextId('seq_pm_graph_scene',24,'');
		run newMap.put('create_time',time.format(time.now(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
		run newMap.put('modify_date',time.format(time.now(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
		run newMap.put('id',newId.shortId);
	}else{
		run newMap.put('modify_date',time.format(time.now(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
	}
	
    return newMap.data()
};

var p = setDefaultData(${param});

var result = saveFun(p);

return p;