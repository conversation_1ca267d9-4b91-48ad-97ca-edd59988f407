// nrm.query_pm_graph_scene.sql

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@mybatis(p)<%
    <select>
        select t.id,t.name,t.code,t.create_time,t.start_node,t.edges from pm_graph_scene t
		where 1=1
		
		<if test="p.name != null and p.name != ''">
			and t.name like  '%'||#{p.name}||'%'
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};

// ===================================================================================