-- 为隐患整改表添加区县和专业字段
-- 执行时间：2024-12-09

-- 1. 添加区县字段
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS district VARCHAR(100);

-- 2. 添加专业字段
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS speciality VARCHAR(50) DEFAULT '数据';

-- 3. 创建索引
CREATE INDEX IF NOT EXISTS idx_hazard_remediation_district ON hazard_remediation(district);
CREATE INDEX IF NOT EXISTS idx_hazard_remediation_speciality ON hazard_remediation(speciality);

-- 4. 添加字段注释
COMMENT ON COLUMN hazard_remediation.district IS '区县';
COMMENT ON COLUMN hazard_remediation.speciality IS '专业：数据/传输/接入/核心/综合';

-- 5. 更新现有记录的专业字段为默认值（如果为空）
UPDATE hazard_remediation SET speciality = '数据' WHERE speciality IS NULL OR speciality = '';

-- 6. 验证字段添加成功
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'hazard_remediation' 
AND column_name IN ('district', 'speciality');
