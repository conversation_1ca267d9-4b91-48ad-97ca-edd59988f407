create table pm_busi_user_scene_member(
id numeric(12) primary key,
scene_id numeric(12),
service_id numeric(24),
area_id numeric(24),
area_name varchar(200),
service_type_id numeric(24),
service_type_name varchar(200),
a_customer_name varchar(200),
z_customer_name varchar(200),
customer_name varchar(200),
access_code varchar(200),
a_access_code varchar(200),
z_access_code varchar(200),
circuit_code varchar(200),
address_id numeric(24),
address_name varchar(200),
a_device_type_name varchar(200),
a_device_id numeric(24),
a_device_name varchar(200),
a_device_code varchar(200),
z_device_type_name varchar(200),
z_device_id numeric(24),
z_device_name varchar(200),
z_device_code varchar(200),
a_address_name varchar(200),
a_address_id numeric(24),
z_address_name varchar(200),
z_address_id numeric(24)
);

drop index idx_pm_busi_user_scene_member;
create unique index idx_pm_busi_user_scene_member on pm_busi_user_scene_member(circuit_code,area_id,scene_id);

alter table pm_busi_user_scene_member add a_node_name varchar(200);
alter table pm_busi_user_scene_member add z_node_name varchar(200);


drop table pm_busi_user_scene_node;
create table pm_busi_user_scene_node (
id numeric(12) primary key,
scene_id numeric(12) not null,
node_name varchar(200) not null,
node_type varchar(200) not null
);

create unique index uidx_pm_busi_user_scene_node_name on pm_busi_user_scene_node(scene_id,node_name);