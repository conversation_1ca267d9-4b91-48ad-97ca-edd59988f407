// pm_busi_user_scene_query.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select s.id,a.name region,s.region_id,s.name,s.create_time,s.modify_time  from pm_busi_user_scene s
		left join rm_area a on s.region_id=a.id
		
		where 1=1 
		
		<if test="p.name != null and p.name != ''">
			and s.name like  '%'||#{p.name}||'%'
		</if>
		<if test="p.region_id != null and p.region_id !=''">
			and s.region_id =  #{p.region_id}::numeric
		</if>
		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo(#{pageSize}, #{currentPage});
return {
"data": data,
"pageInfo": pageInfo
};

// ===================================================================================