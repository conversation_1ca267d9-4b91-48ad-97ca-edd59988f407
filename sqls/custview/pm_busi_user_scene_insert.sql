hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var saveFun = @@sql(p)<%
INSERT INTO pm_busi_user_scene
(id, region_id, name,create_time,modify_time) 
VALUES 
(#{p.id}::numeric, #{p.region_id}::numeric, #{p.name}, #{p.create_time}::timestamp, #{p.modify_time}::timestamp) 
ON CONFLICT (id) do update set region_id=#{p.region_id}::numeric, name=#{p.name},modify_time=#{p.modify_time}::timestamp
%>;

var generateId = (data) -> {
    var newMap = collect.newMap(data);
	if (data['id'] == null || data['id'] == '' ) {
		var newId = nextId('seq_pm_busi_user_scene',24,'0' )
		run newMap.put('id',newId.shortId);
	}
    return newMap.data()
};

var p = generateId(${param});
var result =  saveFun(p);
return {
	"result": result
};