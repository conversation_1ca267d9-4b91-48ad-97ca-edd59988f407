hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var saveFun = @@sql(p)<%
update pm_busi_user_scene_member
 set a_node_name=#{p.a_node_name}, z_node_name=#{p.z_node_name} where id=#{p.id}
%>;

var generateId = (data) -> {
    var newMap = collect.newMap(data);
	if (data['id'] == null || data['id'] == '' ) {
		var newId = nextId('seq_pm_busi_user_scene_member',24,'0' )
		run newMap.put('id',newId.shortId);
	}
    return newMap.data()
};

var p = generateId(${param});
var result =  saveFun(p);
return {
	"result": result
};