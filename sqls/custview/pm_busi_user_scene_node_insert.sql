// pm_busi_user_scene_node_insert.sql

hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var saveFun = @@sql(p)<%
INSERT INTO pm_busi_user_scene_node
(id,scene_id,node_type,node_name) 
VALUES 
(
#{p.id}::numeric, 
#{p.scene_id}::numeric,
#{p.node_type},
#{p.node_name}
) ON CONFLICT (id) do update set scene_id=#{p.scene_id}::numeric, node_type=#{p.node_type},node_name=#{p.node_name}
%>;

var generateId = (data) -> {
    var newMap = collect.newMap(data);
	if (data['id'] == null || data['id'] == '' ) {
		var newId = nextId('seq_pm_busi_user_scene_member',24,'0' )
		run newMap.put('id',newId.shortId);
	}
    return newMap.data()
};

var p = generateId(${param});
var result =  saveFun(p);
return {
	"result": result
};