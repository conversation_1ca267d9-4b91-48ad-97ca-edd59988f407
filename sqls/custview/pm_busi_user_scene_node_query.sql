// pm_busi_user_scene_node_query.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select id,node_type,node_name,scene_id from pm_busi_user_scene_node s
		where scene_id = #{p.scene_id}::numeric
		and (1=0
		<if test="p.name != null and p.name != ''">
			or s.node_name like  '%'||#{p.name}||'%'
		</if>
		<if test="p.name == null or p.name == ''">
			or 1=1
		</if>	
		)
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo(#{pageSize}, #{currentPage});
return {
"data": data,
"pageInfo": pageInfo
};

// ===================================================================================