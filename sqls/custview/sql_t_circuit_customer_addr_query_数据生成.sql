drop table if exists t_circuit_customer_addr_query_1 cascade;
create table t_circuit_customer_addr_query_1 
as
select 
coalesce(rs.id,1000000000000000000000000+cl.id)::varchar(50) id,
cast(null as numeric(24)) service_id,
rs.start_use_date,
rarea.id area_id,
rarea.name area_name, 
pprt.res_type_id service_type_id,pprt.res_type service_type_name, 
rc1.name a_customer_name, rc2.name z_customer_name,rc.name customer_name,
rs.access_code access_code, rs.a_access_code,rs.z_access_code,
cl.code circuit_code, cl.notes,
ra.id address_id,ra.name address_name, 
prt1.res_type a_device_type_name,cd1.id a_device_id,cd1.name a_device_name,cd1.code a_device_code,
prt2.res_type z_device_type_name,cd2.id z_device_id,cd2.name z_device_name,cd2.code z_device_code,
a_ra.name a_address_name, a_ra.id a_address_id,
z_ra.name z_address_name, z_ra.id z_address_id,
cl.id as link_id,le.service_type_id link_type_id
from public.cm_link cl 
inner join public.pe_link le on cl.id=le.link_id
left join public.rr_service_entity rse on cl.id = rse.entity_id and rse.spec_id='2311611310000'
left join public.rm_service rs on rse.service_id=rs.id and rs.spec_id in (2080,2840,2217,3933,2580,2089,2782,3861,2105,2464,4390,2733,3153,2082,2851,2839,2083,2124,2176,2842,2915,2706,2834,2716,2118,2783,700001221,2462,2693,2473,2805,9310192086,2784,2122)
left join public.rm_area rarea on rs.sharding_id = rarea.id
left join public.pm_pub_res_type pprt on rs.spec_id = pprt.res_type_id
left join  public.rm_customer rc  on rs.customer_id = rc.id
left join  public.rm_customer rc1  on rs.a_customer_id = rc1.id
left join  public.rm_customer rc2  on rs.a_customer_id = rc2.id
left join public.rm_address ra on rs.a_address_id = ra.id
left join public.cm_device cd1 on cl.a_physic_device_id = cd1.id
left join public.cm_device cd2 on cl.z_physic_device_id = cd2.id
left join public.pm_pub_res_type prt1 on cd1.spec_id = prt1.res_type_id
left join public.pm_pub_res_type prt2 on cd2.spec_id = prt2.res_type_id
left join public.rm_address a_ra on cd1.address_id = a_ra.id
left join public.rm_address z_ra on cd2.address_id = z_ra.id
where le.service_type_id in (81708243,81708247,81708253,81708254) and rse.id is null ;


drop table if exists t_circuit_customer_addr_query_2_1 cascade;
create table t_circuit_customer_addr_query_2_1
as
select rs.id service_id,
rs.start_use_date,
rs.sharding_id,
rs.access_code access_code, rs.a_access_code,rs.z_access_code,
rs.spec_id,rs.customer_id,rs.a_customer_id,rs.z_customer_id,rs.a_address_id,rs.z_address_id
from public.rm_service rs
where rs.spec_id in (2080,2840,2217,3933,2580,2089,2782,3861,2105,2464,4390,2733,3153,2082,2851,2839,2083,2124,2176,2842,2915,2706,2834,2716,2118,2783,700001221,2462,2693,2473,2805,9310192086,2784,2122,5083)
;



drop table if exists t_circuit_customer_addr_query_2_2 cascade;
create table t_circuit_customer_addr_query_2_2
as
select
rs.service_id||'_'||coalesce(cl.id,0,cl.id) id,
rs.service_id,
rs.start_use_date,
rs.sharding_id,
rs.access_code access_code, rs.a_access_code,rs.z_access_code,
cl.code circuit_code,  cl.notes,
cl.id as link_id,le.service_type_id link_type_id,
cl.a_physic_device_id,cl.z_physic_device_id,rs.spec_id,rs.customer_id,rs.a_customer_id,rs.z_customer_id,rs.a_address_id,rs.z_address_id
from t_circuit_customer_addr_query_2_1 rs
left join public.rr_service_entity rse on rs.service_id = rse.service_id and rse.spec_id='2311611310000'
left join public.cm_link cl on cl.id=rse.entity_id
left join public.pe_link le on cl.id=le.link_id
;


drop table if exists t_circuit_customer_addr_query_2_3 cascade;
create table t_circuit_customer_addr_query_2_3
as
select
rs.id,
rs.service_id,
rs.start_use_date,
rarea.id area_id,
rarea.name area_name,
pprt.res_type_id service_type_id,pprt.res_type service_type_name,
rc1.name a_customer_name, rc2.name z_customer_name,rc.name customer_name,
rs.access_code access_code, rs.a_access_code,rs.z_access_code,
rs.circuit_code,  rs.notes,
ra.id address_id,ra.name address_name,
prt1.res_type a_device_type_name,cd1.id a_device_id,cd1.name a_device_name,cd1.code a_device_code,
prt2.res_type z_device_type_name,cd2.id z_device_id,cd2.name z_device_name,cd2.code z_device_code,
a_ra.name a_address_name, a_ra.id a_address_id,
z_ra.name z_address_name, z_ra.id z_address_id,
rs.link_id,rs.link_type_id
from t_circuit_customer_addr_query_2_2 rs
left join public.rm_area rarea on rs.sharding_id = rarea.id
left join public.pm_pub_res_type pprt on rs.spec_id = pprt.res_type_id
left join  public.rm_customer rc  on rs.customer_id = rc.id
left join  public.rm_customer rc1  on rs.a_customer_id = rc1.id
left join  public.rm_customer rc2  on rs.z_customer_id = rc2.id
left join public.rm_address ra on rs.a_address_id = ra.id
left join public.cm_device cd1 on rs.a_physic_device_id = cd1.id
left join public.cm_device cd2 on rs.z_physic_device_id = cd2.id
left join public.pm_pub_res_type prt1 on cd1.spec_id = prt1.res_type_id
left join public.pm_pub_res_type prt2 on cd2.spec_id = prt2.res_type_id
left join public.rm_address a_ra on cd1.address_id = a_ra.id
left join public.rm_address z_ra on cd2.address_id = z_ra.id
;


drop table if exists t_circuit_customer_addr_query_2 cascade;
alter table t_circuit_customer_addr_query_2_3 rename to t_circuit_customer_addr_query_2;

drop table if exists t_circuit_customer_addr_query3 cascade;
create table t_circuit_customer_addr_query3
as
select * from t_circuit_customer_addr_query_1
union
select * from t_circuit_customer_addr_query_2;

drop table t_circuit_customer_addr_query3_1 cascade;
create table t_circuit_customer_addr_query3_1
as
select t.*,af.id a_facility_id,zf.id z_facility_id,af.address_desc a_facility_address,zf.address_desc z_facility_address,af.name a_facility_name,zf.name z_facility_name from t_circuit_customer_addr_query3 t
left join public.cm_device ad on t.a_device_id=ad.id
left join public.cm_device zd on t.z_device_id=zd.id
left join public.cm_facility af on ad.facility_id=af.id
left join public.cm_facility zf on zd.facility_id=zf.id
;

drop table t_service_circuit_code;
create table t_service_circuit_code as select * from ${o3_res_schema}.rv_service where pkey='50107';
create index idx_t_service_circuit_code_01 on t_service_circuit_code(service_id);

delete from t_service_circuit_code t1 where exists (
select 1 from t_service_circuit_code t2 where t1.service_id=t2.service_id and t1.role_id>t2.role_id
);

drop table t_circuit_customer_addr_query4;
create table t_circuit_customer_addr_query4
as
select t1.*,s.value s_circuit_code from t_circuit_customer_addr_query3_1 t1
left join t_service_circuit_code s on t1.service_id=s.service_id ;

create index idx_t_circuit_customer_addr_query4_serv4 on t_circuit_customer_addr_query4(service_id);


update t_circuit_customer_addr_query4 set circuit_code = s_circuit_code where s_circuit_code is not null and s_circuit_code<>'';
update t_circuit_customer_addr_query4 set access_code=circuit_code where access_code is null and circuit_code is not null;

update t_circuit_customer_addr_query4 set circuit_code=access_code where circuit_code is null and access_code is not null;

delete from t_circuit_customer_addr_query4 where access_code is null;
update t_circuit_customer_addr_query4 set a_address_name=address_name where a_address_name is null and address_name is not null;
update t_circuit_customer_addr_query4 set z_address_name=address_name where z_address_name is null and address_name is not null;

update t_circuit_customer_addr_query4 set a_address_name =a_facility_address  where a_address_name is null  and a_facility_address is not null;

update t_circuit_customer_addr_query4 set z_address_name =z_facility_address  where z_address_name is null  and z_facility_address is not null;


update t_circuit_customer_addr_query4 set a_address_name =a_facility_name  where a_address_name is null  and a_facility_name is not null;

update t_circuit_customer_addr_query4 set z_address_name =z_facility_name  where z_address_name is null  and z_facility_name is not null;

update t_circuit_customer_addr_query4 set a_address_name =a_device_name  where a_address_name is null  and a_device_name is not null;

update t_circuit_customer_addr_query4 set z_address_name =z_device_name  where z_address_name is null  and z_device_name is not null;

update t_circuit_customer_addr_query4 set a_address_name =a_customer_name  where a_address_name is null  and a_customer_name is not null;

update t_circuit_customer_addr_query4 set z_address_name =z_customer_name  where z_address_name is null  and z_customer_name is not null;

update t_circuit_customer_addr_query4 set a_address_name =circuit_code  where a_address_name is null  and circuit_code is not null;

update t_circuit_customer_addr_query4 set z_address_name =circuit_code  where z_address_name is null  and circuit_code is not null;

create index idx_t_circuit_customer_addr_query4_circuit_code on t_circuit_customer_addr_query4(circuit_code);


delete from t_circuit_customer_addr_query4 t1 where t1.link_type_id='81708247'
and exists (
select 1 from t_circuit_customer_addr_query4 t2 where t1.circuit_code=t2.circuit_code and t2.link_type_id='81708253'
);

delete from t_circuit_customer_addr_query4 t1 where t1.link_type_id<>'81708253'
and exists (
select 1 from t_circuit_customer_addr_query4 t2 where t1.circuit_code=t2.circuit_code and t2.link_type_id='81708253'
);

update t_circuit_customer_addr_query4 set area_id=${shardingIdNum} where area_id is null;
update t_circuit_customer_addr_query4 set area_name = '${areaName}' where area_name is null;
alter table t_circuit_customer_addr_query4 add column opt_code varchar(5000);
alter table t_circuit_customer_addr_query4 add column opt_road varchar(5000);





/*
drop table t_circuit_customer_addr_query_link_link;
create table t_circuit_customer_addr_query_link_link(link_id numeric(24), circuit_code varchar(1000), cr_link_link_id numeric(24), lower_link_id numeric(24), upper_link_id numeric(24));

WITH RECURSIVE employee_hierarchy AS (
    SELECT t.link_id, t.circuit_code, cll.id cr_link_link_id, cll.lower_link_id , cll.upper_link_id FROM t_circuit_customer_addr_query4 t left join  ${o3_res_schema}.cr_link_link cll on t.link_id = cll.upper_Link_id

    UNION ALL

    SELECT eh.link_id, eh.circuit_code, cll.id cr_link_link_id, cll.lower_link_id , cll.upper_link_id FROM  ${o3_res_schema}.cr_link_link cll
    INNER JOIN employee_hierarchy eh ON cll.upper_link_id = eh.lower_link_id
)
insert into t_circuit_customer_addr_query_link_link SELECT * FROM employee_hierarchy;




drop table t_circuit_customer_addr_query_link_link_1;
create table t_circuit_customer_addr_query_link_link_1 as
		select t1.*,
		cl1.id lower_link_link_id,cl1.name lower_link_link_name, cl1.code lower_link_link_code , cl1.spec_id lower_link_link_spec_id,
		cl2.id upper_link_link_Id,cl2.name upper_link_link_name, cl2.code upper_link_link_code, cl2.spec_Id upper_link_link_spec_id
		from t_circuit_customer_addr_query_link_link t1
		left join ${o3_res_schema}.cm_link cl1 on t1.lower_link_id = cl1.id
		left join ${o3_res_schema}.cm_link cl2 on t1.upper_link_id = cl2.id;


	drop table t_tmp_opt_road;
	create table t_tmp_opt_road as
	select distinct link_id, circuit_code, upper_link_link_code opt_code, upper_link_link_code||'('||upper_link_link_name||')' opt_road from t_circuit_customer_addr_query_link_link_1 where upper_link_link_spec_id =1131200002
	union
	select distinct link_id, circuit_code, lower_link_link_code opt_code, lower_link_link_code||'('||lower_link_link_name||')' opt_road from t_circuit_customer_addr_query_link_link_1 where lower_link_link_spec_id =1131200002;

drop table t_tmp_opt_road_agg;
create table t_tmp_opt_road_agg as
select link_id,circuit_code, STRING_AGG(opt_code,'；') opt_code, STRING_AGG(opt_road,'；') opt_road from t_tmp_opt_road group by link_id, circuit_code;


update t_circuit_customer_addr_query4 t1 set opt_road = (select opt_road from t_tmp_opt_road_agg t2 where t1.link_id = t2.link_id),opt_code = (select opt_code from t_tmp_opt_road_agg  t3 where t1.link_id = t3.link_id) ;



*/

--
--给t_circuit_customer_addr_query4补上电路维护等级字段maintain_level
drop table if exists t_circuit_customer_addr_query4_1 cascade;
create table t_circuit_customer_addr_query4_1 as
select t.*, t3."value" maintain_level_id,t4.desc_china maintain_level from t_circuit_customer_addr_query4 t
left join public.rv_service t3 on t3.service_id=t.service_id and t3.pkey='181854'
left join public.pm_pub_restriction t4 on t4.serial_no::VARCHAR=t3."value";
/*
drop table t_circuit_customer_addr_query4_2;
CREATE table t_circuit_customer_addr_query4_2 as
SELECT t.*,t1.desc_china maintain_level from t_circuit_customer_addr_query4 t
LEFT JOIN t_circuit_customer_addr_query4_1 t1 on t1.id=t.id and t1.circuit_code=t.circuit_code and t1.a_device_id=t.a_device_id and t1.link_id=t.link_id;
*/
drop table if exists t_circuit_customer_addr_query5_1 cascade;
drop table if exists t_circuit_customer_addr_query5 cascade;
ALTER TABLE t_circuit_customer_addr_query4_1 RENAME TO t_circuit_customer_addr_query5_1;

create table t_circuit_customer_addr_query5 as
SELECT *
FROM (
    SELECT *, ROW_NUMBER() OVER(PARTITION BY id ORDER BY id) as sequence
    FROM t_circuit_customer_addr_query5_1
) a
WHERE sequence = 1;

alter table t_circuit_customer_addr_query5 add constraint pk_t_circuit_customer_addr_query5  primary key(id);

create index idx_t_circuit_customer_addr_query5_code on t_circuit_customer_addr_query5(circuit_code);
create index idx_t_circuit_customer_addr_query5_access on t_circuit_customer_addr_query5(access_code);

delete from t_circuit_customer_addr_query5 t1 where t1.service_id is null and  exists (
    select 1 from t_circuit_customer_addr_query5 t2 where t1.circuit_code=t2.circuit_code and t2.service_id is not null
);



drop view if exists public.t_circuit_customer_addr_query cascade;
create view public.t_circuit_customer_addr_query as select * from t_circuit_customer_addr_query5;
grant select on t_circuit_customer_addr_query5 to public;
grant select on public.t_circuit_customer_addr_query to public;




--circuit_customer_addr_order_query1拉去宽表
drop table if exists t_circuit_customer_addr_order_query cascade;
create table t_circuit_customer_addr_order_query as
SELECT t.*,t3.order_id,t3.order_code,t3.state_date from public.t_circuit_customer_addr_query t
left JOIN public.rm_service t1 on t1.access_code = t.access_code
LEFT JOIN public.gom_order_service t2 on t2.entity_id=t1.id
LEFT JOIN public.gom_order t3 on t3.order_id=t2.order_id
;
drop table if exists public.t_circuit_customer_addr_order_query cascade;
drop view if exists public.t_circuit_customer_addr_order_query cascade;
create view public.t_circuit_customer_addr_order_query as select * from t_circuit_customer_addr_order_query;

grant select on t_circuit_customer_addr_order_query to public;
grant select on public.t_circuit_customer_addr_order_query to public;

create index idx_t_circuit_customer_addr_order_query_1 on t_circuit_customer_addr_order_query(access_code);
create index idx_t_circuit_customer_addr_order_query_2 on t_circuit_customer_addr_order_query(circuit_code);


drop table t_opt_road cascade;
create table t_opt_road
as
select id,name,code from public.cm_link l where spec_id=1132400006;
create unique index idx_t_opt_road_id on t_opt_road(id);
create index idx_t_opt_road_code on t_opt_road(code);
grant select on t_opt_road to public;

drop view if exists public.t_opt_road cascade;
create or replace view public.t_opt_road as select * from t_opt_road;
grant select on public.t_opt_road to public;
