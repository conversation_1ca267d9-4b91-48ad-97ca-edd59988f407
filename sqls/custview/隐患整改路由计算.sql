-- Step 1: Create similarity function
CREATE OR REPLACE FUNCTION array_similarity(arr1 anyarray, arr2 anyarray)
RETURNS numeric AS $$
DECLARE
total_elements int;
intersection_elements int;
BEGIN
    total_elements := array_length(arr1, 1) + array_length(arr2, 1)- array_length(array(SELECT unnest(arr1) INTERSECT SELECT unnest(arr2)), 1);
    intersection_elements := array_length(array(SELECT unnest(arr1) INTERSECT SELECT unnest(arr2)), 1);
    IF total_elements = 0 THEN
        RETURN 0;
		ELSE
        RETURN intersection_elements::numeric / total_elements;
		END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;


drop table if exists pm_gis_bse_segment;
create table pm_gis_bse_segment
as
select pl.id,pl.spec_id,pl.name,pl.code,pl.a_facility_id,pl.z_facility_id,af.no a_code,af.name a_name,zf.no z_code,zf.name z_name,af.pos_x::numeric a_pos_x,af.pos_y::numeric a_pos_y,zf.pos_x::numeric z_pos_x,zf.pos_y::numeric z_pos_y,af.res_spec_id a_spec_id,zf.res_spec_id z_spec_id from cm_pipeline pl
		inner join public.res_bse_equipment_daily af on pl.a_facility_id=af.bse_eqp_id
		inner join public.res_bse_equipment_daily zf on pl.z_facility_id=zf.bse_eqp_id
where  pl.parent_id is null and af.pos_x is not null and af.pos_y is not null and zf.pos_x is not null and zf.pos_y is not null;

alter table pm_gis_bse_segment add constraint pk_pm_gis_bse_segment primary key(id);



-- select * from res_bse_sect_daily ; -- 管道和光缆的承载关系

drop table if exists pm_gis_cable_net;
create table pm_gis_cable_net as
select id,code,name,net_type_id,cn.old_id from cm_net cn
left join ce_net_cable cnc on cn.id=cnc.net_Id
where cn.spec_id=1211200002;
alter table pm_gis_cable_net add constraint pk_pm_gis_cable_net primary key(id);

drop table if exists pm_gis_cable_net_2_pipe;
create table pm_gis_cable_net_2_pipe
as
select cn2cs.net_id::numeric as net_id,cn2cs.entity_id::numeric as cable_seg_id,cs.name as cable_seg_name,cs.code as cable_seg_code,
ps.bse_sect_id::numeric as pipe_seg_id,ps.no as pipe_seg_code,ps.name as pipe_seg_name,
st_setsrid(st_makeline(st_makepoint(a_pos_x,a_pos_y),st_makepoint(z_pos_x,z_pos_y)),4326) geo
from pm_gis_cable_net cn
inner join cr_net_entity cn2cs on cn.id=cn2cs.net_id and entity_spec_id=1121000002
inner join cm_cable cs on cn2cs.entity_id=cs.id
inner join res_bse_sect_daily ps on cs.id=ps.cbl_sect_id
inner join pm_gis_bse_segment gps on ps.bse_sect_id::numeric=gps.id
;

drop table if exists pm_gis_cable_net_pipe;
create table pm_gis_cable_net_pipe
as
select net_id,array_agg(pipe_seg_code order by pipe_seg_code) pipe_seg_code_list,count(pipe_seg_code) as pipe_seg_code_count,st_linemerge(st_union(geo)) as geo from pm_gis_cable_net_2_pipe
group by net_id
;
alter table pm_gis_cable_net_pipe add constraint pk_pm_gis_cable_net_pipe primary key (net_id);


-- 局向光纤 1131100003
drop table if exists pm_gis_cable_jx;
create table pm_gis_cable_jx
as
select cn.id net_id,jx.id jx_id,a_physic_device_id,z_physic_device_id,a_port_id,z_port_id,using_state_id from pm_gis_cable_net cn
inner join cr_net_entity cn2cs on cn.id=cn2cs.net_id and cn2cs.entity_spec_id=1131100003
inner join cm_link jx on cn2cs.entity_id=jx.id
where jx.other_state_id=100381 and jx.a_physic_device_id<=jx.z_physic_device_id
union all
select cn.id net_id,jx.id jx_id,z_physic_device_id,a_physic_device_id,z_port_id,a_port_id,using_state_id from pm_gis_cable_net cn
inner join cr_net_entity cn2cs on cn.id=cn2cs.net_id and cn2cs.entity_spec_id=1131100003
inner join cm_link jx on cn2cs.entity_id=jx.id
where jx.other_state_id=100381 and jx.a_physic_device_id>jx.z_physic_device_id
;

drop table if exists pm_gis_cable_net_side_01;
create table pm_gis_cable_net_side_01
as
select (row_number() over (order by net_id))::numeric as id,net_id,a_physic_device_id,z_physic_device_id,count(1) capacity,
sum(case when (jx.using_state_id=100721) then
1
else
0
end
) as unused_capacity
from pm_gis_cable_jx jx
group by net_id,a_physic_device_id,z_physic_device_id;

alter table pm_gis_cable_net_side_01 add constraint pk_pm_gis_cable_net_side_01 primary key (id);

drop table if exists pm_gis_cable_net_side_02 ;
create table pm_gis_cable_net_side_02
as
select t1.*,t3.name,t3.code,t3.net_type_id,t2.pipe_seg_code_list,t2.geo from pm_gis_cable_net_side_01 t1
inner join pm_gis_cable_net_pipe t2 on t1.net_id=t2.net_id
inner join pm_gis_cable_net t3 on t2.net_id=t3.id
;

drop table if exists pm_gis_cable_net_side_03;
create table pm_gis_cable_net_side_03 as 
select t1.*,ad.name a_device_name,zd.name z_device_name,
af.name a_room_name,zf.name z_room_name,
aj.name a_site_name,zj.name z_site_name,
af.id a_room_id,zf.id z_room_id,
aj.id a_site_id,zj.id z_site_id,
ad.old_id a_short_device_id,
zd.old_id z_short_device_id,
af.old_id a_short_room_id,
zf.old_id z_short_room_id,
aj.old_id a_short_site_id,
zj.old_id z_short_site_id
from pm_gis_cable_net_side_02 t1
inner join cm_device ad on t1.a_physic_device_id=ad.id
inner join cm_device zd on t1.z_physic_device_id=zd.id
left join cm_facility af on ad.facility_id=af.id
left join cm_facility zf on zd.facility_id=zf.id
left join rm_area aj on af.parent_id=aj.id
left join rm_area zj on zf.parent_id=zj.id
;

drop table if exists pm_gis_cable_net_side;
create table pm_gis_cable_net_side as select * from pm_gis_cable_net_side_03;
alter table pm_gis_cable_net_side add constraint pk_pm_gis_cable_net_side primary key (id);
create index idx_pm_gis_cable_net_side_net on pm_gis_cable_net_side(net_id);
create index idx_pm_gis_cable_net_side_az on pm_gis_cable_net_side(a_short_device_id,z_short_device_id);
create index idx_pm_gis_cable_net_side_a on pm_gis_cable_net_side(a_short_device_id);
create index idx_pm_gis_cable_net_side_z on pm_gis_cable_net_side(z_short_device_id);

drop table if exists pm_gis_cable_net_side_pair_01;

create table pm_gis_cable_net_side_pair_01
as
select 
t1.id id1,
t2.id id2,
array_similarity(t1.pipe_seg_code_list, t2.pipe_seg_code_list) similarity 
from pm_gis_cable_net_side t1
inner join pm_gis_cable_net_side t2
on t1.a_short_device_id=t2.a_short_device_id and t1.z_short_device_id=t2.z_short_device_id
where t1.id<t2.id
;

drop table if exists pm_gis_cable_net_side_pair_02;
create table pm_gis_cable_net_side_pair_02 
as select * from pm_gis_cable_net_side_pair_01 where similarity>0.1 and similarity is not null;

create index idx_pm_gis_cable_net_side_pair_02_id1 on pm_gis_cable_net_side_pair_02(id1);

delete from pm_gis_cable_net_side_pair_02 t1
where exists (
select 1 from pm_gis_cable_net_side_pair_02 t2 where t1.id1=t2.id1 and t1.id2<t2.id2
);

drop table if exists pm_gis_cable_net_side_group;
with recursive group_member as (
select id as member_id,id as group_id,1 as depth from pm_gis_cable_net_side
union all
select id2 ,m.group_id,m.depth+1
from  pm_gis_cable_net_side_pair_02 p
inner join group_member m on p.id1=m.member_id
)
select * into pm_gis_cable_net_side_group from group_member ;

delete from pm_gis_cable_net_side_group t1 where exists (
select 1 from pm_gis_cable_net_side_group t2 where t1.member_id=t2.member_id and t1.group_id>t2.group_id
);

create unique index uidx_pm_gis_cable_net_side_group on pm_gis_cable_net_side_group(member_id);

alter table pm_gis_cable_net_side drop column if exists group_id;
alter table pm_gis_cable_net_side add group_id integer;

update pm_gis_cable_net_side t1 set group_id=(
select group_id from pm_gis_cable_net_side_group t2 where t1.id=t2.member_id
);

create index idx_pm_gis_cable_net_side_group on pm_gis_cable_net_side(group_id);

drop table if exists pm_gis_cable_net_group;
create table pm_gis_cable_net_group
as
select group_id::integer as group_id,a_short_device_id::bigint as a_short_device_id,
z_short_device_id::bigint as z_short_device_id,sum(capacity) capacity,sum(unused_capacity) unused_capacity
from pm_gis_cable_net_side group by group_id,a_short_device_id,z_short_device_id;

alter table pm_gis_cable_net_group add constraint pk_pm_gis_cable_net_group primary key(group_id);

create index idx_pm_gis_cable_net_group_a on pm_gis_cable_net_group(a_short_device_id);
create index idx_pm_gis_cable_net_group_z on pm_gis_cable_net_group(z_short_device_id);

-- 25310000010748
-- 25800002655415

SELECT * FROM pgr_ksp(  
    'SELECT group_id as id, a_short_device_id as source, z_short_device_id as target, 1 AS cost, 1 AS reverse_cost FROM pm_gis_cable_net_group',  
    25310000010748, -- 起点节点ID  
    25800002655415, -- 终点节点ID  
    10, -- 想要返回的最短路径数量  
    false -- 图是无向的  
);



-- Step 2 & 3: Self-join to find similar pairs and use recursive query to find groups
/*
WITH self_join AS (
    SELECT
        t1.id AS id1,
        t2.id AS id2,
        array_similarity(t1.array_field, t2.array_field) AS similarity
    FROM
        t t1
            JOIN
        t t2
        ON
            t1.id < t2.id
),
     similar_pairs AS (
         SELECT id1, id2
         FROM self_join
         WHERE similarity >= 0.9
     ),
     group_members AS (
         SELECT id AS member, id AS root, 1 AS depth
         FROM t
         UNION ALL
         SELECT sj.id2 AS member, gm.root, gm.depth + 1
         FROM similar_pairs sj
                  JOIN group_members gm ON sj.id1 = gm.member
         WHERE gm.depth <= 100
     )
-- Step 4: Create temporary table for group assignments and update the original table
CREATE TEMP TABLE group_assignments AS
SELECT
    DISTINCT ON (member)
    member,
    root AS group_id
FROM
    group_members
ORDER BY
    member, depth;

ALTER TABLE t ADD COLUMN group_id int;

UPDATE t
SET group_id = ga.group_id
    FROM group_assignments ga
WHERE t.id = ga.member;
*/