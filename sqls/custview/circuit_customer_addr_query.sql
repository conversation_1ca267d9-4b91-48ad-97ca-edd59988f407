hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
var queryFun = @@mybatis(p)<%
<select>

SELECT t.* from public.t_circuit_customer_addr_order_query t
where 1=1 
	<if test="p.customer_name!= null and p.customer_name!= ''">
    and (t.a_customer_name like '%'||#{p.customer_name}||'%' )
	</if>
	<if test="p.circuit_code!= null and p.circuit_code!= ''">
    and (t.circuit_code like '%'||#{p.circuit_code}||'%'  or t.access_code like '%'||#{p.circuit_code}||'%' )
	</if>
	<if test="p.access_code != null and p.access_code != ''">
    and (t.access_code like '%'||#{p.access_code }||'%')
	</if>
	<if test="p.service_type_id != null and p.service_type_id != ''">
    and (t.service_type_id =#{p.service_type_id}::numeric)
	</if>
	<if test="p.start_use_date != null and p.start_use_date != ''">
    and (t.start_use_date &gt;=#{p.start_use_date}::timestamp)
	</if>
</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};