// pm_busi_user_scene_member_link_neighbor_query.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select id,scene_id,service_id,area_id,area_name,service_type_id,service_type_name,a_customer_name,z_customer_name,customer_name,access_code,a_access_code,z_access_code,circuit_code,address_id,address_name,a_device_type_name,a_device_id,a_device_name,a_device_code,z_device_type_name,z_device_id,z_device_name,z_device_code,a_address_name,a_address_id,z_address_name,z_address_id,a_node_name,z_node_name from pm_busi_user_scene_member s
		
		where s.scene_id = #{p.scene_id}::numeric
		and (s.a_node_name=#{p.a_node_name} and s.z_node_name=#{p.z_node_name} or s.a_node_name=#{p.z_node_name} and s.z_node_name=#{p.a_node_name})
		and (1=0
		<if test="p.name != null and p.name != ''">
			or s.service_type_name like  '%'||#{p.name}||'%'
			or s.customer_name like  '%'||#{p.name}||'%'
			or s.a_customer_name like  '%'||#{p.name}||'%'
			or s.z_customer_name like  '%'||#{p.name}||'%'
			or s.access_code like  '%'||#{p.name}||'%'
			or s.a_access_code like  '%'||#{p.name}||'%'
			or s.z_access_code like  '%'||#{p.name}||'%'
			or s.address_name like  '%'||#{p.name}||'%'
			or s.a_address_name like  '%'||#{p.name}||'%'
			or s.z_address_name like  '%'||#{p.name}||'%'
		</if>
		<if test="p.name == null or p.name == ''">
			or 1=1
		</if>
		
		
		)
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo(#{pageSize}, #{currentPage});
return {
"data": data,
"pageInfo": pageInfo
};

// ===================================================================================