
// query_ip_man_config

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@mybatis(p)<%
    <select>
        select id,entity_spec_id,unit_id,unit_ip,manage_ip,vendor,dev_model,dev_set,host_name,specialty,create_date,col_time,config_type_id from pm_net_config where 1=1
		<if test="p.manage_ip != null and p.manage_ip != ''">
			and manage_ip &lt;&lt;= #{p.manage_ip}::cidr
		</if>
		<if test="p.host_name != null and p.host_name != ''">
			and host_name = #{p.host_name}
		</if>
		<if test="p.specialty != null and p.specialty != ''">
			and specialty = #{p.specialty}
		</if>
		<if test="p.start != null and p.start != ''">
			and col_time &gt;= #{p.start}::timestamp
		</if>
		<if test="p.end != null and p.end != ''">
			and col_time &lt;= #{p.end}::timestamp
		</if>
		<if test="p.config_type_id != null and p.config_type_id != ''">
        	and config_type_id = #{p.config_type_id}::numeric
        </if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};

// ===================================================================================








// get_ip_man_config_file
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";
var get_fun=@@sql(p)<%select id,file_name from pm_net_config where id=#{p.id}::numeric limit 1%>;
var configFile = get_fun(${param});
var configContent = readJSON('oss-wzrh',configFile['file_name']);
return {
	"meta": configFile,
	"content": configContent
};
// ===========================================================










// get_ip_man_config
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";
var get_fun=@@sql(p)<%select * from pm_net_config where id=#{p.id}::numeric limit 1%>;
var configFile = get_fun(${param});

return configFile;
// =======================================================















// get_ip_man_config_port_list
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@sql(p)<%
    select C_IfName.key port_key,C_IfName.value port_name,C_IfMac.value mac,C_IfAddr.value addr, C_IfAlias.value alias, C_IfSpeed.value speed,
C_IfOperStatus.value oper_status,C_IfAdminStatus.value admin_status
from pm_net_config pnc
inner join jsonb_each_text(pnc.content ->'C_IfName') C_IfName on 1=1
left join jsonb_each_text(pnc.content->'C_IfMac') C_IfMac on C_IfName.value=C_IfMac.key
left join jsonb_each_text(pnc.content->'C_IfAddr'->'C_IfAddrIPv4') C_IfAddr on C_IfName.value=C_IfAddr.key
left join jsonb_each_text(pnc.content->'C_IfAlias') C_IfAlias on C_IfName.value=C_IfAlias.key
left join jsonb_each_text(pnc.content->'C_IfSpeed') C_IfSpeed on C_IfName.value=C_IfSpeed.key
left join jsonb_each_text(pnc.content->'C_IfOperStatus') C_IfOperStatus on C_IfName.value=C_IfOperStatus.key
left join jsonb_each_text(pnc.content->'C_IfAdminStatus') C_IfAdminStatus on C_IfName.value=C_IfAdminStatus.key
where pnc.id=#{p.id}::numeric
%>;
var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};

// =========================================================================



// net_resource_integration.query_usefor
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@mybatis(p)<%
    <select>
        select id,entity_spec_id,name,code,usefor_level_id,create_date,modify_date,is_valid from pm_usefor where 1=1

		<if test="p.name != null and p.name != ''">
			and name like '%'||#{p.name}||'%'
		</if>
		<if test="p.usefor_level_id != null and p.usefor_level_id != ''">
			and usefor_level_id = #{p.usefor_level_id}
		</if>
		<if test="p.is_valid != null and p.is_valid != ''">
			and is_valid = #{p.is_valid}
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};

// =====================================================================




// net_resource_integration.save_entity
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
import 'net.hasor.dataql.fx.basic.DateTimeUdfSource' as time;
var result = saveEntity(${param});
return 1 ;
// ==========================================================================




// net_resource_integration.get_entity
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
import 'net.hasor.dataql.fx.basic.DateTimeUdfSource' as time;
var result = getEntity(${param}.id, ${param}.entity_spec_id);
return result ;
// =========================================================



// net_resource_integration.delete_entity
var result = deleteEntity(${param}.id, ${param}.entity_spec_id);
return result ;
// ==========================================================


// net_resource_integration.get_entity_meta
return getEntityMeta(#{id})
// ==================================================================




// net_resource_integration.query_ip_net
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
var queryFun = @@mybatis(p)<%
    <select>
        select id,name,code,create_date,modify_date,is_valid from pm_net_resource where entity_spec_id=1215000004

		<if test="p.name != null and p.name != ''">
			and name like '%'||#{p.name}||'%'
		</if>
		<if test="p.is_valid != null and p.is_valid != ''">
			and is_valid = #{p.is_valid}::numeric
		</if>
    </select>
%>;
var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};
// ==================================================================





// net_resource_integration.query_ip_pool
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
var queryFun = @@mybatis(p)<%
    <select>
        select p.id,p.entity_spec_id,e.net_id,e.usefor_1,e.usefor_2,e.usefor_3,e.region_1,e.region_2,e.region_3,
        r1.name region_name_1,r2.name region_name_2,r3.name region_name_3,
        u1.name use_for_name_1,u2.name use_for_name_2,u3.name use_for_name_3,
        e.net_id,
        n.name net_name,
        e.ip_type_id,
        iptp.name ip_type_name
        from pm_net_number p
        inner join pe_ip_pool_plan e on p.id=e.id and p.entity_spec_id=e.entity_spec_id
        left join rm_area r1 on e.region_1=r1.id
        left join rm_area r2 on e.region_2=r2.id
        left join rm_area r3 on e.region_3=r3.id
        left join pm_usefor u1 on e.usefor_1=u1.id
        left join pm_usefor u2 on e.usefor_2=u2.id
        left join pm_usefor u3 on e.usefor_3=u3.id
        left join pm_net_resource n on e.net_id = n.id
        left join ct_meta.mm_dictvalue iptp on iptp.id=e.ip_type_id
        where p.entity_spec_id=2010200005
        <if test="p.id != null and p.id != ''">
        	and p.id= #{p.id}
        </if>
    </select>
%>;
var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};
// =================================================================================== //



// net_resource_integration.query_region
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
var queryFun = @@mybatis(p)<%
    <select>
        select * from rm_area p where 1=1
        <if test="p.area_level_id != null and p.area_level_id != ''">
        	and p.area_level_id = #{p.area_level_id}
        </if>
        <if test="p.parent_id != null and p.parent_id != ''">
            and p.parent_id = #{p.parent_id}::numeric
        </if>
        limit 100000
    </select>
%>;
var data=queryFun(${param})
return data;
// ======================================================================================





// net_resource_integration.query_ip_segment
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
var queryFun = @@mybatis(p)<%
    <select>
        select p.id,p.entity_spec_id,e.net_id,
        n.name net_name,
        e.ip_type_id,iptp.name ip_type_name,e.cidr_code,p.create_date,p.modify_date,p.parent_id from pm_net_number p
        inner join pe_ip_segment e on p.id=e.id and p.entity_spec_id=e.entity_spec_id
        left join pm_net_resource n on e.net_id = n.id
        left join ct_meta.mm_dictvalue iptp on iptp.id=e.ip_type_id
        where p.entity_spec_id=2010100006
        <if test="p.parent_id != null and p.parent_id != ''">
                    and p.parent_id = #{p.parent_id}::numeric
        </if>
    </select>
%>;
var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};
// ===================================================================================








// 生成ip地址段
with t_input
as
(select cidr('***********/24') start_net, cidr('***********/24') end_net,
(cidr('***********/24')-cidr('***********/24'))::int num_of_net,
floor(2^(masklen(netmask('***********/24')) - masklen('***********/24')))::int num_of_host )
select t_input.start_net,t_input.end_net,(t_input.start_net + t_serial) cidr_no from t_input
inner join generate_series(0,num_of_net,num_of_host) t_serial on 1=1;
// ===================================================================================



// get_ip_man_config_optical_module_list
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@sql(p)<%
SELECT b.key optical_module,
b.value->>'TransBw' trans_bw,
b.value->>'Temperature' temperature,
b.value->>'VendorName' vendor_name,
b.value->>'MaxTxPower' max_tx_power,
b.value->>'MinRxPower' min_rx_power,
b.value->>'MaxRxPower' max_rx_power,
b.value->>'WaveLength' wave_length,
b.value->>'BiasCurrent' bias_current,
b.value->>'CurTxPower' cur_tx_power,
b.value->>'TransferDistance' transfer_distance,
b.value->>'HardwareType' hardware_type,
b.value->>'SerialNumber' serial_number,
b.value->>'MinTxPower' min_tx_power,
b.value->>'CurRxPower' cur_rx_power,
b.value->>'TransceiverType' transceiver_type,
b.value->>'Voltage' voltage
from pm_net_config pnc, jsonb_each(pnc.content->'IPF_Optical_Data') b
where pnc.id=#{p.id}::numeric

    %>;
var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
    "pageInfo": pageInfo,
    "data": data
};
// ===================================================================================

// IP地址分配结果表,在BC库的省库上执行 ========================================
create table VW_RES_IP_USED
(
  area_name           varchar2(255),
  network_id          NUMBER(14),
  network_name        VARCHAR2(255),
  status_name         VARCHAR2(255),
  ip_addr_id          NUMBER(14),
  no                  VARCHAR2(255),
  role_id             NUMBER(8),
  role_name           VARCHAR2(200),
  ip_subnet_id        NUMBER(14),
  lgc_eqp_id          NUMBER(14),
  lgc_eqp_name        VARCHAR2(255),
  lgc_eqp_no          VARCHAR2(255),
  lgc_eqp_spec_id     NUMBER,
  lgc_eqp_spec_name   VARCHAR2(255),
  prod_serv_id        NUMBER(14),
  prod_serv_spec_id   NUMBER(14),
  access_number       VARCHAR2(255),
  prod_serv_spec_name VARCHAR2(255),
  lgc_port_no         VARCHAR2(255),
  port_spec_id        NUMBER,
  port_spec_name      VARCHAR2(255)
);
// ========================================================

// BC库IP地址占用表,在BC库执行  ==============================================================
drop table vw_res_ip_eqp_used purge;
create table vw_res_ip_eqp_used
as
select t2.network_id,t2.name network_name,t3.name status_name,t1.ip_addr_id,t1.no,t1.role_id,t7.desc_china role_name,t1.ip_subnet_id,
t4.lgc_eqp_id,t5.name lgc_eqp_name,t5.no lgc_eqp_no,t5.res_spec_id lgc_eqp_spec_id,t6.name lgc_eqp_spec_name
from res${areaCode}.nbr_ip_address@res${areaCode}_query t1
inner join res${areaCode}.net_network@res${areaCode}_query t2 on t1.network_id=t2.network_id
inner join res${areaCode}.pub_status@res${areaCode}_query t3 on t1.opr_state_id=t3.state_id
inner join res${areaCode}.lgc_device_use_ip@res${areaCode}_query t4 on t1.ip_addr_id = t4.ip_addr_id
inner join res${areaCode}.lgc_equipment@res${areaCode}_query t5 on t4.lgc_eqp_id=t5.lgc_eqp_id
inner join res${areaCode}.pub_resource_spec@res${areaCode}_query t6 on t5.res_spec_id=t6.res_spec_id
inner join res${areaCode}.pub_restriction@res${areaCode}_query t7 on t1.role_id=t7.serial_no
;


drop table vw_res_ip_prod_used purge;
create table vw_res_ip_prod_used
as
select t2.network_id,t2.name network_name,t3.name status_name,t1.ip_addr_id,t1.no,t1.role_id,t6.desc_china role_name,t1.ip_subnet_id,
t5.prod_serv_id,t7.prod_serv_spec_id,t8.no access_number,t9.name prod_serv_spec_name
from res${areaCode}.nbr_ip_address@res${areaCode}_query t1
inner join res${areaCode}.net_network@res${areaCode}_query t2 on t1.network_id=t2.network_id
inner join res${areaCode}.pub_status@res${areaCode}_query t3 on t1.opr_state_id=t3.state_id
inner join res${areaCode}.nbr_namespace@res${areaCode}_query t4 on t1.ip_subnet_id=t4.namespace_id
inner join res${areaCode}.srv_res_serv@res${areaCode}_query t5 on t5.res_id=t1.ip_subnet_id and t4.res_spec_id=t5.res_spec_id
inner join res${areaCode}.pub_restriction@res${areaCode}_query t6 on t1.role_id=t6.serial_no
inner join res${areaCode}.srv_prod_serv_2_spec@res${areaCode}_query t7 on t7.prod_serv_id =t5.prod_serv_id
inner join res${areaCode}.srv_access_number@res${areaCode}_query t8 on t7.prod_serv_id=t8.prod_serv_id
inner join res${areaCode}.srv_prod_serv_spec@res${areaCode}_query t9 on t9.prod_serv_spec_id=t7.prod_serv_spec_id
;


drop table vw_res_ip_port_used purge;
create table vw_res_ip_port_used
as
select t2.network_id,t2.name network_name,t3.name status_name,t1.ip_addr_id,t1.no,t1.role_id,t7.desc_china role_name,t1.ip_subnet_id,
t5.lgc_eqp_id,t5.name lgc_eqp_name,t5.no lgc_eqp_no,t5.res_spec_id lgc_eqp_spec_id,t6.name lgc_eqp_spec_name,
t8.no lgc_port_no,t8.res_spec_id port_spec_id,t9.name port_spec_name
from res${areaCode}.nbr_ip_address@res${areaCode}_query t1
inner join res${areaCode}.net_network@res${areaCode}_query t2 on t1.network_id=t2.network_id
inner join res${areaCode}.pub_status@res${areaCode}_query t3 on t1.opr_state_id=t3.state_id
inner join res${areaCode}.lgc_port_use_ip@res${areaCode}_query t4 on t1.ip_addr_id = t4.ip_addr_id
inner join res${areaCode}.lgc_port@res${areaCode}_query t8 on t4.lgc_port_id=t8.lgc_port_id
inner join res${areaCode}.lgc_equipment@res${areaCode}_query t5 on t8.lgc_eqp_id=t5.lgc_eqp_id
inner join res${areaCode}.pub_resource_spec@res${areaCode}_query t6 on t5.res_spec_id=t6.res_spec_id
inner join res${areaCode}.pub_restriction@res${areaCode}_query t7 on t1.role_id=t7.serial_no
inner join res${areaCode}.pub_resource_spec@res${areaCode}_query t9 on t8.res_spec_id=t9.res_spec_id
;
drop table vw_res_ip_used purge;
create table vw_res_ip_used
as
select network_id,network_name,status_name,ip_addr_id,no,role_id, role_name,ip_subnet_id,
       lgc_eqp_id,lgc_eqp_name,lgc_eqp_no,lgc_eqp_spec_id,lgc_eqp_spec_name,
       cast(null as number(14)) prod_serv_id, cast(null as number(14)) prod_serv_spec_id, cast(null as varchar(200)) access_number, cast(null as varchar(200)) prod_serv_spec_name,
       cast(null as varchar(200)) lgc_port_no,cast(null as number(14)) port_spec_id, cast(null as varchar(200)) port_spec_name
from vw_res_ip_eqp_used
union all
select network_id,network_name,status_name,ip_addr_id,no,role_id, role_name,ip_subnet_id,
       cast(null as number(14)) lgc_eqp_id,cast(null as varchar(200)) lgc_eqp_name,cast(null as varchar(200)) lgc_eqp_no,cast(null as number(14)) lgc_eqp_spec_id,cast(null as varchar(200)) lgc_eqp_spec_name,
       prod_serv_id,prod_serv_spec_id, access_number, prod_serv_spec_name,
       cast(null as varchar(200)) lgc_port_no,cast(null as number(14)) port_spec_id, cast(null as varchar(200)) port_spec_name
from vw_res_ip_prod_used
union all
select network_id,network_name,status_name,ip_addr_id,no,role_id, role_name,ip_subnet_id,
       lgc_eqp_id,lgc_eqp_name,lgc_eqp_no,lgc_eqp_spec_id,lgc_eqp_spec_name,
       cast(null as number(14)) prod_serv_id, cast(null as number(14)) prod_serv_spec_id, cast(null as varchar(200)) access_number, cast(null as varchar(200)) prod_serv_spec_name,
       lgc_port_no,port_spec_id,port_spec_name
from vw_res_ip_port_used;

delete from reportdev.VW_RES_IP_USED@reportdev where area_name='${areaName}';
insert into reportdev.VW_RES_IP_USED@reportdev select '${areaName}',t.* from vw_res_ip_used t;

// ===================================结束=====================================================





// ==============================在网资库执行===================================================

DO LANGUAGE plpgsql $$
declare
idx int4;
v record;
begin
    idx:=0;
	FOR v in (SELECT id FROM mid_mvw_res_ip_used where cidr_no is null) loop
	    begin
            update mid_mvw_res_ip_used t set cidr_no=trim(ip_no)::cidr where t.id=v.id;

        exception when others then
            null;
        end;
        idx:=idx+1;
        if (idx>=1000) then
            commit;
            idx:=0;
        end if;
	end loop;
	commit;
end;
$$

/

truncate table mvw_res_ip_used;
insert into mvw_res_ip_used select * from mid_mvw_res_ip_used;
// ====================================================================================================



// 查看单个设备的光模块配置文件 ================================================================
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@sql(p)<%
SELECT b.key optical_module,
b.value->>'TransBw' trans_bw,
b.value->>'Temperature' temperature,
b.value->>'VendorName' vendor_name,
b.value->>'MaxTxPower' max_tx_power,
b.value->>'MinRxPower' min_rx_power,
b.value->>'MaxRxPower' max_rx_power,
b.value->>'WaveLength' wave_length,
b.value->>'BiasCurrent' bias_current,
b.value->>'CurTxPower' cur_tx_power,
b.value->>'TransferDistance' transfer_distance,
b.value->>'HardwareType' hardware_type,
b.value->>'SerialNumber' serial_number,
b.value->>'MinTxPower' min_tx_power,
b.value->>'CurRxPower' cur_rx_power,
b.value->>'TransceiverType' transceiver_type,
b.value->>'Voltage' voltage
from pm_net_config pnc, jsonb_each(pnc.content->'IPF_Optical_Data') b
where pnc.id=#{p.id}::numeric

%>;
var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};
// ==============================结束========================================




// net_resource_integration.query_device

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@mybatis(p)<%
    <select>
        select id,entity_spec_id,specialty_name,name,code,model_name,vendor_name,create_date,modify_date,region_id,leaf_region_id,manage_ip
        from pm_net_resource where entity_spec_id=1024600001
		<if test="p.manage_ip != null and p.manage_ip != ''">
			and manage_ip &lt;&lt;= #{p.manage_ip}::cidr
		</if>
		<if test="p.name != null and p.name != ''">
			and name like '%'||#{p.name}||'%'
		</if>
		<if test="p.specialty_name != null and p.specialty_name != ''">
			and specialty_name = #{p.specialty_name}
		</if>
		<if test="p.code != null and p.code != ''">
        	and code = #{p.code}
        </if>
		<if test="p.model_name != null and p.model_name != ''">
        	and model_name = #{p.model_name}
        </if>
        <if test="p.vendor_name != null and p.vendor_name != ''">
            and vendor_name = #{p.vendor_name}
        </if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};




// net_resource_integration.query_bc_device

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@mybatis(p)<%
    <select>
        select id,region_name,leaf_region_name,name,code,manage_ip
        from pm_res_device where manage_ip is not null
        <if test="p.manage_ip != null and p.manage_ip != ''">
            manage_ip &lt;&lt;= #{p.manage_ip}::cidr
        </if>
        <if test="p.name != null and p.name != ''">
            and name like '%'||#{p.name}||'%'
        </if>
        <if test="p.code != null and p.code != ''">
            and code = #{p.code}
        </if>
        <if test="p.region_name!= null and p.region_name!= ''">
            and region_name= #{p.region_name}
        </if>
    </select>
    %>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
    "pageInfo": pageInfo,
    "data": data
};

// ===================================================================================



// net_resource_integration.query_port

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryFun = @@mybatis(p)<%
    <select>
        select *
        from pm_net_port where entity_spec_id=1041100003
        and device_id=#{p.device_id}
		<if test="p.code != null and p.code != ''">
        	and code like  '%'||#{p.code}||'%'
        </if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};

// ===================================================================================