// pm_opt_group_resource_problem_batch_inactive.sql
// 光路组检测资源问题清单表批量失效API

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};


// 批量失效，所有业务逻辑在Java层处理
var updateFun = @@mybatis(p)<%
    <update>
        UPDATE pm_opt_group_resource_problem
        SET
            is_active = false,
            <if test="p.inactive_reason != null and p.inactive_reason != ''">
                inactive_reason = #{p.inactive_reason},
            </if>
            <if test="p.update_op != null and p.update_op != ''">
                update_op = #{p.update_op},
            </if>
            <if test="p.update_time != null and p.update_time != ''">
                update_time = #{p.update_time}::timestamp
            </if>
        WHERE 1=1
        <if test="p.ids != null and p.ids.size() > 0">
            AND id in
            <foreach collection="p.ids" item="id" open="(" close=")" separator=",">
                #{id}::numeric
            </foreach>
        </if>
    </update>
%>;

var result = updateFun(p);
return {
    "success": true,
    "message": "光路组检测资源问题清单表批量失效成功",
    "data": result
};