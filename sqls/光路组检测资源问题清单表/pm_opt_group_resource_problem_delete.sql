// pm_opt_group_resource_problem_delete.sql
// 光路组检测资源问题清单表删除API

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};

// 基本参数验证
if (collect.isEmpty(p) || collect.isEmpty(p.id)) {
    return {
        "success": false,
        "message": "必填参数不能为空：id",
        "data": 0
    };
}

// 删除，所有业务逻辑在Java层处理
var deleteFun = @@sql(p)<%
    DELETE FROM pm_opt_group_resource_problem
    WHERE id = #{p.id}::numeric
%>;

var result = deleteFun(p);
return {
    "success": true,
    "message": "光路组检测资源问题清单表删除成功",
    "data": result
};