// pm_opt_group_resource_problem_query.sql
// 光路组检测资源问题清单表查询API

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var queryFun = @@mybatis(p)<%
    <select>
        SELECT
            id,
            group_id,
            check_id,
            resource_type,
            resource_id,
            resource_code,
            resource_name,
            problem_type,
            problem_description,
            related_optical_path_id,
            related_optical_path_code,
            related_optical_paths,
            is_exemption,
            exemption_type,
            exemption_reason,
            white_list_id,
            white_list_code,
            is_ignore,
            ignore_reason,
            deal_status,
            deal_result,
            deal_remark,
            is_active,
            inactive_reason,
            rectification_id,
            rectification_code,
            rectification_status,
            is_front_well,
            is_indoor_optical,
            is_indoor_cable,
            create_op,
            create_time,
            update_op,
            update_time,
            confirm_op,
            confirm_time
        FROM pm_opt_group_resource_problem
        WHERE 1 = 1

        <if test="p.group_id != null and p.group_id != ''">
            AND group_id = #{p.group_id}::numeric
        </if>
        <if test="p.check_id != null and p.check_id != ''">
            AND check_id = #{p.check_id}::numeric
        </if>
        <if test="p.resource_type != null and p.resource_type != ''">
            AND resource_type = #{p.resource_type}
        </if>
        <if test="p.problem_type != null and p.problem_type != ''">
            AND problem_type = #{p.problem_type}
        </if>
        <if test="p.is_exemption != null">
            AND is_exemption = #{p.is_exemption}::boolean
        </if>
        <if test="p.exemption_type != null and p.exemption_type != ''">
            AND exemption_type = #{p.exemption_type}
        </if>
        <if test="p.deal_status != null and p.deal_status != ''">
            AND deal_status = #{p.deal_status}
        </if>
        <if test="p.is_active != null">
            AND is_active = #{p.is_active}::boolean
        </if>
        <if test="p.rectification_id != null and p.rectification_id != ''">
            AND rectification_id = #{p.rectification_id}::numeric
        </if>
        <if test="p.create_op != null and p.create_op != ''">
            AND create_op = #{p.create_op}
        </if>
        <if test="p.keyword != null and p.keyword != ''">
            AND (
                resource_code LIKE '%' || #{p.keyword} || '%'
                OR resource_name LIKE '%' || #{p.keyword} || '%'
                OR problem_description LIKE '%' || #{p.keyword} || '%'
                OR related_optical_path_code LIKE '%' || #{p.keyword} || '%'
            )
        </if>
        <if test="p.start_time != null and p.start_time != ''">
            AND create_time &gt;= #{p.start_time}::timestamp
        </if>
        <if test="p.end_time != null and p.end_time != ''">
            AND create_time &lt;= #{p.end_time}::timestamp
        </if>

        ORDER BY create_time DESC
    </select>
%>;

var pageQuery = queryFun(${param});

run pageQuery.setPageInfo({
    "pageSize": #{pageSize},
    "currentPage": #{currentPage}
});

var data = pageQuery.data();
var pageInfo = pageQuery.pageInfo();

return {
    "success": true,
    "message": "光路组检测资源问题清单表查询成功",
    "pageInfo": pageInfo,
    "data": data
};