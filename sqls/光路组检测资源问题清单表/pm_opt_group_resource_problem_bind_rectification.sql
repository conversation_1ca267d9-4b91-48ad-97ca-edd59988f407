// pm_opt_group_resource_problem_bind_rectification.sql
// 光路组检测资源问题清单表关联整治单API

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};

// 基本参数验证
if (collect.isEmpty(p) || collect.isEmpty(p.id) || collect.isEmpty(p.rectification_id) || collect.isEmpty(p.rectification_code)) {
    return {
        "success": false,
        "message": "必填参数不能为空：id, rectification_id, rectification_code",
        "data": 0
    };
}

// 关联整治单，所有业务逻辑在Java层处理
var updateFun = @@sql(p)<%
    UPDATE pm_opt_group_resource_problem
    SET
        rectification_id = #{p.rectification_id}::numeric,
        rectification_code = #{p.rectification_code},
        rectification_status = #{p.rectification_status},
        deal_status = #{p.deal_status},
        deal_remark = #{p.deal_remark},
        update_op = #{p.update_op},
        update_time = #{p.update_time}::timestamp
    WHERE id = #{p.id}::numeric
%>;

var result = updateFun(p);
return {
    "success": true,
    "message": "光路组检测资源问题清单表关联整治单成功",
    "data": result
};