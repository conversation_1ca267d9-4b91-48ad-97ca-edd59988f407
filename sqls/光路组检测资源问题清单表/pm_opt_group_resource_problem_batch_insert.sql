// pm_opt_group_resource_problem_batch_insert.sql
// 光路组检测资源问题清单表批量插入API

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};

// 基本参数验证
if (collect.isEmpty(p) || collect.isEmpty(p.list)) {
    return {
        "success": false,
        "message": "批量插入参数不能为空",
        "data": 0
    };
}

// 批量插入，所有业务逻辑在Java层处理
var insertFun = @@sql[](p)<%
    INSERT INTO pm_opt_group_resource_problem (
        group_id,
        check_id,
        resource_type,
        resource_id,
        resource_code,
        resource_name,
        problem_type,
        problem_description,
        related_optical_path_id,
        related_optical_path_code,
        related_optical_paths,
        is_exemption,
        exemption_type,
        exemption_reason,
        white_list_id,
        white_list_code,
        is_ignore,
        ignore_reason,
        deal_status,
        deal_result,
        deal_remark,
        is_active,
        inactive_reason,
        rectification_id,
        rectification_code,
        rectification_status,
        is_front_well,
        is_indoor_optical,
        is_indoor_cable,
        create_op,
        create_time,
        update_op,
        update_time,
        confirm_op,
        confirm_time
    ) VALUES (
        #{p.group_id}::numeric,
        #{p.check_id}::numeric,
        #{p.resource_type},
        #{p.resource_id}::numeric,
        #{p.resource_code},
        #{p.resource_name},
        #{p.problem_type},
        #{p.problem_description},
        #{p.related_optical_path_id}::numeric,
        #{p.related_optical_path_code},
        #{p.related_optical_paths},
        #{p.is_exemption}::boolean,
        #{p.exemption_type},
        #{p.exemption_reason},
        #{p.white_list_id}::numeric,
        #{p.white_list_code},
        #{p.is_ignore}::boolean,
        #{p.ignore_reason},
        #{p.deal_status},
        #{p.deal_result},
        #{p.deal_remark},
        #{p.is_active}::boolean,
        #{p.inactive_reason},
        #{p.rectification_id}::numeric,
        #{p.rectification_code},
        #{p.rectification_status},
        #{p.is_front_well}::boolean,
        #{p.is_indoor_optical}::boolean,
        #{p.is_indoor_cable}::boolean,
        #{p.create_op},
        #{p.create_time}::timestamp,
        #{p.update_op},
        #{p.update_time}::timestamp,
        #{p.confirm_op},
        #{p.confirm_time}::timestamp
    )
%>;

var result = insertFun(p.list);
return {
    "success": true,
    "message": "光路组检测资源问题清单表批量插入成功",
    "data": result
};