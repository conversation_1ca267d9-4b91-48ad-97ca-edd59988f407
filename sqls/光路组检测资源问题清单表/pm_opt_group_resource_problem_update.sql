// pm_opt_group_resource_problem_update.sql
// 光路组检测资源问题清单表更新API

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};

// 基本参数验证
if (collect.isEmpty(p) || collect.isEmpty(p.id)) {
    return {
        "success": false,
        "message": "必填参数不能为空：id",
        "data": 0
    };
}

// 更新，所有业务逻辑在Java层处理
var updateFun = @@sql(p)<%
    UPDATE pm_opt_group_resource_problem
    SET
        group_id = #{p.group_id}::numeric,
        check_id = #{p.check_id}::numeric,
        resource_type = #{p.resource_type},
        resource_id = #{p.resource_id}::numeric,
        resource_code = #{p.resource_code},
        resource_name = #{p.resource_name},
        problem_type = #{p.problem_type},
        problem_description = #{p.problem_description},
        related_optical_path_id = #{p.related_optical_path_id}::numeric,
        related_optical_path_code = #{p.related_optical_path_code},
        related_optical_paths = #{p.related_optical_paths},
        is_exemption = #{p.is_exemption}::boolean,
        exemption_type = #{p.exemption_type},
        exemption_reason = #{p.exemption_reason},
        white_list_id = #{p.white_list_id}::numeric,
        white_list_code = #{p.white_list_code},
        is_ignore = #{p.is_ignore}::boolean,
        ignore_reason = #{p.ignore_reason},
        deal_status = #{p.deal_status},
        deal_result = #{p.deal_result},
        deal_remark = #{p.deal_remark},
        is_active = #{p.is_active}::boolean,
        inactive_reason = #{p.inactive_reason},
        rectification_id = #{p.rectification_id}::numeric,
        rectification_code = #{p.rectification_code},
        rectification_status = #{p.rectification_status},
        is_front_well = #{p.is_front_well}::boolean,
        is_indoor_optical = #{p.is_indoor_optical}::boolean,
        is_indoor_cable = #{p.is_indoor_cable}::boolean,
        update_op = #{p.update_op},
        update_time = #{p.update_time}::timestamp,
        confirm_op = #{p.confirm_op},
        confirm_time = #{p.confirm_time}::timestamp
    WHERE id = #{p.id}::numeric
%>;

var result = updateFun(p);
return {
    "success": true,
    "message": "光路组检测资源问题清单表更新成功",
    "data": result
};