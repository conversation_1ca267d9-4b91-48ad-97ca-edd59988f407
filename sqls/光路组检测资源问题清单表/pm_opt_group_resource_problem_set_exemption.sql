// pm_opt_group_resource_problem_set_exemption.sql
// 光路组检测资源问题清单表设置豁免API

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};

// 基本参数验证
if (collect.isEmpty(p) || collect.isEmpty(p.id) || collect.isEmpty(p.is_exemption)) {
    return {
        "success": false,
        "message": "必填参数不能为空：id, is_exemption",
        "data": 0
    };
}

// 设置豁免，所有业务逻辑在Java层处理
var updateFun = @@sql(p)<%
    UPDATE pm_opt_group_resource_problem
    SET
        is_exemption = #{p.is_exemption}::boolean,
        exemption_type = #{p.exemption_type},
        exemption_reason = #{p.exemption_reason},
        white_list_id = #{p.white_list_id}::numeric,
        white_list_code = #{p.white_list_code},
        deal_status = #{p.deal_status},
        deal_remark = #{p.deal_remark},
        update_op = #{p.update_op},
        update_time = #{p.update_time}::timestamp
    WHERE id = #{p.id}::numeric
%>;

var result = updateFun(p);
return {
    "success": true,
    "message": "光路组检测资源问题清单表设置豁免成功",
    "data": result
};