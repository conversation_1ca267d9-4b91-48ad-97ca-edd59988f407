// hazard_remediation_update.sql
// 更新隐患整改单
// limit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var updateFun = @@mybatis(p)<%
    <update>
        UPDATE hazard_remediation SET
            update_time = CURRENT_TIMESTAMP
            <if test="p.status != null and p.status != ''">
                , status = #{p.status}
            </if>
            <if test="p.cityCode != null and p.cityCode != ''">
                , city_code = #{p.cityCode}
            </if>
            <if test="p.district != null and p.district != ''">
                , district = #{p.district}
            </if>
            <if test="p.speciality != null and p.speciality != ''">
                , speciality = #{p.speciality}
            </if>
            <if test="p.hazardSource != null and p.hazardSource != ''">
                , hazard_source = #{p.hazardSource}
            </if>
            <if test="p.hazardType != null and p.hazardType != ''">
                , hazard_type = #{p.hazardType}
            </if>
            <if test="p.hazardLevel != null and p.hazardLevel != ''">
                , hazard_level = #{p.hazardLevel}
            </if>
            <if test="p.title != null and p.title != ''">
                , title = #{p.title}
            </if>
            <if test="p.remediationDescription != null and p.remediationDescription != ''">
                , remediation_description = #{p.remediationDescription}
            </if>
            <if test="p.expectedCompletionDate != null and p.expectedCompletionDate != ''">
                , expected_completion_date = #{p.expectedCompletionDate}::date
            </if>
            <if test="p.actualCompletionDate != null and p.actualCompletionDate != ''">
                , actual_completion_date = #{p.actualCompletionDate}::date
            </if>
            <if test="p.completionDescription != null and p.completionDescription != ''">
                , completion_description = #{p.completionDescription}
            </if>
            <if test="p.userRemainingIssues != null and p.userRemainingIssues != ''">
                , user_remaining_issues = #{p.userRemainingIssues}
            </if>
            <if test="p.exemptionReason != null and p.exemptionReason != ''">
                , exemption_reason = #{p.exemptionReason}
            </if>
            <if test="p.completionOperator != null and p.completionOperator != ''">
                , completion_operator = #{p.completionOperator}
            </if>
            <if test="p.completionTime != null and p.completionTime != ''">
                , completion_time = #{p.completionTime}::timestamp
            </if>
            <if test="p.hasRemainingIssues != null">
                , has_remaining_issues = #{p.hasRemainingIssues}
            </if>
            <if test="p.responsiblePerson != null and p.responsiblePerson != ''">
                , responsible_person = #{p.responsiblePerson}
            </if>
            <if test="p.responsiblePersonContact != null and p.responsiblePersonContact != ''">
                , responsible_person_contact = #{p.responsiblePersonContact}
            </if>
            <if test="p.remediationPerson != null and p.remediationPerson != ''">
                , remediation_person = #{p.remediationPerson}
            </if>
            <if test="p.remediationPersonContact != null and p.remediationPersonContact != ''">
                , remediation_person_contact = #{p.remediationPersonContact}
            </if>
            <if test="p.protectionScenario != null and p.protectionScenario != ''">
                , protection_scenario = #{p.protectionScenario}
            </if>
            <if test="p.customProtectionScenario != null and p.customProtectionScenario != ''">
                , custom_protection_scenario = #{p.customProtectionScenario}
            </if>
            <if test="p.deviceType != null and p.deviceType != ''">
                , device_type = #{p.deviceType}
            </if>
            <if test="p.customDeviceType != null and p.customDeviceType != ''">
                , custom_device_type = #{p.customDeviceType}
            </if>
            <if test="p.selectedProtectionGroupId != null and p.selectedProtectionGroupId != ''">
                , selected_protection_group_id = #{p.selectedProtectionGroupId}
            </if>
            <if test="p.opticalPathGroupCode != null and p.opticalPathGroupCode != ''">
                , optical_path_group_code = #{p.opticalPathGroupCode}
            </if>
            <if test="p.opticalPathGroupName != null and p.opticalPathGroupName != ''">
                , optical_path_group_name = #{p.opticalPathGroupName}
            </if>
            <if test="p.isLifeline != null and p.isLifeline != ''">
                , is_lifeline = #{p.isLifeline}
            </if>
            <if test="p.lifelineBusinessId != null and p.lifelineBusinessId != ''">
                , lifeline_business_id = #{p.lifelineBusinessId}
            </if>
            <if test="p.lifelineBusinessName != null and p.lifelineBusinessName != ''">
                , lifeline_business_name = #{p.lifelineBusinessName}
            </if>
            <if test="p.circuitCode != null and p.circuitCode != ''">
                , circuit_code = #{p.circuitCode}
            </if>
            <if test="p.circuitName != null and p.circuitName != ''">
                , circuit_name = #{p.circuitName}
            </if>
            <if test="p.customerName != null and p.customerName != ''">
                , customer_name = #{p.customerName}
            </if>
            <if test="p.customerAccount != null and p.customerAccount != ''">
                , customer_account = #{p.customerAccount}
            </if>
            <if test="p.customerManager != null and p.customerManager != ''">
                , customer_manager = #{p.customerManager}
            </if>
            <if test="p.customerManagerDepartment != null and p.customerManagerDepartment != ''">
                , customer_manager_department = #{p.customerManagerDepartment}
            </if>
            <if test="p.customerLevel != null and p.customerLevel != ''">
                , customer_level = #{p.customerLevel}
            </if>
            <if test="p.differentiatedServiceLevel != null and p.differentiatedServiceLevel != ''">
                , differentiated_service_level = #{p.differentiatedServiceLevel}
            </if>
            <if test="p.selfDiscoveredDescription != null and p.selfDiscoveredDescription != ''">
                , self_discovered_description = #{p.selfDiscoveredDescription}
            </if>
            <if test="p.discoveredBy != null and p.discoveredBy != ''">
                , discovered_by = #{p.discoveredBy}
            </if>
            <if test="p.discoveredDate != null and p.discoveredDate != ''">
                , discovered_date = #{p.discoveredDate}::date
            </if>
            <if test="p.remark != null and p.remark != ''">
                , remark = #{p.remark}
            </if>
        WHERE id = #{p.id}::numeric
    </update>
%>;

var result = updateFun(${param});

return {
    "success": true,
    "message": "隐患整改单更新成功",
    "data": result
};
