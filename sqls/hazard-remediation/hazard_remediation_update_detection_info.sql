// hazard_remediation_update_detection_info.sql
// 更新隐患整改单检测信息
// limit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param});

var updateFun = @@sql(p)<%
    UPDATE hazard_remediation 
    SET 
        latest_detection_id = #{p.latestDetectionId}::numeric,
        initial_detection_id = COALESCE(initial_detection_id, #{p.initialDetectionId}::numeric),
        completion_detection_id = COALESCE(completion_detection_id, #{p.completionDetectionId}::numeric),
        detection_count = COALESCE(detection_count, 0) + 1,
        has_remaining_issues = COALESCE(#{p.hasRemainingIssues}::boolean, has_remaining_issues),
        update_time = CURRENT_TIMESTAMP
    WHERE id = #{p.remediationId}::numeric
%>;

var result = updateFun(p);

return {
    "success": true,
    "message": "检测信息更新成功",
    "data": {
        "affectedRows": result
    }
};
