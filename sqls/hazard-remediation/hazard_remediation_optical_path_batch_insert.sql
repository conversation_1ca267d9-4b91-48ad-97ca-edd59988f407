// hazard_remediation_optical_path_batch_insert.sql
// 批量插入隐患整改光路
// limit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param});

// 批量插入光路记录
var insertFun = @@mybatis(p)<%
    <insert>
        INSERT INTO hazard_remediation_optical_path (remediation_id, optical_path_code, sort_order)
        VALUES
        <foreach collection="p.opticalPaths" item="item" separator=",">
            (#{p.remediationId}::numeric, #{item.opticalPathCode}, #{item.sortOrder}::integer)
        </foreach>
    </insert>
%>;

var result = insertFun(p);

return {
    "success": true,
    "message": "光路信息保存成功",
    "data": {
        "insertedRows": result
    }
};
