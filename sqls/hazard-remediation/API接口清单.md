# 隐患整改DataQL API接口清单

## 📋 接口概览

本文档包含隐患整改功能的所有DataQL接口，需要在API平台注册后使用。

## 🔧 接口列表

### 1. 创建隐患整改单

- **API编码**: `hazard_remediation_create`
- **API版本**: `V20250606162142624`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_create.sql`
- **功能**: 创建新的隐患整改单
- **入参示例**:

```json
{
    "cityCode": "xz",
    "hazardSource": "selfDiscovered",
    "hazardType": "singlePath",
    "hazardLevel": "high",
    "title": "徐州地区单路由隐患整改",
    "remediationDescription": "发现某光路存在单路由风险，需要进行双路由改造",
    "expectedCompletionDate": "2024-12-31",
    "responsiblePerson": "张三",
    "remediationPerson": "李四",
    "creator": "管理员"
}
```

### 2. 查询隐患整改单列表

- **API编码**: `hazard_remediation_query`
- **API版本**: `V20250606162251306`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_query.sql`
- **功能**: 分页查询隐患整改单列表
- **入参示例**:

```json
{
    "cityCode": "xz",
    "status": "draft",
    "hazardLevel": "high",
    "title": "单路由",
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
}
```

### 3. 查询隐患整改单详情

- **API编码**: `hazard_remediation_get`
- **API版本**: `V20250606162412436`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_get.sql`
- **功能**: 查询单个隐患整改单的详细信息，包含光路和附件
- **入参示例**:

```json
{
    "id": "1"
}
```

### 4. 更新隐患整改单

- **API编码**: `hazard_remediation_update`
- **API版本**: `V20250606162508349`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_update.sql`
- **功能**: 更新隐患整改单信息
- **入参示例**:

```json
{
    "id": "1",
    "status": "inProgress",
    "actualCompletionDate": "2024-12-25",
    "remark": "整改进度更新"
}
```

### 5. 删除隐患整改单

- **API编码**: `hazard_remediation_delete`
- **API版本**: `V20250606162553579`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_delete.sql`
- **功能**: 删除隐患整改单（仅草稿状态可删除）
- **入参示例**:

```json
{
    "id": "1"
}
```

### 6. 批量插入光路信息

- **API编码**: `hazard_remediation_optical_path_batch_insert`
- **API版本**: `V20250606162633796`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_optical_path_batch_insert.sql`
- **功能**: 批量保存隐患整改单的光路信息
- **入参示例**:

```json
{
    "remediationId": "1",
    "opticalPaths": [
        {
            "opticalPathCode": "OPT001",
            "sortOrder": 1
        },
        {
            "opticalPathCode": "OPT002",
            "sortOrder": 2
        }
    ]
}
```

### 7. 查询光路信息

- **API编码**: `hazard_remediation_optical_path_query`
- **API版本**: `V20250606162735865`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_optical_path_query.sql`
- **功能**: 查询隐患整改单的光路列表
- **入参示例**:

```json
{
    "remediationId": "1"
}
```

### 8. 插入附件记录

- **API编码**: `hazard_remediation_attachment_insert`
- **API版本**: `V20250606162836980`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_attachment_insert.sql`
- **功能**: 保存附件记录到数据库
- **入参示例**:

```json
{
    "remediationId": "1",
    "originalFilename": "整改方案.pdf",
    "storedFilename": "uuid-12345-67890.pdf",
    "fileSize": 1024000,
    "fileType": "application/pdf",
    "s3Bucket": "yuanfeng",
    "s3Key": "shuangluyou/hazard-remediation/uuid-12345-67890.pdf",
    "uploader": "管理员"
}
```

### 9. 查询附件列表

- **API编码**: `hazard_remediation_attachment_query`
- **API版本**: `V20250606173330031`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_attachment_query.sql`
- **功能**: 查询隐患整改单的附件列表
- **入参示例**:

```json
{
    "remediationId": "1"
}
```

### 10. 删除附件记录

- **API编码**: `hazard_remediation_attachment_delete`
- **API版本**: `V20250606173411939`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_attachment_delete.sql`
- **功能**: 删除附件记录
- **入参示例**:

```json
{
    "id": "1"
}
```

### 11. 统计查询

- **API编码**: `hazard_remediation_statistics`
- **API版本**: `V20250606173459118`
- **文件路径**: `nrm-graph-rest/sqls/hazard-remediation/hazard_remediation_statistics.sql`
- **功能**: 获取隐患整改统计信息
- **入参示例**:

```json
{
    "cityCode": "xz",
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
}
```

## 🔍 风险检测相关接口

### 风险检测记录管理

| 接口名称 | API编码 | 版本号 | 说明 |
|---------|---------|--------|------|
| 保存风险检测记录 | `hazard_remediation_risk_detection_save` | `V20250608221208377` | 保存检测记录到数据库 |
| 查询风险检测记录 | `hazard_remediation_risk_detection_query` | `V20250608221232725` | 查询整改单的检测历史 |
| 更新检测信息 | `hazard_remediation_update_detection_info` | `V20250608221705120` | 更新主表检测相关字段 |

### 风险对比分析

| 接口名称 | API编码 | 版本号 | 说明 |
|---------|---------|--------|------|
| 保存对比分析 | `hazard_remediation_risk_comparison_save` | `V20250608221259144` | 保存前后对比结果 |

### ~~遗留问题管理~~（已废弃）

~~遗留问题现在存储在主表的用户填写字段中，不再使用单独的遗留问题表~~

### 时间线管理

| 接口名称 | API编码 | 版本号 | 说明 |
|---------|---------|--------|------|
| 查询检测时间线 | `hazard_remediation_detection_timeline_query` | `V20250608221639110` | 查询操作时间线（基于检测记录生成） |

## 📝 注册说明

1. **文件位置**: 所有DataQL文件位于 `nrm-graph-rest/sqls/hazard-remediation/` 目录下
2. **API编码**: 每个接口都有唯一的API编码，用于在API平台注册
3. **版本号**: 使用统一的版本号格式 `V20250606162633XXX`
4. **分片代码**: 在DAO接口中使用 `SHARDING_GRAPH_DB` 作为默认分片代码
5. **业务逻辑**: 复杂的业务逻辑在Java Service层处理，DataQL只负责SQL执行

## 🔗 相关文件

- **DAO接口**: `nrm-graph-rest/src/main/java/com/telecom/nrm/dao/HazardRemediationDao.java`
- **Service接口**: `nrm-graph-rest/src/main/java/com/telecom/nrm/service/HazardRemediationService.java`
- **Service实现**: `nrm-graph-rest/src/main/java/com/telecom/nrm/service/impl/HazardRemediationServiceImpl.java`
- **Controller**: `nrm-graph-rest/src/main/java/com/telecom/nrm/controller/HazardRemediationController.java`
- **数据库表结构**: `nrm-graph-rest/sqls/hazard-remediation-tables.sql`

## ⚠️ 注意事项

1. 所有接口都需要在API平台注册后才能使用
2. 确保数据库表已经创建
3. 分片代码需要根据实际环境调整
4. 文件上传功能需要S3服务正常运行
