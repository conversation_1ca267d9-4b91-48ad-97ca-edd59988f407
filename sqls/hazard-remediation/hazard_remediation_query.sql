// hazard_remediation_query.sql
// 查询隐患整改单列表（分页）

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var queryFun = @@mybatis(p)<%
    <select>
        SELECT
            hr.id,
            hr.status,
            hr.create_time,
            hr.update_time,
            hr.city_code,
            hr.district,
            hr.speciality,
            hr.hazard_source,
            hr.hazard_type,
            hr.hazard_level,
            hr.title,
            hr.remediation_description,
            hr.expected_completion_date,
            hr.actual_completion_date,
            hr.responsible_person,
            hr.responsible_person_contact,
            hr.remediation_person,
            hr.remediation_person_contact,
            hr.creator,
            hr.creator_id,
            hr.remark,
            -- 保护组信息（设备保护组）
            hr.protection_scenario,
            hr.custom_protection_scenario,
            hr.device_type,
            hr.custom_device_type,
            hr.selected_protection_group_id,
            -- 业务保护组信息
            hr.optical_path_group_code,
            hr.optical_path_group_name,
            -- 生命线业务信息
            hr.is_lifeline,
            hr.lifeline_business_id,
            hr.lifeline_business_name,
            -- 业务信息（业务保护组）
            hr.circuit_code,
            hr.circuit_name,
            -- 客户信息（业务保护组）
            hr.customer_name,
            hr.customer_account,
            hr.customer_manager,
            hr.customer_manager_department,
            hr.customer_level,
            hr.differentiated_service_level,
            -- 自行发现相关字段
            hr.self_discovered_description,
            hr.discovered_by,
            hr.discovered_date,
            -- 完成相关信息
            hr.completion_description,
            hr.user_remaining_issues,
            hr.exemption_reason,
            hr.completion_operator,
            hr.completion_time,
            -- 检测关联信息
            hr.latest_detection_id,
            hr.initial_detection_id,
            hr.completion_detection_id,
            hr.detection_count,
            hr.has_remaining_issues,
            COUNT(DISTINCT hrop.id) as optical_path_count,
            COUNT(DISTINCT hra.id) as attachment_count
        FROM hazard_remediation hr
        LEFT JOIN hazard_remediation_optical_path hrop ON hr.id = hrop.remediation_id
        LEFT JOIN hazard_remediation_attachment hra ON hr.id = hra.remediation_id
        WHERE 1=1
        <if test="p.cityCode != null and p.cityCode != ''">
            AND hr.city_code = #{p.cityCode}
        </if>
        <if test="p.status != null and p.status != ''">
            AND hr.status = #{p.status}
        </if>
        <if test="p.hazardSource != null and p.hazardSource != ''">
            AND hr.hazard_source = #{p.hazardSource}
        </if>
        <if test="p.hazardType != null and p.hazardType != ''">
            AND hr.hazard_type = #{p.hazardType}
        </if>
        <if test="p.hazardLevel != null and p.hazardLevel != ''">
            AND hr.hazard_level = #{p.hazardLevel}
        </if>
        <if test="p.title != null and p.title != ''">
            AND hr.title LIKE '%' || #{p.title} || '%'
        </if>
        <if test="p.responsiblePerson != null and p.responsiblePerson != ''">
            AND hr.responsible_person LIKE '%' || #{p.responsiblePerson} || '%'
        </if>
        <if test="p.remediationPerson != null and p.remediationPerson != ''">
            AND hr.remediation_person LIKE '%' || #{p.remediationPerson} || '%'
        </if>
        <if test="p.creator != null and p.creator != ''">
            AND hr.creator LIKE '%' || #{p.creator} || '%'
        </if>
        <if test="p.startDate != null and p.startDate != ''">
            AND hr.create_time >= #{p.startDate}::timestamp
        </if>
        <if test="p.endDate != null and p.endDate != ''">
            AND hr.create_time <= #{p.endDate}::timestamp
        </if>
        <if test="p.protectionScenario != null and p.protectionScenario != ''">
            AND hr.protection_scenario = #{p.protectionScenario}
        </if>
        <if test="p.deviceType != null and p.deviceType != ''">
            AND hr.device_type = #{p.deviceType}
        </if>
        <if test="p.discoveredBy != null and p.discoveredBy != ''">
            AND hr.discovered_by LIKE '%' || #{p.discoveredBy} || '%'
        </if>
        <if test="p.opticalPathGroupCode != null and p.opticalPathGroupCode != ''">
            AND hr.optical_path_group_code LIKE '%' || #{p.opticalPathGroupCode} || '%'
        </if>
        GROUP BY hr.id, hr.status, hr.create_time, hr.update_time, hr.city_code, hr.district, hr.speciality,
                 hr.hazard_source, hr.hazard_type, hr.hazard_level, hr.title,
                 hr.remediation_description, hr.expected_completion_date, hr.actual_completion_date,
                 hr.responsible_person, hr.responsible_person_contact, hr.remediation_person,
                 hr.remediation_person_contact, hr.creator, hr.creator_id, hr.remark,
                 hr.protection_scenario, hr.custom_protection_scenario, hr.device_type, hr.custom_device_type,
                 hr.selected_protection_group_id, hr.optical_path_group_code, hr.optical_path_group_name,
                 hr.is_lifeline, hr.lifeline_business_id, hr.lifeline_business_name,
                 hr.circuit_code, hr.circuit_name, hr.customer_name, hr.customer_account,
                 hr.customer_manager, hr.customer_manager_department, hr.customer_level,
                 hr.differentiated_service_level, hr.self_discovered_description, hr.discovered_by,
                 hr.discovered_date, hr.completion_description, hr.user_remaining_issues,
                 hr.exemption_reason, hr.completion_operator, hr.completion_time,
                 hr.latest_detection_id, hr.initial_detection_id, hr.completion_detection_id,
                 hr.detection_count, hr.has_remaining_issues
        ORDER BY hr.create_time DESC
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 当前页
});

var pageInfo = pageQuery.pageInfo();
var data = pageQuery.data();

return {
    'pageInfo': pageInfo,
    'data': data
};
