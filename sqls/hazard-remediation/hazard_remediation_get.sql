// hazard_remediation_get.sql
// 查询隐患整改单基本信息（仅主表）

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

// 查询主表信息
var getMainFun = @@sql(p)<%
    SELECT * FROM hazard_remediation WHERE id = #{p.id}::numeric LIMIT 1
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})
var mainData = getMainFun(p);

return {
    "success": true,
    "message": "查询成功",
    "data": mainData
};
