// hazard_remediation_detection_timeline_query.sql
// 查询隐患整改检测时间线
// limit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param});

var queryFun = @@sql(p)<%
    SELECT
        rd.id,
        rd.remediation_id,
        rd.id as detection_id,
        'detection' as timeline_type,
        CASE
            WHEN rd.detection_phase = 'before_remediation' THEN '执行风险检测 - 整改前检测'
            WHEN rd.detection_phase = 'after_remediation' THEN '执行风险检测 - 整改后检测'
            ELSE '执行风险检测'
        END as event_description,
        json_build_object(
            'checkResult', rd.check_result,
            'exceptionInfo', rd.exception_info,
            'riskLevel', rd.risk_level,
            'opticalPathCount', rd.optical_path_count,
            'detectionType', rd.detection_type,
            'detectionPhase', rd.detection_phase
        ) as event_data,
        rd.detector as operator,
        rd.detection_time as event_time,
        rd.detection_type,
        rd.detection_phase,
        rd.check_result,
        rd.risk_level,
        rd.exception_info
    FROM hazard_remediation_risk_detection rd
    WHERE rd.remediation_id = #{p.remediationId}::numeric
    ORDER BY rd.detection_time DESC
    LIMIT 100
%>;

var data = queryFun(p);

return {
    "success": true,
    "message": "查询成功",
    "data": data
};
