// hazard_remediation_risk_detection_save.sql
// 保存隐患整改风险检测记录
// limit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param});

var insertFun = @@sql(p)<%
    INSERT INTO hazard_remediation_risk_detection (
        remediation_id, detection_type, detection_phase, check_result, exception_info,
        risk_level, route_num, optical_path_count, detection_snapshot, optical_paths_snapshot,
        detector, detection_status, error_message
    ) VALUES (
        #{p.remediationId}::numeric,
        #{p.detectionType},
        #{p.detectionPhase},
        #{p.checkResult},
        #{p.exceptionInfo},
        #{p.riskLevel},
        #{p.routeNum}::integer,
        #{p.opticalPathCount}::integer,
        #{p.detectionSnapshot}::jsonb,
        #{p.opticalPathsSnapshot}::jsonb,
        #{p.detector},
        #{p.detectionStatus},
        #{p.errorMessage}
    ) RETURNING id, detection_time
%>;

var result = insertFun(p);

return {
    "success": true,
    "message": "风险检测记录保存成功",
    "data": result
};
