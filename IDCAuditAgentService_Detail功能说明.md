# IDCAuditAgentService Detail功能说明

## 功能概述

为 IDCAuditAgentService 新增了 detail 标签解析功能，类似于 IdcChatController 的实现。当最后一个消息包含 `<detail>` 标签时，会自动提取其中的 JSON 报文并作为 `askParam` 添加到结束消息中。

## 实现原理

### 1. 检测机制
在 `execute` 方法的最后，检查最终消息是否包含 `<detail>` 标签：

```java
// 检查最终消息是否包含detail标签，如果包含则提取JSON作为askParam
String finalMessage = react.getString("message");
JSONObject askParam = extractDetailFromMessage(finalMessage);

JSONObject content = new JSONObject();
content.put("content", "<end>");

// 如果提取到了detail信息，添加askParam
if (askParam != null) {
    content.put("askParam", askParam);
    log.info("Extracted askParam from detail: {}", askParam.toJSONString());
}
```

### 2. 提取算法
`extractDetailFromMessage` 方法实现了detail标签的解析，返回包含 `code` 和 `msg` 字段的标准格式：

```java
private JSONObject extractDetailFromMessage(String message) {
    JSONObject result = new JSONObject();

    if (StringUtils.isEmpty(message)) {
        result.put("code", "-1");
        result.put("msg", "消息为空");
        return result;
    }

    try {
        // 查找<detail>和</detail>标签
        int startIndex = message.indexOf("<detail>");
        int endIndex = message.indexOf("</detail>");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            // 提取detail标签之间的内容
            String detailContent = message.substring(startIndex + 8, endIndex).trim();

            // 尝试解析为JSON
            if (StringUtils.isNotEmpty(detailContent)) {
                JSONObject detailJson = JSONObject.parseObject(detailContent);

                // 解析成功
                result.put("code", "0");
                result.put("msg", detailJson);
                return result;
            } else {
                // detail标签内容为空
                result.put("code", "-1");
                result.put("msg", "detail标签内容为空");
                return result;
            }
        } else {
            // 未找到完整的detail标签
            result.put("code", "-1");
            result.put("msg", "未找到完整的detail标签");
            return result;
        }
    } catch (Exception e) {
        // 解析失败
        result.put("code", "-1");
        result.put("msg", "JSON解析失败: " + e.getMessage());
        return result;
    }
}
```

## 使用场景

### 输入消息示例
当 AI 助手的回复包含 detail 标签时：

```
根据您的查询需求，我已经为您生成了审计查询参数。<detail>
{"deviceIp":"*************","portName":"GE0/0/1","userName":"张三","startDate":"2025-05-05 00:00:00","endDate":"2025-06-05 23:59:59","regionNames":["南京"],"code":"0"}
</detail>请在详情界面查看具体信息。
```

### 输出消息格式

#### 解析成功时
系统会自动生成包含 askParam 的结束消息：

```json
{
  "content": "<end>",
  "askParam": {
    "deviceIp": "*************",
    "portName": "GE0/0/1",
    "userName": "张三",
    "startDate": "2025-05-05 00:00:00",
    "endDate": "2025-06-05 23:59:59",
    "regionNames": ["南京"],
    "code": "0"
  }
}
```

#### 解析失败时
系统只发送结束消息，不包含 askParam：

```json
{
  "content": "<end>"
}
```

#### 内部处理格式
`extractDetailFromMessage` 方法返回的标准格式：

**成功解析**：
```json
{
  "code": "0",
  "msg": {
    "deviceIp": "*************",
    "portName": "GE0/0/1",
    "userName": "张三",
    "startDate": "2025-05-05 00:00:00",
    "endDate": "2025-06-05 23:59:59",
    "regionNames": ["南京"],
    "code": "0"
  }
}
```

**解析失败**：
```json
{
  "code": "-1",
  "msg": "JSON解析失败: 错误信息"
}
```

## 特性说明

### 1. 智能解析
- **自动检测**: 自动检测消息中的 `<detail>` 和 `</detail>` 标签
- **内容提取**: 提取标签之间的文本内容
- **JSON解析**: 尝试将内容解析为 JSON 对象
- **错误处理**: 解析失败时返回 null，不影响正常流程

### 2. 容错机制
- **空消息处理**: 正确处理 null 或空字符串消息
- **标签缺失**: 处理只有开始标签或结束标签的情况
- **无效JSON**: 捕获JSON解析异常，记录警告日志
- **多个标签**: 提取第一个有效的 detail 标签内容

### 3. 返回格式标准化
- **统一格式**: 所有情况都返回包含 `code` 和 `msg` 字段的 JSONObject
- **状态码**: `code` 为 "0" 表示成功，"-1" 表示失败
- **结果数据**: 成功时 `msg` 包含解析后的 JSON 对象，失败时包含错误描述

### 4. 日志记录
- **成功提取**: `Successfully parsed detail JSON: {...}`
- **提取失败**: `Failed to extract detail from message: {error}`
- **调试信息**: 包含原始消息内容的调试日志

## 测试验证

### 测试用例覆盖
1. **有效detail提取** - 正确提取包含有效JSON的detail标签
2. **无detail标签** - 正确处理不包含detail标签的消息
3. **无效JSON** - 正确处理包含无效JSON的detail标签
4. **空白字符** - 正确处理detail内容包含空白字符的情况
5. **复杂JSON** - 支持嵌套对象和数组的复杂JSON结构
6. **边界情况** - 处理null消息、空消息、不完整标签等

### 测试结果
```
✅ Detail提取测试通过
✅ 无效detail测试通过
✅ 无detail测试通过
```

## 与 IdcChatController 的对比

### 相似之处
1. **askParam机制**: 都在结束消息中添加 askParam 字段
2. **JSON格式**: askParam 的值都是 JSON 对象
3. **前端引导**: 都用于引导前台界面显示详情

### 差异之处
1. **触发方式**:
   - IdcChatController: 基于参数解析结果
   - IDCAuditAgentService: 基于消息中的 detail 标签
2. **数据来源**:
   - IdcChatController: 来自 prepareParam 方法的解析结果
   - IDCAuditAgentService: 来自 AI 回复消息中的 detail 标签
3. **处理时机**:
   - IdcChatController: 在参数解析阶段
   - IDCAuditAgentService: 在消息处理结束阶段

## 应用效果

### 前端集成
前端可以通过检查结束消息中的 `askParam` 字段来决定是否显示详情界面：

```javascript
// WebSocket消息处理
websocket.onmessage = function(event) {
    const data = JSON.parse(event.data);

    if (data.content === "<end>" && data.askParam) {
        // 显示详情界面，使用 askParam 中的参数
        showDetailView(data.askParam);
    }
};
```

### 用户体验
1. **无缝衔接**: AI 回复自然包含detail信息，用户体验流畅
2. **参数传递**: 自动提取查询参数，无需用户重新输入
3. **界面引导**: 自动引导用户进入详情查看界面

## 总结

通过实现 detail 标签解析功能，IDCAuditAgentService 现在能够：

1. **自动识别** AI 回复中的 detail 标签
2. **智能提取** detail 中的 JSON 参数
3. **无缝传递** 参数给前端界面
4. **增强用户体验** 通过自动界面引导

这个功能使得 IDCAuditAgentService 与 IdcChatController 在用户交互体验上保持一致，为用户提供了统一的操作流程。
