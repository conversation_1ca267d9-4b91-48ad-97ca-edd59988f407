package com.telecom.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Random;

public class Main {
    public static void main(String[] args) {
        LocalDate date = LocalDate.now();
        DayOfWeek day = date.getDayOfWeek();

        LocalDate start = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate end = date;

        JSONObject json = new JSONObject();
        json.put("start", start + " 00:00:00");
        json.put("end", end + " 23:59:59");

        System.out.println(JSON.toJSONString(json));
        System.out.println(day);
    }
}
