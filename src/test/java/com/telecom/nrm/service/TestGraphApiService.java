package com.telecom.nrm.service;


import com.alibaba.fastjson.JSONObject;

import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j

public class TestGraphApiService {
    @Autowired
    GraphApiService graphApiService;
//    @Test
//    public void testDoApi() {
//        GraphRequestBuilder builder = new GraphRequestBuilder();
//        GraphRequest request = builder.setApiId("query_device").setShardingCode("ds_bc_o3_yz")
//                .appendWhereBodyItem(new GraphWhereBodyItem("device","code","PC-17A88E72")).build();
//
//        String requestString = JSONObject.toJSONString(request);
//        log.info("graph api request: {}",requestString );
//        Graph graph = graphApiService.doApi(request);
//
//        log.info("graph api response {} ", graph);
//    }
    //import com.telecom.nrm.domain.graph.Edge;
//import com.telecom.nrm.domain.graph.Graph;



    static class Edge {
        String source;
        String target;

        public Edge(String source, String target) {
            this.source = source;
            this.target = target;
        }

        @Override
        public String toString() {
            return "(" + source + " -> " + target + ")";
        }
    }

    static class Graph {
        private Map<String, List<Edge>> adjacencyList;

        public Graph() {
            this.adjacencyList = new HashMap<>();
        }

        public void addEdge(Edge edge) {
            this.adjacencyList
                    .computeIfAbsent(edge.source, k -> new ArrayList<>())
                    .add(edge);
        }

        public Map<String, List<Edge>> getAdjacencyList() {
            return adjacencyList;
        }
    }
    public static List<Edge> sortEdgesBFS(Set<Edge> edges) {
        Graph graph = new Graph();
        edges.forEach(graph::addEdge);

        Map<String, Boolean> visited = new HashMap<>();
        LinkedList<String> queue = new LinkedList<>();
        List<Edge> sortedEdges = new ArrayList<>();

        // 找到起始点（没有入度的点）
        for (Edge edge : edges) {
            if (!isTarget(edge.source, edges)) {
                queue.add(edge.source);
                visited.put(edge.source, true);
            }
        }

        while (!queue.isEmpty()) {
            String currentNode = queue.poll();
            List<Edge> outgoingEdges = graph.getAdjacencyList().get(currentNode);
            if (outgoingEdges != null) {
                for (Edge edge : outgoingEdges) {
                    sortedEdges.add(edge);
                    if (!visited.containsKey(edge.target)) {
                        queue.add(edge.target);
                        visited.put(edge.target, true);
                    }
                }
            }
        }

        return sortedEdges;
    }

    private static boolean isTarget(String node, Set<Edge> edges) {
        for (Edge edge : edges) {
            if (edge.target.equals(node)) {
                return true;
            }
        }
        return false;
    }

    public static void main(String[] args) {
        Set<Edge> edges = new HashSet<>();
        edges.add(new Edge("B", "C"));
        edges.add(new Edge("C", "D"));
        edges.add(new Edge("A", "B"));
        edges.add(new Edge("C", "F"));
        edges.add(new Edge("B", "E"));


        List<Edge> sortedEdges = sortEdgesBFS(edges);

    }



}
