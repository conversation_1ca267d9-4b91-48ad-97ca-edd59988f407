package com.telecom.nrm.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.HunderRiverService;
import com.telecom.nrm.thread.audit.AbstractCircuitAuditService;
import com.telecom.nrm.thread.audit.QueryCache;
import com.telecom.nrm.utils.ExcelUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@SpringBootTest
@Data
class HunderRiverServiceImplTest    implements Callable<List<JSONObject>> {

    private List<String> param;

    private  int start;
    private  int end;
    private  int totalSize;
    @Autowired
    HunderRiverService hunderRiverService;
    @Test
    void queryPon() {
        String s =  "{\n" +
                "    \"start\":\"2024/06/20 14:32:02\", \n" +
                "    \"end\":\"2024/06/20 15:02:02\",   \n" +
                "    \"queries\":[{   \n" +
                "        \"aggregator\":\"none\",    \n" +
                "        \"metric\":\"pon_p_crc_inputerror\",\n" +
                "        \"tags\":{\n" +
                "         \"MgmtIp\":\"*************\"\n" +
                "        }\n" +
                "    }]\n" +
                "}";
        JSONObject jsonObject=JSONObject.parseObject(s);
        JSONObject ipParent = jsonObject.getJSONArray("queries").getJSONObject(0).getJSONObject("tags");
        ipParent.put("MgmtIp", "***********");
        JSONObject ipParent2 = jsonObject.getJSONArray("queries").getJSONObject(0);
        AlarmPerformanceServiceImpl.addRequestTime(jsonObject, -24);
        ipParent2.put("metric", "pon_p_flux_fluxin");
        System.out.println(jsonObject);
        JSONObject jsonObject1= hunderRiverService.queryPon(jsonObject);
        System.out.println(jsonObject1);
    }

    @Test
    void queryIpran() {
        String s =  "{\n" +
                "    \"start\":\"2024/06/20 14:33:07\", \n" +
                "    \"end\":\"2024/06/20 15:03:07\",   \n" +
                "    \"queries\":[{   \n" +
                "        \"aggregator\":\"none\",    \n" +
                "        \"metric\":\"ipran_p_crc_inputerror\",\n" +
                "        \"tags\":{\n" +
                "         \"MgmtIp\":\"*************\"\n" +
                "        }\n" +
                "    }]\n" +
                "}";
        JSONObject jsonObject=JSONObject.parseObject(s);
        JSONObject jsonObject1= hunderRiverService.queryIpran(jsonObject);
        System.out.println(jsonObject1);
    }

    @Test
    void queryNewCity() {
        String s =  "{\n" +
                "    \"start\":\"2024/06/20 14:33:49\", \n" +
                "    \"end\":\"2024/06/20 15:03:49\",   \n" +
                "    \"queries\":[{   \n" +
                "        \"aggregator\":\"none\",    \n" +
                "        \"metric\":\"ipf_p_crc_inputerror\",\n" +
                "        \"tags\":{\n" +
                "         \"MgmtIp\":\"*************\"\n" +
                "        }\n" +
                "    }]\n" +
                "}";
        JSONObject jsonObject=JSONObject.parseObject(s);
        JSONObject jsonObject1= hunderRiverService.queryNewCity(jsonObject);
        System.out.println(jsonObject1);
    }

    @Test
    void queryFourLogic() {
        String s = "{\n" +
                "\"related_area_code\":\"'YZ'\",\n" +
                "\"spec_code\":\"'SJ','WX','DH'\",\n" +
                "    \"internal\":\"'24 hour'\",\n" +
                "    \"userLabel\":\"高邮\",\n" +
                "    \"summary1\":\"网元断链告警(198099803)\",\n" +
                "\"summary2\":\"温度\",\n" +
                "\"summary3\":\"该地址不能ping通\",\n" +
                "\"summary4\":\"BTS掉站\"\n" +
                "}";
        JSONObject jsonObject=JSONObject.parseObject(s);
        JSONObject jsonObject1= hunderRiverService.queryFourLogic(jsonObject);
        System.out.println(jsonObject1);
    }

    public static void main(String[] args) {
        String s = "{\n" +
                "\"related_area_code\":\"'YZ'\",\n" +
                "\"spec_code\":\"'SJ','WX','DH'\",\n" +
                "    \"internal\":\"'24 hour'\",\n" +
                "    \"userLabel\":\"高邮\",\n" +
                "    \"summary1\":\"网元断链告警(198099803)\",\n" +
                "\"summary2\":\"温度\",\n" +
                "\"summary3\":\"该地址不能ping通\",\n" +
                "\"summary4\":\"BTS掉站\"\n" +
                "}";
        JSONObject jsonObject=JSONObject.parseObject(s);
        jsonObject.put("internal","11111");
        System.out.println(jsonObject);
    }
    @Test
    void zd() {
        String path = "src/main/resources/excel/客户故障工单需查询的接入号.xlsx";
        String sheetName = "Sheet1";
        Sheet sheet = ExcelUtil.getXSSFWorkbookSheet(path, sheetName);
        List<JSONObject> list = new ArrayList<>();
        List<String> query =new ArrayList<>();
        JSONObject bcObject = new JSONObject();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (null != row){
                String  accessCode  = row.getCell(0).getStringCellValue();
                query.add(accessCode);
                }
            }
        List<Node2> node2List =new ArrayList<>();
        if (CollectionUtils.isNotEmpty(query)) {
            query =query.stream().distinct().collect(Collectors.toList());
            int totalSize = query.size();
            int threadNum = AbstractCircuitAuditService.threadNum;
            int batchSize = (totalSize + threadNum - 1) / threadNum; // 计算每批处理的数量
            List<Future<List<JSONObject>>> futures = new ArrayList<>(threadNum);
            // 提交任务
            for (int i = 0; i < totalSize; i += batchSize) {
                int end = Math.min(i + batchSize, totalSize);
                QueryCache abstractCircuitAuditService = new QueryCache();
                abstractCircuitAuditService.setParam(query.subList(i, end));
                abstractCircuitAuditService.setStart(i);
                abstractCircuitAuditService.setEnd(end-1);
                abstractCircuitAuditService.setTotalSize(totalSize);
                Future<List<JSONObject>> future = AbstractCircuitAuditService.executorService.submit(abstractCircuitAuditService);
                futures.add(future);
            }
            // 收集结果
            for (Future<List<JSONObject>> future : futures) {
                try {
                    List<JSONObject> result = future.get();
                    if (CollectionUtils.isNotEmpty(result)) {
                        list.addAll(result);
                    }
                } catch (Exception e) {
                    // 处理异常
                    e.printStackTrace();
                }
            }
            AbstractCircuitAuditService.executorService.shutdown(); // 关闭线程池
        }

        if (CollectionUtils.isNotEmpty(list)){

        }


        if (CollectionUtils.isNotEmpty(node2List)){
            ExcelUtil.writeExcel(AbstractCircuitAuditService.outFilePath +
                            "云专网2.0" + AbstractCircuitAuditService.simpleDate.format(new Date()) + ".xlsx",
                    "云专网2.0", Node2.class, node2List);
        }



    }

    @Override
    public List<JSONObject> call() throws Exception {
        return null;
    }


    @Data
    static  class  Node {

        @ExcelProperty("故障单号")
        @ColumnWidth(30)
        private String  billId;

        @ExcelProperty("时间")
        @ColumnWidth(30)
        private String  date;
        @ExcelProperty("内容")
        @ColumnWidth(30)
        private String  content;

        @ExcelProperty("接入号")
        @ColumnWidth(30)
        private String  acc;
        @ExcelProperty("状态")
        @ColumnWidth(30)
        private String  state ="已完成";
    }
    @Data
    static  class  Node2{
        @ExcelProperty("故障单号")
        @ColumnWidth(30)
        private  String usertroublesn;

        @ExcelProperty("内容")
        @ColumnWidth(30)
        private  String usertroubletitle;

        @ExcelProperty("接入号")
        @ColumnWidth(30)
        private  String usernumber;

        @ExcelProperty("时间")
        @ColumnWidth(30)
        private  String create_time;

        @ExcelProperty("状态")
        @ColumnWidth(30)
        private String  state ="已完成";

        public void setCreate_time(long create_time) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String str = sdf.format(new Date(create_time));
            this.create_time =str;
        }
        public Node2(JSONObject jsonObject){
            this.usertroublesn = jsonObject.getString("usertroublesn");
            this.usertroubletitle = jsonObject.getString("usertroubletitle");
            this.usernumber = jsonObject.getString("usernumber");
             this.setCreate_time(jsonObject.getLong("create_time"));

        }
    }
}