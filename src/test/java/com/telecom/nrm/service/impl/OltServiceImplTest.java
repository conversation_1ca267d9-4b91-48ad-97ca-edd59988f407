package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.OltInfoDao;
import com.telecom.nrm.service.OltService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.cxf.wsdl11.SOAPBindingUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@SpringBootTest
@Slf4j
class OltServiceImplTest {

    @Autowired
    OltService oltService;

    @Autowired
    OltInfoDao oltInfoDao;

    @Test
    void querOltInfoById() {

        List<JSONObject> list = oltService.selectAllOltBySharding(ShardingEnum.SZ.getBcShardingCode());

        Set<BigDecimal> ids = list.stream().map(e -> e.getBigDecimal("id")).collect(Collectors.toSet());
        List<JSONObject> list2 = oltInfoDao.queryResOltByIds(ids, ShardingEnum.SZ.getBcShardingCode());
        System.out.println(ids);
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject jsonObject : list) {
                String name = jsonObject.getString("name");
                String code = jsonObject.getString("code");
                BigDecimal id = jsonObject.getBigDecimal("id");
                oltService.createOptGroupAndElement(code, name, ShardingEnum.SZ.getBcShardingCode(), id);
            }
        }
        System.out.println(list);


    }

    @Test
    void querOltInfoByIdALL() {

        for (ShardingEnum value : ShardingEnum.values()) {
            String region =value.getRegionName();
            if ("南京,镇江,常州,无锡,苏州".contains(region)) continue;
            List<JSONObject> list = oltService.selectAllOltBySharding(value.getBcShardingCode());
            if (CollectionUtils.isNotEmpty(list)) {
                for (JSONObject jsonObject : list) {
                    String name = jsonObject.getString("name");
                    String code = jsonObject.getString("code");
                    BigDecimal id = jsonObject.getBigDecimal("id");
                    oltService.createOptGroupAndElement(code, name, value.getBcShardingCode(), id);
                }
            }
        }


    }


}