package com.telecom.nrm.service.impl;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.Map;

public class FreeMarkerTest {

    static Map<String, Object> dataModel() {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("startTime","");
        dataModel.put("endTime","");
        dataModel.put("name","");
        dataModel.put("regions","");
        dataModel.put("totalOperations",0);
        dataModel.put("openPorts",0);
        dataModel.put("closePorts",0);
        dataModel.put("constructionCount",0);
        dataModel.put("riskCount",0);
        return dataModel;
    }

    public static void main(String[] args) {
        // 初始化FreeMarker配置
        Configuration cfg = new Configuration();
        cfg.setClassForTemplateLoading(FreeMarkerTest.class, "/templates");



        try {
            // 加载模板
            Template template = cfg.getTemplate("template.ftl");

            // 测试用例1：所有字段都为空
            Map<String, Object> dataModel1 = dataModel();
            System.out.println("测试用例1：所有字段都为空");
            generateText(template, dataModel1);
            System.out.println();

            // 测试用例2：只有时间字段有值
            Map<String, Object> dataModel2 = dataModel();

            dataModel2.put("endTime", "2023-12-31");
            System.out.println("测试用例2：只有时间字段有值");
            generateText(template, dataModel2);

            System.out.println();

            // 测试用例3：只有姓名和地区字段有值
            Map<String, Object> dataModel3 = dataModel();
            dataModel3.put("endTime", "2023-12-31");
            dataModel3.put("name", "张三");

            System.out.println("测试用例3：只有姓名和地区字段有值");
            generateText(template, dataModel3);
            System.out.println();

            // 测试用例4：只有操作相关字段有值
            Map<String, Object> dataModel4 = dataModel();
            dataModel4.put("totalOperations", 5);
            dataModel4.put("openPorts", 2);
            dataModel4.put("closePorts", 3);
            dataModel4.put("constructionCount", 1);
            dataModel4.put("riskCount", 0);

            System.out.println("测试用例4：只有操作相关字段有值");
            generateText(template, dataModel4);
            System.out.println();

            // 测试用例5：所有字段都有值
            Map<String, Object> dataModel5 = dataModel();
            dataModel5.put("startTime", "2023-01-01");
            dataModel5.put("endTime", "2023-12-31");
            dataModel5.put("name", "张三");
            dataModel5.put("regions", "北京市");
            dataModel5.put("totalOperations", 5);
            dataModel5.put("openPorts", 2);
            dataModel5.put("closePorts", 3);
            dataModel5.put("constructionCount", 1);
            dataModel5.put("riskCount", 0);

            System.out.println("测试用例5：所有字段都有值");
            generateText(template, dataModel5);
            System.out.println();

        } catch (IOException | TemplateException e) {
            e.printStackTrace();
        }
    }

    public static String generateText(Template template, Map<String, Object> dataModel) throws IOException, TemplateException {
        Writer out = new StringWriter();
        template.process(dataModel, out);
        String result =  out.toString().replace("\r", "").replace("\n", "").replace(" ", "");
        System.out.println(result);
        return result;
    }
}