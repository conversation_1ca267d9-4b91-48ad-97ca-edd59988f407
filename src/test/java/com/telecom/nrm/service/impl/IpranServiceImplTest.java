package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.IpranService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
class IpranServiceImplTest {


    @Autowired
    IpranService ipranService;

    @Test
    void ipran() {

        String  s ="{\n" +
                "    \"code\": \"扬州扬州ETN6169NPH\",\n" +
                "    \"areaCode\": \"yz\",\n" +
                "    \"access_code\": \"MSTPJS1002791227\",\n" +
                "    \"all\": \"N\"\n" +
                "}";
        try {
            ipranService.ipran(JSONObject.parseObject(s));
        }catch (Exception e){

        }
    }

/*    public static void main(String[] args) {


        //dev分支的后台api
    *//*    DaClient client = new DaClient("http://nrm.oss.telecomjs.com:39049/daas-gray-api/interface", "net_resource_integration");// 测试
        DaProxyFactory factory = new DaProxyFactory(client);
        CircuitAnalysisDao dao = (CircuitAnalysisDao) factory.getInstance(CircuitAnalysisDao.class);
        IpranServiceImpl   o    =new IpranServiceImpl();
        //o.setCircuitAnalysisDao(dao);
        //o.saveCache(JSONObject.parseObject(s),"G00164256");
        JSONObject  jsonObject = o.queryCache("G00164256");
        o.saveCache(jsonObject,"G00164256777");*//*



    }*/
}