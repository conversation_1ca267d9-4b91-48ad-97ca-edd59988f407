package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.Test;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.PONService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.stream.Collectors;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PONServiceImplTest {


    @Autowired
    PONService ponService;

    @Autowired
    GraphApiService graphApiService;


    @Autowired
    GraphService graphService;


    @Test
    public void TestgetCableSegmentTree() {
        String areaCode = "sz";
        String access_code = "19970689";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_cfs_2_pon").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cfs","access_code",access_code)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);

        graphService.buildTree(data,"光路","光缆段","光路到光缆段");
        List<JSONObject> result = graphService.filterNodeByLabel(data,"光路");


    }

}