package com.telecom.nrm.service;

import com.telecom.nrm.domain.ResTreeParamDto;
import com.telecom.nrm.domain.WhereBody;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class BaseLineServiceTest {

    @Autowired
    BaseLineService baseLineService;

    @Test
    void getBusinessCircuitByDevice() throws Exception {
        ResTreeParamDto resTreeParamDto=new ResTreeParamDto();
        List<WhereBody> whereBody =new ArrayList<>();
        WhereBody whereBody1 =new WhereBody();
        whereBody1.setPropertyLabel("regionId");
        whereBody1.setPropertyValue("8321300");
        WhereBody whereBody2 =new WhereBody();
        whereBody2.setPropertyLabel("deviceId");
        whereBody2.setPropertyValue("321272500000000004719062");
        whereBody.add(whereBody1);
        whereBody.add(whereBody2);
        resTreeParamDto.setWhereBody(whereBody);
        resTreeParamDto.setPageNum(1);
        resTreeParamDto.setPageSize(10);
        baseLineService.getBusinessCircuitByDevice(resTreeParamDto);
    }
}