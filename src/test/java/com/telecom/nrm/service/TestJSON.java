package com.telecom.nrm.service;
import com.alibaba.fastjson.JSON;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;

public class TestJSON {

    public static void main(String[] args) {
        String jsonString = "{\n" +

                "    \"address\": {\n" +

                "        \"city\": \"Beijing\",\n" +

                "        \"street\": \"Chang'an Avenue\"\n" +

                "    },\n" +

                "    \"person\": {\n" +

                "        \"name\": \"John\",\n" +

                "        \"age\": 30,\n" +

                "        \"address\": {\n" +

                "            \"$ref\": \"address\"\n" +

                "        }\n" +

                "    }\n" +

                "}";



        // 将 JSON 字符串解析为 JSONObject

        JSONObject jsonObject = JSON.parseObject(jsonString, Feature.DisableCircularReferenceDetect);



        // 访问 person 对象中的 name 字段

        String name = jsonObject.getJSONObject("person").getString("name");

        System.out.println("Name: " + name);



        // 访问被引用的 address 对象中的 city 字段

        JSONObject city = jsonObject.getJSONObject("person").getJSONObject("address");

        System.out.println("City: " + city);
    }
}
