package com.telecom.nrm.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestGisService {
    @Autowired
    GisService gisService;
    @Test
    public void testDoApi() {
        double result = gisService.haversine(31.955537,118.836569, 31.989828, 118.870782);
        log.info("result={}", result);

        result = gisService.haversine(31.989828, 118.870782, 31.955537,118.836569 );
        log.info("result={}", result);
    }
}
