package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestobsLocationService {
    @Autowired
    ObsLocationService obsLocationService;

    @Test
    public void testDoApi() {

        List accessCodes = new ArrayList<>();


        accessCodes.add("19970689");

        String sharingCode = "ds_bc_o3_sz";
        boolean isGroup = true;

        JSONObject jsonObject = obsLocationService.queryObstaclepoints(accessCodes, sharingCode,isGroup, "sz");




    }







}
