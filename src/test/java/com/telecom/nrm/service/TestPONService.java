package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestPONService {
    @Autowired
    PONService ponService;

    @Test
    public void testGetResourceTree() {
        JSONObject request = new JSONObject();
        request.put("accessCodes", "148206777,T147655283,148324857,150780340");
        request.put("areaCode", "yz");
        JSONObject response = ponService.getResourceTree(request);
        log.info("response: {}", response);
    }
//
//    @Test
//    public void testPONService() {
//        JSONObject request = new JSONObject();
//        request.put("areaCode","yz");
//        List<String> loids = new ArrayList<>();
//        loids.add("14DBCC1E366594");
//        loids.add("1460AAE13894EDD3");
//        request.put("loids", loids);
//        JSONObject response = ponService.queryLOID2CFS(request);
//
//
//
//        log.info("response: {}", response);
//    }
}
