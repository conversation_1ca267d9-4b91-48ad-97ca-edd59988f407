package com.telecom.nrm.service;

import com.telecom.nrm.entity.PmProductLinkAnalysis;
import org.apache.commons.collections.CollectionUtils;
import org.apache.lucene.util.CollectionUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.xml.bind.annotation.adapters.CollapsedStringAdapter;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class AuditServiceTest {

    @Autowired
    AuditService auditService;

    @Test
    void batchInsertPmProductLinkAnalysis() {
    }

    @Test
    void selectAll() {
        List<PmProductLinkAnalysis> list = auditService.selectByRegion("ds_bc_o3_cz");
        if (CollectionUtils.isNotEmpty(list)){
            auditService.batchInsertPmProductLinkAnalysis(list);
        }
    }
}