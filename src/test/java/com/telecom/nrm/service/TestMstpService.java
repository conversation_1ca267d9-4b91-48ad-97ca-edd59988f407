package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestMstpService {

    @Autowired
    MstpService mstpService;


    @Test
    public void testDoApi() {

        JSONObject jsontest = new JSONObject();

        JSONObject result = mstpService.mstp(jsontest);
    }

    @Test
    public void testQueryMSTPCircuitsToPipeSegment() {

        /*
        JSONObject request = new JSONObject();
        List<String> codes = new ArrayList<>();
        codes.add("ONU-A-ED7C01F4-PC-C3D4AF95");
        request.put("areaCode","cz");
        request.put("shardingCode","ds_bc_o3_cz");

        JSONObject data = mstpService.queryMSTPCircuitPairToPipeSegment(request);

         */
        //System.out.println("待测试的----"+data);
    }

}
