package com.telecom.nrm.thread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.utils.HttpRequestsUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

class IpranTaskTest {

    public static void main(String[] args) {

        String codeUrl = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/circuitsorderapi";
        String ipranUrl = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/ipranView";
        String au = "Bearer eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************.CKaY9J9uxunhPMwk8fncAN9uI5XDCCwKwYuSWSyg7BOSYOaqLWhe0s1x8WImNFAquEnwDQtUODLXkAka_wrr4w";

        int testCount = 0;
        String inputFile = "src/main/resources/电路明细20240829-1.xlsx";
        String outputFile = "src/main/resources/excel/"; // 新的输出文件
        try {
            FileInputStream fis = new FileInputStream(inputFile);
            Workbook workbook = new XSSFWorkbook(fis);

            // 创建一个新的工作簿来保存更新后的数据
            Workbook outputWorkbook = new XSSFWorkbook();
            Sheet outputSheet = outputWorkbook.createSheet();
            String[] sheet2title = {"电路代号", "电路类别", "地市", "电路是存在", "链路模型", "是否存在跨机房软跳"};
            String[] arr = {"Sheet1", "Sheet2", "Sheet3", "Sheet4"};
            Row row = outputSheet.createRow(0);
            // 构建表头
            for (int i = 0; i < sheet2title.length; i++) {
                row.createCell(i).setCellValue(sheet2title[i]);
            }
            int sheet2Row = 1;
            for (String s : arr) {
                Sheet sheet = workbook.getSheet(s);
                int code = 0, type = 0;
                Row row0 = sheet.getRow(0);
                for (int i = 0; i < row0.getLastCellNum(); i++) {
                    Cell cell = row0.getCell(i);
                    if (cell.getStringCellValue().equals("电路代号")) {
                        code = i;
                    } else if (cell.getStringCellValue().equals("电路类别")) {
                        type = i;
                        break;
                    }
                }
                for (int i = 1; i < sheet.getLastRowNum(); i++) {
                    if (testCount > 10) {
                        break;
                    }
                    testCount++;
                    Row row1 = sheet.getRow(i);
                    String codeId = row1.getCell(code).getStringCellValue();
                    String typeId = row1.getCell(type).getStringCellValue();
                    for (int i1 = 0; i1 < ShardingEnum.values().length; i1++) {
                        ShardingEnum shardingEnum = ShardingEnum.values()[i1];
                        String region = shardingEnum.getRegionName();
                        if (codeId.contains(region)) {
                            Row copyRow = outputSheet.createRow(sheet2Row++);
                            copyRow.createCell(0).setCellValue(codeId);
                            copyRow.createCell(1).setCellValue(typeId);
                            copyRow.createCell(2).setCellValue(region);
                            Map<String, String> header = new HashMap<>();
                            header.put("Authorization", au);
                            Map<String, String> params = new HashMap<>();
                            params.put("page", "1");
                            params.put("size", "10");
                            params.put("ds", region);
                            params.put("circuit_code", codeId);
                            String codeResult = HttpRequestsUtils.sendGet(codeUrl, header, params);
                            if (StringUtils.isEmpty(codeResult)) {
                                copyRow.createCell(3).setCellValue("否");
                                copyRow.createCell(4).setCellValue("否");
                                copyRow.createCell(5).setCellValue("否");
                            } else {
                                JSONObject jsonObject = JSONObject.parseObject(codeResult);
                                JSONArray  data = jsonObject.getJSONArray("data");
                                if (data.size()>0 && StringUtils.isNotEmpty(jsonObject.getJSONArray("data").getJSONObject(0).getString("access_code"))){
                                    String access_code = jsonObject.getJSONArray("data").getJSONObject(0).getString("access_code");
                                    JSONObject  body =new JSONObject();
                                    body.put("access_code",access_code);
                                    body.put("areaCode",shardingEnum.toString().toLowerCase(Locale.ROOT));
                                    body.put("code",codeId);
                                    String ipranResult = HttpRequestsUtils.sendPost(ipranUrl,header,body);
                                    if (StringUtils.isEmpty(ipranResult)){
                                        copyRow.createCell(3).setCellValue("否");
                                        copyRow.createCell(4).setCellValue("否");
                                        copyRow.createCell(5).setCellValue("否");
                                    }else {
                                        JSONObject request = JSONObject.parseObject(ipranResult);
                                        Set<IpranTask.Link> linkSet =new LinkedHashSet<>();
                                        IpranTask.recGetLinks(request.getJSONArray("links"), linkSet);
                                        List<IpranTask.Node> nodeList = IpranTask.Node.jsonarryToList(request.getJSONArray("nodes"));
                                        String getLink2LinkSegment = IpranTask.getLink2LinkSegment(linkSet, nodeList);
                                        boolean falg= IpranTask.checkLinkAcrossFactory( linkSet, nodeList);
                                        if ("数量小于1".equals(getLink2LinkSegment)){
                                            copyRow.createCell(3).setCellValue("否");
                                            copyRow.createCell(4).setCellValue("否");
                                            copyRow.createCell(5).setCellValue("否");
                                        }else {
                                            copyRow.createCell(3).setCellValue("是");
                                            copyRow.createCell(4).setCellValue(getLink2LinkSegment);
                                            copyRow.createCell(5).setCellValue(falg ? "是" : "否");
                                        }

                                    }

                                }else {
                                    copyRow.createCell(3).setCellValue("否");
                                    copyRow.createCell(4).setCellValue("否");
                                    copyRow.createCell(5).setCellValue("否");
                                }
                            }
                        }
                    }
                }
            }

            // 将新的工作簿写入输出文件
            FileOutputStream fos = new FileOutputStream(outputFile + "电路汇总果"+ 20240904 + ".xlsx");
            outputWorkbook.write(fos);
            fos.close();
            System.out.println("Excel file updated successfully.");
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Test
    void recGetLinks() {
    }
}