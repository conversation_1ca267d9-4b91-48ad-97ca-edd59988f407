package com.telecom.nrm.thread.audit;

import com.telecom.nrm.utils.ExcelUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashSet;
import java.util.Set;

@SpringBootTest
class InternetSpecialtyLineTest {

    @Autowired
    InternetSpecialtyLineService internetSpecialtyLine;

    @Test
    void saveData() {



        //internetSpecialtyLine.saveData("盐城","154317398");
        String  path ="src/main/resources/excel/江苏-1.xlsx";
        String sheetName ="互联网专线";
        int  startIndex =7, endIndex =9;
        Sheet  sheet = ExcelUtil.getXSSFWorkbookSheet(path, sheetName);
        Set<String> errorList = new HashSet<>();
        for (int i = 1; i <=sheet.getLastRowNum() ; i++) {
            Row  row = sheet.getRow(i);
            String region = row.getCell(endIndex).getStringCellValue();
            String accessCode = row.getCell(startIndex).getStringCellValue();
            try {
                internetSpecialtyLine.saveData(region,accessCode);
            }catch (Exception e){
                errorList.add( accessCode  + "  " +region);
            }
        }

        System.out.printf("总数量为%S,失败的数量为%s%n",(sheet.getLastRowNum()-1),errorList.size());
        for (String s : errorList) {
            System.out.println(s);
        }
    }
}