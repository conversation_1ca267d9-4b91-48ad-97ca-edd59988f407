package com.telecom.nrm.thread.audit;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.utils.ExcelUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

@SpringBootTest
class CustomerManageCircuitServiceTest {

    @Autowired
    CustomerManageCircuitService customerManageCircuitService;

    @Test
    void createCircuit(Set<Node> noDataSet, Set<Node> failSet, Set<String> count ,boolean indexAdd2) {


        String path = "src/main/resources/excel/江苏-2-地区调整.xlsx";
        String[] sheetNames = {"VIP1", "VIP2", "VIP3", "VIP4", "VIP5"};
        String[] index = {"接入号", "接入端"};
        String[] ids = {"58", "59", "60", "61", "62"};
        List<String> noData = new ArrayList<>();
        List<String> failed = new ArrayList<>();
        int total = 0;
        for (int j = 0; j < sheetNames.length; j++) {
            Sheet sheet = ExcelUtil.getXSSFWorkbookSheet(path, sheetNames[j]);
            if (sheet != null) {
                Row row = sheet.getRow(0);
                int codeIndex = 0, typeIndex = 0;
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    String value = row.getCell(i).getStringCellValue();
                    if (index[0].equals(value)) {
                        codeIndex = i;

                    }
                    if (index[1].equals(value)) {
                        typeIndex = indexAdd2 ? i + 2 : i;
                        break;
                    }
                }
                for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                    Row row1 = sheet.getRow(i);
                    if (null != row1) {
                        try {
                            Cell accessCodeCell = row1.getCell(codeIndex);
                            Cell dsCell = row1.getCell(typeIndex);
                            if (accessCodeCell != null && dsCell != null &&
                                    StringUtils.isNotEmpty(accessCodeCell.getStringCellValue())
                                    && StringUtils.isNotEmpty(dsCell.getStringCellValue())) {
                                total += 1;
                                count.add(accessCodeCell.getStringCellValue());
                                customerManageCircuitService.createCircuit(accessCodeCell.getStringCellValue(),
                                        dsCell.getStringCellValue(), Long.valueOf(ids[j]));
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            if ("noData".equals(e.getMessage())) {
                                Node  node =new Node(row1.getCell(codeIndex).getStringCellValue(),
                                        row1.getCell(typeIndex).getStringCellValue(),"noData",sheetNames[j]);
                                noDataSet.add(node);
                            } else {
                                Node node = new Node(row1.getCell(codeIndex).getStringCellValue(),
                                        row1.getCell(typeIndex).getStringCellValue(), e.getMessage(), sheetNames[j]);
                                failSet.add(node);

                            }
                        }

                    }

                }

            }
        }


    }

    @Test
    void createCircuit2() {
        Set<Node> noData = new HashSet<>();
        Set<Node> fails = new HashSet<>();
        Set<String> count = new HashSet<>();
        createCircuit(noData,fails,count,false);
        createCircuit(noData,fails,count,true);
        System.out.printf("总数为%s,失败的数量为%s,没有数据的数量为%s%n", count.size(), fails.size(), noData.size());
        System.out.println("开始打印失败的");
        fails.forEach(System.out::print);
        System.out.println("开始打印失败的");
        System.out.println("开始打印没有数据的");
        noData.forEach(System.out::print);
        List<Node> noDateList =new ArrayList<>();
        List<Node> failsList =new ArrayList<>();
        for (Node noDatum : noData) {
            ShardingEnum  shardingEnum=   ShardingEnum.getShardingEnumByRegionName(noDatum.getAreaCode());
            if (null != shardingEnum){
                noDateList.add(noDatum);
            }
        }
        for (Node fail : fails) {
            ShardingEnum  shardingEnum=   ShardingEnum.getShardingEnumByRegionName(fail.getAreaCode());
            if (null != shardingEnum){
                failsList.add(fail);
            }
        }
        if (CollectionUtils.isNotEmpty(noDateList)){
            ExcelUtil.writeExcel(AbstractCircuitAuditService.outFilePath+ "无数据"+ AbstractCircuitAuditService.simpleDate.format(new Date()) + ".xlsx",
                    "无数据的sheet", Node.class, noDateList);
        }
        if (CollectionUtils.isNotEmpty(failsList)){
            ExcelUtil.writeExcel(AbstractCircuitAuditService.outFilePath+ "异常"+ AbstractCircuitAuditService.simpleDate.format(new Date()) + ".xlsx",
                    "异常的sheet", Node.class, failsList);
        }
        //异常的sheet

    }


    @Data
    public static class Node {
        @ExcelProperty("接入号")
        @ColumnWidth(30)
        private String accessCode;
        @ExcelProperty("地市")
        @ColumnWidth(30)
        private String areaCode;
        @ExcelProperty("异常信息")
        @ColumnWidth(30)
        private String errorMessage;
        @ExcelProperty("客户名称")
        @ColumnWidth(30)
        private String custName;

        public Node(){};

        public Node(String accessCode, String areaCode, String noData,String custName) {
            this.accessCode = accessCode;
            this.areaCode = areaCode;
            this.errorMessage = noData;
            this.custName = custName;
        }

        @Override
        public String toString() {
            return  accessCode  + " " +areaCode + " " +custName + " "+errorMessage +"\n";
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Node node = (Node) o;
            return accessCode.equals(node.accessCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(accessCode);
        }
    }
}