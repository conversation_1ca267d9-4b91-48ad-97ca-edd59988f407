package com.telecom.nrm.dao;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.telecom.da.client.DaClient;
import com.telecom.da.client.DaProxyFactory;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.thread.audit.AbstractCircuitAuditService;
import com.telecom.nrm.thread.audit.AuditExcelDTO;
import com.telecom.nrm.utils.ExcelUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.impl.nio.reactor.ExceptionEvent;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;


@SpringBootTest
class CircuitAnalysisDaoTest {

    @Autowired
    CircuitAnalysisDao circuitAnalysisDao;

    @Test
    void queryStationByCircuitCode() {
        String s= "{\n" +
                "    \"code\": \"F1907261204\",\n" +
                "    \"areaCode\": \"lyg\"\n" +
                "}";
        JSONObject js =JSONObject.parseObject(s);

        Map<String,String> query  =new HashMap<>();
        query.put("contents", JSON.toJSONString(js, SerializerFeature.DisableCircularReferenceDetect));
        query.put("access_code","111");
        query.put("area_code","5165465");
        query.put("circuit_code","4564654");
        JSONObject jsonObject = circuitAnalysisDao.saveIpranViewCache(query,NRMConstants.SHARDING_CODE);
        System.out.println(jsonObject);

        query.put("code","ASQ.YZZDJA01/ODF09");
        query.put("name", null);
        JSONObject sdfsd = circuitAnalysisDao.queryAlarmInfoByDeviceCode(query, NRMConstants.SHARDING_GRAPH_DB);
        System.out.println(sdfsd);
    }

    public static void main(String[] args) {
        String path = "src/main/resources/excel/station.xlsx";
        DaClient client = new DaClient("http://nrm.oss.telecomjs.com:39049/daas-gray-api/interface", "net_resource_integration");// 测试
        DaProxyFactory factory = new DaProxyFactory(client);
        CircuitAnalysisDao circuitAnalysisDao = (CircuitAnalysisDao) factory.getInstance(CircuitAnalysisDao.class);
        CircuitAnalysisDao dao = (CircuitAnalysisDao) factory.getInstance(CircuitAnalysisDao.class);
        List<Node> list = new ArrayList<>();
        Sheet sheet = ExcelUtil.getXSSFWorkbookSheet(path, "Sheet1");
        JSONObject query = new JSONObject();
        for (int i = 1; i < sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            String code = row.getCell(1).getStringCellValue();
            String acc = row.getCell(0).getStringCellValue();
            String region = row.getCell(2).getStringCellValue();
            query.put("code", code);
            Node node = new Node();
            node.setAccessCodeOri(acc);
            node.setAccessCode(code);
            node.setAreaCode(ShardingEnum.getShardingEnumByO3(region).getRegionName());
            try {
                JSONObject jsonObject = circuitAnalysisDao.queryStationByCircuitCode(query, ShardingEnum.getShardingEnumByO3(region).getBcShardingCode());
                if (null != jsonObject && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("data"))) {
                    for (Object data : jsonObject.getJSONArray("data")) {
                        JSONObject da = (JSONObject) data;
                        Node nodeinner = new Node();
                        nodeinner.setAccessCode(da.getString("access_code"));
                        nodeinner.setAStationName(da.getString("a_name"));
                        nodeinner.setZStationName(da.getString("z_name"));
                        nodeinner.setAccessCodeOri(acc);
                        nodeinner.setCircuitCode(code);
                        nodeinner.setAreaCode(ShardingEnum.getShardingEnumByO3(region).getRegionName());
                        list.add(nodeinner);
                    }
                } else {
                    node.setErrorInfo("未查询到信息");
                    list.add(node);
                }
            } catch (Exception e) {
                e.printStackTrace();
                node.setErrorInfo(e.getMessage());
                list.add(node);
            }

        }
        if (CollectionUtils.isNotEmpty(list)) {
            list =list.stream().distinct().collect(Collectors.toList());
            ExcelUtil.writeExcel(AbstractCircuitAuditService.outFilePath +
                            "电路代号换机房" + AbstractCircuitAuditService.simpleDate.format(new Date()) + ".xlsx",
                    "Sheet1", Node.class, list);
        }

    }

    @Data
    public static class Node {
        @ExcelProperty("接入号")
        @ColumnWidth(30)
        String accessCode;
        @ExcelProperty("电路代号")
        @ColumnWidth(30)
        String circuitCode;
        @ExcelProperty("A端局站名称")
        @ColumnWidth(30)
        String aStationName;
        @ExcelProperty("Z端局站名称")
        @ColumnWidth(30)
        String zStationName;
        @ExcelProperty("异常信息")
        @ColumnWidth(30)
        String errorInfo;
        @ExcelProperty("原接入号")
        @ColumnWidth(30)
        String accessCodeOri;
        @ExcelProperty("地市")
        @ColumnWidth(30)
        String areaCode;
    }
}