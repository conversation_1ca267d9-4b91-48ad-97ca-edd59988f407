package com.telecom.nrm.dao;

import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSONObject;

import org.junit.Test;

import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestCircuitToCableDao {


    @Autowired
    CircuitsToCableDao circuitsToCableDao;
    @Test
    public void testCircuitsToCable () {
        JSONObject param = new JSONObject();
        param.put("circuit_ids", Arrays.asList("321256200025800037325495","321252050000000020135647"));
        param.put("circuit_codes", Arrays.asList("北京南京ZQOTN00013"));
        circuitsToCableDao.CircuitsToCable(param,"ds_odso_nj");

    }


    @Test
    public void testCircuitsToFacility () {

        JSONObject param = new JSONObject();
        param.put("circuit_codes", Arrays.asList("北京南京ZQOTN00013"));
        circuitsToCableDao.CircuitsToFacility(param, "ds_odso_nj");
    }

}