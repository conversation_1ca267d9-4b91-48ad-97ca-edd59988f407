package com.telecom.nrm.dao;

import com.telecom.nrm.dao.impl.GraphPathDaoImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.PostConstruct;
import java.net.ProxySelector;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestGraphPathDao {



    @Autowired
    GraphPathDao graphPathDao ;

    @Test
    public void testParseGraph () {
        String[] paths = {"cfs->carry->mstp_circuit->carry->logic_link->carry->opt_road"};
        graphPathDao.parseGraph(paths);
    }
}
