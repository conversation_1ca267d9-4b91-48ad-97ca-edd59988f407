package com.telecom.nrm.dao;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.GraphDao;
import com.telecom.nrm.dao.impl.BaseGraphDaoImpl;

import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestGraphDao {



    @Autowired
    GraphDao graphDao;

    @Test
    public void testRunGql () {
        Node node = new Node();
        node.setId("device");
        Graph g = graphDao.getNeighbors(node);
        log.info("g: {}", JSONObject.toJSON(g));
    }
    @Test
    public void testGetNode () {
        String id="device";
        Node n = graphDao.getNode(id);
        log.info("g: {}", JSONObject.toJSON(n));
    }

    @Test
    public void testReloadNodes() {
        Set<Node> nodeSet = new HashSet<>();
        String[] idArray = {"device","port"};
        for (int i=0;i<idArray.length;i++) {
            Node n= new Node();
            n.setId(idArray[i]);
            nodeSet.add(n);
        }
        Set<Node> result = graphDao.reloadNodes(nodeSet);
        log.info("result: {}", JSONObject.toJSON(result));
    }


    @Test
    public void testReloadEdges() {
        Node node = new Node();
        node.setId("device");
        Graph g = graphDao.getNeighbors(node);
        Set<Edge> edgeSet =g.getEdges();

        Set<Edge> result = graphDao.reloadEdges(edgeSet);
        log.info("result: {}",result);
    }


    @Test
    public void testRunGQL() {
        JSONArray result = graphDao.runGQL("" +
                "insert edge end_with(api_code,api_version,notes,data_source_type) values \"logic_link\"->\"access_device\":(\"nrm.query_edge_link_end_with_device\",\"V20231205110308572\",\"1\", \"city\");\n"
        );
        log.info("result: {}",result);
    }
}
