package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestGroupFaultRapidPositioningDao {


    @Autowired
    GroupFaultRapidPositioningDao groupFaultRapidPositioningDao;
    @Test


    public void testpageQueryOltObd () {
        JSONObject param = new JSONObject();
        param.put("accs_nbr_nos", Arrays.asList("148206777"));
//        param.put("pageSize", 10);
//        param.put("currentPage", 1);

        String city = "yz";
        String sharding_code = "ds_odso_"+ city;

        groupFaultRapidPositioningDao.pageQueryOltObd(param,10,1,sharding_code);

    }


    @Test
    public void testgetSectByPhyPort () {
        JSONObject param = new JSONObject();
        param.put("phy_port_ids", Arrays.asList("321141840000000025476146"));

        String city = "yz";
        String sharding_code = "ds_odso_"+ city;

        groupFaultRapidPositioningDao.getSectByPhyPort(param,sharding_code);

    }
}

