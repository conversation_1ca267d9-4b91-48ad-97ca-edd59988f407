package com.telecom.nrm.controller;

import com.telecom.nrm.domain.PmIpranTopo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;


@SpringBootTest
class PmIpranTopoControllerTest {

    @Autowired
    PmIpranTopoController  pmIpranTopoController;

    @Test
    void createPmIpranTopo() {

        String  parm="盐城滨海STN000102\n" +
                "兰州扬州STN4374\n" +
                "151797016\n";
        List<String> list= Arrays.asList(parm.split("\n"));
        for (String s : list) {
            PmIpranTopo p =new PmIpranTopo();
            p.setServcode(s);
            pmIpranTopoController.createPmIpranTopo(p);
        }
    }
}