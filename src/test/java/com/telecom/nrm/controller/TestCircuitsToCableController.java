package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.security.NoSuchAlgorithmException;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j

public class TestCircuitsToCableController {


    @Autowired
    CircuitToCableController circuitsToCableController;


    private WebClient webClient = WebClient.builder()
            .baseUrl("http://localhost:1084/api/circuits")
            .build();

    @Test
    public void testDoApi() throws NoSuchAlgorithmException {
        String url= "/circuitstocable";
        JSONObject param = new JSONObject();
//        param.put("circuit_ids", "321252050000000020135647");
        param.put("circuit_codes", "北京南京ZQOTN00013");
        param.put("city", "nj");
        Mono<List> result = webClient.post().
                uri(url)
                .contentType(MediaType.APPLICATION_JSON)  //JSON数据类型
                .syncBody(param)  //JSON字符串数据
                .retrieve() // 获取响应体
                .bodyToMono(List.class);

        List<JSONObject> obj= result.block();
        System.out.println(obj.toString());

    }




    @Test
    public void testCircuitsToFacility() throws NoSuchAlgorithmException {
        String url= "/circuitstofacility";
        JSONObject param = new JSONObject();
        param.put("circuit_codes", "北京南京ZQOTN00013,北京南京ZQOTN00013");
        param.put("city", "nj");
        Mono<List> result = webClient.post().
                uri(url)
                .contentType(MediaType.APPLICATION_JSON)  //JSON数据类型
                .syncBody(param)  //JSON字符串数据
                .retrieve() // 获取响应体
                .bodyToMono(List.class);

        List<JSONObject> obj= result.block();
        log.info("doAPI return "+ obj);

    }
}
