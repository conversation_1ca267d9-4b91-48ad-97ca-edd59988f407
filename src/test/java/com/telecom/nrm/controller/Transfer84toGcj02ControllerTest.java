package com.telecom.nrm.controller;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.NoSuchAlgorithmException;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class Transfer84toGcj02ControllerTest {

    @Autowired
    Transfer84toGcj02Controller transfer84toGcj02Controller;

    @Test
    public void testDoApi() throws NoSuchAlgorithmException {

        transfer84toGcj02Controller.doAPI("118.790577","32.05738","");

    }




}