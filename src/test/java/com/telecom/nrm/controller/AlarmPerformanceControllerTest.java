package com.telecom.nrm.controller;

import net.bytebuddy.asm.Advice;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class AlarmPerformanceControllerTest {

    @Autowired
    AlarmPerformanceController  alarmPerformanceController;

    @Test
    void alarmInfo() {
        Map map  =new HashMap();
        map.put("area_id","321122930000000000000021");
        map.put("device_id","321142500000000010933550");
        alarmPerformanceController.alarmInfo(map);
    }
}