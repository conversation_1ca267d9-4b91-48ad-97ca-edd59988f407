package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.netty.http.client.HttpClient;
import reactor.netty.tcp.TcpClient;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

@SpringBootTest
@Slf4j
public class TestAIStream {

    TcpClient tcpClient = TcpClient.create()
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000*200) // Connection Timeout
            .doOnConnected(connection ->
                    connection.addHandlerLast(new ReadTimeoutHandler(10*200)) // Read Timeout
                            .addHandlerLast(new WriteTimeoutHandler(10*200)));

    WebClient webClient = WebClient.builder().defaultHeader("X-APP-ID","a18f86ca710840688ff0fe023ea431f2")
            .defaultHeader("X-APP-KEY","a6c5eebd88fc4330a1ea18e5276faa8a").exchangeStrategies(ExchangeStrategies.builder()
                    .codecs(configurer -> configurer
                            .defaultCodecs()
                            .maxInMemorySize(1024 * 1024 * 1024))
                    .build()).clientConnector(new ReactorClientHttpConnector(HttpClient.from(tcpClient)))
            .build();
    @Test
    public void testStream() {
        String userInput = "我想查近一周内全省IDC风险"; // 获取用户输入

        JSONObject request = new JSONObject();
        request.put("model", "qwen-72b");
        request.put("stream", true);
        request.put("temperature", 0.5);
        request.put("top_p", 0.9);
        request.put("top_k", 10);

        List<JSONObject> messages = new ArrayList<>();


        JSONObject message = new JSONObject();
        message.put("role", "system");
        message.put("content", "你是一名IDC专家,可以帮我分析用户请求的意图,分析出用户期望的开始时间、截至时间、地市(江苏的13个城市1个或者多个)。返回内容结构如下要求：\\n\" +\n" +
                "                \"1. 是json格式,key为中文,包括：开始时间、截至时间、地市;\\n\" +\n" +
                "                \"2. 对于几天内的描述都以系统时间为当前时间进行计算;\\n\" +\n" +
                "                \"3. 有任何一个属性识别成功即代表成功，无法识别的属性作为空存在。否则认为是失败。如果失败则提示:无法识别您的需求,请细化一些信息,例如:开始时间、截至时间、地市; \\n\" +\n" +
                "                \"4. 如果涉及当前时间取值,当前时间为{currentTime},范围包含当天; \\n\" +\n" +
                "                \"5. 返回结果只要json数据,无需其他任何解释内容。");
        messages.add(message);

        message = new JSONObject();
        message.put("role", "user");
        message.put("content", userInput);
        messages.add(message);
        request.put("messages", messages);

        // 调用 DeepSeek 的流式 API
        Flux<String> streamResponse = webClient.post()
                .uri("http://openapi.telecomjs.com:80/eop/AIGLKFPT/ChatBI_LLM/completions") // DeepSeek 的流式 API 地址
                .bodyValue(request) // 请求体
                .retrieve()
                .bodyToFlux(String.class); // 以流的形式接收响应

        // 逐字发送给前端
        streamResponse.subscribe(
                chunk -> {
                    System.out.println(chunk);
                },
                error -> {
                    log.error(error.getMessage(),error);
                },
                () -> {
                    log.info("endend");
                }
        );
        Scanner scanner = new Scanner(System.in);
        System.out.println("Press Enter to exit...");
        scanner.nextLine(); // 等待用户输入
        scanner.close();
    }
}
