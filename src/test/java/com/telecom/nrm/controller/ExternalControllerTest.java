package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class ExternalControllerTest {

    @Autowired
    ExternalController externalController;

    @Test
    void queryAll() {
        JSONObject jsonObject =new JSONObject();
        jsonObject.put("access_code","16179242");
        externalController.queryAll(jsonObject);
    }
}