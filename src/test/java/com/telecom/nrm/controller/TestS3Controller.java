package com.telecom.nrm.controller;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestS3Controller {

    private static final Logger log = LoggerFactory.getLogger(TestS3Controller.class);

    @Autowired
    private S3Controller s3Controller;

    @Test
    public void testDownloadFile() throws Exception {
        String fileName = "地市网络维护后端工作调研提纲-0416-新.docx";

        // 测试下载
        ResponseEntity<InputStreamResource> response = s3Controller.downloadFile(fileName);

        // 验证响应
        assert response.getStatusCode().is2xxSuccessful();
        assert response.getBody() != null;

        // 将下载的文件保存到本地进行验证
        Resource resource = response.getBody();
        File downloadedFile = new File("test-download.docx");

        try (InputStream is = resource.getInputStream();
             FileOutputStream fos = new FileOutputStream(downloadedFile)) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }

        // 验证文件是否下载成功
        assert downloadedFile.exists();
        assert downloadedFile.length() > 0;

        log.info("File downloaded successfully. Size: {} bytes", downloadedFile.length());

        // 清理测试文件
//        downloadedFile.delete();
    }
}