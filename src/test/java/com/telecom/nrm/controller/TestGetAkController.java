package com.telecom.nrm.controller;

import com.telecom.nrm.service.GraphApiService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.NoSuchAlgorithmException;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j

public class TestGetAkController {

    @Autowired
    GetAkController getAkController;

    @Test
    public void testDoApi() throws NoSuchAlgorithmException {

//        getAkController.getAkrequest();
        getAkController.doAPI();


    }
}