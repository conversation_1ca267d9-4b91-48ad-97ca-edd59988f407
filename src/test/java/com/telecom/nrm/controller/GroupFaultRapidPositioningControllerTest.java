package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;

import static org.junit.jupiter.api.Assertions.*;





@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class GroupFaultRapidPositioningControllerTest {

    @Autowired
    GroupFaultRapidPositioningController groupFaultRapidPositioningController;


    private WebClient webClient = WebClient.builder()
            .baseUrl("http://localhost:1084/api/accs_nbr_no")
            .build();

    @Test
    void checkpon() {

        String url= "/checkpon";
        JSONObject param = new JSONObject();
        JSONArray segments =new JSONArray();
        segments.add("ASQ.SCSJ0/PXG03/01(ODF-GT001)");
        segments.add("ASQ.CYJYA/ZGG19/01(ODF-GT001)");
        segments.add("ASQ.CYJYA/ZGG14/03(GT001-GJ012)");

        param.put("segments", segments);
        param.put("city", "yz");

        Mono<JSONObject> result = webClient.post().
                uri(url)
                .contentType(MediaType.APPLICATION_JSON)  //JSON数据类型
                .syncBody(param)  //JSON字符串数据
                .retrieve()
                .bodyToMono(JSONObject.class); // 获取响应体

        JSONObject obj= result.block();
        log.info("test checkpon return "+ obj);




    }


    @Test
    void makecause() {
        JSONObject param = new JSONObject();

//        param.put();
        groupFaultRapidPositioningController.makecause(param);
    }



    @Test
    void XmlToJsonConverter() throws IOException {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                + "<service>"
                + "   <data_info>"
                + "      <ResultDoc>"
                + "         <ReturnCode>1</ReturnCode>"
                + "         <CauseId>1548618543</CauseId>"
                + "         <Message>操作成功！</Message>"
                + "      </ResultDoc>"
                + "   </data_info>"
                + "</service>";

        XmlMapper xmlMapper = new XmlMapper();
        JsonNode jsonNode = xmlMapper.readTree(xml.getBytes("UTF-8"));
        ObjectMapper jsonMapper = new ObjectMapper();
        String json = jsonMapper.writeValueAsString(jsonNode);

    }





}