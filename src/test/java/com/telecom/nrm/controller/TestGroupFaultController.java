package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.security.NoSuchAlgorithmException;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestGroupFaultController {



    @Autowired
    GroupFaultRapidPositioningController groupFaultRapidPositioningController;



    private WebClient webClient = WebClient.builder()
            .baseUrl("http://localhost:1084/api/accs_nbr_no")
            .build();


    @Test
    public void testDoApi() throws NoSuchAlgorithmException {

        String url= "/groupfault";
        JSONObject param = new JSONObject();
        param.put("accs_nbr_nos", "148206777,T147655283,148324857,150780340,147145708,154897137");
        param.put("pageSize", 10);
        param.put("currentPage", 1);
        param.put("city", "yz");
        Mono<JSONObject> result = webClient.post().
                uri(url)
                .contentType(MediaType.APPLICATION_JSON)  //JSON数据类型
                .syncBody(param)  //JSON字符串数据
                .retrieve()
                .bodyToMono(JSONObject.class); // 获取响应体

        JSONObject obj= result.block();
        log.info("doAPI return "+ obj);




    }
}



