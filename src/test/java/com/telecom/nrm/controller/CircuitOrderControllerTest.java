package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.thread.Task;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
class CircuitOrderControllerTest {

    @Autowired
    CircuitOrderController circuitOrderController;

    @Test
    void getPage() {
        Map<String, String> parm = new HashMap<>();
        parm.put("ds", "苏州");
        parm.put("circuit_code", "北京太仓ETN2002NP");
        BiyiPageRequest biyiPageRequest = new BiyiPageRequest();
        biyiPageRequest.setPage(1);
        biyiPageRequest.setSize(10);
        BiyiPageResult<JSONObject> result = circuitOrderController.getPage(parm, biyiPageRequest);
        System.out.println(result.getData());
    }


    public static void main(String[] args) {
        String au = "Bearer eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************._E-MVxB8TsZ3uypXmksxAIGmUC8qXKNsx13baqZYpfJnvYm3MDZLB1jXn_MfxolwpNvpvksB09973B1IT7H2KQ";
        // 创建一个 Runnable 数组
        Runnable[] runnables = new Runnable[ShardingEnum.values().length];
        // 创建并启动线程
        for (int i = 0; i < ShardingEnum.values().length; i++) {
            if (ShardingEnum.values()[i].getRegionName().equals("南京")) {
                runnables[0] = new Task("南京", au);
               // runnables[i] = new Task(ShardingEnum.values()[i].getRegionName(), au);
                Thread thread = new Thread(runnables[0]);
                thread.start();
            }
        }
    }
    }
