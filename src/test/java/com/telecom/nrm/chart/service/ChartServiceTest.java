package com.telecom.nrm.chart.service;

import com.alibaba.fastjson.JSON;
import com.telecom.nrm.agent.chart.service.ChartService;
import com.telecom.nrm.agent.chart.model.ChartData;
import com.telecom.nrm.agent.chart.service.impl.ChartServiceImpl;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.awt.image.BufferedImage;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 图表服务测试类
 */
public class ChartServiceTest {

    private ChartService chartService;

    @BeforeEach
    public void setUp() {
        chartService = new ChartServiceImpl();
    }

    @Test
    public void testParseBase64ChartData() {
        // 创建测试数据
        ChartData chartData = createSampleBarChartData();

        // 将测试数据转换为JSON字符串
        String jsonData = JSON.toJSONString(chartData);

        // 将JSON字符串编码为Base64
        String base64Data = Base64.encodeBase64String(jsonData.getBytes(StandardCharsets.UTF_8));

        // 解析Base64数据
        ChartData parsedData = chartService.parseBase64ChartData(base64Data);

        // 验证解析结果
        assertNotNull(parsedData);
        assertEquals("bar", parsedData.getType());
        assertEquals("示例柱状图", parsedData.getTitle());
        assertEquals(2, parsedData.getSeries().size());
    }

    @Test
    public void testGenerateBarChart() {
        // 创建柱状图数据
        ChartData chartData = createSampleBarChartData();

        // 生成图表
        BufferedImage image = chartService.generateChart(chartData);

        // 验证图表生成结果
        assertNotNull(image);
        assertEquals(800, image.getWidth());
        assertEquals(600, image.getHeight());
    }

    @Test
    public void testGeneratePieChart() {
        // 创建饼图数据
        ChartData chartData = createSamplePieChartData();

        // 生成图表
        BufferedImage image = chartService.generateChart(chartData);

        // 验证图表生成结果
        assertNotNull(image);
        assertEquals(800, image.getWidth());
        assertEquals(600, image.getHeight());
    }

    @Test
    public void testGenerateLineChart() {
        // 创建折线图数据
        ChartData chartData = createSampleLineChartData();

        // 生成图表
        BufferedImage image = chartService.generateChart(chartData);

        // 验证图表生成结果
        assertNotNull(image);
        assertEquals(800, image.getWidth());
        assertEquals(600, image.getHeight());
    }

    @Test
    public void testInvalidChartType() {
        // 创建无效图表类型的数据
        ChartData chartData = new ChartData();
        chartData.setType("invalid");
        chartData.setTitle("无效图表");

        // 验证生成图表时抛出异常
        assertThrows(IllegalArgumentException.class, () -> chartService.generateChart(chartData));
    }

    @Test
    public void testGenerateBarChartSVG() {
        // 创建柱状图数据
        ChartData chartData = createSampleBarChartData();

        // 生成SVG图表
        String svgContent = chartService.generateChartSVG(chartData);

        // 验证SVG生成结果
        assertNotNull(svgContent);
        assertTrue(svgContent.contains("<svg"));
        assertTrue(svgContent.contains("</svg>"));
        assertTrue(svgContent.contains("示例柱状图"));
    }

    @Test
    public void testGeneratePieChartSVG() {
        // 创建饼图数据
        ChartData chartData = createSamplePieChartData();

        // 生成SVG图表
        String svgContent = chartService.generateChartSVG(chartData);

        // 验证SVG生成结果
        assertNotNull(svgContent);
        assertTrue(svgContent.contains("<svg"));
        assertTrue(svgContent.contains("</svg>"));
        assertTrue(svgContent.contains("示例饼图"));
    }

    @Test
    public void testGenerateLineChartSVG() {
        // 创建折线图数据
        ChartData chartData = createSampleLineChartData();

        // 生成SVG图表
        String svgContent = chartService.generateChartSVG(chartData);

        // 验证SVG生成结果
        assertNotNull(svgContent);
        assertTrue(svgContent.contains("<svg"));
        assertTrue(svgContent.contains("</svg>"));
        assertTrue(svgContent.contains("示例折线图"));
    }

    @Test
    public void testInvalidChartTypeSVG() {
        // 创建无效图表类型的数据
        ChartData chartData = new ChartData();
        chartData.setType("invalid");
        chartData.setTitle("无效图表");

        // 验证生成SVG图表时抛出异常
        assertThrows(IllegalArgumentException.class, () -> chartService.generateChartSVG(chartData));
    }

    @Test
    public void testChineseFontInSVG() {
        // 创建包含中文的图表数据
        ChartData chartData = new ChartData();
        chartData.setType("bar");
        chartData.setTitle("中文标题测试");
        chartData.setxAxisLabel("中文X轴");
        chartData.setyAxisLabel("中文Y轴");
        chartData.setWidth(800);
        chartData.setHeight(600);

        List<ChartData.Series> seriesList = new ArrayList<>();
        ChartData.Series series = new ChartData.Series();
        series.setName("中文系列");
        series.setData(Arrays.asList(10, 20, 30));
        series.setLabels(Arrays.asList("一月", "二月", "三月"));
        series.setColor("#FF4500");
        seriesList.add(series);
        chartData.setSeries(seriesList);

        // 生成SVG图表
        String svgContent = chartService.generateChartSVG(chartData);

        // 验证SVG包含中文内容和字体设置
        assertNotNull(svgContent);
        assertTrue(svgContent.contains("<svg"));
        assertTrue(svgContent.contains("</svg>"));
        assertTrue(svgContent.contains("中文标题测试"));

        // 验证SVG包含字体回退设置
        assertTrue(svgContent.contains("sans-serif") ||
                  svgContent.contains("YaHei") ||
                  svgContent.contains("SimHei"));
    }

    @Test
    public void testRealWorldChineseData() {
        // 使用真实的中文数据进行测试
        String base64Data = "eyJ0eXBlIjoibGluZSIsInRpdGxlIjoiMjAyNeW5tDEtNOaciOmmluasoeeoveaguOi/neinhOeOh+i2i+WKvyIsInhBeGlzTGFiZWwiOiLmnIjku70iLCJ5QXhpc0xhYmVsIjoi6L+d6KeE546HKCUpIiwid2lkdGgiOjgwMCwiaGVpZ2h0Ijo1MDAsIm9wdGlvbnMiOnsiZHBpIjoxNTB9LCJzZXJpZXMiOlt7Im5hbWUiOiLov53op4TnjociLCJkYXRhIjpbMzMuNjEsNjIuMDQsMTkuMTMsMzIuMDhdLCJsYWJlbHMiOlsiMjAyNS0wMSIsIjIwMjUtMDIiLCIyMDI1LTAzIiwiMjAyNS0wNCJdLCJjb2xvciI6IiNGRjZCNkIifV19";

        // 解析数据
        ChartData chartData = chartService.parseBase64ChartData(base64Data);

        // 验证解析结果
        assertNotNull(chartData);
        assertEquals("line", chartData.getType());
        assertEquals("2025年1-4月首次稽核违规率趋势", chartData.getTitle());
        assertEquals("月份", chartData.getxAxisLabel());
        assertEquals("违规率(%)", chartData.getyAxisLabel());

        // 生成SVG
        String svgContent = chartService.generateChartSVG(chartData);

        // 验证SVG内容
        assertNotNull(svgContent);
        assertTrue(svgContent.contains("<svg"));
        assertTrue(svgContent.contains("</svg>"));
        assertTrue(svgContent.contains("2025年1-4月首次稽核违规率趋势"));
        assertTrue(svgContent.contains("月份"));
        assertTrue(svgContent.contains("违规率"));

        // 验证数据点
        assertTrue(svgContent.contains("2025-01") || svgContent.contains("33.61"));

        // 输出SVG内容长度以确认生成成功
        assertTrue(svgContent.length() > 1000); // SVG应该有相当的长度

        // 验证SVG包含字体样式设置
        assertTrue(svgContent.contains("<style") || svgContent.contains("font-family"));
        assertTrue(svgContent.contains("Microsoft YaHei") || svgContent.contains("PingFang") || svgContent.contains("sans-serif"));

        // 验证SVG包含UTF-8编码声明
        assertTrue(svgContent.contains("UTF-8") || svgContent.contains("encoding"));

        System.out.println("SVG生成成功，长度: " + svgContent.length() + " 字符");
        System.out.println("包含字体设置: " + (svgContent.contains("Microsoft YaHei") ? "是" : "否"));
        System.out.println("包含样式标签: " + (svgContent.contains("<style") ? "是" : "否"));

        // 输出SVG的前500个字符以便检查
        System.out.println("SVG内容前500字符:");
        System.out.println(svgContent.substring(0, Math.min(500, svgContent.length())));
        System.out.println("...");

        // 查找并输出包含中文的文本元素
        if (svgContent.contains("2025年")) {
            int pos = svgContent.indexOf("2025年");
            int start = Math.max(0, pos - 100);
            int end = Math.min(svgContent.length(), pos + 200);
            System.out.println("包含中文的文本片段:");
            System.out.println(svgContent.substring(start, end));
        }

        // 检查是否有重复的 style 属性
        int styleCount = svgContent.split("style=").length - 1;
        int duplicateStyleCount = svgContent.split("style=\"[^\"]*\"[^>]*style=").length - 1;
        System.out.println("总 style 属性数量: " + styleCount);
        System.out.println("重复 style 属性数量: " + duplicateStyleCount);

        // 验证没有重复的 style 属性
        assertFalse(svgContent.contains("style=\"") && svgContent.matches(".*style=\"[^\"]*\"[^>]*style=\".*"),
                   "SVG 不应包含重复的 style 属性");
    }

    @Test
    public void testTransparentBackgroundSVG() {
        // 创建包含透明背景设置的图表数据
        ChartData chartData = new ChartData();
        chartData.setType("bar");
        chartData.setTitle("透明背景测试图表");
        chartData.setxAxisLabel("类别");
        chartData.setyAxisLabel("数值");
        chartData.setWidth(800);
        chartData.setHeight(600);

        // 设置透明背景
        Map<String, Object> options = new HashMap<>();
        options.put("backgroundColor", "transparent");
        chartData.setOptions(options);

        List<ChartData.Series> seriesList = new ArrayList<>();
        ChartData.Series series = new ChartData.Series();
        series.setName("测试系列");
        series.setData(Arrays.asList(10, 20, 30));
        series.setLabels(Arrays.asList("A", "B", "C"));
        series.setColor("#FF4500");
        seriesList.add(series);
        chartData.setSeries(seriesList);

        // 生成SVG图表
        String svgContent = chartService.generateChartSVG(chartData);

        // 验证SVG生成成功
        assertNotNull(svgContent);
        assertTrue(svgContent.contains("<svg"));
        assertTrue(svgContent.contains("</svg>"));

        // 验证透明背景设置
        assertTrue(svgContent.contains("fill=\"none\"") || !svgContent.contains("fill=\"white\""));

        System.out.println("透明背景SVG生成成功，长度: " + svgContent.length() + " 字符");
        System.out.println("包含透明设置: " + (svgContent.contains("fill=\"none\"") ? "是" : "否"));

        // 输出SVG的前500个字符以便检查背景设置
        System.out.println("SVG内容前500字符:");
        System.out.println(svgContent.substring(0, Math.min(500, svgContent.length())));

        // 检查是否包含白色填充
        boolean hasWhiteFill = svgContent.contains("fill=\"white\"") ||
                              svgContent.contains("fill=\"rgb(255,255,255)\"") ||
                              svgContent.contains("fill=\"#ffffff\"") ||
                              svgContent.contains("fill=\"#FFFFFF\"");
        System.out.println("包含白色填充: " + (hasWhiteFill ? "是" : "否"));
    }

    @Test
    public void testWhiteBackgroundSVG() {
        // 创建包含白色背景设置的图表数据
        ChartData chartData = new ChartData();
        chartData.setType("line");
        chartData.setTitle("白色背景测试图表");
        chartData.setxAxisLabel("时间");
        chartData.setyAxisLabel("数值");
        chartData.setWidth(800);
        chartData.setHeight(600);

        // 设置白色背景（默认）
        Map<String, Object> options = new HashMap<>();
        options.put("backgroundColor", "white");
        chartData.setOptions(options);

        List<ChartData.Series> seriesList = new ArrayList<>();
        ChartData.Series series = new ChartData.Series();
        series.setName("测试系列");
        series.setData(Arrays.asList(15, 25, 35));
        series.setLabels(Arrays.asList("1月", "2月", "3月"));
        series.setColor("#4169E1");
        seriesList.add(series);
        chartData.setSeries(seriesList);

        // 生成SVG图表
        String svgContent = chartService.generateChartSVG(chartData);

        // 验证SVG生成成功
        assertNotNull(svgContent);
        assertTrue(svgContent.contains("<svg"));
        assertTrue(svgContent.contains("</svg>"));

        System.out.println("白色背景SVG生成成功，长度: " + svgContent.length() + " 字符");
    }

    /**
     * 创建示例柱状图数据
     */
    private ChartData createSampleBarChartData() {
        ChartData chartData = new ChartData();
        chartData.setType("bar");
        chartData.setTitle("示例柱状图");
        chartData.setxAxisLabel("类别");
        chartData.setyAxisLabel("数值");
        chartData.setWidth(800);
        chartData.setHeight(600);

        List<ChartData.Series> seriesList = new ArrayList<>();

        // 第一个系列
        ChartData.Series series1 = new ChartData.Series();
        series1.setName("系列1");
        series1.setData(Arrays.asList(10, 20, 30, 40, 50));
        series1.setLabels(Arrays.asList("A", "B", "C", "D", "E"));
        series1.setColor("#FF4500");
        seriesList.add(series1);

        // 第二个系列
        ChartData.Series series2 = new ChartData.Series();
        series2.setName("系列2");
        series2.setData(Arrays.asList(15, 25, 35, 45, 55));
        series2.setLabels(Arrays.asList("A", "B", "C", "D", "E"));
        series2.setColor("#4169E1");
        seriesList.add(series2);

        chartData.setSeries(seriesList);

        return chartData;
    }

    /**
     * 创建示例饼图数据
     */
    private ChartData createSamplePieChartData() {
        ChartData chartData = new ChartData();
        chartData.setType("pie");
        chartData.setTitle("示例饼图");
        chartData.setWidth(800);
        chartData.setHeight(600);

        List<ChartData.Series> seriesList = new ArrayList<>();

        // 饼图只需要一个系列
        ChartData.Series series = new ChartData.Series();
        series.setName("数据");
        series.setData(Arrays.asList(20, 30, 15, 25, 10));
        series.setLabels(Arrays.asList("A", "B", "C", "D", "E"));
        seriesList.add(series);

        chartData.setSeries(seriesList);

        return chartData;
    }

    /**
     * 创建示例折线图数据
     */
    private ChartData createSampleLineChartData() {
        ChartData chartData = new ChartData();
        chartData.setType("line");
        chartData.setTitle("示例折线图");
        chartData.setxAxisLabel("时间");
        chartData.setyAxisLabel("数值");
        chartData.setWidth(800);
        chartData.setHeight(600);

        List<ChartData.Series> seriesList = new ArrayList<>();

        // 第一个系列
        ChartData.Series series1 = new ChartData.Series();
        series1.setName("系列1");
        series1.setData(Arrays.asList(10, 25, 15, 30, 20));
        series1.setLabels(Arrays.asList("1月", "2月", "3月", "4月", "5月"));
        series1.setColor("#FF4500");
        seriesList.add(series1);

        // 第二个系列
        ChartData.Series series2 = new ChartData.Series();
        series2.setName("系列2");
        series2.setData(Arrays.asList(20, 15, 30, 25, 35));
        series2.setLabels(Arrays.asList("1月", "2月", "3月", "4月", "5月"));
        series2.setColor("#4169E1");
        seriesList.add(series2);

        chartData.setSeries(seriesList);

        return chartData;
    }
}
