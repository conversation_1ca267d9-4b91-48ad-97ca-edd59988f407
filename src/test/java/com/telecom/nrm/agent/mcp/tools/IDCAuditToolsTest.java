package com.telecom.nrm.agent.mcp.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * IDCAuditTools 测试类
 */
public class IDCAuditToolsTest {

    private IDCAuditTools idcAuditTools;

    @BeforeEach
    public void setUp() {
        idcAuditTools = new IDCAuditTools();
    }

    @Test
    public void testGenerateAuditQueryJson_AllParameters() {
        // 测试所有参数都提供的情况
        String result = idcAuditTools.generateAuditQueryJson(
            "***********00",
            "GE0/0/1",
            "张三",
            "2025-05-06",
            "2025-06-06",
            "南京,苏州,无锡"
        );

        assertNotNull(result);
        JSONObject json = JSON.parseObject(result);

        System.out.println("完整参数测试结果: " + result);
        System.out.println("JSON包含的键: " + json.keySet());

        // 验证英文字段
        assertEquals("***********00", json.getString("deviceIp"));
        assertEquals("GE0/0/1", json.getString("portName"));
        assertEquals("张三", json.getString("userName"));
        assertEquals("2025-05-06 00:00:00", json.getString("startDate"));
        assertEquals("2025-06-06 00:00:00", json.getString("endDate"));
        assertEquals("0", json.getString("code"));

        // 验证中文字段
        assertEquals("***********00", json.getString("设备IP"));
        assertEquals("GE0/0/1", json.getString("端口名称"));
        assertEquals("张三", json.getString("用户名"));
        assertEquals("2025-05-06 00:00:00", json.getString("开始时间"));
        assertEquals("2025-06-06 00:00:00", json.getString("截至时间"));

        // 验证英文地市数组
        assertTrue(json.getJSONArray("regionNames").contains("南京"));
        assertTrue(json.getJSONArray("regionNames").contains("苏州"));
        assertTrue(json.getJSONArray("regionNames").contains("无锡"));

        // 验证中文地市数组
        assertTrue(json.getJSONArray("地市").contains("南京"));
        assertTrue(json.getJSONArray("地市").contains("苏州"));
        assertTrue(json.getJSONArray("地市").contains("无锡"));
    }

    @Test
    public void testGenerateAuditQueryJson_PartialParameters() {
        // 测试部分参数的情况
        String result = idcAuditTools.generateAuditQueryJson(
            "********",
            null,
            "李四",
            null,
            null,
            "徐州"
        );

        assertNotNull(result);
        JSONObject json = JSON.parseObject(result);

        // 验证提供的参数
        assertEquals("********", json.getString("deviceIp"));
        assertEquals("", json.getString("portName"));
        assertEquals("李四", json.getString("userName"));

        // 验证默认时间范围
        assertNotNull(json.getString("startDate"));
        assertNotNull(json.getString("endDate"));
        assertTrue(json.getString("startDate").matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
        assertTrue(json.getString("endDate").matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));

        // 验证地市
        assertTrue(json.getJSONArray("regionNames").contains("徐州"));

        System.out.println("部分参数测试结果: " + result);
    }

    @Test
    public void testGenerateAuditQueryJson_EmptyParameters() {
        // 测试空参数的情况
        String result = idcAuditTools.generateAuditQueryJson("", "", "", "", "", "");

        assertNotNull(result);
        JSONObject json = JSON.parseObject(result);

        // 验证空字符串
        assertEquals("", json.getString("deviceIp"));
        assertEquals("", json.getString("portName"));
        assertEquals("", json.getString("userName"));

        // 验证默认时间和地市
        assertNotNull(json.getString("startDate"));
        assertNotNull(json.getString("endDate"));
        assertTrue(json.getJSONArray("regionNames").size() > 0);
        assertEquals("0", json.getString("code"));

        System.out.println("空参数测试结果: " + result);
    }

    @Test
    public void testGenerateAuditQueryJson_NullParameters() {
        // 测试null参数的情况
        String result = idcAuditTools.generateAuditQueryJson(null, null, null, null, null, null);

        assertNotNull(result);
        JSONObject json = JSON.parseObject(result);

        // 验证null被处理为空字符串
        assertEquals("", json.getString("deviceIp"));
        assertEquals("", json.getString("portName"));
        assertEquals("", json.getString("userName"));
        assertEquals("0", json.getString("code"));

        System.out.println("null参数测试结果: " + result);
    }

    @Test
    public void testGenerateAuditQueryJson_TimeFormats() {
        // 测试不同时间格式
        String[] startDates = {
            "2025-01-01",
            "2025-01-01 10:30:00",
            "2025年1月1日"
        };

        String[] endDates = {
            "2025-12-31",
            "2025-12-31 23:59:59",
            "2025年12月31日"
        };

        for (int i = 0; i < startDates.length; i++) {
            String result = idcAuditTools.generateAuditQueryJson(
                "***********",
                "GE1/0/1",
                "测试用户",
                startDates[i],
                endDates[i],
                "南京"
            );

            JSONObject json = JSON.parseObject(result);

            System.out.println("时间格式测试 " + (i+1) + ":");
            System.out.println("输入开始时间: " + startDates[i] + " -> " + json.getString("startDate"));
            System.out.println("输入结束时间: " + endDates[i] + " -> " + json.getString("endDate"));

            // 验证时间格式标准化
            assertTrue(json.getString("startDate").matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
            assertTrue(json.getString("endDate").matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
        }
    }

    @Test
    public void testGenerateAuditQueryJson_RegionFormats() {
        // 测试不同地市格式
        String[] regionInputs = {
            "南京,苏州,无锡",
            "南京，苏州，无锡",
            "南京;苏州;无锡",
            "南京 苏州 无锡",
            "南京市,苏州市,无锡市"
        };

        for (String regionInput : regionInputs) {
            String result = idcAuditTools.generateAuditQueryJson(
                "***********",
                "GE1/0/1",
                "测试用户",
                "2025-01-01",
                "2025-12-31",
                regionInput
            );

            JSONObject json = JSON.parseObject(result);

            System.out.println("地市格式测试: " + regionInput + " -> " + json.getJSONArray("regionNames"));

            // 验证包含预期的地市
            assertTrue(json.getJSONArray("regionNames").contains("南京"));
            assertTrue(json.getJSONArray("regionNames").contains("苏州"));
            assertTrue(json.getJSONArray("regionNames").contains("无锡"));
        }
    }

    @Test
    public void testGenerateAuditQueryJson_MatchesExpectedFormat() {
        // 测试生成的JSON格式是否符合预期
        String result = idcAuditTools.generateAuditQueryJson(
            "***********00",
            "GE0/0/1",
            "张三",
            "2025-05-06",
            "2025-06-06",
            "南京,徐州,无锡,连云港,宿迁,苏州,常州,镇江,盐城"
        );

        JSONObject json = JSON.parseObject(result);

        // 验证JSON结构完整性 - 英文字段
        assertTrue(json.containsKey("deviceIp"));
        assertTrue(json.containsKey("portName"));
        assertTrue(json.containsKey("userName"));
        assertTrue(json.containsKey("startDate"));
        assertTrue(json.containsKey("endDate"));
        assertTrue(json.containsKey("regionNames"));
        assertTrue(json.containsKey("code"));

        // 验证JSON结构完整性 - 中文字段
        assertTrue(json.containsKey("设备IP"));
        assertTrue(json.containsKey("端口名称"));
        assertTrue(json.containsKey("用户名"));
        assertTrue(json.containsKey("开始时间"));
        assertTrue(json.containsKey("截至时间"));
        assertTrue(json.containsKey("地市"));

        // 验证数组类型
        assertTrue(json.get("regionNames") instanceof com.alibaba.fastjson.JSONArray);
        assertTrue(json.get("地市") instanceof com.alibaba.fastjson.JSONArray);

        System.out.println("格式匹配测试结果: " + result);

        // 验证与示例格式的一致性
        assertEquals(9, json.getJSONArray("regionNames").size());
        assertEquals(9, json.getJSONArray("地市").size());
        assertEquals(json.getJSONArray("regionNames"), json.getJSONArray("地市"));
    }
}
