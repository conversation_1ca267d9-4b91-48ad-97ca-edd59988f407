package com.telecom.nrm.agent.mcp.service;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试detail提取功能
 */
public class DetailExtractionTest {

    @Test
    public void testDetailExtraction() {
        IDCAuditAgentService service = new IDCAuditAgentService();

        // 测试包含detail的消息
        String message = "根据您的查询需求，我已经为您生成了审计查询参数。<detail>\n" +
                "{\"deviceIp\":\"*************\",\"portName\":\"GE0/0/1\",\"userName\":\"张三\",\"startDate\":\"2025-05-05 00:00:00\",\"endDate\":\"2025-06-05 23:59:59\",\"regionNames\":[\"南京\"],\"code\":\"0\"}\n" +
                "</detail>请在详情界面查看具体信息。";

        // 使用反射调用私有方法
        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        // 验证结果
        assertNotNull(result, "应该能够提取到detail内容");

        // 新的返回格式：成功时返回detail中的JSON内容，并确保包含code="0"
        assertEquals("*************", result.getString("deviceIp"));
        assertEquals("GE0/0/1", result.getString("portName"));
        assertEquals("张三", result.getString("userName"));
        assertEquals("2025-05-05 00:00:00", result.getString("startDate"));
        assertEquals("2025-06-05 23:59:59", result.getString("endDate"));
        assertEquals("0", result.getString("code"), "成功时code应该为0");
        assertTrue(result.getJSONArray("regionNames").contains("南京"));

        System.out.println("✅ Detail提取测试通过");
        System.out.println("提取的结果: " + result.toJSONString());
    }

    @Test
    public void testNoDetail() {
        IDCAuditAgentService service = new IDCAuditAgentService();

        // 测试不包含detail的消息
        String message = "这是一个普通的回复消息，没有detail标签。";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该返回包含code和msg的结果");
        assertEquals("-1", result.getString("code"), "不包含detail的消息code应该为-1");
        assertEquals("未找到完整的detail标签", result.getString("msg"));
        System.out.println("✅ 无detail测试通过: " + result.toJSONString());
    }

    @Test
    public void testInvalidDetail() {
        IDCAuditAgentService service = new IDCAuditAgentService();

        // 测试包含无效JSON的detail
        String message = "包含无效JSON的消息。<detail>{invalid json content}</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该返回包含code和msg的结果");
        assertEquals("-1", result.getString("code"), "无效JSON的code应该为-1");
        assertTrue(result.getString("msg").contains("JSON解析失败"), "msg应该包含解析失败信息");
        System.out.println("✅ 无效detail测试通过: " + result.toJSONString());
    }
}
