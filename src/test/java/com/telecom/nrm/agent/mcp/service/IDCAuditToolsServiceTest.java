package com.telecom.nrm.agent.mcp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.mcp.exception.MCPToolException;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 IDCAuditToolsService 中的工具方法
 */
@SpringBootTest
public class IDCAuditToolsServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(IDCAuditToolsServiceTest.class);

    @Autowired
    @Qualifier("idcAuditToolsService")
    private IMCPToolService idcAuditToolsService;

    /**
     * 测试 sql_query 工具方法
     * 该方法测试执行一个简单的 SQL 查询，并验证返回结果
     */
    @Test
    public void testSqlQuery() {
        // 准备测试数据
        String sql = "SELECT t.dev_code AS 设备编码,t.port_name AS 端口名称,MIN(t.shijian::timestamp) AS 最早异常时间,MAX(t.shijian::timestamp) AS 最近异常时间,MAX(t.shijian::timestamp)-MIN(t.shijian::timestamp) AS 持续时间,MAX(t.out_val + t.in_val) AS 最大流速_bps,MIN(t.out_val + t.in_val) AS 最小流速_bps FROM port_abnormal_traffic t WHERE t.abnormal_type='有资源异常' GROUP BY t.dev_code,t.port_name ORDER BY 最大流速_bps DESC LIMIT 20";
        Map<String, Object> params = new HashMap<>();
        params.put("sql", sql);

        try {
            // 调用 sql_query 工具
            Object result = idcAuditToolsService.invokeTool("sql_query", params);
            
            // 验证结果
            assertNotNull(result, "查询结果不应为空");
            assertTrue(result instanceof JSONArray, "结果应该是 JSONArray 类型");
            
            JSONArray jsonArray = (JSONArray) result;
            logger.info("查询结果: {}", jsonArray);
            
            // 打印查询结果的详细信息
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);
                logger.info("记录 {}: {}", i + 1, item);
            }
            
        } catch (MCPToolException e) {
            logger.error("执行 SQL 查询失败: {}", e.getMessage(), e);
            fail("执行 SQL 查询时发生异常: " + e.getMessage());
        }
    }

    /**
     * 测试 sql_query 工具方法 - 空 SQL 查询
     * 该方法测试当传入空 SQL 查询时的行为
     */
    @Test
    public void testSqlQueryWithEmptySql() {
        // 准备测试数据 - 空 SQL
        Map<String, Object> params = new HashMap<>();
        params.put("sql", "");

        try {
            // 调用 sql_query 工具
            Object result = idcAuditToolsService.invokeTool("sql_query", params);
            
            // 验证结果
            assertNotNull(result, "即使 SQL 为空，结果也不应为 null");
            assertTrue(result instanceof JSONArray, "结果应该是 JSONArray 类型");
            
            JSONArray jsonArray = (JSONArray) result;
            assertEquals(0, jsonArray.size(), "空 SQL 应返回空数组");
            
        } catch (MCPToolException e) {
            logger.error("执行空 SQL 查询失败: {}", e.getMessage(), e);
            fail("执行空 SQL 查询时发生异常: " + e.getMessage());
        }
    }

    /**
     * 测试 sql_query 工具方法 - 无效 SQL 查询
     * 该方法测试当传入无效 SQL 查询时的行为
     */
    @Test
    public void testSqlQueryWithInvalidSql() {
        // 准备测试数据 - 无效 SQL
        String invalidSql = "SELECT * FROM non_existent_table";
        Map<String, Object> params = new HashMap<>();
        params.put("sql", invalidSql);

        try {
            // 调用 sql_query 工具
            Object result = idcAuditToolsService.invokeTool("sql_query", params);
            
            // 验证结果
            assertNotNull(result, "即使 SQL 无效，结果也不应为 null");
            assertTrue(result instanceof JSONArray, "结果应该是 JSONArray 类型");
            
            JSONArray jsonArray = (JSONArray) result;
            logger.info("无效 SQL 查询结果: {}", jsonArray);
            
            // 注意：这里我们不断言数组大小，因为具体行为取决于数据库实现
            // 有些数据库会返回错误，有些可能返回空结果集
            
        } catch (MCPToolException e) {
            // 如果抛出异常，记录但不失败测试，因为这可能是预期行为
            logger.info("执行无效 SQL 查询时抛出异常（可能是预期行为）: {}", e.getMessage());
        }
    }

    /**
     * 测试 sql_query 工具方法 - 复杂 SQL 查询
     * 该方法测试执行一个包含 JOIN 和条件的复杂 SQL 查询
     */
    @Test
    public void testSqlQueryWithComplexQuery() {
        // 准备测试数据 - 复杂 SQL
        String complexSql = "SELECT a.id, a.risk, a.risk_analysis, a.risk_level, a.risk_desc " +
                           "FROM idc_audit_risk_dictvalue a " +
                           "WHERE a.risk_level IN ('高', '中') " +
                           "ORDER BY a.id " +
                           "LIMIT 10";
        Map<String, Object> params = new HashMap<>();
        params.put("sql", complexSql);

        try {
            // 调用 sql_query 工具
            Object result = idcAuditToolsService.invokeTool("sql_query", params);
            
            // 验证结果
            assertNotNull(result, "查询结果不应为空");
            assertTrue(result instanceof JSONArray, "结果应该是 JSONArray 类型");
            
            JSONArray jsonArray = (JSONArray) result;
            logger.info("复杂 SQL 查询结果: {}", jsonArray);
            
            // 打印查询结果的详细信息
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);
                logger.info("记录 {}: {}", i + 1, item);
                
                // 验证返回的记录包含预期的字段
                assertTrue(item.containsKey("id"), "记录应包含 id 字段");
                assertTrue(item.containsKey("risk"), "记录应包含 risk 字段");
                assertTrue(item.containsKey("risk_analysis"), "记录应包含 risk_analysis 字段");
                assertTrue(item.containsKey("risk_level"), "记录应包含 risk_level 字段");
                assertTrue(item.containsKey("risk_desc"), "记录应包含 risk_desc 字段");
                
                // 验证风险等级符合查询条件
                String riskLevel = item.getString("risk_level");
                assertTrue(riskLevel.equals("高") || riskLevel.equals("中"), 
                           "风险等级应为 '高' 或 '中'");
            }
            
        } catch (MCPToolException e) {
            logger.error("执行复杂 SQL 查询失败: {}", e.getMessage(), e);
            fail("执行复杂 SQL 查询时发生异常: " + e.getMessage());
        }
    }
}
