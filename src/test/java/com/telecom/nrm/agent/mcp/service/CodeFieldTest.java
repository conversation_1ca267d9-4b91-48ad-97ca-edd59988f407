package com.telecom.nrm.agent.mcp.service;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试code字段的处理
 */
public class CodeFieldTest {

    @Test
    public void testCodeFieldAddedWhenMissing() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试原始JSON中没有code字段的情况
        String message = "测试没有code字段的detail：<detail>\n" +
                "{\"deviceIp\":\"*************\"," +
                "\"portName\":\"GE0/0/1\"," +
                "\"userName\":\"张三\"," +
                "\"startDate\":\"2025-05-06 00:00:00\"," +
                "\"endDate\":\"2025-06-06 00:00:00\"," +
                "\"regionNames\":[\"南京\",\"苏州\"]}\n" +
                "</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该能够提取到detail内容");
        
        // 验证code字段被自动添加
        assertEquals("0", result.getString("code"), "即使原始JSON没有code字段，也应该自动添加code=0");
        
        // 验证其他字段正常
        assertEquals("*************", result.getString("deviceIp"));
        assertEquals("GE0/0/1", result.getString("portName"));
        assertEquals("张三", result.getString("userName"));
        assertEquals("2025-05-06 00:00:00", result.getString("startDate"));
        assertEquals("2025-06-06 00:00:00", result.getString("endDate"));
        assertTrue(result.getJSONArray("regionNames").contains("南京"));
        assertTrue(result.getJSONArray("regionNames").contains("苏州"));

        System.out.println("✅ 自动添加code字段测试通过");
        System.out.println("提取的结果: " + result.toJSONString());
    }

    @Test
    public void testCodeFieldOverwrittenWhenExists() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试原始JSON中已有code字段的情况（应该被覆盖为"0"）
        String message = "测试已有code字段的detail：<detail>\n" +
                "{\"deviceIp\":\"*************\"," +
                "\"portName\":\"GE0/0/1\"," +
                "\"userName\":\"张三\"," +
                "\"code\":\"1\"," +  // 原始code为"1"
                "\"startDate\":\"2025-05-06 00:00:00\"," +
                "\"endDate\":\"2025-06-06 00:00:00\"," +
                "\"regionNames\":[\"南京\",\"苏州\"]}\n" +
                "</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该能够提取到detail内容");
        
        // 验证code字段被覆盖为"0"
        assertEquals("0", result.getString("code"), "原始JSON中的code字段应该被覆盖为0");
        
        // 验证其他字段正常
        assertEquals("*************", result.getString("deviceIp"));
        assertEquals("GE0/0/1", result.getString("portName"));
        assertEquals("张三", result.getString("userName"));

        System.out.println("✅ 覆盖code字段测试通过");
        System.out.println("提取的结果: " + result.toJSONString());
    }

    @Test
    public void testCodeFieldInFailureCase() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试失败情况的code字段
        String message = "包含无效JSON的消息。<detail>{invalid json}</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该返回错误格式");
        assertEquals("-1", result.getString("code"), "失败时code应该为-1");
        assertTrue(result.containsKey("msg"), "失败时应该包含msg字段");
        
        System.out.println("✅ 失败情况code字段测试通过");
        System.out.println("错误结果: " + result.toJSONString());
    }

    @Test
    public void testEmptyDetailCodeField() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试空detail的code字段
        String message = "空detail测试：<detail></detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该返回错误格式");
        assertEquals("-1", result.getString("code"), "空detail时code应该为-1");
        assertEquals("detail标签内容为空", result.getString("msg"));
        
        System.out.println("✅ 空detail的code字段测试通过");
        System.out.println("错误结果: " + result.toJSONString());
    }

    @Test
    public void testNoDetailTagCodeField() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试没有detail标签的code字段
        String message = "没有detail标签的普通消息";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该返回错误格式");
        assertEquals("-1", result.getString("code"), "没有detail标签时code应该为-1");
        assertEquals("未找到完整的detail标签", result.getString("msg"));
        
        System.out.println("✅ 无detail标签的code字段测试通过");
        System.out.println("错误结果: " + result.toJSONString());
    }

    @Test
    public void testMinimalJsonWithCodeField() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试最小JSON（只有一个字段）的code字段处理
        String message = "最小JSON测试：<detail>{\"deviceIp\":\"********\"}</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该能够提取到detail内容");
        assertEquals("0", result.getString("code"), "最小JSON也应该自动添加code=0");
        assertEquals("********", result.getString("deviceIp"));
        
        System.out.println("✅ 最小JSON的code字段测试通过");
        System.out.println("提取的结果: " + result.toJSONString());
    }
}
