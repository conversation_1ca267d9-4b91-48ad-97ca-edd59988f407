package com.telecom.nrm.agent.mcp.service;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试detail提取的具体格式要求
 */
public class DetailExtractionFormatTest {

    @Test
    public void testExpectedFormatWithAllFields() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试包含所有字段的detail（包括中英文字段）
        String message = "根据您的查询需求，我已经为您生成了审计查询参数。<detail>\n" +
                "{\"deviceIp\":\"*************\"," +
                "\"regionNames\":[\"南京\",\"徐州\",\"无锡\",\"连云港\",\"宿迁\",\"苏州\",\"常州\",\"镇江\",\"盐城\"]," +
                "\"code\":\"0\"," +
                "\"开始时间\":\"2025-05-06 00:00:00\"," +
                "\"设备IP\":\"*************\"," +
                "\"endDate\":\"2025-06-06 00:00:00\"," +
                "\"portName\":\"GE0/0/1\"," +
                "\"userName\":\"张三\"," +
                "\"端口名称\":\"GE0/0/1\"," +
                "\"截至时间\":\"2025-06-06 00:00:00\"," +
                "\"地市\":[\"南京\",\"徐州\",\"无锡\",\"连云港\",\"宿迁\",\"苏州\",\"常州\",\"镇江\",\"盐城\"]," +
                "\"用户名\":\"张三\"," +
                "\"startDate\":\"2025-05-06 00:00:00\"}\n" +
                "</detail>请查看详情界面。";

        // 使用反射调用私有方法
        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        // 验证结果
        assertNotNull(result, "应该能够提取到detail内容");
        
        // 验证英文字段
        assertEquals("*************", result.getString("deviceIp"));
        assertEquals("GE0/0/1", result.getString("portName"));
        assertEquals("张三", result.getString("userName"));
        assertEquals("2025-05-06 00:00:00", result.getString("startDate"));
        assertEquals("2025-06-06 00:00:00", result.getString("endDate"));
        assertEquals("0", result.getString("code"));
        
        // 验证中文字段
        assertEquals("2025-05-06 00:00:00", result.getString("开始时间"));
        assertEquals("*************", result.getString("设备IP"));
        assertEquals("GE0/0/1", result.getString("端口名称"));
        assertEquals("2025-06-06 00:00:00", result.getString("截至时间"));
        assertEquals("张三", result.getString("用户名"));
        
        // 验证数组字段
        assertTrue(result.getJSONArray("regionNames").contains("南京"));
        assertTrue(result.getJSONArray("regionNames").contains("徐州"));
        assertTrue(result.getJSONArray("regionNames").contains("无锡"));
        assertEquals(9, result.getJSONArray("regionNames").size());
        
        assertTrue(result.getJSONArray("地市").contains("南京"));
        assertTrue(result.getJSONArray("地市").contains("徐州"));
        assertTrue(result.getJSONArray("地市").contains("无锡"));
        assertEquals(9, result.getJSONArray("地市").size());

        System.out.println("✅ 完整格式测试通过");
        System.out.println("提取的结果: " + result.toJSONString());
        
        // 验证结果包含所有必要字段
        assertTrue(result.containsKey("deviceIp"), "应包含deviceIp字段");
        assertTrue(result.containsKey("设备IP"), "应包含设备IP字段");
        assertTrue(result.containsKey("portName"), "应包含portName字段");
        assertTrue(result.containsKey("端口名称"), "应包含端口名称字段");
        assertTrue(result.containsKey("userName"), "应包含userName字段");
        assertTrue(result.containsKey("用户名"), "应包含用户名字段");
        assertTrue(result.containsKey("startDate"), "应包含startDate字段");
        assertTrue(result.containsKey("开始时间"), "应包含开始时间字段");
        assertTrue(result.containsKey("endDate"), "应包含endDate字段");
        assertTrue(result.containsKey("截至时间"), "应包含截至时间字段");
        assertTrue(result.containsKey("regionNames"), "应包含regionNames字段");
        assertTrue(result.containsKey("地市"), "应包含地市字段");
        assertTrue(result.containsKey("code"), "应包含code字段");
    }

    @Test
    public void testFailureFormat() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试解析失败的情况
        String message = "包含无效JSON的消息。<detail>{invalid json content}</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该返回错误格式");
        assertEquals("-1", result.getString("code"), "失败时code应该为-1");
        assertTrue(result.containsKey("msg"), "失败时应该包含msg字段");
        assertTrue(result.getString("msg").contains("JSON解析失败"), "msg应该包含错误描述");
        
        System.out.println("✅ 失败格式测试通过: " + result.toJSONString());
    }

    @Test
    public void testEmptyFieldsFormat() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试包含空字段的detail
        String message = "包含空字段的detail：<detail>\n" +
                "{\"deviceIp\":\"\"," +
                "\"regionNames\":[\"南京\",\"徐州\",\"无锡\",\"连云港\",\"宿迁\",\"苏州\",\"常州\",\"镇江\",\"盐城\"]," +
                "\"code\":\"0\"," +
                "\"开始时间\":\"2025-05-06 00:00:00\"," +
                "\"设备IP\":\"\"," +
                "\"endDate\":\"2025-06-06 00:00:00\"," +
                "\"portName\":\"\"," +
                "\"userName\":\"\"," +
                "\"端口名称\":\"\"," +
                "\"截至时间\":\"2025-06-06 00:00:00\"," +
                "\"地市\":[\"南京\",\"徐州\",\"无锡\",\"连云港\",\"宿迁\",\"苏州\",\"常州\",\"镇江\",\"盐城\"]," +
                "\"用户名\":\"\"," +
                "\"startDate\":\"2025-05-06 00:00:00\"}\n" +
                "</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该能够提取到detail内容");
        
        // 验证空字段
        assertEquals("", result.getString("deviceIp"));
        assertEquals("", result.getString("设备IP"));
        assertEquals("", result.getString("portName"));
        assertEquals("", result.getString("端口名称"));
        assertEquals("", result.getString("userName"));
        assertEquals("", result.getString("用户名"));
        
        // 验证非空字段
        assertEquals("0", result.getString("code"));
        assertEquals("2025-05-06 00:00:00", result.getString("startDate"));
        assertEquals("2025-05-06 00:00:00", result.getString("开始时间"));
        assertEquals("2025-06-06 00:00:00", result.getString("endDate"));
        assertEquals("2025-06-06 00:00:00", result.getString("截至时间"));
        
        // 验证数组字段
        assertEquals(9, result.getJSONArray("regionNames").size());
        assertEquals(9, result.getJSONArray("地市").size());

        System.out.println("✅ 空字段格式测试通过");
        System.out.println("提取的结果: " + result.toJSONString());
    }

    @Test
    public void testMinimalValidFormat() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 测试最小有效格式
        String message = "最小格式：<detail>{\"code\":\"0\"}</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result, "应该能够提取到detail内容");
        assertEquals("0", result.getString("code"));
        
        System.out.println("✅ 最小格式测试通过: " + result.toJSONString());
    }
}
