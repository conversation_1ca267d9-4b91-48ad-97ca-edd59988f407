package com.telecom.nrm.agent.mcp.service;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 高级detail提取功能测试
 */
public class DetailExtractionAdvancedTest {

    @Test
    public void testEmptyMessage() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", "");

        assertNotNull(result);
        assertEquals("-1", result.getString("code"));
        assertEquals("消息为空", result.getString("msg"));
        System.out.println("✅ 空消息测试通过: " + result.toJSONString());
    }

    @Test
    public void testNullMessage() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", (String) null);

        assertNotNull(result);
        assertEquals("-1", result.getString("code"));
        assertEquals("消息为空", result.getString("msg"));
        System.out.println("✅ null消息测试通过: " + result.toJSONString());
    }

    @Test
    public void testEmptyDetail() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        String message = "包含空detail标签。<detail></detail>";
        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result);
        assertEquals("-1", result.getString("code"));
        assertEquals("detail标签内容为空", result.getString("msg"));
        System.out.println("✅ 空detail测试通过: " + result.toJSONString());
    }

    @Test
    public void testDetailWithWhitespace() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        String message = "包含空白字符的detail。<detail>   \n\t   </detail>";
        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result);
        assertEquals("-1", result.getString("code"));
        assertEquals("detail标签内容为空", result.getString("msg"));
        System.out.println("✅ 空白字符detail测试通过: " + result.toJSONString());
    }

    @Test
    public void testIncompleteDetailTags() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 只有开始标签
        String message1 = "只有开始标签。<detail>{\"code\":\"0\"}";
        JSONObject result1 = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message1);
        
        assertEquals("-1", result1.getString("code"));
        assertEquals("未找到完整的detail标签", result1.getString("msg"));
        
        // 只有结束标签
        String message2 = "只有结束标签。{\"code\":\"0\"}</detail>";
        JSONObject result2 = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message2);
        
        assertEquals("-1", result2.getString("code"));
        assertEquals("未找到完整的detail标签", result2.getString("msg"));
        
        System.out.println("✅ 不完整标签测试通过");
    }

    @Test
    public void testMultipleDetailTags() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        String message = "第一个detail：<detail>{\"deviceIp\":\"***********\",\"code\":\"0\"}</detail>" +
                        "第二个detail：<detail>{\"deviceIp\":\"***********\",\"code\":\"1\"}</detail>";
        
        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result);
        assertEquals("0", result.getString("code"));
        
        JSONObject msgData = result.getJSONObject("msg");
        assertEquals("***********", msgData.getString("deviceIp"));
        assertEquals("0", msgData.getString("code"));
        
        System.out.println("✅ 多个detail标签测试通过（提取第一个）: " + result.toJSONString());
    }

    @Test
    public void testComplexJsonInDetail() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        String message = "复杂JSON测试：<detail>" +
                "{\"deviceIp\":\"************\"," +
                "\"portName\":\"GE2/0/24\"," +
                "\"userName\":\"管理员\"," +
                "\"startDate\":\"2025-01-01 00:00:00\"," +
                "\"endDate\":\"2025-12-31 23:59:59\"," +
                "\"regionNames\":[\"南京\",\"苏州\",\"无锡\"]," +
                "\"code\":\"0\"," +
                "\"extra\":{\"nested\":\"value\",\"array\":[1,2,3]}}" +
                "</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result);
        assertEquals("0", result.getString("code"));
        
        JSONObject msgData = result.getJSONObject("msg");
        assertEquals("************", msgData.getString("deviceIp"));
        assertEquals("GE2/0/24", msgData.getString("portName"));
        assertEquals("管理员", msgData.getString("userName"));
        assertEquals("2025-01-01 00:00:00", msgData.getString("startDate"));
        assertEquals("2025-12-31 23:59:59", msgData.getString("endDate"));
        assertEquals("0", msgData.getString("code"));
        
        // 验证数组
        assertTrue(msgData.getJSONArray("regionNames").contains("南京"));
        assertTrue(msgData.getJSONArray("regionNames").contains("苏州"));
        assertTrue(msgData.getJSONArray("regionNames").contains("无锡"));
        
        // 验证嵌套对象
        assertNotNull(msgData.getJSONObject("extra"));
        assertEquals("value", msgData.getJSONObject("extra").getString("nested"));
        assertEquals(3, msgData.getJSONObject("extra").getJSONArray("array").size());

        System.out.println("✅ 复杂JSON测试通过: " + result.toJSONString());
    }

    @Test
    public void testDetailWithNewlines() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        String message = "包含换行符的detail：<detail>\n" +
                "{\n" +
                "  \"deviceIp\": \"********\",\n" +
                "  \"portName\": \"GE1/0/1\",\n" +
                "  \"code\": \"0\"\n" +
                "}\n" +
                "</detail>";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result);
        assertEquals("0", result.getString("code"));
        
        JSONObject msgData = result.getJSONObject("msg");
        assertEquals("********", msgData.getString("deviceIp"));
        assertEquals("GE1/0/1", msgData.getString("portName"));
        assertEquals("0", msgData.getString("code"));

        System.out.println("✅ 换行符detail测试通过: " + result.toJSONString());
    }

    @Test
    public void testRealWorldExample() {
        IDCAuditAgentService service = new IDCAuditAgentService();
        
        // 模拟真实场景的消息
        String message = "根据您的查询需求，我已经为您生成了审计查询参数。请查看以下详细信息：\n\n" +
                "设备IP: *************\n" +
                "端口: GE0/0/1\n" +
                "用户: 张三\n" +
                "时间范围: 2025-05-05 到 2025-06-05\n" +
                "地区: 南京\n\n" +
                "<detail>\n" +
                "{\"deviceIp\":\"*************\",\"portName\":\"GE0/0/1\",\"userName\":\"张三\",\"startDate\":\"2025-05-05 00:00:00\",\"endDate\":\"2025-06-05 23:59:59\",\"regionNames\":[\"南京\"],\"code\":\"0\"}\n" +
                "</detail>\n\n" +
                "请在详情界面查看具体的审计记录。";

        JSONObject result = (JSONObject) ReflectionTestUtils.invokeMethod(
                service, "extractDetailFromMessage", message);

        assertNotNull(result);
        assertEquals("0", result.getString("code"));
        
        JSONObject msgData = result.getJSONObject("msg");
        assertEquals("*************", msgData.getString("deviceIp"));
        assertEquals("GE0/0/1", msgData.getString("portName"));
        assertEquals("张三", msgData.getString("userName"));
        assertEquals("2025-05-05 00:00:00", msgData.getString("startDate"));
        assertEquals("2025-06-05 23:59:59", msgData.getString("endDate"));
        assertEquals("0", msgData.getString("code"));
        assertTrue(msgData.getJSONArray("regionNames").contains("南京"));

        System.out.println("✅ 真实场景测试通过");
        System.out.println("提取结果: " + result.toJSONString());
    }
}
