package com.telecom.nrm.agent.chart;

import com.alibaba.fastjson.JSON;
import com.telecom.nrm.agent.chart.model.ChartData;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * 测试Base64解码
 */
public class Base64DecodingTest {

    private static final Logger logger = LoggerFactory.getLogger(Base64DecodingTest.class);

    @Test
    public void testBase64Decoding() {
        // 您提供的Base64字符串
        String base64Data = "ewogICJ0eXBlIjogImJhciIsCiAgInRpdGxlIjogIuaXpeacrOW5tOe7hOWkjeWQjOaVtOS4quWkhOeQhiIsCiAgInhBeGlzTGFiZWwiOiAi5Zyw5Z2AIiwKICAieUF4aXNMYWJlbCI6ICLmlbTnm5giLAogICJ3aWR0aCI6IDgwMCwKICAiaGVpZ2h0IjogNjAwLAogICJzZXJpZXMiOiBbCiAgICB7CiAgICAgICJuYW1lIjogIue7hOWkjeWQjCIsCiAgICAgICJkYXRhIjogWzExLCAxMCwgMSwgMV0sCiAgICAgICJsYWJlbHMiOiBbIua0i+WtkCIsICLmnY7lpKQiLCAi5peg5rC0IiwgIumHkea3kSJdLAogICAgICAiY29sb3IiOiAiIzQxNjlFMSIKICAgIH0KICBdCn0=";

        try {
            // 使用Apache Commons Codec解码
            byte[] decodedBytes = Base64.decodeBase64(base64Data);
            String jsonData = new String(decodedBytes, StandardCharsets.UTF_8);
            
            logger.info("解码后的JSON数据: {}", jsonData);
            
            // 尝试解析为ChartData对象
            ChartData chartData = JSON.parseObject(jsonData, ChartData.class);
            
            logger.info("解析成功! 图表类型: {}, 标题: {}", chartData.getType(), chartData.getTitle());
            logger.info("系列数量: {}", chartData.getSeries().size());
            
            // 尝试使用Java 8内置的Base64解码器
            byte[] javaDecodedBytes = java.util.Base64.getDecoder().decode(base64Data);
            String javaJsonData = new String(javaDecodedBytes, StandardCharsets.UTF_8);
            
            logger.info("Java 8 Base64解码后的JSON数据: {}", javaJsonData);
            
        } catch (Exception e) {
            logger.error("解码或解析失败", e);
        }
    }
}
