<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration PUBLIC "-//APACHE//DTD LOG4J 1.2//EN" "log4j.dtd">

<!-- by wuchangzheng -->

<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

	<!-- Appenders -->
	<appender name="console" class="org.apache.log4j.ConsoleAppender">
		<param name="Target" value="System.out" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%-5p: %d{yyyy/MM/dd HH:mm:ss,SSS} %c - %m%n" />
		</layout>
	</appender>

	<appender name="file" class="org.apache.log4j.FileAppender">
		<param name="File" value="logs/log.log" />
		<param name="Encoding" value="UTF-8" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%-5p: %d{yyyy/MM/dd HH:mm:ss,SSS} %c - %m%n" />
		</layout>
	</appender>

	<!-- Application Loggers -->
	<logger name="com.telecom">
		<level value="warn" />
	</logger>

	<logger name="com.telecom.da.client">
		<level value="warn" />
	</logger>
	
	<logger name="common">
		<level value="debug" />
	</logger>


	<logger name="com.alibaba">
		<level value="debug" />
	</logger>


	<logger name="org">
		<level value="debug" />
	</logger>
	
	<logger name="java">
		<level value="debug" />
	</logger>
	
	

	<!-- Root Logger -->
	<root>
		<priority value="info" />
		<appender-ref ref="console" />
	</root>

</log4j:configuration>
