<?xml version="1.0" encoding="UTF-8" ?>
<!-- by wuchangzheng -->
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<!-- Authors: <AUTHORS>

<!-- Version: 1.2 -->

<!-- A configuration element consists of optional renderer
elements,appender elements, categories and an optional root
element. -->

<!ELEMENT log4j:configuration (renderer*, throwableRenderer?,
                               appender*,plugin*, (category|logger)*,root?,
                               (categoryFactory|loggerFactory)?)>

<!-- The "threshold" attribute takes a level value below which -->
<!-- all logging statements are disabled. -->

<!-- Setting the "debug" enable the printing of internal log4j logging   -->
<!-- statements.                                                         -->

<!-- By default, debug attribute is "null", meaning that we not do touch -->
<!-- internal log4j logging settings. The "null" value for the threshold -->
<!-- attribute can be misleading. The threshold field of a repository	 -->
<!-- cannot be set to null. The "null" value for the threshold attribute -->
<!-- simply means don't touch the threshold field, the threshold field   --> 
<!-- keeps its old value.                                                -->
     
<!ATTLIST log4j:configuration
  xmlns:log4j              CDATA #FIXED "http://jakarta.apache.org/log4j/" 
  threshold                (all|trace|debug|info|warn|error|fatal|off|null) "null"
  debug                    (true|false|null)  "null"
  reset                    (true|false) "false"
>

<!-- renderer elements allow the user to customize the conversion of  -->
<!-- message objects to String.                                       -->

<!ELEMENT renderer EMPTY>
<!ATTLIST renderer
  renderedClass  CDATA #REQUIRED
  renderingClass CDATA #REQUIRED
>

<!--  throwableRenderer allows the user to customize the conversion
         of exceptions to a string representation.  -->
<!ELEMENT throwableRenderer (param*)>
<!ATTLIST throwableRenderer
  class  CDATA #REQUIRED
>


<!-- Appenders must have a name and a class. -->
<!-- Appenders may contain an error handler, a layout, optional parameters -->
<!-- and filters. They may also reference (or include) other appenders. -->
<!ELEMENT appender (errorHandler?, param*,
      rollingPolicy?, triggeringPolicy?, connectionSource?,
      layout?, filter*, appender-ref*)>
<!ATTLIST appender
  name 		CDATA 	#REQUIRED
  class 	CDATA	#REQUIRED
>

<!ELEMENT layout (param*)>
<!ATTLIST layout
  class		CDATA	#REQUIRED
>

<!ELEMENT filter (param*)>
<!ATTLIST filter
  class		CDATA	#REQUIRED
>

<!-- ErrorHandlers can be of any class. They can admit any number of -->
<!-- parameters. -->

<!ELEMENT errorHandler (param*, root-ref?, logger-ref*,  appender-ref?)> 
<!ATTLIST errorHandler
   class        CDATA   #REQUIRED 
>

<!ELEMENT root-ref EMPTY>

<!ELEMENT logger-ref EMPTY>
<!ATTLIST logger-ref
  ref CDATA #REQUIRED
>

<!ELEMENT param EMPTY>
<!ATTLIST param
  name		CDATA   #REQUIRED
  value		CDATA	#REQUIRED
>


<!-- The priority class is org.apache.log4j.Level by default -->
<!ELEMENT priority (param*)>
<!ATTLIST priority
  class   CDATA	#IMPLIED
  value	  CDATA #REQUIRED
>

<!-- The level class is org.apache.log4j.Level by default -->
<!ELEMENT level (param*)>
<!ATTLIST level
  class   CDATA	#IMPLIED
  value	  CDATA #REQUIRED
>


<!-- If no level element is specified, then the configurator MUST not -->
<!-- touch the level of the named category. -->
<!ELEMENT category (param*,(priority|level)?,appender-ref*)>
<!ATTLIST category
  class         CDATA   #IMPLIED
  name		CDATA	#REQUIRED
  additivity	(true|false) "true"  
>

<!-- If no level element is specified, then the configurator MUST not -->
<!-- touch the level of the named logger. -->
<!ELEMENT logger (param*,level?,appender-ref*)>
<!ATTLIST logger
  class         CDATA   #IMPLIED
  name		CDATA	#REQUIRED
  additivity	(true|false) "true"  
>


<!ELEMENT categoryFactory (param*)>
<!ATTLIST categoryFactory 
   class        CDATA #REQUIRED>

<!ELEMENT loggerFactory (param*)>
<!ATTLIST loggerFactory
   class        CDATA #REQUIRED>

<!ELEMENT appender-ref EMPTY>
<!ATTLIST appender-ref
  ref CDATA #REQUIRED
>

<!-- plugins must have a name and class and can have optional parameters -->
<!ELEMENT plugin (param*, connectionSource?)>
<!ATTLIST plugin
  name 		CDATA 	   #REQUIRED
  class 	CDATA  #REQUIRED
>

<!ELEMENT connectionSource (dataSource?, param*)>
<!ATTLIST connectionSource
  class        CDATA  #REQUIRED
>

<!ELEMENT dataSource (param*)>
<!ATTLIST dataSource
  class        CDATA  #REQUIRED
>

<!ELEMENT triggeringPolicy ((param|filter)*)>
<!ATTLIST triggeringPolicy
  name 		CDATA  #IMPLIED
  class 	CDATA  #REQUIRED
>

<!ELEMENT rollingPolicy (param*)>
<!ATTLIST rollingPolicy
  name 		CDATA  #IMPLIED
  class 	CDATA  #REQUIRED
>


<!-- If no priority element is specified, then the configurator MUST not -->
<!-- touch the priority of root. -->
<!-- The root category always exists and cannot be subclassed. -->
<!ELEMENT root (param*, (priority|level)?, appender-ref*)>


<!-- ==================================================================== -->
<!--                       A logging event                                -->
<!-- ==================================================================== -->
<!ELEMENT log4j:eventSet (log4j:event*)>
<!ATTLIST log4j:eventSet
  xmlns:log4j             CDATA #FIXED "http://jakarta.apache.org/log4j/" 
  version                (1.1|1.2) "1.2" 
  includesLocationInfo   (true|false) "true"
>



<!ELEMENT log4j:event (log4j:message, log4j:NDC?, log4j:throwable?, 
                       log4j:locationInfo?, log4j:properties?) >

<!-- The timestamp format is application dependent. -->
<!ATTLIST log4j:event
    logger     CDATA #REQUIRED
    level      CDATA #REQUIRED
    thread     CDATA #REQUIRED
    timestamp  CDATA #REQUIRED
    time       CDATA #IMPLIED
>

<!ELEMENT log4j:message (#PCDATA)>
<!ELEMENT log4j:NDC (#PCDATA)>

<!ELEMENT log4j:throwable (#PCDATA)>

<!ELEMENT log4j:locationInfo EMPTY>
<!ATTLIST log4j:locationInfo
  class  CDATA	#REQUIRED
  method CDATA	#REQUIRED
  file   CDATA	#REQUIRED
  line   CDATA	#REQUIRED
>

<!ELEMENT log4j:properties (log4j:data*)>

<!ELEMENT log4j:data EMPTY>
<!ATTLIST log4j:data
  name   CDATA	#REQUIRED
  value  CDATA	#REQUIRED
>
