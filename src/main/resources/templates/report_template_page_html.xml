<html>
    <head>
        <style>
            body { line-height:1.5; font-family: 'Microsoft YaHei', sans-serif;}
            h1,h2,h3,h4,h5,h6,p,ul,li,div{ margin:0 0 !important;line-height: 40px !important; }
            table { border-collapse: collapse; width: 100%; margin: 10px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            span {font-weight: bold;}
            ul { list-style-type: "• " !important; }
            .center {text-align:center}
            tr:nth-child(even) { background-color: #f2f2f2;    }
        </style>
    </head>
    <body>
        <h1 class="center" >IDC智审稽核报告</h1>
        <p class="center"> <strong>报告时间</strong>：${reportDate}</p>
        <br/>
        <h1>一&nbsp;本次稽核发现风险</h1>
        <h2>1.1&nbsp;概述</h2>
        <p><strong>稽核时间范围：</strong> ${startDate} ~ ${endDate}</p>
        <p><strong>端口操作稽核：</strong> 总操作${currentCycle.portAudit.total}次，其中存在风险${currentCycle.portAudit.risk}次。涉及地市：<#if currentCycle.portAudit.citys?has_content>${currentCycle.portAudit.citys}。风险类型分布如下：<#else>无</#if></p>
        <ul>
            <#list currentCycle.portAudit.riskCatalogs as riskCatalog>
                <li><strong>${riskCatalog.title}：</strong> ${riskCatalog.count}次，占比${riskCatalog.percent}</li>
            </#list>
        </ul>

        <h2>1.2&nbsp;稽核情况</h2>
        <h3>1.2.1&nbsp;端口操作风险分析</h3>
        <!--只展示有风险-->
        <#list currentCycle.portAudit.hasRiskCatalogs as riskCatalog>
                <h4>${riskCatalog.title}</h4>
                <ul>
                    <li><strong>数量：</strong> ${riskCatalog.count} </li>
                    <li><strong>现象：</strong> ${riskCatalog.description}</li>
                    <li><strong>稽核：</strong> ${riskCatalog.checkRule}</li>
                    <li><strong>示例：</strong>
                        <ul>
                            <li>时间：${riskCatalog.exampleOperateTime}</li>
                            <li>设备：${riskCatalog.exampleDeviceIp}</li>
                            <li>端口：${riskCatalog.examplePortName}</li>
                            <li>操作：${riskCatalog.exampleAction}</li>
                            <li>操作人员：${riskCatalog.exampleOperationName}</li>
                            <li>操作部门：${riskCatalog.exampleOperationDept}</li>
                        </ul>
                    </li>
                    <li><strong>建议措施：</strong> ${riskCatalog.suggestion}</li>
                </ul>
        </#list>

        <h1>二&nbsp;上线以来累计情况</h1>
        <h2>2.1 &nbsp;概述</h2>
        <p><strong>时间范围：</strong> ${totalCycle.startDate} ~ ${totalCycle.endDate}</p>
        <h3>2.1.1 &nbsp;当前IDC系统未关闭的风险</h3>
        <p><strong>端口操作复核：</strong> 复核发现风险操作${totalCycle.portAudit.reviewRisk}次。涉及地市：${totalCycle.portReviewAudit.citys}风险类型分布如下：</p>
        <table>
            <thead>
                <tr>
                    <th>地市</th>
                    <th>风险类型</th>
                    <th>数量</th>
                    <th>样例</th>
                </tr>
            </thead>
            <tbody>
                <#list totalCycle.portReviewAudit.hasRiskCatalogs as riskCatalog>
                    <tr>
                        <td>${riskCatalog.city}</td>
                        <td>${riskCatalog.riskAnalysis}</td>
                        <td>${riskCatalog.count}</td>
                        <td>${riskCatalog.description}</td>
                    </tr>
            </#list>
            </tbody>
        </table>

        <h3>2.1.2 &nbsp;全量风险统计</h3>
        <p><strong>端口操作稽核：</strong> 总操作${totalCycle.portAudit.total}次，其中首次稽核发现风险${totalCycle.portAudit.risk}次。涉及地市：${totalCycle.portAudit.citys}风险类型分布如下：</p>
        <ul>
            <#list totalCycle.portAudit.riskCatalogs as riskCatalog>
            <li><strong>${riskCatalog.title}：</strong> ${riskCatalog.count}次，占比${riskCatalog.percent}</li>
            </#list>
        </ul>

        <h2>2.2&nbsp;整改进度</h2>
        <h3>2.2.1 &nbsp;端口操作风险整改</h3>
        <table>
            <thead>
                <tr>
                    <th>地市</th>
                    <th>累计不按单操作数</th>
                    <th>累计风险消除数</th>
                    <th>累计风险消除率</th>
                    <th>补开通单消除数量</th>
                    <th>补割接单消除数量</th>
                    <th>补中继单消除数量</th>
                    <th>关闭端口消除数量</th>
                    <th>无需复核消除数量</th>
                    <th>派单数量</th>
                    <th>超时未回单数量</th>
                    <th>完成整改（风险消除并且审计通过）数量</th>
                    <th>完成整改率</th>
                </tr>
            </thead>
            <tbody>
                <#list totalCycle.portAudit.portImprovements as portImprovement>
                <tr>
                    <td>${portImprovement.city}</td>
                    <td>${portImprovement.riskCount}</td>
                    <td>${portImprovement.reviewCount}</td>
                    <td>${portImprovement.reviewPercent}</td>
                    <td>${portImprovement.openReviewCount}</td>
                    <td>${portImprovement.relayReviewCount}</td>
                    <td>${portImprovement.cutReviewCount}</td>
                    <td>${portImprovement.closePortReviewCount}</td>
                    <td>${portImprovement.notNeedReviewCount}</td>
                    <td>${portImprovement.dispatch}</td>
                    <td>${portImprovement.dispatchTimeOut}</td>
                    <td>${portImprovement.complete}</td>
                    <td>${portImprovement.completePercent}</td>
                </tr>
                </#list>
            </tbody>
        </table>
        <p><strong>注：</strong> 不在范围内的地市为在该地市未发生有风险操作。</p>
        <h2>三&nbsp;稽核详单</h2>
        <p>详单见${detailFileName},供复核参考。内容包括：1、端口稽核信息清单；</p>
    </body>
</html>