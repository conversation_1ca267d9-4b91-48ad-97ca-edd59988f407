<html>
    <head>
        <style>
            body { line-height:1.5; font-family: 'Microsoft YaHei', sans-serif;}
            h1,h2,h3,h4,h5,h6,p,ul,li,div{ margin:0 0 !important;line-height: 40px !important; }
            table { border-collapse: collapse; width: 100%; margin: 10px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            span {font-weight: bold;}
            ul { list-style-type: "• " !important; }
            .center {text-align:center}
            tr:nth-child(even) { background-color: #f2f2f2;    }
        </style>
    </head>
    <body>
        <h1 class="center" >IDC智审稽核报告</h1>
        <p class="center"> <strong>报告时间</strong>：${reportDate}</p>
        <br/>
        <h1>一&nbsp;当前风险概况</h1>
        <p><strong>时间范围：</strong> ${totalCycle.startDate} ~ ${totalCycle.endDate}</p>
        <h2>1.1 &nbsp;未关闭风险</h2>
        <h3>1.1.1 &nbsp;未关闭的操作风险</h3>
        <p>复核后仍发现风险操作${totalCycle.portAudit.reviewRisk}次。涉及地市：${totalCycle.portReviewAudit.citys}风险类型分布如下，详单见${detailFileName},供复核参考。内容包括：1、历史端口稽核信息清单；2、异常流量清单；3、端口速率异常清单。</p>
        <table>
            <thead>
                <tr>
                    <th>地市</th>
                    <th>风险类型</th>
                    <th>数量</th>
                    <th>样例</th>
                </tr>
            </thead>
            <tbody>
                <#list totalCycle.portReviewAudit.hasRiskCatalogs as riskCatalog>
                    <tr>
                        <td>${riskCatalog.city}</td>
                        <td>${riskCatalog.riskAnalysis}</td>
                        <td>${riskCatalog.count}</td>
                        <td>${riskCatalog.description}</td>
                    </tr>
                </#list>
            </tbody>
        </table>
    <h3>1.1.2 &nbsp;未关闭流量风险</h3>
    <table>
        <thead>
            <tr>
                <th><strong>项</strong></th>
                <th><strong>上周发现</strong></th>
                <th><strong>本周发现</strong></th>
                <th>本周新增</th>
                <th>本周恢复</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>异常数量</td>
                <td>${currentCycle.portTraffic.laskweekFreeTotal}</td>
                <td>${currentCycle.portTraffic.exceptFreeTotal}</td>
                <td>${currentCycle.portTraffic.currentAdd}</td>
                <td>${currentCycle.portTraffic.currentNormal}</td>
            </tr>
        </tbody>
        </table>
        <h3>1.1.3&nbsp;未关闭速率风险</h3>
        <table>
            <thead>
                <tr>
                    <th><strong>项</strong></th>
                    <th><strong>上周发现</strong></th>
                    <th><strong>本周发现</strong></th>
                    <th>本周新增</th>
                    <th>本周恢复</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>异常数量</td>
                    <td>${currentCycle.portRate.laskweekExceptTotal}</td>
                    <td>${currentCycle.portRate.exceptTotal}</td>
                    <td>${currentCycle.portRate.currentAdd}</td>
                    <td>${currentCycle.portRate.currentNormal}</td>
                </tr>
            </tbody>
        </table>

        <h2>1.2&nbsp;整改进度</h2>
        <h3>1.2.1 &nbsp;操作风险整改进度</h3>
        <table>
            <thead>
                <tr>
                    <th>地市</th>
                    <th>累计不按单操作数</th>
                    <th>累计风险消除数</th>
                    <th>累计风险消除率</th>
                    <th>补开通单消除数量</th>
                    <th>补割接单消除数量</th>
                    <th>补中继单消除数量</th>
                    <th>关闭端口消除数量</th>
                    <th>无需复核消除数量</th>
                    <th>派单数量</th>
                    <th>超时未回单数量</th>
                    <th>完成整改（风险消除并且审计通过）数量</th>
                    <th>完成整改率</th>
                </tr>
            </thead>
            <tbody>
                <#list totalCycle.portAudit.portImprovements as portImprovement>
                    <tr>
                        <td>${portImprovement.city}</td>
                        <td>${portImprovement.riskCount}</td>
                        <td>${portImprovement.reviewCount}</td>
                        <td>${portImprovement.reviewPercent}</td>
                        <td>${portImprovement.openReviewCount}</td>
                        <td>${portImprovement.relayReviewCount}</td>
                        <td>${portImprovement.cutReviewCount}</td>
                        <td>${portImprovement.closePortReviewCount}</td>
                        <td>${portImprovement.notNeedReviewCount}</td>
                        <td>${portImprovement.dispatch}</td>
                        <td>${portImprovement.dispatchTimeOut}</td>
                        <td>${portImprovement.complete}</td>
                        <td>${portImprovement.completePercent}</td>
                    </tr>
                </#list>
            </tbody>
        </table>
        <p><strong>注：</strong> 不在范围内的地市为在该地市未发生有风险操作。</p>

        <h1>二&nbsp;风险发现情况</h1>
        <h2>2.1&nbsp;本周新发现的操作风险</h2>
        <p><strong>稽核时间范围：</strong> ${startDate} ~ ${endDate}</p>
        <p><strong>端口操作稽核：</strong> 总操作${currentCycle.portAudit.total}次，其中存在风险${currentCycle.portAudit.risk}次。涉及地市：<#if currentCycle.portAudit.citys?has_content>${currentCycle.portAudit.citys}。风险类型分布如下：<#else>无</#if></p>
        <ul>
        <#list currentCycle.portAudit.riskCatalogs as riskCatalog>
            <p><strong>${riskCatalog.title}：</strong> ${riskCatalog.count}次，占比${riskCatalog.percent}</p>
            <li><strong>数量：</strong> ${riskCatalog.count} </li>
            <li><strong>现象：</strong> ${riskCatalog.description}</li>
            <li><strong>稽核：</strong> ${riskCatalog.checkRule}</li>
            <li><strong>示例：</strong>
                <ul>
                    <li>时间：${riskCatalog.exampleOperateTime}</li>
                    <li>设备：${riskCatalog.exampleDeviceIp}</li>
                    <li>端口：${riskCatalog.examplePortName}</li>
                    <li>操作：${riskCatalog.exampleAction}</li>
                    <li>操作人员：${riskCatalog.exampleOperationName}</li>
                    <li>操作部门：${riskCatalog.exampleOperationDept}</li>
                </ul>
            </li>
            <li><strong>建议措施：</strong> ${riskCatalog.suggestion}</li>
        </#list>
        </ul>
        <p><strong>异常流量稽核：</strong> ${currentCycle.portTraffic.exceptFreeTotal}个空闲端口存在流量。</p>
        <p><strong>端口配置速率稽核：</strong> 异常端口${currentCycle.portRate.exceptTotal}个。</p>
        <ul>
            <li>端口实际速率高于资源设备配置速率的端口${currentCycle.portRate.exceptUpperTotal}个。</li>
            <li>端口实际速率低于资源设备配置速率的端口${currentCycle.portRate.exceptLowerTotal}个。</li>
        </ul>

        <h2>2.2&nbsp;累计发现的操作风险</h2>
        <p>${totalCycle.startDate} ~ ${totalCycle.endDate}，总操作${totalCycle.portAudit.total}次，其中首次稽核发现风险${totalCycle.portAudit.risk}次。涉及地市：${totalCycle.portAudit.citys}风险类型分布如下：</p>
        <ul>
            <#list totalCycle.portAudit.riskCatalogs as riskCatalog>
                <li><strong>${riskCatalog.title}：</strong> ${riskCatalog.count}次，占比${riskCatalog.percent}</li>
            </#list>
        </ul>
    </body>
</html>