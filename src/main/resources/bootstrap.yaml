spring:
  application:
    name: nrm-graph-rest
  cloud:
    nacos:
      config:
        server-addr: daas.oss.telecomjs.com:39051
        file-extension: yaml
        username: nacos
        password: <PERSON><PERSON><PERSON>@8c9kQs.v<
        group: DEFAULT_GROUP
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  jmx:
    enabled: false
  servlet:
    multipart:
      max-file-size: 104857600
      max-request-size: 104857600
  profiles:
    active: dev
management:
  endpoints:
    web:
      exposure:
        include: "*"
s3:
  accessKey: HI4NU11DKLT42466EGM4
  secretKey: auihaF3wvhSevafknlm6voVbNiAbdio9HOQBRJua
  endpoint: *************:52000
O2:
  abilityAddress: http://jsjteop.telecomjs.com:8764/jseop/oss/O3Interface/crm/queryAddress
datas:
  maps: '{"83210": "oss-zynl-isearch10021_1103_address_search_v1",
   "83209": "oss-zynl-isearch10027_10003_address_search_v1",
   "83212": "oss-zynl-isearch10025_10002_address_search_v1",
   "83208": "oss-zynl-isearch10061_10036_9796_v1"
   }'
etl-rest:
  url: http://*************:39032

#不清楚nacos的配置是否可以随时发布,暂时写在这边
#下面是百川生产的接口
hundredRivers:
  pon:
    url: http://sjgxpt.telecomjs.com:8090/gxpt/dataservice/api/queryData/pon
    appKey: fffe1240942a4230
    appSecret: ab244f8ffffe1240942a4230fd772182
  ipran:
    url: http://sjgxpt.telecomjs.com:8090/gxpt/dataservice/api/queryData/ipran
    appKey: fffe1240942a4230
    appSecret: ab244f8ffffe1240942a4230fd772182
  newOldCity:
    url: http://sjgxpt.telecomjs.com:8090/gxpt/dataservice/api/queryData
    appKey: fffe1240942a4230
    appSecret: ab244f8ffffe1240942a4230fd772182
  four:
    url: http://sjgxpt.telecomjs.com:8090/dataway/api/alarm_data/common/alarm/dzxp/time-his-dsxgjqd-four
    appKey: fffe1240942a4230
    appSecret: ab244f8ffffe1240942a4230fd772182
  deviceAlarm:
    url: http://sjgxpt.telecomjs.com:8090/dataway/api/alarm_data/common/alarm/yybz/deviceAlarm
    appKey: fffe1240942a4230
    appSecret: ab244f8ffffe1240942a4230fd772182
  ipm:
    url: http://sjgxpt.telecomjs.com:8090/gxpt/dataservice/api/queryData/ipm
    appKey: fffe1240942a4230
    appSecret: ab244f8ffffe1240942a4230fd772182
  zd:
    url: http://sjgxpt.telecomjs.com:8090/dataway/api/zd_reporter/common/common/yybz/accessCodeZDOrderQuery
    appKey: fffe1240942a4230
    appSecret: ab244f8ffffe1240942a4230fd772182
  alarm:
    url: http://sjgxpt.telecomjs.com:8090/dataway/api/gxpt_user/it/alarm/geso/query_alarm_by_access_number
    appKey: fffe1240942a4230
    appSecret: ab244f8ffffe1240942a4230fd772182
  zdCustOrderQuery:
    url: http://sjgxpt.telecomjs.com:8090/dataway/api/zd_reporter/common/common/yybz/zdCustOrderQuery
    appKey: 2cb7f8e46a9cc539
    appSecret: a57def9b2cb7f8e46a9cc5397bdb5541
  zdNetFaultQuery:
    url: http://sjgxpt.telecomjs.com:8090//dataway/api/zd_reporter/common/wod/yybz/netFaultQuery
    appKey: 2cb7f8e46a9cc539
    appSecret: a57def9b2cb7f8e46a9cc5397bdb5541



external:
  queryConfigDevice:
    url: http://**************:7070/queryConfigDevice
  point2Point:
    url: http://**************:7070/LineRouteQuery
  queryRes:
    url: http://**************:7070/queryTunnelInfo

elasticsearch:
  hosts:
    - host: nrm.oss.telecomjs.com
      port: 39049
  username: oss-sw_rw
  password: BinSSLP*f0U4lXmK
