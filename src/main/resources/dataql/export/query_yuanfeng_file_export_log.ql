// 查询导出日志
// 简化的DataQL脚本，只负责数据查询，业务逻辑在Java层处理

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var queryLogList = @@mybatis(p)<%
    <select>
        SELECT 
            id, operator_id, operator_name, export_module, export_interface, export_type,
            file_name, file_size, data_count, sheet_name, export_status, error_message,
            sftp_upload_status, redirect_url, start_time, end_time, duration_ms,
            client_ip, area_code, create_time
        FROM yuanfeng_file_export_log
        WHERE 1=1
        <if test="p.operatorId != null and p.operatorId != ''">
            AND operator_id = #{p.operatorId}
        </if>
        <if test="p.exportModule != null and p.exportModule != ''">
            AND export_module = #{p.exportModule}
        </if>
        <if test="p.exportInterface != null and p.exportInterface != ''">
            AND export_interface = #{p.exportInterface}
        </if>
        <if test="p.exportType != null and p.exportType != ''">
            AND export_type = #{p.exportType}
        </if>
        <if test="p.exportStatus != null and p.exportStatus != ''">
            AND export_status = #{p.exportStatus}
        </if>
        <if test="p.startTimeBegin != null and p.startTimeBegin != ''">
            AND start_time >= #{p.startTimeBegin}
        </if>
        <if test="p.startTimeEnd != null and p.startTimeEnd != ''">
            AND start_time <= #{p.startTimeEnd}
        </if>
        <if test="p.areaCode != null and p.areaCode != ''">
            AND area_code = #{p.areaCode}
        </if>
        ORDER BY create_time DESC
    </select>
%>;

// 执行分页查询
var pageQuery = queryLogList(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}// 第n页
});

var data = pageQuery.data();
var pageInfo = pageQuery.pageInfo();

return {
    "success": true,
    "message": "导出日志查询成功",
    "pageInfo": pageInfo,
    "data": data
};
