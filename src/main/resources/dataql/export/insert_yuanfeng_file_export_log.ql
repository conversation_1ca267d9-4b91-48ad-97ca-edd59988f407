// 保存导出日志
// 简化的DataQL脚本，只负责数据插入，业务逻辑在Java层处理

var insertLog = @@sql(p)<%
    INSERT INTO yuanfeng_file_export_log (
        id, operator_id, operator_name, export_module, export_interface, export_type,
        file_name, file_size, data_count, sheet_name, export_status, error_message,
        export_params, sftp_upload_status, sftp_path, redirect_url,
        start_time, end_time, duration_ms, client_ip, user_agent, area_code,
        create_time, update_time
    ) VALUES (
        #{p.id}, #{p.operatorId}, #{p.operatorName}, 
        #{p.exportModule}, #{p.exportInterface}, #{p.exportType},
        #{p.fileName}, #{p.fileSize}, #{p.dataCount}, 
        #{p.sheetName}, #{p.exportStatus}, #{p.errorMessage},
        #{p.exportParams}, #{p.sftpUploadStatus}, #{p.sftpPath}, 
        #{p.redirectUrl}, #{p.startTime}, #{p.endTime}, 
        #{p.durationMs}, #{p.clientIp}, #{p.userAgent}, 
        #{p.areaCode}, NOW(), NOW()
    )
%>;

// 执行插入并返回影响行数
return insertLog(${param});
