<?xml version="1.0" encoding="UTF-8"?>
<!--        scan : 当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true-->
<configuration scan="false" scanPeriod="60 seconds" debug="false">

    <property name="Console_Pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%logger{50}] - %msg%n"/>

    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%-5p: %d{yyyy/MM/dd HH:mm:ss,SSS} %c - %m%n</pattern>
        </encoder>
    </appender>

    <appender name="File" class="ch.qos.logback.core.rolling.RollingFileAppender">
    　　　<file>logs/log.log</file>
    　　　<append>true</append>
    　　　<encoder>
    　　　　　<pattern>%d %p - %m%n</pattern>
    　　　</encoder>
    </appender>

    <root level="info">
        <appender-ref ref="Console" />
        <appender-ref ref="File"  />
    </root>

    <logger name="com.telecom">
        <level value="info" />
    </logger>

    <logger name="com.telecom.da.client">
        <level value="warn" />
    </logger>

    <logger name="common">
        <level value="info" />
    </logger>

    <!-- 3rdparty Loggers -->
    <logger name="org.springframework.security">
        <level value="warn" />
    </logger>

    <logger name="org">
        <level value="info" />
    </logger>

    <logger name="com.alibaba">
        <level value="warn" />
    </logger>

    <logger name="io.netty">
        <level value="warn" />
    </logger>

    <logger name="java.sql">
        <level value="warn" />
    </logger>

</configuration>