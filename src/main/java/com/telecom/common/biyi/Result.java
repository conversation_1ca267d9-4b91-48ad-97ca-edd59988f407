package com.telecom.common.biyi;

import lombok.Data;

/**
 * @ProjectName: udp6
 * @Package: com.ctsi.util
 * @ClassName: Result
 * @Author: lx
 * @Description: 输出工具类
 * @Date: 2021年9月3日
 * @Version: 1.0
 */
@Data
public class Result {

    public static final String SUCCESS = "200";     //成功
    public static final String ERROR = "500";   //失败

    private String resultCode;

    private String resultMsg;

    private Object data;

    private String extendParams;//扩展参数

    public Result() {
    }

    public Result(String code, String msg) {
        this.resultCode = code;
        this.resultMsg = msg;
    }

    public Result(String code, String msg, String extendParams) {
        this.resultCode = code;
        this.resultMsg = msg;
        this.extendParams = extendParams;
    }

    public Result(boolean success, String msg) {
        this.setSuccess(success);
        this.resultMsg = msg;
    }

    public Result(String code, String msg, Object data) {
        this.resultCode = code;
        this.resultMsg = msg;
        this.data = data;
    }

    public Result(boolean success, String msg, Object data) {
        this(success, msg);
        this.data = data;
    }

    public void setSuccess(boolean b) {
        this.resultCode = b ? SUCCESS : ERROR;
    }


}
