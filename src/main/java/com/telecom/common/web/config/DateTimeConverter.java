package com.telecom.common.web.config;

import org.springframework.core.convert.converter.Converter;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;


public class DateTimeConverter implements Converter<Integer, LocalDateTime>  {

    private static final List<String> formarts = new ArrayList<String>(4);
    static{
        formarts.add("yyyy-MM");
        formarts.add("yyyy-MM-dd");
        formarts.add("yyyy-MM-dd hh:mm");
        formarts.add("yyyy-MM-dd hh:mm:ss");
    }
    public LocalDateTime convert(Integer source) {

            LocalDateTime date = LocalDateTime.ofEpochSecond(source,0, ZoneOffset.ofHours(0));
            return date.plusHours(8);

    }

    /**
     * 功能描述：格式化日期
     *
     * @param dateStr
     *            String 字符型日期
     * @param format
     *            String 格式
     * @return Date 日期
     */
    public  LocalDateTime parseDate(String dateStr, String format) {
        LocalDateTime date=null;
        try {
            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(format);
            date = (LocalDateTime) dateFormat.parse(dateStr);
        } catch (Exception e) {
        }
        return date;
    }
    public static void main(String[] args) {
        System.err.println(new DateConverter().convert("2014-04"));
    }
}
