package com.telecom.common.web.config;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Collections;

@Configuration
public class RestTemplateConfig {
    private static final Logger logger = LoggerFactory.getLogger(RestTemplateConfig.class);

    @Bean
    public RestTemplate restTemplate(){

        // StringHttpMessageConverter m = new StringHttpMessageConverter(Charset.forName("UTF-8"));
        RestTemplate restTemplate = new RestTemplate();

        restTemplate.getInterceptors().add(((httpRequest, bytes, clientHttpRequestExecution) -> {
            // logger.info("拦截器起作用");
            httpRequest.getHeaders().setContentType(MediaType.APPLICATION_JSON);;
            httpRequest.getHeaders().setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            return clientHttpRequestExecution.execute(httpRequest, bytes);
        }));

        /*
        List<HttpMessageConverter<?>> coverters=restTemplate.getMessageConverters();
        int i=0;
        for (HttpMessageConverter<?> converter:coverters) {
            if (converter instanceof StringHttpMessageConverter){
                coverters.remove(converter);
                break;
            }
            i++;
        }

         */




        return restTemplate;
    }
}
