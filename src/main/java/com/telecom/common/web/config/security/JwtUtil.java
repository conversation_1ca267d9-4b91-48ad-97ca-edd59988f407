package com.telecom.common.web.config.security;




import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import io.jsonwebtoken.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Service;

/*
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
 */


@Service
public class JwtUtil {

	private final Base64.Encoder encoder = Base64.getEncoder();
	private static final Logger LOG = LoggerFactory.getLogger(JwtUtil.class);

	private static final String AUTHORITIES_KEY = "auth";
	private static final String USER_ID_KEY = "id";
	private static final String USER_NAME_KEY = "name";
	private static final String USER_ROLE_KEY = "role";
	private static final String USER_SUBJECT_KEY = "sub";

	private static final String REMEMBERME_KEY = "rem";


	
    @Value("${jwt.secret:my-secret-token-to-change-in-production}")
    private String secretKey;
    
    final long TTL_MILLIS=1000*60*30;

	/**
	 * 创建JWT
	 * @param user
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws
	 */

     public String createJWT(JwtUser user) throws UnsupportedEncodingException {
		 String secretKeyB64 = encoder.encodeToString(this.secretKey.getBytes(StandardCharsets.UTF_8));
		 String token= Jwts.builder().signWith(SignatureAlgorithm.HS512, secretKeyB64).addClaims(new HashMap<String,Object>() {{
			 put(USER_NAME_KEY,user.getUsername());
			 put(USER_ID_KEY,user.getId());
			 put(AUTHORITIES_KEY,user.getAuthorities());
		 }}).compact();
		 return token;
		 /*
    	 try {
    		 	Date now=new Date();
    		 	
    		    Algorithm algorithm = Algorithm.HMAC256(secret);
    		    String token = JWT.create()
    		        .withIssuer("auth0")
    		        .withSubject(user.getUsername())
    		        .withJWTId(user.getId()+"-"+System.currentTimeMillis()+"")
    		        .withClaim("roles", user.getAuthorities()).withClaim("id", user.getId().toString()).withExpiresAt(new Date(now.getTime()+TTL_MILLIS))
    		        .sign(algorithm);
    		    return token;
    		} catch (UnsupportedEncodingException exception){
    		    //UTF-8 encoding not supported
    			throw exception;
    		} catch (JWTCreationException exception){
    		    //Invalid Signing configuration / Couldn't convert Claims.
    			throw exception;
    		}

		  */
    }



	/**
	 * 验证jwt
	 * @param token
	 * @return
	 * @throws AuthenticationException
	 */
	public JwtUser verifyJWT(String token) throws AuthenticationException{
		String secretKeyB64 = encoder.encodeToString(this.secretKey.getBytes(StandardCharsets.UTF_8));
		// LOG.info("token:"+token);
		// LOG.info("secretKey:"+ secretKeyB64);
		Claims claims = Jwts.parser()
				.setSigningKey(secretKeyB64)
				.parseClaimsJws(token)
				.getBody();
		String userName = (String) claims.get(USER_NAME_KEY);
		String id = (String) claims.get(USER_ID_KEY);
		String auth = (String) claims.get(AUTHORITIES_KEY);
		JwtUser user = new JwtUser();
		user.setUsername(userName);
		user.setId(id);
		user.setAuthorities(auth);
		return user;

		/*
    	 try {
    		    Algorithm algorithm = Algorithm.HMAC256(secret);
    		    JWTVerifier verifier = JWT.require(algorithm)
    		        .withIssuer("auth0")
    		        .build(); //Reusable verifier instance
    		    DecodedJWT jwt = verifier.verify(token);
    		    Map<String,Claim> claims=jwt.getClaims();
    		    JwtUser user=new JwtUser();
    		    user.setUsername(jwt.getSubject());
    		    user.setId(claims.get("id").asString());
    		    user.setAuthorities(claims.get("roles")==null?"":claims.get("roles").asString());
    		   
    		    return user;
    		} catch (UnsupportedEncodingException exception){
    			LOG.error(exception.getMessage(),exception);
    			throw new JwtTokenMalformedException("jwt unsupported encoding");
    		} catch (JWTVerificationException exception){
			 	LOG.error(exception.getMessage(),exception);
    			throw new AccountExpiredException("会话失效");
    		}

		 */
    }
     
    
	@Deprecated
    public JwtUser decodeJwt(String token) throws AuthenticationException{
		/*
		Claims claims = Jwts.parser().
		    DecodedJWT jwt = JWT.decode(token);
		    Map<String,Claim> claims=jwt.getClaims();
		    
		    
		    JwtUser user=new JwtUser();
		    user.setUsername(jwt.getSubject());
		    user.setId(claims.get("id").asString());
		    user.setAuthorities(claims.get("roles")==null?"":claims.get("roles").asString());
		   
		    return user;

		 */
		Claims claims = Jwts.parser()
				.setSigningKey(secretKey)
				.parseClaimsJws(token)
				.getBody();
		String userName = (String) claims.get(USER_NAME_KEY);
		String id = (String) claims.get(USER_ID_KEY);
		String auth = (String) claims.get(AUTHORITIES_KEY);
		JwtUser user = new JwtUser();
		user.setUsername(userName);
		user.setId(id);
		user.setAuthorities(auth);
		return user;
		
    }
}
