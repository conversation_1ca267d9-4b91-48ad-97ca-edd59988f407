package com.telecom.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

public class JSONObjectUtil {

    public static void convertBigNumberToString(List list) {
        if (CollectionUtils.isEmpty(list)) return;
        int i=0;
        for (Object obj : list) {
            if (obj instanceof BigInteger) {
                // param.put(entry.getKey(), obj.toString());
                list.set(i, ((BigInteger) obj).toString());
            }else if (obj instanceof BigDecimal) {
                list.set(i, ((BigInteger) obj).toString());
            }else if (obj instanceof JSONObject) {
                convertBigNumberToString((JSONObject)obj);
            }else if (obj instanceof List) {
                List subList = (List)obj;
                convertBigNumberToString(subList);
            }
            i++;
        }
    }

    public static void convertBigNumberToString(JSONObject param) {
        Set<Map.Entry<String,Object>> entrySet= param.entrySet();
        for (Map.Entry<String,Object> entry: entrySet) {
            Object obj = entry.getValue();
            if (obj instanceof BigInteger) {
                param.put(entry.getKey(), obj.toString());
            }else if (obj instanceof BigDecimal) {
                param.put(entry.getKey(), obj.toString());
            }else if (obj instanceof JSONObject) {
                convertBigNumberToString((JSONObject)obj);
            }else if (obj instanceof List) {
                List list = (List)obj;
                convertBigNumberToString(list);
            }
        }
    }

    public static List<JSONObject> jsonObjectArrayToList(JSONArray jsonArray) {

        return Optional.ofNullable(jsonArray).orElse(new JSONArray()).toJavaList(JSONObject.class);
    }

    public static List<String> jsonStringArrayToList(JSONArray jsonArray) {
        return (List<String>) Optional.ofNullable(jsonArray).orElse(new JSONArray()).stream().collect(Collectors.toList());
    }
}
