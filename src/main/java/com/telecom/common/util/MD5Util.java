package com.telecom.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

import java.io.UnsupportedEncodingException;

public class MD5Util {
    static final Logger log = LoggerFactory.getLogger(MD5Util.class);
    public static String encrypt3ToMD5(String str) {
        log.debug("MD5待加密字符串：\n"+str);
        String md5 = "  ";
        try {
            md5 = DigestUtils.md5DigestAsHex(str.getBytes("utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        log.debug("MD5加密结果：\n"+md5);
        return md5;
    }
}
