package com.telecom.common.util;



import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.Key;

public class CryptAES {

    private static final String AESTYPE = "AES/ECB/PKCS5Padding";

    /** 加密
     * @param keyStr
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String AES_Encrypt(String keyStr, String plainText) throws Exception {
        Key key = generateKey(keyStr);
        Cipher cipher = Cipher.getInstance(AESTYPE);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] encrypt = cipher.doFinal(plainText.getBytes("UTF-8"));
        return bytesToHex(encrypt);
    }

    /** 解密
     * @param keyStr
     * @param encryptData
     * @return
     * @throws Exception
     */
    public static String AES_Decrypt(String keyStr, String encryptData) throws Exception {
        Key key = generateKey(keyStr);
        Cipher cipher = Cipher.getInstance(AESTYPE);
        cipher.init(Cipher.DECRYPT_MODE, key);
        byte[] decrypt = cipher.doFinal(hexToBytes(encryptData));
        return new String(decrypt, "UTF-8").trim();
    }

    /** 密钥标准化处理
     * @param key
     * @return
     */
    private static Key generateKey(String key) {
        byte[] bytes = key.getBytes();
        try
        {
            bytes = key.getBytes("UTF-8");
        }
        catch (UnsupportedEncodingException e)
        {
            e.printStackTrace();
        }
        int oriLen = bytes.length, len = ((oriLen - 1) / 16 + 1) * 16;
        System.arraycopy(bytes, 0, bytes = new byte[len], 0, oriLen);
        return new SecretKeySpec(bytes, "AES");
    }


    /** byte数组转hex
     * @param bytes
     * @return
     */
    private static String bytesToHex(byte[] bytes){
        StringBuilder sb = new StringBuilder();
        for(byte b : bytes) {
            String strHex = Integer.toHexString(b & 0xFF);
            sb.append((strHex.length() == 1) ? "0" + strHex : strHex); // 每个字节由两个字符表示，位数不够，高位补0
        }
        return sb.toString().trim();
    }

    /** hex转byte数组
     * @param hex
     * @return
     */
    private static byte[] hexToBytes(String hex){
        int byteLen = hex.length() / 2; // 每两个字符描述一个字节
        byte[] ret = new byte[byteLen];
        for(int i = 0; i < byteLen; i++) {
            int m = i * 2 + 1;
            int n = m + 1;
            int intVal = Integer.decode("0x" + hex.substring(i * 2, m) + hex.substring(m, n));
            ret[i] = (byte) intVal;
        }
        return ret;
    }

    //测试主类
    public static void main(String[] args) throws Exception {

            String keyStr = "abcd";//密钥key
            String plainText = "张三18507985897";

            String cipher = AES_Encrypt(keyStr, "张三18507985897");//加密
            String plain = AES_Decrypt(keyStr, cipher);//解密
            System.out.println(cipher);
            System.out.println(plain);

    }

}
