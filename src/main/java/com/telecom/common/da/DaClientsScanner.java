package com.telecom.common.da;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
// import com.mysgk.blogdemo.annotation.MyHttpClient;
// import com.mysgk.blogdemo.proxy.RibbonAopProxyFactory;
import com.telecom.da.client.annotation.DaClientProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class DaClientsScanner implements BeanDefinitionRegistryPostProcessor, EnvironmentAware {
    @Autowired
    DaClientProperties daClientProperties;


    private final Logger logger = LoggerFactory.getLogger(DaClientsScanner.class);

    private ApplicationContext ctx;

    public void run(BeanDefinitionRegistry registry) {
        String scanPackagePath = environment.getProperty("da-client.scan-package");
        Set<Class<?>> scanPackage = ClassUtil.scanPackageByAnnotation(scanPackagePath, DaClientProxy.class);

        for (Class<?> cls : scanPackage) {

            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(cls);
            GenericBeanDefinition definition = (GenericBeanDefinition) builder.getRawBeanDefinition();
            definition.getPropertyValues().add("interfaceClass", definition.getBeanClassName());
            definition.setBeanClass(DaSpringBeanFactory.class);
            definition.setAutowireMode(GenericBeanDefinition.AUTOWIRE_BY_TYPE);
            String beanName = StrUtil.removePreAndLowerFirst(cls.getSimpleName(), 0) + "RibbonClient";
            registry.registerBeanDefinition(beanName, definition);
            logger.info("注册:"+cls.getName());
        }

    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        run(registry);
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {

    }

    Environment environment;
    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }
}
