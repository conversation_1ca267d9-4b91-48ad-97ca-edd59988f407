package com.telecom.common.da;

import com.telecom.da.client.DaProxyFactory;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.telecom.da.client.DaClient;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.tcp.TcpClient;

import java.util.concurrent.TimeUnit;

@Configuration
public class DaClientConfig {
    private final Logger logger = LoggerFactory.getLogger(DaClientConfig.class);




    ApplicationContext applicationContext;

    public DaClientConfig(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Bean
    public DaClient daClient() {
        // JSON.DEFFAULT_DATE_FORMAT="yyyy-MM-dd HH:mm:ss.SSS";
        DaClientProperties properties = daClientProperties();
        logger.info("访问url:"+properties.getUrl());
        logger.info("scan-package:"+properties.getScanPackage());


        TcpClient tcpClient = TcpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000*200) // Connection Timeout
                .doOnConnected(connection ->
                        connection.addHandlerLast(new ReadTimeoutHandler(10*200)) // Read Timeout
                                .addHandlerLast(new WriteTimeoutHandler(10*200))); // Write Timeout

        WebClient webClient = WebClient.builder().defaultHeader("app_id",properties.getAppId()).exchangeStrategies(ExchangeStrategies.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(1024 * 1024 * 1024))
                .build()).clientConnector(new ReactorClientHttpConnector(HttpClient.from(tcpClient)))
                .build();



        DaClient client = new DaClient(properties.getUrl(),properties.getAppId(), webClient);// 初始化工具类
        return client;
    }

    @Bean
    public DaProxyFactory daClientProxyFactory() {
        DaClient client = daClient();
        DaProxyFactory factory = new DaProxyFactory(client);
        return factory;
    }



    @Bean
    @ConfigurationProperties(prefix = "da-client")
    public DaClientProperties daClientProperties() {
        return new DaClientProperties();
    }




}
