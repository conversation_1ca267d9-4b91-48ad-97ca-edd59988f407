package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品(RmProductNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:04:30
 */
public class RmProductNet implements Serializable {
    private static final long serialVersionUID = 968431743003492304L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * CRM产品ID
     */
    private String crmProductNetId;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 产品实际速率
     */
    private Object actrate;
    /**
     * 开通日期
     */
    private Date archievedDate;
    /**
     * 归档日期
     */
    private Date archiveDate;
    /**
     * 电信管理区ID
     */
    private BigDecimal tmlId;
    /**
     * 业务状态ID
     */
    private BigDecimal processStatusId;
    /**
     * 产品类型ID
     */
    private BigDecimal productTypeId;
    /**
     * 区域ID
     */
    private BigDecimal areaId;
    /**
     * A端客户ID
     */
    private BigDecimal aCustomerId;
    /**
     * Z端客户ID
     */
    private BigDecimal zCustomerId;
    /**
     * 前台选址ID
     */
    private BigDecimal addressId;
    /**
     * A端前台选址ID
     */
    private BigDecimal aAddressId;
    /**
     * Z端前台选址ID
     */
    private BigDecimal zAddressId;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    
    private String businessPackage;
    /**
     * 合同编码
     */
    private Object crmContractNo;
    
    private Date quitData;
    
    private Date downData;
    
    private Integer isValid;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 版本
     */
    private BigDecimal version;
    
    private String crmProductNetSpecId;
    
    private Integer otherState;
    /**
     * 角色ID
     */
    private BigDecimal roleId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCrmProductNetId() {
        return crmProductNetId;
    }

    public void setCrmProductNetId(String crmProductNetId) {
        this.crmProductNetId = crmProductNetId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Object getActrate() {
        return actrate;
    }

    public void setActrate(Object actrate) {
        this.actrate = actrate;
    }

    public Date getArchievedDate() {
        return archievedDate;
    }

    public void setArchievedDate(Date archievedDate) {
        this.archievedDate = archievedDate;
    }

    public Date getArchiveDate() {
        return archiveDate;
    }

    public void setArchiveDate(Date archiveDate) {
        this.archiveDate = archiveDate;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public BigDecimal getProcessStatusId() {
        return processStatusId;
    }

    public void setProcessStatusId(BigDecimal processStatusId) {
        this.processStatusId = processStatusId;
    }

    public BigDecimal getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(BigDecimal productTypeId) {
        this.productTypeId = productTypeId;
    }

    public BigDecimal getAreaId() {
        return areaId;
    }

    public void setAreaId(BigDecimal areaId) {
        this.areaId = areaId;
    }

    public BigDecimal getACustomerId() {
        return aCustomerId;
    }

    public void setACustomerId(BigDecimal aCustomerId) {
        this.aCustomerId = aCustomerId;
    }

    public BigDecimal getZCustomerId() {
        return zCustomerId;
    }

    public void setZCustomerId(BigDecimal zCustomerId) {
        this.zCustomerId = zCustomerId;
    }

    public BigDecimal getAddressId() {
        return addressId;
    }

    public void setAddressId(BigDecimal addressId) {
        this.addressId = addressId;
    }

    public BigDecimal getAAddressId() {
        return aAddressId;
    }

    public void setAAddressId(BigDecimal aAddressId) {
        this.aAddressId = aAddressId;
    }

    public BigDecimal getZAddressId() {
        return zAddressId;
    }

    public void setZAddressId(BigDecimal zAddressId) {
        this.zAddressId = zAddressId;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public String getBusinessPackage() {
        return businessPackage;
    }

    public void setBusinessPackage(String businessPackage) {
        this.businessPackage = businessPackage;
    }

    public Object getCrmContractNo() {
        return crmContractNo;
    }

    public void setCrmContractNo(Object crmContractNo) {
        this.crmContractNo = crmContractNo;
    }

    public Date getQuitData() {
        return quitData;
    }

    public void setQuitData(Date quitData) {
        this.quitData = quitData;
    }

    public Date getDownData() {
        return downData;
    }

    public void setDownData(Date downData) {
        this.downData = downData;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public String getCrmProductNetSpecId() {
        return crmProductNetSpecId;
    }

    public void setCrmProductNetSpecId(String crmProductNetSpecId) {
        this.crmProductNetSpecId = crmProductNetSpecId;
    }

    public Integer getOtherState() {
        return otherState;
    }

    public void setOtherState(Integer otherState) {
        this.otherState = otherState;
    }

    public BigDecimal getRoleId() {
        return roleId;
    }

    public void setRoleId(BigDecimal roleId) {
        this.roleId = roleId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

