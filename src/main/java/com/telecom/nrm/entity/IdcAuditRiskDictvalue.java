package com.telecom.nrm.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 
 * @TableName idc_audit_risk_dictvalue
 */
@Data
public class IdcAuditRiskDictvalue {

    private BigDecimal id;

    private String risk;

    private String riskAnalysis;

    private String riskLevel;

    private String riskDesc;

    private Integer existOrder;
    private String existOrderDesc;

    private String portStatus;

    private String operationType;

    private String psoType;

    private String riskResult;

    private BigDecimal roleId;
    private String roleDesc;

    private Integer groupId;

    private BigDecimal targetRiskId;

    private Integer ruleCount;
}