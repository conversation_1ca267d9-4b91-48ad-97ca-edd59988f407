package com.telecom.nrm.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName pm_audit_daily
 */
@Data
public class PmAuditDaily {

    /**
     * 
     */
    private BigDecimal id;

    /**
     * 
     */
    private String auditType;

    /**
     * 
     */
    private Integer dealResult;

    /**
     * 
     */
    private String dealMessage;

    /**
     * 
     */
    private String fileName;

    /**
     * 
     */
    private String filePath;

    /**
     * 
     */
    private Long createId;

    /**
     * 
     */
    private Date createDate;

    /**
     * 
     */
    private Date stateDate;

    /**
     * 
     */
    private String shardingId;

    /**
     * 
     */
    private Integer seq;

    /**
     * 
     */
    private Date startDate;

    private String auditFileName;

    private String auditFilePath;


    private String batchId;


    private int auditTotalCount;


    private int finishCount;

    private int waitFinishCount;

    private String auditFileContent;

    public void setId(BigDecimal id) {
        this.id = id;
        this.batchId = id != null ? id.toString() : "";
    }
}