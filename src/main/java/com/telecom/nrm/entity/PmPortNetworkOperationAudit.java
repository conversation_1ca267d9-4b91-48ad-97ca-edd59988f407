package com.telecom.nrm.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 
 * @TableName pm_port_network_operation_audit
 */
@Data
public class PmPortNetworkOperationAudit {
    /**
     * 
     */
    private BigDecimal id;

    /**
     * 
     */
    private BigDecimal deviceId;

    /**
     * 
     */
    private String deviceIp;

    /**
     * 
     */
    private String deviceName;

    /**
     * 
     */
    private String deviceCode;

    /**
     * 
     */
    private BigDecimal portId;

    /**
     * 
     */
    private String portName;

    /**
     * 
     */
    private String portCode;

    /**
     * 
     */
    private String portStatus;

    /**
     * 
     */
    private String portBusinessCode;

    /**
     * 
     */
    private String regionName;

    /**
     * 
     */
    private Date lastModifyTime;


    private Date operationDate;
    private String operationInstruct;
    private String operationNotes;
    private String operationType;
    private String crmCustOrderCode;
    private String psoType;
    private String orderBusinessCode;
    private Date acceptTime;
    private Date completeTime;
    private String orderStatus;
    private String prePortName;
    private String desc;
    private String intervalTime;
    private String risk;
    private String riskDesc;
    private String riskLevel;
    private String riskResult;
    private String riskAnalysis;
    private Date createDate;
    private BigDecimal auditId;
    private String auditResult;
    private Integer auditStatus;
    private String operatorName;
    private String operatorAccount;
    private String logContent;
    private BigDecimal riskId; // 对应风险规则ID
    private String defineRiskType; // 在字段规则内额风险类型
    private String suggest;//建议措施

    private int pageSize;

    private List<BigDecimal> ids;

    private String key;

    private String operatorFullDept;

    private int count;

    private Integer reviewStatus;
    private String reviewStatusDesc;

    private String reviewRisk;

    private Integer groupId;

    private String dispatchStatus;
    private Date dispatchDate;
    private String dispatchealResult;

    private String opAuditRemark;

    private Date auditDate;
    private String busiAnalysis;
    private String itAnalysis;
    private Date reviewDate;
    private String operatorRegionName;
    private String reviewPsoType;
    private String reviewOperationType;
    private String reviewRiskAnalysis;
}