package com.telecom.nrm.entity;

import lombok.Data;

@Data
public class BcInfoQueryDTO {

    /**
     * 电路/通道/中继Id
     */
    private String bcId;

    /**
     * 电路/通道/中继名称
     */
    private String bcCode;

    /**
     * 区间段Id
     */
    private String bcLsegId;

    /**
     * 区间段名称
     */
    private String bcLsegName;

    /**
     * 区间段前局站Id
     */
    private String bcLsegStationId;

    /**
     * 区间段前局站名称
     */
    private String bcLsegStationName;

    /**
     * 区间段下一局站Id
     */
    private String bcLsegNextStationId;

    /**
     * 区间段下一局站名称
     */
    private String bcLsegNextStationName;

    /**
     * 支路Id
     */
    private String bcLsegRouteId;

    /**
     * 支路序号
     */
    private String bcLsegRouteNum;

    /**
     * 段Id
     */
    private String bcSegId;

    /**
     * 段名称
     */
    private String bcSegName;

    /**
     * 段当前局站Id
     */
    private String stationId;

    /**
     * 段当前局站名称
     */
    private String stationName;


    /**
     * 段下一局站Id
     */
    private String nextStationId;

    /**
     * 段下一局站名称
     */
    private String nextStationName;

}
