package com.telecom.nrm.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class WaveQueryDTO {

    /**
     * 光复用段名称
     */
    @NotBlank(message = "光复用段名称不能为空")
    private String multiSegName;

    /**
     * 光复用段Id
     */
    @NotBlank(message = "光复用段Id不能为空")
    private String multiSegId;

    /**
     * 波道名称
     */
    private String waveName;

    /**
     * 地市编码（PPM编码）
     */
    @NotBlank(message = "地市编码（PPM编码）不能为空")
    private String cityCode;


}
