package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 区域(RmAreaNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:04:30
 */
public class RmAreaNet implements Serializable {
    private static final long serialVersionUID = 518723528330738112L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 父区域ID
     */
    private BigDecimal parentId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 区域类型ID
     */
    private Integer areaTypeId;
    /**
     * 行政区划单位ID
     */
    private Integer areaLevelId;
    /**
     * 有效性
     */
    private Integer isValid;
    /**
     * 区号
     */
    private String zoneNumber;
    /**
     * 邮编
     */
    private String zipCode;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 所属地区ID
     */
    private BigDecimal regionId;
    /**
     * 简称
     */
    private String alias;
    /**
     * 城郊属性ID
     */
    private Integer citySuburbId;
    /**
     * 面积（M2）
     */
    private Object measureArea;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    
    private String coordinateX;
    
    private BigDecimal leafRegionId;
    
    private String coordinateY;
    /**
     * 地址ID
     */
    private BigDecimal addressId;
    /**
     * 封锁状态ID
     */
    private Integer lockStateId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 集团编码
     */
    private String standardCode;
    /**
     * 数据来源系统ID
     */
    private BigDecimal dataSourceSysId;
    /**
     * 集团名称
     */
    private String standardName;
    /**
     * 原实体ID
     */
    private String oldId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getParentId() {
        return parentId;
    }

    public void setParentId(BigDecimal parentId) {
        this.parentId = parentId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public Integer getAreaTypeId() {
        return areaTypeId;
    }

    public void setAreaTypeId(Integer areaTypeId) {
        this.areaTypeId = areaTypeId;
    }

    public Integer getAreaLevelId() {
        return areaLevelId;
    }

    public void setAreaLevelId(Integer areaLevelId) {
        this.areaLevelId = areaLevelId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getZoneNumber() {
        return zoneNumber;
    }

    public void setZoneNumber(String zoneNumber) {
        this.zoneNumber = zoneNumber;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Integer getCitySuburbId() {
        return citySuburbId;
    }

    public void setCitySuburbId(Integer citySuburbId) {
        this.citySuburbId = citySuburbId;
    }

    public Object getMeasureArea() {
        return measureArea;
    }

    public void setMeasureArea(Object measureArea) {
        this.measureArea = measureArea;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public String getCoordinateX() {
        return coordinateX;
    }

    public void setCoordinateX(String coordinateX) {
        this.coordinateX = coordinateX;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public String getCoordinateY() {
        return coordinateY;
    }

    public void setCoordinateY(String coordinateY) {
        this.coordinateY = coordinateY;
    }

    public BigDecimal getAddressId() {
        return addressId;
    }

    public void setAddressId(BigDecimal addressId) {
        this.addressId = addressId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public BigDecimal getDataSourceSysId() {
        return dataSourceSysId;
    }

    public void setDataSourceSysId(BigDecimal dataSourceSysId) {
        this.dataSourceSysId = dataSourceSysId;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

