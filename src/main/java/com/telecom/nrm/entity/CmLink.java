package com.telecom.nrm.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * (CmLinkNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
@Data
public class CmLink implements Serializable {
    private static final long serialVersionUID = -57289478330424299L;
    
    private BigDecimal id;
    
    private String name;
    
    private String code;
    
    private Long specId;
    
    private String notes;
    
    private BigDecimal creatorId;
    
    private Date createDate;
    
    private BigDecimal modifierId;
    
    private Date modifyDate;
    
    private BigDecimal version;
    
    private BigDecimal shardingId;
    
    private String standardCode;
    
    private String standardName;
    
    private Integer seq;
    
    private String alias;
    
    private Date intoNetworkDate;
    
    private Date startUseDate;
    
    private Date stopUseDate;
    
    private Date exitNetworkDate;
    
    private BigDecimal zFacilityId;
    
    private BigDecimal aFacilityId;
    
    private BigDecimal aPhysicDeviceId;

    private String aPhysicDeviceName;
    
    private BigDecimal aLogicDeviceId;
    
    private BigDecimal aWareId;
    
    private BigDecimal aPortId;
    
    private BigDecimal zPhysicDeviceId;

    private String zPhysicDeviceName;

    private BigDecimal zLogicDeviceId;
    
    private BigDecimal zWareId;
    
    private BigDecimal zPortId;
    
    private Object textRoute;
    
    private Integer lifeStateId;
    
    private Integer physicalStateId;
    
    private Integer usingStateId;
    
    private Integer lockStateId;
    
    private Integer otherStateId;
    
    private Integer linkTypeId;
    
    private Integer longLocalId;
    
    private Integer networkLayerId;
    
    private BigDecimal tmlId;
    
    private BigDecimal regionId;
    
    private BigDecimal aAddressId;
    
    private BigDecimal aTmlId;
    
    private BigDecimal aRegionId;
    
    private BigDecimal zAddressId;
    
    private BigDecimal zTmlId;
    
    private BigDecimal zRegionId;
    
    private Integer mntLevelId;
    
    private Integer serviceLevelId;
    
    private BigDecimal aMntPersonId;
    
    private BigDecimal zMntPersonId;
    
    private Integer propertyTypeId;
    
    private String rentContractNo;
    
    private String lifespan;
    
    private Integer propertyOwnerId;
    
    private Integer directionId;
    
    private Integer rateId;
    
    private Integer workModeId;
    
    private Integer workWayId;
    
    private Integer securityLevelId;
    
    private Date timeStamp;
    
    private Integer accessModeId;
    
    private Integer applyNetId;
    
    private String attenuation;
    
    private Integer aAccessModeId;
    
    private String aMatainContactDetail;
    
    private BigDecimal aMntDeptId;
    
    private Object aTextRoute;
    
    private BigDecimal aUseDeptId;
    
    private Integer dmRuleId;
    
    private Integer doubleEndId;
    
    private Integer intigrityId;
    
    private BigDecimal length;
    
    private String matainContactDetail;
    
    private BigDecimal mntDeptId;
    
    private BigDecimal mntPersonId;
    
    private Integer netLevelId;
    
    private String nmCode;
    
    private BigDecimal nmId;
    
    private String nmName;
    
    private Integer ownerNetId;
    
    private Integer persistenceId;
    
    private Integer protectWayId;
    
    private Integer protocolId;
    
    private Integer qualityLevelId;
    
    private Integer reservedLevelId;
    
    private Integer roleId;
    
    private Integer routeModeId;
    
    private Integer signalTypeId;
    
    private BigDecimal useDeptId;
    
    private Integer wavelengthId;
    
    private Integer workStateId;
    
    private Integer zAccessModeId;
    
    private String zMatainContactDetail;
    
    private BigDecimal zMntDeptId;
    
    private Object zTextRoute;
    
    private BigDecimal zUseDeptId;
    
    private Integer reservedTypeId;
    
    private Integer dataSourceId;
    
    private BigDecimal aLeafRegionId;
    
    private BigDecimal zLeafRegionId;
    
    private Integer locationNatureId;
    
    private BigDecimal leafRegionId;
    
    private BigDecimal facilityId;
    
    private Integer isValid;
    /**
     * 原实体ID
     */
    private String oldId;
    /**
     * 电路编码
     */
    private Object circuitCode;
    
    private Date netTime;

}

