package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 链路缆线关系(CrLinkCableNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CrLinkCableNet implements Serializable {
    private static final long serialVersionUID = -52891801930513461L;
    /**
     * 关联ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 关系规格ID
     */
    private Long specId;
    /**
     * 链路ID
     */
    private BigDecimal linkId;
    /**
     * 缆线ID
     */
    private BigDecimal cableId;
    /**
     * 路由顺序
     */
    private Object seq;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 链路规格ID
     */
    private Long linkSpecId;
    /**
     * 缆线规格ID
     */
    private Long cableSpecId;
    /**
     * 其它状态ID
     */
    private Object otherStateId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getLinkId() {
        return linkId;
    }

    public void setLinkId(BigDecimal linkId) {
        this.linkId = linkId;
    }

    public BigDecimal getCableId() {
        return cableId;
    }

    public void setCableId(BigDecimal cableId) {
        this.cableId = cableId;
    }

    public Object getSeq() {
        return seq;
    }

    public void setSeq(Object seq) {
        this.seq = seq;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public Long getLinkSpecId() {
        return linkSpecId;
    }

    public void setLinkSpecId(Long linkSpecId) {
        this.linkSpecId = linkSpecId;
    }

    public Long getCableSpecId() {
        return cableSpecId;
    }

    public void setCableSpecId(Long cableSpecId) {
        this.cableSpecId = cableSpecId;
    }

    public Object getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Object otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

