package com.telecom.nrm.entity;

public class Result<T> {
    /**
     * 结果编码；200成功；500失败
     */
    private Integer code;

    /**
     * 返回结果描述
     */
    private String msg;


    /**
     * 数据
     */
    private T data;


    public Result() {

    }

    public static <T> Result<T> instance( T data, Integer code, String msg) {
        Result<T> result = new Result();
        result.setCode(code);
        result.setData(data);
        result.setMsg(msg);
        return result;
    }

    public static <T> Result<T> ok(T data){
        return instance(data,200, null);
    }
    public static <T> Result<T> fail(String msg){
        return instance(null,500, msg);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
