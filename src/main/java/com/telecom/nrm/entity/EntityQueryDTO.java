package com.telecom.nrm.entity;

import lombok.Data;

@Data
public class EntityQueryDTO {

    /**
     * 分页大小
     */
    private String size;

    /**
     * 页码
     */
    private String page;

    /**
     * 省份编码（采用ppm编码）
     */
    private String provinceCode;

    /**
     * 地市编码（PPM编码）
     */
    private String cityCode;

    /**
     * 设备网络角色
     */
    private String networkRole;

    /**
     * 设备/端口名称
     */
    private String name;

    /**
     * 设备管理IP
     */
    private String nmIp;

    /**
     * 设备/端口ID
     */
    private String id;

}
