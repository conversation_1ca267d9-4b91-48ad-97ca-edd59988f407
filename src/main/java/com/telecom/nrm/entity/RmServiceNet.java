package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品服务(RmServiceNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:04:30
 */
public class RmServiceNet implements Serializable {
    private static final long serialVersionUID = -53259874409846862L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 启用日期
     */
    private Date startUseDate;
    /**
     * 停用日期
     */
    private Date stopUseDate;
    /**
     * 业务状态ID
     */
    private Integer usingStateId;
    /**
     * 产品服务类型ID
     */
    private Integer serviceTypeId;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 接入号码
     */
    private String accessCode;
    /**
     * A端接入号码
     */
    private String aAccessCode;
    /**
     * Z端接入号码
     */
    private String zAccessCode;
    /**
     * 区域ID
     */
    private BigDecimal areaId;
    /**
     * 选址ID
     */
    private BigDecimal addressId;
    /**
     * A端选址ID
     */
    private BigDecimal aAddressId;
    /**
     * Z端选址ID
     */
    private BigDecimal zAddressId;
    
    private Integer serviceVersionId;
    
    private Integer serviceRoleId;
    
    private BigDecimal tmlId;
    
    private Integer paymentTypeId;
    
    private Integer zNetStandardId;
    
    private BigDecimal netStandardId;
    
    private Integer aNetStandardId;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    
    private Integer isValid;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    
    private Integer otherState;
    
    private BigDecimal aCustomerId;
    
    private BigDecimal zCustomerId;
    
    private BigDecimal aAreaId;
    
    private BigDecimal zAreaId;
    
    private BigDecimal customerId;
    /**
     * 原实体ID
     */
    private String oldId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public Date getStartUseDate() {
        return startUseDate;
    }

    public void setStartUseDate(Date startUseDate) {
        this.startUseDate = startUseDate;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Integer serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public String getAccessCode() {
        return accessCode;
    }

    public void setAccessCode(String accessCode) {
        this.accessCode = accessCode;
    }

    public String getAAccessCode() {
        return aAccessCode;
    }

    public void setAAccessCode(String aAccessCode) {
        this.aAccessCode = aAccessCode;
    }

    public String getZAccessCode() {
        return zAccessCode;
    }

    public void setZAccessCode(String zAccessCode) {
        this.zAccessCode = zAccessCode;
    }

    public BigDecimal getAreaId() {
        return areaId;
    }

    public void setAreaId(BigDecimal areaId) {
        this.areaId = areaId;
    }

    public BigDecimal getAddressId() {
        return addressId;
    }

    public void setAddressId(BigDecimal addressId) {
        this.addressId = addressId;
    }

    public BigDecimal getAAddressId() {
        return aAddressId;
    }

    public void setAAddressId(BigDecimal aAddressId) {
        this.aAddressId = aAddressId;
    }

    public BigDecimal getZAddressId() {
        return zAddressId;
    }

    public void setZAddressId(BigDecimal zAddressId) {
        this.zAddressId = zAddressId;
    }

    public Integer getServiceVersionId() {
        return serviceVersionId;
    }

    public void setServiceVersionId(Integer serviceVersionId) {
        this.serviceVersionId = serviceVersionId;
    }

    public Integer getServiceRoleId() {
        return serviceRoleId;
    }

    public void setServiceRoleId(Integer serviceRoleId) {
        this.serviceRoleId = serviceRoleId;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public Integer getPaymentTypeId() {
        return paymentTypeId;
    }

    public void setPaymentTypeId(Integer paymentTypeId) {
        this.paymentTypeId = paymentTypeId;
    }

    public Integer getZNetStandardId() {
        return zNetStandardId;
    }

    public void setZNetStandardId(Integer zNetStandardId) {
        this.zNetStandardId = zNetStandardId;
    }

    public BigDecimal getNetStandardId() {
        return netStandardId;
    }

    public void setNetStandardId(BigDecimal netStandardId) {
        this.netStandardId = netStandardId;
    }

    public Integer getANetStandardId() {
        return aNetStandardId;
    }

    public void setANetStandardId(Integer aNetStandardId) {
        this.aNetStandardId = aNetStandardId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Integer getOtherState() {
        return otherState;
    }

    public void setOtherState(Integer otherState) {
        this.otherState = otherState;
    }

    public BigDecimal getACustomerId() {
        return aCustomerId;
    }

    public void setACustomerId(BigDecimal aCustomerId) {
        this.aCustomerId = aCustomerId;
    }

    public BigDecimal getZCustomerId() {
        return zCustomerId;
    }

    public void setZCustomerId(BigDecimal zCustomerId) {
        this.zCustomerId = zCustomerId;
    }

    public BigDecimal getAAreaId() {
        return aAreaId;
    }

    public void setAAreaId(BigDecimal aAreaId) {
        this.aAreaId = aAreaId;
    }

    public BigDecimal getZAreaId() {
        return zAreaId;
    }

    public void setZAreaId(BigDecimal zAreaId) {
        this.zAreaId = zAreaId;
    }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

