package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 链路链路关系(CrLinkLinkNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CrLinkLinkNet implements Serializable {
    private static final long serialVersionUID = -63148543376409315L;
    /**
     * 关联ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 关系规格ID
     */
    private Long specId;
    /**
     * 上级链路ID
     */
    private BigDecimal upperLinkId;
    /**
     * 下级链路ID
     */
    private BigDecimal lowerLinkId;
    /**
     * 路由顺序
     */
    private Object seq;
    /**
     * A端端口ID
     */
    private BigDecimal aPortId;
    /**
     * Z端端口ID
     */
    private BigDecimal zPortId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 上级链路规格ID
     */
    private Long upperLinkSpecId;
    /**
     * 下级链路规格ID
     */
    private Long lowerLinkSpecId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    
    private Object wayType;
    
    private Object dataSourceId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getUpperLinkId() {
        return upperLinkId;
    }

    public void setUpperLinkId(BigDecimal upperLinkId) {
        this.upperLinkId = upperLinkId;
    }

    public BigDecimal getLowerLinkId() {
        return lowerLinkId;
    }

    public void setLowerLinkId(BigDecimal lowerLinkId) {
        this.lowerLinkId = lowerLinkId;
    }

    public Object getSeq() {
        return seq;
    }

    public void setSeq(Object seq) {
        this.seq = seq;
    }

    public BigDecimal getAPortId() {
        return aPortId;
    }

    public void setAPortId(BigDecimal aPortId) {
        this.aPortId = aPortId;
    }

    public BigDecimal getZPortId() {
        return zPortId;
    }

    public void setZPortId(BigDecimal zPortId) {
        this.zPortId = zPortId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public Long getUpperLinkSpecId() {
        return upperLinkSpecId;
    }

    public void setUpperLinkSpecId(Long upperLinkSpecId) {
        this.upperLinkSpecId = upperLinkSpecId;
    }

    public Long getLowerLinkSpecId() {
        return lowerLinkSpecId;
    }

    public void setLowerLinkSpecId(Long lowerLinkSpecId) {
        this.lowerLinkSpecId = lowerLinkSpecId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Object getWayType() {
        return wayType;
    }

    public void setWayType(Object wayType) {
        this.wayType = wayType;
    }

    public Object getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Object dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

