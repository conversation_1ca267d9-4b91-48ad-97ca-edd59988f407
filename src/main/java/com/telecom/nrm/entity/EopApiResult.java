package com.telecom.nrm.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR> humiao
 * @version : 1.0
 * @description : Eop接口调用结果
 * @date : Created in 2022/5/12 9:35
 * @modified by :
 * @lastModifiedDate :
 */
public class EopApiResult<T> {
    //api调用结果
    private boolean status;
    //api调用详情
    private String desc;
    //回参数据
    private T returnData;
    //class<T>
    private Class<T> clazz;

    public EopApiResult(Class<T> clazz){
        this.clazz = clazz;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public T getReturnData() {
        return returnData;
    }

    public void setReturnData(T returnData) {
        this.returnData = returnData;
    }

    public void setReturnDataInString(String str){
        JSONObject json = JSON.parseObject(str);
        T t = JSONObject.toJavaObject(json,clazz);
        setReturnData(t);
    }

    @Override
    public String toString() {
        return "EopApiResult{" +
                "status=" + status +
                ", desc='" + desc + '\'' +
                ", returnData=" + returnData +
                '}';
    }
}
