package com.telecom.nrm.entity;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

@Setter
@Getter
public class PmProductLinkAnalysisDTO {


    @ExcelProperty("接入号")
    private String accessCode; // 接入号


    @ExcelProperty("地市")
    private String regionName; // 地市

    @ExcelProperty("产品类型")
    private String productType; // 产品类型


    @ExcelProperty("电路编号")
    private String linkCode; // 电路编号

    @ExcelProperty("cjtype")
    private String cjtype; // cjtype

    @ExcelProperty("基础校验")
    private String basicCheck; // 基础校验

    @ExcelProperty("增强校验")
    private String enhanceCheck; // 增强校验


    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("更新时间")
    private String updateDate; // 更新时间时间

    @ExcelProperty("稽核状态")
    private String checkState; // 稽核状态

    @ExcelProperty("稽核结果")
    private String msg; // 稽核结果


    public void setUpdateDate(String updateDate) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", java.util.Locale.ENGLISH);
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = inputFormat.parse(updateDate);
            this.updateDate = outputFormat.format(date);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void setCheckState(String checkState) {
        if (StringUtils.isNotEmpty(checkState)) {
            switch (checkState) {
                case "I":
                    this.checkState = "待稽核";
                    break;
                case "S":
                    this.checkState = "稽核成功";
                    break;
                case "E":
                    this.checkState = "稽核失败";
                    break;
                default:
                    this.checkState = checkState;
            }
        }

    }

    public void setEnhanceCheck(String enhanceCheck) {
        if (StringUtils.isNotEmpty(enhanceCheck)) {
            switch (enhanceCheck) {
                case "I":
                    this.enhanceCheck = "待稽核";
                    break;
                case "S":
                    this.enhanceCheck = "稽核成功";
                    break;
                case "E":
                    this.enhanceCheck = "稽核失败";
                    break;
                default:
                    this.enhanceCheck = enhanceCheck;
            }
        }

    }

    public void setBasicCheck(String basicCheck) {

        if (StringUtils.isNotEmpty(basicCheck)) {
            switch (basicCheck) {
                case "I":
                    this.basicCheck = "待稽核";
                    break;
                case "S":
                    this.basicCheck = "稽核成功";
                    break;
                case "E":
                    this.basicCheck = "稽核失败";
                    break;
                default:
                    this.basicCheck = basicCheck;
            }
        }

    }
}
