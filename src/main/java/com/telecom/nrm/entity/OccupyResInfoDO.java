package com.telecom.nrm.entity;

import lombok.Data;

@Data
public class OccupyResInfoDO {
    /**
     * 缺失资源类型
     */
    private String deficiencyResType;

    /**
     * 省份ppmCode
     */
    private String provinceCode;

    /**
     * 地市ppmCode
     */
    private String cityCode;

    /**
     * 缺失资源ID
     */
    private String deficiencyResId;

    /**
     * 缺失资源名称
     */
    private String deficiencyResName;

    /**
     * 缺失资源段ID
     */
    private String deficiencySegId;

    /**
     * 缺失资源段名称
     */
    private String deficiencySegName;

    /**
     * 缺失资源段起始局站Id
     */
    private String deficiencySegStationId;

    /**
     * 缺失资源段起始局站名称
     */
    private String deficiencySegStationName;

    /**
     * 缺失资源段结束局站Id
     */
    private String deficiencySegNextStationId;

    /**
     * 缺失资源段结束局站名称
     */
    private String deficiencySegNextStationName;

    /**
     * 占用资源类型
     */
    private String occupyResType;

    /**
     * 占用资源Id
     */
    private String occupyResPId;

    /**
     * 占用资源名称
     */
    private String occupyResPName;

    /**
     * 占用资源编码
     */
    private String occupyResPCode;


    /**
     * 占用逻辑资源Id
     */
    private String occupyResId;

    /**
     * 占用逻辑资源名称
     */
    private String occupyResName;

    /**
     * 占用逻辑资源编码
     */
    private String occupyResCode;


    /**
     * a端端口局站Id
     */
    private String aStationId;

    /**
     * a端端口局站名称
     */
    private String aStationName;

    /**
     * a端端口设备Id
     */
    private String aDeviceId;

    /**
     * a端端口设备名称
     */
    private String aDeviceName;

    /**
     * a端端口Id
     */
    private String aPortId;

    /**
     * a端端口名称
     */
    private String aPortName;

    /**
     * z端端口局站Id
     */
    private String zStationId;

    /**
     * z端端口局站名称
     */
    private String zStationName;

    /**
     * z端端口设备Id
     */
    private String zDeviceId;

    /**
     * z端端口设备名称
     */
    private String zDeviceName;

    /**
     * z端端口Id
     */
    private String zPortId;

    /**
     * z端端口名称
     */
    private String zPortName;


}
