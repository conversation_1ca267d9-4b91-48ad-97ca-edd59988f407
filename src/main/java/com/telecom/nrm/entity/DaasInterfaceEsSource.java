package com.telecom.nrm.entity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Date;

@Data
public class DaasInterfaceEsSource {
    @JsonProperty("app-id")
    private String appId;

    @JsonProperty("@timestamp")
    private Date timestamp;

    private String rest;

    @JsonProperty("class")
    private String className;

    @JsonProperty("@timestamp-service")
    private Date timestampService;

    private String stackTrace;
    private String severity;
    private String shardingCode;
    private String trace;

    @JsonProperty("service-name")
    private String serviceName;

    private String thread;

    @JsonProperty("api-code")
    private String apiCode;

    private String pid;
    private String service;

}
