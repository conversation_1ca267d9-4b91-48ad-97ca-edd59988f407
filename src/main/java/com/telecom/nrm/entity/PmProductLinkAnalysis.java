package com.telecom.nrm.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
public class PmProductLinkAnalysis {

    @JsonProperty("id")
    private Long id;


    @JsonProperty("accessCode")
    private String accessCode; // 接入号

    @JsonProperty("serviceId")
    private BigDecimal serviceId;


    @JsonProperty("regionName")
    private String regionName; // 地市


    @JsonProperty("productType")
    private String productType; // 产品类型

    @JsonProperty("regionId")
    private BigDecimal regionId;

    @JsonProperty("linkId")
    private BigDecimal linkId;


    @JsonProperty("linkCode")
    private String linkCode; // 电路编号


    @JsonProperty("cjtype")
    private String cjtype; // cjtype

    @JsonProperty("data")
    private String data;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("createDate")
    private Date createDate;

    @JsonProperty("basicCheck")
    private String basicCheck; // 基础校验


    @JsonProperty("enhanceCheck")
    private String enhanceCheck; // 增强校验
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("checkTime")
    private Date checkTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("updateDate")
    private Date updateDate; // 更新时间


    private String productDate; // 归档时间, api限制

    @JsonProperty("checkState")
    private String checkState; // 稽核状态


    @JsonProperty("msg")
    private String msg; // 稽核结果

    @JsonProperty("jtMsg")
    private String jtMsg; // 集团返回

    @JsonProperty("crmProductId")
    private BigDecimal crmProductId;

    @JsonProperty("crmProductSpecId")
    private BigDecimal crmProductSpecId;

    @JsonProperty("aProvinceId")
    private BigDecimal aProvinceId;
    @JsonProperty("zProvinceId")
    private BigDecimal zProvinceId;
    @JsonProperty("aRegionId")
    private BigDecimal aRegionId;
    @JsonProperty("zRegionId")
    private BigDecimal zRegionId;

    @JsonProperty("provinceAuditState")
    private String provinceAuditState;

    @JsonProperty("longDistanceType")
    private String longDistanceType;

    @JsonProperty("aAreaName")
    private String aAreaName;

    @JsonProperty("zAreaName")
    private String zAreaName;


    @JsonProperty("aCityId")
    private BigDecimal aCityId;
    @JsonProperty("zCityId")
    private BigDecimal zCityId;


    @JsonProperty("productState")
    private String productState;



    private JSONArray initLinks ;

    private JSONArray initPorts;

    private JSONArray initDevices;

    private JSONArray initRooms;

    private JSONArray initStations;

    private JSONArray initAddresses;

    private JSONArray initIps;

    private JSONArray initWares; // 板卡

    private JSONObject cfs;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("provinceSuccessDate")
    private Date provinceSuccessDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("basicSuccessDate")
    private Date basicSuccessDate;

    // 组网模型
    @JsonProperty("netText")
    private String netText;


}
