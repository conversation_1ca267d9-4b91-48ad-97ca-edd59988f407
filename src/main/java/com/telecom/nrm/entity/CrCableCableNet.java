package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 缆线之间的关系(CrCableCableNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CrCableCableNet implements Serializable {
    private static final long serialVersionUID = 123192065170624955L;
    /**
     * 关联ID
     */
    private BigDecimal id;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 关系规格ID
     */
    private Long specId;
    /**
     * 上级缆线ID
     */
    private BigDecimal upperCableId;
    /**
     * 下级缆线ID
     */
    private BigDecimal lowerCableId;
    /**
     * 路由顺序
     */
    private Object seq;
    /**
     * 熔接设备ID
     */
    private BigDecimal deviceId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 熔接方式ID
     */
    private Integer spliceModeId;
    /**
     * 上级缆线规格ID
     */
    private Long upperCableSpecId;
    /**
     * 下级缆线规格ID
     */
    private Long lowerCableSpecId;
    /**
     * 其它状态ID
     */
    private Object otherStateId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getUpperCableId() {
        return upperCableId;
    }

    public void setUpperCableId(BigDecimal upperCableId) {
        this.upperCableId = upperCableId;
    }

    public BigDecimal getLowerCableId() {
        return lowerCableId;
    }

    public void setLowerCableId(BigDecimal lowerCableId) {
        this.lowerCableId = lowerCableId;
    }

    public Object getSeq() {
        return seq;
    }

    public void setSeq(Object seq) {
        this.seq = seq;
    }

    public BigDecimal getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(BigDecimal deviceId) {
        this.deviceId = deviceId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Integer getSpliceModeId() {
        return spliceModeId;
    }

    public void setSpliceModeId(Integer spliceModeId) {
        this.spliceModeId = spliceModeId;
    }

    public Long getUpperCableSpecId() {
        return upperCableSpecId;
    }

    public void setUpperCableSpecId(Long upperCableSpecId) {
        this.upperCableSpecId = upperCableSpecId;
    }

    public Long getLowerCableSpecId() {
        return lowerCableSpecId;
    }

    public void setLowerCableSpecId(Long lowerCableSpecId) {
        this.lowerCableSpecId = lowerCableSpecId;
    }

    public Object getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Object otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

