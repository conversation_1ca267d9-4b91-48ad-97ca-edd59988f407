package com.telecom.nrm.entity;

import lombok.Data;

@Data
public class CmPort {

    /**
     * 端口id
     */
    private String id;

    /**
     * 端口编码
     */
    private String code;

    /**
     * 端口名称
     */
    private String name;

    /**
     * 端口类型
     */
    private String portType;

    /**
     * 端口角色
     */
    private String roleId;

    /**
     * 端口规格
     */
    private String specId;

    /**
     * 最大接入速率
     */
    private String maxAccessRate;

    /**
     * 业务状态
     */
    private String serviceFeatures;

    /**
     * 虚实属性
     */
    private String virtualReal;


}
