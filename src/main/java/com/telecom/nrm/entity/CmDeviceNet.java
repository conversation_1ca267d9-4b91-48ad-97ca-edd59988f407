package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * (CmDeviceNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:44
 */
public class CmDeviceNet implements Serializable {
    private static final long serialVersionUID = 566864427788757571L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 集团编码
     */
    private String standardCode;
    /**
     * 集团名称
     */
    private String standardName;
    /**
     * 别名
     */
    private String alias;
    /**
     * 所属设施ID
     */
    private BigDecimal facilityId;
    /**
     * 安装时间
     */
    private Date installDate;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 入网日期
     */
    private Date intoNetworkDate;
    /**
     * 启用日期
     */
    private Date startUseDate;
    /**
     * 停用日期
     */
    private Date stopUseDate;
    /**
     * 退网日期
     */
    private Date exitNetworkDate;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 生命周期状态ID
     */
    private Integer lifeStateId;
    /**
     * 物理状态ID
     */
    private Integer physicalStateId;
    /**
     * 业务状态ID
     */
    private Integer usingStateId;
    /**
     * 封锁状态ID
     */
    private Integer lockStateId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 设备类型ID
     */
    private Integer deviceTypeId;
    /**
     * 型号ID
     */
    private BigDecimal modelId;
    /**
     * 生产厂家ID
     */
    private BigDecimal manufactorId;
    /**
     * 排列方式ID
     */
    private Integer arrangementId;
    /**
     * 行数
     */
    private Object rowNum;
    /**
     * 列数
     */
    private Object colNum;
    /**
     * 软件版本号
     */
    private String softwareVersion;
    /**
     * 网管IP
     */
    private String nmIp;
    /**
     * 长本属性ID
     */
    private Integer longLocalId;
    /**
     * 网络层次ID
     */
    private Integer networkLayerId;
    /**
     * 网络角色ID
     */
    private Integer networkRoleId;
    /**
     * 户端局用ID
     */
    private Integer userOfficeId;
    /**
     * 组网平面ID
     */
    private Integer networkPlaneId;
    /**
     * 硬件版本号
     */
    private String hardwareVersion;
    /**
     * 虚实属性ID
     */
    private Integer virtualRealId;
    /**
     * 标称容量
     */
    private Object capacity;
    /**
     * 最大容量
     */
    private Object maxCapacity;
    /**
     * 地址ID
     */
    private BigDecimal addressId;
    /**
     * 方位信息
     */
    private String addressDesc;
    /**
     * 所属电信管理区ID
     */
    private BigDecimal tmlId;
    /**
     * 所属地区ID
     */
    private BigDecimal regionId;
    /**
     * 安装方式ID
     */
    private Integer installWayId;
    /**
     * 安装位置类型ID
     */
    private Integer installPositionTypeId;
    /**
     * 安装依附对象类型ID
     */
    private Integer installSupporterTypeId;
    /**
     * 维护人ID
     */
    private BigDecimal mntPersonId;
    /**
     * 维护方式ID
     */
    private Integer mntWayId;
    /**
     * 数据采集单位ID
     */
    private BigDecimal collectionDeptId;
    /**
     * 数据采集人ID
     */
    private BigDecimal collectionPersonId;
    /**
     * 数据采集日期
     */
    private Date collectionDate;
    /**
     * 检查责任人ID
     */
    private BigDecimal checkingPersonId;
    /**
     * 维护部门ID
     */
    private BigDecimal mntDeptId;
    /**
     * 代维单位ID
     */
    private BigDecimal agentMntCompanyId;
    /**
     * 代维联系人
     */
    private String agentMntContact;
    /**
     * 代维电话
     */
    private String agentMntPhone;
    /**
     * 集中监控标志ID
     */
    private Integer monitorFlagId;
    /**
     * 维护等级ID
     */
    private Integer mntLevelId;
    /**
     * 保修开始时间
     */
    private Date warrantyStartDate;
    /**
     * 保修结束时间
     */
    private Date warrantyEndDate;
    /**
     * 供货商ID
     */
    private BigDecimal vendorId;
    /**
     * 使用部门ID
     */
    private BigDecimal usingDepartmentId;
    /**
     * 使用年限
     */
    private BigDecimal usefulLife;
    /**
     * 产权性质ID
     */
    private Integer propertyTypeId;
    /**
     * 租用合同号
     */
    private String rentContractNo;
    /**
     * 有效期限
     */
    private String lifespan;
    /**
     * 产权归属ID
     */
    private Integer propertyOwnerId;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 接入方式ID
     */
    private Integer accessModeId;
    /**
     * 应用网络ID
     */
    private Integer applyNetId;
    /**
     * 网管系统编码
     */
    private String codeinems;
    /**
     * 离地高度
     */
    private BigDecimal groundHeight;
    /**
     * 高度（米）
     */
    private BigDecimal height;
    /**
     * 所属范围
     */
    private String inoutplant;
    /**
     * 是否虚拟
     */
    private Integer isVirtual;
    /**
     * 长度（米）
     */
    private BigDecimal length;
    /**
     * 设备占用License的情况
     */
    private String licenseOccupation;
    /**
     * MAC地址
     */
    private String mac;
    /**
     * 网管系统名称
     */
    private String nameinems;
    /**
     * 子网掩码
     */
    private String netmask;
    /**
     * 网管中的编码
     */
    private String nmCode;
    /**
     * 网管中的标识ID
     */
    private BigDecimal nmId;
    /**
     * 所属网络ID
     */
    private Integer ownerNetId;
    /**
     * 电源来源性质ID
     */
    private Integer powerSourceId;
    /**
     * 电源性质ID
     */
    private Integer powerTypeId;
    /**
     * 保护方式ID
     */
    private Integer protectWayId;
    /**
     * 依赖端口ID
     */
    private BigDecimal relayPortId;
    /**
     * 安全等级ID
     */
    private Integer securityLevelId;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 服务等级ID
     */
    private Integer serviceLevelId;
    /**
     * 设备使用共用性ID
     */
    private Integer sharedModeId;
    /**
     * 上行方式ID
     */
    private Integer upcontactWayId;
    /**
     * 保修期
     */
    private String warrantyPeriod;
    /**
     * 宽度
     */
    private BigDecimal width;
    /**
     * 工作状态ID
     */
    private Integer workStateId;
    /**
     * 工作方式ID
     */
    private Integer workWayId;
    /**
     * TELNET端口
     */
    private String telnetPort;
    /**
     * TELNET登录信息
     */
    private String telnetLoginInfo;
    /**
     * TelnetIP地址
     */
    private String telnetIp;
    /**
     * 设备形态ID
     */
    private Integer deviceShapeId;
    /**
     * 作用对象ID
     */
    private Integer userObjectId;
    /**
     * 实物ID
     */
    private BigDecimal materialObjectId;
    /**
     * 行号
     */
    private String rowNo;
    /**
     * 列号
     */
    private String colNo;
    /**
     * X坐标
     */
    private Object posX;
    /**
     * Y坐标
     */
    private Object posY;
    
    private BigDecimal templetId;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    /**
     * 维护人联系方式
     */
    private Object maintainerTele;
    /**
     * 维保类型ID
     */
    private Integer maintenanceTypeId;
    /**
     * 保修级别ID
     */
    private Integer warrantyLevelId;
    /**
     * 尺寸（长mm*宽mm*高mm）
     */
    private String deviceSize;
    
    private BigDecimal leafRegionId;
    
    private Integer isValid;
    /**
     * 序号
     */
    private Integer seq;
    
    private Integer supplyType;
    /**
     * 网管名称
     */
    private String nmName;
    /**
     * 原实体ID
     */
    private String oldId;
    /**
     * 数据来源系统ID
     */
    private Object dataSourceSysId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public BigDecimal getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(BigDecimal facilityId) {
        this.facilityId = facilityId;
    }

    public Date getInstallDate() {
        return installDate;
    }

    public void setInstallDate(Date installDate) {
        this.installDate = installDate;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getIntoNetworkDate() {
        return intoNetworkDate;
    }

    public void setIntoNetworkDate(Date intoNetworkDate) {
        this.intoNetworkDate = intoNetworkDate;
    }

    public Date getStartUseDate() {
        return startUseDate;
    }

    public void setStartUseDate(Date startUseDate) {
        this.startUseDate = startUseDate;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public Date getExitNetworkDate() {
        return exitNetworkDate;
    }

    public void setExitNetworkDate(Date exitNetworkDate) {
        this.exitNetworkDate = exitNetworkDate;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public Integer getLifeStateId() {
        return lifeStateId;
    }

    public void setLifeStateId(Integer lifeStateId) {
        this.lifeStateId = lifeStateId;
    }

    public Integer getPhysicalStateId() {
        return physicalStateId;
    }

    public void setPhysicalStateId(Integer physicalStateId) {
        this.physicalStateId = physicalStateId;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Integer getDeviceTypeId() {
        return deviceTypeId;
    }

    public void setDeviceTypeId(Integer deviceTypeId) {
        this.deviceTypeId = deviceTypeId;
    }

    public BigDecimal getModelId() {
        return modelId;
    }

    public void setModelId(BigDecimal modelId) {
        this.modelId = modelId;
    }

    public BigDecimal getManufactorId() {
        return manufactorId;
    }

    public void setManufactorId(BigDecimal manufactorId) {
        this.manufactorId = manufactorId;
    }

    public Integer getArrangementId() {
        return arrangementId;
    }

    public void setArrangementId(Integer arrangementId) {
        this.arrangementId = arrangementId;
    }

    public Object getRowNum() {
        return rowNum;
    }

    public void setRowNum(Object rowNum) {
        this.rowNum = rowNum;
    }

    public Object getColNum() {
        return colNum;
    }

    public void setColNum(Object colNum) {
        this.colNum = colNum;
    }

    public String getSoftwareVersion() {
        return softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public String getNmIp() {
        return nmIp;
    }

    public void setNmIp(String nmIp) {
        this.nmIp = nmIp;
    }

    public Integer getLongLocalId() {
        return longLocalId;
    }

    public void setLongLocalId(Integer longLocalId) {
        this.longLocalId = longLocalId;
    }

    public Integer getNetworkLayerId() {
        return networkLayerId;
    }

    public void setNetworkLayerId(Integer networkLayerId) {
        this.networkLayerId = networkLayerId;
    }

    public Integer getNetworkRoleId() {
        return networkRoleId;
    }

    public void setNetworkRoleId(Integer networkRoleId) {
        this.networkRoleId = networkRoleId;
    }

    public Integer getUserOfficeId() {
        return userOfficeId;
    }

    public void setUserOfficeId(Integer userOfficeId) {
        this.userOfficeId = userOfficeId;
    }

    public Integer getNetworkPlaneId() {
        return networkPlaneId;
    }

    public void setNetworkPlaneId(Integer networkPlaneId) {
        this.networkPlaneId = networkPlaneId;
    }

    public String getHardwareVersion() {
        return hardwareVersion;
    }

    public void setHardwareVersion(String hardwareVersion) {
        this.hardwareVersion = hardwareVersion;
    }

    public Integer getVirtualRealId() {
        return virtualRealId;
    }

    public void setVirtualRealId(Integer virtualRealId) {
        this.virtualRealId = virtualRealId;
    }

    public Object getCapacity() {
        return capacity;
    }

    public void setCapacity(Object capacity) {
        this.capacity = capacity;
    }

    public Object getMaxCapacity() {
        return maxCapacity;
    }

    public void setMaxCapacity(Object maxCapacity) {
        this.maxCapacity = maxCapacity;
    }

    public BigDecimal getAddressId() {
        return addressId;
    }

    public void setAddressId(BigDecimal addressId) {
        this.addressId = addressId;
    }

    public String getAddressDesc() {
        return addressDesc;
    }

    public void setAddressDesc(String addressDesc) {
        this.addressDesc = addressDesc;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public Integer getInstallWayId() {
        return installWayId;
    }

    public void setInstallWayId(Integer installWayId) {
        this.installWayId = installWayId;
    }

    public Integer getInstallPositionTypeId() {
        return installPositionTypeId;
    }

    public void setInstallPositionTypeId(Integer installPositionTypeId) {
        this.installPositionTypeId = installPositionTypeId;
    }

    public Integer getInstallSupporterTypeId() {
        return installSupporterTypeId;
    }

    public void setInstallSupporterTypeId(Integer installSupporterTypeId) {
        this.installSupporterTypeId = installSupporterTypeId;
    }

    public BigDecimal getMntPersonId() {
        return mntPersonId;
    }

    public void setMntPersonId(BigDecimal mntPersonId) {
        this.mntPersonId = mntPersonId;
    }

    public Integer getMntWayId() {
        return mntWayId;
    }

    public void setMntWayId(Integer mntWayId) {
        this.mntWayId = mntWayId;
    }

    public BigDecimal getCollectionDeptId() {
        return collectionDeptId;
    }

    public void setCollectionDeptId(BigDecimal collectionDeptId) {
        this.collectionDeptId = collectionDeptId;
    }

    public BigDecimal getCollectionPersonId() {
        return collectionPersonId;
    }

    public void setCollectionPersonId(BigDecimal collectionPersonId) {
        this.collectionPersonId = collectionPersonId;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public BigDecimal getCheckingPersonId() {
        return checkingPersonId;
    }

    public void setCheckingPersonId(BigDecimal checkingPersonId) {
        this.checkingPersonId = checkingPersonId;
    }

    public BigDecimal getMntDeptId() {
        return mntDeptId;
    }

    public void setMntDeptId(BigDecimal mntDeptId) {
        this.mntDeptId = mntDeptId;
    }

    public BigDecimal getAgentMntCompanyId() {
        return agentMntCompanyId;
    }

    public void setAgentMntCompanyId(BigDecimal agentMntCompanyId) {
        this.agentMntCompanyId = agentMntCompanyId;
    }

    public String getAgentMntContact() {
        return agentMntContact;
    }

    public void setAgentMntContact(String agentMntContact) {
        this.agentMntContact = agentMntContact;
    }

    public String getAgentMntPhone() {
        return agentMntPhone;
    }

    public void setAgentMntPhone(String agentMntPhone) {
        this.agentMntPhone = agentMntPhone;
    }

    public Integer getMonitorFlagId() {
        return monitorFlagId;
    }

    public void setMonitorFlagId(Integer monitorFlagId) {
        this.monitorFlagId = monitorFlagId;
    }

    public Integer getMntLevelId() {
        return mntLevelId;
    }

    public void setMntLevelId(Integer mntLevelId) {
        this.mntLevelId = mntLevelId;
    }

    public Date getWarrantyStartDate() {
        return warrantyStartDate;
    }

    public void setWarrantyStartDate(Date warrantyStartDate) {
        this.warrantyStartDate = warrantyStartDate;
    }

    public Date getWarrantyEndDate() {
        return warrantyEndDate;
    }

    public void setWarrantyEndDate(Date warrantyEndDate) {
        this.warrantyEndDate = warrantyEndDate;
    }

    public BigDecimal getVendorId() {
        return vendorId;
    }

    public void setVendorId(BigDecimal vendorId) {
        this.vendorId = vendorId;
    }

    public BigDecimal getUsingDepartmentId() {
        return usingDepartmentId;
    }

    public void setUsingDepartmentId(BigDecimal usingDepartmentId) {
        this.usingDepartmentId = usingDepartmentId;
    }

    public BigDecimal getUsefulLife() {
        return usefulLife;
    }

    public void setUsefulLife(BigDecimal usefulLife) {
        this.usefulLife = usefulLife;
    }

    public Integer getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Integer propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }

    public String getRentContractNo() {
        return rentContractNo;
    }

    public void setRentContractNo(String rentContractNo) {
        this.rentContractNo = rentContractNo;
    }

    public String getLifespan() {
        return lifespan;
    }

    public void setLifespan(String lifespan) {
        this.lifespan = lifespan;
    }

    public Integer getPropertyOwnerId() {
        return propertyOwnerId;
    }

    public void setPropertyOwnerId(Integer propertyOwnerId) {
        this.propertyOwnerId = propertyOwnerId;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Integer getAccessModeId() {
        return accessModeId;
    }

    public void setAccessModeId(Integer accessModeId) {
        this.accessModeId = accessModeId;
    }

    public Integer getApplyNetId() {
        return applyNetId;
    }

    public void setApplyNetId(Integer applyNetId) {
        this.applyNetId = applyNetId;
    }

    public String getCodeinems() {
        return codeinems;
    }

    public void setCodeinems(String codeinems) {
        this.codeinems = codeinems;
    }

    public BigDecimal getGroundHeight() {
        return groundHeight;
    }

    public void setGroundHeight(BigDecimal groundHeight) {
        this.groundHeight = groundHeight;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public String getInoutplant() {
        return inoutplant;
    }

    public void setInoutplant(String inoutplant) {
        this.inoutplant = inoutplant;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public String getLicenseOccupation() {
        return licenseOccupation;
    }

    public void setLicenseOccupation(String licenseOccupation) {
        this.licenseOccupation = licenseOccupation;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getNameinems() {
        return nameinems;
    }

    public void setNameinems(String nameinems) {
        this.nameinems = nameinems;
    }

    public String getNetmask() {
        return netmask;
    }

    public void setNetmask(String netmask) {
        this.netmask = netmask;
    }

    public String getNmCode() {
        return nmCode;
    }

    public void setNmCode(String nmCode) {
        this.nmCode = nmCode;
    }

    public BigDecimal getNmId() {
        return nmId;
    }

    public void setNmId(BigDecimal nmId) {
        this.nmId = nmId;
    }

    public Integer getOwnerNetId() {
        return ownerNetId;
    }

    public void setOwnerNetId(Integer ownerNetId) {
        this.ownerNetId = ownerNetId;
    }

    public Integer getPowerSourceId() {
        return powerSourceId;
    }

    public void setPowerSourceId(Integer powerSourceId) {
        this.powerSourceId = powerSourceId;
    }

    public Integer getPowerTypeId() {
        return powerTypeId;
    }

    public void setPowerTypeId(Integer powerTypeId) {
        this.powerTypeId = powerTypeId;
    }

    public Integer getProtectWayId() {
        return protectWayId;
    }

    public void setProtectWayId(Integer protectWayId) {
        this.protectWayId = protectWayId;
    }

    public BigDecimal getRelayPortId() {
        return relayPortId;
    }

    public void setRelayPortId(BigDecimal relayPortId) {
        this.relayPortId = relayPortId;
    }

    public Integer getSecurityLevelId() {
        return securityLevelId;
    }

    public void setSecurityLevelId(Integer securityLevelId) {
        this.securityLevelId = securityLevelId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getServiceLevelId() {
        return serviceLevelId;
    }

    public void setServiceLevelId(Integer serviceLevelId) {
        this.serviceLevelId = serviceLevelId;
    }

    public Integer getSharedModeId() {
        return sharedModeId;
    }

    public void setSharedModeId(Integer sharedModeId) {
        this.sharedModeId = sharedModeId;
    }

    public Integer getUpcontactWayId() {
        return upcontactWayId;
    }

    public void setUpcontactWayId(Integer upcontactWayId) {
        this.upcontactWayId = upcontactWayId;
    }

    public String getWarrantyPeriod() {
        return warrantyPeriod;
    }

    public void setWarrantyPeriod(String warrantyPeriod) {
        this.warrantyPeriod = warrantyPeriod;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public Integer getWorkStateId() {
        return workStateId;
    }

    public void setWorkStateId(Integer workStateId) {
        this.workStateId = workStateId;
    }

    public Integer getWorkWayId() {
        return workWayId;
    }

    public void setWorkWayId(Integer workWayId) {
        this.workWayId = workWayId;
    }

    public String getTelnetPort() {
        return telnetPort;
    }

    public void setTelnetPort(String telnetPort) {
        this.telnetPort = telnetPort;
    }

    public String getTelnetLoginInfo() {
        return telnetLoginInfo;
    }

    public void setTelnetLoginInfo(String telnetLoginInfo) {
        this.telnetLoginInfo = telnetLoginInfo;
    }

    public String getTelnetIp() {
        return telnetIp;
    }

    public void setTelnetIp(String telnetIp) {
        this.telnetIp = telnetIp;
    }

    public Integer getDeviceShapeId() {
        return deviceShapeId;
    }

    public void setDeviceShapeId(Integer deviceShapeId) {
        this.deviceShapeId = deviceShapeId;
    }

    public Integer getUserObjectId() {
        return userObjectId;
    }

    public void setUserObjectId(Integer userObjectId) {
        this.userObjectId = userObjectId;
    }

    public BigDecimal getMaterialObjectId() {
        return materialObjectId;
    }

    public void setMaterialObjectId(BigDecimal materialObjectId) {
        this.materialObjectId = materialObjectId;
    }

    public String getRowNo() {
        return rowNo;
    }

    public void setRowNo(String rowNo) {
        this.rowNo = rowNo;
    }

    public String getColNo() {
        return colNo;
    }

    public void setColNo(String colNo) {
        this.colNo = colNo;
    }

    public Object getPosX() {
        return posX;
    }

    public void setPosX(Object posX) {
        this.posX = posX;
    }

    public Object getPosY() {
        return posY;
    }

    public void setPosY(Object posY) {
        this.posY = posY;
    }

    public BigDecimal getTempletId() {
        return templetId;
    }

    public void setTempletId(BigDecimal templetId) {
        this.templetId = templetId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public Object getMaintainerTele() {
        return maintainerTele;
    }

    public void setMaintainerTele(Object maintainerTele) {
        this.maintainerTele = maintainerTele;
    }

    public Integer getMaintenanceTypeId() {
        return maintenanceTypeId;
    }

    public void setMaintenanceTypeId(Integer maintenanceTypeId) {
        this.maintenanceTypeId = maintenanceTypeId;
    }

    public Integer getWarrantyLevelId() {
        return warrantyLevelId;
    }

    public void setWarrantyLevelId(Integer warrantyLevelId) {
        this.warrantyLevelId = warrantyLevelId;
    }

    public String getDeviceSize() {
        return deviceSize;
    }

    public void setDeviceSize(String deviceSize) {
        this.deviceSize = deviceSize;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getSupplyType() {
        return supplyType;
    }

    public void setSupplyType(Integer supplyType) {
        this.supplyType = supplyType;
    }

    public String getNmName() {
        return nmName;
    }

    public void setNmName(String nmName) {
        this.nmName = nmName;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Object getDataSourceSysId() {
        return dataSourceSysId;
    }

    public void setDataSourceSysId(Object dataSourceSysId) {
        this.dataSourceSysId = dataSourceSysId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

