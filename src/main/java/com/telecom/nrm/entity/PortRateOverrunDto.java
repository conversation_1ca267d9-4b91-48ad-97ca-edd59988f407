package com.telecom.nrm.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
//速率超限端口信息
public class PortRateOverrunDto{

    //端口ID
    private BigDecimal portId;

    //入向限速
    private String inSpeed;

    //出向限速
    private String outSpeed;

    //设备IP
    private String ipAddr;

    //IDC资源端口名称
    private String idcPortName;

    //采集端口名称
    private String portName;

    //配置速率
    private String confRate;

    //端口状态
    private String usingStateName;

    //业务编码
    private String businessCode;

    //机房名称
    private String facilityName;

    //数据中心名称
    private String dataCenterName;

    //设备名称
    private String deviceName;

    //设备编码
    private String deviceCode;

    //端口编码
    private String portCode;

    //客户名称
    private String custName;

    //稽核结果
    private String auditResult;

    private String name;

    private String regionName;

}
