package com.telecom.nrm.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 隐患整改实体类
 */
public class HazardRemediation {
    
    private BigDecimal id;
    
    // 状态信息
    private String status; // 状态：draft-发起, inProgress-整改中, completed-整改完
    private Date createTime; // 创建时间
    private Date updateTime; // 最后更新时间
    
    // 基本信息
    private String cityCode; // 地市代码
    private String hazardSource; // 隐患来源：selfDiscovered/businessGroup/deviceGroup
    private String hazardType; // 隐患类型
    private String hazardLevel; // 隐患等级：high/medium/low
    private String title; // 整改标题
    private String remediationDescription; // 整改描述及措施
    private Date expectedCompletionDate; // 预计完成时间
    private Date actualCompletionDate; // 实际完成时间
    private String responsiblePerson; // 责任人
    private String responsiblePersonContact; // 责任人联系方式
    private String remediationPerson; // 整改人
    private String remediationPersonContact; // 整改人联系方式
    
    // 保护组信息（设备保护组）
    private String protectionScenario; // 保护组场景
    private String customProtectionScenario; // 自定义保护组场景
    private String deviceType; // 设备类型
    private String customDeviceType; // 自定义设备类型
    private String selectedProtectionGroupId; // 选择的保护组ID
    
    // 业务保护组信息
    private String opticalPathGroupCode; // 光路组编码
    private String opticalPathGroupName; // 光路组名称
    
    // 生命线业务信息
    private String isLifeline; // 是否生命线业务：0-是，1-否
    private String lifelineBusinessId; // 生命线业务ID
    
    // 业务信息（业务保护组）
    private String circuitCode; // 电路编码
    private String circuitName; // 电路名称
    
    // 客户信息（业务保护组）
    private String customerName; // 客户名称
    private String customerAccount; // 客户账号
    private String customerManager; // 客户经理姓名
    private String customerManagerDepartment; // 客户经理部门
    private String customerLevel; // 客户等级
    private String differentiatedServiceLevel; // 差异化服务等级（2A-5A）
    
    // 自行发现相关字段
    private String selfDiscoveredDescription; // 自行发现说明
    private String discoveredBy; // 发现人
    private Date discoveredDate; // 发现时间
    
    // 创建人信息
    private String creator; // 创建人
    private String creatorId; // 创建人ID
    
    // 备注
    private String remark; // 备注信息
    
    // 关联数据（非数据库字段）
    private List<HazardRemediationOpticalPath> opticalPaths; // 关联的光路列表
    private List<HazardRemediationAttachment> attachments; // 关联的附件列表
    
    // 构造函数
    public HazardRemediation() {}
    
    // Getter和Setter方法
    public BigDecimal getId() {
        return id;
    }
    
    public void setId(BigDecimal id) {
        this.id = id;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getCityCode() {
        return cityCode;
    }
    
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }
    
    public String getHazardSource() {
        return hazardSource;
    }
    
    public void setHazardSource(String hazardSource) {
        this.hazardSource = hazardSource;
    }
    
    public String getHazardType() {
        return hazardType;
    }
    
    public void setHazardType(String hazardType) {
        this.hazardType = hazardType;
    }
    
    public String getHazardLevel() {
        return hazardLevel;
    }
    
    public void setHazardLevel(String hazardLevel) {
        this.hazardLevel = hazardLevel;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getRemediationDescription() {
        return remediationDescription;
    }
    
    public void setRemediationDescription(String remediationDescription) {
        this.remediationDescription = remediationDescription;
    }
    
    public Date getExpectedCompletionDate() {
        return expectedCompletionDate;
    }
    
    public void setExpectedCompletionDate(Date expectedCompletionDate) {
        this.expectedCompletionDate = expectedCompletionDate;
    }
    
    public Date getActualCompletionDate() {
        return actualCompletionDate;
    }
    
    public void setActualCompletionDate(Date actualCompletionDate) {
        this.actualCompletionDate = actualCompletionDate;
    }
    
    public String getResponsiblePerson() {
        return responsiblePerson;
    }
    
    public void setResponsiblePerson(String responsiblePerson) {
        this.responsiblePerson = responsiblePerson;
    }
    
    public String getResponsiblePersonContact() {
        return responsiblePersonContact;
    }
    
    public void setResponsiblePersonContact(String responsiblePersonContact) {
        this.responsiblePersonContact = responsiblePersonContact;
    }
    
    public String getRemediationPerson() {
        return remediationPerson;
    }
    
    public void setRemediationPerson(String remediationPerson) {
        this.remediationPerson = remediationPerson;
    }
    
    public String getRemediationPersonContact() {
        return remediationPersonContact;
    }
    
    public void setRemediationPersonContact(String remediationPersonContact) {
        this.remediationPersonContact = remediationPersonContact;
    }
    
    public String getProtectionScenario() {
        return protectionScenario;
    }
    
    public void setProtectionScenario(String protectionScenario) {
        this.protectionScenario = protectionScenario;
    }
    
    public String getCustomProtectionScenario() {
        return customProtectionScenario;
    }
    
    public void setCustomProtectionScenario(String customProtectionScenario) {
        this.customProtectionScenario = customProtectionScenario;
    }
    
    public String getDeviceType() {
        return deviceType;
    }
    
    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }
    
    public String getCustomDeviceType() {
        return customDeviceType;
    }
    
    public void setCustomDeviceType(String customDeviceType) {
        this.customDeviceType = customDeviceType;
    }
    
    public String getSelectedProtectionGroupId() {
        return selectedProtectionGroupId;
    }
    
    public void setSelectedProtectionGroupId(String selectedProtectionGroupId) {
        this.selectedProtectionGroupId = selectedProtectionGroupId;
    }
    
    public String getOpticalPathGroupCode() {
        return opticalPathGroupCode;
    }
    
    public void setOpticalPathGroupCode(String opticalPathGroupCode) {
        this.opticalPathGroupCode = opticalPathGroupCode;
    }
    
    public String getOpticalPathGroupName() {
        return opticalPathGroupName;
    }
    
    public void setOpticalPathGroupName(String opticalPathGroupName) {
        this.opticalPathGroupName = opticalPathGroupName;
    }
    
    public String getIsLifeline() {
        return isLifeline;
    }
    
    public void setIsLifeline(String isLifeline) {
        this.isLifeline = isLifeline;
    }
    
    public String getLifelineBusinessId() {
        return lifelineBusinessId;
    }
    
    public void setLifelineBusinessId(String lifelineBusinessId) {
        this.lifelineBusinessId = lifelineBusinessId;
    }
    
    public String getCircuitCode() {
        return circuitCode;
    }
    
    public void setCircuitCode(String circuitCode) {
        this.circuitCode = circuitCode;
    }
    
    public String getCircuitName() {
        return circuitName;
    }
    
    public void setCircuitName(String circuitName) {
        this.circuitName = circuitName;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getCustomerAccount() {
        return customerAccount;
    }
    
    public void setCustomerAccount(String customerAccount) {
        this.customerAccount = customerAccount;
    }
    
    public String getCustomerManager() {
        return customerManager;
    }
    
    public void setCustomerManager(String customerManager) {
        this.customerManager = customerManager;
    }
    
    public String getCustomerManagerDepartment() {
        return customerManagerDepartment;
    }
    
    public void setCustomerManagerDepartment(String customerManagerDepartment) {
        this.customerManagerDepartment = customerManagerDepartment;
    }
    
    public String getCustomerLevel() {
        return customerLevel;
    }
    
    public void setCustomerLevel(String customerLevel) {
        this.customerLevel = customerLevel;
    }
    
    public String getDifferentiatedServiceLevel() {
        return differentiatedServiceLevel;
    }
    
    public void setDifferentiatedServiceLevel(String differentiatedServiceLevel) {
        this.differentiatedServiceLevel = differentiatedServiceLevel;
    }
    
    public String getSelfDiscoveredDescription() {
        return selfDiscoveredDescription;
    }
    
    public void setSelfDiscoveredDescription(String selfDiscoveredDescription) {
        this.selfDiscoveredDescription = selfDiscoveredDescription;
    }
    
    public String getDiscoveredBy() {
        return discoveredBy;
    }
    
    public void setDiscoveredBy(String discoveredBy) {
        this.discoveredBy = discoveredBy;
    }
    
    public Date getDiscoveredDate() {
        return discoveredDate;
    }
    
    public void setDiscoveredDate(Date discoveredDate) {
        this.discoveredDate = discoveredDate;
    }
    
    public String getCreator() {
        return creator;
    }
    
    public void setCreator(String creator) {
        this.creator = creator;
    }
    
    public String getCreatorId() {
        return creatorId;
    }
    
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public List<HazardRemediationOpticalPath> getOpticalPaths() {
        return opticalPaths;
    }
    
    public void setOpticalPaths(List<HazardRemediationOpticalPath> opticalPaths) {
        this.opticalPaths = opticalPaths;
    }
    
    public List<HazardRemediationAttachment> getAttachments() {
        return attachments;
    }
    
    public void setAttachments(List<HazardRemediationAttachment> attachments) {
        this.attachments = attachments;
    }
}
