package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设施(CmFacilityNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CmFacilityNet implements Serializable {
    private static final long serialVersionUID = -19898684130109228L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 父设施ID
     */
    private BigDecimal parentId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 集团编码
     */
    private String standardCode;
    /**
     * 集团名称
     */
    private String standardName;
    /**
     * 序号
     */
    private Integer seq;
    /**
     * 别名
     */
    private String alias;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 入网日期
     */
    private Date intoNetworkDate;
    /**
     * 启用日期
     */
    private Date startUseDate;
    /**
     * 停用日期
     */
    private Date stopUseDate;
    /**
     * 退网日期
     */
    private Date exitNetworkDate;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 生命周期状态ID
     */
    private Integer lifeStateId;
    /**
     * 物理状态ID
     */
    private Integer physicalStateId;
    /**
     * 业务状态ID
     */
    private Integer usingStateId;
    /**
     * 封锁状态ID
     */
    private Integer lockStateId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 设施类型ID
     */
    private Integer facilityTypeId;
    /**
     * 材质ID
     */
    private Integer materialId;
    /**
     * 型号ID
     */
    private BigDecimal modelId;
    /**
     * 生产厂家ID
     */
    private BigDecimal manufactorId;
    /**
     * 长度（米）
     */
    private BigDecimal length;
    /**
     * 高度（米）
     */
    private BigDecimal height;
    /**
     * 宽度
     */
    private BigDecimal width;
    /**
     * 直径
     */
    private BigDecimal diameter;
    /**
     * 长本属性ID
     */
    private Integer longLocalId;
    /**
     * 地址ID
     */
    private BigDecimal addressId;
    /**
     * 方位信息
     */
    private String addressDesc;
    /**
     * 所属电信管理区ID
     */
    private BigDecimal tmlId;
    /**
     * 所属地区ID
     */
    private BigDecimal regionId;
    /**
     * 维护人ID
     */
    private BigDecimal mntPersonId;
    /**
     * 维护方式ID
     */
    private Integer mntWayId;
    /**
     * 数据采集单位ID
     */
    private BigDecimal collectionDeptId;
    /**
     * 数据采集人ID
     */
    private BigDecimal collectionPersonId;
    /**
     * 数据采集日期
     */
    private Date collectionDate;
    /**
     * 检查责任人ID
     */
    private BigDecimal checkingPersonId;
    /**
     * 维护部门ID
     */
    private BigDecimal mntDeptId;
    /**
     * 代维单位ID
     */
    private BigDecimal agentMntCompanyId;
    /**
     * 代维联系人
     */
    private String agentMntContact;
    /**
     * 代维电话
     */
    private String agentMntPhone;
    /**
     * 集中监控标志ID
     */
    private Integer monitorFlagId;
    /**
     * 维护等级ID
     */
    private Integer mntLevelId;
    /**
     * 产权性质ID
     */
    private Integer propertyTypeId;
    /**
     * 租用合同号
     */
    private String rentContractNo;
    /**
     * 有效期限
     */
    private String lifespan;
    /**
     * 产权归属ID
     */
    private Integer propertyOwnerId;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 应用网络ID
     */
    private Integer applyNetId;
    /**
     * 受益网络ID
     */
    private Integer benefitNetId;
    /**
     * 折旧年限（年）
     */
    private String depreciationPeriod;
    /**
     * 是否测量点ID
     */
    private Integer isTestpoint;
    /**
     * 是否安全
     */
    private Integer isSafe;
    /**
     * 是否虚拟
     */
    private Integer isVirtual;
    /**
     * 租用单位
     */
    private String leasingUnit;
    /**
     * 所属网络ID
     */
    private Integer ownerNetId;
    /**
     * 安全级别ID
     */
    private Integer safeLeveId;
    /**
     * 承租年限
     */
    private BigDecimal tenantryAgeLimit;
    /**
     * 承租联系人
     */
    private String tenantryMan;
    /**
     * 承租联系电话
     */
    private String tenantryPhone;
    /**
     * 承租费用
     */
    private BigDecimal tenantCost;
    /**
     * 是否有避雷装置ID
     */
    private Integer isArrester;
    /**
     * 程式ID
     */
    private Integer formulaId;
    /**
     * 作用对象ID
     */
    private Integer userObjectId;
    /**
     * 实物ID
     */
    private BigDecimal materialObjectId;
    
    private BigDecimal templetId;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    /**
     * 行号
     */
    private String rowNo;
    /**
     * 列号
     */
    private String colNo;
    /**
     * 维护人联系方式
     */
    private String maintainerTele;
    
    private Object rowNum;
    
    private Object colNum;
    
    private Object coordinateX;
    
    private Object coordinateY;
    
    private Object coordinateZ;
    
    private Integer arrangementId;
    
    private BigDecimal leafRegionId;
    
    private Integer isValid;
    /**
     * 原实体ID
     */
    private String oldId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getParentId() {
        return parentId;
    }

    public void setParentId(BigDecimal parentId) {
        this.parentId = parentId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getIntoNetworkDate() {
        return intoNetworkDate;
    }

    public void setIntoNetworkDate(Date intoNetworkDate) {
        this.intoNetworkDate = intoNetworkDate;
    }

    public Date getStartUseDate() {
        return startUseDate;
    }

    public void setStartUseDate(Date startUseDate) {
        this.startUseDate = startUseDate;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public Date getExitNetworkDate() {
        return exitNetworkDate;
    }

    public void setExitNetworkDate(Date exitNetworkDate) {
        this.exitNetworkDate = exitNetworkDate;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public Integer getLifeStateId() {
        return lifeStateId;
    }

    public void setLifeStateId(Integer lifeStateId) {
        this.lifeStateId = lifeStateId;
    }

    public Integer getPhysicalStateId() {
        return physicalStateId;
    }

    public void setPhysicalStateId(Integer physicalStateId) {
        this.physicalStateId = physicalStateId;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Integer getFacilityTypeId() {
        return facilityTypeId;
    }

    public void setFacilityTypeId(Integer facilityTypeId) {
        this.facilityTypeId = facilityTypeId;
    }

    public Integer getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Integer materialId) {
        this.materialId = materialId;
    }

    public BigDecimal getModelId() {
        return modelId;
    }

    public void setModelId(BigDecimal modelId) {
        this.modelId = modelId;
    }

    public BigDecimal getManufactorId() {
        return manufactorId;
    }

    public void setManufactorId(BigDecimal manufactorId) {
        this.manufactorId = manufactorId;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getDiameter() {
        return diameter;
    }

    public void setDiameter(BigDecimal diameter) {
        this.diameter = diameter;
    }

    public Integer getLongLocalId() {
        return longLocalId;
    }

    public void setLongLocalId(Integer longLocalId) {
        this.longLocalId = longLocalId;
    }

    public BigDecimal getAddressId() {
        return addressId;
    }

    public void setAddressId(BigDecimal addressId) {
        this.addressId = addressId;
    }

    public String getAddressDesc() {
        return addressDesc;
    }

    public void setAddressDesc(String addressDesc) {
        this.addressDesc = addressDesc;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public BigDecimal getMntPersonId() {
        return mntPersonId;
    }

    public void setMntPersonId(BigDecimal mntPersonId) {
        this.mntPersonId = mntPersonId;
    }

    public Integer getMntWayId() {
        return mntWayId;
    }

    public void setMntWayId(Integer mntWayId) {
        this.mntWayId = mntWayId;
    }

    public BigDecimal getCollectionDeptId() {
        return collectionDeptId;
    }

    public void setCollectionDeptId(BigDecimal collectionDeptId) {
        this.collectionDeptId = collectionDeptId;
    }

    public BigDecimal getCollectionPersonId() {
        return collectionPersonId;
    }

    public void setCollectionPersonId(BigDecimal collectionPersonId) {
        this.collectionPersonId = collectionPersonId;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public BigDecimal getCheckingPersonId() {
        return checkingPersonId;
    }

    public void setCheckingPersonId(BigDecimal checkingPersonId) {
        this.checkingPersonId = checkingPersonId;
    }

    public BigDecimal getMntDeptId() {
        return mntDeptId;
    }

    public void setMntDeptId(BigDecimal mntDeptId) {
        this.mntDeptId = mntDeptId;
    }

    public BigDecimal getAgentMntCompanyId() {
        return agentMntCompanyId;
    }

    public void setAgentMntCompanyId(BigDecimal agentMntCompanyId) {
        this.agentMntCompanyId = agentMntCompanyId;
    }

    public String getAgentMntContact() {
        return agentMntContact;
    }

    public void setAgentMntContact(String agentMntContact) {
        this.agentMntContact = agentMntContact;
    }

    public String getAgentMntPhone() {
        return agentMntPhone;
    }

    public void setAgentMntPhone(String agentMntPhone) {
        this.agentMntPhone = agentMntPhone;
    }

    public Integer getMonitorFlagId() {
        return monitorFlagId;
    }

    public void setMonitorFlagId(Integer monitorFlagId) {
        this.monitorFlagId = monitorFlagId;
    }

    public Integer getMntLevelId() {
        return mntLevelId;
    }

    public void setMntLevelId(Integer mntLevelId) {
        this.mntLevelId = mntLevelId;
    }

    public Integer getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Integer propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }

    public String getRentContractNo() {
        return rentContractNo;
    }

    public void setRentContractNo(String rentContractNo) {
        this.rentContractNo = rentContractNo;
    }

    public String getLifespan() {
        return lifespan;
    }

    public void setLifespan(String lifespan) {
        this.lifespan = lifespan;
    }

    public Integer getPropertyOwnerId() {
        return propertyOwnerId;
    }

    public void setPropertyOwnerId(Integer propertyOwnerId) {
        this.propertyOwnerId = propertyOwnerId;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Integer getApplyNetId() {
        return applyNetId;
    }

    public void setApplyNetId(Integer applyNetId) {
        this.applyNetId = applyNetId;
    }

    public Integer getBenefitNetId() {
        return benefitNetId;
    }

    public void setBenefitNetId(Integer benefitNetId) {
        this.benefitNetId = benefitNetId;
    }

    public String getDepreciationPeriod() {
        return depreciationPeriod;
    }

    public void setDepreciationPeriod(String depreciationPeriod) {
        this.depreciationPeriod = depreciationPeriod;
    }

    public Integer getIsTestpoint() {
        return isTestpoint;
    }

    public void setIsTestpoint(Integer isTestpoint) {
        this.isTestpoint = isTestpoint;
    }

    public Integer getIsSafe() {
        return isSafe;
    }

    public void setIsSafe(Integer isSafe) {
        this.isSafe = isSafe;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }

    public String getLeasingUnit() {
        return leasingUnit;
    }

    public void setLeasingUnit(String leasingUnit) {
        this.leasingUnit = leasingUnit;
    }

    public Integer getOwnerNetId() {
        return ownerNetId;
    }

    public void setOwnerNetId(Integer ownerNetId) {
        this.ownerNetId = ownerNetId;
    }

    public Integer getSafeLeveId() {
        return safeLeveId;
    }

    public void setSafeLeveId(Integer safeLeveId) {
        this.safeLeveId = safeLeveId;
    }

    public BigDecimal getTenantryAgeLimit() {
        return tenantryAgeLimit;
    }

    public void setTenantryAgeLimit(BigDecimal tenantryAgeLimit) {
        this.tenantryAgeLimit = tenantryAgeLimit;
    }

    public String getTenantryMan() {
        return tenantryMan;
    }

    public void setTenantryMan(String tenantryMan) {
        this.tenantryMan = tenantryMan;
    }

    public String getTenantryPhone() {
        return tenantryPhone;
    }

    public void setTenantryPhone(String tenantryPhone) {
        this.tenantryPhone = tenantryPhone;
    }

    public BigDecimal getTenantCost() {
        return tenantCost;
    }

    public void setTenantCost(BigDecimal tenantCost) {
        this.tenantCost = tenantCost;
    }

    public Integer getIsArrester() {
        return isArrester;
    }

    public void setIsArrester(Integer isArrester) {
        this.isArrester = isArrester;
    }

    public Integer getFormulaId() {
        return formulaId;
    }

    public void setFormulaId(Integer formulaId) {
        this.formulaId = formulaId;
    }

    public Integer getUserObjectId() {
        return userObjectId;
    }

    public void setUserObjectId(Integer userObjectId) {
        this.userObjectId = userObjectId;
    }

    public BigDecimal getMaterialObjectId() {
        return materialObjectId;
    }

    public void setMaterialObjectId(BigDecimal materialObjectId) {
        this.materialObjectId = materialObjectId;
    }

    public BigDecimal getTempletId() {
        return templetId;
    }

    public void setTempletId(BigDecimal templetId) {
        this.templetId = templetId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public String getRowNo() {
        return rowNo;
    }

    public void setRowNo(String rowNo) {
        this.rowNo = rowNo;
    }

    public String getColNo() {
        return colNo;
    }

    public void setColNo(String colNo) {
        this.colNo = colNo;
    }

    public String getMaintainerTele() {
        return maintainerTele;
    }

    public void setMaintainerTele(String maintainerTele) {
        this.maintainerTele = maintainerTele;
    }

    public Object getRowNum() {
        return rowNum;
    }

    public void setRowNum(Object rowNum) {
        this.rowNum = rowNum;
    }

    public Object getColNum() {
        return colNum;
    }

    public void setColNum(Object colNum) {
        this.colNum = colNum;
    }

    public Object getCoordinateX() {
        return coordinateX;
    }

    public void setCoordinateX(Object coordinateX) {
        this.coordinateX = coordinateX;
    }

    public Object getCoordinateY() {
        return coordinateY;
    }

    public void setCoordinateY(Object coordinateY) {
        this.coordinateY = coordinateY;
    }

    public Object getCoordinateZ() {
        return coordinateZ;
    }

    public void setCoordinateZ(Object coordinateZ) {
        this.coordinateZ = coordinateZ;
    }

    public Integer getArrangementId() {
        return arrangementId;
    }

    public void setArrangementId(Integer arrangementId) {
        this.arrangementId = arrangementId;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

