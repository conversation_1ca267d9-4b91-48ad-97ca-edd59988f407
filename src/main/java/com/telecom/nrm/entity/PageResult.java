package com.telecom.nrm.entity;

import java.io.Serializable;

public class PageResult<T> implements Serializable {

    /**
     * 结果编码；0，成功；1失败
     */
    private String resultCode;

    /**
     * 数据总量
     */
    private String total;

    /**
     * 分页大小
     */
    private String size;

    /**
     * 当前页码
     */
    private String page;

    /**
     * 数据
     */
    private T data;


    public PageResult() {

    }

    public static <T> PageResult<T> instance( T data, String resultCode, String total, String size, String page) {
        PageResult<T> pageResult = new PageResult();
        pageResult.setResultCode(resultCode);
        pageResult.setData(data);
        pageResult.setTotal(total);
        pageResult.setSize(size);
        pageResult.setPage(page);
        return pageResult;
    }

    public static <T> PageResult<T> ok(T data, String total, String size, String page){
        return instance(data,"0", total, size, page);
    }
    public static <T> PageResult<T> fail(T data){
        return instance(data,"1", "", "", "");
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
