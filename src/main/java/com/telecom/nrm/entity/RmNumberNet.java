package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 码号(RmNumberNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:04:30
 */
public class RmNumberNet implements Serializable {
    private static final long serialVersionUID = -49744736905608367L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 启用日期
     */
    private Date startUseDate;
    /**
     * 停用日期
     */
    private Date stopUseDate;
    /**
     * 所属码号空间ID
     */
    private BigDecimal nbrspaceId;
    /**
     * 生命周期状态ID
     */
    private Integer lifeStateId;
    /**
     * 业务状态ID
     */
    private Integer usingStateId;
    /**
     * 封锁状态ID
     */
    private Integer lockStateId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 码号类型ID
     */
    private Integer numberTypeId;
    /**
     * 业务类型ID
     */
    private Integer businessTypeId;
    /**
     * 号码等级ID
     */
    private Integer nbrLevelId;
    /**
     * 所属地区ID
     */
    private BigDecimal regionId;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 保留等级ID
     */
    private Integer reservedLevelId;
    /**
     * 码号角色ID
     */
    private Integer numberRoleId;
    /**
     * 预占渠道类型ID
     */
    private Integer reservedTypeId;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    
    private BigDecimal leafRegionId;
    
    private Integer isValid;
    /**
     * 旧系统编码
     */
    private String oldSp;
    /**
     * 原实体ID
     */
    private String oldId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public Date getStartUseDate() {
        return startUseDate;
    }

    public void setStartUseDate(Date startUseDate) {
        this.startUseDate = startUseDate;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public BigDecimal getNbrspaceId() {
        return nbrspaceId;
    }

    public void setNbrspaceId(BigDecimal nbrspaceId) {
        this.nbrspaceId = nbrspaceId;
    }

    public Integer getLifeStateId() {
        return lifeStateId;
    }

    public void setLifeStateId(Integer lifeStateId) {
        this.lifeStateId = lifeStateId;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Integer getNumberTypeId() {
        return numberTypeId;
    }

    public void setNumberTypeId(Integer numberTypeId) {
        this.numberTypeId = numberTypeId;
    }

    public Integer getBusinessTypeId() {
        return businessTypeId;
    }

    public void setBusinessTypeId(Integer businessTypeId) {
        this.businessTypeId = businessTypeId;
    }

    public Integer getNbrLevelId() {
        return nbrLevelId;
    }

    public void setNbrLevelId(Integer nbrLevelId) {
        this.nbrLevelId = nbrLevelId;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public Integer getReservedLevelId() {
        return reservedLevelId;
    }

    public void setReservedLevelId(Integer reservedLevelId) {
        this.reservedLevelId = reservedLevelId;
    }

    public Integer getNumberRoleId() {
        return numberRoleId;
    }

    public void setNumberRoleId(Integer numberRoleId) {
        this.numberRoleId = numberRoleId;
    }

    public Integer getReservedTypeId() {
        return reservedTypeId;
    }

    public void setReservedTypeId(Integer reservedTypeId) {
        this.reservedTypeId = reservedTypeId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getOldSp() {
        return oldSp;
    }

    public void setOldSp(String oldSp) {
        this.oldSp = oldSp;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

