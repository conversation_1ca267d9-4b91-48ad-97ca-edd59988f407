package com.telecom.nrm.entity;

import lombok.Data;

import javax.xml.ws.handler.PortInfo;

@Data
public class OccupyResInfo {

    /**
     * 缺失资源段ID
     */
    private String deficiencySegId;

    /**
     * 缺失资源段名称
     */
    private String deficiencySegName;

    /**
     * 缺失资源段起始局站Id
     */
    private String deficiencySegStationId;

    /**
     * 缺失资源段起始局站名称
     */
    private String deficiencySegStationName;

    /**
     * 缺失资源段结束局站Id
     */
    private String deficiencySegNextStationId;

    /**
     * 缺失资源段结束局站名称
     */
    private String deficiencySegNextStationName;

    /**
     * 占用资源类型
     */
    private String occupyResType;

    /**
     * 占用资源Id
     */
    private String occupyResPId;

    /**
     * 占用资源名称
     */
    private String occupyResPName;

    /**
     * 占用资源编码
     */
    private String occupyResPCode;


    /**
     * 占用逻辑资源Id
     */
    private String occupyResId;

    /**
     * 占用逻辑资源名称
     */
    private String occupyResName;

    /**
     * 占用逻辑资源编码
     */
    private String occupyResCode;

    /**
     * A端端口信息
     */
    private OccupyPortInfo portInfoA;


    /**
     * Z端端口信息
     */
    private OccupyPortInfo portInfoZ;
}
