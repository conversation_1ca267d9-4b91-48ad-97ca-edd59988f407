package com.telecom.nrm.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 隐患整改光路关联实体类
 */
public class HazardRemediationOpticalPath {
    
    private BigDecimal id;
    private BigDecimal remediationId; // 关联隐患整改主表ID
    private String opticalPathCode; // 光路编码
    private Integer sortOrder; // 排序序号
    private Date createTime; // 创建时间
    
    // 构造函数
    public HazardRemediationOpticalPath() {}
    
    public HazardRemediationOpticalPath(BigDecimal remediationId, String opticalPathCode, Integer sortOrder) {
        this.remediationId = remediationId;
        this.opticalPathCode = opticalPathCode;
        this.sortOrder = sortOrder;
    }
    
    // Getter和Setter方法
    public BigDecimal getId() {
        return id;
    }
    
    public void setId(BigDecimal id) {
        this.id = id;
    }
    
    public BigDecimal getRemediationId() {
        return remediationId;
    }
    
    public void setRemediationId(BigDecimal remediationId) {
        this.remediationId = remediationId;
    }
    
    public String getOpticalPathCode() {
        return opticalPathCode;
    }
    
    public void setOpticalPathCode(String opticalPathCode) {
        this.opticalPathCode = opticalPathCode;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "HazardRemediationOpticalPath{" +
                "id=" + id +
                ", remediationId=" + remediationId +
                ", opticalPathCode='" + opticalPathCode + '\'' +
                ", sortOrder=" + sortOrder +
                ", createTime=" + createTime +
                '}';
    }
}
