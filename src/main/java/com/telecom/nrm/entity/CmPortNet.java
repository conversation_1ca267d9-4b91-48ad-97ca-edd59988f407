package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 端口(CmPortNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CmPortNet implements Serializable {
    private static final long serialVersionUID = -35469277952500787L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 网管系统编码
     */
    private String nmCode;
    /**
     * 网管系统名称
     */
    private String nmName;
    /**
     * 集团编码
     */
    private String standardCode;
    /**
     * 集团名称
     */
    private String standardName;
    /**
     * 序号
     */
    private String seq;
    /**
     * 别名
     */
    private String alias;
    /**
     * 所属物理设备ID
     */
    private BigDecimal physicDeviceId;
    /**
     * 所属逻辑设备ID
     */
    private BigDecimal logicDeviceId;
    /**
     * 所属硬件ID
     */
    private BigDecimal wareId;
    /**
     * 行号
     */
    private String rowNo;
    /**
     * 列号
     */
    private String colNo;
    /**
     * X坐标
     */
    private Object posX;
    /**
     * Y坐标
     */
    private Object posY;
    /**
     * 生命周期状态ID
     */
    private Integer lifeStateId;
    /**
     * 物理状态ID
     */
    private Integer physicalStateId;
    /**
     * 业务状态ID
     */
    private Integer usingStateId;
    /**
     * 封锁状态ID
     */
    private Integer lockStateId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 端口类型ID
     */
    private Integer portTypeId;
    /**
     * 接口类型ID
     */
    private Integer interfaceTypeId;
    /**
     * 虚实属性ID
     */
    private Integer virtualRealId;
    /**
     * 光电属性ID
     */
    private Integer photoelectricTypeId;
    /**
     * 收发属性ID
     */
    private Integer sendReceiveTypeId;
    /**
     * 上下联属性ID
     */
    private Integer upDownId;
    /**
     * 所属电信管理区ID
     */
    private BigDecimal tmlId;
    /**
     * 所属地区ID
     */
    private BigDecimal regionId;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 连接器类型ID
     */
    private Integer connectorTypeId;
    /**
     * 双工方式ID
     */
    private Integer duplexModId;
    /**
     * 接口标准ID
     */
    private Integer interfaceStandardId;
    /**
     * 是否虚拟
     */
    private Integer isVirtual;
    /**
     * 长本属性ID
     */
    private Integer longLocalId;
    /**
     * 最大接入速率ID
     */
    private Integer maxAccessRateId;
    /**
     * 最大处理速率ID
     */
    private Integer maxDealRateId;
    /**
     * 网管中的ID
     */
    private BigDecimal nmId;
    /**
     * 可选工作速率ID
     */
    private Integer optionalWorkRateId;
    /**
     * Z坐标
     */
    private Object posZ;
    /**
     * 保护方式ID
     */
    private Integer protectWayId;
    /**
     * 协议ID
     */
    private Integer protocolId;
    /**
     * 服务质量ID
     */
    private Integer qosId;
    /**
     * 实际配置速率ID
     */
    private Integer realConfigRateId;
    /**
     * 保留等级ID
     */
    private Integer reservedLevelId;
    /**
     * 端口角色ID
     */
    private Integer roleId;
    /**
     * 业务特性ID
     */
    private Integer serviceFeaturesId;
    /**
     * 信号类型ID
     */
    private Integer signalTypeId;
    /**
     * 工作状态ID
     */
    private Integer workStateId;
    /**
     * 工作方式ID
     */
    private Integer workWayId;
    /**
     * 预占渠道类型ID
     */
    private Integer reservedTypeId;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    
    private Integer sharedStatusId;
    
    private BigDecimal leafRegionId;
    
    private BigDecimal facilityId;
    
    private Date stopUseDate;
    
    private Integer isValid;
    /**
     * 原实体ID
     */
    private String oldId;
    
    private Object dataSourceSysId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public String getNmCode() {
        return nmCode;
    }

    public void setNmCode(String nmCode) {
        this.nmCode = nmCode;
    }

    public String getNmName() {
        return nmName;
    }

    public void setNmName(String nmName) {
        this.nmName = nmName;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public BigDecimal getPhysicDeviceId() {
        return physicDeviceId;
    }

    public void setPhysicDeviceId(BigDecimal physicDeviceId) {
        this.physicDeviceId = physicDeviceId;
    }

    public BigDecimal getLogicDeviceId() {
        return logicDeviceId;
    }

    public void setLogicDeviceId(BigDecimal logicDeviceId) {
        this.logicDeviceId = logicDeviceId;
    }

    public BigDecimal getWareId() {
        return wareId;
    }

    public void setWareId(BigDecimal wareId) {
        this.wareId = wareId;
    }

    public String getRowNo() {
        return rowNo;
    }

    public void setRowNo(String rowNo) {
        this.rowNo = rowNo;
    }

    public String getColNo() {
        return colNo;
    }

    public void setColNo(String colNo) {
        this.colNo = colNo;
    }

    public Object getPosX() {
        return posX;
    }

    public void setPosX(Object posX) {
        this.posX = posX;
    }

    public Object getPosY() {
        return posY;
    }

    public void setPosY(Object posY) {
        this.posY = posY;
    }

    public Integer getLifeStateId() {
        return lifeStateId;
    }

    public void setLifeStateId(Integer lifeStateId) {
        this.lifeStateId = lifeStateId;
    }

    public Integer getPhysicalStateId() {
        return physicalStateId;
    }

    public void setPhysicalStateId(Integer physicalStateId) {
        this.physicalStateId = physicalStateId;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Integer getPortTypeId() {
        return portTypeId;
    }

    public void setPortTypeId(Integer portTypeId) {
        this.portTypeId = portTypeId;
    }

    public Integer getInterfaceTypeId() {
        return interfaceTypeId;
    }

    public void setInterfaceTypeId(Integer interfaceTypeId) {
        this.interfaceTypeId = interfaceTypeId;
    }

    public Integer getVirtualRealId() {
        return virtualRealId;
    }

    public void setVirtualRealId(Integer virtualRealId) {
        this.virtualRealId = virtualRealId;
    }

    public Integer getPhotoelectricTypeId() {
        return photoelectricTypeId;
    }

    public void setPhotoelectricTypeId(Integer photoelectricTypeId) {
        this.photoelectricTypeId = photoelectricTypeId;
    }

    public Integer getSendReceiveTypeId() {
        return sendReceiveTypeId;
    }

    public void setSendReceiveTypeId(Integer sendReceiveTypeId) {
        this.sendReceiveTypeId = sendReceiveTypeId;
    }

    public Integer getUpDownId() {
        return upDownId;
    }

    public void setUpDownId(Integer upDownId) {
        this.upDownId = upDownId;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Integer getConnectorTypeId() {
        return connectorTypeId;
    }

    public void setConnectorTypeId(Integer connectorTypeId) {
        this.connectorTypeId = connectorTypeId;
    }

    public Integer getDuplexModId() {
        return duplexModId;
    }

    public void setDuplexModId(Integer duplexModId) {
        this.duplexModId = duplexModId;
    }

    public Integer getInterfaceStandardId() {
        return interfaceStandardId;
    }

    public void setInterfaceStandardId(Integer interfaceStandardId) {
        this.interfaceStandardId = interfaceStandardId;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }

    public Integer getLongLocalId() {
        return longLocalId;
    }

    public void setLongLocalId(Integer longLocalId) {
        this.longLocalId = longLocalId;
    }

    public Integer getMaxAccessRateId() {
        return maxAccessRateId;
    }

    public void setMaxAccessRateId(Integer maxAccessRateId) {
        this.maxAccessRateId = maxAccessRateId;
    }

    public Integer getMaxDealRateId() {
        return maxDealRateId;
    }

    public void setMaxDealRateId(Integer maxDealRateId) {
        this.maxDealRateId = maxDealRateId;
    }

    public BigDecimal getNmId() {
        return nmId;
    }

    public void setNmId(BigDecimal nmId) {
        this.nmId = nmId;
    }

    public Integer getOptionalWorkRateId() {
        return optionalWorkRateId;
    }

    public void setOptionalWorkRateId(Integer optionalWorkRateId) {
        this.optionalWorkRateId = optionalWorkRateId;
    }

    public Object getPosZ() {
        return posZ;
    }

    public void setPosZ(Object posZ) {
        this.posZ = posZ;
    }

    public Integer getProtectWayId() {
        return protectWayId;
    }

    public void setProtectWayId(Integer protectWayId) {
        this.protectWayId = protectWayId;
    }

    public Integer getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(Integer protocolId) {
        this.protocolId = protocolId;
    }

    public Integer getQosId() {
        return qosId;
    }

    public void setQosId(Integer qosId) {
        this.qosId = qosId;
    }

    public Integer getRealConfigRateId() {
        return realConfigRateId;
    }

    public void setRealConfigRateId(Integer realConfigRateId) {
        this.realConfigRateId = realConfigRateId;
    }

    public Integer getReservedLevelId() {
        return reservedLevelId;
    }

    public void setReservedLevelId(Integer reservedLevelId) {
        this.reservedLevelId = reservedLevelId;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public Integer getServiceFeaturesId() {
        return serviceFeaturesId;
    }

    public void setServiceFeaturesId(Integer serviceFeaturesId) {
        this.serviceFeaturesId = serviceFeaturesId;
    }

    public Integer getSignalTypeId() {
        return signalTypeId;
    }

    public void setSignalTypeId(Integer signalTypeId) {
        this.signalTypeId = signalTypeId;
    }

    public Integer getWorkStateId() {
        return workStateId;
    }

    public void setWorkStateId(Integer workStateId) {
        this.workStateId = workStateId;
    }

    public Integer getWorkWayId() {
        return workWayId;
    }

    public void setWorkWayId(Integer workWayId) {
        this.workWayId = workWayId;
    }

    public Integer getReservedTypeId() {
        return reservedTypeId;
    }

    public void setReservedTypeId(Integer reservedTypeId) {
        this.reservedTypeId = reservedTypeId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public Integer getSharedStatusId() {
        return sharedStatusId;
    }

    public void setSharedStatusId(Integer sharedStatusId) {
        this.sharedStatusId = sharedStatusId;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public BigDecimal getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(BigDecimal facilityId) {
        this.facilityId = facilityId;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Object getDataSourceSysId() {
        return dataSourceSysId;
    }

    public void setDataSourceSysId(Object dataSourceSysId) {
        this.dataSourceSysId = dataSourceSysId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

