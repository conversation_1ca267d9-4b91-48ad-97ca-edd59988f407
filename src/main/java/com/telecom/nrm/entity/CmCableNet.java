package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 缆线(CmCableNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:37
 */
public class CmCableNet implements Serializable {
    private static final long serialVersionUID = -83325709802681247L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 父缆线ID
     */
    private BigDecimal parentId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 集团编码
     */
    private String standardCode;
    /**
     * 集团名称
     */
    private String standardName;
    /**
     * 序号
     */
    private Integer seq;
    /**
     * 别名
     */
    private String alias;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 入网日期
     */
    private Date intoNetworkDate;
    /**
     * 启用日期
     */
    private Date startUseDate;
    /**
     * 停用日期
     */
    private Date stopUseDate;
    /**
     * 退网日期
     */
    private Date exitNetworkDate;
    /**
     * 缆线类型ID
     */
    private Integer cableTypeId;
    /**
     * A端设施ID
     */
    private BigDecimal aFacilityId;
    /**
     * A端设备ID
     */
    private BigDecimal aDeviceId;
    /**
     * Z端设施ID
     */
    private BigDecimal zFacilityId;
    /**
     * Z端设备ID
     */
    private BigDecimal zDeviceId;
    /**
     * 生命周期状态ID
     */
    private Integer lifeStateId;
    /**
     * 物理状态ID
     */
    private Integer physicalStateId;
    /**
     * 业务状态ID
     */
    private Integer usingStateId;
    /**
     * 封锁状态ID
     */
    private Integer lockStateId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 型号ID
     */
    private BigDecimal modelId;
    /**
     * 生产厂家ID
     */
    private BigDecimal manufactorId;
    /**
     * 长度（米）
     */
    private BigDecimal length;
    /**
     * 直径
     */
    private BigDecimal diameter;
    /**
     * 线对数/纤芯数
     */
    private Long capacity;
    /**
     * 地址ID
     */
    private BigDecimal addressId;
    /**
     * 方位信息
     */
    private String addressDesc;
    /**
     * 所属电信管理区ID
     */
    private BigDecimal tmlId;
    /**
     * 所属地区ID
     */
    private BigDecimal regionId;
    /**
     * A端地址ID
     */
    private BigDecimal aAddressId;
    /**
     * A端电信管理区ID
     */
    private BigDecimal aTmlId;
    /**
     * A端地区ID
     */
    private BigDecimal aRegionId;
    /**
     * Z端地址ID
     */
    private BigDecimal zAddressId;
    /**
     * Z端电信管理区ID
     */
    private BigDecimal zTmlId;
    /**
     * Z端地区ID
     */
    private BigDecimal zRegionId;
    /**
     * 数据采集人ID
     */
    private BigDecimal collectionPersonId;
    /**
     * 敷设方式ID
     */
    private Integer layingWayId;
    /**
     * 维护人ID
     */
    private BigDecimal mntPersonId;
    /**
     * 数据采集日期
     */
    private Date collectionDate;
    /**
     * 维护方式ID
     */
    private Integer mntWayId;
    /**
     * 数据采集单位ID
     */
    private BigDecimal collectionDeptId;
    /**
     * 检查责任人ID
     */
    private BigDecimal checkingPersonId;
    /**
     * 维护部门ID
     */
    private BigDecimal mntDeptId;
    /**
     * 代维单位ID
     */
    private BigDecimal agentMntCompanyId;
    /**
     * 代维联系人
     */
    private String agentMntContact;
    /**
     * 代维电话
     */
    private String agentMntPhone;
    /**
     * 维护等级ID
     */
    private Integer mntLevelId;
    /**
     * 产权性质ID
     */
    private Integer propertyTypeId;
    /**
     * 租用合同号
     */
    private String rentContractNo;
    /**
     * 有效期限
     */
    private String lifespan;
    /**
     * 产权归属ID
     */
    private Integer propertyOwnerId;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 是否虚拟
     */
    private Integer isVirtual;
    /**
     * 复接标志
     */
    private Integer multiJointFlag;
    /**
     * 保护方式ID
     */
    private Integer protectWayId;
    /**
     * 工作状态ID
     */
    private Integer workStateId;
    /**
     * 工作方式ID
     */
    private Integer workWayId;
    /**
     * 保留等级ID
     */
    private Integer reservedLevelId;
    /**
     * 长本属性ID
     */
    private Integer longLocalId;
    /**
     * 材质ID
     */
    private Integer materialId;
    /**
     * 所属网络ID
     */
    private Integer ownerNetId;
    /**
     * 结构尺寸ID
     */
    private Integer structureSizeId;
    /**
     * 结构类型ID
     */
    private Integer structureTypeId;
    /**
     * 作用对象ID
     */
    private Integer userObjectId;
    /**
     * 应用网络ID
     */
    private Integer applyNetId;
    /**
     * 实物ID
     */
    private BigDecimal materialObjectId;
    
    private BigDecimal templetId;
    /**
     * 缆线角色ID
     */
    private Integer cableRoleId;
    /**
     * 保修期
     */
    private String warrantyPeriod;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    
    private BigDecimal aLeafRegionId;
    
    private BigDecimal zLeafRegionId;
    
    private BigDecimal useUnitId;
    
    private BigDecimal leafRegionId;
    
    private Integer isValid;
    /**
     * 原实体ID
     */
    private String oldId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getParentId() {
        return parentId;
    }

    public void setParentId(BigDecimal parentId) {
        this.parentId = parentId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getIntoNetworkDate() {
        return intoNetworkDate;
    }

    public void setIntoNetworkDate(Date intoNetworkDate) {
        this.intoNetworkDate = intoNetworkDate;
    }

    public Date getStartUseDate() {
        return startUseDate;
    }

    public void setStartUseDate(Date startUseDate) {
        this.startUseDate = startUseDate;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public Date getExitNetworkDate() {
        return exitNetworkDate;
    }

    public void setExitNetworkDate(Date exitNetworkDate) {
        this.exitNetworkDate = exitNetworkDate;
    }

    public Integer getCableTypeId() {
        return cableTypeId;
    }

    public void setCableTypeId(Integer cableTypeId) {
        this.cableTypeId = cableTypeId;
    }

    public BigDecimal getAFacilityId() {
        return aFacilityId;
    }

    public void setAFacilityId(BigDecimal aFacilityId) {
        this.aFacilityId = aFacilityId;
    }

    public BigDecimal getADeviceId() {
        return aDeviceId;
    }

    public void setADeviceId(BigDecimal aDeviceId) {
        this.aDeviceId = aDeviceId;
    }

    public BigDecimal getZFacilityId() {
        return zFacilityId;
    }

    public void setZFacilityId(BigDecimal zFacilityId) {
        this.zFacilityId = zFacilityId;
    }

    public BigDecimal getZDeviceId() {
        return zDeviceId;
    }

    public void setZDeviceId(BigDecimal zDeviceId) {
        this.zDeviceId = zDeviceId;
    }

    public Integer getLifeStateId() {
        return lifeStateId;
    }

    public void setLifeStateId(Integer lifeStateId) {
        this.lifeStateId = lifeStateId;
    }

    public Integer getPhysicalStateId() {
        return physicalStateId;
    }

    public void setPhysicalStateId(Integer physicalStateId) {
        this.physicalStateId = physicalStateId;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public BigDecimal getModelId() {
        return modelId;
    }

    public void setModelId(BigDecimal modelId) {
        this.modelId = modelId;
    }

    public BigDecimal getManufactorId() {
        return manufactorId;
    }

    public void setManufactorId(BigDecimal manufactorId) {
        this.manufactorId = manufactorId;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getDiameter() {
        return diameter;
    }

    public void setDiameter(BigDecimal diameter) {
        this.diameter = diameter;
    }

    public Long getCapacity() {
        return capacity;
    }

    public void setCapacity(Long capacity) {
        this.capacity = capacity;
    }

    public BigDecimal getAddressId() {
        return addressId;
    }

    public void setAddressId(BigDecimal addressId) {
        this.addressId = addressId;
    }

    public String getAddressDesc() {
        return addressDesc;
    }

    public void setAddressDesc(String addressDesc) {
        this.addressDesc = addressDesc;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public BigDecimal getAAddressId() {
        return aAddressId;
    }

    public void setAAddressId(BigDecimal aAddressId) {
        this.aAddressId = aAddressId;
    }

    public BigDecimal getATmlId() {
        return aTmlId;
    }

    public void setATmlId(BigDecimal aTmlId) {
        this.aTmlId = aTmlId;
    }

    public BigDecimal getARegionId() {
        return aRegionId;
    }

    public void setARegionId(BigDecimal aRegionId) {
        this.aRegionId = aRegionId;
    }

    public BigDecimal getZAddressId() {
        return zAddressId;
    }

    public void setZAddressId(BigDecimal zAddressId) {
        this.zAddressId = zAddressId;
    }

    public BigDecimal getZTmlId() {
        return zTmlId;
    }

    public void setZTmlId(BigDecimal zTmlId) {
        this.zTmlId = zTmlId;
    }

    public BigDecimal getZRegionId() {
        return zRegionId;
    }

    public void setZRegionId(BigDecimal zRegionId) {
        this.zRegionId = zRegionId;
    }

    public BigDecimal getCollectionPersonId() {
        return collectionPersonId;
    }

    public void setCollectionPersonId(BigDecimal collectionPersonId) {
        this.collectionPersonId = collectionPersonId;
    }

    public Integer getLayingWayId() {
        return layingWayId;
    }

    public void setLayingWayId(Integer layingWayId) {
        this.layingWayId = layingWayId;
    }

    public BigDecimal getMntPersonId() {
        return mntPersonId;
    }

    public void setMntPersonId(BigDecimal mntPersonId) {
        this.mntPersonId = mntPersonId;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public Integer getMntWayId() {
        return mntWayId;
    }

    public void setMntWayId(Integer mntWayId) {
        this.mntWayId = mntWayId;
    }

    public BigDecimal getCollectionDeptId() {
        return collectionDeptId;
    }

    public void setCollectionDeptId(BigDecimal collectionDeptId) {
        this.collectionDeptId = collectionDeptId;
    }

    public BigDecimal getCheckingPersonId() {
        return checkingPersonId;
    }

    public void setCheckingPersonId(BigDecimal checkingPersonId) {
        this.checkingPersonId = checkingPersonId;
    }

    public BigDecimal getMntDeptId() {
        return mntDeptId;
    }

    public void setMntDeptId(BigDecimal mntDeptId) {
        this.mntDeptId = mntDeptId;
    }

    public BigDecimal getAgentMntCompanyId() {
        return agentMntCompanyId;
    }

    public void setAgentMntCompanyId(BigDecimal agentMntCompanyId) {
        this.agentMntCompanyId = agentMntCompanyId;
    }

    public String getAgentMntContact() {
        return agentMntContact;
    }

    public void setAgentMntContact(String agentMntContact) {
        this.agentMntContact = agentMntContact;
    }

    public String getAgentMntPhone() {
        return agentMntPhone;
    }

    public void setAgentMntPhone(String agentMntPhone) {
        this.agentMntPhone = agentMntPhone;
    }

    public Integer getMntLevelId() {
        return mntLevelId;
    }

    public void setMntLevelId(Integer mntLevelId) {
        this.mntLevelId = mntLevelId;
    }

    public Integer getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Integer propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }

    public String getRentContractNo() {
        return rentContractNo;
    }

    public void setRentContractNo(String rentContractNo) {
        this.rentContractNo = rentContractNo;
    }

    public String getLifespan() {
        return lifespan;
    }

    public void setLifespan(String lifespan) {
        this.lifespan = lifespan;
    }

    public Integer getPropertyOwnerId() {
        return propertyOwnerId;
    }

    public void setPropertyOwnerId(Integer propertyOwnerId) {
        this.propertyOwnerId = propertyOwnerId;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }

    public Integer getMultiJointFlag() {
        return multiJointFlag;
    }

    public void setMultiJointFlag(Integer multiJointFlag) {
        this.multiJointFlag = multiJointFlag;
    }

    public Integer getProtectWayId() {
        return protectWayId;
    }

    public void setProtectWayId(Integer protectWayId) {
        this.protectWayId = protectWayId;
    }

    public Integer getWorkStateId() {
        return workStateId;
    }

    public void setWorkStateId(Integer workStateId) {
        this.workStateId = workStateId;
    }

    public Integer getWorkWayId() {
        return workWayId;
    }

    public void setWorkWayId(Integer workWayId) {
        this.workWayId = workWayId;
    }

    public Integer getReservedLevelId() {
        return reservedLevelId;
    }

    public void setReservedLevelId(Integer reservedLevelId) {
        this.reservedLevelId = reservedLevelId;
    }

    public Integer getLongLocalId() {
        return longLocalId;
    }

    public void setLongLocalId(Integer longLocalId) {
        this.longLocalId = longLocalId;
    }

    public Integer getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Integer materialId) {
        this.materialId = materialId;
    }

    public Integer getOwnerNetId() {
        return ownerNetId;
    }

    public void setOwnerNetId(Integer ownerNetId) {
        this.ownerNetId = ownerNetId;
    }

    public Integer getStructureSizeId() {
        return structureSizeId;
    }

    public void setStructureSizeId(Integer structureSizeId) {
        this.structureSizeId = structureSizeId;
    }

    public Integer getStructureTypeId() {
        return structureTypeId;
    }

    public void setStructureTypeId(Integer structureTypeId) {
        this.structureTypeId = structureTypeId;
    }

    public Integer getUserObjectId() {
        return userObjectId;
    }

    public void setUserObjectId(Integer userObjectId) {
        this.userObjectId = userObjectId;
    }

    public Integer getApplyNetId() {
        return applyNetId;
    }

    public void setApplyNetId(Integer applyNetId) {
        this.applyNetId = applyNetId;
    }

    public BigDecimal getMaterialObjectId() {
        return materialObjectId;
    }

    public void setMaterialObjectId(BigDecimal materialObjectId) {
        this.materialObjectId = materialObjectId;
    }

    public BigDecimal getTempletId() {
        return templetId;
    }

    public void setTempletId(BigDecimal templetId) {
        this.templetId = templetId;
    }

    public Integer getCableRoleId() {
        return cableRoleId;
    }

    public void setCableRoleId(Integer cableRoleId) {
        this.cableRoleId = cableRoleId;
    }

    public String getWarrantyPeriod() {
        return warrantyPeriod;
    }

    public void setWarrantyPeriod(String warrantyPeriod) {
        this.warrantyPeriod = warrantyPeriod;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public BigDecimal getALeafRegionId() {
        return aLeafRegionId;
    }

    public void setALeafRegionId(BigDecimal aLeafRegionId) {
        this.aLeafRegionId = aLeafRegionId;
    }

    public BigDecimal getZLeafRegionId() {
        return zLeafRegionId;
    }

    public void setZLeafRegionId(BigDecimal zLeafRegionId) {
        this.zLeafRegionId = zLeafRegionId;
    }

    public BigDecimal getUseUnitId() {
        return useUnitId;
    }

    public void setUseUnitId(BigDecimal useUnitId) {
        this.useUnitId = useUnitId;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

