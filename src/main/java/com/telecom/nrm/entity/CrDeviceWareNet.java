package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备与硬件关系(CrDeviceWareNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CrDeviceWareNet implements Serializable {
    private static final long serialVersionUID = 230330229599777808L;
    /**
     * 关联ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 关系规格ID
     */
    private Long specId;
    /**
     * 子实体ID
     */
    private BigDecimal childId;
    /**
     * 子实体规格ID
     */
    private Long childSpecId;
    /**
     * 父实体ID
     */
    private BigDecimal parentId;
    /**
     * 父实体规格ID
     */
    private Long parentSpecId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 其它状态ID
     */
    private Object otherStateId;
    
    private Object dataSourceId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getChildId() {
        return childId;
    }

    public void setChildId(BigDecimal childId) {
        this.childId = childId;
    }

    public Long getChildSpecId() {
        return childSpecId;
    }

    public void setChildSpecId(Long childSpecId) {
        this.childSpecId = childSpecId;
    }

    public BigDecimal getParentId() {
        return parentId;
    }

    public void setParentId(BigDecimal parentId) {
        this.parentId = parentId;
    }

    public Long getParentSpecId() {
        return parentSpecId;
    }

    public void setParentSpecId(Long parentSpecId) {
        this.parentSpecId = parentSpecId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Object getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Object otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Object getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Object dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

