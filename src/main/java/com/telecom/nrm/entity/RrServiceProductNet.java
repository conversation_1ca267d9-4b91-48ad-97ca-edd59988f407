package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品服务与产品关系(RrServiceProductNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:04:29
 */
public class RrServiceProductNet implements Serializable {
    private static final long serialVersionUID = 315276161309241562L;
    /**
     * 关联ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 关系规格ID
     */
    private Long specId;
    /**
     * 产品服务ID
     */
    private BigDecimal serviceId;
    /**
     * 产品ID
     */
    private BigDecimal productId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 服务规格ID
     */
    private Long serviceSpecId;
    /**
     * 产品规格ID
     */
    private Long productSpecId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    
    private BigDecimal dataSourceId;
    
    private Integer otherState;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getServiceId() {
        return serviceId;
    }

    public void setServiceId(BigDecimal serviceId) {
        this.serviceId = serviceId;
    }

    public BigDecimal getProductId() {
        return productId;
    }

    public void setProductId(BigDecimal productId) {
        this.productId = productId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public Long getServiceSpecId() {
        return serviceSpecId;
    }

    public void setServiceSpecId(Long serviceSpecId) {
        this.serviceSpecId = serviceSpecId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public BigDecimal getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(BigDecimal dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public Integer getOtherState() {
        return otherState;
    }

    public void setOtherState(Integer otherState) {
        this.otherState = otherState;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

