package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 硬件(CmWareNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CmWareNet implements Serializable {
    private static final long serialVersionUID = 171537663641904527L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 集团编码
     */
    private String standardCode;
    /**
     * 集团名称
     */
    private String standardName;
    /**
     * 序号
     */
    private Integer seq;
    /**
     * 别名
     */
    private String alias;
    /**
     * 所属设施ID
     */
    private BigDecimal facilityId;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 入网日期
     */
    private Date intoNetworkDate;
    /**
     * 启用日期
     */
    private Date startUseDate;
    /**
     * 停用日期
     */
    private Date stopUseDate;
    /**
     * 退网日期
     */
    private Date exitNetworkDate;
    /**
     * 行号
     */
    private String rowNo;
    /**
     * 列号
     */
    private String colNo;
    /**
     * X坐标
     */
    private Object posX;
    /**
     * Y坐标
     */
    private Object posY;
    /**
     * 生命周期状态ID
     */
    private Integer lifeStateId;
    /**
     * 物理状态ID
     */
    private Integer physicalStateId;
    /**
     * 业务状态ID
     */
    private Integer usingStateId;
    /**
     * 封锁状态ID
     */
    private Integer lockStateId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 硬件类型ID
     */
    private Integer wareTypeId;
    /**
     * 型号ID
     */
    private BigDecimal modelId;
    /**
     * 生产厂家ID
     */
    private BigDecimal manufactorId;
    /**
     * 排列方式ID
     */
    private Integer arrangementId;
    /**
     * 长度（米）
     */
    private BigDecimal length;
    /**
     * 宽度(m)
     */
    private BigDecimal width;
    /**
     * 高度（米）
     */
    private BigDecimal height;
    /**
     * 行数
     */
    private Object rowNum;
    /**
     * 列数
     */
    private Object colNum;
    /**
     * 标称容量
     */
    private BigDecimal capacity;
    /**
     * 地址ID
     */
    private BigDecimal addressId;
    /**
     * 方位信息
     */
    private String addressDesc;
    /**
     * 所属电信管理区ID
     */
    private BigDecimal tmlId;
    /**
     * 所属地区ID
     */
    private BigDecimal regionId;
    /**
     * 维护人ID
     */
    private BigDecimal mntPersonId;
    /**
     * 维护方式ID
     */
    private Integer mntWayId;
    /**
     * 数据采集单位ID
     */
    private BigDecimal collectionDeptId;
    /**
     * 数据采集人ID
     */
    private BigDecimal collectionPersonId;
    /**
     * 数据采集日期
     */
    private Date collectionDate;
    /**
     * 检查责任人ID
     */
    private BigDecimal checkingPersonId;
    /**
     * 维护部门ID
     */
    private BigDecimal mntDeptId;
    /**
     * 代维单位ID
     */
    private BigDecimal agentMntCompanyId;
    /**
     * 代维联系人
     */
    private String agentMntContact;
    /**
     * 代维电话
     */
    private String agentMntPhone;
    /**
     * 维护等级ID
     */
    private Integer mntLevelId;
    /**
     * 保修开始时间
     */
    private Date warrantyStartDate;
    /**
     * 保修结束时间
     */
    private Date warrantyEndDate;
    /**
     * 供货商ID
     */
    private BigDecimal vendorId;
    /**
     * 产权性质ID
     */
    private Integer propertyTypeId;
    /**
     * 租用合同号
     */
    private String rentContractNo;
    /**
     * 有效期限
     */
    private String lifespan;
    /**
     * 产权归属ID
     */
    private Integer propertyOwnerId;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 应用网络ID
     */
    private Integer applyNetId;
    /**
     * 离地高度(m)
     */
    private Object groundDistance;
    /**
     * 硬件版本
     */
    private String hardwareVersion;
    /**
     * 是否虚拟
     */
    private Integer isVirtual;
    /**
     * 布局方式ID
     */
    private Integer layoutModeId;
    /**
     * 长本属性ID
     */
    private Integer longLocalId;
    /**
     * MAC地址
     */
    private String mac;
    /**
     * 维保情况
     */
    private String mntInfo;
    /**
     * 掩码
     */
    private String netmask;
    /**
     * 网络归属
     */
    private Object networkBelong;
    /**
     * 网络级别
     */
    private Object netLevel;
    /**
     * 保护方式ID
     */
    private Integer protectWayId;
    /**
     * 硬件角色ID
     */
    private Integer roleId;
    /**
     * 安全等级ID
     */
    private Integer securityLevelId;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 软件版本
     */
    private String softwareVersion;
    /**
     * 工作状态ID
     */
    private Integer workStateId;
    /**
     * 工作方式ID
     */
    private Integer workWayId;
    /**
     * 网管编号
     */
    private String nmCode;
    /**
     * 网管ID
     */
    private Integer nmId;
    /**
     * 网管名称
     */
    private Object nmName;
    /**
     * 所属网络ID
     */
    private Integer ownerNetId;
    /**
     * 作用对象ID
     */
    private Integer userObjectId;
    /**
     * 安装方式ID
     */
    private Integer installWayId;
    /**
     * 实物ID
     */
    private BigDecimal materialObjectId;
    
    private BigDecimal templetId;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    /**
     * 安装时间
     */
    private Date installDate;
    /**
     * 保修级别ID
     */
    private Integer warrantyLevelId;
    
    private Integer singleDoubleSideId;
    
    private BigDecimal leafRegionId;
    
    private Integer virtualRealId;
    
    private Integer isValid;
    /**
     * 原实体ID
     */
    private String oldId;
    
    private String oldSp;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public BigDecimal getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(BigDecimal facilityId) {
        this.facilityId = facilityId;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getIntoNetworkDate() {
        return intoNetworkDate;
    }

    public void setIntoNetworkDate(Date intoNetworkDate) {
        this.intoNetworkDate = intoNetworkDate;
    }

    public Date getStartUseDate() {
        return startUseDate;
    }

    public void setStartUseDate(Date startUseDate) {
        this.startUseDate = startUseDate;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public Date getExitNetworkDate() {
        return exitNetworkDate;
    }

    public void setExitNetworkDate(Date exitNetworkDate) {
        this.exitNetworkDate = exitNetworkDate;
    }

    public String getRowNo() {
        return rowNo;
    }

    public void setRowNo(String rowNo) {
        this.rowNo = rowNo;
    }

    public String getColNo() {
        return colNo;
    }

    public void setColNo(String colNo) {
        this.colNo = colNo;
    }

    public Object getPosX() {
        return posX;
    }

    public void setPosX(Object posX) {
        this.posX = posX;
    }

    public Object getPosY() {
        return posY;
    }

    public void setPosY(Object posY) {
        this.posY = posY;
    }

    public Integer getLifeStateId() {
        return lifeStateId;
    }

    public void setLifeStateId(Integer lifeStateId) {
        this.lifeStateId = lifeStateId;
    }

    public Integer getPhysicalStateId() {
        return physicalStateId;
    }

    public void setPhysicalStateId(Integer physicalStateId) {
        this.physicalStateId = physicalStateId;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Integer getWareTypeId() {
        return wareTypeId;
    }

    public void setWareTypeId(Integer wareTypeId) {
        this.wareTypeId = wareTypeId;
    }

    public BigDecimal getModelId() {
        return modelId;
    }

    public void setModelId(BigDecimal modelId) {
        this.modelId = modelId;
    }

    public BigDecimal getManufactorId() {
        return manufactorId;
    }

    public void setManufactorId(BigDecimal manufactorId) {
        this.manufactorId = manufactorId;
    }

    public Integer getArrangementId() {
        return arrangementId;
    }

    public void setArrangementId(Integer arrangementId) {
        this.arrangementId = arrangementId;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public Object getRowNum() {
        return rowNum;
    }

    public void setRowNum(Object rowNum) {
        this.rowNum = rowNum;
    }

    public Object getColNum() {
        return colNum;
    }

    public void setColNum(Object colNum) {
        this.colNum = colNum;
    }

    public BigDecimal getCapacity() {
        return capacity;
    }

    public void setCapacity(BigDecimal capacity) {
        this.capacity = capacity;
    }

    public BigDecimal getAddressId() {
        return addressId;
    }

    public void setAddressId(BigDecimal addressId) {
        this.addressId = addressId;
    }

    public String getAddressDesc() {
        return addressDesc;
    }

    public void setAddressDesc(String addressDesc) {
        this.addressDesc = addressDesc;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public BigDecimal getMntPersonId() {
        return mntPersonId;
    }

    public void setMntPersonId(BigDecimal mntPersonId) {
        this.mntPersonId = mntPersonId;
    }

    public Integer getMntWayId() {
        return mntWayId;
    }

    public void setMntWayId(Integer mntWayId) {
        this.mntWayId = mntWayId;
    }

    public BigDecimal getCollectionDeptId() {
        return collectionDeptId;
    }

    public void setCollectionDeptId(BigDecimal collectionDeptId) {
        this.collectionDeptId = collectionDeptId;
    }

    public BigDecimal getCollectionPersonId() {
        return collectionPersonId;
    }

    public void setCollectionPersonId(BigDecimal collectionPersonId) {
        this.collectionPersonId = collectionPersonId;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public BigDecimal getCheckingPersonId() {
        return checkingPersonId;
    }

    public void setCheckingPersonId(BigDecimal checkingPersonId) {
        this.checkingPersonId = checkingPersonId;
    }

    public BigDecimal getMntDeptId() {
        return mntDeptId;
    }

    public void setMntDeptId(BigDecimal mntDeptId) {
        this.mntDeptId = mntDeptId;
    }

    public BigDecimal getAgentMntCompanyId() {
        return agentMntCompanyId;
    }

    public void setAgentMntCompanyId(BigDecimal agentMntCompanyId) {
        this.agentMntCompanyId = agentMntCompanyId;
    }

    public String getAgentMntContact() {
        return agentMntContact;
    }

    public void setAgentMntContact(String agentMntContact) {
        this.agentMntContact = agentMntContact;
    }

    public String getAgentMntPhone() {
        return agentMntPhone;
    }

    public void setAgentMntPhone(String agentMntPhone) {
        this.agentMntPhone = agentMntPhone;
    }

    public Integer getMntLevelId() {
        return mntLevelId;
    }

    public void setMntLevelId(Integer mntLevelId) {
        this.mntLevelId = mntLevelId;
    }

    public Date getWarrantyStartDate() {
        return warrantyStartDate;
    }

    public void setWarrantyStartDate(Date warrantyStartDate) {
        this.warrantyStartDate = warrantyStartDate;
    }

    public Date getWarrantyEndDate() {
        return warrantyEndDate;
    }

    public void setWarrantyEndDate(Date warrantyEndDate) {
        this.warrantyEndDate = warrantyEndDate;
    }

    public BigDecimal getVendorId() {
        return vendorId;
    }

    public void setVendorId(BigDecimal vendorId) {
        this.vendorId = vendorId;
    }

    public Integer getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Integer propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }

    public String getRentContractNo() {
        return rentContractNo;
    }

    public void setRentContractNo(String rentContractNo) {
        this.rentContractNo = rentContractNo;
    }

    public String getLifespan() {
        return lifespan;
    }

    public void setLifespan(String lifespan) {
        this.lifespan = lifespan;
    }

    public Integer getPropertyOwnerId() {
        return propertyOwnerId;
    }

    public void setPropertyOwnerId(Integer propertyOwnerId) {
        this.propertyOwnerId = propertyOwnerId;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public Integer getApplyNetId() {
        return applyNetId;
    }

    public void setApplyNetId(Integer applyNetId) {
        this.applyNetId = applyNetId;
    }

    public Object getGroundDistance() {
        return groundDistance;
    }

    public void setGroundDistance(Object groundDistance) {
        this.groundDistance = groundDistance;
    }

    public String getHardwareVersion() {
        return hardwareVersion;
    }

    public void setHardwareVersion(String hardwareVersion) {
        this.hardwareVersion = hardwareVersion;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }

    public Integer getLayoutModeId() {
        return layoutModeId;
    }

    public void setLayoutModeId(Integer layoutModeId) {
        this.layoutModeId = layoutModeId;
    }

    public Integer getLongLocalId() {
        return longLocalId;
    }

    public void setLongLocalId(Integer longLocalId) {
        this.longLocalId = longLocalId;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMntInfo() {
        return mntInfo;
    }

    public void setMntInfo(String mntInfo) {
        this.mntInfo = mntInfo;
    }

    public String getNetmask() {
        return netmask;
    }

    public void setNetmask(String netmask) {
        this.netmask = netmask;
    }

    public Object getNetworkBelong() {
        return networkBelong;
    }

    public void setNetworkBelong(Object networkBelong) {
        this.networkBelong = networkBelong;
    }

    public Object getNetLevel() {
        return netLevel;
    }

    public void setNetLevel(Object netLevel) {
        this.netLevel = netLevel;
    }

    public Integer getProtectWayId() {
        return protectWayId;
    }

    public void setProtectWayId(Integer protectWayId) {
        this.protectWayId = protectWayId;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public Integer getSecurityLevelId() {
        return securityLevelId;
    }

    public void setSecurityLevelId(Integer securityLevelId) {
        this.securityLevelId = securityLevelId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getSoftwareVersion() {
        return softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public Integer getWorkStateId() {
        return workStateId;
    }

    public void setWorkStateId(Integer workStateId) {
        this.workStateId = workStateId;
    }

    public Integer getWorkWayId() {
        return workWayId;
    }

    public void setWorkWayId(Integer workWayId) {
        this.workWayId = workWayId;
    }

    public String getNmCode() {
        return nmCode;
    }

    public void setNmCode(String nmCode) {
        this.nmCode = nmCode;
    }

    public Integer getNmId() {
        return nmId;
    }

    public void setNmId(Integer nmId) {
        this.nmId = nmId;
    }

    public Object getNmName() {
        return nmName;
    }

    public void setNmName(Object nmName) {
        this.nmName = nmName;
    }

    public Integer getOwnerNetId() {
        return ownerNetId;
    }

    public void setOwnerNetId(Integer ownerNetId) {
        this.ownerNetId = ownerNetId;
    }

    public Integer getUserObjectId() {
        return userObjectId;
    }

    public void setUserObjectId(Integer userObjectId) {
        this.userObjectId = userObjectId;
    }

    public Integer getInstallWayId() {
        return installWayId;
    }

    public void setInstallWayId(Integer installWayId) {
        this.installWayId = installWayId;
    }

    public BigDecimal getMaterialObjectId() {
        return materialObjectId;
    }

    public void setMaterialObjectId(BigDecimal materialObjectId) {
        this.materialObjectId = materialObjectId;
    }

    public BigDecimal getTempletId() {
        return templetId;
    }

    public void setTempletId(BigDecimal templetId) {
        this.templetId = templetId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public Date getInstallDate() {
        return installDate;
    }

    public void setInstallDate(Date installDate) {
        this.installDate = installDate;
    }

    public Integer getWarrantyLevelId() {
        return warrantyLevelId;
    }

    public void setWarrantyLevelId(Integer warrantyLevelId) {
        this.warrantyLevelId = warrantyLevelId;
    }

    public Integer getSingleDoubleSideId() {
        return singleDoubleSideId;
    }

    public void setSingleDoubleSideId(Integer singleDoubleSideId) {
        this.singleDoubleSideId = singleDoubleSideId;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public Integer getVirtualRealId() {
        return virtualRealId;
    }

    public void setVirtualRealId(Integer virtualRealId) {
        this.virtualRealId = virtualRealId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public String getOldSp() {
        return oldSp;
    }

    public void setOldSp(String oldSp) {
        this.oldSp = oldSp;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

