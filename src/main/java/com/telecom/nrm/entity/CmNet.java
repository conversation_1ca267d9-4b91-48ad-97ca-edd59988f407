package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 网(CmNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CmNet implements Serializable {
    private static final long serialVersionUID = 947542135866853383L;
    /**
     * ID
     */
    private BigDecimal id;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 备注
     */
    private String notes;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人ID
     */
    private BigDecimal creatorId;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 修改人ID
     */
    private BigDecimal modifierId;
    /**
     * 规格ID
     */
    private Long specId;
    /**
     * 分片ID
     */
    private BigDecimal shardingId;
    /**
     * 版本
     */
    private BigDecimal version;
    /**
     * 集团编码
     */
    private String standardCode;
    /**
     * 集团名称
     */
    private String standardName;
    /**
     * 别名
     */
    private String alias;
    /**
     * 所属设施ID
     */
    private BigDecimal facilityId;
    /**
     * 入网日期
     */
    private Date intoNetworkDate;
    /**
     * 启用日期
     */
    private Date startUseDate;
    /**
     * 停用日期
     */
    private Date stopUseDate;
    /**
     * 退网日期
     */
    private Date exitNetworkDate;
    /**
     * 网类型ID
     */
    private Integer netTypeId;
    /**
     * 所属电信管理区ID
     */
    private BigDecimal tmlId;
    /**
     * 所属地区ID
     */
    private BigDecimal regionId;
    /**
     * 维护人ID
     */
    private BigDecimal mntPersonId;
    /**
     * 维护方式ID
     */
    private Integer mntWayId;
    /**
     * 数据采集单位ID
     */
    private BigDecimal collectionDeptId;
    /**
     * 数据采集人ID
     */
    private BigDecimal collectionPersonId;
    /**
     * 数据采集日期
     */
    private Date collectionDate;
    /**
     * 检查责任人ID
     */
    private BigDecimal checkingPersonId;
    /**
     * 维护部门ID
     */
    private BigDecimal mntDeptId;
    /**
     * 代维单位ID
     */
    private BigDecimal agentMntCompanyId;
    /**
     * 代维联系人
     */
    private String agentMntContact;
    /**
     * 代维电话
     */
    private String agentMntPhone;
    /**
     * 维护等级ID
     */
    private Integer mntLevelId;
    /**
     * 生命周期状态ID
     */
    private Integer lifeStateId;
    /**
     * 物理状态ID
     */
    private Integer physicalStateId;
    /**
     * 业务状态ID
     */
    private Integer usingStateId;
    /**
     * 封锁状态ID
     */
    private Integer lockStateId;
    /**
     * 其它状态ID
     */
    private Integer otherStateId;
    /**
     * 长本属性ID
     */
    private Integer longLocalId;
    /**
     * 传输媒质ID
     */
    private Integer transmissionMediumId;
    /**
     * 时间戳
     */
    private Date timeStamp;
    /**
     * 应用网络ID
     */
    private Integer applyNetId;
    /**
     * 服务等级ID
     */
    private Integer cableServiceLevelId;
    /**
     * 生产厂家ID
     */
    private BigDecimal manufactorId;
    /**
     * 型号ID
     */
    private BigDecimal modelId;
    /**
     * 网络层次ID
     */
    private Integer networkLayerId;
    /**
     * 网管中的名称
     */
    private String nmName;
    /**
     * 所属网络ID
     */
    private Integer ownerNetId;
    /**
     * 产权性质ID
     */
    private Integer propertyTypeId;
    /**
     * 保护方式ID
     */
    private Integer protectWayId;
    /**
     * 安全等级ID
     */
    private Integer securityLevelId;
    /**
     * 拓扑结构ID
     */
    private Integer topoId;
    /**
     * 工作状态ID
     */
    private Integer workStateId;
    /**
     * 工作方式ID
     */
    private Integer workWayId;
    /**
     * 网管中的编码
     */
    private String nmCode;
    /**
     * 产权归属ID
     */
    private Integer propertyOwnerId;
    /**
     * 实物ID
     */
    private BigDecimal materialObjectId;
    
    private BigDecimal templetId;
    /**
     * 数据来源标识
     */
    private Integer dataSourceId;
    
    private BigDecimal leafRegionId;
    
    private Integer isValid;
    /**
     * 网管中的标识ID
     */
    private BigDecimal nmId;
    /**
     * 原实体ID
     */
    private String oldId;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public BigDecimal getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(BigDecimal facilityId) {
        this.facilityId = facilityId;
    }

    public Date getIntoNetworkDate() {
        return intoNetworkDate;
    }

    public void setIntoNetworkDate(Date intoNetworkDate) {
        this.intoNetworkDate = intoNetworkDate;
    }

    public Date getStartUseDate() {
        return startUseDate;
    }

    public void setStartUseDate(Date startUseDate) {
        this.startUseDate = startUseDate;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public Date getExitNetworkDate() {
        return exitNetworkDate;
    }

    public void setExitNetworkDate(Date exitNetworkDate) {
        this.exitNetworkDate = exitNetworkDate;
    }

    public Integer getNetTypeId() {
        return netTypeId;
    }

    public void setNetTypeId(Integer netTypeId) {
        this.netTypeId = netTypeId;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public BigDecimal getMntPersonId() {
        return mntPersonId;
    }

    public void setMntPersonId(BigDecimal mntPersonId) {
        this.mntPersonId = mntPersonId;
    }

    public Integer getMntWayId() {
        return mntWayId;
    }

    public void setMntWayId(Integer mntWayId) {
        this.mntWayId = mntWayId;
    }

    public BigDecimal getCollectionDeptId() {
        return collectionDeptId;
    }

    public void setCollectionDeptId(BigDecimal collectionDeptId) {
        this.collectionDeptId = collectionDeptId;
    }

    public BigDecimal getCollectionPersonId() {
        return collectionPersonId;
    }

    public void setCollectionPersonId(BigDecimal collectionPersonId) {
        this.collectionPersonId = collectionPersonId;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public BigDecimal getCheckingPersonId() {
        return checkingPersonId;
    }

    public void setCheckingPersonId(BigDecimal checkingPersonId) {
        this.checkingPersonId = checkingPersonId;
    }

    public BigDecimal getMntDeptId() {
        return mntDeptId;
    }

    public void setMntDeptId(BigDecimal mntDeptId) {
        this.mntDeptId = mntDeptId;
    }

    public BigDecimal getAgentMntCompanyId() {
        return agentMntCompanyId;
    }

    public void setAgentMntCompanyId(BigDecimal agentMntCompanyId) {
        this.agentMntCompanyId = agentMntCompanyId;
    }

    public String getAgentMntContact() {
        return agentMntContact;
    }

    public void setAgentMntContact(String agentMntContact) {
        this.agentMntContact = agentMntContact;
    }

    public String getAgentMntPhone() {
        return agentMntPhone;
    }

    public void setAgentMntPhone(String agentMntPhone) {
        this.agentMntPhone = agentMntPhone;
    }

    public Integer getMntLevelId() {
        return mntLevelId;
    }

    public void setMntLevelId(Integer mntLevelId) {
        this.mntLevelId = mntLevelId;
    }

    public Integer getLifeStateId() {
        return lifeStateId;
    }

    public void setLifeStateId(Integer lifeStateId) {
        this.lifeStateId = lifeStateId;
    }

    public Integer getPhysicalStateId() {
        return physicalStateId;
    }

    public void setPhysicalStateId(Integer physicalStateId) {
        this.physicalStateId = physicalStateId;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Integer getLongLocalId() {
        return longLocalId;
    }

    public void setLongLocalId(Integer longLocalId) {
        this.longLocalId = longLocalId;
    }

    public Integer getTransmissionMediumId() {
        return transmissionMediumId;
    }

    public void setTransmissionMediumId(Integer transmissionMediumId) {
        this.transmissionMediumId = transmissionMediumId;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Integer getApplyNetId() {
        return applyNetId;
    }

    public void setApplyNetId(Integer applyNetId) {
        this.applyNetId = applyNetId;
    }

    public Integer getCableServiceLevelId() {
        return cableServiceLevelId;
    }

    public void setCableServiceLevelId(Integer cableServiceLevelId) {
        this.cableServiceLevelId = cableServiceLevelId;
    }

    public BigDecimal getManufactorId() {
        return manufactorId;
    }

    public void setManufactorId(BigDecimal manufactorId) {
        this.manufactorId = manufactorId;
    }

    public BigDecimal getModelId() {
        return modelId;
    }

    public void setModelId(BigDecimal modelId) {
        this.modelId = modelId;
    }

    public Integer getNetworkLayerId() {
        return networkLayerId;
    }

    public void setNetworkLayerId(Integer networkLayerId) {
        this.networkLayerId = networkLayerId;
    }

    public String getNmName() {
        return nmName;
    }

    public void setNmName(String nmName) {
        this.nmName = nmName;
    }

    public Integer getOwnerNetId() {
        return ownerNetId;
    }

    public void setOwnerNetId(Integer ownerNetId) {
        this.ownerNetId = ownerNetId;
    }

    public Integer getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Integer propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }

    public Integer getProtectWayId() {
        return protectWayId;
    }

    public void setProtectWayId(Integer protectWayId) {
        this.protectWayId = protectWayId;
    }

    public Integer getSecurityLevelId() {
        return securityLevelId;
    }

    public void setSecurityLevelId(Integer securityLevelId) {
        this.securityLevelId = securityLevelId;
    }

    public Integer getTopoId() {
        return topoId;
    }

    public void setTopoId(Integer topoId) {
        this.topoId = topoId;
    }

    public Integer getWorkStateId() {
        return workStateId;
    }

    public void setWorkStateId(Integer workStateId) {
        this.workStateId = workStateId;
    }

    public Integer getWorkWayId() {
        return workWayId;
    }

    public void setWorkWayId(Integer workWayId) {
        this.workWayId = workWayId;
    }

    public String getNmCode() {
        return nmCode;
    }

    public void setNmCode(String nmCode) {
        this.nmCode = nmCode;
    }

    public Integer getPropertyOwnerId() {
        return propertyOwnerId;
    }

    public void setPropertyOwnerId(Integer propertyOwnerId) {
        this.propertyOwnerId = propertyOwnerId;
    }

    public BigDecimal getMaterialObjectId() {
        return materialObjectId;
    }

    public void setMaterialObjectId(BigDecimal materialObjectId) {
        this.materialObjectId = materialObjectId;
    }

    public BigDecimal getTempletId() {
        return templetId;
    }

    public void setTempletId(BigDecimal templetId) {
        this.templetId = templetId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public BigDecimal getNmId() {
        return nmId;
    }

    public void setNmId(BigDecimal nmId) {
        this.nmId = nmId;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

