package com.telecom.nrm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * (CmLinkNet)实体类
 *
 * <AUTHOR>
 * @since 2024-09-23 15:02:49
 */
public class CmLinkNet implements Serializable {
    private static final long serialVersionUID = -57289478330424299L;
    
    private BigDecimal id;
    
    private String name;
    
    private String code;
    
    private Long specId;
    
    private String notes;
    
    private BigDecimal creatorId;
    
    private Date createDate;
    
    private BigDecimal modifierId;
    
    private Date modifyDate;
    
    private BigDecimal version;
    
    private BigDecimal shardingId;
    
    private String standardCode;
    
    private String standardName;
    
    private Integer seq;
    
    private String alias;
    
    private Date intoNetworkDate;
    
    private Date startUseDate;
    
    private Date stopUseDate;
    
    private Date exitNetworkDate;
    
    private BigDecimal zFacilityId;
    
    private BigDecimal aFacilityId;
    
    private BigDecimal aPhysicDeviceId;
    
    private BigDecimal aLogicDeviceId;
    
    private BigDecimal aWareId;
    
    private BigDecimal aPortId;
    
    private BigDecimal zPhysicDeviceId;
    
    private BigDecimal zLogicDeviceId;
    
    private BigDecimal zWareId;
    
    private BigDecimal zPortId;
    
    private Object textRoute;
    
    private Integer lifeStateId;
    
    private Integer physicalStateId;
    
    private Integer usingStateId;
    
    private Integer lockStateId;
    
    private Integer otherStateId;
    
    private Integer linkTypeId;
    
    private Integer longLocalId;
    
    private Integer networkLayerId;
    
    private BigDecimal tmlId;
    
    private BigDecimal regionId;
    
    private BigDecimal aAddressId;
    
    private BigDecimal aTmlId;
    
    private BigDecimal aRegionId;
    
    private BigDecimal zAddressId;
    
    private BigDecimal zTmlId;
    
    private BigDecimal zRegionId;
    
    private Integer mntLevelId;
    
    private Integer serviceLevelId;
    
    private BigDecimal aMntPersonId;
    
    private BigDecimal zMntPersonId;
    
    private Integer propertyTypeId;
    
    private String rentContractNo;
    
    private String lifespan;
    
    private Integer propertyOwnerId;
    
    private Integer directionId;
    
    private Integer rateId;
    
    private Integer workModeId;
    
    private Integer workWayId;
    
    private Integer securityLevelId;
    
    private Date timeStamp;
    
    private Integer accessModeId;
    
    private Integer applyNetId;
    
    private String attenuation;
    
    private Integer aAccessModeId;
    
    private String aMatainContactDetail;
    
    private BigDecimal aMntDeptId;
    
    private Object aTextRoute;
    
    private BigDecimal aUseDeptId;
    
    private Integer dmRuleId;
    
    private Integer doubleEndId;
    
    private Integer intigrityId;
    
    private BigDecimal length;
    
    private String matainContactDetail;
    
    private BigDecimal mntDeptId;
    
    private BigDecimal mntPersonId;
    
    private Integer netLevelId;
    
    private String nmCode;
    
    private BigDecimal nmId;
    
    private String nmName;
    
    private Integer ownerNetId;
    
    private Integer persistenceId;
    
    private Integer protectWayId;
    
    private Integer protocolId;
    
    private Integer qualityLevelId;
    
    private Integer reservedLevelId;
    
    private Integer roleId;
    
    private Integer routeModeId;
    
    private Integer signalTypeId;
    
    private BigDecimal useDeptId;
    
    private Integer wavelengthId;
    
    private Integer workStateId;
    
    private Integer zAccessModeId;
    
    private String zMatainContactDetail;
    
    private BigDecimal zMntDeptId;
    
    private Object zTextRoute;
    
    private BigDecimal zUseDeptId;
    
    private Integer reservedTypeId;
    
    private Integer dataSourceId;
    
    private BigDecimal aLeafRegionId;
    
    private BigDecimal zLeafRegionId;
    
    private Integer locationNatureId;
    
    private BigDecimal leafRegionId;
    
    private BigDecimal facilityId;
    
    private Integer isValid;
    /**
     * 原实体ID
     */
    private String oldId;
    /**
     * 电路编码
     */
    private Object circuitCode;
    
    private Date netTime;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(BigDecimal creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getModifierId() {
        return modifierId;
    }

    public void setModifierId(BigDecimal modifierId) {
        this.modifierId = modifierId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public BigDecimal getShardingId() {
        return shardingId;
    }

    public void setShardingId(BigDecimal shardingId) {
        this.shardingId = shardingId;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Date getIntoNetworkDate() {
        return intoNetworkDate;
    }

    public void setIntoNetworkDate(Date intoNetworkDate) {
        this.intoNetworkDate = intoNetworkDate;
    }

    public Date getStartUseDate() {
        return startUseDate;
    }

    public void setStartUseDate(Date startUseDate) {
        this.startUseDate = startUseDate;
    }

    public Date getStopUseDate() {
        return stopUseDate;
    }

    public void setStopUseDate(Date stopUseDate) {
        this.stopUseDate = stopUseDate;
    }

    public Date getExitNetworkDate() {
        return exitNetworkDate;
    }

    public void setExitNetworkDate(Date exitNetworkDate) {
        this.exitNetworkDate = exitNetworkDate;
    }

    public BigDecimal getZFacilityId() {
        return zFacilityId;
    }

    public void setZFacilityId(BigDecimal zFacilityId) {
        this.zFacilityId = zFacilityId;
    }

    public BigDecimal getAFacilityId() {
        return aFacilityId;
    }

    public void setAFacilityId(BigDecimal aFacilityId) {
        this.aFacilityId = aFacilityId;
    }

    public BigDecimal getAPhysicDeviceId() {
        return aPhysicDeviceId;
    }

    public void setAPhysicDeviceId(BigDecimal aPhysicDeviceId) {
        this.aPhysicDeviceId = aPhysicDeviceId;
    }

    public BigDecimal getALogicDeviceId() {
        return aLogicDeviceId;
    }

    public void setALogicDeviceId(BigDecimal aLogicDeviceId) {
        this.aLogicDeviceId = aLogicDeviceId;
    }

    public BigDecimal getAWareId() {
        return aWareId;
    }

    public void setAWareId(BigDecimal aWareId) {
        this.aWareId = aWareId;
    }

    public BigDecimal getAPortId() {
        return aPortId;
    }

    public void setAPortId(BigDecimal aPortId) {
        this.aPortId = aPortId;
    }

    public BigDecimal getZPhysicDeviceId() {
        return zPhysicDeviceId;
    }

    public void setZPhysicDeviceId(BigDecimal zPhysicDeviceId) {
        this.zPhysicDeviceId = zPhysicDeviceId;
    }

    public BigDecimal getZLogicDeviceId() {
        return zLogicDeviceId;
    }

    public void setZLogicDeviceId(BigDecimal zLogicDeviceId) {
        this.zLogicDeviceId = zLogicDeviceId;
    }

    public BigDecimal getZWareId() {
        return zWareId;
    }

    public void setZWareId(BigDecimal zWareId) {
        this.zWareId = zWareId;
    }

    public BigDecimal getZPortId() {
        return zPortId;
    }

    public void setZPortId(BigDecimal zPortId) {
        this.zPortId = zPortId;
    }

    public Object getTextRoute() {
        return textRoute;
    }

    public void setTextRoute(Object textRoute) {
        this.textRoute = textRoute;
    }

    public Integer getLifeStateId() {
        return lifeStateId;
    }

    public void setLifeStateId(Integer lifeStateId) {
        this.lifeStateId = lifeStateId;
    }

    public Integer getPhysicalStateId() {
        return physicalStateId;
    }

    public void setPhysicalStateId(Integer physicalStateId) {
        this.physicalStateId = physicalStateId;
    }

    public Integer getUsingStateId() {
        return usingStateId;
    }

    public void setUsingStateId(Integer usingStateId) {
        this.usingStateId = usingStateId;
    }

    public Integer getLockStateId() {
        return lockStateId;
    }

    public void setLockStateId(Integer lockStateId) {
        this.lockStateId = lockStateId;
    }

    public Integer getOtherStateId() {
        return otherStateId;
    }

    public void setOtherStateId(Integer otherStateId) {
        this.otherStateId = otherStateId;
    }

    public Integer getLinkTypeId() {
        return linkTypeId;
    }

    public void setLinkTypeId(Integer linkTypeId) {
        this.linkTypeId = linkTypeId;
    }

    public Integer getLongLocalId() {
        return longLocalId;
    }

    public void setLongLocalId(Integer longLocalId) {
        this.longLocalId = longLocalId;
    }

    public Integer getNetworkLayerId() {
        return networkLayerId;
    }

    public void setNetworkLayerId(Integer networkLayerId) {
        this.networkLayerId = networkLayerId;
    }

    public BigDecimal getTmlId() {
        return tmlId;
    }

    public void setTmlId(BigDecimal tmlId) {
        this.tmlId = tmlId;
    }

    public BigDecimal getRegionId() {
        return regionId;
    }

    public void setRegionId(BigDecimal regionId) {
        this.regionId = regionId;
    }

    public BigDecimal getAAddressId() {
        return aAddressId;
    }

    public void setAAddressId(BigDecimal aAddressId) {
        this.aAddressId = aAddressId;
    }

    public BigDecimal getATmlId() {
        return aTmlId;
    }

    public void setATmlId(BigDecimal aTmlId) {
        this.aTmlId = aTmlId;
    }

    public BigDecimal getARegionId() {
        return aRegionId;
    }

    public void setARegionId(BigDecimal aRegionId) {
        this.aRegionId = aRegionId;
    }

    public BigDecimal getZAddressId() {
        return zAddressId;
    }

    public void setZAddressId(BigDecimal zAddressId) {
        this.zAddressId = zAddressId;
    }

    public BigDecimal getZTmlId() {
        return zTmlId;
    }

    public void setZTmlId(BigDecimal zTmlId) {
        this.zTmlId = zTmlId;
    }

    public BigDecimal getZRegionId() {
        return zRegionId;
    }

    public void setZRegionId(BigDecimal zRegionId) {
        this.zRegionId = zRegionId;
    }

    public Integer getMntLevelId() {
        return mntLevelId;
    }

    public void setMntLevelId(Integer mntLevelId) {
        this.mntLevelId = mntLevelId;
    }

    public Integer getServiceLevelId() {
        return serviceLevelId;
    }

    public void setServiceLevelId(Integer serviceLevelId) {
        this.serviceLevelId = serviceLevelId;
    }

    public BigDecimal getAMntPersonId() {
        return aMntPersonId;
    }

    public void setAMntPersonId(BigDecimal aMntPersonId) {
        this.aMntPersonId = aMntPersonId;
    }

    public BigDecimal getZMntPersonId() {
        return zMntPersonId;
    }

    public void setZMntPersonId(BigDecimal zMntPersonId) {
        this.zMntPersonId = zMntPersonId;
    }

    public Integer getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Integer propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }

    public String getRentContractNo() {
        return rentContractNo;
    }

    public void setRentContractNo(String rentContractNo) {
        this.rentContractNo = rentContractNo;
    }

    public String getLifespan() {
        return lifespan;
    }

    public void setLifespan(String lifespan) {
        this.lifespan = lifespan;
    }

    public Integer getPropertyOwnerId() {
        return propertyOwnerId;
    }

    public void setPropertyOwnerId(Integer propertyOwnerId) {
        this.propertyOwnerId = propertyOwnerId;
    }

    public Integer getDirectionId() {
        return directionId;
    }

    public void setDirectionId(Integer directionId) {
        this.directionId = directionId;
    }

    public Integer getRateId() {
        return rateId;
    }

    public void setRateId(Integer rateId) {
        this.rateId = rateId;
    }

    public Integer getWorkModeId() {
        return workModeId;
    }

    public void setWorkModeId(Integer workModeId) {
        this.workModeId = workModeId;
    }

    public Integer getWorkWayId() {
        return workWayId;
    }

    public void setWorkWayId(Integer workWayId) {
        this.workWayId = workWayId;
    }

    public Integer getSecurityLevelId() {
        return securityLevelId;
    }

    public void setSecurityLevelId(Integer securityLevelId) {
        this.securityLevelId = securityLevelId;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Integer getAccessModeId() {
        return accessModeId;
    }

    public void setAccessModeId(Integer accessModeId) {
        this.accessModeId = accessModeId;
    }

    public Integer getApplyNetId() {
        return applyNetId;
    }

    public void setApplyNetId(Integer applyNetId) {
        this.applyNetId = applyNetId;
    }

    public String getAttenuation() {
        return attenuation;
    }

    public void setAttenuation(String attenuation) {
        this.attenuation = attenuation;
    }

    public Integer getAAccessModeId() {
        return aAccessModeId;
    }

    public void setAAccessModeId(Integer aAccessModeId) {
        this.aAccessModeId = aAccessModeId;
    }

    public String getAMatainContactDetail() {
        return aMatainContactDetail;
    }

    public void setAMatainContactDetail(String aMatainContactDetail) {
        this.aMatainContactDetail = aMatainContactDetail;
    }

    public BigDecimal getAMntDeptId() {
        return aMntDeptId;
    }

    public void setAMntDeptId(BigDecimal aMntDeptId) {
        this.aMntDeptId = aMntDeptId;
    }

    public Object getATextRoute() {
        return aTextRoute;
    }

    public void setATextRoute(Object aTextRoute) {
        this.aTextRoute = aTextRoute;
    }

    public BigDecimal getAUseDeptId() {
        return aUseDeptId;
    }

    public void setAUseDeptId(BigDecimal aUseDeptId) {
        this.aUseDeptId = aUseDeptId;
    }

    public Integer getDmRuleId() {
        return dmRuleId;
    }

    public void setDmRuleId(Integer dmRuleId) {
        this.dmRuleId = dmRuleId;
    }

    public Integer getDoubleEndId() {
        return doubleEndId;
    }

    public void setDoubleEndId(Integer doubleEndId) {
        this.doubleEndId = doubleEndId;
    }

    public Integer getIntigrityId() {
        return intigrityId;
    }

    public void setIntigrityId(Integer intigrityId) {
        this.intigrityId = intigrityId;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public String getMatainContactDetail() {
        return matainContactDetail;
    }

    public void setMatainContactDetail(String matainContactDetail) {
        this.matainContactDetail = matainContactDetail;
    }

    public BigDecimal getMntDeptId() {
        return mntDeptId;
    }

    public void setMntDeptId(BigDecimal mntDeptId) {
        this.mntDeptId = mntDeptId;
    }

    public BigDecimal getMntPersonId() {
        return mntPersonId;
    }

    public void setMntPersonId(BigDecimal mntPersonId) {
        this.mntPersonId = mntPersonId;
    }

    public Integer getNetLevelId() {
        return netLevelId;
    }

    public void setNetLevelId(Integer netLevelId) {
        this.netLevelId = netLevelId;
    }

    public String getNmCode() {
        return nmCode;
    }

    public void setNmCode(String nmCode) {
        this.nmCode = nmCode;
    }

    public BigDecimal getNmId() {
        return nmId;
    }

    public void setNmId(BigDecimal nmId) {
        this.nmId = nmId;
    }

    public String getNmName() {
        return nmName;
    }

    public void setNmName(String nmName) {
        this.nmName = nmName;
    }

    public Integer getOwnerNetId() {
        return ownerNetId;
    }

    public void setOwnerNetId(Integer ownerNetId) {
        this.ownerNetId = ownerNetId;
    }

    public Integer getPersistenceId() {
        return persistenceId;
    }

    public void setPersistenceId(Integer persistenceId) {
        this.persistenceId = persistenceId;
    }

    public Integer getProtectWayId() {
        return protectWayId;
    }

    public void setProtectWayId(Integer protectWayId) {
        this.protectWayId = protectWayId;
    }

    public Integer getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(Integer protocolId) {
        this.protocolId = protocolId;
    }

    public Integer getQualityLevelId() {
        return qualityLevelId;
    }

    public void setQualityLevelId(Integer qualityLevelId) {
        this.qualityLevelId = qualityLevelId;
    }

    public Integer getReservedLevelId() {
        return reservedLevelId;
    }

    public void setReservedLevelId(Integer reservedLevelId) {
        this.reservedLevelId = reservedLevelId;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public Integer getRouteModeId() {
        return routeModeId;
    }

    public void setRouteModeId(Integer routeModeId) {
        this.routeModeId = routeModeId;
    }

    public Integer getSignalTypeId() {
        return signalTypeId;
    }

    public void setSignalTypeId(Integer signalTypeId) {
        this.signalTypeId = signalTypeId;
    }

    public BigDecimal getUseDeptId() {
        return useDeptId;
    }

    public void setUseDeptId(BigDecimal useDeptId) {
        this.useDeptId = useDeptId;
    }

    public Integer getWavelengthId() {
        return wavelengthId;
    }

    public void setWavelengthId(Integer wavelengthId) {
        this.wavelengthId = wavelengthId;
    }

    public Integer getWorkStateId() {
        return workStateId;
    }

    public void setWorkStateId(Integer workStateId) {
        this.workStateId = workStateId;
    }

    public Integer getZAccessModeId() {
        return zAccessModeId;
    }

    public void setZAccessModeId(Integer zAccessModeId) {
        this.zAccessModeId = zAccessModeId;
    }

    public String getZMatainContactDetail() {
        return zMatainContactDetail;
    }

    public void setZMatainContactDetail(String zMatainContactDetail) {
        this.zMatainContactDetail = zMatainContactDetail;
    }

    public BigDecimal getZMntDeptId() {
        return zMntDeptId;
    }

    public void setZMntDeptId(BigDecimal zMntDeptId) {
        this.zMntDeptId = zMntDeptId;
    }

    public Object getZTextRoute() {
        return zTextRoute;
    }

    public void setZTextRoute(Object zTextRoute) {
        this.zTextRoute = zTextRoute;
    }

    public BigDecimal getZUseDeptId() {
        return zUseDeptId;
    }

    public void setZUseDeptId(BigDecimal zUseDeptId) {
        this.zUseDeptId = zUseDeptId;
    }

    public Integer getReservedTypeId() {
        return reservedTypeId;
    }

    public void setReservedTypeId(Integer reservedTypeId) {
        this.reservedTypeId = reservedTypeId;
    }

    public Integer getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(Integer dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public BigDecimal getALeafRegionId() {
        return aLeafRegionId;
    }

    public void setALeafRegionId(BigDecimal aLeafRegionId) {
        this.aLeafRegionId = aLeafRegionId;
    }

    public BigDecimal getZLeafRegionId() {
        return zLeafRegionId;
    }

    public void setZLeafRegionId(BigDecimal zLeafRegionId) {
        this.zLeafRegionId = zLeafRegionId;
    }

    public Integer getLocationNatureId() {
        return locationNatureId;
    }

    public void setLocationNatureId(Integer locationNatureId) {
        this.locationNatureId = locationNatureId;
    }

    public BigDecimal getLeafRegionId() {
        return leafRegionId;
    }

    public void setLeafRegionId(BigDecimal leafRegionId) {
        this.leafRegionId = leafRegionId;
    }

    public BigDecimal getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(BigDecimal facilityId) {
        this.facilityId = facilityId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getOldId() {
        return oldId;
    }

    public void setOldId(String oldId) {
        this.oldId = oldId;
    }

    public Object getCircuitCode() {
        return circuitCode;
    }

    public void setCircuitCode(Object circuitCode) {
        this.circuitCode = circuitCode;
    }

    public Date getNetTime() {
        return netTime;
    }

    public void setNetTime(Date netTime) {
        this.netTime = netTime;
    }

}

