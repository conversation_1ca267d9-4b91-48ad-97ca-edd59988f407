package com.telecom.nrm.entity;

import lombok.Data;

@Data
public class CmDevice {

    /**
     * 设备id
     */
    private String id;

    /**
     * 设备编码
     */
    private String code;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 网管设备IP
     */
    private String nmIp;

    /**
     * 设备规格
     */
    private String specId;

    /**
     * 设备网络角色
     */
    private String networkRole;


    private String tmlId;

    private String tmlName;


}
