package com.telecom.nrm.entity;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

public abstract class TypeReference<T> {
    private final Type type;

    protected TypeReference() {
        Type superClass = getClass().getGenericSuperclass();
        if (superClass instanceof Class) {
            throw new IllegalArgumentException("需要指定具体泛型类型");
        }
        this.type = ((ParameterizedType) superClass).getActualTypeArguments()[0];
    }

    public Type getType() {
        return type;
    }

    // 快速构建方法
    public static <T> TypeReference<T> of(Class<T> clazz) {
        return new TypeReference<T>() {
            @Override
            public Type getType() {
                return clazz;
            }
        };
    }
}