package com.telecom.nrm.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 隐患整改附件实体类
 */
public class HazardRemediationAttachment {
    
    private BigDecimal id;
    private BigDecimal remediationId; // 关联隐患整改主表ID
    private String originalFilename; // 原始文件名
    private String storedFilename; // 存储在S3的文件名（UUID格式）
    private BigDecimal fileSize; // 文件大小（字节）
    private String fileType; // 文件类型/MIME类型
    private String s3Bucket; // S3存储桶名称
    private String s3Key; // S3存储路径/键名
    private Date uploadTime; // 上传时间
    private String uploader; // 上传人
    
    // 构造函数
    public HazardRemediationAttachment() {}
    
    public HazardRemediationAttachment(BigDecimal remediationId, String originalFilename, 
                                     String storedFilename, BigDecimal fileSize, String fileType, 
                                     String s3Bucket, String s3Key, String uploader) {
        this.remediationId = remediationId;
        this.originalFilename = originalFilename;
        this.storedFilename = storedFilename;
        this.fileSize = fileSize;
        this.fileType = fileType;
        this.s3Bucket = s3Bucket;
        this.s3Key = s3Key;
        this.uploader = uploader;
    }
    
    // Getter和Setter方法
    public BigDecimal getId() {
        return id;
    }
    
    public void setId(BigDecimal id) {
        this.id = id;
    }
    
    public BigDecimal getRemediationId() {
        return remediationId;
    }
    
    public void setRemediationId(BigDecimal remediationId) {
        this.remediationId = remediationId;
    }
    
    public String getOriginalFilename() {
        return originalFilename;
    }
    
    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }
    
    public String getStoredFilename() {
        return storedFilename;
    }
    
    public void setStoredFilename(String storedFilename) {
        this.storedFilename = storedFilename;
    }
    
    public BigDecimal getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(BigDecimal fileSize) {
        this.fileSize = fileSize;
    }
    
    public String getFileType() {
        return fileType;
    }
    
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    
    public String getS3Bucket() {
        return s3Bucket;
    }
    
    public void setS3Bucket(String s3Bucket) {
        this.s3Bucket = s3Bucket;
    }
    
    public String getS3Key() {
        return s3Key;
    }
    
    public void setS3Key(String s3Key) {
        this.s3Key = s3Key;
    }
    
    public Date getUploadTime() {
        return uploadTime;
    }
    
    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }
    
    public String getUploader() {
        return uploader;
    }
    
    public void setUploader(String uploader) {
        this.uploader = uploader;
    }
    
    @Override
    public String toString() {
        return "HazardRemediationAttachment{" +
                "id=" + id +
                ", remediationId=" + remediationId +
                ", originalFilename='" + originalFilename + '\'' +
                ", storedFilename='" + storedFilename + '\'' +
                ", fileSize=" + fileSize +
                ", fileType='" + fileType + '\'' +
                ", s3Bucket='" + s3Bucket + '\'' +
                ", s3Key='" + s3Key + '\'' +
                ", uploadTime=" + uploadTime +
                ", uploader='" + uploader + '\'' +
                '}';
    }
}
