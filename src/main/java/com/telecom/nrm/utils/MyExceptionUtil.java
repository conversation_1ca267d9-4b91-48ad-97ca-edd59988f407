package com.telecom.nrm.utils;

import com.alibaba.nacos.common.utils.ExceptionUtil;

public class MyExceptionUtil {

    private static final Integer MAX_MSG_LENGTH = 2000;

    public static String getExceptionMsg(Exception e) {
        String exceptionMsg = ExceptionUtil.getStackTrace(e);

        String msg = null;
        if (exceptionMsg.length() > MAX_MSG_LENGTH) {
            msg = exceptionMsg.substring(0, MAX_MSG_LENGTH);
        } else {
            msg = exceptionMsg;
        }

        return msg;
    }

}
