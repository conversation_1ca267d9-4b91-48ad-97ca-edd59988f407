package com.telecom.nrm.utils;

public class ExceptionUtil {

    public static String getExceptionMessage(Exception exception, Integer maxLength) {
        String message;
        if (exception instanceof NullPointerException) {
            StackTraceElement stackTraceElement = exception.getStackTrace()[0];
            message = "空指针: " + stackTraceElement.getClassName() + "#" + stackTraceElement.getMethodName()
                    + ":" + stackTraceElement.getLineNumber();
        } else {
            message = exception.getMessage();
        }
        if (maxLength != null && message.length() > maxLength) {
            message = message.substring(0, maxLength);
        }
        return message;
    }

    public static String getExceptionMsg(Exception e, Integer maxLength) {
        String exceptionMsg = com.alibaba.nacos.common.utils.ExceptionUtil.getStackTrace(e);

        String msg = null;
        if (exceptionMsg.length() > maxLength) {
            msg = exceptionMsg.substring(0, maxLength);
        } else {
            msg = exceptionMsg;
        }

        return msg;
    }
}
