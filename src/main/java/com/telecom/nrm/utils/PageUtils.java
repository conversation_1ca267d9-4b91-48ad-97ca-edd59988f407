package com.telecom.nrm.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Consumer;

@Slf4j
public class PageUtils {

    public static void doSomethingByPage(Consumer<List<JSONObject>> consumer, List<JSONObject> paramList, int pageSize) {
        int paramTotal = paramList.size();

        int pageNum  = (paramTotal/pageSize)+1;

        for (int pageIndex=0;pageIndex < pageNum;pageIndex++) {
            int start = pageIndex*pageSize;
            int end = Math.min(start + pageSize, paramTotal);
            if (end<=start) break;
            log.info("start index:{},end index:{}", start, end);
            List<JSONObject> part = paramList.subList(start, end);
            consumer.accept(part);
        }
    }

    public static void doSomeStringByPage(Consumer<List<String>> consumer, List<String> paramList, int pageSize) {
        int paramTotal = paramList.size();

        int pageNum  = (paramTotal/pageSize)+1;

        for (int pageIndex=0;pageIndex < pageNum;pageIndex++) {
            int start = pageIndex*pageSize;
            int end = Math.min(start + pageSize, paramTotal);
            if (end<=start) break;
            log.info("start index:{},end index:{}", start, end);
            List<String> part = paramList.subList(start, end);
            consumer.accept(part);
        }
    }
}
