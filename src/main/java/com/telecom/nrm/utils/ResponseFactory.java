package com.telecom.nrm.utils;

import com.google.common.base.Strings;

public class ResponseFactory {


    public static Result successResponse(String msg, Object data) {
        return new Result(Result.SUCCESS, msg, data);
    }

    public static Result failResponse(String msg, Object data) {
        return new Result(Result.ERROR_999, msg, data);
    }

    public static Result failResponse(int code, String msg) {
        return new Result(String.valueOf(code), msg);
    }


    public static Result successResponse(String msg, boolean msgMode) {
        return new Result(true, msg);
    }

    public static Result successResponse(Object data) {
        return new Result(true, "success", data);
    }


    public static Result failResponse(String msg) {
        if (Strings.isNullOrEmpty(msg)) {
            msg = "接口异常";
        }
        return new Result(false, msg);
    }
}
