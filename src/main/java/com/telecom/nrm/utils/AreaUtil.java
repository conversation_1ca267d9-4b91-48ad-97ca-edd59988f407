package com.telecom.nrm.utils;

/**
 * <AUTHOR> humiao
 * @version :
 * @description :
 * @date : Created in 2022/6/9 15:35
 * @modified by :
 * @lastModifiedDate :
 */
public class AreaUtil {
    public static String ZJ_NO = "0511";
    public static String NJ_NO = "025";
    public static String SZ_NO = "0512";
    public static String CZ_NO = "0519";
    public static String WX_NO = "0510";
    public static String TZ_NO = "0523";
    public static String XZ_NO = "0516";
    public static String NT_NO = "0513";
    public static String YZ_NO = "0514";
    public static String YC_NO = "0515";
    public static String SQ_NO = "0527";
    public static String HA_NO = "0517";
    public static String LYG_NO = "0518";

    public static String getCityNo(){
        String ds = DataSourceHolder.getDataSource();
        if(StringUtil.isEmptyOrNull(ds)){
            return ZJ_NO;
        }
        switch (ds) {
            case "pg_nj":
                return NJ_NO;
            case "pg_sz":
                return SZ_NO;
            case "pg_cz":
                return CZ_NO;
            case "pg_wx":
                return WX_NO;
            case "pg_tz":
                return TZ_NO;
            case "pg_xz":
                return XZ_NO;
            case "pg_nt":
                return NT_NO;
            case "pg_yz":
                return YZ_NO;
            case "pg_yc":
                return YC_NO;
            case "pg_sq":
                return SQ_NO;
            case "pg_ha":
                return HA_NO;
            case "pg_lyg":
                return LYG_NO;
            default:
                return ZJ_NO;
        }
    }

    public static String getCityCode(){
//        String ds = DataSourceHolder.getDataSource();
//        if(StringUtil.isEmptyOrNull(ds)){
//            return "zj";
//        }
//        return ds.replace("pg_","");
        return "js";
    }
}
