package com.telecom.nrm.utils;


import cn.hutool.core.convert.impl.StringConverter;

/**
 * String工具类
 * <AUTHOR>
 * @create 2021/3/8 10:51
 */
public class StringUtil {

    /**
     * 对象转成String
     * @param obj
     * @return
     */
    public static String objectToString(Object obj) {
        return new StringConverter().convertQuietly(obj, "");
    }

    /**
     * 判断对象是否为空
     * @param obj
     * @return
     */
    public static boolean isEmptyOrNull(Object obj) {
        if (null == obj) {
            return true;
        }
        if ("".equals(objectToString(obj))) {
            return true;
        }
        return false;
    }

}
