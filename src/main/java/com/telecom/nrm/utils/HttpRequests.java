package com.telecom.nrm.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> humiao
 * @version :
 * @description :
 * @date : Created in 2022/05/12 10:16
 * @modified by :
 * @lastModifiedDate :
 */

@Slf4j
public class HttpRequests {
    private Map<String,String> headers = new HashMap<>();
    private JSONObject params = new JSONObject();
    public HttpRequests addParam(String name, String value){
        this.params.put(name, value);
        return this;
    }

    public HttpRequests addHeader(String name, String value){
        this.headers.put(name, value);
        return this;
    }

    public String post(String url){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        String json = getParams().toJSONString();
        RequestBody body = RequestBody.create(mediaType, json);
        Request.Builder builder = new Request.Builder()
                .url(url).method("POST", body);
        for(String key : headers.keySet()){
            String value = headers.get(key);
            builder.addHeader(key,value);
        }
        Request request = builder.build();
        BufferedReader reader = null;
        try {
            Response response = client.newCall(request).execute();
            log.info("响应结果：{}",response.body());
            if(response.body() != null){
                return response.body().string();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    public JSONObject getParams() {
        return this.params;
    }

    public void setParams(JSONObject params) {
        this.params = params;
    }

    public static String sendPost(String url,Map<String,String> headers,JSONObject body){
        log.info("请求地址：{}\n 请求头:{}\n 请求体:{}",url,headers,body);
        HttpRequests httpRequests=new HttpRequests();
        httpRequests.setParams(body);
        for (String key:headers.keySet()){
            httpRequests.addHeader(key,headers.get(key));
        }
       return httpRequests.post(url);
    }
 }

