package com.telecom.nrm.utils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

public class AreaMapS {
    public static HashMap<String,String> maps=new LinkedHashMap<>();
    static {

        maps.put("321122930000000000000063", "0518");
        maps.put("321122930000000000000079", "0523");
        maps.put("321122930000000000000017", "0515");
        maps.put("321122930000000000000073", "0527");
        maps.put("321122930000000000000056", "0517");
        maps.put("321122930000000000000006", "025");
        maps.put("321122930000000000000037", "0511");
        maps.put("321122930000000000000013", "0510");
        maps.put("321122930000000000000046", "0516");
        maps.put("321122930000000000000008", "0512");
        maps.put("321122930000000000000024", "0513");
        maps.put("321122930000000000000069", "0519");
        maps.put("321122930000000000000021", "0514");
    }

    public static List<String> areasForLink = Arrays.asList(new String[]{"321122930000000000000013"});

    public static HashMap<String,String> areaCodeMap=new LinkedHashMap<>();
    static {

        areaCodeMap.put("321122930000000000000056", "ha.js.cn");
        areaCodeMap.put("321122930000000000000063", "lyg.js.cn");
        areaCodeMap.put("321122930000000000000073", "sq.js.cn");
        areaCodeMap.put("321122930000000000000006", "nj.js.cn");
        areaCodeMap.put("321122930000000000000037", "zj.js.cn");
        areaCodeMap.put("321122930000000000000013", "wx.js.cn");
        areaCodeMap.put("321122930000000000000024", "nt.js.cn");
        areaCodeMap.put("321122930000000000000008", "sz.js.cn");
        areaCodeMap.put("321122930000000000000069", "cz.js.cn");
        areaCodeMap.put("321122930000000000000079", "tz.js.cn");
        areaCodeMap.put("321122930000000000000021", "yz.js.cn");
        areaCodeMap.put("321122930000000000000017", "yc.js.cn");
        areaCodeMap.put("321122930000000000000046", "xz.js.cn");
    }
}
