package com.telecom.nrm.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpRequestsUtils {

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .proxy(Proxy.NO_PROXY)
            .proxySelector(new ProxySelector() {  // 创建一个空的 ProxySelector
                @Override
                public List<Proxy> select(URI uri) {
                    return Collections.singletonList(Proxy.NO_PROXY);
                }

                @Override
                public void connectFailed(URI uri, SocketAddress sa, IOException ioe) {
                    // 处理连接失败
                }
            })
            .connectTimeout(6000, TimeUnit.SECONDS)
            .writeTimeout(6000, TimeUnit.SECONDS)
            .readTimeout(6000, TimeUnit.SECONDS)
            .build();

    /**
     * Sends an HTTP POST request with the provided URL, headers, and JSON body.
     *
     * @param url     The target URL for the request.
     * @param headers Headers to be included in the request.
     * @param body    The JSON body of the request.
     * @return The response body as a string or null if an error occurs.
     * @throws IOException If an I/O error occurs during the request.
     */
    public static String sendPost(String url, Map<String, String> headers, JSONObject body) throws IOException {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        log.info("\n 请求地址：{}\n 请求头:{}\n 请求体:{}",url,headers,body);
        RequestBody requestBody = RequestBody.create(mediaType, body.toJSONString());
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        try (Response response = CLIENT.newCall(requestBuilder.build()).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return response.body() != null ? response.body().string() : null;
        }
    }


    /**
     * Sends an HTTP GET request with the provided URL and headers.
     *
     * @param url     The target URL for the request.
     * @param headers Headers to be included in the request.
     * @return The response body as a string or null if an error occurs.
     * @throws IOException If an I/O error occurs during the request.
     */
    public static String sendGet(String url, Map<String, String> headers, Map<String, String> params) throws IOException {
        log.info("\n 请求地址：{}\n 请求头:{}\n 请参数:{}",url,headers,params);
        StringBuilder urlBuilder = new StringBuilder(url);
        if (params != null && !params.isEmpty()) {
            urlBuilder.append("?");
            params.forEach((key, value) -> {
                try {
                    urlBuilder.append(URLEncoder.encode(key, StandardCharsets.UTF_8.name()))
                            .append("=")
                            .append(URLEncoder.encode(value, StandardCharsets.UTF_8.name()))
                            .append("&");
                } catch (UnsupportedEncodingException e) {
                    // This should never happen since UTF-8 is always supported
                    throw new RuntimeException(e);
                }
            });
            urlBuilder.setLength(urlBuilder.length() - 1); // Remove the last '&'
        }

        Request.Builder requestBuilder = new Request.Builder()
                .url(urlBuilder.toString())
                .get();
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        try (Response response = CLIENT.newCall(requestBuilder.build()).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return response.body() != null ? response.body().string() : null;
        }
    }

    public static void main(String[] args) throws IOException {
        sendGet("http://132.254.89.105:7070/queryConfigDevice", null, null);
    }
}
