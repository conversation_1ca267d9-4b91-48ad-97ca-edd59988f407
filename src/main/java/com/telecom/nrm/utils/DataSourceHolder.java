package com.telecom.nrm.utils;

/**
 * 数据源切换类
 * <AUTHOR>
 * @create 2020/10/12 14:49
 */
public class DataSourceHolder {
    /**
     * 线程本地环境
     */
    private static final ThreadLocal<String> dataSources = new ThreadLocal<String>();

    /**
     * 设置数据源,动态切换,就是调用这个setDataSource方法
     * @param dataSource
     */
    public static void setDataSource(String dataSource) {
        dataSources.set(dataSource);
    }

    /**
     * 获取数据源
     * @return
     */
    public static String getDataSource() {
        return (String) dataSources.get();
    }

    /**
     * 清除数据源
     */
    public static void clearDataSource() {
        dataSources.remove();
    }
}
