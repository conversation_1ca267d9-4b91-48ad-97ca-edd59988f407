package com.telecom.nrm.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OptGroupElementVO {

    @JsonProperty("ds")
    private String ds;

    @JsonProperty("opt_code")
    private String optCode;

    @JsonProperty("group_id")
    private int groupId;

    @JsonProperty("route_no")
    private String routeNo;

    @JsonProperty("code")
    private String code;

    public JSONObject toJsonObject(){
        return JSONObject.parseObject(JSON.toJSONString(this));
    }

}
