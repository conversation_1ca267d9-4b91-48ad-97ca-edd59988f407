package com.telecom.nrm.domain;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Region {
    //@JSONField(name="id")
    String id;
    //@JSONField(name="entity_spec_id")
    @JsonProperty("entity_spec_id")
    Long entitySpecId;

    //@JSONField(name="name")
    String name;

   //@JSONField(name="code")
    String code;

    //@JSONField(name="area_level_id")
    @JsonProperty("area_level_id")
    Long areaLevelId;

    //@JSONField(name="is_valid")
    @JsonProperty("is_valid")
    Long isValid;

    //@JSONField(name="parent_id")
    @JsonProperty("parent_id")
    String parentId;

}
