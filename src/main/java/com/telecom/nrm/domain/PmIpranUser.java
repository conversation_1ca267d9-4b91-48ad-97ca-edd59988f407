package com.telecom.nrm.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PmIpranUser {
    @JsonProperty("id")
    private BigDecimal id;
    @JsonProperty("accessCode")
    private String accessCode;
    @JsonProperty("bussinessCode")
    private String bussinessCode;
    @JsonProperty("vcid1")
    private String vcid1;
    @JsonProperty("vcid2")
    private String vcid2;
    @JsonProperty("tunnelid")
    private String tunnelid;
    @JsonProperty("vclabelin1")
    private String vclabelin1;
    @JsonProperty("vclabelout1")
    private String vclabelout1;
    @JsonProperty("vclabelin2")
    private String vclabelin2;
    @JsonProperty("vclabelout2")
    private String vclabelout2;
    @JsonProperty("createTime")
    private Date createTime;

    // <PERSON><PERSON> and Setters
}