package com.telecom.nrm.domain;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * (PmIpranTopo)表实体类
 *
 * @since 2024-10-17 15:16:46
 */
@SuppressWarnings("serial")
@Data
public class PmIpranTopo  {
    //源端接入设备类型
    @JsonProperty("S_SERVMODELID")
    private String sServmodelid;
    //源端U设备编码
    @JsonProperty("S_U_DEVNAME")
    private String sUDevname;
    //源端U设备IP
    @JsonProperty("S_U_IP")
    private String sUIp;
    //源端接入端口
    @JsonProperty("S_ACCESSDEVPROP")
    private String sAccessdevprop;
    //用户端口
    @JsonProperty("S_USERPORT")
    private String sUserport;
    //用户端口vlan
    @JsonProperty("S_USERPORTVLAN")
    private String sUserportvlan;
    //源端A1设备编码
    @JsonProperty("S_A_DEVNAME")
    private String sADevname;
    //源端A1设备IP
    @JsonProperty("S_A_IP")
    private String sAIp;
    //源端A2设备编码
    @JsonProperty("S_A_DEVNAME2")
    private String sADevname2;
    //源端A2设备IP
    @JsonProperty("S_A_IP2")
    private String sAIp2;
    //B1设备编码
    @JsonProperty("B1_DEVNAME")
    private String b1Devname;
    //B1设备IP
    @JsonProperty("B1_IP")
    private String b1Ip;
    //B1设备编码
    @JsonProperty("B2_DEVNAME")
    private String b2Devname;
    //B1设备IP
    @JsonProperty("B2_IP")
    private String b2Ip;
    //专线编码
    @JsonProperty("SERVCODE")
    private String servcode;
    //VCID1
    @JsonProperty("VCID1")
    private String vcid1;
    //VCID2
    @JsonProperty("VCID2")
    private String vcid2;
    //业务带宽
    @JsonProperty("BANDWIDTH")
    private String bandwidth;
    //客户名称
    @JsonProperty("CUSTNAME")
    private String custname;
    //客户等级
    @JsonProperty("CUSTLEVEL")
    private String custlevel;
    //业务类型
    @JsonProperty("SERVTYPE")
    private String servtype;
    //业务场景
    @JsonProperty("OPERTYPE")
    private String opertype;
    //安装地址
    @JsonProperty("INSTADDRESS")
    private String instaddress;
    //开通省份
    @JsonProperty("PROVINCE")
    private String province;
    //开通城市
    @JsonProperty("CITY")
    private String city;
    //端口类型
    @JsonProperty("PORTTYPE")
    private String porttype;
    //B3设备编码
    @JsonProperty("B3_DEVNAME")
    private String b3Devname;
    //B3_IP
    @JsonProperty("B3_IP")
    private String b3Ip;
    //B4设备编码
    @JsonProperty("B4_DEVNAME")
    private String b4Devname;
    //B4_IP
    @JsonProperty("B4_IP")
    private String b4Ip;
    //宿端A1设备编码
    @JsonProperty("T_A_DEVNAME")
    private String tADevname;
    //宿端A1设备IP
    @JsonProperty("T_A_IP")
    private String tAIp;
    //宿端A2设备编码
    @JsonProperty("T_A_DEVNAME2")
    private String tADevname2;
    //宿端A2设备IP
    @JsonProperty("T_A_IP2")
    private String tAIp2;
    //宿端U设备编码
    @JsonProperty("T_U_DEVNAME")
    private String tUDevname;
    //宿端U设备IP
    @JsonProperty("T_U_IP")
    private String tUIp;
    //宿端接入端口
    @JsonProperty("T_ACCESSDEVPROP")
    private String tAccessdevprop;
    //宿端用户端口
    @JsonProperty("T_USERPORT")
    private String tUserport;
    //宿端接入端口vlan
    @JsonProperty("T_USERPORTVLAN")
    private String tUserportvlan;
    //宿端接入设备类型
    @JsonProperty("SERVMODELID")
    private String servmodelid;
    //备注
    @JsonProperty("EXECLOG")
    private String execlog;

}

