package com.telecom.nrm.domain.report;

import lombok.Data;

import java.util.Date;

/**
 * IDC 标准化报告
 */
@Data
public class IReport {

    private String fileName; // 文件名, 例如：XX.pdf

    private String detailFileName; //详单

    // 标题
    private String title;


    // 开始时间
    private String startDate;

    // 截至时间
    private String endDate;

    // 稽核数据开始时间
    private String dataStartDate;

    // 稽核数据截至时间
    private String dataEndDate;

    // 报告时间
    private String reportDate; // 默认是当前时间

    // 本周期
    private SegmentReport currentCycle = new SegmentReport();

    // 整体=>上线以来
    private SegmentReport totalCycle = new SegmentReport();

}
