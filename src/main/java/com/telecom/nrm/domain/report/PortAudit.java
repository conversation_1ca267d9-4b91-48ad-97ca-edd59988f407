package com.telecom.nrm.domain.report;

import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class PortAudit {

    // 操作总数
    int total;

    // 有风险次数
    int risk;

    //复核风险次数
    int reviewRisk;

    String citys; // 涉及地市

    List<RiskItem> riskCatalogs = new ArrayList<>(); // 风险分类

    List<PortImprovement> portImprovements = new ArrayList<>(); // 整改情况

    // 添加个风险分类
    public PortAudit addRiskCatalog(RiskItem item) {
        this.riskCatalogs.add(item);
        return this;
    }

    public List<RiskItem> getHasRiskCatalogs() {
        return this.riskCatalogs.stream().filter(r -> r.isRisk() == true).collect(Collectors.toList());
    }

    public PortAudit addPortImprovement(PortImprovement item) {
        this.portImprovements.add(item);
        return this;
    }

    public void statisticImprovements() {
        // 统计下风险总数合计
        DecimalFormat df = new DecimalFormat("0.00");
        PortImprovement improvement = PortImprovement.builder().city("合计").build();
        for (int i = 0; i < this.portImprovements.size(); i++) {
            improvement.setRiskCount(this.portImprovements.get(i).getRiskCount() + improvement.getRiskCount());
            improvement.setComplete(this.portImprovements.get(i).getComplete() + improvement.getComplete());
            improvement.setDispatch(this.portImprovements.get(i).getDispatch() + improvement.getDispatch());
            improvement.setReviewCount(this.portImprovements.get(i).getReviewCount() + improvement.getReviewCount());
            improvement.setCompletePercent(df.format((double) improvement.getComplete() / (double) improvement.getDispatch() * 100) + "%");
            improvement.setReviewPercent(df.format((double) improvement.getReviewCount()/(double) improvement.getRiskCount()* 100) + "%");
            improvement.setOpenReviewCount(this.portImprovements.get(i).getOpenReviewCount() + improvement.getOpenReviewCount());
            improvement.setCutReviewCount(this.portImprovements.get(i).getCutReviewCount() + improvement.getCutReviewCount());
            improvement.setClosePortReviewCount(this.portImprovements.get(i).getClosePortReviewCount() + improvement.getClosePortReviewCount());
            improvement.setNotNeedReviewCount(this.portImprovements.get(i).getNotNeedReviewCount() + improvement.getNotNeedReviewCount());
            improvement.setRelayReviewCount(this.portImprovements.get(i).getRelayReviewCount() + improvement.getRelayReviewCount());
            improvement.setDispatchTimeOut(this.portImprovements.get(i).getDispatchTimeOut() + improvement.getDispatchTimeOut());
        }
        this.portImprovements.add(improvement);
    }


}
