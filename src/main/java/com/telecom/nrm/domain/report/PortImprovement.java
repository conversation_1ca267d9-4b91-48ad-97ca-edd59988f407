package com.telecom.nrm.domain.report;

import lombok.Builder;
import lombok.Data;

/**
 * 端口操作整改
 */
@Builder
@Data
public class PortImprovement {
    private String city;

    private int riskCount = 0;

    private int dispatch = 0;

    private int dispatchTimeOut = 0;

    private int complete = 0; // 完成

    private int reviewCount = 0; // 完成复核+无需复核

    private int openReviewCount = 0; // 补开通单消除

    private int relayReviewCount = 0; // 补中继单消除

    private int cutReviewCount = 0; // 补割接单消除

    private int closePortReviewCount = 0; // 关闭端口消除

    private int notNeedReviewCount = 0; // 无需复核消除

    private String reviewPercent; // 完成复核+无需复核占比

    private String completePercent; // 完成整改占比
}
