package com.telecom.nrm.domain.report;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class RiskItem {

    String title; // 类目

    int count; // 次数

    String percent; // 占比

    String description = ""; // 现象

    String checkRule = "";// 稽核规则

    String suggestion = ""; // 建议


    String exampleOperateTime;

    String exampleDeviceIp;

    String examplePortName;

    String exampleAction;

    String exampleOperationName;

    String exampleOperationDept;

    String city;

    String riskAnalysis;

    boolean risk = true; // 表示有风险
 }
