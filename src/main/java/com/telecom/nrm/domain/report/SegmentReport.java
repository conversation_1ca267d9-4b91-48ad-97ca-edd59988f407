package com.telecom.nrm.domain.report;

import lombok.Data;

import java.util.Date;

@Data
public class SegmentReport {

    // 开始时间
    private String startDate;

    // 截至时间
    private String endDate;

    // 端口稽核情况
    private PortAudit portAudit = new PortAudit(); // 端口稽核

    private PortReviewAudit portReviewAudit = new PortReviewAudit(); // 端口复核

    private  PortRate portRate = new PortRate(); // 速率稽核

    private  PortTraffic portTraffic = new PortTraffic(); // 流量稽核
}
