package com.telecom.nrm.domain;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class ProductInBO {

    private String productTypeID;
    private String productName;
    private int sum;
    private Integer regionId;

    public static List<ProductInBO> getList(JSONArray jsonArray) {
        List<ProductInBO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(jsonArray)) return null;
        for (Object o : jsonArray) {
            JSONObject jsonObject = (JSONObject) o;
            result.add(new ProductInBO(jsonObject));
        }
        return result;
    }

    public ProductInBO(JSONObject jsonObject) {

        this.productTypeID = jsonObject.getString("productTypeID");
        this.productName = jsonObject.getString("productName");
        this.sum = jsonObject.getInteger("sum");
        this.regionId = jsonObject.getInteger("regionId");

    }

    public String getProductTypeID() {
        return productTypeID;
    }

    public void setProductTypeID(String productTypeID) {
        this.productTypeID = productTypeID;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public int getSum() {
        return sum;
    }

    public void setSum(int sum) {
        this.sum = sum;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    @Override
    public String toString() {
        return "ProductInfo{" +
                "productTypeID='" + productTypeID + '\'' +
                ", productName='" + productName + '\'' +
                ", sum=" + sum +
                ", regionId=" + regionId +
                '}';
    }
}