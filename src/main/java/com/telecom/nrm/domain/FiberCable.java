package com.telecom.nrm.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class FiberCable {
    @JsonProperty("id")
    String id;
    @JsonProperty("spec_id")
    String specId;
    @JsonProperty("code")
    String code;
    @JsonProperty("name")
    String name;
    @JsonProperty("a_device_id")
    String aphysicDeviceId;
    @JsonProperty("z_device_id")
    String zphysicDeviceId;
    @JsonProperty("capacity")
    String capacity;
}
