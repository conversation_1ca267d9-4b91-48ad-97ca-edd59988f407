package com.telecom.nrm.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OptGroupQueryVO {

    @JsonProperty("area_name")
    private String areaName;

    @JsonProperty("leafRegion")
    private String leafRegion;

    @JsonProperty("name")
    private String name;

    @JsonProperty("code")
    private String code;

    @JsonProperty("speciality")
    private String speciality;

    @JsonProperty("customer")
    private String customer;

    @JsonProperty("maintainers")
    private String maintainers;

    @JsonProperty("note")
    private String note;

    @JsonProperty("ds")
    private String ds;

    public JSONObject toJsonObject() {
        return JSONObject.parseObject(JSON.toJSONString(this));
    }


}
