package com.telecom.nrm.domain.graph;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;


import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class Graph implements Serializable,Cloneable {
    private Set<Node> nodes = new HashSet<>();

    private Set<Edge> edges = new HashSet<>();



    @JSONField(serialize =false)
    public void addNode(Node node) {
        nodes.add(node);
    }

    @JSONField(serialize =false)
    public void addEdge (Edge edge) {
        edges.add(edge);
    }


    @JSONField(serialize =false)
    @JsonIgnore
    public void deleteNode(Node node) {

        Set<Edge> outEdgeSet=getOutEdges(node.getId());
        for (Edge e: outEdgeSet) {
            deleteEdge(e);
        }

        Set<Edge> inEdgeSet=getInEdges(node.getId());
        for (Edge e: inEdgeSet) {
            deleteEdge(e);
        }

        nodes.remove(node);
    }

    @J<PERSON>NField(serialize =false)
    @JsonIgnore
    public void deleteEdge(Edge edge) {
        edges.remove(edge);
    }

    @JSONField(serialize =false)
    @JsonIgnore
    public Set<Edge> getInEdges(String nodeId) {
        Set<Edge> result = new HashSet<>();
        for (Edge e : edges) {
            if (e.getTarget().equals(nodeId)) {
                result.add(e);
            }
        }
        return result;
    }
    @JSONField(serialize =false)
    @JsonIgnore
    public Set<Edge> getOutEdges(String nodeId) {
        Set<Edge> result = new HashSet<>();
        for (Edge e : edges) {
            if (e.getSource().equals(nodeId)) {
                result.add(e);
            }
        }
        return result;
    }

    @JSONField(serialize =false)
    @JsonIgnore
    public Node getNodeById (String id) {
        for (Node n : nodes ) {
            if (n.getId().equals(id)) {
                return n;
            }
        }
        return null;
    }

    @JSONField(serialize =false)
    @JsonIgnore
    public Set<Node> findFirstNodes () {
        Set<Node> results = new HashSet<>();
        for (Node n : nodes ) {
            if (this.getInEdges(n.getId()).size()==0) {
                results.add(n);
            }
        }
        return results;
    }


    @Override
    public Graph clone() {
        Graph clone = new Graph();
        Set<Node> nodeSet = new HashSet<>();
        nodeSet.addAll(this.nodes);

        Set<Edge> edgeSet = new HashSet<>();
        edgeSet.addAll(this.edges);



        clone.setNodes(nodeSet);
        clone.setEdges(edgeSet);

        
        return clone;
    }

    @JSONField(serialize =false)
    @JsonIgnore
    public List<Edge> sortEdgesBFS() {
        Map<String, List<Edge>> adjacencyList = new HashMap<>();;
        this.edges.forEach(edge-> adjacencyList
                    .computeIfAbsent(edge.getSource(), k -> new ArrayList<>())
                    .add(edge)
        );

        Map<String, Boolean> visited = new HashMap<>();
        LinkedList<String> queue = new LinkedList<>();
        List<Edge> sortedEdges = new ArrayList<>();

        // 找到起始点（没有入度的点）
        for (Edge edge : edges) {
            if (!isTarget(edge.getSource(), edges)) {
                queue.add(edge.getSource());
                visited.put(edge.getSource(), true);
            }
        }

        while (!queue.isEmpty()) {
            String currentNode = queue.poll();
            List<Edge> outgoingEdges =adjacencyList.get(currentNode);
            if (outgoingEdges != null) {
                for (Edge edge : outgoingEdges) {
                    sortedEdges.add(edge);
                    if (!visited.containsKey(edge.getTarget())) {
                        queue.add(edge.getTarget());
                        visited.put(edge.getTarget(), true);
                    }
                }
            }
        }

        return sortedEdges;
    }
    @JSONField(serialize =false)
    @JsonIgnore
    private static boolean isTarget(String node, Set<Edge> edges) {
        for (Edge edge : edges) {
            if (edge.getTarget().equals(node)) {
                return true;
            }
        }
        return false;
    }


}
