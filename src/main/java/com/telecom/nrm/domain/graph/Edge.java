package com.telecom.nrm.domain.graph;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class Edge implements Serializable {

    public Edge() {

    }

    public Edge(String aId,String bId, String type) {
        this.aId = aId;
        this.bId = bId;
        this.type = type;
    }

    public int hashCode(){
        return getId().hashCode();
    }

    public String getId(){
        String id=aId+"_"+type+"_"+bId;
        return id;
    }

    public String getReverseId(){
        String id=bId+"_"+type+"_"+aId;
        return id;
    }

    public boolean equals (Object e) {
        return getId().equals(((Edge)e).getId());
    }



    String source;
    String target;
    String type;
    String apiCode;
    String apiVersion;


    String aId;
    String bId;

    String dataSourceType;

    List<JSONObject> data = new ArrayList<>();

    public Boolean isReverse() {
        if (source.equals(aId)) {
            return false;
        }else{
            return true;
        }
    }




}
