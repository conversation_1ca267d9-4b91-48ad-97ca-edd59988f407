package com.telecom.nrm.domain.graph;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class Node implements Serializable {

    public int hashCode() {
        return id.hashCode();
    }

    public boolean equals (Object e) {
        return getId().equals(((Node)e).getId());
    }

    String id;
    String name;
    String code;
    String apiCode;
    String apiVersion;

    String method="union";

    Double x;
    Double y;

    String labels;
    String dataSourceType;

    List<JSONObject> data = new ArrayList<>();
}
