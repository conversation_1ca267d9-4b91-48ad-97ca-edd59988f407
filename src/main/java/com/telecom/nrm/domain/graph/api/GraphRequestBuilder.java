package com.telecom.nrm.domain.graph.api;

import java.util.List;

public class GraphRequestBuilder {
    GraphRequest request = new GraphRequest();

    public GraphRequestBuilder setCjType(String cjType) {
        request.businessParam.setCjType(cjType);
        return this;
    }

    public GraphRequestBuilder setApiId(String apiId) {
        request.businessParam.setApiId(apiId);
        return this;
    }

    public GraphRequestBuilder setShardingCode(String shardingCode) {
        request.businessParam.setShardingCode(shardingCode);
        return this;
    }

    public GraphRequestBuilder setCustomer(String customer) {
        request.businessParam.setCustomer(customer);
        return this;
    }

    public GraphRequestBuilder whereBody(List<GraphWhereBodyItem> whereBody) {
        request.setWhereBody(whereBody);
        return this;
    }

    public GraphRequestBuilder appendWhereBodyItem(GraphWhereBodyItem whereBodyItem) {
        request.whereBody.add(whereBodyItem);
        return this;
    }

    public GraphRequestBuilder appendFilter(String labelCode, GraphFilter filter) {
        request.filters.put(labelCode, filter);
        return this;
    }

    public GraphRequest build() {
        return this.request;
    }
}
