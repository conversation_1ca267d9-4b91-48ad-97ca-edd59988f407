package com.telecom.nrm.domain.graph.api;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class GraphRequest {
    GraphParam businessParam = new GraphParam();
    List<GraphWhereBodyItem> whereBody = new ArrayList<>();
    GraphPage page = new GraphPage();

    Map<String, GraphFilter> filters = new HashMap<>();

    @JSONField(serialize = false)
    public JSONObject getWhereParam() {
        JSONObject param = new JSONObject();

        for (GraphWhereBodyItem item: whereBody) {
            String nodeLabel = item.getNodeLabel();
            String property = item.getPropertyLabel();
            Object value = item.getPropertyValue();
            if (!param.containsKey(nodeLabel)) {
                JSONObject node = new JSONObject();
                node.put(property,value);
                param.put(nodeLabel,node);
            } else {
                JSONObject node = param.getJSONObject(nodeLabel);
                node.put(property,value);
            }
        }
        return param;
    }
}
