package com.telecom.nrm.domain.graph;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class GraphScene {
    String id;

    String name;

    String code;

    @JSONField(name="create_time", format = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "GMT+8")
    Date createTime = new Date();

    @JSONField(name="start_node")
    @JsonProperty("start_node")
    String startNode;

    @JSONField(name="graph_data")
    @JsonProperty("graph_data")
    String graphData;

    Graph graph = new Graph();


}
