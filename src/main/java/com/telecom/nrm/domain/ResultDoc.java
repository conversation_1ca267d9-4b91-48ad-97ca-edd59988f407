package com.telecom.nrm.domain;


//import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
//import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;


//@Data
//@JacksonXmlRootElement(localName = "ResultDoc")
public class ResultDoc {
//
//    @JacksonXmlProperty(localName = "ReturnCode")
//    private String  ReturnCode;
//
//    @JacksonXmlProperty(localName = "CauseId")
//    private String  CauseId;
//
//    @JacksonXmlProperty(localName = "Message")
//    private String  Message;

}
