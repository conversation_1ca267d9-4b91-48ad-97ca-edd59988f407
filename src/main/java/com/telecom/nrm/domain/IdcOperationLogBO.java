package com.telecom.nrm.domain;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * (IdcOperationLog)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-30 10:17:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class IdcOperationLogBO {
        
    private Long id;
        
    private String traceId;
        
    private BigDecimal orderId;
        
    private BigDecimal gomWorkOrderId;
        
    private String operationId;
        
    private String operationName;
        
    private String operationType;
        
    private Date operationDate;
        
    private String operationDescription;
        
    private Integer isException;
        
    private String exceptionMessage;
        
    private String fromSource;
        
    private BigDecimal eventId;

    private String methodMark;

    private String methodResult;

    public IdcOperationLogBO(String traceId, String operationId, String operationName, Date operationDate, String operationDescription, Integer isException,
                             String exceptionMessage) {
        this.traceId = traceId;
        this.operationId = operationId;
        this.operationName = operationName;
        this.operationDate = operationDate;
        this.operationDescription = operationDescription;
        this.isException = isException;
        this.exceptionMessage = exceptionMessage;
    }

    public IdcOperationLogBO(String operationId, String operationName, Date operationDate, String operationDescription, Integer isException,
                             String exceptionMessage) {
        this.operationId = operationId;
        this.operationName = operationName;
        this.operationDate = operationDate;
        this.operationDescription = operationDescription;
        this.isException = isException;
        this.exceptionMessage = exceptionMessage;
    }

    public IdcOperationLogBO(BigDecimal orderId, BigDecimal gomWorkOrderId, String operationType, String operationDescription, String fromSource) {
        this.orderId = orderId;
        this.gomWorkOrderId = gomWorkOrderId;
        this.operationType = operationType;
        this.operationDescription = operationDescription;
        this.fromSource = fromSource;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public BigDecimal getOrderId() {
        return orderId;
    }

    public void setOrderId(BigDecimal orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getGomWorkOrderId() {
        return gomWorkOrderId;
    }

    public void setGomWorkOrderId(BigDecimal gomWorkOrderId) {
        this.gomWorkOrderId = gomWorkOrderId;
    }

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public Date getOperationDate() {
        return operationDate;
    }

    public void setOperationDate(Date operationDate) {
        this.operationDate = operationDate;
    }

    public String getOperationDescription() {
        return operationDescription;
    }

    public void setOperationDescription(String operationDescription) {
        this.operationDescription = operationDescription;
    }

    public Integer getIsException() {
        return isException;
    }

    public void setIsException(Integer isException) {
        this.isException = isException;
    }

    public String getExceptionMessage() {
        return exceptionMessage;
    }

    public void setExceptionMessage(String exceptionMessage) {
        this.exceptionMessage = exceptionMessage;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public BigDecimal getEventId() {
        return eventId;
    }

    public void setEventId(BigDecimal eventId) {
        this.eventId = eventId;
    }

    public String getMethodMark() {
        return methodMark;
    }

    public void setMethodMark(String methodMark) {
        this.methodMark = methodMark;
    }

    public String getMethodResult() {
        return methodResult;
    }

    public void setMethodResult(String methodResult) {
        this.methodResult = methodResult;
    }

}

