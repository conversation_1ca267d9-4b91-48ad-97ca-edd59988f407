package com.telecom.nrm.domain;

import java.util.HashMap;

public class NRMConstants {
    public static final String SHARDING_CODE="ds_net_resource_js";

    public static final String SHARDING_GRAPH_DB="ds_graph_js";
    public static final String MAINDATA="ds_main_data_js";
    public static final String BUCKET_NAME="oss-wzrh";
    public static final String IDC_DATA="ds_idc_js";
    public static final Long ROUTE_SPEC_ID=1024600001L ;


    public static final Long CARD_ENTITY_SPEC_ID=1030400001L;

    public static final Long SLOT_ENTITY_SPEC_ID=1030300001L;

    public static final Long PORT_ENTITY_SPEC_ID=1041100003L;

    public static final Long SHELF_ENTITY_SPEC_ID=1030200001L;

    public static final HashMap<String, String> AREA_CODE_MAP = new HashMap<String, String>() {{
        put("南京", "NJ");
        put("苏州", "SZ");
        put("南通", "NT");
        put("扬州", "YZ");
        put("连云港", "LYG");
        put("常州", "CZ");
        put("无锡", "WX");
        put("宿迁", "SQ");
        put("镇江", "ZJ");
        put("盐城", "YC");
        put("泰州", "TZ");
        put("淮安", "HA");
        put("徐州", "XZ");
        put("江苏", "A01");
        put("JS","A01");
        put("js", "A01");
    }
    };

    public static final HashMap<String, String> AREA_NAME_MAP = new HashMap<String, String>() {{
        put("nj", "南京");
        put("sz", "苏州");
        put("nt", "南通");
        put("yz", "扬州");
        put("lyg", "连云港");
        put("cz", "常州");
        put("wx", "无锡");
        put("sq", "宿迁");
        put("zj", "镇江");
        put("yc", "盐城");
        put("tz", "泰州");
        put("ha", "淮安");
        put("xz", "徐州");
    }
    };

    public static final HashMap<String, Integer> LOCAL_AREA_ID = new HashMap<String, Integer>() {{
        put("nj", 3  );
        put("zj", 4  );
        put("wx", 15 );
        put("sz", 20 );
        put("nt",26 );
        put("xz", 48 );
        put("lyg", 63 );
        put("cz", 69 );
        put("sq", 84 );
        put("yz", 33 );
        put("yc", 39 );
        put("ha", 60 );
        put("tz", 79 );
    }
    };

    public static final HashMap<String, String> NAT_ABBREVIATION_MAP = new HashMap<String, String>() {{
        put( "南京分公司","ds_odso_nj");
        put( "苏州分公司","ds_odso_sz");
        put( "南通分公司","ds_odso_nt");
        put( "扬州分公司","ds_odso_yz");
        put( "连云港分公司","ds_odso_lyg");
        put( "常州分公司","ds_odso_cz");
        put( "无锡分公司","ds_odso_wx");
        put( "宿迁分公司","ds_odso_sq");
        put( "镇江分公司","ds_odso_zj");
        put( "盐城分公司","ds_odso_yc");
        put( "泰州分公司","ds_odso_tz");
        put( "淮安分公司","ds_odso_ha");
        put( "徐州分公司","ds_odso_xz");
    }
    };

    public static final HashMap<String, String> FAULT_AREA_NAME = new HashMap<String, String>() {{
        put( "南京","ds_odso_nj");
        put( "苏州","ds_odso_sz");
        put( "南通","ds_odso_nt");
        put( "扬州","ds_odso_yz");
        put( "连云港","ds_odso_lyg");
        put( "常州","ds_odso_cz");
        put( "无锡","ds_odso_wx");
        put( "宿迁","ds_odso_sq");
        put( "镇江","ds_odso_zj");
        put( "盐城","ds_odso_yc");
        put( "泰州","ds_odso_tz");
        put( "淮安","ds_odso_ha");
        put( "徐州","ds_odso_xz");
    }
    };




    public static final HashMap<String, String> AREA_NAME_TO_ID_MAP = new HashMap<String, String>(){
        {
            put("南京", "321122930000000000000006");
            put("苏州", "321122930000000000000008");
            put("南通", "321122930000000000000024");
            put("扬州", "321122930000000000000021");
            put("连云港", "321122930000000000000063");
            put("常州", "321122930000000000000069");
            put("无锡", "321122930000000000000013");
            put("宿迁", "321122930000000000000073");
            put("镇江", "321122930000000000000037");
            put("盐城", "321122930000000000000017");
            put("泰州", "321122930000000000000079");
            put("淮安", "321122930000000000000056");
            put("徐州", "321122930000000000000046");
        }
    };



    public static final HashMap<String, String> ZD_NATIVENET_ID = new HashMap<String, String>(){
        {
            put( "0000003", "ds_odso_nj");//"南京"
            put( "0000013", "ds_odso_zj");//"镇江"
            put( "0000004", "ds_odso_wx");//"无锡"
            put( "0000007", "ds_odso_sz");//"苏州"
            put( "0000008", "ds_odso_nt");//"南通"
            put( "0000012", "ds_odso_yz");//"扬州"
            put( "0000011", "ds_odso_yc");//"盐城"
            put( "0000005", "ds_odso_xz");//"徐州"
            put( "0000010", "ds_odso_ha");//"淮安"
            put("0000009", "ds_odso_lyg");//"连云港"
            put( "0000006", "ds_odso_cz");//"常州"
            put( "0000014", "ds_odso_tz");//"泰州"
            put( "0000015", "ds_odso_sq");//"宿迁"
        }
    };


















    public static final HashMap<String, String> AREA_NAME_WLYY_DATABSE_MAP = new HashMap<String, String>(){
        {
            put("南京", "ds_bc_o3_nj");
            put("苏州", "ds_bc_o3_sz");
            put("南通", "ds_bc_o3_nt");
            put("扬州", "ds_bc_o3_yz");
            put("连云港", "ds_bc_o3_lyg");
            put("常州", "ds_bc_o3_cz");
            put("无锡", "ds_bc_o3_wx");
            put("宿迁", "ds_bc_o3_sq");
            put("镇江", "ds_bc_o3_zj");
            put("盐城", "ds_bc_o3_yc");
            put("泰州", "ds_bc_o3_tz");
            put("淮安", "ds_bc_o3_ha");
            put("徐州", "ds_bc_o3_xz");

            put("nj", "ds_bc_o3_nj");
            put("sz", "ds_bc_o3_sz");
            put("nt", "ds_bc_o3_nt");
            put("yz", "ds_bc_o3_yz");
            put("lyg", "ds_bc_o3_lyg");
            put("cz", "ds_bc_o3_cz");
            put("wx", "ds_bc_o3_wx");
            put("sq", "ds_bc_o3_sq");
            put("zj", "ds_bc_o3_zj");
            put("yc", "ds_bc_o3_yc");
            put("tz", "ds_bc_o3_tz");
            put("ha", "ds_bc_o3_ha");
            put("xz", "ds_bc_o3_xz");

            put("NJ", "ds_bc_o3_nj");
            put("SZ", "ds_bc_o3_sz");
            put("NT", "ds_bc_o3_nt");
            put("YZ", "ds_bc_o3_yz");
            put("LYG", "ds_bc_o3_lyg");
            put("CZ", "ds_bc_o3_cz");
            put("WX", "ds_bc_o3_wx");
            put("SQ", "ds_bc_o3_sq");
            put("ZJ", "ds_bc_o3_zj");
            put("YC", "ds_bc_o3_yc");
            put("TZ", "ds_bc_o3_tz");
            put("HA", "ds_bc_o3_ha");
            put("XZ", "ds_bc_o3_xz");

            put("321122930000000000000006", "ds_bc_o3_nj");
            put("321122930000000000000008", "ds_bc_o3_sz");
            put("321122930000000000000024", "ds_bc_o3_nt");
            put("321122930000000000000021", "ds_bc_o3_yz");
            put("321122930000000000000063", "ds_bc_o3_lyg");
            put("321122930000000000000069", "ds_bc_o3_cz");
            put("321122930000000000000013", "ds_bc_o3_wx");
            put("321122930000000000000073", "ds_bc_o3_sq");
            put("321122930000000000000037", "ds_bc_o3_zj");
            put("321122930000000000000017", "ds_bc_o3_yc");
            put("321122930000000000000079", "ds_bc_o3_tz");
            put("321122930000000000000056", "ds_bc_o3_ha");
            put("321122930000000000000046", "ds_bc_o3_xz");

            put("8321300", "ds_bc_o3_sq");
            put("8320800", "ds_bc_o3_ha");
            put("8320700", "ds_bc_o3_lyg");
            put("8320500", "ds_bc_o3_sz");
            put("8320400", "ds_bc_o3_cz");
            put("8321200", "ds_bc_o3_tz");
            put("8320100", "ds_bc_o3_nj");
            put("8321100", "ds_bc_o3_zj");
            put("8320200", "ds_bc_o3_wx");
            put("8320600", "ds_bc_o3_nt");
            put("8321000", "ds_bc_o3_yz");
            put("8320900", "ds_bc_o3_yc");
            put("8320300", "ds_bc_o3_xz");
        }
    };

    /*
    public static final HashMap<String, String> AREA_ID_ODSO_DATABSE_MAP = new HashMap<String, String>(){
        {
            put("321122930000000000000006", "ds_odso_nj");
            put("321122930000000000000008", "ds_odso_sz");
            put("321122930000000000000024", "ds_odso_nt");
            put("321122930000000000000021", "ds_odso_yz");
            put("321122930000000000000063", "ds_odso_lyg");
            put("321122930000000000000069", "ds_odso_cz");
            put("321122930000000000000013", "ds_odso_wx");
            put("321122930000000000000073", "ds_odso_sq");
            put("321122930000000000000037", "ds_odso_zj");
            put("321122930000000000000017", "ds_odso_yc");
            put("321122930000000000000079", "ds_odso_tz");
            put("321122930000000000000056", "ds_odso_ha");
            put("321122930000000000000046", "ds_odso_xz");
        }
    };

     */

    /*
    public static final HashMap<String, String> ResDatabaseMap = new HashMap<String, String>(){
        {
            put("8321300", "ds_res_sq");
            put("8320800", "ds_res_ha");
            put("8320700", "ds_res_lyg");
            put("8320500", "ds_res_sz");
            put("8320400", "ds_res_cz");
            put("8321200", "ds_res_tz");
            put("8320100", "ds_res_nj");
            put("8321100", "ds_res_zj");
            put("8320200", "ds_res_wx");
            put("8320600", "ds_res_nt");
            put("8321000", "ds_res_yz");
            put("8320900", "ds_res_yc");
            put("8320300", "ds_res_xz");
        }
    };

     */


    public static Integer PROJECT_TYPE_CUT_OVER= 1; // 割接单

    public static Integer PROJECT_TYPE_OTHER= 2;

    public static Integer PROJECT_TYPE_CUT_OVER_FROM_NOC= 3;


}
