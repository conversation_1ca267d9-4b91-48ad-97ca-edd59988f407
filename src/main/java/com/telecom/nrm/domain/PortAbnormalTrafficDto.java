package com.telecom.nrm.domain;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PortAbnormalTrafficDto {
    @JsonProperty("id")
    private BigDecimal id;

    @JsonProperty("shijian")
    private String shijian;

    @JsonProperty("areaName")
    private String areaName;

    @JsonProperty("stationName")
    private String stationName;

    @JsonProperty("roomName")
    private String roomName;

    @JsonProperty("devCode")
    private String devCode;

    @JsonProperty("devName")
    private String devName;

    @JsonProperty("devAddr")
    private String devAddr;

    @JsonProperty("portName")
    private String portName;

    @JsonProperty("portCode")
    private String portCode;

    @JsonProperty("usingstate")
    private String usingstate;

    @JsonProperty("businessCode")
    private String businessCode;

    @JsonProperty("cusName")
    private String cusName;

    @JsonProperty("crmCustOrderCode")
    private String crmCustOrderCode;

    @JsonProperty("gomOrderState")
    private String gomOrderState;

    @JsonProperty("isArchived")
    private String isArchived;

    @JsonProperty("timeFor")
    private String timeFor;

    @JsonProperty("ip")
    private String ip;

    @JsonProperty("tagkey")
    private String tagkey;

    @JsonProperty("outval")
    private double outval;

    @JsonProperty("inVal")
    private double inVal;

    @JsonProperty("abnormalType")
    private String abnormalType;

    @JsonProperty("createDate")
    private String createDate;
}
