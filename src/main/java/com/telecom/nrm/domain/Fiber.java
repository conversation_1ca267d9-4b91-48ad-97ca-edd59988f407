package com.telecom.nrm.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Fiber {
    @JsonProperty("id")
    String id;
    @JsonProperty("spec_id")
    String specId;
    @JsonProperty("code")
    String code;
    @JsonProperty("name")
    String name;
    @JsonProperty("a_device_id")
    String aphysicDeviceId;
    @JsonProperty("a_port_id")
    String aportId;
    @JsonProperty("z_device_id")
    String zphysicDeviceId;
    @JsonProperty("z_port_id")
    String zportId;
}
