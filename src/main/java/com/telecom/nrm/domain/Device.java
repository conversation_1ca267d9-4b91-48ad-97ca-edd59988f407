package com.telecom.nrm.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Device {
    @JsonProperty("id")
    String id;
    @JsonProperty("spec_id")
    String specId;
    @JsonProperty("code")
    String code;
    @JsonProperty("name")
    String name;
    @JsonProperty("port_id")
    String portID;
    @JsonProperty("port_name")
    String portName;
    @JsonProperty("port_code")
    String portCode;
    @JsonProperty("card_name")
    String cardName;
    @JsonProperty("card_code")
    String cardCode;
    @JsonProperty("slot_name")
    String slotName;
    @JsonProperty("slot_code")
    String slotCode;
    @JsonProperty("facility_id")
    String facilityID;
    @JsonProperty("device_ip")
    String deviceIP;
}
