package com.telecom.nrm.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/22 15:31
 **/
@Data
public class CircuitPair2FiberPair {

    @JsonProperty("id")
    String id;
    @JsonProperty("circuit_a_id")
    String circuitAId;
    @JsonProperty("circuit_a_code")
    String circuitACode;
    @JsonProperty("fiber_a_id")
    String fiberAId;
    @JsonProperty("fiber_a_code")
    String fiberACode;

    @JsonProperty("circuit_b_id")
    String circuitBId;
    @JsonProperty("circuit_b_code")
    String circuitBCode;
    @JsonProperty("fiber_b_id")
    String fiberBId;
    @JsonProperty("fiber_b_code")
    String fiberBCode;
    @JsonProperty("is_pair_circuit")

    String isPairCircuit;
    @JsonProperty("result")
    String result;
}
