package com.telecom.nrm.domain;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Row {

    @JsonProperty("circuitId")
    private String circuitId;
    @JsonProperty("circuitSpecId")
    private String circuitSpecId;
    @JsonProperty("circuitType")
    private String circuitType;
    @JsonProperty("circuitName")
    private String circuitName;
    @JsonProperty("circuitCode")
    private String circuitCode;
    @JsonProperty("crmProductId")
    private String crmProductId;
    @JsonProperty("accessCode")
    private String accessCode;
    @JsonProperty("custName")
    private String custName;
    @JsonProperty("rate")
    private String rate;
    @JsonProperty("circuitRate")
    private Object circuitRate;
    @JsonProperty("usingState")
    private String usingState;
    @JsonProperty("aportId")
    private String aportId;
    @JsonProperty("zportId")
    private String zportId;
    @JsonProperty("aportCode")
    private String aportCode;
    @JsonProperty("zportCode")
    private String zportCode;
    @JsonProperty("zphysicDeviceSpec")
    private Object zphysicDeviceSpec;
    @JsonProperty("zphysicDeviceCode")
    private String zphysicDeviceCode;
    @JsonProperty("zphysicDeviceName")
    private String zphysicDeviceName;
    @JsonProperty("aportSpec")
    private Object aportSpec;
    @JsonProperty("zportSpec")
    private Object zportSpec;
    @JsonProperty("aphysicDeviceId")
    private String aphysicDeviceId;
    @JsonProperty("aphysicDeviceSpec")
    private String aphysicDeviceSpec;
    @JsonProperty("aphysicDeviceCode")
    private String aphysicDeviceCode;
    @JsonProperty("aphysicDeviceName")
    private String aphysicDeviceName;
    @JsonProperty("zphysicDeviceId")
    private String zphysicDeviceId;

    public Row() {
    }

}
