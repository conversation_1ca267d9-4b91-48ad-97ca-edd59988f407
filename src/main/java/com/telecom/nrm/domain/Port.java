package com.telecom.nrm.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Port {
    @JsonProperty("id")
    String id;
    @JsonProperty("spec_id")
    String specId;
    @JsonProperty("code")
    String code;
    @JsonProperty("name")
    String name;
    @JsonProperty("standard_name")
    String standardName;
    @JsonProperty("standard_code")
    String standardCode;
    @JsonProperty("nm_name")
    String nmName;
    @JsonProperty("nm_code")
    String nmCode;
    @JsonProperty("physic_device_id")
    String physicDeviceId;
}
