package com.telecom.nrm.domain;

public class DeviceBO {

    private Long id;
    private Long specId;
    private String code;
    private String name;
    private Long regionId;
    private Long tmlId;
    private String regionName;
    private Long facilityId;
    private String facilityName;
    private String nmIp;
    private String nmName;
    private String modelName;
    private String modifierName;
    private String addressName;
    private String lifeStateName;

    // Getters 和 Setters 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getTmlId() {
        return tmlId;
    }

    public void setTmlId(Long tmlId) {
        this.tmlId = tmlId;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
    }

    public String getFacilityName() {
        return facilityName;
    }

    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }

    public String getNmIp() {
        return nmIp;
    }

    public void setNmIp(String nmIp) {
        this.nmIp = nmIp;
    }

    public String getNmName() {
        return nmName;
    }

    public void setNmName(String nmName) {
        this.nmName = nmName;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModifierName() {
        return modifierName;
    }

    public void setModifierName(String modifierName) {
        this.modifierName = modifierName;
    }

    public String getAddressName() {
        return addressName;
    }

    public void setAddressName(String addressName) {
        this.addressName = addressName;
    }

    public String getLifeStateName() {
        return lifeStateName;
    }

    public void setLifeStateName(String lifeStateName) {
        this.lifeStateName = lifeStateName;
    }
}