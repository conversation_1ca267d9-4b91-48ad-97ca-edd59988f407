package com.telecom.nrm.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PortAbnormalRateDto {
    @JsonProperty("id")
    private BigDecimal id;

    @JsonProperty("shijian")
    private String shijian;

    @JsonProperty("areaName")
    private String areaName;

    @JsonProperty("stationName")
    private String stationName;

    @JsonProperty("roomName")
    private String roomName;

    @JsonProperty("devCode")
    private String devCode;

    @JsonProperty("devName")
    private String devName;

    @JsonProperty("devAddr")
    private String devAddr;

    @JsonProperty("portName")
    private String portName;

    @JsonProperty("portCode")
    private String portCode;

    @JsonProperty("usingstateid")
    private String usingstateid;

    @JsonProperty("businessCode")
    private String businessCode;

    @JsonProperty("cusName")
    private String cusName;

    @JsonProperty("crmCustOrderCode")
    private String crmCustOrderCode;

    @JsonProperty("psoTypeId")
    private String psoTypeId;

    @JsonProperty("speed")
    private String speed;

    @JsonProperty("confRate")
    private String confRate;

    @JsonProperty("gatherOutCir")
    private String gatherOutCir;

    @JsonProperty("comparetheresults")
    private String comparetheresults;

    @JsonProperty("actualConfRate")
    private String actualConfRate;

    @JsonProperty("orderType")
    private String orderType;

    @JsonProperty("numericalComparison")
    private String numericalComparison;

    @JsonProperty("createDate")
    private String createDate;

    @JsonProperty("inSpeed")
    private String inSpeed;

    @JsonProperty("outSpeed")
    private String outSpeed;

    @JsonProperty("portId")
    private String portId;
}
