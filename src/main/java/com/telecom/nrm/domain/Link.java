package com.telecom.nrm.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Link {
    @JsonProperty("id")
    String id;
    @JsonProperty("spec_id")
    String specId;
    @JsonProperty("code")
    String code;
    @JsonProperty("name")
    String name;
    @JsonProperty("a_physic_device_id")
    String aphysicDeviceId;
    @JsonProperty("a_device_facility_id")
    String adeviceFacilityID;
    @JsonProperty("a_port_id")
    String aportId;
    @JsonProperty("z_physic_device_id")
    String zphysicDeviceId;
    @JsonProperty("z_device_facility_id")
    String zdeviceFacilityID;
    @JsonProperty("z_port_id")
    String zportId;
}
