package com.telecom.nrm.domain;

import lombok.Data;

@Data
public class Response <T>{
    Result result;
    T data;

    public static <T> Response ok(T data) {
        Response <T> response=new Response<>();
        Result result = new Result();
        result.setStatus("0");
        response.setResult(result);
        response.setData(data);
        return response;
    }

    public static Response error(String errorCode,String errorMessage) {
        Response response=new Response<>();
        Result result = new Result();
        result.setStatus("1");
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        response.setResult(result);
        return  response;
    }
}
