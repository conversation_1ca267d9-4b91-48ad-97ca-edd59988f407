package com.telecom.nrm.domain;


import lombok.Data;

import java.util.List;

@Data
public class ResponseData {

    public static final String SUCCESS = "0";     //成功
    public static final String ERROR_999 = "900"; //未查询到数据
    public static final String HALF_SUCCESS = "200"; //未查询到数据

    private int pageSize;
    private int pageNum;
    private Object notes;
    private String code = ResponseData.SUCCESS;
    private String message;
    private Object data;
    private int pageCount;


/*    public static class Data {
        private List<Row> row;

        // Getter and Setter
        public List<Row> getRow() {
            return row;
        }

        public void setRow(List<Row> row) {
            this.row = row;
        }
    }*/


}
