package com.telecom.nrm.domain.topo;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.parameters.P;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Slf4j
public class TopoSorter {
    List<JSONObject> nodes = new ArrayList<>();
    List<JSONObject> links = new ArrayList<>();

    // 查询一个节点的入链路
    public List<JSONObject> getInLinks(JSONObject node) {
        if (ObjectUtil.isNotEmpty(node)) {

            return links.stream().filter(l->ObjectUtil.isNotEmpty(l.getString("target_id")) && l.getString("target_id").equals(node.getString("gid"))).collect(Collectors.toList());
        }else {
            return new ArrayList<>();
        }

    }

    // 查询一个节点的出链路
    public List<JSONObject> getOutLinks(JSONObject node) {
        if (ObjectUtil.isNotEmpty(node)) {
            return links.stream().filter(l->ObjectUtil.isNotEmpty(l.getString("source_id")) && l.getString("source_id").equals(node.getString("gid"))).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }

    }

    // 查询节点入链路的数量
    public int getInCount(JSONObject node) {
        Set<String> edges = new HashSet<>();
        List<JSONObject> links = getInLinks(node);
        for (JSONObject link : links) {
            edges.add(link.getString("source_id")+"_"+link.getString("target_id"));
        }
        return edges.size();
    }

    // 查询节点出链路的数量
    public int getOutCount(JSONObject node) {
        Set<String> edges = new HashSet<>();
        List<JSONObject> links = getOutLinks(node);
        for (JSONObject link : links) {
            edges.add(link.getString("source_id")+"_"+link.getString("target_id"));
        }
        return edges.size();
    }


    // 查询起点
    public List<JSONObject> getStartNodes(String lableName) {
        return nodes.stream().filter(n->getInCount(n)==0 && n.getString("label_name").equals(lableName)).collect(Collectors.toList());
    }



    public JSONObject getNext(JSONObject node) {

        List<JSONObject> nextNodes = new ArrayList<>();
        List<JSONObject> nextLinks = new ArrayList<>();
        {
            List<JSONObject> outLinks = getOutLinks(node);
            Set<String> outNodeIds = outLinks.stream().map(o -> o.getString("target_id")).collect(Collectors.toSet());

            /*
            List<JSONObject> inLinks = getInLinks(node);
            Set<String> inNodeIds = inLinks.stream().map(o -> o.getString("source_id")).collect(Collectors.toSet());
            for (JSONObject link : inLinks) {
                log.info("交换 link:{}", JSON.toJSONString(link));
                String sourceId = link.getString("source_id");
                String targetId = link.getString("target_id");
                link.put("target_id", sourceId);
                link.put("source_id", targetId);
            }

             */


            Set<String> nodeIds = new HashSet<>();
            nodeIds.addAll(outNodeIds);
            // nodeIds.addAll(inNodeIds);

            List<JSONObject> outNodes = nodes.stream().filter(n -> nodeIds.contains(n.getString("gid"))).collect(Collectors.toList());
            // log.info("outNodes: {}", JSON.toJSONString(outNodes));
            // log.info("过滤前 outNodes size: {}", outNodes.size());
            // outNodes = outNodes.stream().filter(n -> getInCount(n) <= 1).collect(Collectors.toList());
            // log.info("过滤后 outNodes size: {}", outNodes.size());
            nextNodes.addAll(outNodes);


            nextLinks.addAll(outLinks);
            // nextLinks.addAll(inLinks);
        }
        JSONObject nextTopo = new JSONObject();
        nextTopo.put("nodes", nextNodes);
        nextTopo.put("links", nextLinks);
        return nextTopo;

    }

    public List<JSONObject> getStartLinks(String rootLabelName) {
        List<JSONObject> startNodes = getStartNodes(rootLabelName);
        Set<String> startIds = startNodes.stream().map(n->n.getString("gid")).collect(Collectors.toSet());

        return links.stream().filter(l->startIds.contains(l.getString("source_id"))).collect(Collectors.toList());
    }

    // 删除起驶节点
    public void removeNodes(List<JSONObject> p) {
        Set<String> ids = p.stream().map(n->n.getString("gid")).collect(Collectors.toSet());
        nodes.removeIf(n->ids.contains(n.getString("gid")) );
    }

    public void removeLinks(List<JSONObject> p) {
        Set<String> ids = p.stream().map(n->n.getString("gid")).collect(Collectors.toSet());
        links.removeIf(l->ids.contains(l.getString("gid")));
    }



    public TopoSorter (JSONObject topo) {
        List<JSONObject> nodes = new ArrayList<>(topo.getJSONArray("nodes").toJavaList(JSONObject.class));
        List<JSONObject> links = new ArrayList<>(topo.getJSONArray("links").toJavaList(JSONObject.class));
        this.nodes = nodes;
        this.links = links;

    }

    // 对整体拓扑进行排序
    public void sort(String rootLabelName) {

        List<JSONObject> allNodes = new ArrayList<>(this.nodes);
        List<JSONObject> allLinks = new ArrayList<>(this.links);
        List<JSONObject> allCurrentNodes = getStartNodes(rootLabelName);
        for (JSONObject currentNode : allCurrentNodes) {
            this.nodes = new ArrayList<>(allNodes);
            this.links = new ArrayList<>(allLinks);
            List<JSONObject> currentNodes = new ArrayList<>();
            currentNodes.add(currentNode);
            int seq =0;
            while (true) {
                if (ObjectUtil.isEmpty(currentNodes)) {
                    break;
                }
                List<JSONObject> allNextNodes = new ArrayList<>();
                for (JSONObject node : currentNodes) {
                    JSONObject next = getNext(node);
                    List<JSONObject> nextNodes = next.getJSONArray("nodes").toJavaList(JSONObject.class);
                    List<JSONObject> nextLinks = next.getJSONArray("links").toJavaList(JSONObject.class);
                    removeLinks(nextLinks);
                    allNextNodes.addAll(nextNodes);
                    if (ObjectUtil.isNotEmpty(node.getInteger("seq")) && node.getInteger("seq")<seq || ObjectUtil.isEmpty(node.getInteger("seq"))) {
                        node.put("seq", seq);
                    }

                    for (JSONObject link : nextLinks) {
                        if (ObjectUtil.isNotEmpty(link.getInteger("seq")) && link.getInteger("seq")<seq || ObjectUtil.isEmpty(link.getInteger("seq"))) {
                            link.put("seq", seq);
                        }
                    }
                }
                removeNodes(currentNodes);
                currentNodes = allNextNodes;
                seq++;




            }

        }



    }

}
