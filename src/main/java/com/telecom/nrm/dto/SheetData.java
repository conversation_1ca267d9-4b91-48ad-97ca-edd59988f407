package com.telecom.nrm.dto;

import java.util.List;
import java.util.Map;

/**
 * Excel工作表数据封装类
 * 用于多Sheet导出功能
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public class SheetData {
    
    /**
     * 工作表名称
     */
    private String sheetName;
    
    /**
     * 工作表数据
     */
    private List<Map<String, Object>> data;
    
    /**
     * 列定义，格式："显示名称:字段名"
     */
    private List<String> columns;
    
    public SheetData() {
    }
    
    public SheetData(String sheetName, List<Map<String, Object>> data, List<String> columns) {
        this.sheetName = sheetName;
        this.data = data;
        this.columns = columns;
    }
    
    public String getSheetName() {
        return sheetName;
    }
    
    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }
    
    public List<Map<String, Object>> getData() {
        return data;
    }
    
    public void setData(List<Map<String, Object>> data) {
        this.data = data;
    }
    
    public List<String> getColumns() {
        return columns;
    }
    
    public void setColumns(List<String> columns) {
        this.columns = columns;
    }
    
    @Override
    public String toString() {
        return "SheetData{" +
                "sheetName='" + sheetName + '\'' +
                ", dataSize=" + (data != null ? data.size() : 0) +
                ", columnsSize=" + (columns != null ? columns.size() : 0) +
                '}';
    }
}
