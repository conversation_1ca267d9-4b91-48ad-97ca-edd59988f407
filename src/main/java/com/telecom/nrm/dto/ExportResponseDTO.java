package com.telecom.nrm.dto;

import lombok.Data;

/**
 * 导出响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
public class ExportResponseDTO {

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 生成的文件名
     */
    private String fileName;

    /**
     * 文档安全平台跳转URL
     */
    private String redirectUrl;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 导出的数据行数
     */
    private Integer dataCount;

    /**
     * 构造成功响应
     */
    public static ExportResponseDTO success(String fileName, String redirectUrl, Long fileSize, Integer dataCount) {
        ExportResponseDTO response = new ExportResponseDTO();
        response.setSuccess(true);
        response.setMessage("文件导出并上传成功");
        response.setFileName(fileName);
        response.setRedirectUrl(redirectUrl);
        response.setFileSize(fileSize);
        response.setDataCount(dataCount);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static ExportResponseDTO failure(String message) {
        ExportResponseDTO response = new ExportResponseDTO();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
}
