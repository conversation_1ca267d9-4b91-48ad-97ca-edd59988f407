package com.telecom.nrm.dto;

import lombok.Data;

import java.util.Date;

/**
 * 导出日志记录DTO
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
public class ExportLogDTO {
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 操作人ID（统一工号）
     */
    private String operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    /**
     * 导出模块
     */
    private String exportModule;
    
    /**
     * 导出接口路径
     */
    private String exportInterface;
    
    /**
     * 导出类型
     */
    private String exportType;
    
    /**
     * 生成的文件名
     */
    private String fileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 导出数据行数
     */
    private Integer dataCount;
    
    /**
     * 工作表名称
     */
    private String sheetName;
    
    /**
     * 导出状态
     */
    private String exportStatus;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 导出参数（JSON格式）
     */
    private String exportParams;
    
    /**
     * SFTP上传状态
     */
    private String sftpUploadStatus;
    
    /**
     * SFTP文件路径
     */
    private String sftpPath;
    
    /**
     * 文档安全平台跳转URL
     */
    private String redirectUrl;
    
    /**
     * 开始时间
     */
    private Date startTime;
    
    /**
     * 结束时间
     */
    private Date endTime;
    
    /**
     * 耗时（毫秒）
     */
    private Long durationMs;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 地市编码
     */
    private String areaCode;
    
    /**
     * 创建成功日志的静态方法
     */
    public static ExportLogDTO createSuccessLog(String operatorId, String operatorName, 
                                              String exportModule, String exportInterface, 
                                              String exportType, String fileName, 
                                              Long fileSize, Integer dataCount, 
                                              String sheetName, String redirectUrl,
                                              Date startTime, Date endTime,
                                              String areaCode) {
        ExportLogDTO log = new ExportLogDTO();
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setExportModule(exportModule);
        log.setExportInterface(exportInterface);
        log.setExportType(exportType);
        log.setFileName(fileName);
        log.setFileSize(fileSize);
        log.setDataCount(dataCount);
        log.setSheetName(sheetName);
        log.setExportStatus("SUCCESS");
        log.setSftpUploadStatus("SUCCESS");
        log.setRedirectUrl(redirectUrl);
        log.setStartTime(startTime);
        log.setEndTime(endTime);
        log.setDurationMs(endTime.getTime() - startTime.getTime());
        log.setAreaCode(areaCode);
        return log;
    }
    
    /**
     * 创建失败日志的静态方法
     */
    public static ExportLogDTO createFailureLog(String operatorId, String operatorName,
                                              String exportModule, String exportInterface,
                                              String exportType, String errorMessage,
                                              Date startTime, Date endTime,
                                              String areaCode) {
        ExportLogDTO log = new ExportLogDTO();
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setExportModule(exportModule);
        log.setExportInterface(exportInterface);
        log.setExportType(exportType);
        log.setExportStatus("FAILED");
        log.setErrorMessage(errorMessage);
        log.setStartTime(startTime);
        log.setEndTime(endTime);
        if (startTime != null && endTime != null) {
            log.setDurationMs(endTime.getTime() - startTime.getTime());
        }
        log.setAreaCode(areaCode);
        return log;
    }
}
