package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface SplitterService {

    /**
     * 获取分光器设备统计信息
     * @param queryParam 查询参数
     * @return 统计结果
     */
    JSONObject getDeviceStatistics(JSONObject queryParam);

    /**
     * 数据转换：API数据转为前端格式
     * @param apiDataList API返回的原始数据
     * @return 转换后的设备数据列表
     */
    List<JSONObject> convertToDeviceDataList(List<JSONObject> apiDataList);

    /**
     * 计算设备健康状态
     * @param occupancyRate 实占率
     * @return 健康状态: normal/attention/alarm
     */
    String calculateHealthStatus(Integer occupancyRate);

    /**
     * 解析实占率字符串
     * @param rateStr 实占率字符串（可能包含%）
     * @return 实占率数值
     */
    Integer parseOccupancyRate(String rateStr);

    /**
     * 获取设备预测信息（预留接口）
     * @param deviceCode 设备编码
     * @return 预测信息
     */
    JSONObject getDevicePrediction(String deviceCode);

    /**
     * 批量获取设备预测（预留接口）
     * @param deviceCodes 设备编码列表
     * @return 批量预测结果
     */
    List<JSONObject> batchGetDevicePrediction(List<String> deviceCodes);
}