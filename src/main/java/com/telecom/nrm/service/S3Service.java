package com.telecom.nrm.service;

import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public interface S3Service {
    public List<Bucket> listBuckets ();



    public String readFile (String bucketName,String fileName) throws IOException;

    public InputStream readStream (String bucketName, String fileName);

    public void writeStream(String bucketName, String fileName, InputStream inputStream);

    public ObjectMetadata getObjectMetadata(String bucketName, String fileName);


    public ObjectListing listObjects(String bucketName, String prefix);

    public void deleteObject(String bucketName, String fileName);

}
