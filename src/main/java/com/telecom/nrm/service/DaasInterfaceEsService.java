package com.telecom.nrm.service;

import com.telecom.nrm.dto.ApiCodeCountDTO;
import com.telecom.nrm.dto.ApiCodeCountResponse;
import com.telecom.nrm.dto.AppApiCountDTO;
import com.telecom.nrm.dto.AppApiCountResponse;

import java.util.List;

public interface DaasInterfaceEsService {

    public ApiCodeCountResponse getApiCodeCallCounts(String startTime, String endTime, int page, int size);

    public AppApiCountResponse getAppApiCallCounts(String startTime, String endTime, int page, int size);


}
