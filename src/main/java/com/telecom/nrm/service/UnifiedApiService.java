package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.entity.TypeReference;

import java.lang.reflect.Type;

public interface UnifiedApiService {
    /**
     * 执行API调用（基础类型版本）
     * @param request 请求参数
     * @param responseType 响应类型Class对象
     * @return 类型化响应结果
     */
    <T> T executeApi(JSONObject request, Class<T> responseType);

    /**
     * 执行API调用（复杂泛型版本）
     * @param request 请求参数
     * @param typeRef 类型引用
     * @return 类型化响应结果
     */
    <T> T executeApi(JSONObject request, TypeReference<T> typeRef);
}