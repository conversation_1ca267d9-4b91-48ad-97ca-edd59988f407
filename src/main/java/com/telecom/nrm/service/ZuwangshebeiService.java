package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.RequestBody;

public interface ZuwangshebeiService{


    public JSONObject optPair_statistic_dictionary(JSONObject jsonObject);

    /**
     * 通过F号列表查询数据
     */
    public JSONObject pm_opt_road_group_query_zuwang_by_f_number(JSONObject jsonObject);

    /**
     * 将两个F号纳入光路保护组
     * @param jsonObject
     * @return
     */
    Integer pair_circuit(JSONObject jsonObject);

    /**
     * 通过F号查询双路由方案
     * @param jsonObject
     * @return
     */
    JSONObject queryDoubleRouteByFNumber(@RequestBody JSONObject jsonObject);



    public JSONObject risk_analyze(JSONObject request);

    /**
     * 通过两个设备id查询双路由方案
     * @param jsonObject
     * @return
     */
    JSONObject queryDoubleRouteByDeviceId(JSONObject jsonObject);

    /**
     * 通过两个光缆code查询双路由方案
     * @param jsonObject
     * @return
     */
    JSONObject queryDoubleRouteByCableCode(JSONObject jsonObject);
}