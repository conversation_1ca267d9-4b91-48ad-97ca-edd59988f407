package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;

import java.io.IOException;

public interface HunderRiverService {
    JSONObject zdNetFaultQuery(JSONObject jsonObject) throws IOException;

    JSONObject zdCustOrderQuery(JSONObject jsonObject) throws IOException;

    JSONObject queryAlarmByAccessNumber(JSONObject jsonObject) throws IOException;

    JSONObject   queryPon(JSONObject jsonObject);

    JSONObject   queryIpran(JSONObject jsonObject);

    JSONObject   queryNewCity(JSONObject jsonObject);

    JSONObject   queryFourLogic(JSONObject jsonObject);

    JSONObject   deviceAlarm(JSONObject jsonObject);

    JSONObject   zd(JSONObject jsonObject);

    /**
     * 调用百川的通用方法
     * @param jsonObject
     * @return
     */
    JSONObject common(JSONObject jsonObject);


    JSONObject ipm(JSONObject jsonObject);

    JSONObject ipran(JSONObject jsonObject);
}
