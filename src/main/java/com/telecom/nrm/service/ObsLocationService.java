package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface ObsLocationService {

    JSONObject queryObstaclepoints(List<String> accessCodes, String sharingCode,Boolean isGroup, String city);

    public JSONObject queryObstaclepointsV2(List<String> accessCodes, String sharingCode,Boolean isGroup);


    public JSONObject queryObstaclepointsbyalarm(JSONArray poninfo, String sharingCode);


}
