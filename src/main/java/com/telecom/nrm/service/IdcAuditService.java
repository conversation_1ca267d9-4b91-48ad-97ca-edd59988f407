package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.socket.WebSocketSession;

import java.io.File;
import java.util.List;
import java.util.Map;

public interface IdcAuditService {

    JSONObject qryIdcAuditStatistics(JSONObject params);

    void export(JSONObject jsonObject, JSONObject statisticCount, List<JSONObject> auditDetails, File retFile, String excelName) throws Exception;

    String weekExport(JSONObject jsonObject, JSONObject statisticCount, List<JSONObject> auditDetails, File retFile, String excelName) throws Exception;

    void exportDetailExcel(List<JSONObject> auditDetails, File retFile) throws Exception;

    void weekExportDetailExcel(List<JSONObject> auditDetails, File retFile) throws Exception;

    String dynamicAsk(String userContent,JSONObject paramResult,WebSocketSession session);

    void idcAnalysisMachineConfig(JSONObject params);

    void idcAnalysisMachinePortInfo(JSONObject params);

    Map<String, List<JSONObject>> qryIdcAnalysisMachinePortInfo(String method);

    void idcAnalysisMachineBookSize();

    void insertIdcPortGatherLock(String lockSign);

    JSONObject qryIdcPortGatherLock(String lockSign);

    void auditPmPortRateOverrun() throws Exception ;
}
