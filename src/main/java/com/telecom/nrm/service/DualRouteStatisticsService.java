package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;

/**
 * 双路由光路检测运营统计服务接口
 *
 * <AUTHOR> Generated
 * @date 2025-06-16
 */
public interface DualRouteStatisticsService {

    /**
     * 获取双路由月度运营统计数据
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (可选，格式: YYYY-MM-DD HH:mm:ss)
     *              - endDate: 结束时间 (可选，格式: YYYY-MM-DD HH:mm:ss)
     *              - shardingCode: 分库代码 (必需，用于确定查询哪个地市的分库)
     * @return 月度统计数据，包含：
     *         - summary: 汇总指标 (总整改数、总确认数、总变好数、总变差数、各项比率)
     *         - monthlyStats: 按月统计明细
     *         - statusDistribution: 状态分布统计
     *         - areaStats: 地区统计
     */
    JSONObject getMonthlyStatistics(JSONObject param);

    /**
     * 获取双路由历史趋势分析数据
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (可选)
     *              - endDate: 结束时间 (可选)
     *              - shardingCode: 分库代码 (必需，用于确定查询哪个地市的分库)
     * @return 趋势分析数据，包含：
     *         - summary: 趋势汇总指标
     *         - monthlyTrend: 月度检测频次和问题发现率趋势
     *         - statusChangeTrend: 状态变化事件趋势
     *         - problemDuration: 问题持续时间分析
     *         - riskObjects: 高风险对象识别
     */
    JSONObject getTrendAnalysis(JSONObject param);



    /**
     * 获取双路由变化统计
     *
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (可选，格式: YYYY-MM-DD HH:mm:ss)
     *              - endDate: 结束时间 (可选，格式: YYYY-MM-DD HH:mm:ss)
     *              - groupId: 光路组ID (可选，用于查询单个光路组)
     *              - objectType: 保护组类型 (可选，optGroup=光路保护组, CircuitPair=电路保护组)
     *              - historyLimit: 历史记录条数限制 (可选，默认2，可放宽到20查看更多历史)
     *              - pageSize: 每页大小 (可选，默认20)
     *              - currentPage: 当前页码 (可选，默认1)
     *              - shardingCode: 分库代码 (必需)
     * @return 变化统计数据，包含分页信息
     */
    JSONObject getChangeStatistics(JSONObject param);

    /**
     * 获取双路由风险对象列表
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (必需)
     *              - endDate: 结束时间 (必需)
     *              - riskLevel: 风险等级 (可选): 'high_risk', 'medium_risk', 'low_risk'
     *              - areaCode: 地区代码 (可选)
     *              - shardingCode: 分库代码 (必需)
     * @return 风险对象数据
     */
    JSONObject getRiskObjects(JSONObject param);

    /**
     * 获取双路由运营KPI汇总数据
     * 
     * @param param 查询参数，包含：
     *              - timeRange: 时间范围 ('1m', '3m', '6m', '1y')
     *              - areaCode: 地区代码 (可选)
     *              - shardingCode: 分库代码 (必需)
     * @return KPI汇总数据
     */
    JSONObject getKpiSummary(JSONObject param);

    /**
     * 获取双路由地区对比分析数据
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (必需)
     *              - endDate: 结束时间 (必需)
     *              - areaCodes: 地区代码列表 (可选，为空则查询所有地区)
     *              - shardingCode: 分库代码 (必需)
     * @return 地区对比分析数据
     */
    JSONObject getAreaComparison(JSONObject param);

    /**
     * 获取双路由问题类型分析数据
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (必需)
     *              - endDate: 结束时间 (必需)
     *              - areaCode: 地区代码 (可选)
     *              - problemTypes: 问题类型列表 (可选)
     *              - shardingCode: 分库代码 (必需)
     * @return 问题类型分析数据
     */
    JSONObject getProblemAnalysis(JSONObject param);

    /**
     * 获取双路由运营报表导出数据
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (必需)
     *              - endDate: 结束时间 (必需)
     *              - areaCode: 地区代码 (可选)
     *              - reportType: 报表类型 ('monthly', 'trend', 'detail', 'summary')
     *              - shardingCode: 分库代码 (必需)
     * @return 报表导出数据
     */
    JSONObject getExportData(JSONObject param);

    /**
     * 验证和处理查询参数
     * 
     * @param param 原始参数
     * @return 处理后的参数
     */
    JSONObject validateAndProcessParams(JSONObject param);

    /**
     * 格式化时间参数
     * 
     * @param dateStr 时间字符串
     * @param isEndDate 是否为结束时间
     * @return 格式化后的时间字符串
     */
    String formatDateParam(String dateStr, boolean isEndDate);

    /**
     * 计算业务指标比率
     * 
     * @param numerator 分子
     * @param denominator 分母
     * @return 比率（百分比，保留2位小数）
     */
    Double calculateRate(Integer numerator, Integer denominator);

    /**
     * 生成运营统计报告
     * 
     * @param param 查询参数
     * @return 运营统计报告数据
     */
    JSONObject generateOperationalReport(JSONObject param);

    /**
     * 获取状态变化类型的中文描述
     * 
     * @param metricType 指标类型
     * @return 中文描述
     */
    String getMetricTypeDescription(String metricType);

    /**
     * 获取问题严重程度的中文描述
     * 
     * @param severity 严重程度
     * @return 中文描述
     */
    String getSeverityDescription(String severity);
}
