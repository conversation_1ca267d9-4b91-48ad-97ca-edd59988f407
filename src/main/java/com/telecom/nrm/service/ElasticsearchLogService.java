package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
public class ElasticsearchLogService {

    private static final String INDEX_PREFIX = "oss-sw-yf-";
    private static final ThreadLocal<SimpleDateFormat> dateFormat =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));
    private static final ThreadLocal<SimpleDateFormat> timestampFormat =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ"));

    @Autowired
    private RestHighLevelClient esClient;

    public void saveInterfaceLog(JSONObject logData) {
        String indexName = INDEX_PREFIX + dateFormat.get().format(new Date());
        
        logData.put("@timestamp", timestampFormat.get().format(new Date()));
        IndexRequest request = new IndexRequest(indexName, "_doc")
                .source(logData.toJSONString(), XContentType.JSON);
        
        try {
            esClient.index(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException("Failed to save log to Elasticsearch", e);
        }
    }
}