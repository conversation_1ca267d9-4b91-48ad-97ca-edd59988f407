package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;

/**
 * 隐患整改服务接口
 */
public interface HazardRemediationService {

    /**
     * 创建隐患整改单
     * @param param 隐患整改单信息
     * @param shardingCode 分片代码
     * @return 创建结果
     */
    JSONObject createHazardRemediation(JSONObject param, String shardingCode);

    /**
     * 查询隐患整改单列表（分页）
     * @param param 查询条件
     * @param pageSize 页大小
     * @param currentPage 当前页
     * @param shardingCode 分片代码
     * @return 分页查询结果
     */
    PageResponse<JSONObject> queryHazardRemediationList(JSONObject param, Integer pageSize, Integer currentPage, String shardingCode);

    /**
     * 查询隐患整改单详情
     * @param id 隐患整改单ID
     * @param shardingCode 分片代码
     * @return 隐患整改单详情
     */
    JSONObject getHazardRemediationDetail(String id, String shardingCode);

    /**
     * 更新隐患整改单
     * @param param 更新参数
     * @param shardingCode 分片代码
     * @return 更新结果
     */
    JSONObject updateHazardRemediation(JSONObject param, String shardingCode);

    /**
     * 删除隐患整改单
     * @param id 隐患整改单ID
     * @param shardingCode 分片代码
     * @return 删除结果
     */
    JSONObject deleteHazardRemediation(String id, String shardingCode);

    /**
     * 保存隐患整改光路信息
     * @param remediationId 隐患整改单ID
     * @param opticalPaths 光路列表
     * @param shardingCode 分片代码
     * @return 保存结果
     */
    JSONObject saveOpticalPaths(String remediationId, JSONObject opticalPaths, String shardingCode);

    /**
     * 查询隐患整改光路列表
     * @param remediationId 隐患整改单ID
     * @param shardingCode 分片代码
     * @return 光路列表
     */
    JSONObject queryOpticalPaths(String remediationId, String shardingCode);

    /**
     * 保存附件记录
     * @param param 附件信息
     * @param shardingCode 分片代码
     * @return 保存结果
     */
    JSONObject saveAttachment(JSONObject param, String shardingCode);

    /**
     * 查询附件列表
     * @param remediationId 隐患整改单ID
     * @param shardingCode 分片代码
     * @return 附件列表
     */
    JSONObject queryAttachments(String remediationId, String shardingCode);

    /**
     * 删除附件记录
     * @param attachmentId 附件ID
     * @param shardingCode 分片代码
     * @return 删除结果
     */
    JSONObject deleteAttachment(String attachmentId, String shardingCode);

    /**
     * 获取统计信息
     * @param param 统计参数
     * @param shardingCode 分片代码
     * @return 统计结果
     */
    JSONObject getStatistics(JSONObject param, String shardingCode);

    // ==================== 风险检测相关方法 ====================

    /**
     * 执行风险检测
     * @param param 检测参数
     * @param shardingCode 分片代码
     * @return 检测结果
     */
    JSONObject performRiskDetection(JSONObject param, String shardingCode);

    /**
     * 保存风险检测记录
     * @param param 检测记录参数
     * @param shardingCode 分片代码
     * @return 保存结果
     */
    JSONObject saveRiskDetection(JSONObject param, String shardingCode);

    /**
     * 查询风险检测记录
     * @param remediationId 整改单ID
     * @param shardingCode 分片代码
     * @return 检测记录列表
     */
    JSONObject queryRiskDetections(String remediationId, String shardingCode);

    /**
     * 执行风险对比分析
     * @param param 对比参数
     * @param shardingCode 分片代码
     * @return 对比结果
     */
    JSONObject performRiskComparison(JSONObject param, String shardingCode);



    /**
     * 查询检测时间线
     * @param remediationId 整改单ID
     * @param shardingCode 分片代码
     * @return 时间线记录列表
     */
    JSONObject queryDetectionTimeline(String remediationId, String shardingCode);

    /**
     * 获取隐患整改检测时间线（POST方式）
     * @param param 查询参数（包含remediationId和shardingCode）
     * @return 时间线记录列表
     */
    JSONObject getDetectionTimeline(JSONObject param);
}
