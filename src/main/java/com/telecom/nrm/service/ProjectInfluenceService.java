package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface ProjectInfluenceService {
    public List<JSONObject> influenceRoute(@RequestBody JSONObject request, String batchNo);

    public List<JSONObject> resourceInfluenceRoute (JSONObject res, String city);

    public void influenceRouteCarryCFS (List<String> routeCodes, String city);

    public void influenceRouteCarryCFS (JSONObject request);

    public JSONObject influenceBusiness(@RequestBody JSONObject request, String batchNo);

    public List<JSONObject> influenceOptGroup(@RequestBody JSONObject request, String batchNo);


}
