package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface
OptRoadPairService {
    public List<JSONObject> findSameList(List<JSONObject> list1, List<JSONObject> list2);

    public JSONObject buildOptRoadPair(JSONObject optRoad1,JSONObject optRoad2);

    public List<JSONObject> getSamePipeSegments_d(List<JSONObject> optRoadPairList);

    public List<JSONObject> getSameCables_d(List<JSONObject> optRoadPairList);

    public JSONObject opt_pair_analyse  (List<JSONObject> optGroupElements, String ds);
    public void opt_pair_route_no_analyse(List<JSONObject> optGroupElements, String ds);
    public String result2format(JSONObject result);

    public void Result_dealt(JSONObject result);

    /**
     * 根据F号查下光缆数据
     */
    public JSONObject queryOptRoadByFNumber(String fCode, String ds);


}
