package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.Graph;

import java.util.List;

public interface MstpService {

    public JSONObject mstp(JSONObject request);

    public JSONObject queryMSTPToPipeSegment(JSONObject request);

    public List<JSONObject> getFirstLastDevice(Graph graph, List<String> codes);

    // public JSONObject queryMSTPCircuitPairToPipeSegment(JSONObject request);

    void deviceAddAlarmInfo(List<JSONObject> devices);

    public JSONObject queryMSTPCircuit(JSONObject request);

    JSONObject unionCityRoute(J<PERSON>NObject local, JSONObject request);
}
