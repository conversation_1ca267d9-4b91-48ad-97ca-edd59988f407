package com.telecom.nrm.service;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.math.BigDecimal;
import java.util.List;

public interface OltService {

    List<JSONObject> selectAllOlt();

    List<JSONObject> selectAllOltBySharding(String shardingCode);

    void createOptGroupAndElement(String code, String name, String shardingCode, BigDecimal oltId);

    JSONObject querOltInfoById(BigDecimal oltId, String shardingCode);

    void addAlarmInfo(List<JSONObject> devices,String region);
}
