package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.dao.IPRanJsDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.IPRanJsService;
import com.telecom.nrm.thread.audit.Otn;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class IPRanJsServiceImpl implements IPRanJsService {
    private static final String IPRAN_URL1 = "http://**************:7070/CloudLineRouteQuery";
    private static final String IPRAN_URL2 = "http://**************:7070/queryConfigDevice";

    @Autowired
    private IPRanJsDao iPRanJsDao;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private CircuitAnalysisDao circuitAnalysisDao;

    @Autowired
    @Lazy
    private Otn otn;

    private static final String OBD = "1020200006";

    private static final String ONU = "1028400002";

    private static final String DOWN = "100366";

    private static final String OBD_O2 = "2530";

    private static final String ONU_O2 = "420";

    private static final String DOWN_O2 = "80000408";


    @Override
    public JSONObject queryiPRanJs(JSONObject param, int i, int i1) {
        JSONObject response = new JSONObject();
        int mm = 0;
        try {
            JSONObject data = new JSONObject();


            JSONObject json = JSONObject.parseObject(param.toJSONString());
            JSONObject businessParam = json.getJSONObject("businessParam");
            JSONArray whereBody = json.getJSONArray("whereBody");
            JSONObject whereBodyChid = whereBody.getJSONObject(0);
            String cjType = businessParam.get("cjType").toString();
            String custmor = businessParam.get("custmor").toString();
            String requestId = businessParam.get("requestId").toString();
            String sendTime = businessParam.get("sendTime").toString();
            String apiId = businessParam.get("apiId").toString();
            String nodeLabel = whereBodyChid.get("nodeLabel").toString();
            String propertyLabel = whereBodyChid.get("propertyLabel").toString();
            String propertyValue = whereBodyChid.get("propertyValue").toString();

            String area_code = "";
            String propertyId = "";
            String accessCode = "";
            String serviceLevelId = "";
            JSONObject whereBodyAreacode = whereBody.getJSONObject(0);
            int k = 1;
            while(whereBody.size()>=k) {
                whereBodyAreacode = whereBody.getJSONObject(k - 1);
                mm = 8;
                String propertyLabelAreacode = whereBodyAreacode.get("propertyLabel").toString();
                if (propertyLabelAreacode.equals("area_code")) {
                    area_code = whereBodyAreacode.get("propertyValue").toString();

                }
                if (propertyLabelAreacode.equals("gid")) {
                    propertyId = whereBodyAreacode.get("propertyValue").toString();

                }
                if (propertyLabelAreacode.equals("code")) {
                    propertyValue = whereBodyAreacode.get("propertyValue").toString();
                    propertyLabel = whereBodyAreacode.get("propertyLabel").toString();

                }
                if (propertyLabelAreacode.equals("access_code") &&  whereBodyAreacode.get("propertyValue") !=null) {
                    accessCode = whereBodyAreacode.get("propertyValue").toString();
                }
                if (propertyLabelAreacode.equals("service_level_id") && whereBodyAreacode.get("propertyValue") !=null) {
                    serviceLevelId = whereBodyAreacode.get("propertyValue").toString();
                }

                k++;
            }

            if (StringUtils.isNotEmpty(accessCode) && "102657".equals(serviceLevelId)) {
                // 有接入号没电路编码，需要通过接入号翻译出电路编码
                JSONObject cjTypeParam = new JSONObject();
                cjTypeParam.put("accessCode",accessCode );
                cjTypeParam.put("cityCode",area_code );
                PageResponse<JSONObject> cjTypes = iPRanJsDao.queryTopoRelay(cjTypeParam, 10, 1, "ds_graph_js");
                List<JSONObject> cjTypeData = cjTypes.getData();
                if (CollectionUtils.isNotEmpty(cjTypeData)) {
                    propertyValue = cjTypeData.get(0).getString("circuitCode");
                }
            }




            /**20241012互联网专线集团入参为接入号access_code接口适配化处理**/
                int l = 1;
                while(whereBody.size()>=l){
                    whereBodyAreacode = whereBody.getJSONObject(l-1);
                    mm = 8;
                    String propertyLabelAreacodezx = whereBodyAreacode.get("propertyLabel").toString();

                    if(cjType.equals("PON接入老城域网") || cjType.equals("LAN接入城域网") || cjType.equals("PON接入新城域网") || cjType.equals("光纤接入城域网")){
                        if(propertyLabelAreacodezx.equals("access_code")){
                            propertyValue = whereBodyAreacode.get("propertyValue").toString();
                            propertyLabel = whereBodyAreacode.get("propertyLabel").toString();

                        }
                    }
                    l++;
                mm = 10;
            }

            Date date=new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化时间
            String reqTime=formatter.format(date);

            response.put("backTime",reqTime);
            response.put("cjType",cjType);
            response.put("data","");
            response.put("nextPage",true);

            if (StringUtils.isBlank(cjType)) {
                response.put("code","900");
                response.put("message","场景类型不能为空");
                return response;
            }

            if (StringUtils.isBlank(requestId)) {
                response.put("code","900");
                response.put("message","请求id不能为空");
                return response;
            }

            if (StringUtils.isBlank(apiId)) {
                response.put("code","900");
                response.put("message","接口的key不能为空");
                return response;
            }

            mm = 1;



            //int pageSize = json.getInteger("pageSize");
            //int pageNum = json.getInteger("pageNum");
            int pageSize = 1000;
            int pageNum = 0;
            mm = 4;

            response.put("pageSize",pageSize);
            response.put("pageNum",pageNum);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("nodeLabel",nodeLabel);
            jsonObject.put("propertyLabel",propertyLabel);
            jsonObject.put("propertyValue",propertyValue);
            jsonObject.put("areaCode",area_code);
            jsonObject.put("propertyId",propertyId);
            mm = 5;



            //数通设备物理位置关系接口
            if (cjType.equals("数通设备物理位置关系")) {
                PageResponse<JSONObject> result5GCnodes = null;
                PageResponse<JSONObject> result5GCrelations = null;
                try {
                    result5GCnodes = iPRanJsDao.get5GCnodes(jsonObject, pageSize, pageNum, "ds_graph_js");
                    mm = 61;
                    result5GCrelations = iPRanJsDao.get5GCrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                    mm = 62;
                    List<JSONObject> data5GCnodes = result5GCnodes.getData();
                    List<JSONObject> data5GCrelations = result5GCrelations.getData();

                    int ii = 0;
                    if(data5GCnodes.size()>0) {
                        for (JSONObject nodes : data5GCnodes) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            data5GCnodes.set(ii, nodes);
                            ii++;
                        }
                    }
                    mm = 63;

                    ii = 0;
                    if(data5GCrelations.size()>0) {
                        for (JSONObject nodes : data5GCrelations) {
                            String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels", nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels", znodelable);
                            nodes.remove("z_nodelabels");
                            data5GCrelations.set(ii, nodes);
                            ii++;
                        }
                    }
                    mm = 64;

                    data.put("nodes",data5GCnodes);
                    data.put("relations",data5GCrelations);



                    response.put("data",data);
                    response.put("code",0);
                    response.put("message","5GC设备："+propertyLabel);
                } catch (Exception e) {
                    e.printStackTrace();
                    response.put("message",e.getMessage()+mm);
                }

                return response;
            }else if ((cjType.equals("物理连接图省段局向光纤"))){
                PageResponse<JSONObject> resultcablenodes = null;
                PageResponse<JSONObject> resultcablerelations = null;
                PageResponse<JSONObject> resultcablelinks = null;
                try {
                    resultcablenodes = iPRanJsDao.getgraphnodescable(jsonObject, pageSize, pageNum, "ds_graph_js");
                    mm = 61;
                    resultcablerelations = iPRanJsDao.getgraphrelationscable(jsonObject, pageSize, pageNum, "ds_graph_js");
                    mm = 62;
                    resultcablelinks = iPRanJsDao.getgraphlinkscable(jsonObject, pageSize, pageNum, "ds_graph_js");
                    mm = 660;
                    List<JSONObject> datacableCnodes = resultcablenodes.getData();
                    List<JSONObject> datacablerelations = resultcablerelations.getData();
                    List<JSONObject> datacablelinks = resultcablelinks.getData();

                    int ii = 0;
                    if(datacableCnodes.size()>0) {
                        for (JSONObject nodes : datacableCnodes) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datacableCnodes.set(ii, nodes);
                            ii++;
                        }
                    }
                    mm = 63;

                    ii = 0;
                    if(datacablerelations.size()>0) {
                        for (JSONObject nodes : datacablerelations) {
                            String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels", nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels", znodelable);
                            nodes.remove("z_nodelabels");
                            datacablerelations.set(ii, nodes);
                            ii++;
                        }
                    }
                    mm = 64;

                    ii = 0;
                    if(datacablelinks.size()>0) {
                        for (JSONObject nodes : datacablelinks) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.put("linkType", nodes.get("linktype"));
                            nodes.remove("nodelabels");
                            nodes.remove("linktype");
                            datacablelinks.set(ii, nodes);
                            ii++;
                        }
                    }

                    data.put("nodes",datacableCnodes);
                    data.put("relations",datacablerelations);
                    data.put("links",datacablelinks);



                    response.put("data",data);
                    response.put("code",0);
                    response.put("message","局向光纤："+propertyId);
                } catch (Exception e) {
                    e.printStackTrace();
                    response.put("message",e.getMessage()+mm);
                }

                return response;
            }


            //如果链路中查不到电路编号，就不走接下来的逻辑
            PageResponse<JSONObject> resultCodeExists = iPRanJsDao.getgraphcodeexists(jsonObject, pageSize, pageNum, "ds_graph_js");
            List<JSONObject> dataCodeExistslist = resultCodeExists.getData();
            if (param.toJSONString().indexOf("_TEST") >= 0) {
                dataCodeExistslist = new ArrayList<>();
                // 移除变成真实的电路或者接入号
                jsonObject = JSONObject.parseObject(jsonObject.toJSONString().replace("_TEST", ""));
            }

            if(dataCodeExistslist.size() == 0){
                try {
                    // 添加补偿查询
                    PageResponse<JSONObject> results = iPRanJsDao.queryProductLinkAnalysisData(jsonObject, pageSize, pageNum, "ds_graph_js");
                    if (results.getData().size() > 0) {
                        return results.getData().get(0).getJSONObject("data");
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                }
            }


            if(dataCodeExistslist.size() == 0){
                response.put("code",200);
                response.put("message","连接图不存在");
                return response;
            }

            //预存接口结果数据，如果有，则直接返回集团
            PageResponse<JSONObject> resultpre = iPRanJsDao.getgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
            List<JSONObject> dataprelist = resultpre.getData();
            if(dataprelist.size() > 0){
                JSONObject datapre = dataprelist.get(0);
                if(!datapre.isEmpty()){
                    JSONObject jsonpre = datapre.getJSONObject("results");
                    return jsonpre;
                }
            }

            //一树一图客户资源树nodes的信息
            PageResponse<JSONObject> resultnodes = null;
            PageResponse<JSONObject> resultnodesport = null;
            PageResponse<JSONObject> resultnodesdevice = null;
            PageResponse<JSONObject> resultnodesfacility = null;
            PageResponse<JSONObject> resultnodesarea = null;
            PageResponse<JSONObject> resultnodesservice = null;
            PageResponse<JSONObject> resultnodesware = null;

            //根据cjType切换不同接口查询
            //if("云专网连接图PON接入段".equals(cjType)){
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                mm = 51;
                resultnodes = iPRanJsDao.getgraphnodes(jsonObject, pageSize, pageNum, "ds_graph_js");
                mm = 52;
                resultnodesport = iPRanJsDao.getgraphnodesport(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesdevice = iPRanJsDao.getgraphnodesdevice(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesfacility = iPRanJsDao.getgraphnodesfacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesarea = iPRanJsDao.getgraphnodesarea(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesservice = iPRanJsDao.getgraphnodesservice(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesware = iPRanJsDao.getgraphnodesware(jsonObject, pageSize, pageNum, "ds_graph_js");
            }
            List<JSONObject> datanodes = resultnodes.getData();
            List<JSONObject> datanodesport = resultnodesport.getData();
            List<JSONObject> datanodesdevice = resultnodesdevice.getData();
            List<JSONObject> datanodesfacility = resultnodesfacility.getData();
            List<JSONObject> datanodesarea = resultnodesarea.getData();
            List<JSONObject> datanodesservice = resultnodesservice.getData();
            List<JSONObject> datanodesware = resultnodesware.getData();

            mm = 53;
            int ii = 0;
            if(datanodes.size()>0) {
                for (JSONObject nodes : datanodes) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.set(ii, nodes);
                    ii++;
                }
            }

            mm = 54;
            if(datanodesport.size()>0) {
                for (JSONObject nodes : datanodesport) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            mm = 55;
            if(datanodesfacility.size()>0) {
                for (JSONObject nodes : datanodesfacility) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }


            if(datanodesdevice.size()>0) {
                for (JSONObject nodes : datanodesdevice) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }


            if(datanodesarea.size()>0) {
                for (JSONObject nodes : datanodesarea) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }


            if(datanodesservice.size()>0) {
                for (JSONObject nodes : datanodesservice) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesware.size()>0) {
                for (JSONObject nodes : datanodesware) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            mm = 6;
            //一树一图客户资源树relations的信息
            PageResponse<JSONObject> resultrelations = null;
            PageResponse<JSONObject> resultlinkrelations = null;
            PageResponse<JSONObject> resultrelationsda = null;
            PageResponse<JSONObject> resultrelationsdf = null;
            PageResponse<JSONObject> resultrelationsdn = null;
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){
             resultrelations= iPRanJsDao.getgraphrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
             resultlinkrelations= iPRanJsDao.getgraphlinkrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
             resultrelationsda= iPRanJsDao.getgraphrelationsdevicearea(jsonObject, pageSize, pageNum, "ds_graph_js");
             resultrelationsdf= iPRanJsDao.getgraphrelationsdevicefacility(jsonObject, pageSize, pageNum, "ds_graph_js");
             resultrelationsdn= iPRanJsDao.getgraphrelationsdevicenumber(jsonObject, pageSize, pageNum, "ds_graph_js");
            }


            List<JSONObject> datarelations = resultrelations.getData();
            List<JSONObject> datalinkrelations = resultlinkrelations.getData();
            List<JSONObject> datalinkrelationsda = resultrelationsda.getData();
            List<JSONObject> datalinkrelationsdf = resultrelationsdf.getData();
            List<JSONObject> datalinkrelationsdn = resultrelationsdn.getData();

            ii = 0;
            if(datarelations.size()>0) {
                for (JSONObject nodes : datarelations) {
                    String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels", nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels", znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.set(ii, nodes);
                    ii++;
                }
            }

            if(datalinkrelations.size()>0){
                for(JSONObject nodes:datalinkrelations){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }

            if(datalinkrelationsda.size()>0){
                for(JSONObject nodes:datalinkrelationsda){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }

            if(datalinkrelationsdf.size()>0){
                for(JSONObject nodes:datalinkrelationsdf){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }

            if(datalinkrelationsdn.size()>0){
                for(JSONObject nodes:datalinkrelationsdn){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }


            //一树一图客户资源树links的信息
            PageResponse<JSONObject> resultlinks = null;
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){
             resultlinks= iPRanJsDao.getgraphlinks(jsonObject, pageSize, pageNum, "ds_graph_js");
            }


            List<JSONObject> datalinks = resultlinks.getData();

            ii = 0;
            if(datalinks.size()>0) {
                for (JSONObject nodes : datalinks) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.put("linkType", nodes.get("linktype"));
                    nodes.remove("nodelabels");
                    nodes.remove("linktype");
                    datalinks.set(ii, nodes);
                    ii++;
                }
            }

            data.put("nodes",datanodes);
            data.put("relations",datarelations);
            data.put("links",datalinks);

            mm = 7;

            response.put("data",data);
            response.put("code",0);
            response.put("message","证明我加了"+propertyLabel);

        } catch (Exception e) {
            e.printStackTrace();
            response.put("message",e.getMessage()+mm);
        }

        return response;
    }


    @Override
    public JSONObject test(int i, int i1, String cjType) {
        JSONObject response = new JSONObject();
        com.alibaba.fastjson.JSONObject data = new com.alibaba.fastjson.JSONObject();


//        JSONObject json = JSONObject.parseObject(param.toJSONString());
//        JSONObject businessParam = json.getJSONObject("businessParam");
//        JSONObject whereBody = json.getJSONObject("whereBody");
//        String custmor = businessParam.get("custmor").toString();
//        String requestId = businessParam.get("requestId").toString();
//        String sendTime = businessParam.get("sendTime").toString();
//        String apiId = businessParam.get("apiId").toString();
//        String nodeLabel = whereBody.get("nodeLabel").toString();
          String propertyLabel = "CRM_PRODUCT_IDD";
//        String propertyValue = whereBody.get("propertyValue").toString();

        Date date=new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化时间
        String reqTime=formatter.format(date);

        response.put("backTime",reqTime);
        response.put("cjType",cjType);
        response.put("data","");
        response.put("nextPage",true);

        if (StringUtils.isBlank(cjType)) {
            response.put("code","900");
            response.put("message","场景类型不能为空");
            return response;
        }


        int pageSize = 10000;
        int pageNum = 0;

        response.put("pageSize",pageSize);
        response.put("pageNum",pageNum);

        JSONObject jsonObject = new JSONObject();

        PageResponse<JSONObject> resultcode = iPRanJsDao.getgraphcodeall(jsonObject, pageSize, pageNum, "ds_graph_js");
        List<JSONObject> datacode = resultcode.getData();

        try {
            if(datacode.size()>0) {
                for (JSONObject code : datacode) {

            pageSize = 700;
            response.put("pageSize",pageSize);
            jsonObject.put("nodeLabel","asfdas");
            jsonObject.put("propertyLabel","asfdas");
            jsonObject.put("propertyValue",code.get("code"));
            jsonObject.put("areaCode",code.get("citySuburbId").toString());

                   // jsonObject.put("propertyValue","南京CTVPN189K072");
                  //  jsonObject.put("areaCode","8320100");

            PageResponse<JSONObject> resultpre = iPRanJsDao.getgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
            List<JSONObject> dataprelist = resultpre.getData();
            if(dataprelist.size() > 0){
                JSONObject datapre = dataprelist.get(0);
                if(!datapre.isEmpty()){
                    continue;
                }
            }


            PageResponse<JSONObject> resultnodes = null;
            PageResponse<JSONObject> resultnodesport = null;
            PageResponse<JSONObject> resultnodesdevice = null;
            PageResponse<JSONObject> resultnodesfacility = null;
            PageResponse<JSONObject> resultnodesarea = null;
            PageResponse<JSONObject> resultnodesservice = null;
            PageResponse<JSONObject> resultnodesware = null;

            //根据cjType切换不同接口查询
            //if("云专网连接图PON接入段".equals(cjType)){
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){

                resultnodes = iPRanJsDao.getgraphnodes(jsonObject, pageSize, pageNum, "ds_graph_js");

                resultnodesport = iPRanJsDao.getgraphnodesport(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesdevice = iPRanJsDao.getgraphnodesdevice(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesfacility = iPRanJsDao.getgraphnodesfacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesarea = iPRanJsDao.getgraphnodesarea(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesservice = iPRanJsDao.getgraphnodesservice(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesware = iPRanJsDao.getgraphnodesware(jsonObject, pageSize, pageNum, "ds_graph_js");
            }
            List<JSONObject> datanodes = resultnodes.getData();
            List<JSONObject> datanodesport = resultnodesport.getData();
            List<JSONObject> datanodesdevice = resultnodesdevice.getData();
            List<JSONObject> datanodesfacility = resultnodesfacility.getData();
            List<JSONObject> datanodesarea = resultnodesarea.getData();
            List<JSONObject> datanodesservice = resultnodesservice.getData();
            List<JSONObject> datanodesware = resultnodesware.getData();


            int ii = 0;
            for(JSONObject nodes:datanodes){
                String[] nodelable =  nodes.get("nodelabels").toString().split(",");
                nodes.put("nodeLabels",nodelable);
                nodes.remove("nodelabels");
                datanodes.set(ii,nodes);
                ii++;
            }


            if(datanodesport.size()>0) {
                for (JSONObject nodes : datanodesport) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesfacility.size()>0) {
                for (JSONObject nodes : datanodesfacility) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesdevice.size()>0) {
                for (JSONObject nodes : datanodesdevice) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesarea.size()>0) {
                for (JSONObject nodes : datanodesarea) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesservice.size()>0) {
                for (JSONObject nodes : datanodesservice) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesware.size()>0) {
                for (JSONObject nodes : datanodesware) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }


            PageResponse<JSONObject> resultrelations = null;
            PageResponse<JSONObject> resultlinkrelations = null;
            PageResponse<JSONObject> resultrelationsda = null;
            PageResponse<JSONObject> resultrelationsdf = null;
            PageResponse<JSONObject> resultrelationsdn = null;
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                resultrelations= iPRanJsDao.getgraphrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultlinkrelations= iPRanJsDao.getgraphlinkrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultrelationsda= iPRanJsDao.getgraphrelationsdevicearea(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultrelationsdf= iPRanJsDao.getgraphrelationsdevicefacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultrelationsdn= iPRanJsDao.getgraphrelationsdevicenumber(jsonObject, pageSize, pageNum, "ds_graph_js");
            }


            List<JSONObject> datarelations = resultrelations.getData();
            List<JSONObject> datalinkrelations = resultlinkrelations.getData();
            List<JSONObject> datalinkrelationsda = resultrelationsda.getData();
            List<JSONObject> datalinkrelationsdf = resultrelationsdf.getData();
            List<JSONObject> datalinkrelationsdn = resultrelationsdn.getData();

            ii = 0;
             if(datarelations.size()>0){
            for(JSONObject nodes:datarelations){
                String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                nodes.put("a_nodeLabels",nodelable);
                nodes.remove("a_nodelabels");
                String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                nodes.put("z_nodeLabels",znodelable);
                nodes.remove("z_nodelabels");
                datarelations.set(ii,nodes);
                ii++;
            }}

            if(datalinkrelations.size()>0){
                for(JSONObject nodes:datalinkrelations){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }

            if(datalinkrelationsda.size()>0){
                for(JSONObject nodes:datalinkrelationsda){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }

            if(datalinkrelationsdf.size()>0){
                for(JSONObject nodes:datalinkrelationsdf){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }

            if(datalinkrelationsdn.size()>0){
                for(JSONObject nodes:datalinkrelationsdn){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }

            PageResponse<JSONObject> resultlinks = null;
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                resultlinks= iPRanJsDao.getgraphlinks(jsonObject, pageSize, pageNum, "ds_graph_js");
            }


            List<JSONObject> datalinks = resultlinks.getData();

            ii = 0;
            for(JSONObject nodes:datalinks){
                String[] nodelable =  nodes.get("nodelabels").toString().split(",");
                nodes.put("nodeLabels",nodelable);
                nodes.put("linkType",nodes.get("linktype"));
                nodes.remove("nodelabels");
                nodes.remove("linktype");
                datalinks.set(ii,nodes);
                ii++;
            }

            data.put("nodes",datanodes);
            data.put("relations",datarelations);
            data.put("links",datalinks);


            response.put("data",data);
            response.put("code",0);
            response.put("message","证明我加了"+propertyLabel);
            if(datalinks.size()>0){
                jsonObject.put("results",response.toJSONString());
                try {
                    iPRanJsDao.recordgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;
    }

    @Override
    public JSONObject queryTopoRelay(JSONObject param, int i, int i1) {
        JSONObject response = new JSONObject();
        int mm = 0;
        try {
            JSONObject data = new JSONObject();


            JSONObject json = JSONObject.parseObject(param.toJSONString());
            if(!json.containsKey("provinceCode") || json.get("provinceCode") == null) {
                json.put("provinceCode", "8320000"); // 默认就是江苏
            }
            String provinceCode = json.get("provinceCode").toString();
            String cityCode = json.get("cityCode").toString();
            String circuitType =  null;
            if (json.containsKey("circuitType")) {
                circuitType = json.getString("circuitType");
            }
            String circuitCode  = null;
            if (json.containsKey("circuitCode")) {
                circuitCode = json.getString("circuitCode");
            }
            String serviceLevelId = null;
            if (json.containsKey("serviceLevelId")) {
                serviceLevelId = json.getString("serviceLevelId");
            }




            /**
             * 20241107 集团通知
             * 纯本地电路的调用接口变更为通过接入号调各省接口，
             * 同时增加业务级别，通过业务级别判断电路是跨域还是纯本地。涉及5.3 、5.4 、5.8、5.9、8接口的调整。请各省尽快完成接口升级。
             */
            String accessCode  = null;
            if (json.containsKey("accessCode")) {
                accessCode = json.getString("accessCode");
            }

            if (StringUtils.isNotEmpty(accessCode) && "102657".equals(serviceLevelId)) {
                // 纯本地 只使用接入号
                circuitCode = null;
            }


            Date date=new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化时间
            String reqTime=formatter.format(date);

            response.put("data","");


            mm = 1;

            mm = 2;

            mm = 3;


            int pageSize = json.getInteger("pageSize");
            int pageNum = json.getInteger("pageNum")-1;
            //int pageSize = 500;
            //int pageNum = 0;
            mm = 4;

            response.put("pageSize",pageSize);
            response.put("pageNum",pageNum);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("provinceCode",provinceCode);
            jsonObject.put("cityCode",cityCode);
            jsonObject.put("circuitType",circuitType);
            jsonObject.put("circuitCode",circuitCode);
            jsonObject.put("accessCode",accessCode);
            mm = 5;

            //根据cjType切换不同接口查询
            //if("云专网连接图PON接入段".equals(cjType)){
            PageResponse<JSONObject> resultnodes = iPRanJsDao.queryTopoRelay(jsonObject, pageSize, pageNum, "ds_graph_js");
            List<JSONObject> datanodes = resultnodes.getData();
            // 电路稽核时使用，防止假数据影响真稽核，有TEST是用于实际稽核请求
            if (param.toJSONString().indexOf("_TEST") >= 0) {
                datanodes = new ArrayList<>();
                // 移除变成真实的电路或者接入号
                jsonObject = JSONObject.parseObject(jsonObject.toJSONString().replace("_TEST", ""));
            }
            if (CollectionUtils.isEmpty(datanodes)) {
                // 补偿查询，这里都是真实成树数据。上面的可能是假的
                try {
                    resultnodes = iPRanJsDao.queryProductLinkAnalysisCjtype(jsonObject, pageSize, pageNum, "ds_graph_js");
                    datanodes  = resultnodes.getData();
                }catch (Exception e) {
                    e.printStackTrace();
                }

            }


            mm = 6;



            mm = 7;
            if(datanodes.size()>0){
                response.put("resultCode",0);
                response.put("pageSize",pageSize);
                response.put("pageNum",pageNum);
                response.put("data",datanodes);
                response.put("resultMsg","成功");
            }else{
                response.put("resultCode",1);
                response.put("pageSize",pageSize);
                response.put("pageNum",pageNum);
                response.put("data",datanodes);
                response.put("resultMsg","江苏未查询到相关电路");
            }
        } catch (Exception e) {
            e.printStackTrace();
            response.put("resultCode",1);
            response.put("message",e.getMessage()+mm);
            response.put("resultMsg","江苏业务电路查询接口内部异常");
        }
        return response;
    }



    @Override
    public JSONObject queryiPRanJsPre(JSONObject param, int i, int i1) {
        JSONObject response = new JSONObject();
        int mm = 0;
        try {
            JSONObject data = new JSONObject();


            JSONObject json = JSONObject.parseObject(param.toJSONString());
            JSONObject businessParam = json.getJSONObject("businessParam");
            JSONArray whereBody = json.getJSONArray("whereBody");
            JSONObject whereBodyChid = whereBody.getJSONObject(0);
            String cjType = businessParam.get("cjType").toString();
            String custmor = businessParam.get("custmor").toString();
            String requestId = businessParam.get("requestId").toString();
            String sendTime = businessParam.get("sendTime").toString();
            String apiId = businessParam.get("apiId").toString();
            String nodeLabel = whereBodyChid.get("nodeLabel").toString();
            String propertyLabel = whereBodyChid.get("propertyLabel").toString();
            String propertyValue = whereBodyChid.get("propertyValue").toString();

            String area_code = "";
            JSONObject whereBodyAreacode = whereBody.getJSONObject(0);
            int k = 1;
            while(whereBody.size()>k){
                whereBodyAreacode = whereBody.getJSONObject(k);
                mm = 8;
                String propertyLabelAreacode = whereBodyAreacode.get("propertyLabel").toString();
                if(propertyLabelAreacode.equals("area_code")){
                    area_code = whereBodyAreacode.get("propertyValue").toString();
                    mm = 9;
                }
                k++;
                mm = 10;
            }

            Date date=new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化时间
            String reqTime=formatter.format(date);

            response.put("backTime",reqTime);
            response.put("cjType",cjType);
            response.put("data","");
            response.put("nextPage",true);

            if (StringUtils.isBlank(cjType)) {
                response.put("code","900");
                response.put("message","场景类型不能为空");
                return response;
            }

            if (StringUtils.isBlank(requestId)) {
                response.put("code","900");
                response.put("message","请求id不能为空");
                return response;
            }

            if (StringUtils.isBlank(apiId)) {
                response.put("code","900");
                response.put("message","接口的key不能为空");
                return response;
            }

            mm = 1;

//            if (StringUtils.isBlank(json.getJSONObject("pageSize").toString())) {
//                response.put("code","900");
//                response.put("message","分页大小不能为空");
//                return response;
//            }

            mm = 2;

//            if (StringUtils.isBlank(json.getJSONObject("pageNum").toString())) {
//                response.put("code","900");
//                response.put("message","当前页码不能为空");
//                return response;
//            }

            mm = 3;


            //int pageSize = json.getInteger("pageSize");
            //int pageNum = json.getInteger("pageNum");
            int pageSize = 10000;
            int pageNum = 0;
            mm = 4;

            response.put("pageSize",pageSize);
            response.put("pageNum",pageNum);



            JSONObject jsonObject = new JSONObject();

            PageResponse<JSONObject> resultcode = iPRanJsDao.getgraphcodeall(jsonObject, pageSize, pageNum, "ds_graph_js");
            List<JSONObject> datacode = resultcode.getData();

            if(datacode.size()>0) {
                for (JSONObject code : datacode) {


                    pageSize = 700;
                    response.put("pageSize",pageSize);
                    jsonObject.put("nodeLabel","asfdas");
                    jsonObject.put("propertyLabel","asfdas");
                    jsonObject.put("propertyValue",code.get("code"));
                    jsonObject.put("areaCode",code.get("citySuburbId").toString());
            mm = 5;

            PageResponse<JSONObject> resultpre = iPRanJsDao.getgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
            List<JSONObject> dataprelist = resultpre.getData();
            if(dataprelist.size() > 0){
                JSONObject datapre = dataprelist.get(0);
                if(!datapre.isEmpty()){
                    continue;
                }
            }

            PageResponse<JSONObject> resultnodes = null;
            PageResponse<JSONObject> resultnodesport = null;
            PageResponse<JSONObject> resultnodesdevice = null;
            PageResponse<JSONObject> resultnodesfacility = null;
            PageResponse<JSONObject> resultnodesarea = null;
            PageResponse<JSONObject> resultnodesservice = null;
            PageResponse<JSONObject> resultnodesware = null;

            //根据cjType切换不同接口查询
            //if("云专网连接图PON接入段".equals(cjType)){
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                mm = 51;
                resultnodes = iPRanJsDao.getgraphnodes(jsonObject, pageSize, pageNum, "ds_graph_js");
                mm = 52;
                resultnodesport = iPRanJsDao.getgraphnodesport(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesdevice = iPRanJsDao.getgraphnodesdevice(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesfacility = iPRanJsDao.getgraphnodesfacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesarea = iPRanJsDao.getgraphnodesarea(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesservice = iPRanJsDao.getgraphnodesservice(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultnodesware = iPRanJsDao.getgraphnodesware(jsonObject, pageSize, pageNum, "ds_graph_js");
            }
            List<JSONObject> datanodes = resultnodes.getData();
            List<JSONObject> datanodesport = resultnodesport.getData();
            List<JSONObject> datanodesdevice = resultnodesdevice.getData();
            List<JSONObject> datanodesfacility = resultnodesfacility.getData();
            List<JSONObject> datanodesarea = resultnodesarea.getData();
            List<JSONObject> datanodesservice = resultnodesservice.getData();
            List<JSONObject> datanodesware = resultnodesware.getData();

            mm = 53;
            int ii = 0;

            if(datanodes.size()>0) {
                for (JSONObject nodes : datanodes) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.set(ii, nodes);
                    ii++;
                }
            }

            mm = 54;
            if(datanodesport.size()>0) {
                for (JSONObject nodes : datanodesport) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            mm = 55;
            if(datanodesfacility.size()>0) {
                for (JSONObject nodes : datanodesfacility) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            mm = 56;
            if(datanodesdevice.size()>0) {
                for (JSONObject nodes : datanodesdevice) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesarea.size()>0) {
                for (JSONObject nodes : datanodesarea) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesservice.size()>0) {
                for (JSONObject nodes : datanodesservice) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            if(datanodesware.size()>0) {
                for (JSONObject nodes : datanodesware) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.remove("nodelabels");
                    datanodes.add(ii, nodes);
                    ii++;
                }
            }

            mm = 6;
            PageResponse<JSONObject> resultrelations = null;
            PageResponse<JSONObject> resultlinkrelations = null;
            PageResponse<JSONObject> resultrelationsda = null;
            PageResponse<JSONObject> resultrelationsdf = null;
            PageResponse<JSONObject> resultrelationsdn = null;
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                resultrelations= iPRanJsDao.getgraphrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultlinkrelations= iPRanJsDao.getgraphlinkrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultrelationsda= iPRanJsDao.getgraphrelationsdevicearea(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultrelationsdf= iPRanJsDao.getgraphrelationsdevicefacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                resultrelationsdn= iPRanJsDao.getgraphrelationsdevicenumber(jsonObject, pageSize, pageNum, "ds_graph_js");
            }


            List<JSONObject> datarelations = resultrelations.getData();
            List<JSONObject> datalinkrelations = resultlinkrelations.getData();
            List<JSONObject> datalinkrelationsda = resultrelationsda.getData();
            List<JSONObject> datalinkrelationsdf = resultrelationsdf.getData();
            List<JSONObject> datalinkrelationsdn = resultrelationsdn.getData();

            ii = 0;
             mm = 60;
             if(datarelations.size()>0) {
                 for (JSONObject nodes : datarelations) {
                     String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                     nodes.put("a_nodeLabels", nodelable);
                     nodes.remove("a_nodelabels");
                     String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                     nodes.put("z_nodeLabels", znodelable);
                     nodes.remove("z_nodelabels");
                     datarelations.set(ii, nodes);
                     ii++;
                 }
             }
                    mm = 61;
            if(datalinkrelations.size()>0){
                for(JSONObject nodes:datalinkrelations){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }
                    mm = 62;
            if(datalinkrelationsda.size()>0){
                for(JSONObject nodes:datalinkrelationsda){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }
                    mm = 63;
            if(datalinkrelationsdf.size()>0){
                for(JSONObject nodes:datalinkrelationsdf){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }

                    mm = 64;
            if(datalinkrelationsdn.size()>0){
                for(JSONObject nodes:datalinkrelationsdn){
                    String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                    nodes.put("a_nodeLabels",nodelable);
                    nodes.remove("a_nodelabels");
                    String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                    nodes.put("z_nodeLabels",znodelable);
                    nodes.remove("z_nodelabels");
                    datarelations.add(ii,nodes);
                    ii++;
                }
            }


            PageResponse<JSONObject> resultlinks = null;
            if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                resultlinks= iPRanJsDao.getgraphlinks(jsonObject, pageSize, pageNum, "ds_graph_js");
            }


            List<JSONObject> datalinks = resultlinks.getData();

            ii = 0;
            if(datalinks.size()>0) {
                for (JSONObject nodes : datalinks) {
                    String[] nodelable = nodes.get("nodelabels").toString().split(",");
                    nodes.put("nodeLabels", nodelable);
                    nodes.put("linkType", nodes.get("linktype"));
                    nodes.remove("nodelabels");
                    nodes.remove("linktype");
                    datalinks.set(ii, nodes);
                    ii++;
                }
            }

            data.put("nodes",datanodes);
            data.put("relations",datarelations);
            data.put("links",datalinks);

            mm = 7;

            response.put("data",data);
            response.put("code",0);
            response.put("message","证明我加了"+propertyLabel);

            if(datalinks.size()>0){
                jsonObject.put("results",response.toJSONString());
                try {
                    iPRanJsDao.recordgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            response.put("message",e.getMessage()+mm);
        }

        return response;
    }


    @Override
    public JSONObject queryiPRanJsPrelocal(int i, int i1, String cjType) {
        JSONObject response = new JSONObject();
        com.alibaba.fastjson.JSONObject data = new com.alibaba.fastjson.JSONObject();
        int mm = 0;

        //        JSONObject json = JSONObject.parseObject(param.toJSONString());
        //        JSONObject businessParam = json.getJSONObject("businessParam");
        //        JSONObject whereBody = json.getJSONObject("whereBody");
        //        String custmor = businessParam.get("custmor").toString();
        //        String requestId = businessParam.get("requestId").toString();
        //        String sendTime = businessParam.get("sendTime").toString();
        //        String apiId = businessParam.get("apiId").toString();
        //        String nodeLabel = whereBody.get("nodeLabel").toString();
        String propertyLabel = "CRM_PRODUCT_IDD";
        //        String propertyValue = whereBody.get("propertyValue").toString();

        Date date = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化时间
        String reqTime = formatter.format(date);

        response.put("backTime", reqTime);
        response.put("data", "");
        response.put("nextPage", true);


        int pageSize = 10000;
        int pageNum = 0;

        response.put("pageSize", pageSize);
        response.put("pageNum", pageNum);

        JSONObject jsonObject = new JSONObject();

        PageResponse<JSONObject> resultcode = iPRanJsDao.getgraphcodeallLocal(jsonObject, pageSize, pageNum, "ds_graph_js");
        List<JSONObject> datacode = resultcode.getData();

        if (datacode.size() > 0) {
            for (JSONObject code : datacode) {


                pageSize = 700;
                response.put("pageSize", pageSize);
                jsonObject.put("nodeLabel", "asfdas");
                jsonObject.put("propertyLabel", "asfdas");
                jsonObject.put("propertyValue", code.get("code"));
                jsonObject.put("areaCode", code.get("citySuburbId").toString());
                mm = 5;

                PageResponse<JSONObject> resultpre = iPRanJsDao.getgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                List<JSONObject> dataprelist = resultpre.getData();
                if (dataprelist.size() > 0) {
                    JSONObject datapre = dataprelist.get(0);
                    if (!datapre.isEmpty()) {
                        continue;
                    }
                }

                PageResponse<JSONObject> resultnodes = null;
                PageResponse<JSONObject> resultnodesport = null;
                PageResponse<JSONObject> resultnodesdevice = null;
                PageResponse<JSONObject> resultnodesfacility = null;
                PageResponse<JSONObject> resultnodesarea = null;
                PageResponse<JSONObject> resultnodesservice = null;
                PageResponse<JSONObject> resultnodesware = null;

                //根据cjType切换不同接口查询
                //if("云专网连接图PON接入段".equals(cjType)){
                if (!propertyLabel.equals("CRM_PRODUCT_ID")) {
                    mm = 51;
                    resultnodes = iPRanJsDao.getgraphnodes(jsonObject, pageSize, pageNum, "ds_graph_js");
                    mm = 52;
                    resultnodesport = iPRanJsDao.getgraphnodesport(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesdevice = iPRanJsDao.getgraphnodesdevice(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesfacility = iPRanJsDao.getgraphnodesfacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesarea = iPRanJsDao.getgraphnodesarea(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesservice = iPRanJsDao.getgraphnodesservice(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesware = iPRanJsDao.getgraphnodesware(jsonObject, pageSize, pageNum, "ds_graph_js");
                }
                List<JSONObject> datanodes = resultnodes.getData();
                List<JSONObject> datanodesport = resultnodesport.getData();
                List<JSONObject> datanodesdevice = resultnodesdevice.getData();
                List<JSONObject> datanodesfacility = resultnodesfacility.getData();
                List<JSONObject> datanodesarea = resultnodesarea.getData();
                List<JSONObject> datanodesservice = resultnodesservice.getData();
                List<JSONObject> datanodesware = resultnodesware.getData();

                mm = 53;
                int ii = 0;

                if (datanodes.size() > 0) {
                    for (JSONObject nodes : datanodes) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.set(ii, nodes);
                        ii++;
                    }
                }

                mm = 54;
                if (datanodesport.size() > 0) {
                    for (JSONObject nodes : datanodesport) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                mm = 55;
                if (datanodesfacility.size() > 0) {
                    for (JSONObject nodes : datanodesfacility) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                mm = 56;
                if (datanodesdevice.size() > 0) {
                    for (JSONObject nodes : datanodesdevice) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                mm = 57;
                if (datanodesarea.size() > 0) {
                    for (JSONObject nodes : datanodesarea) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                mm = 58;
                if (datanodesservice.size() > 0) {
                    for (JSONObject nodes : datanodesservice) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                if(datanodesware.size()>0) {
                    for (JSONObject nodes : datanodesware) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                mm = 6;
                PageResponse<JSONObject> resultrelations = null;
                PageResponse<JSONObject> resultlinkrelations = null;
                PageResponse<JSONObject> resultrelationsda = null;
                PageResponse<JSONObject> resultrelationsdf = null;
                PageResponse<JSONObject> resultrelationsdn = null;
                if (!propertyLabel.equals("CRM_PRODUCT_ID")) {
                    resultrelations = iPRanJsDao.getgraphrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultlinkrelations = iPRanJsDao.getgraphlinkrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultrelationsda = iPRanJsDao.getgraphrelationsdevicearea(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultrelationsdf = iPRanJsDao.getgraphrelationsdevicefacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultrelationsdn = iPRanJsDao.getgraphrelationsdevicenumber(jsonObject, pageSize, pageNum, "ds_graph_js");
                }


                List<JSONObject> datarelations = resultrelations.getData();
                List<JSONObject> datalinkrelations = resultlinkrelations.getData();
                List<JSONObject> datalinkrelationsda = resultrelationsda.getData();
                List<JSONObject> datalinkrelationsdf = resultrelationsdf.getData();
                List<JSONObject> datalinkrelationsdn = resultrelationsdn.getData();

                ii = 0;
                if (datarelations.size() > 0) {
                    for (JSONObject nodes : datarelations) {
                        String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels", nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels", znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.set(ii, nodes);
                        ii++;
                    }
                }
                ii = 1;
                if (datalinkrelations.size() > 0) {
                    for (JSONObject nodes : datalinkrelations) {
                        String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels", nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels", znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.add(ii, nodes);
                        ii++;
                    }
                }

                ii = 2;
                if (datalinkrelationsda.size() > 0) {
                    for (JSONObject nodes : datalinkrelationsda) {
                        String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels", nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels", znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.add(ii, nodes);
                        ii++;
                    }
                }

                ii = 3;
                if (datalinkrelationsdf.size() > 0) {
                    for (JSONObject nodes : datalinkrelationsdf) {
                        String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels", nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels", znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.add(ii, nodes);
                        ii++;
                    }
                }

                ii = 4;
                if (datalinkrelationsdn.size() > 0) {
                    for (JSONObject nodes : datalinkrelationsdn) {
                        String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels", nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels", znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.add(ii, nodes);
                        ii++;
                    }
                }


                PageResponse<JSONObject> resultlinks = null;
                if (!propertyLabel.equals("CRM_PRODUCT_ID")) {
                    resultlinks = iPRanJsDao.getgraphlinks(jsonObject, pageSize, pageNum, "ds_graph_js");
                }


                List<JSONObject> datalinks = resultlinks.getData();

                ii = 5;
                if (datalinks.size() > 0) {
                    for (JSONObject nodes : datalinks) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.put("linkType", nodes.get("linktype"));
                        nodes.remove("nodelabels");
                        nodes.remove("linktype");
                        datalinks.set(ii, nodes);
                        ii++;
                    }
                }

                ii = 6;
                data.put("nodes", datanodes);
                data.put("relations", datarelations);
                data.put("links", datalinks);

                mm = 7;

                response.put("data", data);
                response.put("code", 0);
                response.put("message", "证明我加了" + propertyLabel);

                if (datalinks.size() > 0) {
                    jsonObject.put("results", response.toJSONString());
                    try {
                        iPRanJsDao.recordgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

            }
        }

    return response;
    }


    /**
     * 添加支持入参为电路对象 , 继承中博原有逻辑不动
     * @param i
     * @param i1
     * @param cjType
     * @param circuit
     * @return
     */
    public JSONObject queryiPRanJsPre4(int i, int i1, String cjType,JSONObject circuit) {
        JSONObject response = new JSONObject();
        com.alibaba.fastjson.JSONObject data = new com.alibaba.fastjson.JSONObject();
        int mm = 0;

        String propertyLabel = "CRM_PRODUCT_IDD";
        //        String propertyValue = whereBody.get("propertyValue").toString();

        Date date=new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化时间
        String reqTime=formatter.format(date);

        response.put("backTime",reqTime);
        response.put("data","");
        response.put("nextPage",true);



        int pageSize = 10000;
        int pageNum = 0;

        response.put("pageSize",pageSize);
        response.put("pageNum",pageNum);

        JSONObject jsonObject = new JSONObject();

        List<JSONObject> datacode = new ArrayList<>();
        datacode.add(circuit);

        try {
            if(datacode.size()>0) {
                for (JSONObject code : datacode) {
                    System.out.println("code:"+code.get("code").toString());


                    pageSize = 700;
                    response.put("pageSize",pageSize);
                    jsonObject.put("nodeLabel","asfdas");
                    jsonObject.put("propertyLabel","asfdas");
                    jsonObject.put("propertyValue",code.get("code"));
                    jsonObject.put("areaCode",code.get("citySuburbId").toString());
                    mm = 5;

                    PageResponse<JSONObject> resultpre = iPRanJsDao.getgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                    List<JSONObject> dataprelist = resultpre.getData();
                    if(dataprelist.size() > 0){
                        JSONObject datapre = dataprelist.get(0);
                        if(!datapre.isEmpty()){
                            continue;
                        }
                    }


                    PageResponse<JSONObject> resultnodes = null;
                    PageResponse<JSONObject> resultnodesport = null;
                    PageResponse<JSONObject> resultnodesdevice = null;
                    PageResponse<JSONObject> resultnodesfacility = null;
                    PageResponse<JSONObject> resultnodesarea = null;
                    PageResponse<JSONObject> resultnodesservice = null;
                    PageResponse<JSONObject> resultnodesware = null;

                    //根据cjType切换不同接口查询
                    //if("云专网连接图PON接入段".equals(cjType)){
                    if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                        mm = 51;
                        resultnodes = iPRanJsDao.getgraphnodes(jsonObject, pageSize, pageNum, "ds_graph_js");
                        mm = 52;
                        resultnodesport = iPRanJsDao.getgraphnodesport(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesdevice = iPRanJsDao.getgraphnodesdevice(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesfacility = iPRanJsDao.getgraphnodesfacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesarea = iPRanJsDao.getgraphnodesarea(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesservice = iPRanJsDao.getgraphnodesservice(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesware = iPRanJsDao.getgraphnodesware(jsonObject, pageSize, pageNum, "ds_graph_js");
                    }
                    List<JSONObject> datanodes = resultnodes.getData();
                    List<JSONObject> datanodesport = resultnodesport.getData();
                    List<JSONObject> datanodesdevice = resultnodesdevice.getData();
                    List<JSONObject> datanodesfacility = resultnodesfacility.getData();
                    List<JSONObject> datanodesarea = resultnodesarea.getData();
                    List<JSONObject> datanodesservice = resultnodesservice.getData();
                    List<JSONObject> datanodesware = resultnodesware.getData();

                    mm = 53;
                    int ii = 0;
                    if(datanodes.size()>0) {
                        for (JSONObject nodes : datanodes) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.set(ii, nodes);
                            ii++;
                        }
                    }

                    mm = 54;
                    if(datanodesport.size()>0) {
                        for (JSONObject nodes : datanodesport) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesfacility.size()>0) {
                        for (JSONObject nodes : datanodesfacility) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesdevice.size()>0) {
                        for (JSONObject nodes : datanodesdevice) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesarea.size()>0) {
                        for (JSONObject nodes : datanodesarea) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesservice.size()>0) {
                        for (JSONObject nodes : datanodesservice) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesware.size()>0) {
                        for (JSONObject nodes : datanodesware) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    mm = 6;
                    PageResponse<JSONObject> resultrelations = null;
                    PageResponse<JSONObject> resultlinkrelations = null;
                    PageResponse<JSONObject> resultrelationsda = null;
                    PageResponse<JSONObject> resultrelationsdf = null;
                    PageResponse<JSONObject> resultrelationsdn = null;
                    if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                        resultrelations= iPRanJsDao.getgraphrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultlinkrelations= iPRanJsDao.getgraphlinkrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultrelationsda= iPRanJsDao.getgraphrelationsdevicearea(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultrelationsdf= iPRanJsDao.getgraphrelationsdevicefacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultrelationsdn= iPRanJsDao.getgraphrelationsdevicenumber(jsonObject, pageSize, pageNum, "ds_graph_js");
                    }


                    List<JSONObject> datarelations = resultrelations.getData();
                    List<JSONObject> datalinkrelations = resultlinkrelations.getData();
                    List<JSONObject> datalinkrelationsda = resultrelationsda.getData();
                    List<JSONObject> datalinkrelationsdf = resultrelationsdf.getData();
                    List<JSONObject> datalinkrelationsdn = resultrelationsdn.getData();

                    ii = 0;
                    if(datarelations.size()>0) {
                        for (JSONObject nodes : datarelations) {
                            String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels", nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels", znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.set(ii, nodes);
                            ii++;
                        }
                    }

                    if(datalinkrelations.size()>0){
                        for(JSONObject nodes:datalinkrelations){
                            String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels",nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels",znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.add(ii,nodes);
                            ii++;
                        }
                    }

                    if(datalinkrelationsda.size()>0){
                        for(JSONObject nodes:datalinkrelationsda){
                            String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels",nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels",znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.add(ii,nodes);
                            ii++;
                        }
                    }

                    if(datalinkrelationsdf.size()>0){
                        for(JSONObject nodes:datalinkrelationsdf){
                            String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels",nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels",znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.add(ii,nodes);
                            ii++;
                        }
                    }

                    if(datalinkrelationsdn.size()>0){
                        for(JSONObject nodes:datalinkrelationsdn){
                            String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels",nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels",znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.add(ii,nodes);
                            ii++;
                        }
                    }


                    PageResponse<JSONObject> resultlinks = null;
                    if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                        resultlinks= iPRanJsDao.getgraphlinks(jsonObject, pageSize, pageNum, "ds_graph_js");
                    }


                    List<JSONObject> datalinks = resultlinks.getData();

                    ii = 0;
                    if(datalinks.size()>0) {
                        for (JSONObject nodes : datalinks) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.put("linkType", nodes.get("linktype"));
                            nodes.remove("nodelabels");
                            nodes.remove("linktype");
                            datalinks.set(ii, nodes);
                            ii++;
                        }
                    }

                    data.put("nodes",datanodes);
                    data.put("relations",datarelations);
                    data.put("links",datalinks);

                    mm = 7;

                    response.put("data",data);
                    response.put("code",0);
                    response.put("message","证明我加了"+propertyLabel);

                    if(datalinks.size()>0){
                        jsonObject.put("results",response.toJSONString());
                        try {
                            iPRanJsDao.recordgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;
    }

    @Override
    public JSONObject queryiPRanJsPre2(int i, int i1, String cjType) {
        JSONObject response = new JSONObject();
        com.alibaba.fastjson.JSONObject data = new com.alibaba.fastjson.JSONObject();
        int mm = 0;

        //        JSONObject json = JSONObject.parseObject(param.toJSONString());
        //        JSONObject businessParam = json.getJSONObject("businessParam");
        //        JSONObject whereBody = json.getJSONObject("whereBody");
        //        String custmor = businessParam.get("custmor").toString();
        //        String requestId = businessParam.get("requestId").toString();
        //        String sendTime = businessParam.get("sendTime").toString();
        //        String apiId = businessParam.get("apiId").toString();
        //        String nodeLabel = whereBody.get("nodeLabel").toString();
        String propertyLabel = "CRM_PRODUCT_IDD";
        //        String propertyValue = whereBody.get("propertyValue").toString();

        Date date=new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化时间
        String reqTime=formatter.format(date);

        response.put("backTime",reqTime);
        response.put("data","");
        response.put("nextPage",true);



        int pageSize = 10000;
        int pageNum = 0;

        response.put("pageSize",pageSize);
        response.put("pageNum",pageNum);

        JSONObject jsonObject = new JSONObject();

        PageResponse<JSONObject> resultcode = iPRanJsDao.getgraphcodeall2(jsonObject, pageSize, pageNum, "ds_graph_js");
        List<JSONObject> datacode = resultcode.getData();

        try {
            if(datacode.size()>0) {
                for (JSONObject code : datacode) {
                    System.out.println("code:"+code.get("code").toString());


                    pageSize = 700;
                    response.put("pageSize",pageSize);
                    jsonObject.put("nodeLabel","asfdas");
                    jsonObject.put("propertyLabel","asfdas");
                    jsonObject.put("propertyValue",code.get("code"));
                    jsonObject.put("areaCode",code.get("citySuburbId").toString());
                    mm = 5;

                    PageResponse<JSONObject> resultpre = iPRanJsDao.getgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                    List<JSONObject> dataprelist = resultpre.getData();
                    if(dataprelist.size() > 0){
                        JSONObject datapre = dataprelist.get(0);
                        if(!datapre.isEmpty()){
                            continue;
                        }
                    }


                    PageResponse<JSONObject> resultnodes = null;
                    PageResponse<JSONObject> resultnodesport = null;
                    PageResponse<JSONObject> resultnodesdevice = null;
                    PageResponse<JSONObject> resultnodesfacility = null;
                    PageResponse<JSONObject> resultnodesarea = null;
                    PageResponse<JSONObject> resultnodesservice = null;
                    PageResponse<JSONObject> resultnodesware = null;

                    //根据cjType切换不同接口查询
                    //if("云专网连接图PON接入段".equals(cjType)){
                    if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                        mm = 51;
                        resultnodes = iPRanJsDao.getgraphnodes(jsonObject, pageSize, pageNum, "ds_graph_js");
                        mm = 52;
                        resultnodesport = iPRanJsDao.getgraphnodesport(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesdevice = iPRanJsDao.getgraphnodesdevice(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesfacility = iPRanJsDao.getgraphnodesfacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesarea = iPRanJsDao.getgraphnodesarea(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesservice = iPRanJsDao.getgraphnodesservice(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultnodesware = iPRanJsDao.getgraphnodesware(jsonObject, pageSize, pageNum, "ds_graph_js");
                    }
                    List<JSONObject> datanodes = resultnodes.getData();
                    List<JSONObject> datanodesport = resultnodesport.getData();
                    List<JSONObject> datanodesdevice = resultnodesdevice.getData();
                    List<JSONObject> datanodesfacility = resultnodesfacility.getData();
                    List<JSONObject> datanodesarea = resultnodesarea.getData();
                    List<JSONObject> datanodesservice = resultnodesservice.getData();
                    List<JSONObject> datanodesware = resultnodesware.getData();

                    mm = 53;
                    int ii = 0;
                    if(datanodes.size()>0) {
                        for (JSONObject nodes : datanodes) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.set(ii, nodes);
                            ii++;
                        }
                    }

                    mm = 54;
                    if(datanodesport.size()>0) {
                        for (JSONObject nodes : datanodesport) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesfacility.size()>0) {
                        for (JSONObject nodes : datanodesfacility) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesdevice.size()>0) {
                        for (JSONObject nodes : datanodesdevice) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesarea.size()>0) {
                        for (JSONObject nodes : datanodesarea) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesservice.size()>0) {
                        for (JSONObject nodes : datanodesservice) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    if(datanodesware.size()>0) {
                        for (JSONObject nodes : datanodesware) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.remove("nodelabels");
                            datanodes.add(ii, nodes);
                            ii++;
                        }
                    }

                    mm = 6;
                    PageResponse<JSONObject> resultrelations = null;
                    PageResponse<JSONObject> resultlinkrelations = null;
                    PageResponse<JSONObject> resultrelationsda = null;
                    PageResponse<JSONObject> resultrelationsdf = null;
                    PageResponse<JSONObject> resultrelationsdn = null;
                    if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                        resultrelations= iPRanJsDao.getgraphrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultlinkrelations= iPRanJsDao.getgraphlinkrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultrelationsda= iPRanJsDao.getgraphrelationsdevicearea(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultrelationsdf= iPRanJsDao.getgraphrelationsdevicefacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                        resultrelationsdn= iPRanJsDao.getgraphrelationsdevicenumber(jsonObject, pageSize, pageNum, "ds_graph_js");
                    }


                    List<JSONObject> datarelations = resultrelations.getData();
                    List<JSONObject> datalinkrelations = resultlinkrelations.getData();
                    List<JSONObject> datalinkrelationsda = resultrelationsda.getData();
                    List<JSONObject> datalinkrelationsdf = resultrelationsdf.getData();
                    List<JSONObject> datalinkrelationsdn = resultrelationsdn.getData();

                    ii = 0;
                    if(datarelations.size()>0) {
                        for (JSONObject nodes : datarelations) {
                            String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels", nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels", znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.set(ii, nodes);
                            ii++;
                        }
                    }

                    if(datalinkrelations.size()>0){
                        for(JSONObject nodes:datalinkrelations){
                            String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels",nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels",znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.add(ii,nodes);
                            ii++;
                        }
                    }

                    if(datalinkrelationsda.size()>0){
                        for(JSONObject nodes:datalinkrelationsda){
                            String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels",nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels",znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.add(ii,nodes);
                            ii++;
                        }
                    }

                    if(datalinkrelationsdf.size()>0){
                        for(JSONObject nodes:datalinkrelationsdf){
                            String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels",nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels",znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.add(ii,nodes);
                            ii++;
                        }
                    }

                    if(datalinkrelationsdn.size()>0){
                        for(JSONObject nodes:datalinkrelationsdn){
                            String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                            nodes.put("a_nodeLabels",nodelable);
                            nodes.remove("a_nodelabels");
                            String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                            nodes.put("z_nodeLabels",znodelable);
                            nodes.remove("z_nodelabels");
                            datarelations.add(ii,nodes);
                            ii++;
                        }
                    }


                    PageResponse<JSONObject> resultlinks = null;
                    if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                        resultlinks= iPRanJsDao.getgraphlinks(jsonObject, pageSize, pageNum, "ds_graph_js");
                    }


                    List<JSONObject> datalinks = resultlinks.getData();

                    ii = 0;
                    if(datalinks.size()>0) {
                        for (JSONObject nodes : datalinks) {
                            String[] nodelable = nodes.get("nodelabels").toString().split(",");
                            nodes.put("nodeLabels", nodelable);
                            nodes.put("linkType", nodes.get("linktype"));
                            nodes.remove("nodelabels");
                            nodes.remove("linktype");
                            datalinks.set(ii, nodes);
                            ii++;
                        }
                    }

                    data.put("nodes",datanodes);
                    data.put("relations",datarelations);
                    data.put("links",datalinks);

                    mm = 7;

                    response.put("data",data);
                    response.put("code",0);
                    response.put("message","证明我加了"+propertyLabel);

                    if(datalinks.size()>0){
                        jsonObject.put("results",response.toJSONString());
                        try {
                            iPRanJsDao.recordgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;
    }

    @Override
    public JSONObject queryiPRanJsPre3(int i, int i1, String cjType) {
        JSONObject response = new JSONObject();
        com.alibaba.fastjson.JSONObject data = new com.alibaba.fastjson.JSONObject();
        int mm = 0;

        //        JSONObject json = JSONObject.parseObject(param.toJSONString());
        //        JSONObject businessParam = json.getJSONObject("businessParam");
        //        JSONObject whereBody = json.getJSONObject("whereBody");
        //        String custmor = businessParam.get("custmor").toString();
        //        String requestId = businessParam.get("requestId").toString();
        //        String sendTime = businessParam.get("sendTime").toString();
        //        String apiId = businessParam.get("apiId").toString();
        //        String nodeLabel = whereBody.get("nodeLabel").toString();
        String propertyLabel = "CRM_PRODUCT_IDD";
        //        String propertyValue = whereBody.get("propertyValue").toString();

        Date date=new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化时间
        String reqTime=formatter.format(date);

        response.put("backTime",reqTime);
        response.put("data","");
        response.put("nextPage",true);



        int pageSize = 10000;
        int pageNum = 0;

        response.put("pageSize",pageSize);
        response.put("pageNum",pageNum);

        JSONObject jsonObject = new JSONObject();

        PageResponse<JSONObject> resultcode = iPRanJsDao.getgraphcodeall3(jsonObject, pageSize, pageNum, "ds_graph_js");
        List<JSONObject> datacode = resultcode.getData();

        if(datacode.size()>0) {
            for (JSONObject code : datacode) {


                pageSize = 700;
                response.put("pageSize",pageSize);
                jsonObject.put("nodeLabel","asfdas");
                jsonObject.put("propertyLabel","asfdas");
                jsonObject.put("propertyValue",code.get("code"));
                jsonObject.put("areaCode",code.get("citySuburbId").toString());
                mm = 5;

                PageResponse<JSONObject> resultpre = iPRanJsDao.getgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                List<JSONObject> dataprelist = resultpre.getData();
                if(dataprelist.size() > 0){
                    JSONObject datapre = dataprelist.get(0);
                    if(!datapre.isEmpty()){
                        continue;
                    }
                }

                PageResponse<JSONObject> resultnodes = null;
                PageResponse<JSONObject> resultnodesport = null;
                PageResponse<JSONObject> resultnodesdevice = null;
                PageResponse<JSONObject> resultnodesfacility = null;
                PageResponse<JSONObject> resultnodesarea = null;
                PageResponse<JSONObject> resultnodesservice = null;
                PageResponse<JSONObject> resultnodesware = null;

                //根据cjType切换不同接口查询
                //if("云专网连接图PON接入段".equals(cjType)){
                if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                    mm = 51;
                    resultnodes = iPRanJsDao.getgraphnodes(jsonObject, pageSize, pageNum, "ds_graph_js");
                    mm = 52;
                    resultnodesport = iPRanJsDao.getgraphnodesport(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesdevice = iPRanJsDao.getgraphnodesdevice(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesfacility = iPRanJsDao.getgraphnodesfacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesarea = iPRanJsDao.getgraphnodesarea(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesservice = iPRanJsDao.getgraphnodesservice(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultnodesware = iPRanJsDao.getgraphnodesware(jsonObject, pageSize, pageNum, "ds_graph_js");
                }
                List<JSONObject> datanodes = resultnodes.getData();
                List<JSONObject> datanodesport = resultnodesport.getData();
                List<JSONObject> datanodesdevice = resultnodesdevice.getData();
                List<JSONObject> datanodesfacility = resultnodesfacility.getData();
                List<JSONObject> datanodesarea = resultnodesarea.getData();
                List<JSONObject> datanodesservice = resultnodesservice.getData();
                List<JSONObject> datanodesware = resultnodesware.getData();

                mm = 53;
                int ii = 0;
                if(datanodes.size()>0) {
                    for (JSONObject nodes : datanodes) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.set(ii, nodes);
                        ii++;
                    }
                }

                mm = 54;
                if(datanodesport.size()>0) {
                    for (JSONObject nodes : datanodesport) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                if(datanodesfacility.size()>0) {
                    for (JSONObject nodes : datanodesfacility) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                if(datanodesdevice.size()>0) {
                    for (JSONObject nodes : datanodesdevice) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                if(datanodesarea.size()>0) {
                    for (JSONObject nodes : datanodesarea) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                if(datanodesservice.size()>0) {
                    for (JSONObject nodes : datanodesservice) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                if(datanodesware.size()>0) {
                    for (JSONObject nodes : datanodesware) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.remove("nodelabels");
                        datanodes.add(ii, nodes);
                        ii++;
                    }
                }

                mm = 6;
                PageResponse<JSONObject> resultrelations = null;
                PageResponse<JSONObject> resultlinkrelations = null;
                PageResponse<JSONObject> resultrelationsda = null;
                PageResponse<JSONObject> resultrelationsdf = null;
                PageResponse<JSONObject> resultrelationsdn = null;
                if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                    resultrelations= iPRanJsDao.getgraphrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultlinkrelations= iPRanJsDao.getgraphlinkrelations(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultrelationsda= iPRanJsDao.getgraphrelationsdevicearea(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultrelationsdf= iPRanJsDao.getgraphrelationsdevicefacility(jsonObject, pageSize, pageNum, "ds_graph_js");
                    resultrelationsdn= iPRanJsDao.getgraphrelationsdevicenumber(jsonObject, pageSize, pageNum, "ds_graph_js");
                }


                List<JSONObject> datarelations = resultrelations.getData();
                List<JSONObject> datalinkrelations = resultlinkrelations.getData();
                List<JSONObject> datalinkrelationsda = resultrelationsda.getData();
                List<JSONObject> datalinkrelationsdf = resultrelationsdf.getData();
                List<JSONObject> datalinkrelationsdn = resultrelationsdn.getData();

                ii = 0;
                if(datarelations.size()>0) {
                    for (JSONObject nodes : datarelations) {
                        String[] nodelable = nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels", nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable = nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels", znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.set(ii, nodes);
                        ii++;
                    }
                }

                if(datalinkrelations.size()>0){
                    for(JSONObject nodes:datalinkrelations){
                        String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels",nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels",znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.add(ii,nodes);
                        ii++;
                    }
                }

                if(datalinkrelationsda.size()>0){
                    for(JSONObject nodes:datalinkrelationsda){
                        String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels",nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels",znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.add(ii,nodes);
                        ii++;
                    }
                }

                if(datalinkrelationsdf.size()>0){
                    for(JSONObject nodes:datalinkrelationsdf){
                        String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels",nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels",znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.add(ii,nodes);
                        ii++;
                    }
                }

                if(datalinkrelationsdn.size()>0){
                    for(JSONObject nodes:datalinkrelationsdn){
                        String[] nodelable =  nodes.get("a_nodelabels").toString().split(",");
                        nodes.put("a_nodeLabels",nodelable);
                        nodes.remove("a_nodelabels");
                        String[] znodelable =  nodes.get("z_nodelabels").toString().split(",");
                        nodes.put("z_nodeLabels",znodelable);
                        nodes.remove("z_nodelabels");
                        datarelations.add(ii,nodes);
                        ii++;
                    }
                }


                PageResponse<JSONObject> resultlinks = null;
                if(!propertyLabel.equals("CRM_PRODUCT_ID")){
                    resultlinks= iPRanJsDao.getgraphlinks(jsonObject, pageSize, pageNum, "ds_graph_js");
                }


                List<JSONObject> datalinks = resultlinks.getData();

                ii = 0;
                if(datalinks.size()>0) {
                    for (JSONObject nodes : datalinks) {
                        String[] nodelable = nodes.get("nodelabels").toString().split(",");
                        nodes.put("nodeLabels", nodelable);
                        nodes.put("linkType", nodes.get("linktype"));
                        nodes.remove("nodelabels");
                        nodes.remove("linktype");
                        datalinks.set(ii, nodes);
                        ii++;
                    }
                }

                data.put("nodes",datanodes);
                data.put("relations",datarelations);
                data.put("links",datalinks);

                mm = 7;

                response.put("data",data);
                response.put("code",0);
                response.put("message","证明我加了"+propertyLabel);

                if(datalinks.size()>0){
                    jsonObject.put("results",response.toJSONString());
                    try {
                        iPRanJsDao.recordgraphresults(jsonObject, pageSize, pageNum, "ds_graph_js");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

            }
        }

        return response;
    }

    @Async("graphRestPool")
    public void saveIpranInfo(JSONObject param) {
        String circuitCode = param.getString("circuitCode");
        try {
            String areaName = circuitCode.substring(0, 2);

            //获取路由信息
            JSONObject result = null;
            result = this.getRouteByAccessCode(IPRAN_URL1, param.getString("accessCode"));

            if (StringUtils.isNotEmpty(result.getString("source_ip"))) {
                //接口1
                //构建数据（设备、端口、链路）
                this.saveIpranData(circuitCode, areaName, result.getString("source_ip"), result.getString("source_port"),
                        result.getString("terminal1_ip"), result.getString("terminal1_port"),
                        result.getString("a1_ip"), result.getString("b1_ip"), result.getString("b2_ip"));
            } else {
                result = this.getRouteByAccessCode(IPRAN_URL2, param.getString("accessCode"));
                if (StringUtils.isNotEmpty(result.getString("source_ip"))) {
                    //接口2
                    //构建数据（设备、端口、链路）
                    this.saveIpranData(circuitCode, areaName, result.getString("source_ip"), result.getString("source_port"),
                            result.getString("terminal_ip"), result.getString("terminal_port"),
                            result.getString("a1_ip"), result.getString("b1_ip"), result.getString("b2_ip"));
                } else {
                    return;
                }
            }

//            circuitAnalysisService.saveException(circuitCode, "IPRAN", null, "成功", null);
        } catch (Exception e) {
            e.printStackTrace();
//            circuitAnalysisService.saveException(circuitCode, "IPRAN", null, "异常", MyExceptionUtil.getExceptionMsg(e));
        }
    }

    private JSONObject getRouteByAccessCode(String url, String accessCode) {
        JSONObject result = restTemplate.postForObject(url + "?access_code=" + accessCode, null, JSONObject.class);
        return result;
    }


    private void saveIpranData(String circuitCode, String areaName, String sourceIp, String sourcePort,
                               String terminalIp, String terminalPort,
                               String aIp, String b1Ip, String b2Ip) {
        //设备
        JSONObject deviceJson = this.getIpranDevice(areaName, sourceIp, terminalIp, aIp, b1Ip, b2Ip);
        JSONArray deviceIds = deviceJson.getJSONArray("deviceIds");

        //端口
        JSONObject portJson = this.getIpranPort(areaName, deviceJson.getString("sourceId"), sourcePort,
                deviceJson.getString("terminalId"), terminalPort);
        JSONArray portIds = portJson.getJSONArray("portIds");

        //链路
        JSONObject linkJson = this.getIpranLink(circuitCode, areaName, deviceJson, portJson);

        //保存设备、端口
        JSONObject queryDevicePortParam = new JSONObject();
        queryDevicePortParam.put("ids", portIds);
        JSONObject ports = circuitAnalysisDao.queryResPortsByIds(queryDevicePortParam, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
        queryDevicePortParam.put("ids", deviceIds);
        JSONObject devices = circuitAnalysisDao.queryResDeviceDetailPortsByDevice(queryDevicePortParam,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));

        JSONObject saveDevicePortParam = new JSONObject();
        saveDevicePortParam.put("ports", ports.getJSONArray("ports"));
        saveDevicePortParam.put("devices", devices.getJSONArray("devices"));
        saveDevicePortParam.put("wares", devices.getJSONArray("wares"));
        saveDevicePortParam.put("deviceWareRelas", devices.getJSONArray("deviceWareRelas"));
        circuitAnalysisDao.saveNetDevices(saveDevicePortParam, NRMConstants.SHARDING_CODE);

        //保存链路
        JSONArray cmLinkList = linkJson.getJSONArray("cmLinkList");
        JSONArray crLinkLinkList = linkJson.getJSONArray("crLinkLinkList");

        JSONObject saveLinkParam = new JSONObject();
        saveLinkParam.put("links", cmLinkList);
        saveLinkParam.put("linkRelas", crLinkLinkList);
        saveLinkParam.put("circuit_code", circuitCode);
        circuitAnalysisDao.saveNetLinks(saveLinkParam, NRMConstants.SHARDING_CODE);
    }

    private JSONObject getIpranDevice(String areaName, String sourceIp, String terminalIp, String aIp, String b1Ip, String b2Ip) {
        JSONObject result = new JSONObject();
        JSONArray deviceIds = new JSONArray();

        JSONObject bcDevParam = new JSONObject();

        if (StringUtils.isNotEmpty(sourceIp)) {
            bcDevParam.put("ip", sourceIp);
            PageResponse<JSONObject> sourceInfo = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                    NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
            List<JSONObject> sourceList = sourceInfo.getData();
            if (CollectionUtils.isNotEmpty(sourceList)) {
                result.put("sourceId", sourceList.get(0).getString("id"));
                deviceIds.add(sourceList.get(0).getBigDecimal("id"));
            }
        }

        if (StringUtils.isNotEmpty(terminalIp)) {
            bcDevParam.put("ip", terminalIp);
            PageResponse<JSONObject> terminalInfo = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                    NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
            List<JSONObject> terminalList = terminalInfo.getData();
            if (CollectionUtils.isNotEmpty(terminalList)) {
                result.put("terminalId", terminalList.get(0).getString("id"));
                deviceIds.add(terminalList.get(0).getBigDecimal("id"));
            }
        }

        if (StringUtils.isNotEmpty(aIp)) {
            bcDevParam.put("ip", aIp);
            PageResponse<JSONObject> aInfo = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                    NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
            List<JSONObject> aList = aInfo.getData();
            if (CollectionUtils.isNotEmpty(aList)) {
                result.put("aId", aList.get(0).getString("id"));
                deviceIds.add(aList.get(0).getBigDecimal("id"));
            }
        }

        if (StringUtils.isNotEmpty(b1Ip)) {
            bcDevParam.put("ip", b1Ip);
            PageResponse<JSONObject> b1Info = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                    NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
            List<JSONObject> b1List = b1Info.getData();
            if (CollectionUtils.isNotEmpty(b1List)) {
                result.put("b1Id", b1List.get(0).getString("id"));
                deviceIds.add(b1List.get(0).getBigDecimal("id"));
            }
        }

        if (StringUtils.isNotEmpty(b2Ip)) {
            bcDevParam.put("ip", b2Ip);
            PageResponse<JSONObject> b2Info = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                    NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
            List<JSONObject> b2List = b2Info.getData();
            if (CollectionUtils.isNotEmpty(b2List)) {
                result.put("b2Id", b2List.get(0).getString("id"));
                deviceIds.add(b2List.get(0).getBigDecimal("id"));
            }
        }

        result.put("deviceIds", deviceIds);
        return result;
    }

    private JSONObject getIpranPort(String areaName, String sourceId, String sourcePort, String terminalId, String terminalPort) {
        JSONObject result = new JSONObject();
        JSONArray portIds = new JSONArray();

        JSONObject bcPortParam = new JSONObject();

        if (StringUtils.isNotEmpty(sourcePort)) {
            bcPortParam.put("deviceId", sourceId);
            bcPortParam.put("code", sourcePort);
            PageResponse<JSONObject> sourcePortInfo = circuitAnalysisDao.queryBcPortByParam(bcPortParam, 100, 0,
                    NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
            List<JSONObject> sourcePortList = sourcePortInfo.getData();
            if (CollectionUtils.isNotEmpty(sourcePortList)) {
                result.put("sourcePortId", sourcePortList.get(0).getString("id"));
                portIds.add(sourcePortList.get(0).getBigDecimal("id"));
            }
        }

        if (StringUtils.isNotEmpty(sourcePort)) {
            bcPortParam.put("deviceId", terminalId);
            bcPortParam.put("code", terminalPort);
            PageResponse<JSONObject> terminalPortInfo = circuitAnalysisDao.queryBcPortByParam(bcPortParam, 100, 0,
                    NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
            List<JSONObject> terminalPortList = terminalPortInfo.getData();
            if (CollectionUtils.isNotEmpty(terminalPortList)) {
                result.put("terminalPortId", terminalPortList.get(0).getString("id"));
                portIds.add(terminalPortList.get(0).getBigDecimal("id"));
            }
        }

        result.put("portIds", portIds);
        return result;
    }

    private JSONObject getIpranLink(String circuitCode, String areaName, JSONObject deviceJson, JSONObject portJson) {
        JSONObject result = new JSONObject();
        JSONArray cmLinkList = new JSONArray();
        JSONArray crLinkLinkList = new JSONArray();

        Integer linkCount = 0;
        Integer linkLinkCount = 0;

        //source--terminal
        String sourceToTerminalId = otn.getId(linkCount);
        JSONObject sourceToTerminal = otn.createLinkObj(sourceToTerminalId, circuitCode, circuitCode, 1132100021L,
                NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("sourceId"),
                portJson.getString("sourcePortId"), deviceJson.getString("terminalId"), portJson.getString("terminalPortId"));
        cmLinkList.add(sourceToTerminal);
        linkCount ++;

        if (deviceJson.get("aId") != null) {
            //有A设备
            //source--a
            String sourceToAId = otn.getId(linkCount);
            JSONObject sourceToA = otn.createLinkObj(sourceToAId, "source-a", "source-a", 1132292008L,
                    NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("sourceId"),
                    portJson.getString("sourcePortId"), deviceJson.getString("aId"), null);
            cmLinkList.add(sourceToA);
            linkCount ++;

            //a--b1
            if (deviceJson.get("b1Id") != null) {
                String aToB1Id = otn.getId(linkCount);
                JSONObject aToB1 = otn.createLinkObj(aToB1Id, "a-b1", "a-b1", 1132292008L,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("aId"), null,
                        deviceJson.getString("b1Id"), null);
                cmLinkList.add(aToB1);
                linkCount ++;

                //b1--terminal
                String b1ToTerminalId = otn.getId(linkCount);
                JSONObject b1ToTerminal = otn.createLinkObj(b1ToTerminalId, "b1-asbr", "b1-asbr", 1132292008L,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("b1Id"), null,
                        deviceJson.getString("terminalId"), portJson.getString("terminalPortId"));
                cmLinkList.add(b1ToTerminal);
                linkCount ++;

                //1级--2级
                String firstToSecendId = otn.getId(linkLinkCount);
                JSONObject firstToSecend = otn.createLinkLinkObj(firstToSecendId, 1130311310000L, sourceToTerminalId, b1ToTerminalId,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132100021L, 1132292008L);
                crLinkLinkList.add(firstToSecend);
                linkLinkCount ++;

                //2级--3级
                String secendToThirdId = otn.getId(linkLinkCount);
                JSONObject secendToThird = otn.createLinkLinkObj(secendToThirdId, 1130311310000L, b1ToTerminalId, aToB1Id,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132292008L, 1132292008L);
                crLinkLinkList.add(secendToThird);
                linkLinkCount ++;

                //3级--4级
                String thirdToForthId = otn.getId(linkLinkCount);
                JSONObject thirdToForth = otn.createLinkLinkObj(thirdToForthId, 1130311310000L, aToB1Id, sourceToAId,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132292008L, 1132292008L);
                crLinkLinkList.add(thirdToForth);
                linkLinkCount ++;
            }

            //a--b2
            if (deviceJson.get("b2Id") != null) {
                String aToB2Id = otn.getId(linkCount);
                JSONObject aToB2 = otn.createLinkObj(aToB2Id, "a-b2", "a-b2", 1132292008L,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("aId"), null,
                        deviceJson.getString("b2Id"), null);
                cmLinkList.add(aToB2);
                linkCount ++;

                //b2--terminal
                String b2ToTerminalId = otn.getId(linkCount);
                JSONObject b2ToTerminal = otn.createLinkObj(b2ToTerminalId, "b2-asbr", "b2-asbr", 1132292008L,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("b2Id"), null,
                        deviceJson.getString("terminalId"), portJson.getString("terminalPortId"));
                cmLinkList.add(b2ToTerminal);
                linkCount ++;

                //1级--2级
                String firstToSecendId = otn.getId(linkLinkCount);
                JSONObject firstToSecend = otn.createLinkLinkObj(firstToSecendId, 1130311310000L, sourceToTerminalId, b2ToTerminalId,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132100021L, 1132292008L);
                crLinkLinkList.add(firstToSecend);
                linkLinkCount ++;

                //2级--3级
                String secendToThirdId = otn.getId(linkLinkCount);
                JSONObject secendToThird = otn.createLinkLinkObj(secendToThirdId, 1130311310000L, b2ToTerminalId, aToB2Id,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132292008L, 1132292008L);
                crLinkLinkList.add(secendToThird);
                linkLinkCount ++;

                //3级--4级
                String thirdToForthId = otn.getId(linkLinkCount);
                JSONObject thirdToForth = otn.createLinkLinkObj(thirdToForthId, 1130311310000L, aToB2Id, sourceToAId,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132292008L, 1132292008L);
                crLinkLinkList.add(thirdToForth);
                linkLinkCount ++;
            }

        } else {
            //无A设备
            //source--b1
            if (deviceJson.get("b1Id") != null) {
                String sourceToB1Id = otn.getId(linkCount);
                JSONObject sourceToB1 = otn.createLinkObj(sourceToB1Id, "source-b1", "source-b1", 1132292008L,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("sourceId"),
                        portJson.getString("sourcePortId"), deviceJson.getString("b1Id"), null);
                cmLinkList.add(sourceToB1);
                linkCount ++;

                //b1--terminal
                String b1ToTerminalId = otn.getId(linkCount);
                JSONObject b1ToTerminal = otn.createLinkObj(b1ToTerminalId, "b1-asbr", "b1-asbr", 1132292008L,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("b1Id"), null,
                        deviceJson.getString("terminalId"), portJson.getString("terminalPortId"));
                cmLinkList.add(b1ToTerminal);
                linkCount ++;

                //1级--2级
                String firstToSecendId = otn.getId(linkLinkCount);
                JSONObject firstToSecend = otn.createLinkLinkObj(firstToSecendId, 1130311310000L, sourceToTerminalId, b1ToTerminalId,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132100021L, 1132292008L);
                crLinkLinkList.add(firstToSecend);
                linkLinkCount ++;

                //2级--3级
                String secendToThirdId = otn.getId(linkLinkCount);
                JSONObject secendToThird = otn.createLinkLinkObj(secendToThirdId, 1130311310000L, b1ToTerminalId, sourceToB1Id,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132292008L, 1132292008L);
                crLinkLinkList.add(secendToThird);
                linkLinkCount ++;
            }

            //source--b2
            if (deviceJson.get("b2Id") != null) {
                String sourceToB2Id = otn.getId(linkCount);
                JSONObject sourceToB2 = otn.createLinkObj(sourceToB2Id, "source-b2", "source-b2", 1132292008L,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("sourceId"),
                        portJson.getString("sourcePortId"), deviceJson.getString("b2Id"), null);
                cmLinkList.add(sourceToB2);
                linkCount ++;

                //b2--terminal
                String b2ToTerminalId = otn.getId(linkCount);
                JSONObject b2ToTerminal = otn.createLinkObj(b2ToTerminalId, "b2-asbr", "b2-asbr", 1132292008L,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, deviceJson.getString("b2Id"), null,
                        deviceJson.getString("terminalId"), portJson.getString("terminalPortId"));
                cmLinkList.add(b2ToTerminal);
                linkCount ++;

                //1级--2级
                String firstToSecendId = otn.getId(linkLinkCount);
                JSONObject firstToSecend = otn.createLinkLinkObj(firstToSecendId, 1130311310000L, sourceToTerminalId, b2ToTerminalId,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132100021L, 1132292008L);
                crLinkLinkList.add(firstToSecend);
                linkLinkCount ++;

                //2级--3级
                String secendToThirdId = otn.getId(linkLinkCount);
                JSONObject secendToThird = otn.createLinkLinkObj(secendToThirdId, 1130311310000L, b2ToTerminalId, sourceToB2Id,
                        NRMConstants.AREA_NAME_TO_ID_MAP.get(areaName), circuitCode, 1132292008L, 1132292008L);
                crLinkLinkList.add(secendToThird);
                linkLinkCount ++;
            }
        }

        result.put("cmLinkList", cmLinkList);
        result.put("crLinkLinkList", crLinkLinkList);
        return result;
    }


}
