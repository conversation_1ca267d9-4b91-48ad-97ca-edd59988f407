package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.ProductDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.ResTreeParamDto;
import com.telecom.nrm.domain.WhereBody;
import com.telecom.nrm.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

    @Autowired
    ProductDao productDao;

    @Override
    public PageResponse<JSONObject> accessCodeProdQuery(JSONObject jsonObject, String ds) {
        return productDao.accessCodeProdQuery(jsonObject,ds);
    }

    public JSONObject queryProdByCircuit(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("circuitId".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitId", body.getPropertyValue());
            } else if ("circuitCode".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitCode", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  productDao.queryProdByCircuit(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("productSpecId", obj.getString("product_spec_id"));
                node.put("productSpec", obj.getString("product_spec"));
                node.put("accessCode", obj.getString("access_code"));
                node.put("crmProductId", obj.getString("crm_product_id"));
                node.put("processState", obj.getString("prod_process_state"));
                node.put("rate", obj.getString("rate"));
                node.put("crmOrderCode", obj.getString("crm_order_code"));
                node.put("crmOrderState", obj.getString("crm_order_state"));
                node.put("archiveDate", obj.getString("archive_date"));
                node.put("circuitName", obj.getString("circuit_name"));
                node.put("circuitCode", obj.getString("circuit_code"));
                node.put("custName", obj.getString("a_customer_name"));
                //regionType
                node.put("aAreaId", obj.getString("a_area_id"));
                node.put("aAreaName", obj.getString("a_area_name"));
                node.put("aAdderssId", obj.getString("a_address_id"));
                node.put("aAdderssName", obj.getString("a_address_name"));
                node.put("zAreaId", obj.getString("z_area_id"));
                node.put("zAreaName", obj.getString("z_area_name"));
                node.put("zAdderssId", obj.getString("z_address_id"));
                node.put("zAdderssName", obj.getString("z_address_name"));
                node.put("memo", obj.getString("notes"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }
}
