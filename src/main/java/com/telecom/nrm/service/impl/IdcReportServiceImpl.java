package com.telecom.nrm.service.impl;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.layout.font.FontProvider;
import com.telecom.nrm.controller.IdcAuditController;
import com.telecom.nrm.domain.report.IReport;
import com.telecom.nrm.domain.report.Item;
import com.telecom.nrm.domain.report.PortImprovement;
import com.telecom.nrm.domain.report.RiskItem;
import com.telecom.nrm.service.IdcReportService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.SneakyThrows;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
public class IdcReportServiceImpl implements IdcReportService {

    public static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddhhmmss");

    @SneakyThrows
    @Override
    public String generateWeekendReport(IReport iReport, File file, String templateName) {
        ConverterProperties props = new ConverterProperties();
        FontProvider fontProvider = new FontProvider();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        // 应用本地字体,微软雅黑是ttc 不好处理
        Resource[] fontResources = resolver.getResources("classpath*:fonts/*.ttf");
        for (int i = 0; i < fontResources.length; i++) {
            InputStream inputStream = fontResources[i].getInputStream();
            File tempFile = File.createTempFile("temp_" ,  fontResources[i].getFilename() );
            Files.copy(inputStream, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            fontProvider.addFont(fileToBytes(tempFile));
            tempFile.delete();
            inputStream.close();
        }
        props.setFontProvider(fontProvider);
        String html = generateHtml(iReport, templateName);

        try (FileOutputStream fos = new FileOutputStream(file)) {
            HtmlConverter.convertToPdf(html, fos, props);
        }
        return html;
    }


    @SneakyThrows
    private String generateHtml(IReport iReport, String templateName) {
        // 获取配置
        Configuration cfg = new Configuration();
        cfg.setClassForTemplateLoading(IdcAuditController.class, "/templates");
        Template template = cfg.getTemplate(templateName);
        Writer out = new StringWriter();
        template.process(iReport, out);
        String result =  out.toString();
        return result;
    }

    /**
     * 样例
     * @return
     */
    @Override
    public File demo(){
        /*IReport report = new IReport();
        report.setFileName(simpleDateFormat.format(new Date())+".pdf"); // 报告文件名
        report.setDetailFileName("aa.xlsx"); // 详单文件名
        report.setStartDate(new Date()); // 报告开始时间
        report.setEndDate(new Date());// 报告截至时间
        report.getCurrentCycle().getPortAudit().setTotal(16); // 本周期内端口操作总数
        report.getCurrentCycle().getPortAudit().setRisk(8); // 本周期内端口风险操作总数
        report.getCurrentCycle().getPortAudit().setCitys("南通、苏州、无锡"); // 涉及地市
        // 添加操作记录列表信息。 按单操作的无需添加建议样例等
        report.getCurrentCycle().getPortAudit().addRiskCatalog(
                RiskItem.builder().title("按单操作")
                        .count(8)
                        .percent("50%")
                        .risk(false)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("无工单关闭中继端口")
                        .count(4)
                        .percent("25%")
                        .description("检测到当日对中继端口关闭的命令，该端口在IDC资源系统的状态为\"中继\"，未匹配中继工单。")
                        .checkRule("无工单施工")
                        .suggestion("存在未按流程操作风险，系统建议补录工单或现场核查使用情况。")
                        .exampleOperateTime(new Date())
                        .exampleDeviceIp("**************")
                        .examplePortName("GigabitEthernet12/0/1")
                        .exampleAction("关闭端口")
                        .risk(true)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("无工单关闭空闲端口")
                        .count(2)
                        .percent("12.5%")
                        .description("检测到当日对网络设备的端口关闭的命令，该端口在IDC资源系统的状态为\"空闲\"，未匹配拆机工单。")
                        .checkRule("无工单施工")
                        .suggestion("存在未按流程操作风险，系统建议补录工单或现场核查使用情况。")
                        .exampleOperateTime(new Date())
                        .exampleDeviceIp("************")
                        .examplePortName("Ten-GigabitEthernet5/0/1")
                        .exampleAction("关闭端口")
                        .risk(true)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("关闭未纳管端口")
                        .count(1)
                        .percent("6.25%")
                        .description("根据端口名称在IDC资源系统未查询到端口实例。")
                        .checkRule("无工单施工")
                        .suggestion("端口未纳管，系统建议核实IDC资源详情，缺失请补录端口。")
                        .exampleOperateTime(new Date())
                        .exampleDeviceIp("************")
                        .examplePortName("TenGigabitEthernet5001")
                        .exampleAction("关闭端口")
                        .risk(true)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("打开未纳管端口").
                        count(1)
                        .percent("6.25%")
                        .description("端口未纳管，系统建议核实IDC资源详情，缺失请补录端口。")
                        .checkRule("无工单施工")
                        .suggestion("端口未纳管，系统建议核实IDC资源详情，缺失请补录端口。")
                        .exampleOperateTime(new Date())
                        .exampleDeviceIp("*************")
                        .examplePortName("GigabitEthernet1/0/2")
                        .exampleAction("打开端口")
                        .risk(true)
                        .build()
        );
        // 流量
        report.getCurrentCycle().getPortTraffic().setExceptFreeTotal(200); // 本周期流量空闲总数
        report.getCurrentCycle().getPortTraffic().setLaskweekFreeTotal(0);// 上周量空闲总数
        report.getCurrentCycle().getPortTraffic().setCurrentAdd(200); // 本周新增
        report.getCurrentCycle().getPortTraffic().setCurrentNormal(0); // 本周恢复

        // 速率
        report.getCurrentCycle().getPortRate().setExceptTotal(200); // 异常总数
        report.getCurrentCycle().getPortRate().setLaskweekExceptTotal(0); // 上周异常总数
        report.getCurrentCycle().getPortRate().setExceptUpperTotal(100); // 超过
        report.getCurrentCycle().getPortRate().setExceptLowerTotal(100); // 不足
        report.getCurrentCycle().getPortRate().setCurrentAdd(200);// 本周新增
        report.getCurrentCycle().getPortRate().setCurrentNormal(0);// 本周恢复

        *//*************上线以来*************//*
        report.getTotalCycle().setStartDate(new Date()); // 开始时间
        report.getTotalCycle().setEndDate(new Date()); // 截至时间
        report.getTotalCycle().getPortAudit().setTotal(131); // 端口操作总数
        report.getTotalCycle().getPortAudit().setRisk(26); // 端口操作风险速
        report.getTotalCycle().getPortAudit().setCitys("南通、苏州、无锡"); // 涉及地市
        // 添加风险分布情况
        report.getTotalCycle().getPortAudit().addRiskCatalog(
                RiskItem.builder().title("按单操作")
                        .count(105)
                        .percent("80.15%")
                        .risk(false)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("无工单开启空闲端口")
                        .count(1)
                        .percent("0.76%")
                        .risk(true)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("无工单关闭中继端口")
                        .count(13)
                        .percent("9.92%")
                        .risk(true)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("无工单关闭空闲端口")
                        .count(2)
                        .percent("1.53%")
                        .risk(true)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("关闭未纳管端口")
                        .count(2)
                        .percent("1.53%")
                        .risk(true)
                        .build()
        ).addRiskCatalog(
                RiskItem.builder()
                        .title("打开未纳管端口").
                        count(5)
                        .percent("3.82%")
                        .risk(true)
                        .build()
        );
        // 添加端口操作整治情况
        report.getTotalCycle().getPortAudit().addPortImprovement(
                PortImprovement.builder().city("南京").riskCount(4).dispatch(4).complete(0).build()
        ).addPortImprovement(
                PortImprovement.builder().city("南通").riskCount(11).dispatch(11).complete(0).build()
        ).addPortImprovement(
                PortImprovement.builder().city("无锡").riskCount(1).dispatch(1).complete(0).build()
        ).statisticImprovements(); // 最后添加合计

        report.getTotalCycle().getPortRate().setExceptTotal(500); // 速率总数
        report.getTotalCycle().getPortRate().setCurrentEexcept(300); // 速率当前还异常

        report.getTotalCycle().getPortTraffic().setExceptFreeTotal(500);// 流量总数 空闲
        report.getTotalCycle().getPortTraffic().setCurrentFreeEexcept(400); // 流量当前还异常 空闲

        return this.generateWeekendReport(report);*/
        return null;
    }

    public  static byte[] fileToBytes(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toByteArray();
        }
    }

}
