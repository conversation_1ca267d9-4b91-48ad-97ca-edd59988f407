package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.domain.topo.TopoMode;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OptRoadServiceImpl implements OptRoadService {

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    CommonCircuitService commonCircuitService;

    @Autowired
    IpranService ipranService;


    @Override
    public JSONObject queryOPTRoute(JSONObject request) {
        JSONObject cacheQuery =new JSONObject();
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");
        cacheQuery.put("circuit_code",code);
        cacheQuery.put("area_code",areaCode);
        JSONObject cacheResult = ipranService.queryCache(cacheQuery);
        if (null != cacheResult) return cacheResult;

        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_opt_road").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("opt_road","code",code))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject  graphResult = graphToTOPO(graph);
        ipranService.saveCache(graphResult,code,areaCode,code);
        return graphResult;
    }

    @Override
    public JSONObject optRoadGis(JSONObject request) {
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");
        GraphRequestBuilder builder = new GraphRequestBuilder();
        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequest graphRequest = builder.setApiId("query_opt_roads_2_pipesegment").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("opt_road","code",code))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);
        graphService.buildReverseTree(data, "光路", "管道段", "opt_road_list");
        graphService.buildReverseTree(data, "光缆段", "管道段", "cable_sect_list");

        List<JSONObject> pipeSegmentList = graphService.filterNodeByLabel(data,"管道段");
        JSONObject result = new JSONObject();
        result.put("pipe_segments",pipeSegmentList);
        List<JSONObject> deviceList = graphService.filterNodeByLabel(data, "设备");
        if (CollectionUtils.isNotEmpty(pipeSegmentList) && CollectionUtils.isNotEmpty(deviceList)) {
            Map<String, JSONObject> deviceMap = deviceList.stream()
                    .collect(Collectors.toMap(obj -> obj.getString("id"), Function.identity(), (e1, e2) -> e1));
//
//            JSONObject jsonObject = pipeSegmentList.stream()
//                    .map(e -> e.getJSONArray("opt_road_list").getJSONObject(0))
//                    .map(road -> {
//                        JSONObject object = new JSONObject();
//                        fillDeviceInfo(object, "a", road.getString("a_physic_device_id"), deviceMap);
//                        fillDeviceInfo(object, "z", road.getString("z_physic_device_id"), deviceMap);
//                        return object;
//                    })
//                    .findFirst()
//                    .orElse(new JSONObject());
//            result.put("road_both_point_devices_xy", jsonObject);

            JSONObject jsonObject = pipeSegmentList.stream()
                    .filter(e -> e != null && e.getJSONArray("opt_road_list") != null && e.getJSONArray("opt_road_list").size() > 0)
                    .map(e -> e.getJSONArray("opt_road_list").getJSONObject(0))
                    .filter(road -> road != null && road.containsKey("a_physic_device_id") && road.containsKey("z_physic_device_id"))
                    .map(road -> {
                        JSONObject object = new JSONObject();
                        if (road.containsKey("a_physic_device_id")) {
                            fillDeviceInfo(object, "a", road.getString("a_physic_device_id"), deviceMap);
                        }
                        if (road.containsKey("z_physic_device_id")) {
                            fillDeviceInfo(object, "z", road.getString("z_physic_device_id"), deviceMap);
                        }
                        return object;
                    })
                    .findFirst()
                    .orElse(new JSONObject());

            result.put("road_both_point_devices_xy", jsonObject);
        }
        return result;
    }
     void fillDeviceInfo(JSONObject jsonObject, String prefix, String deviceId, Map<String, JSONObject> deviceMap) {
        JSONObject device = deviceMap.get(deviceId);
        if (device != null) {
            jsonObject.put(prefix + "_id", device.getString("id"));
            jsonObject.put(prefix + "_spec_id", device.getString("spec_id"));
            jsonObject.put(prefix + "_spec_name", device.getString("spec_name"));
            jsonObject.put(prefix + "_pos_y", device.getString("pos_y"));
            jsonObject.put(prefix + "_pos_x", device.getString("pos_x"));
            jsonObject.put(prefix + "_name", device.getString("name"));
        }
    }

    void updateLayer(List<JSONObject> list, Integer level) {
        for (JSONObject l: list) {
            l.put("layer", level);
        }
    }
    JSONObject specMap = new JSONObject(){{
        put("1131100001","硬跳");
        put("1131100003","局向光纤");
        put("1121000002","光缆段");
        put("1132400006","光路");
        put("1121100002","纤芯");
    }
    };
    void repairLink(List<JSONObject> list) {
        for (JSONObject l: list) {
            if (ObjectUtil.isNotEmpty(l.get("children"))) {
                @SuppressWarnings("unchecked")
                List<JSONObject> children = (List<JSONObject>) l.get("children");
                children.stream().filter(d->ObjectUtil.isEmpty(d.getString("a_physic_device_id"))).forEach(d->{
                    d.put("a_physic_device_id","0");
                });
                children.stream().filter(d->ObjectUtil.isEmpty(d.getString("z_physic_device_id"))).forEach(d->{
                    d.put("z_physic_device_id","9999");
                });
                children.removeIf(d->d.getString("a_physic_device_id").equals(d.getString("z_physic_device_id")));
                l.put("children", children);
                l.put("children_num", children.size());
            }else{
                l.put("children_num",0);
            }

            String specId = l.getString("spec_id");
            String specName = specMap.getString(specId);
            l.put("spec_name",specName);
        }
    }

    public JSONObject graphToTOPO (Graph graph) {

        JSONObject graphData = graphService.getData(graph);

        List<JSONObject> optRoadList = graphService.filterNodeByLabel(graphData,"光路");
        updateLayer(optRoadList,0);
        List<JSONObject> jxgxList = graphService.filterNodeByLabel(graphData,"局向光纤");
        updateLayer(jxgxList,1);
        List<JSONObject> cableSegmentList = graphService.filterNodeByLabel(graphData,"纤芯");
        updateLayer(cableSegmentList,2);




        List<JSONObject> deviceList = graphService.filterNodeByLabel(graphData,"设备");


        graphService.buildTree(graphData,"光路","局向光纤","children");
        graphService.buildTree(graphData, "局向光纤","纤芯", "children");

        repairLink(optRoadList);
        repairLink(jxgxList);
        repairLink(cableSegmentList);

        commonCircuitService.sortCircuitTopology(deviceList, optRoadList, TopoMode.NET, "");


        JSONObject result = new JSONObject();
        result.put("links", optRoadList);
        result.put("nodes",deviceList);

        return result;
    }
}
