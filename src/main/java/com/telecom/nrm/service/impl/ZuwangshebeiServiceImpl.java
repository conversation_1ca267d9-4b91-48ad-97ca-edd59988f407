package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.controller.GraphSceneController;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.dao.ZuwangshebeiDao;
import com.telecom.nrm.dao.ZuwangshebeiDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.telecom.nrm.service.impl.OptViewServiceImpl.threadLocal;

@Service
public class ZuwangshebeiServiceImpl implements ZuwangshebeiService {
    //画GIS图
    @Autowired
    GraphService graphService;

    private final Logger log = LoggerFactory.getLogger(GraphSceneController.class);

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    //图API服务
    @Autowired
    GraphApiService graphApiService;

    //调用组网设备Dao文件
    @Autowired
    ZuwangshebeiDao zuwangshebeiDao;

    //调用光路对service
    @Autowired
    OptRoadPairService optRoadPairService;

    @Autowired
    OptGroupService optGroupService;

    @Autowired
    OptGroupDao optGroupDao;
    @Autowired
    OptViewService optViewService;


    //光路对分析
    public JSONObject opt_pair_analyze(List<JSONObject> zuwangshebei, JSONObject request){
        List<String> codes = (List<String>) request.getJSONArray("codes").stream().map(Object::toString).collect(Collectors.toList());
        String ds = request.getString("ds");
        List<JSONObject> optRoadGroupMembers = new ArrayList<>();
        int routeNo=1;
        for (String code : codes) {
            JSONObject optRoadGroupMember = new JSONObject();
            optRoadGroupMember.put("code", code);
            optRoadGroupMember.put("route_no", routeNo);
            optRoadGroupMembers.add(optRoadGroupMember);
        }
        JSONObject result =optRoadPairService.opt_pair_analyse(optRoadGroupMembers,ds);
        result.put("ds", request.getString("ds"));

        JSONObject need_save = new JSONObject();
        need_save.put("object_id", zuwangshebei.get(0).getString("group_id"));
        need_save.put("object_type", "optGroup");
        need_save.put("check_result",result.getString("check_result"));
        log.info("need_save"+need_save);
        //需要更改pm_risk_check_insert为pm_risk_check_insert_zuwang
        zuwangshebeiDao.pm_risk_check_insert(need_save, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        return result;
    }

    //组网设备隐患分析
    public JSONObject risk_analyze(JSONObject request) {
        request.put("group_id", request.getString("id"));
        List<JSONObject> zuwangshebei =  zuwangshebeiDao.pm_opt_road_group_element_query(request , NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        JSONObject result=new JSONObject();
        log.info("zuwangshebei:"+zuwangshebei);
        if(ObjectUtil.isNotEmpty(zuwangshebei)) {
            result=  opt_pair_analyze(zuwangshebei, request);
        }
        return result;
    }

    @Override
    public JSONObject queryDoubleRouteByDeviceId(JSONObject jsonObject) {
        JSONObject result = new JSONObject();
        //解析devices
        List<Integer> devices = (List<Integer>) jsonObject.getJSONArray("devices").stream().map(Object::toString).collect(Collectors.toList());
        if (devices.size() != 2) {
            result.put("error", "设备数量不为2");
            return result;
        }
        JSONObject param = new JSONObject();
        param.put("beginNodeId", devices.get(0));
        param.put("endNodeId", devices.get(1));
        param.put("num",5);
        List<JSONObject> paths =  optGroupDao.query_path_scheme_by_az(param, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if (paths==null || paths.isEmpty()) {
            result.put("error", "未找到方案");
            return result;
        }
        //jsonPlanList 按照path_id 进行分组
        List<List<JSONObject>> jsonPlanGroupList = new ArrayList<>(paths.stream().collect(Collectors.groupingBy(jsonPlan -> jsonPlan.getInteger("path_id"))).values());
        //成双路由的数据
        List<JSONObject> doubleRouteList = new ArrayList<>();
        result.put("doubleRouteList", doubleRouteList);
        try{
            threadLocal.set(new HashMap<>());
            //两两对比
            for (int i = 0; i < jsonPlanGroupList.size(); i++) {
                List<JSONObject> jsonPlanGroup = jsonPlanGroupList.get(i);
                for (int j = i+1; j < jsonPlanGroupList.size(); j++) {
                    List<JSONObject> jsonPlanGroup2 = jsonPlanGroupList.get(j);
                    List<String> plan1 = jsonPlanGroup.stream().filter(jsonPlan -> jsonPlan.containsKey("cable_code")).map(jsonPlan -> jsonPlan.getString("cable_code")).collect(Collectors.toList());
                    List<String> plan2 = jsonPlanGroup2.stream().filter(jsonPlan -> jsonPlan.containsKey("cable_code")).map(jsonPlan -> jsonPlan.getString("cable_code")).collect(Collectors.toList());
                    JSONObject checkResult = checkDoubleRoute(plan1, plan2, jsonObject.getString("ds"));
                    if (!checkResult.getBoolean("isDoubleRoute")) {
                        continue;
                    }
                    JSONObject doubleRoute = new JSONObject();
                    doubleRoute.put("plan1", plan1);
                    doubleRoute.put("plan2", plan2);
                    doubleRoute.put("samePipeSegments", checkResult.getJSONArray("samePipeSegments"));
                    doubleRouteList.add(doubleRoute);
                }
            }
        }catch (Exception e){
            log.error("queryDoubleRouteByDeviceId error",e);
            result.put("error", "查询异常");
        }finally {
            threadLocal.remove();
        }
        return result;
    }

    @Override
    public JSONObject queryDoubleRouteByCableCode(JSONObject jsonObject) {
        return null;
    }

    public JSONObject pm_opt_road_group_query_zuwang_by_f_number(JSONObject request) {
        List<String> codes = (List<String>) request.getJSONArray("codes").stream().map(Object::toString).collect(Collectors.toList());
        String ds = request.getString("ds");

        List<JSONObject> optRoadGroupMembers = new ArrayList<>();
        int routeNo=1;
        for (String code : codes) {
            JSONObject optRoadGroupMember = new JSONObject();
            optRoadGroupMember.put("code", code);
            //optRoadGroupMember.put("route_no", routeNo);
            optRoadGroupMembers.add(optRoadGroupMember);
        }

        JSONObject result =optRoadPairService.opt_pair_analyse(optRoadGroupMembers,ds);
        if ("单路由".equals(result.getString("check_result"))) {
            result = optRoadPairService.queryOptRoadByFNumber(codes.get(0),ds);
        }

        JSONObject saveParam = new JSONObject();
        saveParam.put("staff_number", request.getString("staff_number"));
        saveParam.put("name", "双路由整改方案智能输出");
        saveParam.put("in_parameter", request.toJSONString());
        saveParam.put("result", result.getString("check_result"));
        optGroupDao.fiber_opt_operation_log(saveParam, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        log.info("result:{}",result);
        return result;
    }

    @Override
    public Integer pair_circuit(JSONObject jsonObject) {
        //获得页面数据
        //根据code查询是否存在过
        //插入group数据
        //插入element数据
        JSONArray fs = jsonObject.getJSONArray("fs");
        List<LinkedHashMap> fsJavaList = fs.toJavaList(LinkedHashMap.class);
        List<String> codes = fsJavaList.stream().map(f->f.get("code").toString()).collect(Collectors.toList());
        //排序
        Collections.sort(codes);
        //拼接
        String code = String.join("-",codes);
        String shardingCode= NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"));
        JSONObject queryParame = new JSONObject();
        queryParame.put("code",code);
        PageResponse<JSONObject> pageResponse = optGroupDao.pm_opt_road_group_query(queryParame,1,1, shardingCode );
        //存在数据
        if (!pageResponse.getData().isEmpty()) {
            return 0;
        }
        //name,code,create_op,area_name,source_type_id,state_id
        queryParame = new JSONObject();
        queryParame.put("code",code);
        queryParame.put("name",jsonObject.getString("name"));
        queryParame.put("create_op",jsonObject.getString("create_op"));
        queryParame.put("area_name",jsonObject.getString("ds"));
        queryParame.put("source_type_id",3);
        queryParame.put("state_id",1);
        queryParame.put("speciality",jsonObject.getString("speciality"));
        log.info("queryParame:{}",queryParame);
        optGroupDao.pm_opt_road_group_insert_v2(queryParame, shardingCode);
        //查询刚插入的数据
        pageResponse = optGroupDao.pm_opt_road_group_query(queryParame,1,1, shardingCode );
        Integer insertNum = pageResponse.getData().get(0).getInteger("id");
        List<JSONObject> insertList = new ArrayList<>();
        for (LinkedHashMap f: fsJavaList) {
            JSONObject insert = new JSONObject();
            insert.put("group_id",insertNum);
            insert.put("code",f.get("code"));
            insert.put("name",f.get("name"));
            insertList.add(insert);
        }
        optGroupDao.pm_opt_road_group_element_insert(insertList, shardingCode);
        return 1;
    }

    @Override
    public JSONObject queryDoubleRouteByFNumber(JSONObject jsonObject) {
        //1、通过F号查询两端设备
        //2、通过两端设备查询方案
        //3、判断方案是否符合双路由
        JSONObject result = new JSONObject();
        List<JSONObject> devices = optGroupDao.pm_opt_road_group_element_query_by_code(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(null == devices || devices.size() != 2){
            devices = optGroupDao.pm_opt_road_group_element_query_by_code_v2(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        }
        if (null==devices || devices.size() != 2) {
            result.put("error", "未找到设备数据");
            return result;
        }
        List<JSONObject> planList = new ArrayList<>();
        result.put("planList", planList);
        //放入设备数据
        result.put("devices", devices.stream().map(item -> item.getString("node_name")).collect(Collectors.toList()));
        List<String> plan1 = (List<String>) jsonObject.getJSONArray("opts").stream().map(Object::toString).collect(Collectors.toList());

        log.info("devices:{}",devices);
        JSONObject param = new JSONObject();
        param.put("beginNodeId", devices.get(0).getInteger("node_id"));
        param.put("endNodeId", devices.get(1).getInteger("node_id"));
        param.put("num",5);
        List<JSONObject> paths =  optGroupDao.query_path_scheme_by_az(param, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        log.info("paths:{}",paths);
        if (paths==null || paths.isEmpty()) {
            result.put("error", "局内直连光路");
            return result;
        }
        //jsonPlanList 按照path_id 进行分组
        List<List<JSONObject>> jsonPlanGroupList = paths.stream().collect(Collectors.groupingBy(jsonPlan -> jsonPlan.getInteger("path_id"))).values().stream().collect(Collectors.toList());

        try{
            result.put("isDoubleRoute",false);
            //设置线程变量 优化重复请求
            threadLocal.set(new HashMap<>());
            //查询plan1信息
            JSONObject plan1JsonObj = optViewService.queryOptLinkInfo(plan1, jsonObject.getString("ds"));
            //放入光缆
            result.put("cables", plan1JsonObj.getJSONArray("cables"));
            for (int i = 0; i < jsonPlanGroupList.size(); i++) {
                List<JSONObject> jsonPlanGroup = jsonPlanGroupList.get(i);
                //查找存在cable_code 字段的数据 并获得集合
                List<String> plan2 = jsonPlanGroup.stream().filter(jsonPlan -> jsonPlan.containsKey("cable_code")).map(jsonPlan -> jsonPlan.getString("cable_code")).collect(Collectors.toList());
                JSONObject checkResult = checkDoubleRoute(plan1, plan2, jsonObject.getString("ds"));
                log.info("checkResult:{}",checkResult);
                //if (checkResult.getBoolean("isDoubleRoute")) {
                //    return checkResult;
                //}
                //添加经过的设备
                checkResult.put("devices", jsonPlanGroup.stream().map(jsonPlan -> jsonPlan.getString("node_name")).collect(Collectors.toList()));
                planList.add(checkResult);
            }
        }catch (Exception e){
            log.error("queryDoubleRouteByFNumber error",e);
        }finally {
            threadLocal.remove();
        }
        return result;
    }

    /**
     * 判断两条光缆集合是否符合双路由
     * @param plan1 光缆集合1
     * @param plan2 光缆集合2
     * @param ds
     * @return
     */
    public JSONObject checkDoubleRoute(List<String> plan1, List<String> plan2, String ds) {
        return optViewService.checkDoubleRouteV2(plan1, plan2, ds);
    }

    //光路对统计字典
    public JSONObject optPair_statistic_dictionary(JSONObject jsonObject) {
        JSONObject result = new JSONObject();
        jsonObject.put("object_type", "optGroup");
        jsonObject.put("result",null);
        result.put("totalCount", zuwangshebeiDao.pm_opt_road_group_state_statistic_query_zuwang(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","同管道");
        result.put("samePipeSementCount", zuwangshebeiDao.pm_opt_road_group_state_statistic_query_zuwang(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","同光缆");
        result.put("sameCableCount", zuwangshebeiDao.pm_opt_road_group_state_statistic_query_zuwang(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","正常");
        result.put("normal", zuwangshebeiDao.pm_opt_road_group_state_statistic_query_zuwang(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","已忽略异常");
        result.put("ignoreCount", zuwangshebeiDao.pm_opt_road_group_state_statistic_query_zuwang(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","已整改");
        result.put("checkNormalCount", zuwangshebeiDao.pm_opt_road_group_state_statistic_query_zuwang(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        return result;
    }


}
