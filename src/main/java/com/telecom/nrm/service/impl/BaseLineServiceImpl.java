package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CableSectionDao;
import com.telecom.nrm.dao.CustomerDao;
import com.telecom.nrm.dao.DeviceDao;
import com.telecom.nrm.domain.*;
import com.telecom.nrm.service.BaseLineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class BaseLineServiceImpl implements BaseLineService {

    static final int pageSize = 2000;
    static final int currentPage = 1;

    @Autowired
    DeviceDao deviceDao;

    @Autowired
    CableSectionDao cableSectionDao;

    @Override
    public ResponseData getBusinessCircuitByDevice(ResTreeParamDto resTreeParamDto) throws Exception {
        int pageSize =resTreeParamDto.getPageSize();
        int pageNumber =resTreeParamDto.getPageNum();
        ResponseData responseData =new ResponseData();
        responseData.setPageNum(resTreeParamDto.getPageNum());
        responseData.setPageSize(resTreeParamDto.getPageSize());

        List<Row> rowList = null;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pageSize", BaseLineServiceImpl.pageSize);
        jsonObject.put("currentPage", BaseLineServiceImpl.currentPage);
        String ppmCode = getPpmCodeAndSetParam(resTreeParamDto, jsonObject);
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (null == shardingEnum) throw new RuntimeException("regionId 错误,请送PPMCODE");
        PageResponse<Row> response = deviceDao.graph_query_link_by_device_port_ware_id(jsonObject, shardingEnum.getBcShardingCode());
        if (null != response && CollectionUtils.isNotEmpty(response.getData())) {
            int totalCount = response.getPageInfo().getTotalCount();
            int pageCount = (totalCount + pageSize - 1) / pageSize;
            responseData.setPageCount(Math.max(0, pageCount));
            rowList = response.getData().stream().distinct().collect(Collectors.toList());
            Set<String> deviceIds = rowList.stream()
                    .flatMap(row -> Stream.of(
                            Optional.ofNullable(row.getAphysicDeviceId()),
                            Optional.ofNullable(row.getZphysicDeviceId())
                    ).filter(Optional::isPresent).map(Optional::get))
                    .collect(Collectors.toSet());

            Set<String> portIds = rowList.stream()
                    .flatMap(row -> Stream.of(
                            Optional.ofNullable(row.getAportId()),
                            Optional.ofNullable(row.getZportId())
                    ).filter(Optional::isPresent).map(Optional::get))
                    .collect(Collectors.toSet());
            jsonObject.put("devicesIds", deviceIds);
            jsonObject.put("portIds", portIds);
            JSONObject deviceAndPorts = deviceDao.graph_query_devices_ports_by_ids(jsonObject, shardingEnum.getBcShardingCode());
            if (null != deviceAndPorts) {
                if (CollectionUtils.isNotEmpty(deviceAndPorts.getJSONArray("devices"))) {
                    Map<String, Row> a_deviceMap = rowList.stream().collect(Collectors.toMap(Row::getAphysicDeviceId, Function.identity(), (e1, e2) -> e2));
                    Map<String, Row> z_deviceMap = rowList.stream().collect(Collectors.toMap(Row::getZphysicDeviceId, Function.identity(), (e1, e2) -> e2));
                    JSONArray devices = deviceAndPorts.getJSONArray("devices");
                    for (Object device : devices) {
                        JSONObject jsonObject1 = (JSONObject) device;
                        if (a_deviceMap.containsKey(jsonObject1.getString("id"))) {
                            Row row = a_deviceMap.get(jsonObject1.getString("id"));
                            row.setAphysicDeviceCode(jsonObject1.getString("code"));
                            row.setAphysicDeviceSpec(jsonObject1.getString("specName"));
                            row.setAphysicDeviceName(jsonObject1.getString("name"));
                        }
                        if (z_deviceMap.containsKey(jsonObject1.getString("id"))) {
                            Row row = z_deviceMap.get(jsonObject1.getString("id"));
                            row.setZphysicDeviceCode(jsonObject1.getString("code"));
                            row.setZphysicDeviceSpec(jsonObject1.getString("specName"));
                            row.setZphysicDeviceName(jsonObject1.getString("name"));
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(deviceAndPorts.getJSONArray("ports"))) {
                    Map<String, Row> a_portMap = rowList.stream().collect(Collectors.toMap(Row::getAportId, Function.identity(), (e1, e2) -> e2));
                    Map<String, Row> z_portMap = rowList.stream().collect(Collectors.toMap(Row::getZportId, Function.identity(), (e1, e2) -> e2));
                    JSONArray ports = deviceAndPorts.getJSONArray("ports");

                    for (Object port : ports) {
                        JSONObject jsonObject1 = (JSONObject) port;
                        if (a_portMap.containsKey(jsonObject1.getString("id"))) {
                            Row row = a_portMap.get(jsonObject1.getString("id"));
                            row.setAportCode(jsonObject1.getString("code"));
                            row.setAportSpec(jsonObject1.getString("specName"));
                        }
                        if (z_portMap.containsKey(jsonObject1.getString("id"))) {
                            Row row = z_portMap.get(jsonObject1.getString("id"));
                            row.setZportCode(jsonObject1.getString("code"));
                            row.setZportSpec(jsonObject1.getString("specName"));
                        }
                    }

                }
                rowList = paginate(rowList, pageNumber, pageSize);
                responseData.setData(rowList);
            }

        }
        if (responseData.getData() == null) {
            responseData.setCode(ResponseData.HALF_SUCCESS);
        }
        return responseData;
    }

    private String getPpmCodeAndSetParam(ResTreeParamDto resTreeParamDto, JSONObject jsonObject) {
        String ppmCode = null;
        for (WhereBody whereBody : resTreeParamDto.getWhereBody()) {
            jsonObject.put(whereBody.getPropertyLabel(), whereBody.getPropertyValue());
            if ("regionId".equals(whereBody.getPropertyLabel())) {
                ppmCode = whereBody.getPropertyValue().toString();
            }
        }
        return ppmCode;
    }

 /*     @Override
  public ResponseData getServiceCircuitByCable(ResTreeParamDto resTreeParamDto) throws Exception {
        int pageSize =resTreeParamDto.getPageSize();
        int pageNumber =resTreeParamDto.getPageNum();
        JSONObject jsonObject = new JSONObject();
        String ppmCode = getPpmCodeAndSetParam(resTreeParamDto,jsonObject);
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (null == shardingEnum) throw new RuntimeException("regionId 错误,请送PPMCODE");
        JSONObject routeJson =  cableSectionDao.graph_query_route_by_cable_id(jsonObject,shardingEnum.getBcShardingCode());
        if (null!=routeJson && CollectionUtils.isNotEmpty(routeJson.getJSONArray("route"))){
            JSONArray  routeArray = routeJson.getJSONArray("route");
            Set<String> routeIds = (Set<String>) routeArray.stream()
                    .map(e -> ((JSONObject) e).getString("id"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            jsonObject.put("linkIds",routeIds);
            JSONObject existRrServiceEntity =  cableSectionDao.graph_check_links_exist_rr_service_entity(jsonObject,shardingEnum.getBcShardingCode());
            if (null!= existRrServiceEntity && CollectionUtils.isNotEmpty(existRrServiceEntity.getJSONArray("exist"))){
                JSONArray  existLinks = routeJson.getJSONArray("exist");
                Set<String> existLinkIds = (Set<String>) existLinks.stream()
                        .map(e -> ((JSONObject) e).getString("id"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
            }else {
                jsonObject.put("lowerLinkIds",routeIds);
                JSONObject upperLink =  cableSectionDao.graph_query_upper_links_by_lower_link_ids(jsonObject,shardingEnum.getBcShardingCode());
                if (null!=upperLink && CollectionUtils.isNotEmpty(upperLink.getJSONArray("upperLinks"))){
                    JSONArray upperLinks = upperLink.getJSONArray("upperLinks");
                    Set<String> upperLinkIds = (Set<String>) upperLinks.stream()
                            .map(e -> ((JSONObject) e).getString("id"))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());

                }

            }

        }

        return null;
    }*/

    public ResponseData getServiceCircuitByCable(ResTreeParamDto resTreeParamDto) throws Exception {

        JSONObject params = new JSONObject();
        String ppmCode = getPpmCodeAndSetParam(resTreeParamDto, params);
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (null == shardingEnum) throw new RuntimeException("regionId 错误,请送PPMCODE");
        JSONObject routeResult = cableSectionDao.graph_query_route_by_cable_id(params, shardingEnum.getBcShardingCode());
        Set<String> initialLinkIds = extractLinkIds(routeResult.getJSONArray("route"));
        // 递归处理（初始深度为1,最大深度为5）
        Set<String> realLinkIds = processLinkRecursive(initialLinkIds, params, shardingEnum, new AtomicInteger(1));
        if (CollectionUtils.isNotEmpty(realLinkIds)) {
            List<WhereBody> whereBodies = resTreeParamDto.getWhereBody();
            WhereBody whereBody = new WhereBody();
            whereBody.setPropertyLabel("linkIds");
            whereBody.setPropertyValue(realLinkIds);
            whereBodies.add(whereBody);
            return getBusinessCircuitByDevice(resTreeParamDto);
        } else {
            ResponseData responseData = new ResponseData();
            responseData.setMessage("未查询到电路信息");
            return responseData;
        }

    }

    // 新增：从查询结果提取链路ID集合
    private Set<String> extractLinkIds(JSONObject routeResult) {
        if (routeResult == null || CollectionUtils.isEmpty(routeResult.getJSONArray("route"))) {
            return Collections.emptySet();
        }
        JSONArray  routeArray = routeResult.getJSONArray("route");
        Set<String> routeIds = (Set<String>) routeArray.stream()
                .map(e -> ((JSONObject) e).getString("id"))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return  routeIds;
    }

    private Set<String> extractLinkIds(JSONArray routeResult) {
        if ( CollectionUtils.isEmpty(routeResult)) {
            return Collections.emptySet();
        }
        Set<String> routeIds = (Set<String>) routeResult.stream()
                .map(e -> ((JSONObject) e).getString("id"))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return  routeIds;
    }

    private Set<String> processLinkRecursive(Set<String> currentLinkIds,
                                             JSONObject params,
                                             ShardingEnum sharding,
                                             AtomicInteger recursionDepth) throws Exception {

        if (recursionDepth.get() > 5) {
            log.warn("Recursion depth exceeded 5 levels");
            return null;
        }
        if (CollectionUtils.isEmpty(currentLinkIds)) {
            return null;
        }
        params.put("linkIds", currentLinkIds);
        JSONObject existCheckResult = cableSectionDao.graph_check_links_exist_rr_service_entity(params, sharding.getBcShardingCode());

        if (existCheckResult != null && CollectionUtils.isNotEmpty(existCheckResult.getJSONArray("exist"))) {

            currentLinkIds = (Set<String>) existCheckResult.getJSONArray("exist").stream()
                    .map(e -> ((JSONObject) e).getString("id"))
                    .collect(Collectors.toSet());
            return currentLinkIds;
        } else {
            params.clear();
            params.put("lowerLinkIds", currentLinkIds);
            JSONObject upperLinksResult = cableSectionDao.graph_query_upper_links_by_lower_link_ids(params, sharding.getBcShardingCode());
            Set<String> upperLinkIds = extractLinkIds(upperLinksResult.getJSONArray("upperLinks"));
            recursionDepth.incrementAndGet();
            return processLinkRecursive(upperLinkIds, params, sharding, recursionDepth);
        }
    }


    @Override
    public ResponseData getServiceCircuitByCustID(ResTreeParamDto resTreeParamDto) throws Exception {
        return  getBusinessCircuitByDevice(resTreeParamDto);
    }

    @Override
    public ResponseData getServiceCircuitByCircuitCode(ResTreeParamDto resTreeParamDto) throws Exception {
        return  getBusinessCircuitByDevice(resTreeParamDto);
    }

    @Override
    public ResponseData getProductInfoByCustId(ResTreeParamDto resTreeParamDto) throws Exception {
        return null;
    }

    @Override
    public ResponseData getDeviceResourceListByCondition(ResTreeParamDto resTreeParamDto) throws Exception {
        int pageSize =resTreeParamDto.getPageSize();
        int pageNumber =resTreeParamDto.getPageNum();
        ResponseData responseData =new ResponseData();
        responseData.setPageNum(resTreeParamDto.getPageNum());
        responseData.setPageSize(resTreeParamDto.getPageSize());

        List<DeviceBO> deviceBOS = null;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pageSize", pageSize);
        jsonObject.put("currentPage", pageNumber);
        String ppmCode = getPpmCodeAndSetParam(resTreeParamDto, jsonObject);
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (null == shardingEnum) throw new RuntimeException("regionId 错误,请送PPMCODE");
        PageResponse<DeviceBO>  response=  deviceDao.graph_device_by_condition(jsonObject,shardingEnum.getBcShardingCode());
        if (null != response && CollectionUtils.isNotEmpty(response.getData())) {
            responseData.setPageCount(response.getPageInfo().getTotalPage());
            deviceBOS= response.getData();
            responseData.setData(deviceBOS);
        }else {
            responseData.setCode(ResponseData.HALF_SUCCESS);
        }
        return responseData;
    }
    @Autowired
    CustomerDao customerDao;

    @Override
    public ResponseData getCirCountByCustId(ResTreeParamDto resTreeParamDto) throws Exception {
        int pageSize = resTreeParamDto.getPageSize();
        int pageNumber = resTreeParamDto.getPageNum();
        ResponseData responseData = new ResponseData();
        responseData.setPageNum(resTreeParamDto.getPageNum());
        responseData.setPageSize(resTreeParamDto.getPageSize());
        List<ProductInBO> productInBOS ;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pageSize", pageSize);
        jsonObject.put("currentPage", pageNumber);
        String ppmCode = getPpmCodeAndSetParam(resTreeParamDto, jsonObject);
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (null == shardingEnum) throw new RuntimeException("regionId 错误,请送PPMCODE");
        JSONObject result = customerDao.graph_query_link_cout_by_custId(jsonObject, shardingEnum.getBcShardingCode());
        if (null != result && CollectionUtils.isNotEmpty(result.getJSONArray("count"))) {
            productInBOS = ProductInBO.getList(result.getJSONArray("count"));
            productInBOS = paginate(productInBOS, pageNumber, pageSize);
            responseData.setData(productInBOS);
        } else {
            responseData.setCode(ResponseData.HALF_SUCCESS);
        }
        return responseData;
    }



    public static <T> List<T> paginate(List<T> list, int pageNumber, int pageSize) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        // 计算起始索引
        int startIndex = (pageNumber - 1) * pageSize;
        if (startIndex >= list.size()) {
            return new ArrayList<>();
        }

        // 计算结束索引
        int endIndex = Math.min(startIndex + pageSize, list.size());

        // 截取分页数据
        return list.subList(startIndex, endIndex);
    }
}
