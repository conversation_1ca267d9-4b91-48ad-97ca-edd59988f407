package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.Page;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.JsonQuery;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.AlarmPerformanceService;
import com.telecom.nrm.service.DeviceService;
import com.telecom.nrm.service.HunderRiverService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class AlarmPerformanceServiceImpl implements AlarmPerformanceService {

    @Autowired
    HunderRiverService hunderRiverService;
    @Autowired
    DeviceService deviceService;

    @Autowired
    CircuitAnalysisDao circuitAnalysisDao;

    /**
     * 归一化格式
     *
     * @param str
     * @return
     */
    public static String getCommonStrForm(String str) {
        StringBuilder sb = new StringBuilder(str);
        int slashCount = 0;
        boolean needChange = false;
        int count = 0;
        for (int i = str.length() - 1; i > 0; i--) {
            if (count == 3) {
                needChange = true;
                break;
            }
            if (sb.charAt(i) == '/') {
                count++;
            }
        }
        if (needChange) {
            for (int i = sb.length() - 1; i >= 0; i--) {
                if (sb.charAt(i) == '/') {
                    slashCount++;
                    if (slashCount <= 3) {
                        int start = i + 1;
                        int end = i + 1;

                        // 找到数字部分的开始和结束位置
                        while (end < sb.length() && Character.isDigit(sb.charAt(end))) {
                            end++;
                        }

                        // 替换数字,去掉前导零
                        String numStr = sb.substring(start, end);
                        if (numStr.length() > 1 && numStr.startsWith("0")) {
                            numStr = String.valueOf(Integer.parseInt(numStr));
                        }
                        sb.replace(start, end, numStr);
                    }
                    if (slashCount == 3) {
                        int right = i - 1;
                        int left = i - 1;
                        while (left > 0 && Character.isDigit(sb.charAt(left))) {
                            left--;
                        }
                        String numStr = sb.substring(left == 0 ? 0 : left + 1, right + 1);
                        if (numStr.length() > 1 && numStr.startsWith("0")) {
                            numStr = String.valueOf(Integer.parseInt(numStr));
                        }
                        sb.replace(left == 0 ? 0 : left + 1, right + 1, numStr);
                        break;

                    }
                }
            }

            return sb.toString();
        } else {
            return str;
        }

    }


    public static String getCommonStrFormEasy(String str) {
        if (StringUtils.isEmpty(str)) return null;
        str= str.replace("00","0");
        str= str.replace("01","1");
        str= str.replace("02","2");
        str= str.replace("03","3");
        str= str.replace("04","4");
        str= str.replace("05","5");
        str= str.replace("06","6");
        str= str.replace("07","7");
        str= str.replace("08","8");
        str= str.replace("09","9");
        return str;

    }


    @Override
    public PageResponse<JSONObject> alarmInfo(JSONObject param, String shardingCode) {
        PageResponse<JSONObject> deviceInfo = deviceService.deviceDetailQuery(param, shardingCode);
        if (null != deviceInfo && CollectionUtils.isNotEmpty(deviceInfo.getData())) {
            JSONObject deviceInfoJson = deviceInfo.getData().get(0);
            String deviceName = deviceInfoJson.getString("code");
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("deviceName", deviceName);
            String areaId = param.getString("area_id");
            String areaName = ShardingEnum.getRegionNameByO3(areaId);
            jsonBody.put("city", areaName);
            JSONObject jsonObject = hunderRiverService.deviceAlarm(jsonBody);
            return getJsonObjectPageResponse(jsonObject);
        }
        return null;
    }

    @Override
    public List<JSONObject> queryAlarmInfo(Map<String, String> param) {
        // 根据设备ID 获取IP
        JSONObject devParam  = new JSONObject();
        String deviceId = MapUtils.getString(param,"device_id");
        String areaId = MapUtils.getString(param,"area_id");
        if (StringUtils.isNotEmpty(deviceId)) {
            devParam.put("deviceId",deviceId);
            PageResponse<JSONObject> deviceInfo =
                    deviceService.deviceDetailQuery(devParam, ShardingEnum.getShardingEnumByO3(areaId).getBcShardingCode());
            if (null != deviceInfo && CollectionUtils.isNotEmpty(deviceInfo.getData())) {
                JSONObject deviceInfoJson = deviceInfo.getData().get(0);
                String ip = deviceInfoJson.getString("nm_ip");
                if (StringUtils.isNotEmpty(ip)) {
                    param.clear();
                    param.put("ip",ip);
                }

            }
        }




        JSONObject jsonObject = circuitAnalysisDao.queryAlarmInfoByDeviceCode(param, NRMConstants.SHARDING_GRAPH_DB);
        if (null!= jsonObject && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("data"))){
            return JSONObject.parseArray(jsonObject.getJSONArray("data").toString(), JSONObject.class);
        }else {
            return null;
        }

    }

    @Override
    public PageResponse<JSONObject> performance(JSONObject param, String shardingCode) {
//        JSONArray portCodeArray = param.getJSONArray("portCodeArray");
        JSONArray portCodeArray = new JSONArray();
        PageResponse<JSONObject> deviceInfo = deviceService.deviceDetailQuery(param, shardingCode);
        if (null != deviceInfo && CollectionUtils.isNotEmpty(deviceInfo.getData())) {
            JSONObject deviceInfoJson = deviceInfo.getData().get(0);
            String ip = deviceInfoJson.getString("nm_ip");
            if (StringUtils.isEmpty(ip)) return null;
            JSONObject jsonBody = JSONObject.parseObject(JsonQuery.ipm.toJSONString());
            JSONObject tags = jsonBody.getJSONArray("queries").getJSONObject(0).getJSONObject("tags");
            tags.put("MgmtIp", ip);
            addRequestTime(jsonBody, -12);
            JSONObject jsonObject = hunderRiverService.ipm(jsonBody);
            JSONArray data = jsonObject.getJSONArray("value");
            if (CollectionUtils.isEmpty(data)){
                JSONObject queries = jsonBody.getJSONArray("queries").getJSONObject(0);
                addRequestTime(jsonBody, -24);
                queries.put("metric", "pon_p_flux_fluxin");
                jsonObject=hunderRiverService.queryPon(jsonBody);
                data = jsonObject.getJSONArray("value");
            }

            if (CollectionUtils.isEmpty(data)){
                JSONObject queries = jsonBody.getJSONArray("queries").getJSONObject(0);
                addRequestTime(jsonBody, -24);
                queries.put("metric", "ipran_p_flux_fluxin");
                jsonObject = hunderRiverService.ipran(jsonBody);
                data = jsonObject.getJSONArray("value");
            }
            return filterJsonObject(jsonObject, portCodeArray);
        }
        return null;
    }

    public PageResponse<JSONObject> getJsonObjectPageResponse(JSONObject jsonObject) {
        List<JSONObject> jsonObjectList = new ArrayList<>();
        if (null != jsonObject) {
            jsonObjectList.add(jsonObject);
            PageResponse<JSONObject> pageResponse = new PageResponse<>();
            Page page = new Page();
            page.setTotalPage(jsonObjectList.size());
            pageResponse.setData(jsonObjectList);
            pageResponse.setPageInfo(page);
            return pageResponse;
        }
        return null;
    }

    public PageResponse<JSONObject> filterJsonObject(JSONObject jsonObject, JSONArray portCodeArray) {
        if (null == portCodeArray || portCodeArray.isEmpty()) {
            return getJsonObjectPageResponse(jsonObject);
        }
        List<String> filter = new ArrayList<>();
        for (Object o : portCodeArray) {
            String filterContion = getCommonStrFormEasy(o.toString());
            filter.add(filterContion);
        }
//        JSONArray valueArray = jsonObject.getJSONArray("value");
//        if (null != valueArray && !valueArray.isEmpty()) {
//            Iterator iterator = valueArray.iterator();
//            while (iterator.hasNext()) {
//                JSONObject valueJson = (JSONObject) iterator.next();
//                JSONObject tagsObject = valueJson.getJSONObject("tags");
//                String tagKey = tagsObject.getString("tagKey");
//                Optional optional = filter.stream().filter(tagKey::endsWith).findFirst();
//                if (!optional.isPresent()) {
//                    iterator.remove();
//                }
//            }
//
//        }
        return getJsonObjectPageResponse(jsonObject);
    }

    public static void addRequestTime(JSONObject body, int hour) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.HOUR_OF_DAY, hour);
        body.put("start", format.format(calendar.getTime()));
        body.put("end", format.format(now));
    }


}
