package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.da.client.domain.Page;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.controller.OptGroupController;
import com.telecom.nrm.dao.OltInfoDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.domain.OptGroupElementVO;
import com.telecom.nrm.domain.OptGroupQueryVO;
import com.telecom.nrm.domain.QueryConditionVO;
import com.telecom.nrm.service.OltService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class OltServiceImpl implements OltService {

    @Autowired
    OltInfoDao oltInfoDao;

    @Autowired
    OptGroupDao optGroupDao;

    @Autowired
    OptGroupController optGroupController;

    public static void main(String[] args) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddhhmmss");
        System.out.println(simpleDateFormat.format(new Date()));
        Date date = new Date();
        System.out.println(date);
    }

    @Override
    public List<JSONObject> selectAllOlt() {
        List<JSONObject> result = new ArrayList<>();
        for (ShardingEnum value : ShardingEnum.values()) {
            List<JSONObject> oneList = selectAllOltBySharding(value.getBcShardingCode());
            if (CollectionUtils.isNotEmpty(oneList)) {
                result.addAll(oneList);
            }
        }
        return result;
    }

    @Override
    public List<JSONObject> selectAllOltBySharding(String shardingCode) {
        List<JSONObject> result = new ArrayList<>();
        QueryConditionVO queryConditionVO = new QueryConditionVO(1, 500);
        PageResponse<JSONObject> pageResponse = oltInfoDao.selectAllOlt(queryConditionVO, shardingCode);
        if (null != pageResponse && CollectionUtils.isNotEmpty(pageResponse.getData())) {
            Page pageInfo = pageResponse.getPageInfo();
            int totalPage = pageInfo.getTotalPage();
            result.addAll(pageResponse.getData());
            int start = 1;
            while (totalPage > 1) {
                queryConditionVO.setCurrentPage(start += 1);
                PageResponse<com.alibaba.fastjson.JSONObject> pageResponseRec
                        = oltInfoDao.selectAllOlt(queryConditionVO, shardingCode);
                if (null != pageResponseRec && CollectionUtils.isNotEmpty(pageResponseRec.getData())) {
                    result.addAll(pageResponseRec.getData());
                }
                totalPage -= 1;
            }
        }
        return result;
    }

    @Override
    public void createOptGroupAndElement(String code, String name, String shardingCode, BigDecimal oltId) {

        OptGroupQueryVO optGroupQueryVO = new OptGroupQueryVO();
        optGroupQueryVO.setCode(code);
        optGroupQueryVO.setName(name);
        JSONObject queryJson = optGroupQueryVO.toJsonObject();
        optGroupQueryVO.setAreaName(ShardingEnum.getShardingEnumByBc(shardingCode).getRegionName());
        JSONObject jsonObject = optGroupQueryVO.toJsonObject();
        jsonObject.put("source_type_id", 10);
        jsonObject.put("state_id", 1);
        List<JSONObject> list = Arrays.asList(jsonObject);
        try {
            PageResponse<JSONObject> jsonObjectPageResponse = optGroupDao.pm_opt_road_group_result_query(queryJson, 100, 1, shardingCode);
            if (ObjectUtil.isNotNull(jsonObjectPageResponse) && CollectionUtils.isNotEmpty(jsonObjectPageResponse.getData())) {
                JSONObject jsonObject1 = jsonObjectPageResponse.getData().get(0);
                Integer integer = jsonObject1.getInteger("id");
                queryJson.put("id", integer);
                optGroupDao.pm_opt_road_group_delete(queryJson, shardingCode);
                optGroupDao.pm_opt_road_group_insert(list, shardingCode);
                List<JSONObject> link = oltInfoDao.queryResOltRelaLink(oltId, shardingCode);
                if (CollectionUtils.isNotEmpty(link)) {
                    AtomicInteger start = new AtomicInteger(0);
                    List<JSONObject> jsonObject2 = link.stream().map(item -> {
                        OptGroupElementVO optGroupElementVO = new OptGroupElementVO();
                        optGroupElementVO.setGroupId(integer);
                        optGroupElementVO.setCode(code);
                        optGroupElementVO.setRouteNo(String.valueOf(start.addAndGet(1)));
                        optGroupElementVO.setOptCode(item.getString("opt_road_code"));
                        return optGroupElementVO.toJsonObject();
                    }).collect(java.util.stream.Collectors.toList());
                    optGroupDao.pm_opt_road_group_element_insert(jsonObject2, shardingCode);
                }
            } else {
                optGroupDao.pm_opt_road_group_insert(list, shardingCode);
                List<JSONObject> link = oltInfoDao.queryResOltRelaLink(oltId, shardingCode);
                if (CollectionUtils.isNotEmpty(link)) {
                    AtomicInteger start = new AtomicInteger(0);
                    List<JSONObject> jsonObject2 = link.stream().map(item -> {
                        OptGroupElementVO optGroupElementVO = new OptGroupElementVO();
                        optGroupElementVO.setCode(code);
                        optGroupElementVO.setRouteNo(String.valueOf(start.addAndGet(1)));
                        optGroupElementVO.setOptCode(item.getString("opt_road_code"));
                        return optGroupElementVO.toJsonObject();
                    }).collect(java.util.stream.Collectors.toList());
                    optGroupDao.pm_opt_road_group_element_insert(jsonObject2, shardingCode);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public JSONObject querOltInfoById(BigDecimal oltId, String shardingCode) {
        Set<BigDecimal> bigDecimals = new HashSet<>();
        bigDecimals.add(oltId);
        List<JSONObject> list = oltInfoDao.queryResOltByIds(bigDecimals, shardingCode);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public void addAlarmInfo(List<JSONObject> devices,String region) {
        if (CollectionUtils.isNotEmpty(devices)) {
            for (JSONObject device : devices) {
                String name = device.getString("oltname");
                //String code = device.getString("oltcode");
                Map<String, Object> map = new HashMap<>();
                map.put("name", name);
                map.put("source_type_id", 10);
                map.put("page", 1);
                map.put("size", 10);
                map.put("ds", region);
                BiyiPageResult<JSONObject> result
                        = optGroupController.pageQuery(map, new BiyiPageRequest(1, 10));
                if (null != result && CollectionUtils.isNotEmpty(result.getData())) {
                    String previousCheckResult = result.getData().get(0).getString("checkResult");
                    device.put("result", previousCheckResult);
                }
            }
        }

    }

}
