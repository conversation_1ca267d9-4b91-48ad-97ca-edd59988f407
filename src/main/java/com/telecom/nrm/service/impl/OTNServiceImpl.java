package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.CommonCircuitService;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.OTNService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;




@Slf4j
public class OTNServiceImpl implements OTNService {

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    CommonCircuitService commonCircuitService;


    @Override
    public JSONObject queryOTNRoute(JSONObject request) {
        JSONObject result = queryOTNRoute_1(request);

        List<JSONObject> nodes = JSONObjectUtil.jsonObjectArrayToList( result.getJSONArray("nodes"));
        if (ObjectUtil.isEmpty(nodes)) {
            result = queryOTNRoute_2(request);
        }

        return result;
    }


    public JSONObject queryOTNRoute_1(JSONObject request) {
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");


        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_otn_circuit_route").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("otn_circuit","code",code))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);

        return graphToTOPO(graph);
    }

    public JSONObject queryOTNRoute_2(JSONObject request) {
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");


        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_otn_circuit_route_2").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("otn_circuit_route","code",code))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);


        return graphToTOPO(graph);
    }


    public JSONObject graphToTOPO (Graph graph) {
        Set<Node> nodes = graph.getNodes();
        Set<Edge> edges = graph.getEdges();
        Node deviceNode = nodes.stream().filter(n->n.getId().equals("otn_device")).collect(Collectors.toList()).get(0);
        Node linkNode = nodes.stream().filter(n->n.getId().equals("otn_link")).collect(Collectors.toList()).get(0);
        Edge linkCarryLinkEdge = edges.stream().filter(e->e.getAId().equals("otn_link") && e.getBId().equals("otn_circuit_route") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);


        List<JSONObject> devices = deviceNode.getData();
        List<JSONObject> links = linkNode.getData();
        List<JSONObject> linkRelations = linkCarryLinkEdge.getData();

        linkRelations.removeIf(d->d.getString("upper_link_id").equals(d.getString("lower_link_id")));

        JSONObject result = sortCircuitTopology(devices,links,linkRelations);
        return result;
    }




    public JSONObject sortCircuitTopology(List<JSONObject> devices, List<JSONObject> links ,List<JSONObject> linkRelations) {


        links = links.stream().filter(d->
        {
            if (ObjectUtil.isEmpty(d.getString("a_physic_device_id")) || ObjectUtil.isEmpty(d.getString("z_physic_device_id"))) {
                return false;
            } else if (d.getString("a_physic_device_id").equals(d.getString("z_physic_device_id"))) {
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        Map<String,JSONObject> lowerToUpperLink = new HashMap<String,JSONObject>();
        linkRelations.forEach(d->{
            lowerToUpperLink.put(d.getString("lower_link_id"), d);
        });
        updateParentId( links, lowerToUpperLink);

        List<JSONObject> rootLinks = links.stream().filter(d->ObjectUtil.isEmpty(d.getString("parent_id"))).collect(Collectors.toList());
        rootLinks.forEach(l->{updateChildrenNum(l);l.put("layer",0);});


        List<JSONObject> sortedDevices = sortDevices(devices ,rootLinks);

        JSONObject result = new JSONObject();
        result.put("nodes", sortedDevices);
        result.put("links", rootLinks);
        return result;


    }


    void updateChildrenNum (JSONObject link) {
        List<JSONObject> children = JSONObjectUtil.jsonObjectArrayToList(link.getJSONArray("children"));
        if (ObjectUtil.isEmpty(children)) {
            link.put("children_num", 0);
        }else{
            for (JSONObject child: children) {
                updateChildrenNum(child);
            }
            int childrenNum = 0;
            for (JSONObject child: children) {
                childrenNum+=(child.getInteger("children_num")+1);
            }
            link.put("children_num", childrenNum);

        }

    }
    Map<String,JSONObject> listToMap(List<JSONObject> list,String key) {
        Map<String,JSONObject> map = new HashMap<>();
        for (JSONObject o: list) {
            map.put(o.getString(key),o);
        }
        return map;
    }
    /**
     * 设置链路的上下级关系
     * @param links
     * @param lowerToUpperLink
     */
    void updateParentId(List<JSONObject> links, Map<String,JSONObject> lowerToUpperLink) {
        Map<String,JSONObject>  linkMap= listToMap(links,"id");
        links.forEach(d->{
            JSONObject upper = lowerToUpperLink.get(d.getString("id"));
            if (ObjectUtil.isNotEmpty(upper)) {
                String parentId= upper.getString("upper_link_id");
                d.put("parent_id", upper.getString("upper_link_id"));
                JSONObject parentLink =linkMap.get(parentId);
                d.put("parent_code",parentLink.getString("code"));
                d.put("layer", upper.getInteger("layer"));
                d.put("a_physic_device_id", upper.getString("a_ne_id"));
                d.put("z_physic_device_id", upper.getString("z_ne_id"));
                d.put("a_ne_id", upper.getString("a_ne_id"));
                d.put("z_ne_id", upper.getString("z_ne_id"));
                d.put("a_port_id", upper.getString("a_port_id"));
                d.put("z_port_id", upper.getString("z_port_id"));
                d.put("seg_no",upper.getInteger("seg_no"));
                d.put("grp_no",upper.getInteger("grp_no"));
                d.put("order_no",upper.getInteger("order_no"));
            }
        });
        Map<String,List<JSONObject>> linkDataGroup = links.stream().collect(Collectors.groupingBy(d->d.getString("parent_id")==null?"":d.getString("parent_id"), Collectors.toList()));
        links.forEach(d->{
            List<JSONObject> children = linkDataGroup.get(d.getString("id"));
            if (ObjectUtil.isNotEmpty(children)) {
                d.put("children", children);
            }
        });
    }






    /**
     * 根据链路的AZ端对节点排序
     * @param devices
     * @param rootLinks
     * @return
     */
    List<JSONObject> sortDevices (List<JSONObject> devices,List<JSONObject> rootLinks) {
        // 计算节点的顺序

        Map<String,JSONObject> idMapDevice = new HashMap();
        for (JSONObject d: devices) idMapDevice.put(d.getString("id"), d);

        List<String> deviceIds= new ArrayList<>();
        for (JSONObject root: rootLinks) {
             computeNodeOrder(deviceIds,root);
        }

        List<JSONObject> sortedDevice = new ArrayList<>();

        for (String id: deviceIds) {
            JSONObject d = idMapDevice.get(id);
            if (ObjectUtil.isEmpty(d)) {
                d = new JSONObject();
                d.put("id",id);
                d.put("name", "虚拟设备");
            }
            sortedDevice.add(d);
        }
        // sortedByRoom(sortedDevice);
        int i=0;
        for (JSONObject d:sortedDevice) {
            d.put("seq",i);
            i++;
        }
        return sortedDevice;
    }






    /**
     * 计算节点的序号
     * @param link
     * @return
     */
    List<String> computeNodeOrder(List<String> nodeIds , JSONObject link) {
        // List<String> nodeIds = new ArrayList<>();

        String startId = link.getString("a_physic_device_id");
        String endId = link.getString("z_physic_device_id");
        appendNodeId(nodeIds, startId); // 将起始节点加入节点列表
        List<JSONObject> children = JSONObjectUtil.jsonObjectArrayToList(link.getJSONArray("children"));






        if (ObjectUtil.isNotEmpty(children)) {

            Collections.sort(children,(d1,d2)->{
                Integer seg1 = d1.getInteger("seg_no");
                Integer seg2 = d2.getInteger("seg_no");
                Integer grp1 = d1.getInteger("grp_no");
                Integer grp2 = d2.getInteger("grp_no");
                Integer order1 = d1.getInteger("order_no");
                Integer order2 = d1.getInteger("order_no");

                if (seg1>seg2) {
                    return 1;
                }else if (seg1<seg2) {
                    return -1;
                }else if (grp1>grp2) {
                    return 1;
                }else if (grp1<grp2) {
                    return -1;
                }else if (order1>order2) {
                    return 1;
                }else if (order1<order2) {
                    return -1;
                }else{
                    return 0;
                }
            });

            JSONObject first = children.get(0);
            if (!first.getString("a_physic_device_id").equals(link.getString("a_physic_device_id"))) {
                JSONObject patchLink = new JSONObject();
                String aNeId = link.getString("a_physic_device_id");
                String zNeId = first.getString("a_physic_device_id");
                String id = aNeId+"-"+zNeId;
                patchLink.put("id", id);
                patchLink.put("a_physic_device_id",aNeId);
                patchLink.put("z_physic_device_id",zNeId);
                patchLink.put("spec_name","虚拟连接");
                // children.add(0,patchLink);
            }

            for (JSONObject child: children) {
                computeNodeOrder(nodeIds,child);
            }

            JSONObject end = children.get(children.size()-1);
            if (!end.getString("z_physic_device_id").equals(link.getString("z_physic_device_id"))) {
                JSONObject patchLink = new JSONObject();
                String aNeId = end.getString("z_physic_device_id");
                String zNeId = link.getString("z_physic_device_id");
                String id = aNeId+"-"+zNeId;
                patchLink.put("id", id);
                patchLink.put("a_physic_device_id",aNeId);
                patchLink.put("z_physic_device_id",zNeId);
                patchLink.put("spec_name","虚拟连接");
                // children.add(patchLink);
            }
        }


        // computeNodeOrder(Arrays.asList(link),startId,endId, nodeIds,idMapDevice);

        appendNodeId(nodeIds, endId); // 将起始节点加入节点列表

        return nodeIds;
    }


    void appendNodeId(List<String> nodeIds, String id) {
        /*
        if (nodeIds.contains(id)) {
            nodeIds.remove(id);
        }
        nodeIds.add(id);
        */
        if (!nodeIds.contains(id)) {
            nodeIds.add(id);
        }

    }

    void appendEndNodeId (List<String> nodeIds, String id) {
        String firstId=nodeIds.get(0);
        if (id.equals(firstId)) return;
        if (nodeIds.contains(id)) {
            nodeIds.remove(id);
        }
        nodeIds.add(id);
    }
}
