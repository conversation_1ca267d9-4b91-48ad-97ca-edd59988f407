package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.HazardRemediationDao;
import com.telecom.nrm.service.HazardRemediationService;
import com.telecom.nrm.service.OptGroupService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.telecom.nrm.domain.NRMConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 隐患整改服务实现类
 */
@Service
public class HazardRemediationServiceImpl implements HazardRemediationService {

    private static final Logger log = LoggerFactory.getLogger(HazardRemediationServiceImpl.class);

    @Autowired
    private HazardRemediationDao hazardRemediationDao;

    @Autowired
    private OptGroupService optGroupService;

    // 用于并行查询的线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    public JSONObject createHazardRemediation(JSONObject param, String shardingCode) {
        try {
            log.info("创建隐患整改单，参数：{}", param.toJSONString());

            // 提取光路数据
            Object opticalPathsData = param.get("opticalPaths");

            // 创建主表记录
            JSONObject createResult = hazardRemediationDao.createHazardRemediation(param, shardingCode);

            if (!createResult.getBooleanValue("success")) {
                return createResult;
            }

            // 获取创建的整改单ID
            JSONObject data = createResult.getJSONObject("data");
            if (data == null || data.get("id") == null) {
                log.error("创建整改单成功但未返回ID");
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "创建整改单成功但未返回ID");
                return result;
            }

            String remediationId = data.getString("id");
            log.info("整改单创建成功，ID：{}，开始保存光路信息", remediationId);

            // 保存光路信息
            if (opticalPathsData != null) {
                log.info("开始处理光路数据，类型：{}，内容：{}",
                        opticalPathsData.getClass().getSimpleName(), opticalPathsData);
                try {
                    JSONArray opticalPathsArray = new JSONArray();

                    // 处理ArrayList类型的光路数据
                    if (opticalPathsData instanceof java.util.List) {
                        java.util.List<?> sourceList = (java.util.List<?>) opticalPathsData;
                        log.info("光路数据是List，长度：{}", sourceList.size());

                        for (int i = 0; i < sourceList.size(); i++) {
                            Object item = sourceList.get(i);
                            log.info("处理光路项 {}，类型：{}，内容：{}", i,
                                    item != null ? item.getClass().getSimpleName() : "null", item);

                            if (item instanceof String) {
                                // 如果是字符串，转换为对象
                                String pathCode = (String) item;
                                if (pathCode != null && !pathCode.trim().isEmpty()) {
                                    JSONObject pathObj = new JSONObject();
                                    pathObj.put("opticalPathCode", pathCode.trim());
                                    pathObj.put("sortOrder", i + 1);
                                    opticalPathsArray.add(pathObj);
                                    log.info("添加光路：{}, 排序：{}", pathCode.trim(), i + 1);
                                } else {
                                    log.warn("跳过空的光路编码，索引：{}", i);
                                }
                            } else {
                                log.warn("光路项不是字符串类型，索引：{}，类型：{}", i,
                                        item != null ? item.getClass().getSimpleName() : "null");
                            }
                        }
                    } else {
                        log.warn("光路数据不是List类型：{}", opticalPathsData.getClass().getSimpleName());
                    }

                    log.info("处理后的光路数组长度：{}，内容：{}", opticalPathsArray.size(), opticalPathsArray.toJSONString());

                    // 保存光路数据到数据库
                    JSONObject opticalPathParam = new JSONObject();
                    opticalPathParam.put("remediationId", remediationId);
                    opticalPathParam.put("opticalPaths", opticalPathsArray);

                    log.info("准备保存光路数据，参数：{}", opticalPathParam.toJSONString());

                    JSONObject saveOpticalPathsResult = hazardRemediationDao.batchInsertOpticalPaths(opticalPathParam, shardingCode);
                    log.info("光路信息保存结果：{}", saveOpticalPathsResult.toJSONString());

                    if (!saveOpticalPathsResult.getBooleanValue("success")) {
                        log.error("光路信息保存失败，但主表已创建：{}", saveOpticalPathsResult.getString("message"));
                    } else {
                        log.info("光路信息保存成功，插入{}条记录", opticalPathsArray.size());
                    }

                } catch (Exception opticalPathException) {
                    log.error("保存光路信息失败：{}", opticalPathException.getMessage(), opticalPathException);
                    // 光路保存失败不影响主表创建成功的返回
                }
            }

            return createResult;

        } catch (Exception e) {
            log.error("创建隐患整改单失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "创建隐患整改单失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public PageResponse<JSONObject> queryHazardRemediationList(JSONObject param, Integer pageSize, Integer currentPage, String shardingCode) {
        try {
            log.info("查询隐患整改单列表，参数：{}，页大小：{}，当前页：{}", param.toJSONString(), pageSize, currentPage);
            return hazardRemediationDao.queryHazardRemediationList(param, pageSize, currentPage, shardingCode);
        } catch (Exception e) {
            log.error("查询隐患整改单列表失败：{}", e.getMessage(), e);
            throw new RuntimeException("查询隐患整改单列表失败：" + e.getMessage());
        }
    }

    @Override
    public JSONObject getHazardRemediationDetail(String id, String shardingCode) {
        try {
            log.info("查询隐患整改单详情，ID：{}", id);

            // 并行查询主表、光路、附件信息
            CompletableFuture<JSONObject> mainDataFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    JSONObject param = new JSONObject();
                    param.put("id", id);
                    return hazardRemediationDao.getHazardRemediationDetail(param, shardingCode);
                } catch (Exception e) {
                    log.error("查询主表信息失败：{}", e.getMessage(), e);
                    JSONObject errorResult = new JSONObject();
                    errorResult.put("success", false);
                    errorResult.put("message", "查询主表信息失败：" + e.getMessage());
                    return errorResult;
                }
            }, executorService);

            CompletableFuture<JSONObject> opticalPathsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return queryOpticalPaths(id, shardingCode);
                } catch (Exception e) {
                    log.error("查询光路信息失败：{}", e.getMessage(), e);
                    JSONObject errorResult = new JSONObject();
                    errorResult.put("success", false);
                    errorResult.put("data", new JSONArray());
                    return errorResult;
                }
            }, executorService);

            CompletableFuture<JSONObject> attachmentsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return queryAttachments(id, shardingCode);
                } catch (Exception e) {
                    log.error("查询附件信息失败：{}", e.getMessage(), e);
                    JSONObject errorResult = new JSONObject();
                    errorResult.put("success", false);
                    errorResult.put("data", new JSONArray());
                    return errorResult;
                }
            }, executorService);

            // 等待所有查询完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                mainDataFuture, opticalPathsFuture, attachmentsFuture
            );

            allFutures.join();

            // 获取查询结果
            JSONObject mainResult = mainDataFuture.get();
            JSONObject opticalPathsResult = opticalPathsFuture.get();
            JSONObject attachmentsResult = attachmentsFuture.get();

            // 检查主表查询是否成功
            if (!mainResult.getBooleanValue("success")) {
                return mainResult;
            }

            // 组装完整的结果
            JSONObject mainData = mainResult.getJSONObject("data");
            if (mainData == null) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "隐患整改单不存在");
                return errorResult;
            }

            // 添加光路信息
            JSONArray opticalPaths = new JSONArray();
            if (opticalPathsResult.getBooleanValue("success")) {
                Object opticalPathsData = opticalPathsResult.get("data");
                if (opticalPathsData instanceof JSONArray) {
                    opticalPaths = (JSONArray) opticalPathsData;
                } else if (opticalPathsData != null) {
                    // 如果返回的不是数组，尝试转换
                    opticalPaths.add(opticalPathsData);
                }
            }
            mainData.put("opticalPaths", opticalPaths);

            // 添加附件信息
            JSONArray attachments = new JSONArray();
            if (attachmentsResult.getBooleanValue("success")) {
                Object attachmentsData = attachmentsResult.get("data");
                if (attachmentsData instanceof JSONArray) {
                    attachments = (JSONArray) attachmentsData;
                } else if (attachmentsData != null) {
                    // 如果返回的不是数组，尝试转换
                    attachments.add(attachmentsData);
                }
            }
            mainData.put("attachments", attachments);

            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", mainData);

            log.info("隐患整改单详情查询完成，ID：{}，光路数量：{}，附件数量：{}",
                    id, opticalPaths.size(), attachments.size());

            return result;

        } catch (Exception e) {
            log.error("查询隐患整改单详情失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "查询隐患整改单详情失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject updateHazardRemediation(JSONObject param, String shardingCode) {
        try {
            log.info("更新隐患整改单，参数：{}", param.toJSONString());

            // 提取光路数据和整改单ID
            Object opticalPathsData = param.get("opticalPaths");
            String remediationId = param.getString("id");

            // 更新主表记录
            JSONObject updateResult = hazardRemediationDao.updateHazardRemediation(param, shardingCode);

            if (!updateResult.getBooleanValue("success")) {
                return updateResult;
            }

            log.info("整改单更新成功，ID：{}，开始更新光路信息", remediationId);

            // 更新光路信息
            if (opticalPathsData != null && remediationId != null) {
                log.info("开始更新光路数据，类型：{}，内容：{}",
                        opticalPathsData.getClass().getSimpleName(), opticalPathsData);
                try {
                    JSONArray opticalPathsArray = new JSONArray();

                    // 处理ArrayList类型的光路数据
                    if (opticalPathsData instanceof java.util.List) {
                        java.util.List<?> sourceList = (java.util.List<?>) opticalPathsData;
                        log.info("光路数据是List，长度：{}", sourceList.size());

                        for (int i = 0; i < sourceList.size(); i++) {
                            Object item = sourceList.get(i);
                            log.info("处理光路项 {}，类型：{}，内容：{}", i,
                                    item != null ? item.getClass().getSimpleName() : "null", item);

                            if (item instanceof String) {
                                // 如果是字符串，转换为对象
                                String pathCode = (String) item;
                                if (pathCode != null && !pathCode.trim().isEmpty()) {
                                    JSONObject pathObj = new JSONObject();
                                    pathObj.put("opticalPathCode", pathCode.trim());
                                    pathObj.put("sortOrder", i + 1);
                                    opticalPathsArray.add(pathObj);
                                    log.info("添加光路：{}, 排序：{}", pathCode.trim(), i + 1);
                                } else {
                                    log.warn("跳过空的光路编码，索引：{}", i);
                                }
                            } else {
                                log.warn("光路项不是字符串类型，索引：{}，类型：{}", i,
                                        item != null ? item.getClass().getSimpleName() : "null");
                            }
                        }
                    } else {
                        log.warn("光路数据不是List类型：{}", opticalPathsData.getClass().getSimpleName());
                    }

                    // 先删除旧的光路信息
                    JSONObject deleteParam = new JSONObject();
                    deleteParam.put("remediationId", remediationId);

                    log.info("准备删除旧光路信息，参数：{}，shardingCode：{}", deleteParam.toJSONString(), shardingCode);
                    JSONObject deleteResult = hazardRemediationDao.deleteOpticalPaths(deleteParam, shardingCode);
                    log.info("删除旧光路信息结果：{}", deleteResult.toJSONString());

                    // 再插入新的光路信息
                    JSONObject opticalPathParam = new JSONObject();
                    opticalPathParam.put("remediationId", remediationId);
                    opticalPathParam.put("opticalPaths", opticalPathsArray);

                    log.info("准备插入新光路信息，参数：{}，shardingCode：{}", opticalPathParam.toJSONString(), shardingCode);
                    JSONObject saveOpticalPathsResult = hazardRemediationDao.batchInsertOpticalPaths(opticalPathParam, shardingCode);
                    log.info("光路信息更新结果：{}", saveOpticalPathsResult.toJSONString());

                    if (!saveOpticalPathsResult.getBooleanValue("success")) {
                        log.warn("光路信息更新失败，但主表已更新：{}", saveOpticalPathsResult.getString("message"));
                    } else {
                        log.info("光路信息更新成功，插入{}条记录", opticalPathsArray.size());
                    }

                } catch (Exception opticalPathException) {
                    log.error("更新光路信息失败：{}", opticalPathException.getMessage(), opticalPathException);
                    // 光路更新失败不影响主表更新成功的返回
                }
            }

            return updateResult;

        } catch (Exception e) {
            log.error("更新隐患整改单失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "更新隐患整改单失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject deleteHazardRemediation(String id, String shardingCode) {
        try {
            log.info("删除隐患整改单，ID：{}", id);

            // 1. 先查询记录是否存在及其状态
            JSONObject queryParam = new JSONObject();
            queryParam.put("id", id);
            JSONObject detailResult = hazardRemediationDao.getHazardRemediationDetail(queryParam, shardingCode);

            if (!detailResult.getBooleanValue("success")) {
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "隐患整改单不存在");
                return result;
            }

            JSONObject data = detailResult.getJSONObject("data");
            if (data == null) {
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "隐患整改单不存在");
                return result;
            }

            // 2. 检查是否可以删除（移除状态限制，支持删除所有状态的记录）
            String status = data.getString("status");
            log.info("隐患整改单状态：{}，开始执行删除操作", status);

            // 注意：权限控制已在前端实现，这里不再进行权限验证
            // 如果需要后端权限验证，可以在这里添加责任人/整改人的检查逻辑

            // 3. 执行删除操作
            JSONObject deleteParam = new JSONObject();
            deleteParam.put("id", id);
            JSONObject deleteResult = hazardRemediationDao.deleteHazardRemediation(deleteParam, shardingCode);

            if (deleteResult.getBooleanValue("success")) {
                log.info("隐患整改单删除成功，ID：{}", id);
            }

            return deleteResult;

        } catch (Exception e) {
            log.error("删除隐患整改单失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "删除隐患整改单失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject saveOpticalPaths(String remediationId, JSONObject opticalPaths, String shardingCode) {
        try {
            log.info("保存隐患整改光路信息，整改单ID：{}，光路数量：{}", remediationId, opticalPaths.size());
            JSONObject param = new JSONObject();
            param.put("remediationId", remediationId);
            param.put("opticalPaths", opticalPaths);
            return hazardRemediationDao.batchInsertOpticalPaths(param, shardingCode);
        } catch (Exception e) {
            log.error("保存隐患整改光路信息失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "保存光路信息失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject queryOpticalPaths(String remediationId, String shardingCode) {
        try {
            log.info("查询隐患整改光路列表，整改单ID：{}", remediationId);
            JSONObject param = new JSONObject();
            param.put("remediationId", remediationId);
            return hazardRemediationDao.queryOpticalPaths(param, shardingCode);
        } catch (Exception e) {
            log.error("查询隐患整改光路列表失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "查询光路列表失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject saveAttachment(JSONObject param, String shardingCode) {
        try {
            log.info("保存附件记录，参数：{}", param.toJSONString());
            return hazardRemediationDao.insertAttachment(param, shardingCode);
        } catch (Exception e) {
            log.error("保存附件记录失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "保存附件记录失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject queryAttachments(String remediationId, String shardingCode) {
        try {
            log.info("查询附件列表，整改单ID：{}", remediationId);
            JSONObject param = new JSONObject();
            param.put("remediationId", remediationId);
            return hazardRemediationDao.queryAttachments(param, shardingCode);
        } catch (Exception e) {
            log.error("查询附件列表失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "查询附件列表失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject deleteAttachment(String attachmentId, String shardingCode) {
        try {
            log.info("删除附件记录，附件ID：{}", attachmentId);
            JSONObject param = new JSONObject();
            param.put("id", attachmentId);
            return hazardRemediationDao.deleteAttachment(param, shardingCode);
        } catch (Exception e) {
            log.error("删除附件记录失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "删除附件记录失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject getStatistics(JSONObject param, String shardingCode) {
        try {
            log.info("获取统计信息，参数：{}", param.toJSONString());
            return hazardRemediationDao.getStatistics(param, shardingCode);
        } catch (Exception e) {
            log.error("获取统计信息失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "获取统计信息失败：" + e.getMessage());
            return result;
        }
    }

    // ==================== 风险检测相关方法实现 ====================

    @Override
    public JSONObject performRiskDetection(JSONObject param, String shardingCode) {
        try {
            log.info("执行风险检测，参数：{}", param.toJSONString());

            // 确保参数中包含必要的字段
            if (!param.containsKey("ds")) {
                // 从shardingCode反推地市代码
                String ds = getAreaCodeFromShardingCode(shardingCode);
                param.put("ds", ds);
                log.info("从shardingCode {} 推导出地市代码: {}", shardingCode, ds);
            }

            // 调用光路组风险分析服务
            JSONObject riskResult = optGroupService.risk_analyze(param);

            // 构建检测记录参数
            JSONObject detectionParam = new JSONObject();
            detectionParam.put("remediationId", param.getString("remediationId"));
            detectionParam.put("detectionType", param.getString("detectionType"));
            detectionParam.put("detectionPhase", param.getString("detectionPhase"));
            detectionParam.put("checkResult", riskResult.getString("check_result"));
            detectionParam.put("exceptionInfo", riskResult.getString("exception_info"));
            detectionParam.put("riskLevel", determineRiskLevel(riskResult.getString("check_result")));
            detectionParam.put("routeNum", riskResult.getInteger("route_num"));
            detectionParam.put("opticalPathCount", param.getInteger("opticalPathCount"));
            detectionParam.put("detectionSnapshot", riskResult.toJSONString());
            detectionParam.put("opticalPathsSnapshot", param.getString("opticalPathsSnapshot"));
            detectionParam.put("detector", param.getString("detector"));
            detectionParam.put("detectionStatus", "completed");

            // 保存检测记录
            JSONObject saveResult = saveRiskDetection(detectionParam, shardingCode);

            // 返回检测结果
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "风险检测完成");
            result.put("data", riskResult);
            result.put("detectionRecord", saveResult.get("data"));

            return result;

        } catch (Exception e) {
            log.error("执行风险检测失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "执行风险检测失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject saveRiskDetection(JSONObject param, String shardingCode) {
        try {
            log.info("保存风险检测记录，参数：{}", param.toJSONString());

            // 1. 保存检测记录
            JSONObject saveResult = hazardRemediationDao.saveRiskDetection(param, shardingCode);

            if (!saveResult.getBooleanValue("success")) {
                return saveResult;
            }

            // 获取保存的检测记录ID
            Object dataObj = saveResult.get("data");
            JSONObject detectionRecord = null;
            String detectionId = null;

            if (dataObj instanceof JSONArray) {
                JSONArray dataArray = (JSONArray) dataObj;
                if (dataArray != null && !dataArray.isEmpty()) {
                    detectionRecord = dataArray.getJSONObject(0);
                    detectionId = detectionRecord.getString("id");
                }
            } else if (dataObj instanceof JSONObject) {
                detectionRecord = (JSONObject) dataObj;
                detectionId = detectionRecord.getString("id");
            }

            if (detectionId == null) {
                log.error("保存检测记录成功但未返回ID，返回数据：{}", saveResult.toJSONString());
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "保存检测记录成功但未返回ID");
                return result;
            }
            String remediationId = param.getString("remediationId");
            String detectionType = param.getString("detectionType");
            String checkResult = param.getString("checkResult");

            // 2. 检测结果已保存到检测记录表，不再需要单独的遗留问题表

            // 3. 更新主表检测信息
            JSONObject updateParam = new JSONObject();
            updateParam.put("remediationId", remediationId);
            updateParam.put("latestDetectionId", detectionId);
            updateParam.put("hasRemainingIssues", !"正常".equals(checkResult));

            // 根据检测类型设置相应字段
            if ("initial".equals(detectionType)) {
                updateParam.put("initialDetectionId", detectionId);
            } else if ("completion".equals(detectionType)) {
                updateParam.put("completionDetectionId", detectionId);
            }

            JSONObject updateResult = hazardRemediationDao.updateDetectionInfo(updateParam, shardingCode);
            log.info("更新主表检测信息结果：{}", updateResult.toJSONString());

            // 注意：不再需要单独保存时间线记录，直接从检测记录表生成时间线

            return saveResult;

        } catch (Exception e) {
            log.error("保存风险检测记录失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "保存风险检测记录失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject queryRiskDetections(String remediationId, String shardingCode) {
        try {
            log.info("查询风险检测记录，整改单ID：{}", remediationId);
            JSONObject param = new JSONObject();
            param.put("remediationId", remediationId);
            return hazardRemediationDao.queryRiskDetections(param, shardingCode);
        } catch (Exception e) {
            log.error("查询风险检测记录失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "查询风险检测记录失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public JSONObject performRiskComparison(JSONObject param, String shardingCode) {
        try {
            log.info("执行风险对比分析，参数：{}", param.toJSONString());

            String remediationId = param.getString("remediationId");
            String beforeDetectionId = param.getString("beforeDetectionId");
            String afterDetectionId = param.getString("afterDetectionId");

            // 查询检测记录
            JSONObject queryParam = new JSONObject();
            queryParam.put("remediationId", remediationId);
            JSONObject detectionsResult = hazardRemediationDao.queryRiskDetections(queryParam, shardingCode);

            if (!detectionsResult.getBooleanValue("success")) {
                return detectionsResult;
            }

            // 处理DataQL返回数据格式并查找对应的检测记录
            JSONArray detections = normalizeDataQLResult(detectionsResult.get("data"));
            JSONObject beforeDetection = null;
            JSONObject afterDetection = null;

            for (int i = 0; i < detections.size(); i++) {
                JSONObject detection = detections.getJSONObject(i);
                if (beforeDetectionId.equals(detection.getString("id"))) {
                    beforeDetection = detection;
                }
                if (afterDetectionId.equals(detection.getString("id"))) {
                    afterDetection = detection;
                }
            }

            if (beforeDetection == null || afterDetection == null) {
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "未找到对应的检测记录");
                return result;
            }

            // 执行对比分析
            JSONObject comparisonResult = analyzeRiskComparison(beforeDetection, afterDetection);

            // 不再保存对比结果到单独的表，直接处理遗留问题

            // 遗留问题现在存储在主表的用户填写字段中，不再使用单独的遗留问题表
            JSONArray remainingIssuesList = comparisonResult.getJSONArray("remainingIssuesList");
            if (remainingIssuesList != null && !remainingIssuesList.isEmpty()) {
                // 更新主表标记有遗留问题
                JSONObject updateMainParam = new JSONObject();
                updateMainParam.put("remediationId", remediationId);
                updateMainParam.put("latestDetectionId", afterDetectionId);
                updateMainParam.put("hasRemainingIssues", true);

                JSONObject updateMainResult = hazardRemediationDao.updateDetectionInfo(updateMainParam, shardingCode);
                log.info("更新主表遗留问题标记结果：{}", updateMainResult.toJSONString());
            }

            // 时间线现在直接从检测记录生成，不需要单独保存

            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "风险对比分析完成");
            result.put("data", comparisonResult);

            return result;

        } catch (Exception e) {
            log.error("执行风险对比分析失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "执行风险对比分析失败：" + e.getMessage());
            return result;
        }
    }





    @Override
    public JSONObject queryDetectionTimeline(String remediationId, String shardingCode) {
        try {
            log.info("查询检测时间线，整改单ID：{}", remediationId);
            JSONObject param = new JSONObject();
            param.put("remediationId", remediationId);
            return hazardRemediationDao.queryDetectionTimeline(param, shardingCode);
        } catch (Exception e) {
            log.error("查询检测时间线失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "查询检测时间线失败：" + e.getMessage());
            return result;
        }
    }

    /**
     * 确定风险等级
     */
    private String determineRiskLevel(String checkResult) {
        if (checkResult == null) return "none";

        switch (checkResult.toLowerCase()) {
            case "同管道":
            case "同光缆":
            case "high_risk":
                return "high";
            case "medium_risk":
                return "medium";
            case "low_risk":
                return "low";
            case "正常":
            case "no_risk":
                return "none";
            default:
                return "medium";
        }
    }

    /**
     * 分析风险对比结果
     */
    private JSONObject analyzeRiskComparison(JSONObject beforeDetection, JSONObject afterDetection) {
        JSONObject result = new JSONObject();

        String beforeResult = beforeDetection.getString("check_result");
        String afterResult = afterDetection.getString("check_result");
        String beforeRiskLevel = beforeDetection.getString("risk_level");
        String afterRiskLevel = afterDetection.getString("risk_level");

        // 分析对比结果
        String comparisonResult;
        double improvementScore = 50.0; // 基础分数

        if ("正常".equals(afterResult) && !"正常".equals(beforeResult)) {
            comparisonResult = "improved";
            improvementScore = 90.0;
        } else if (!"正常".equals(afterResult) && "正常".equals(beforeResult)) {
            comparisonResult = "worsened";
            improvementScore = 20.0;
        } else if (beforeResult.equals(afterResult)) {
            comparisonResult = "unchanged";
            improvementScore = 50.0;
        } else {
            // 根据风险等级变化判断
            int beforeLevel = getRiskLevelScore(beforeRiskLevel);
            int afterLevel = getRiskLevelScore(afterRiskLevel);

            if (afterLevel < beforeLevel) {
                comparisonResult = "improved";
                improvementScore = 70.0 + (beforeLevel - afterLevel) * 10;
            } else if (afterLevel > beforeLevel) {
                comparisonResult = "worsened";
                improvementScore = 30.0 - (afterLevel - beforeLevel) * 10;
            } else {
                comparisonResult = "unchanged";
                improvementScore = 50.0;
            }
        }

        // 构建变化摘要
        String riskChangeSummary = String.format("风险状态从 %s 变为 %s", beforeResult, afterResult);

        // 构建问题列表（简化版本，实际应该详细分析）
        JSONArray resolvedIssues = new JSONArray();
        JSONArray remainingIssues = new JSONArray();
        JSONArray newIssues = new JSONArray();
        JSONArray remainingIssuesList = new JSONArray();

        if (!"正常".equals(afterResult)) {
            JSONObject remainingIssue = new JSONObject();
            remainingIssue.put("issueType", afterResult);
            remainingIssue.put("issueDescription", afterDetection.getString("exception_info"));
            remainingIssue.put("riskLevel", afterRiskLevel);
            remainingIssue.put("affectedOpticalPaths", "[]");

            remainingIssues.add(remainingIssue);
            remainingIssuesList.add(remainingIssue);
        }

        if (!"正常".equals(beforeResult) && "正常".equals(afterResult)) {
            JSONObject resolvedIssue = new JSONObject();
            resolvedIssue.put("issueType", beforeResult);
            resolvedIssue.put("issueDescription", beforeDetection.getString("exception_info"));
            resolvedIssue.put("riskLevel", beforeRiskLevel);

            resolvedIssues.add(resolvedIssue);
        }

        result.put("comparisonResult", comparisonResult);
        result.put("riskChangeSummary", riskChangeSummary);
        result.put("resolvedIssues", resolvedIssues.toJSONString());
        result.put("remainingIssues", remainingIssues.toJSONString());
        result.put("newIssues", newIssues.toJSONString());
        result.put("improvementScore", improvementScore);
        result.put("resolvedCount", resolvedIssues.size());
        result.put("remainingCount", remainingIssues.size());
        result.put("newIssuesCount", newIssues.size());
        result.put("remainingIssuesList", remainingIssuesList);

        return result;
    }

    /**
     * 获取风险等级分数
     */
    private int getRiskLevelScore(String riskLevel) {
        if (riskLevel == null) return 0;

        switch (riskLevel.toLowerCase()) {
            case "high": return 3;
            case "medium": return 2;
            case "low": return 1;
            case "none": return 0;
            default: return 1;
        }
    }

    /**
     * 统一处理DataQL返回的数据格式
     * DataQL在返回单条记录时返回JSONObject，多条记录时返回JSONArray
     * 此方法统一转换为JSONArray格式
     */
    private JSONArray normalizeDataQLResult(Object dataObj) {
        JSONArray result = new JSONArray();

        if (dataObj != null) {
            if (dataObj instanceof JSONArray) {
                result = (JSONArray) dataObj;
            } else if (dataObj instanceof JSONObject) {
                result.add((JSONObject) dataObj);
            }
        }

        return result;
    }

    /**
     * 从shardingCode反推地市代码
     */
    private String getAreaCodeFromShardingCode(String shardingCode) {
        if (shardingCode == null) {
            return "无锡"; // 默认值
        }

        // 遍历NRMConstants.AREA_NAME_WLYY_DATABSE_MAP找到对应的地市
        for (Map.Entry<String, String> entry : NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.entrySet()) {
            if (shardingCode.equals(entry.getValue())) {
                // 优先返回小写的简写格式（如xz, nj等）
                String key = entry.getKey();
                if (key.length() <= 3 && key.matches("[a-z]+")) {
                    return key;
                }
            }
        }

        // 如果是标准分片代码格式，提取地市简写
        if (shardingCode.startsWith("ds_bc_o3_")) {
            return shardingCode.substring("ds_bc_o3_".length());
        }

        // 如果没找到，返回默认值
        log.warn("未找到shardingCode {} 对应的地市代码，使用默认值: wx", shardingCode);
        return "wx"; // 返回无锡的简写
    }

    /**
     * 获取隐患整改检测时间线
     */
    @Override
    public JSONObject getDetectionTimeline(JSONObject param) {
        try {
            String remediationId = param.getString("remediationId");
            String shardingCode = param.getString("shardingCode");

            if (remediationId == null || remediationId.trim().isEmpty()) {
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "缺少必要参数：remediationId");
                result.put("data", null);
                return result;
            }

            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = NRMConstants.SHARDING_GRAPH_DB;
            }

            log.info("获取隐患整改检测时间线，整改单ID: {}, 分片代码: {}", remediationId, shardingCode);

            // 直接从风险检测记录表查询，生成时间线
            JSONObject queryParam = new JSONObject();
            queryParam.put("remediationId", remediationId);
            JSONObject detectionsResult = hazardRemediationDao.queryRiskDetections(queryParam, shardingCode);

            if (!detectionsResult.getBooleanValue("success")) {
                return detectionsResult;
            }

            // 处理DataQL返回数据格式并生成时间线
            JSONArray detections = normalizeDataQLResult(detectionsResult.get("data"));
            JSONArray timeline = new JSONArray();

            // 遍历检测记录，生成时间线
            for (int i = 0; i < detections.size(); i++) {
                JSONObject detection = detections.getJSONObject(i);

                JSONObject timelineItem = new JSONObject();
                timelineItem.put("id", detection.get("id"));
                timelineItem.put("timeline_type", "detection");
                timelineItem.put("event_description", "执行风险检测 - " + detection.getString("detection_phase"));
                timelineItem.put("event_time", detection.get("detection_time"));
                timelineItem.put("operator", detection.getString("detector"));

                // 构建事件数据
                JSONObject eventData = new JSONObject();
                eventData.put("detectionType", detection.getString("detection_type"));
                eventData.put("detectionPhase", detection.getString("detection_phase"));
                eventData.put("checkResult", detection.getString("check_result"));
                eventData.put("riskLevel", detection.getString("risk_level"));
                eventData.put("opticalPathCount", detection.get("optical_path_count"));
                eventData.put("exceptionInfo", detection.getString("exception_info"));
                timelineItem.put("event_data", eventData.toJSONString());

                timeline.add(timelineItem);
            }

            log.info("生成检测时间线成功，记录数量: {}", timeline.size());

            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "查询检测时间线成功");
            result.put("data", timeline);
            return result;

        } catch (Exception e) {
            log.error("获取隐患整改检测时间线失败：{}", e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "获取检测时间线失败：" + e.getMessage());
            result.put("data", null);
            return result;
        }
    }

    /**
     * 销毁时清理线程池资源
     */
    @PreDestroy
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            log.info("正在关闭隐患整改服务线程池...");
            executorService.shutdown();
        }
    }
}
