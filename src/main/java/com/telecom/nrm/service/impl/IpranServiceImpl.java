package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.DeviceDao;
import com.telecom.nrm.dao.OTNDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.*;
import com.telecom.nrm.thread.audit.AbstractCircuitAuditService;
import com.telecom.nrm.thread.audit.AuditExcelDTO;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.*;

@Service
@Slf4j
public class IpranServiceImpl extends AbstractCircuitAuditService implements IpranService {

    public static final String IPRAN_TYPE = "108243,108240";
    @Value("${external.queryConfigDevice.url}")
    String url;
    // 递归顺序map  如不考虑link方向 可放入一个map 调整方向只需改变 递归位置
    static final List<String> RECURSION_T2B_LIST = Arrays.asList("B3_A3", "B3_A4", "B4_A3", "B4_A4", "A3_T", "A4_T");
    static final List<String> RECURSION_B12B3_LIST = Arrays.asList("B1_B3", "B1_B4", "B1_B2", "B2_B3", "B2_B4", "B3_B4");
    static final Map<String, List<String>> RECURSION_S2T_MAP = new LinkedHashMap<>();

    static {
        List<String> S_2_A = Arrays.asList("A1", "A2");
        List<String> A1_2_B = Arrays.asList("B1", "B2");
        List<String> A2_2_B = Arrays.asList("B1", "B2");
        List<String> B1_2_B3 = Arrays.asList("B2", "S", "B3");
        List<String> T_2_A = Arrays.asList("A3", "A4", "B4", "T");
        List<String> T_2_B = Arrays.asList("A3", "A4", "T");
        List<String> A3_2_T = Arrays.asList("T");


        List<String> B2_2_S = Arrays.asList("S", "B4");

        RECURSION_S2T_MAP.put("S", S_2_A);
        RECURSION_S2T_MAP.put("A1", A1_2_B);
        RECURSION_S2T_MAP.put("A2", A2_2_B);
        RECURSION_S2T_MAP.put("B1", B1_2_B3);
        RECURSION_S2T_MAP.put("B3", T_2_A);
        RECURSION_S2T_MAP.put("B4", T_2_B);
        RECURSION_S2T_MAP.put("A3", A3_2_T);
        RECURSION_S2T_MAP.put("A4", A3_2_T);
        RECURSION_S2T_MAP.put("B2", B2_2_S);


    }

    @Autowired
    MstpService mstpService;

    @Autowired
    DeviceDao deviceDao;

    @Autowired
    OTNDao otnDao;

    @Autowired
    OTNService otnService;


    @Autowired
    CircuitAnalysisService circuitAnalysisService;

    @Autowired
    CircuitAnalysisDao circuitAnalysisDao;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    ExternalInterfaceService externalInterfaceService;

    @Async("graphRestPool")
    @Override
    public void saveCache(JSONObject result, String accessCode, String areaCode, String circuitCode) {
        if (null == result || (StringUtils.isEmpty(accessCode) && StringUtils.isEmpty(circuitCode))) return;
        cn.hutool.json.JSONObject huJSON0bject = new cn.hutool.json.JSONObject(result);
        String snapshot = huJSON0bject.toJSONString(0);

        Map<String, String> query = new HashMap<>();
        query.put("contents", snapshot);
        query.put("access_code", accessCode);
        query.put("area_code", areaCode);
        query.put("circuit_code", circuitCode);
        if (null != queryCache(accessCode, areaCode)) return;
        circuitAnalysisDao.saveIpranViewCache(query, NRMConstants.SHARDING_CODE);
    }

    public void clearAccessCodeCache(String accessCode) {
        if (StringUtils.isEmpty(accessCode)) return;
        JSONObject query = new JSONObject();
        query.put("accessCode", accessCode);

        circuitAnalysisDao.clearAccessCodeCache(query, NRMConstants.SHARDING_CODE);
    }

    @Override
    public JSONObject queryCache(String accessCode, String areaCode) {
//        JSONObject query = new JSONObject();
//        query.put("access_code", accessCode);
//        query.put("area_code", areaCode);
//        try {
//            JSONObject cache = circuitAnalysisDao.queryCacheByAccessCode(query, NRMConstants.SHARDING_CODE);
//            if (null != cache && CollectionUtils.isNotEmpty(cache.getJSONArray("data"))) {
//                return JSONObject.parseObject(cache.getJSONArray("data").getJSONObject(0).getString("contents"));
//            }
//        } catch (Exception e) {
//            log.error("", e);
//        }
        return null;
    }


    @Override
    public JSONObject queryCache(JSONObject query) {
        try {
            JSONObject cache = circuitAnalysisDao.queryCacheByAccessCode(query, NRMConstants.SHARDING_CODE);
            if (null != cache && CollectionUtils.isNotEmpty(cache.getJSONArray("data"))) {
                return JSONObject.parseObject(cache.getJSONArray("data").getJSONObject(0).getString("contents"));
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    @Override
    public JSONObject ipran(JSONObject request) throws IOException {
        String all = request.getString("all");
        String accessCode = request.getString("access_code");
        String areaCode = request.getString("areaCode");
        String circuitCode = request.getString("code");
        JSONObject mstp = null;
        if (StringUtils.isNotEmpty(accessCode)) {
            JSONObject cacheResult = queryCache(accessCode, areaCode);
            if (null != cacheResult) mstp = cacheResult;
        }

        String shardingCode = "ds_bc_o3_" + areaCode.toLowerCase();
        if (mstp == null) {
//            /************本地测试代码*********/
//            Map<String, String> header = new HashMap<>();
//            String res = HttpRequestsUtils.sendPost("http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/mstp", header, request);
//            mstp = JSONObject.parseObject(res);

            mstp = mstpService.mstp(request); // 原代码
            //JSONObject mstp = super.mstpQuery(request);
            JSONArray devices = null;
            JSONArray links = null;
            boolean flag = false;
            JSONObject firstUDevice = null;
            if (null != mstp) {
                devices = mstp.getJSONArray("nodes");
                links = mstp.getJSONArray("links");
                if (CollectionUtils.isNotEmpty(devices)) {
                    for (Object device : devices) {
                        JSONObject jsonObject = (JSONObject) device;
                        firstUDevice = jsonObject;
                        String type = jsonObject.getString("network_role_id");
                        if (StringUtils.isNotEmpty(type) && IPRAN_TYPE.contains(type)) {
                            flag = true;
                            break;
                        }
                    }
                }
            }
            boolean isPoint2Point = isPoint2Point(devices);
            boolean forceGoto = false;
            Map<String, String> ips = new HashMap<>();
            List<JSONObject> ipfindeDevices = new ArrayList<>();
            Map<String, JSONObject> ipfindeDevicesMap = new HashMap<>();
            if (flag && !isPoint2Point) {
                Map<String, String> params = new HashMap<>();
                params.put("access_code", accessCode);
                String str = HttpRequestsUtils.sendGet(url, null, params);
//                JSONObject ipranTestParam = new JSONObject();
//                ipranTestParam.put("a1_ip","************");
//                ipranTestParam.put("b1_ip","***********");
//                ipranTestParam.put("b2_ip","***********");
//                ipranTestParam.put("source_ip","***********");
//                ipranTestParam.put("terminal_ip","************");
//                ipranTestParam.put("source_type","U");
//                String str = ipranTestParam.toJSONString();
                if (StringUtils.isNotEmpty(str)) {
                    JSONObject jsonObject = JSONObject.parseObject(str);
                    if (jsonObject.containsKey("error")) {
                        forceGoto = true;
                        log.error("调用接口:{} 出现错误，入参为:{} 出参为:{}", url, params, str);
                    } else {
                        boolean uFlag = "U".equals(jsonObject.getString("source_type"));
                        initIpranNetInfo(shardingCode, str, ips, ipfindeDevices, ipfindeDevicesMap, false);
                        if (CollectionUtils.isNotEmpty(ipfindeDevices) && CollectionUtils.isNotEmpty(devices) &&
                                CollectionUtils.isNotEmpty(links)) {
                            buildTree(links, devices, firstUDevice, ipfindeDevices, ipfindeDevicesMap, uFlag);

                        }
                    }

                }

            }
            if (isPoint2Point || forceGoto) {
                String str = externalInterfaceService.queryPoint2Point("", circuitCode);
                if (StringUtils.isNotEmpty(str)) {
                    initIpranNetInfo(shardingCode, str, ips, ipfindeDevices, ipfindeDevicesMap, true);
                    if (CollectionUtils.isNotEmpty(ipfindeDevices) && CollectionUtils.isNotEmpty(devices) &&
                            CollectionUtils.isNotEmpty(links)) {
                        buildTree(links, devices, ipfindeDevices, ipfindeDevicesMap);

                    }

                }
            }
//            addOTNRoute(areaCode, devices, links);
            //异步保存缓存
            saveCache(mstp, accessCode, areaCode, null);
        }


        if ("Y".equalsIgnoreCase(all)) {
            // 需要返回全程路由
            return this.allRoute(mstp, request);
        }
        return mstp;
    }

    /**
     * @param local 本地路由
     * @param param 请求参数
     * @return
     */
    private JSONObject allRoute(JSONObject local, JSONObject param) {
        String localCityCode = local.getString("cityCode");
        JSONObject route = new JSONObject();
        // 通过cjtype识别对端
        JSONObject cjTypeParam = new JSONObject();
        cjTypeParam.put("code", param.getString("code")); // 电路代号
        JSONObject cjTypeData = this.circuitAnalysisDao.queryCjtype(cjTypeParam, NRMConstants.SHARDING_GRAPH_DB);
        if (cjTypeData == null || CollectionUtils.isEmpty(cjTypeData.getJSONArray("data"))) {
            // 通过电路编号分析地市
            String currentCity = ShardingEnum.getShardingEnumByPmCode(localCityCode).getRegionName();
            String tempCode = param.getString("code").replace(currentCity, "");
            ShardingEnum shardingEnum = ShardingEnum.analysisShardingEnumByText(tempCode);
            if (shardingEnum != null) {
                JSONObject tempType = new JSONObject();
                tempType.put("citycode", shardingEnum.getPpmCode());
                cjTypeData.getJSONArray("data").add(tempType);
            } else {
                return local;
            }


        }
        // 获取另外一端

        JSONArray types = cjTypeData.getJSONArray("data");
        // 确认对端
        JSONObject other = null;
        for (int i = 0; i < types.size(); i++) {
            if (!types.getJSONObject(i).getString("citycode").equals(localCityCode)) {
                // 是对端
                String area = ShardingEnum.getShardingEnumByPmCode(types.getJSONObject(i).getString("citycode"))
                        .getBcShardingCode().replace("ds_bc_o3_", "");
                JSONObject otherParam = new JSONObject();
                otherParam.put("areaCode", area);
                otherParam.put("code", param.getString("code"));
                otherParam.put("circuit_code", param.getString("code"));

                PageResponse<JSONObject> pageResponse = custLinkInterfaceDao.getCircuitsOrder(otherParam, 10, 1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(area));
                if (pageResponse != null && pageResponse.getData() != null && pageResponse.getData().size() != 0) {
                    String access_code = pageResponse.getData().get(0).getString("access_code");
                    otherParam.put("access_code", access_code);
                    try {
                        other = this.ipran(otherParam);
                    } catch (Exception e) {
                        return local;
                    }
                }
                break;
            }
        }
        // 判断组网类型
        /**
         * 特别关注：本地混合组网、省内混合组网、跨省混合组网
         */
        JSONObject longOtnRoute = new JSONObject(); //  长途OTN路由
        String netType = types.getJSONObject(0).getString("net_type");
        if (StringUtils.isNotEmpty(netType) && netType.indexOf("混合组网") >= 0) {
            try {
                Map<String, String> header = new HashMap<>();
                JSONObject request = new JSONObject();
                request.put("code", param.getString("code"));
//                String res = HttpRequestsUtils.sendPost("http://localhost:/api/customer-topology/otn", header, request);
//                longOtnRoute = JSONObject.parseObject(res);
                longOtnRoute = otnService.queryOTNRoute(request);
                longOtnRoute = translateOtn(longOtnRoute);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // 开始合并
        if (longOtnRoute == null || CollectionUtils.isEmpty(longOtnRoute.getJSONArray("links"))) {
            return this.unionPointPoint(local, other);
        }
        // 合并本地和长途
        if (netType.equalsIgnoreCase("跨省混合组网")) {
            route = this.unitLocalLong(local, longOtnRoute, false);
        }
        if (netType.equalsIgnoreCase("本地混合组网")) {
            JSONObject longLink = this.prepareLongLink(local.getJSONArray("links")); // 找到需要扩展的那个软跳
            if (longLink == null) {
                return local;
            }
            JSONArray otnLinks = longOtnRoute.getJSONArray("links");
            JSONArray otnNodes = longOtnRoute.getJSONArray("nodes");
            JSONArray oduLinks = circuitAnalysisService.prePareOtnOduRoute(otnLinks, true);
            longLink.put("children", oduLinks);
            local.getJSONArray("nodes").addAll(otnNodes);
            return local;
        }
        if (netType.equalsIgnoreCase("省内混合组网")) {
            route = this.unitLocalLong(local, longOtnRoute, false); //
            route = this.unitLocalLong(other, route, true); //
        }


        return route;
    }

    /**
     * OTN设备转换为O3设备
     *
     * @param longOtnRoute
     * @return
     */
    private JSONObject translateOtn(JSONObject longOtnRoute) {
        JSONObject route = new JSONObject();
        JSONArray otnLinks = longOtnRoute.getJSONArray("links");
        String otnLinksContent = JSONArray.toJSONString(otnLinks);
        JSONArray otnDevices = longOtnRoute.getJSONArray("nodes");
        for (int i = 0; i < otnDevices.size(); i++) {
            String id = otnDevices.getJSONObject(i).getString("id");
            JSONObject param = new JSONObject();
            param.put("neId", id);
            JSONObject device = circuitAnalysisDao.queryResDeviceOTN(param, NRMConstants.SHARDING_GRAPH_DB);
            String resId = null;
            String city = null;
            if (device != null && CollectionUtils.isNotEmpty(device.getJSONArray("data"))) {
                resId = device.getJSONArray("data").getJSONObject(0).getString("oss_device_id");
                city = device.getJSONArray("data").getJSONObject(0).getString("city");
            }

            if (StringUtils.isNotEmpty(resId)) {
                otnLinksContent = otnLinksContent.replace(id, resId);
                otnDevices.getJSONObject(i).put("ne_id", id);
                otnDevices.getJSONObject(i).put("id", resId);
                otnDevices.getJSONObject(i).put("city", city);
                otnDevices.getJSONObject(i).put("region", ShardingEnum.getShardingEnumByRegionName(city).getPpmCode());
            }
        }
        route.put("nodes", otnDevices);
        route.put("links", JSONArray.parseArray(otnLinksContent));
        return route;
    }

    private JSONObject prepareLongLink(JSONArray links) {
        for (int i = 0; i < links.size(); i++) {
            JSONObject res = prepareLongLink(links.getJSONObject(i));
            if (res != null) {
                return res;
            }
        }
        return null;
    }

    private JSONObject prepareLongLink(JSONObject local) {

        // 找到波分设备那段软跳
        if (local.getString("spec_name").equalsIgnoreCase("软跳")) {
            if (local.getString("a_device_spec_id").equalsIgnoreCase("1022500001")
                    && local.getString("z_device_spec_id").equalsIgnoreCase("1022500001")
                    && local.getString("a_name").endsWith("-E") && local.getString("z_name").endsWith("-E")) {
                return local;
            }
        }
        JSONArray children = local.getJSONArray("children");
        if (children != null) {
            for (int i = 0; i < children.size(); i++) {
                JSONObject longLink = prepareLongLink(children.getJSONObject(i));
                if (longLink != null) {
                    return longLink;
                }
            }
        }
        return null;

    }

    private JSONObject unitLocalLong(JSONObject local, JSONObject longOtnRoute, boolean pre) {
        if (longOtnRoute == null || CollectionUtils.isEmpty(longOtnRoute.getJSONArray("links"))) {
            return local;
        }
        if (longOtnRoute == local || CollectionUtils.isEmpty(local.getJSONArray("links"))) {
            return longOtnRoute;
        }
        String localCityCode = local.getString("cityCode");
        JSONObject route = new JSONObject();
        // 跨省混合组网，将本地接到长途OTN上
        /**
         * 1. 找到OTN的起始设备
         * 2. 在本地找到该设备
         * 3. 删除本地该设备所有关系
         * 4 本身剩余接到OTN起始设备上， 
         * 5 本地端点设备和OTN设备形成连接 添加光路
         */
        JSONArray oduLinks = longOtnRoute.getJSONArray("links");
        JSONArray otnDevices = longOtnRoute.getJSONArray("nodes");
        if (!pre) {
            // 如果是被合并过的，otn已经完成分解
            oduLinks = circuitAnalysisService.prePareOtnOduRoute(oduLinks, true);
        }

        // 获取OTN两端设备，非省外设备，且同地区的
        String otnEndDeviceId = otnDevices.getJSONObject(0).getString("id");
        String otnEndDeviceName = otnDevices.getJSONObject(0).getString("name");
        ;
        if (otnDevices.getJSONObject(0).getString("name").equals("省外设备") ||
                !localCityCode.equalsIgnoreCase(otnDevices.getJSONObject(0).getString("region"))
        ) {
            otnEndDeviceId = otnDevices.getJSONObject(otnDevices.size() - 1).getString("id");
            otnEndDeviceName = otnDevices.getJSONObject(otnDevices.size() - 1).getString("name");
        }

        String endDeviceId = null; // O3的设备ID， 需要翻译， 不然页面图无法连接
        String localDeviceId = null; //本地电路和OTN连接的设备
        // 在本地找到这个设备
        // 默认第1层，0开始
        JSONArray localDevices = local.getJSONArray("nodes");
        for (int i = 0; i < localDevices.size(); i++) {
            if (localDevices.getJSONObject(i).getString("id").equals(otnEndDeviceId)) {
                endDeviceId = localDevices.getJSONObject(i).getString("id");
                break;
            }
        }
        /**
         * 以下有两种场景
         * 1、终端1-endDEVICE --- (软跳) --设备2- 终端。   该场景仅保留终端1， 链路都不需要
         * 2、终端1-设备1 --- (软跳) --endDEVICE- 终端。   该场景仅保留终端1-设备1
         */
        JSONArray localLinks = local.getJSONArray("links").getJSONObject(0).getJSONArray("children");
        String terminalDeviceId = localLinks.getJSONObject(0).getString("a_physic_device_id"); // 保存起始终端
        for (int i = localLinks.size() - 1; i >= 0; i--) {
            // 直到删除endDevice 在Z端结束
            String zDeviceId = localLinks.getJSONObject(i).getString("z_physic_device_id");
            localLinks.remove(i);
            if (zDeviceId.equalsIgnoreCase(endDeviceId)) {
                break;
            }

        }
        // 删除之后，检查终端1是否还存在，如果有


        // 在剩余的本地链路里找到连接的设备
        if (localLinks.size() > 0 && (localLinks.getJSONObject(0).getString("a_name").indexOf("终端") >= 0 ||
                localLinks.getJSONObject(0).getString("z_name").indexOf("终端") >= 0)) {
            // 在另外一头
            JSONObject last = localLinks.getJSONObject(localLinks.size() - 1);
            String lastADeviceId = last.getString("a_physic_device_id");
            String lastZDeviceId = last.getString("z_physic_device_id");
            if (localLinks.size() > 1) {
                if (localLinks.getJSONObject(localLinks.size() - 2).getString("a_physic_device_id").equalsIgnoreCase(lastADeviceId)) {
                    localDeviceId = lastZDeviceId;
                } else {
                    localDeviceId = lastADeviceId;
                }
            } else {
                if (localLinks.getJSONObject(0).getString("a_name").indexOf("终端") >= 0) {
                    localDeviceId = lastZDeviceId;
                } else {
                    localDeviceId = lastADeviceId;
                }
            }
        } else {
            // 终端
            localDeviceId = terminalDeviceId;
        }
        // 开始拼接
        route.put("nodes", local.getJSONArray("nodes"));
        route.getJSONArray("nodes").addAll(longOtnRoute.getJSONArray("nodes"));
        route.put("links", localLinks);

        JSONObject optRoadParam = new JSONObject();
        optRoadParam.put("aDeviceId", new BigDecimal(localDeviceId));
        optRoadParam.put("zDeviceId", new BigDecimal(endDeviceId));
        JSONObject optRoads = circuitAnalysisDao.queryResRouteByEndDevices(optRoadParam, ShardingEnum.getShardingEnumByPmCode(localCityCode).getBcShardingCode());
        if (optRoads != null && CollectionUtils.isNotEmpty(optRoads.getJSONArray("data"))) {
            JSONArray opts = optRoads.getJSONArray("data");
            for (int i = 0; i < opts.size(); i++) {
                opts.getJSONObject(i).put("id", opts.getJSONObject(i).getString("id"));
                opts.getJSONObject(i).put("a_physic_device_id", opts.getJSONObject(i).getString("a_physic_device_id"));
                opts.getJSONObject(i).put("z_physic_device_id", opts.getJSONObject(i).getString("z_physic_device_id"));
                opts.getJSONObject(i).put("a_port_id", opts.getJSONObject(i).getString("a_port_id"));
                opts.getJSONObject(i).put("z_port_id", opts.getJSONObject(i).getString("z_port_id"));
            }
            route.getJSONArray("links").addAll(opts);

        } else {
            JSONObject connet = new JSONObject();
            connet.put("a_physic_device_id", localDeviceId);
            connet.put("z_physic_device_id", endDeviceId);
            connet.put("spec_name", "长途连接");
            connet.put("children", new JSONArray());
            route.getJSONArray("links").add(connet);
        }


        route.getJSONArray("links").addAll(oduLinks);
        return route;
    }


    private void createLinkByAdeviceZdevice(JSONArray layer1List, JSONObject aDevice, JSONObject zDevice) {
        JSONObject link = new JSONObject();
        link.put("layer", 1);
        link.put("a_name", aDevice.getString("name"));
        link.put("a_physic_device_id", aDevice.getString("id"));
        link.put("z_name", zDevice.getString("name"));
        link.put("z_physic_device_id", zDevice.getString("id"));
        layer1List.add(link);
    }

    JSONArray getLayer1(JSONArray links) {
        for (Object link : links) {
            JSONObject jsonObject = (JSONObject) link;
            int parentLayer = jsonObject.getInteger("layer");
            if (parentLayer == 0) {
                return jsonObject.getJSONArray("children");
            }
        }
        return null;
    }

    private void buildTree(JSONArray links, JSONArray devices, List<JSONObject> ipfindeDevices, Map<String, JSONObject> ipfindeDevicesMap) {
        JSONArray layeer1 = getLayer1(links);
        JSONArray layer1List = new JSONArray();
        for (JSONObject ipfindeDevice : ipfindeDevices) {
            GraphApiServiceImpl.convertNumericToString(ipfindeDevice);
            // deviceIdMapDevice.put(ipfindeDevice.getString("id"), ipfindeDevice);
        }


        for (Map.Entry<String, List<String>> entry : RECURSION_S2T_MAP.entrySet()) {
            String key = entry.getKey();
            List<String> value = entry.getValue();
            if (ipfindeDevicesMap.containsKey(key)) {
                JSONObject aDevice = ipfindeDevicesMap.get(key);
                for (String s : value) {

                    if ((key.equals("B3") || key.equals("B4"))
                            && s.equals("T")
                            && (ipfindeDevicesMap.containsKey("A3") || ipfindeDevicesMap.containsKey("A4"))) {
                        continue;
                    }
                    if ((key.equals("B2") || key.equals("B1"))
                            && s.equals("S")
                            && (ipfindeDevicesMap.containsKey("A1") || ipfindeDevicesMap.containsKey("A2"))) {
                        continue;
                    }

                    if (ipfindeDevicesMap.containsKey(s)) {
                        JSONObject zDevice = ipfindeDevicesMap.get(s);
                        boolean isT2S = RECURSION_T2B_LIST.contains(key + "_" + s);
                        boolean bothway = RECURSION_B12B3_LIST.contains(key + "_" + s);
                        createLinkByAdeviceZdevice(layer1List, isT2S ? zDevice : aDevice, isT2S ? aDevice : zDevice);
                        if (bothway && !(key.equals("B3") || key.equals("B1"))) {
                            createLinkByAdeviceZdevice(layer1List, zDevice, aDevice);
                        }
                    }

                }
            }
        }

        if (CollectionUtils.isNotEmpty(devices) && CollectionUtils.isNotEmpty(ipfindeDevices)) {
            Set<String> originDevices = new HashSet<>();
            for (Object device : devices) {
                JSONObject jsonObject = (JSONObject) device;
                originDevices.add(jsonObject.getString("id"));
            }
            for (JSONObject ipfindeDevice : ipfindeDevices) {
                if (!originDevices.contains(ipfindeDevice.getString("id"))) {
                    ipfindeDevice.put("ipran", "Y");
                    devices.add(ipfindeDevice);
                } else {
                    // 有设备是IPRAN  但是role不是，会影响后续判断，这里添加标识
                    for (int i = 0; i < devices.size(); i++) {
                        if (devices.getJSONObject(i).getString("id").equals(ipfindeDevice.getString("id"))) {
                            devices.getJSONObject(i).put("ipran", "Y");
                        }
                    }
                }
            }

        }
        if (CollectionUtils.isNotEmpty(layer1List)) {
            layeer1.addAll(layer1List);
        }
        deleteLinkBySpec(links, devices);
    }

    void deleteLinkBySpec(JSONArray link, JSONArray device) {

        if (CollectionUtils.isEmpty(device) || CollectionUtils.isEmpty(link)) return;

        Set<String> set = new HashSet<>();
        for (Object o : device) {
            JSONObject jsonObject = (JSONObject) o;
            String type = jsonObject.getString("network_role_id");
            if (StringUtils.isNotEmpty(type) && IPRAN_TYPE.contains(type)) {
                set.add(jsonObject.getString("id"));
            }
            if ("Y".equalsIgnoreCase(((JSONObject) o).getString("ipran"))) {
                set.add(jsonObject.getString("id"));
            }
        }

        List<JSONObject> softJumplists = findSoftJumpSpec(link);
        if (CollectionUtils.isNotEmpty(softJumplists)) {
            for (JSONObject jsonObject : softJumplists) {
                String a_physic_device_id = jsonObject.getString("a_physic_device_id");
                String z_physic_device_id = jsonObject.getString("z_physic_device_id");

                if (set.contains(a_physic_device_id) && set.contains(z_physic_device_id)) {
                    String needDeleteLinkObj = jsonObject.getString("id");
                    removeById(link, needDeleteLinkObj);
                }
            }
        }

    }

    public void removeById(JSONArray jsonArray, String targetId) {
        // 从后向前遍历数组，防止删除元素后影响索引
        for (int i = jsonArray.size() - 1; i >= 0; i--) {
            JSONObject obj = jsonArray.getJSONObject(i);
            // 如果有 children 数组，递归调用
            if (obj.containsKey("children") && CollectionUtils.isNotEmpty(obj.getJSONArray("children"))) {
                JSONArray children = obj.getJSONArray("children");
                removeById(children, targetId);
            }
            // 如果当前对象的 id 等于 targetId，则从数组中删除该对象
            if (targetId.equals(obj.getString("id"))) {
                jsonArray.remove(i);
            }
        }
    }


    public List<JSONObject> findSoftJumpSpec(JSONArray jsonArray, Set<String> visited) {
        List<JSONObject> result = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject obj = jsonArray.getJSONObject(i);
            // 获取对象唯一标识，这里假定 "id" 字段可作为唯一标识
            String id = obj.getString("id");
            if (StringUtils.isEmpty(id) ) {
                continue;
            }
            // 如果该对象已经处理过，则跳过以避免循环引用
            if (!id.isEmpty() && visited.contains(id)) {
                continue;
            }
            if (!id.isEmpty()) {
                visited.add(id);
            }
            // 如果当前对象的 spec_name 为 "软跳"，则加入结果列表
            if ("软跳".equals(obj.getString("spec_name"))) {
                result.add(obj);
            }
            // 如果当前对象的 spec_name 为 "软跳"，则加入结果列表
            if ("硬跳".equals(obj.getString("spec_name"))) {
                result.add(obj);
            }
            // 如果对象中有 children 字段，则递归查找
            if (obj.containsKey("children") && CollectionUtils.isNotEmpty(obj.getJSONArray("children"))) {
                JSONArray children = obj.getJSONArray("children");
                result.addAll(findSoftJumpSpec(children, visited));
            }
        }
        return result;
    }

    public List<JSONObject> findSoftJumpSpec(JSONArray jsonArray) {
        return findSoftJumpSpec(jsonArray, new HashSet<>());
    }


    boolean isPoint2Point(JSONArray devices) {
        if (CollectionUtils.isEmpty(devices)) return false;
        int cout = 0;
        for (Object device : devices) {
            JSONObject jsonObject = (JSONObject) device;
            String type = jsonObject.getString("network_role_id");
            if (StringUtils.isNotEmpty(type) && IPRAN_TYPE.contains(type)) {
                cout++;
            }
        }
        return cout > 1;

    }

    /**
     * 将数字电路等点到点的路由合并
     *
     * @param aResult
     * @param zResult
     * @return
     */
    @Override
    public JSONObject unionPointPoint(JSONObject aResult, JSONObject zResult) {
        /**
         * 方案：对第0层路由进行合并
         * 1. 确认两个路由靠近长途站点的终端
         * 2. 两个长途站点的终端合并
         */
        if (zResult == null || CollectionUtils.isEmpty(zResult.getJSONArray("nodes"))) {
            return aResult;
        }


        JSONObject result = new JSONObject();
        result.put("links", new JSONArray());
        result.put("nodes", aResult.getJSONArray("nodes"));
        result.getJSONArray("nodes").addAll(zResult.getJSONArray("nodes"));
        JSONObject aCfsNode = aResult.getJSONObject("cfs");
        JSONObject zCfsNode = zResult.getJSONObject("cfs");
        String aAreaId = aResult.getString("areaId");
        String zAreaId = zResult.getString("areaId");
        if (StringUtils.isEmpty(zAreaId) || zCfsNode == null || CollectionUtils.isEmpty(zCfsNode.getJSONArray("data"))) {
            return null;
        }
        if (zAreaId.equals(aAreaId)) {
            return null;
        }
        String aCityCode = aResult.getString("cityCode");
        String zCityCode = zResult.getString("cityCode");
        String aArea = ShardingEnum.getShardingEnumByPmCode(aCityCode).getRegionName();
        String zArea = ShardingEnum.getShardingEnumByPmCode(aCityCode).getRegionName();
        String aAddressName = aCfsNode.getJSONArray("data").getJSONObject(0).getString("a_address_name");
        String aZddressName = aCfsNode.getJSONArray("data").getJSONObject(0).getString("z_address_name");
        String zAddressName = zCfsNode.getJSONArray("data").getJSONObject(0).getString("a_address_name");
        String zZddressName = zCfsNode.getJSONArray("data").getJSONObject(0).getString("z_address_name");
        if (StringUtils.isEmpty(aZddressName) || StringUtils.isEmpty(zZddressName)) {
            return null;
        }
        com.telecom.nrm.domain.graph.Node aNode = null;
        com.telecom.nrm.domain.graph.Node zNode = null;
        JSONObject virtualLink = new JSONObject();
        /**
         *
         * {
         *   "service_type_id": 81708243,
         *   "code": "南京无锡ETN0352NP",
         *   "z_port_spec_id": 1041192019,
         *   "a_port_id": "321101840000000054065596",
         *   "a_port_spec_id": 1041192019,
         *   "a_physic_device_id": "321102500000000083154797",
         *   "children_num": 9,
         *   "z_name": "数字电路用户终端（以太网电口）-46A79333",
         *   "layer": 0,
         *   "service_type_name": "MSTP电路",
         *   "a_port_code": "LAN1",
         *   "z_physic_device_id": "321102500000000091695181",
         *   "z_port_code": "LAN1",
         *   "children":[],
         *   "spec_id": 1132100021,
         *   "name": "南京无锡ETN0352NP",
         *   "z_port_id": "321101840000000069984067",
         *   "id": "321102060000000078617712",
         *   "spec_name": "以太网链路",
         *   "a_name": "数字电路用户终端（以太网电口）-A1D6C475"
         * }
         */
        virtualLink.put("id", new SecureRandom().nextLong());
        virtualLink.put("layer", 0);
        virtualLink.put("spec_name", "长途连接");
        virtualLink.put("code", "长途");
        virtualLink.put("name", "长途");
        virtualLink.put("virtual", "Y");
        virtualLink.put("a_physic_device_id", null);
        virtualLink.put("z_physic_device_id", null);

        if (aZddressName.indexOf(aArea) < 0 || aZddressName.indexOf("外地") >= 0) { // aResult是正向
            result.getJSONArray("links").addAll(aResult.getJSONArray("links"));
            virtualLink.put("a_physic_device_id", aResult.getJSONArray("links").getJSONObject(0).getString("z_physic_device_id"));
            if (zAddressName.indexOf(zArea) < 0 || zAddressName.indexOf("外地") >= 0) {
                virtualLink.put("z_physic_device_id", zResult.getJSONArray("links").getJSONObject(0).getString("a_physic_device_id"));
            } else {
                virtualLink.put("z_physic_device_id", aResult.getJSONArray("links").getJSONObject(0).getString("z_physic_device_id"));

            }
            result.getJSONArray("links").add(virtualLink);
            result.getJSONArray("links").addAll(zResult.getJSONArray("links"));

        } else {

            result.getJSONArray("links").addAll(zResult.getJSONArray("links"));
            virtualLink.put("z_physic_device_id", aResult.getJSONArray("links").getJSONObject(0).getString("a_physic_device_id"));
            if (zAddressName.indexOf(zArea) < 0 || zAddressName.indexOf("外地") >= 0) {
                virtualLink.put("a_physic_device_id", zResult.getJSONArray("links").getJSONObject(0).getString("a_physic_device_id"));
            } else {
                virtualLink.put("a_physic_device_id", aResult.getJSONArray("links").getJSONObject(0).getString("z_physic_device_id"));
            }
            result.getJSONArray("links").add(virtualLink);
            result.getJSONArray("links").addAll(aResult.getJSONArray("links"));
        }
        return result;
    }

    public void addOTNRoute(String areaCode, JSONArray devices, JSONArray links) {
        if (CollectionUtils.isNotEmpty(links)) {
            JSONObject client = findClientRec(links);
            if (null != client) {
                String code = client.getString("nm_code");
                JSONObject circuitCodeJson = otnDao.getCircuitByCode(code, NRMConstants.SHARDING_GRAPH_DB);
                String circuitCode = circuitCodeJson.getString("result");
                if (StringUtils.isNotEmpty(circuitCode)) {
                    JSONObject otnQuery = new JSONObject();
                    otnQuery.put("code", circuitCode);
                    otnQuery.put("areaCode", areaCode);
                    JSONObject otnResult = otnService.queryOTNRoute(otnQuery);
                    if (null != otnResult) {
                        JSONArray otnDevice = otnResult.getJSONArray("nodes");
                        JSONArray otnLinks = otnResult.getJSONArray("links");
                        if (CollectionUtils.isNotEmpty(otnLinks)) {
                            int startLayer = client.getInteger("layer");
                            for (Object otnLink : otnLinks) {
                                JSONObject jsonObject = (JSONObject) otnLink;
                                jsonObject.put("layer", startLayer + 1);
                                changNodeLayerRec(jsonObject);
                            }
                            //实际上只替换了头和尾
                            changOTNNodeIdRec(otnLinks, otnDevice, devices);
                            client.put("children", otnLinks);
                        }
                        if (CollectionUtils.isNotEmpty(otnDevice) && CollectionUtils.isNotEmpty(devices)) {
                            devices.addAll(otnDevice);
                        }
                    }

                }

            }
        }
    }

    public void changOTNNodeIdRec(JSONArray otnLinks, JSONArray otnDevice, JSONArray mstpNodes) {

        if (CollectionUtils.isEmpty(otnLinks) || CollectionUtils.isEmpty(mstpNodes) ||
                CollectionUtils.isEmpty(otnDevice)) {
            return;
        }
        Map<String, JSONObject> otnIdMapSelf = new HashMap<>();
        for (Object o : otnDevice) {
            JSONObject jsonObject = (JSONObject) o;
            otnIdMapSelf.put(jsonObject.getString("id"), jsonObject);
        }
        Map<String, String> mstpCodeMapId = new HashMap<>();
        for (Object o : mstpNodes) {
            JSONObject jsonObject = (JSONObject) o;
            mstpCodeMapId.put(jsonObject.getString("code"), jsonObject.getString("id"));
        }
        for (Object otnLink : otnLinks) {
            JSONObject jsonObject = (JSONObject) otnLink;
            String aPhysicDeviceId = jsonObject.getString("a_physic_device_id");
            String zPhysicDeviceId = jsonObject.getString("z_physic_device_id");
            JSONArray children = jsonObject.getJSONArray("children");
            if (StringUtils.isNotEmpty(aPhysicDeviceId)) {
                if (otnIdMapSelf.containsKey(aPhysicDeviceId)) {
                    String code = otnIdMapSelf.get(aPhysicDeviceId).getString("code");
                    if (StringUtils.isNotEmpty(code)) {
                        if (mstpCodeMapId.containsKey(code)) {
                            jsonObject.put("a_physic_device_id", mstpCodeMapId.get(code));
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(zPhysicDeviceId)) {
                if (otnIdMapSelf.containsKey(aPhysicDeviceId)) {
                    String code = otnIdMapSelf.get(zPhysicDeviceId).getString("code");
                    if (StringUtils.isNotEmpty(code)) {
                        if (mstpCodeMapId.containsKey(code)) {
                            jsonObject.put("z_physic_device_id", mstpCodeMapId.get(code));
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(children)) {
                changOTNNodeIdRec(children, otnDevice, mstpNodes);
            }
        }

    }

    private void initIpranNetInfo(String shardingCode, String str, Map<String, String> ips,
                                  List<JSONObject> ipranDevice, Map<String, JSONObject> ipDevicesMap, boolean isPoint2Point) {
        if (isPoint2Point) {
            JSONObject result = JSONObject.parseObject(str);
            String a1_ip = result.getString("a1_ip");
            String a2_ip = result.getString("a2_ip");
            String a3_ip = result.getString("a3_ip");
            String a4_ip = result.getString("a4_ip");
            String b1_ip = result.getString("b1_ip");
            String b2_ip = result.getString("b2_ip");
            String b3_ip = result.getString("b3_ip");
            String b4_ip = result.getString("b4_ip");
            String source_ip = result.getString("source_ip");
            String terminal_ip = result.getString("terminal_ip");
            ips.put("A1", a1_ip);
            ips.put("A2", a2_ip);
            ips.put("A3", a3_ip);
            ips.put("A4", a4_ip);
            ips.put("B1", b1_ip);
            ips.put("B2", b2_ip);
            ips.put("B3", b3_ip);
            ips.put("B4", b4_ip);
            ips.put("S", source_ip);
            ips.put("T", terminal_ip);
        } else {
            JSONObject result = JSONObject.parseObject(str);
            String b1_ip = result.getString("b1_ip");
            String b2_ip = result.getString("b2_ip");
            String source_ip = result.getString("source_ip");
            String a_sbr = result.getString("terminal_ip");
            String a1_ip = result.getString("a1_ip");
            ips.put("b1_ip", b1_ip);
            ips.put("b2_ip", b2_ip);
            ips.put("source_ip", source_ip);
            ips.put("terminal_ip", a_sbr);
            ips.put("a1_bussiness_ip", a1_ip);
        }
        ips.forEach((k, v) -> {
            if (StringUtils.isNotEmpty(v)) {
                JSONObject query = new JSONObject();
                query.put("code", v);
                JSONObject ipFindDevice = deviceDao.queryNodeDeviceByIp(query, shardingCode);
                if (null != ipFindDevice) {
                    JSONObject data = ipFindDevice.getJSONArray("data").getJSONObject(0);
                    data.put("ipran", "Y");
                    ipDevicesMap.put(k, data);
                    ipranDevice.add(data);
                }
            }
        });
    }

    void buildTree(JSONArray links, JSONArray originalDevice, JSONObject firstUDevice, List<JSONObject> ipfindeDevices,
                   Map<String, JSONObject> ipfindeDevicesMap, boolean uflag) {
        boolean flag = ipfindeDevices.stream().anyMatch(e -> e.getString("id").equals(firstUDevice.getString("id")));


        originalDevice.addAll(ipfindeDevices);
        ipfindeDevicesMap.put("first", firstUDevice);
        ipfindeDevices.add(firstUDevice);
        Map<String, JSONObject> deviceIdMapDevice = new HashMap<>();
        for (JSONObject ipfindeDevice : ipfindeDevices) {
            GraphApiServiceImpl.convertNumericToString(ipfindeDevice);
            deviceIdMapDevice.put(ipfindeDevice.getString("id"), ipfindeDevice);
        }
        List<Node> deviceLinks = new ArrayList<>();
        Node terminal_2_b1 = new Node(ipfindeDevicesMap.get("terminal_ip").getString("id"), ipfindeDevicesMap.get("b1_ip").getString("id"));
        Node terminal_2_b2 = new Node(ipfindeDevicesMap.get("terminal_ip").getString("id"), ipfindeDevicesMap.get("b2_ip").getString("id"));
        if (uflag) {
            Node b1_2_a1 = new Node(ipfindeDevicesMap.get("b1_ip").getString("id"), ipfindeDevicesMap.get("a1_bussiness_ip").getString("id"));
            Node b2_2_a1 = new Node(ipfindeDevicesMap.get("b2_ip").getString("id"), ipfindeDevicesMap.get("a1_bussiness_ip").getString("id"));
            Node a1_2_source = new Node(ipfindeDevicesMap.get("a1_bussiness_ip").getString("id"), ipfindeDevicesMap.get("source_ip").getString("id"));
            deviceLinks.add(b1_2_a1);
            deviceLinks.add(b2_2_a1);
            deviceLinks.add(a1_2_source);
        } else {
            Node b1_2_a = new Node(ipfindeDevicesMap.get("b1_ip").getString("id"), ipfindeDevicesMap.get("source_ip").getString("id"));
            Node b2_2_a = new Node(ipfindeDevicesMap.get("b2_ip").getString("id"), ipfindeDevicesMap.get("source_ip").getString("id"));
            deviceLinks.add(b1_2_a);
            deviceLinks.add(b2_2_a);
            if (!flag) {
                Node a_ip2first = new Node(ipfindeDevicesMap.get("source_ip").getString("id"), ipfindeDevicesMap.get("first").getString("id"));
                deviceLinks.add(a_ip2first);
            }
        }
        deviceLinks.add(terminal_2_b1);
        deviceLinks.add(terminal_2_b2);
        for (Node deviceLink : deviceLinks) {
            String id = deviceLink.getId();
            String parent = deviceLink.getParent();
            JSONObject link = new JSONObject();
            JSONObject zDevice = deviceIdMapDevice.get(id);
            JSONObject aDevice = deviceIdMapDevice.get(parent);
            link.put("layer", 0);
            link.put("a_name", aDevice.getString("name"));
            link.put("a_physic_device_id", aDevice.getString("id"));

            link.put("z_name", zDevice.getString("name"));
            link.put("z_physic_device_id", zDevice.getString("id"));
            links.add(link);
        }
    }

    @Override
    public List<AuditExcelDTO> call() throws Exception {
        return null;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Node {
        String id;
        String parent;
    }


    public static void changNodeLayerRec(JSONObject link) {
        int parentLayer = link.getInteger("layer");
        JSONArray children = link.getJSONArray("children");
        if (children != null) {
            for (int i = 0; i < children.size(); i++) {
                JSONObject child = children.getJSONObject(i);
                child.put("layer", parentLayer + 1);
                changNodeLayerRec(child);
            }
        }
    }

    public static JSONObject findClientRec(JSONArray links) {
        for (Object link : links) {
            JSONObject jsonObject = (JSONObject) link;
            String specName = jsonObject.getString("spec_name");
            if ("CLIENT链路".equals(specName)) {
                return jsonObject;
            } else {
                JSONArray children = jsonObject.getJSONArray("children");
                if (CollectionUtils.isNotEmpty(children)) {
                    return findClientRec(children);
                }

            }

        }
        return null;
    }


}
