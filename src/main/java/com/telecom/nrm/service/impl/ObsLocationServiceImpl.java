package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.simpleworkflow.flow.core.TryCatch;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.ObsLocationDao;
import com.telecom.nrm.domain.Device;
import com.telecom.nrm.domain.FiberCable;
import com.telecom.nrm.domain.Link;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ObsLocationServiceImpl implements ObsLocationService {

    @Autowired
    MstpService mstpService;

    @Autowired
    private ObsLocationDao obsLocationDao;

    @Autowired
    PONService ponService;

    @Autowired
    GraphService graphService;

    @Autowired
    GraphApiService graphApiService;

    public ArrayList<FiberCable> sortFiberCableSegment(List<FiberCable> fiberCableSegments, String startDeviceID, String sharingCode){
        boolean bool = true;
        ArrayList<FiberCable> sortedFiberCableSegment = new ArrayList<>();
        while (!fiberCableSegments.isEmpty() && bool){
            for (int i = 0; i < fiberCableSegments.size(); i++) {
                FiberCable fiberCableSegment = fiberCableSegments.get(i);
                if (fiberCableSegment.getAphysicDeviceId().equals(startDeviceID)){
                    sortedFiberCableSegment.add(fiberCableSegment);
                    startDeviceID = fiberCableSegment.getZphysicDeviceId();
                    fiberCableSegments.remove(i);
                    break;
                } else if (fiberCableSegment.getZphysicDeviceId().equals(startDeviceID)){
                    sortedFiberCableSegment.add(fiberCableSegment);
                    startDeviceID = fiberCableSegment.getAphysicDeviceId();
                    fiberCableSegments.remove(i);
                    break;
                }
                if (i == fiberCableSegments.size() - 1){
                    bool = false;
                    break;
                }
            }
        }
        return sortedFiberCableSegment;
    }

    //根据传入的局向光纤和起始设备排序光缆段
    public ArrayList<FiberCable> sortFiberCable(List<Link> fiberLinks, String startDeviceID, String sharingCode){
        boolean bool = true;
        ArrayList<FiberCable> sortedFiberCableSegment = new ArrayList<>();
        Device opticalFacility = obsLocationDao.queryOpticalFacility(startDeviceID, sharingCode);
        //增加双纤光猫连接的光设施查询
        if (ObjectUtil.isEmpty(opticalFacility)){
            opticalFacility = obsLocationDao.queryDuplexOpticalFacility(startDeviceID, sharingCode);
        }
        String startOpticalDeviceID = "-1";
        String startFacilityID = "-1";
        if (ObjectUtil.isEmpty(opticalFacility)) {
            bool = false;
        } else {
            startOpticalDeviceID = ObjectUtil.isEmpty(opticalFacility.getId()) ? "-1" : opticalFacility.getId();
            startFacilityID = ObjectUtil.isEmpty(opticalFacility.getFacilityID()) ? "-1" : opticalFacility.getFacilityID();
        }
        while (!fiberLinks.isEmpty() && bool) {
            for (int i = 0; i < fiberLinks.size(); i++) {
                Link link = fiberLinks.get(i);
                if (startOpticalDeviceID.equals(link.getAphysicDeviceId())
                        || startFacilityID.equals(link.getAdeviceFacilityID())){
                    PageResponse<FiberCable> fiberCableSegmentsResp = obsLocationDao.queryFiberCableSegment(link.getId(), 10, 0, sharingCode);
                    List<FiberCable> fiberCableSegments = fiberCableSegmentsResp.getData();
                    ArrayList<FiberCable> sorted = sortFiberCableSegment(fiberCableSegments, link.getAphysicDeviceId(), sharingCode);
                    sortedFiberCableSegment.addAll(sorted);
                    startFacilityID = ObjectUtil.isEmpty(link.getZdeviceFacilityID()) ? "-1" : link.getZdeviceFacilityID();
                    startOpticalDeviceID = ObjectUtil.isEmpty(link.getZphysicDeviceId()) ? "-1" : link.getZphysicDeviceId();
                    fiberLinks.remove(i);
                    break;
                } else if (startOpticalDeviceID.equals(link.getZphysicDeviceId())
                        || startFacilityID.equals(link.getZdeviceFacilityID())) {
                    PageResponse<FiberCable> fiberCableSegmentsResp = obsLocationDao.queryFiberCableSegment(link.getId(), 10, 0, sharingCode);
                    List<FiberCable> fiberCableSegments = fiberCableSegmentsResp.getData();
                    ArrayList<FiberCable> sorted = sortFiberCableSegment(fiberCableSegments, link.getZphysicDeviceId(), sharingCode);
                    sortedFiberCableSegment.addAll(sorted);
                    startFacilityID = ObjectUtil.isEmpty(link.getAdeviceFacilityID()) ? "-1" : link.getAdeviceFacilityID();
                    startOpticalDeviceID = ObjectUtil.isEmpty(link.getAphysicDeviceId()) ? "-1" : link.getAphysicDeviceId();
                    fiberLinks.remove(i);
                    break;
                }
                if (i == fiberLinks.size() - 1){
                    bool = false;
                    break;
                }
            }
        }
        if (bool == false){
            for (int i = 0; i < fiberLinks.size(); i++) {
                Link link = fiberLinks.get(i);
                FiberCable fiberCableResp = obsLocationDao.queryFiberCable(link.getId(), sharingCode);
                if (!ObjectUtil.isEmpty(fiberCableResp)) {
                    sortedFiberCableSegment.add(fiberCableResp);
                }
            }
        }
        return sortedFiberCableSegment;
    }

    public List<ArrayList> sortDeviceByStartDevice(List<Link> fiberRouteLinks, String startDeviceID, String sharingCode){
        boolean bool = true;
        ArrayList<Device> sortedDevices = new ArrayList<>();
        ArrayList<FiberCable> sortedFiberCableSegment = new ArrayList<>();
        List<ArrayList> res = new ArrayList<>();
        while (!fiberRouteLinks.isEmpty() && bool){
            for (int i = 0; i < fiberRouteLinks.size(); i++) {
                Link curLink = fiberRouteLinks.get(i);
                if (curLink.getAphysicDeviceId().equals(startDeviceID)){
                    JSONObject param = new JSONObject();
                    param.put("device_id", curLink.getZphysicDeviceId());
                    param.put("port_id", curLink.getZportId());
                    Device device = obsLocationDao.queryDevice(param, sharingCode);
                    sortedDevices.add(device);
                    PageResponse<Link> fiberLinkResp = obsLocationDao.queryFiber(curLink.getId(), 5, 0, sharingCode);
                    List<Link> fiberLinks = fiberLinkResp.getData();
                    ArrayList<FiberCable> links = sortFiberCable(fiberLinks, startDeviceID, sharingCode);
                    sortedFiberCableSegment.addAll(links);
                    startDeviceID = curLink.getZphysicDeviceId();
                    fiberRouteLinks.remove(i);
                    break;
                } else if (curLink.getZphysicDeviceId().equals(startDeviceID)){
                    JSONObject param = new JSONObject();
                    param.put("device_id", curLink.getAphysicDeviceId());
                    param.put("port_id", curLink.getAportId());
                    Device device = obsLocationDao.queryDevice(param, sharingCode);
                    sortedDevices.add(device);
                    PageResponse<Link> fiberLinkResp = obsLocationDao.queryFiber(curLink.getId(), 5, 0, sharingCode);
                    List<Link> fiberLinks = fiberLinkResp.getData();
                    ArrayList<FiberCable> links = sortFiberCable(fiberLinks, startDeviceID, sharingCode);
                    sortedFiberCableSegment.addAll(links);
                    startDeviceID = curLink.getAphysicDeviceId();
                    fiberRouteLinks.remove(i);
                    break;
                }
                if (i == fiberRouteLinks.size() - 1){
                    bool = false;
                    break;
                }
            }
        }
        res.add(sortedDevices);
        res.add(sortedFiberCableSegment);
        return res;
    }

    public List<ArrayList> sortDeviceAndFiberSegmentByAccessCode(String accessCode, String sharingCode) {
        List<ArrayList> resp = new ArrayList<>();
        Link ponLine = obsLocationDao.queryPonLine(accessCode, sharingCode);
        //增加双纤的光链路路由查询
        if (ObjectUtil.isEmpty(ponLine)){
            Link ethernetLine = obsLocationDao.queryEthernetLine(accessCode, sharingCode);
            if (ObjectUtil.isEmpty(ethernetLine)) {
                return resp;
            }
            PageResponse<Link> routeByAccessCode = obsLocationDao.getRouteByDuplexAccessCode(ethernetLine.getId(), 10, 0, sharingCode);
            List<Link> duplexRoutes = routeByAccessCode.getData();
            List<Link> duplexAllLinks = new ArrayList<>();
            HashSet<String> linkCode = new HashSet<>();
            for (int i = 0; i < duplexRoutes.size(); i++) {
                Link tem = duplexRoutes.get(i);
                if (linkCode.contains(tem.getCode())) {
                    continue;
                } else {
                    duplexAllLinks.add(tem);
                    linkCode.add(tem.getCode());
                }
            }
            String startDeviceID = obsLocationDao.getStartPC(ethernetLine, sharingCode);
//            String startDeviceID = ethernetLine.getZphysicDeviceId();
            Link opticalTransform = obsLocationDao.queryOpticalTransform(startDeviceID, sharingCode);
            if (!ObjectUtil.isEmpty(opticalTransform)) {
                if (startDeviceID.equals(opticalTransform.getAphysicDeviceId())) {
                    resp = sortDeviceByStartDevice(duplexAllLinks, opticalTransform.getZphysicDeviceId(), sharingCode);
                } else if (startDeviceID.equals(opticalTransform.getZphysicDeviceId())) {
                    resp = sortDeviceByStartDevice(duplexAllLinks, opticalTransform.getAphysicDeviceId(), sharingCode);
                }
            }
            return resp;
        }
        PageResponse<Link> fiberRouteLink = obsLocationDao.queryRoute(ponLine.getId(), 10, 0, sharingCode);
        List<Link> allFiberRouteLinks = fiberRouteLink.getData();
//        if (ObjectUtil.isEmpty(ponLine)) {
//            return resp;
//        }
        List<Link> allLinks = new ArrayList<>();
        allLinks.addAll(allFiberRouteLinks);
        resp = sortDeviceByStartDevice(allLinks, ponLine.getZphysicDeviceId(), sharingCode);
        return resp;
    }
    //allAccesscodeResouceID的key是接入号，值为光缆清单
    public HashMap<HashSet<String>, HashSet<String>> getFiberCableSegment(HashMap<String, ArrayList<String>> allAccesscodeResouceID) {
        HashMap<HashSet<String>, HashSet<String>> sameResource = new HashMap<>();
        HashMap<String, Boolean> used = new HashMap<>();
        for (Map.Entry<String, ArrayList<String>> preEntry : allAccesscodeResouceID.entrySet()) {
            if (used.get(preEntry.getKey()) != null) {
                continue;
            }
            HashSet<String> preSet = new HashSet<>();
            preSet.addAll(preEntry.getValue());
            HashSet<String> sameAccessCodes = new HashSet<>();
            sameAccessCodes.add(preEntry.getKey());
            used.put(preEntry.getKey(), true);
            for (Map.Entry<String, ArrayList<String>> nextEntry : allAccesscodeResouceID.entrySet()) {
                if (used.get(nextEntry.getKey()) != null){
                    continue;
                }
                HashSet<String> nextSet = new HashSet<>();
                nextSet.addAll(nextEntry.getValue());
                HashSet<String> tem = new HashSet<>();
                tem.addAll(preSet);
                tem.retainAll(nextSet);
                if (ObjectUtil.isEmpty(tem)) {
                    continue;
                }
                sameAccessCodes.add(nextEntry.getKey());
                preSet = tem;
                used.put(nextEntry.getKey(), true);
            }
            sameResource.put(sameAccessCodes, preSet);
        }
        //(资源一样的接入号set，一样的资源清单set)
        return sameResource;
    }

    public HashMap<HashSet<String>, HashSet<String>> getSameDevice(HashMap<String, ArrayList<String>> allAccesscodeResouceID, HashMap<String, Device> allAccesscodeOLTDevice) {
        HashMap<HashSet<String>, HashSet<String>> sameResource = new HashMap<>();
        HashMap<String, Boolean> used = new HashMap<>();
        for (Map.Entry<String, ArrayList<String>> preEntry : allAccesscodeResouceID.entrySet()) {
            if (used.get(preEntry.getKey()) != null) {
                continue;
            }
            HashSet<String> preSet = new HashSet<>();
            preSet.addAll(preEntry.getValue());
            HashSet<String> sameAccessCodes = new HashSet<>();
            sameAccessCodes.add(preEntry.getKey());
            used.put(preEntry.getKey(), true);
            for (Map.Entry<String, ArrayList<String>> nextEntry : allAccesscodeResouceID.entrySet()) {
                if (used.get(nextEntry.getKey()) != null){
                    continue;
                }
                HashSet<String> nextSet = new HashSet<>();
                nextSet.addAll(nextEntry.getValue());
                HashSet<String> tem = new HashSet<>();
                tem.addAll(preSet);
                tem.retainAll(nextSet);
                //增加一个设备端口判断条件，如果判定的相同设备是olt设备，再判断一次pon口是否同一个，是同一个pon口设备再归并为共设备接入号
                //入参增加一个接入号和PON设备的map
                Device preOLT = allAccesscodeOLTDevice.get(preEntry.getKey());
                Device nextOLT = allAccesscodeOLTDevice.get(nextEntry.getKey());
                if (!ObjectUtil.isEmpty(preOLT) && !ObjectUtil.isEmpty(nextOLT)) {
                    if (!preOLT.getPortID().equals(nextOLT.getPortID())) {
                        tem.remove(preOLT.getId());
                    }
                }
                if (ObjectUtil.isEmpty(tem)) {
                    continue;
                }
                sameAccessCodes.add(nextEntry.getKey());
                preSet = tem;
                used.put(nextEntry.getKey(), true);
            }
            sameResource.put(sameAccessCodes, preSet);
        }
        return sameResource;
    }

    public void getLastDevice(HashMap<HashSet<String>, HashSet<String>> sameAccesscodeDevices, HashMap<HashSet<String>, Device> resAccessCodeDevices, HashMap<String, ArrayList<Device>> allAccessCodeDevices) {
        for (Map.Entry<HashSet<String>, HashSet<String>> cur : sameAccesscodeDevices.entrySet()) {
            HashSet<String> key = cur.getKey();
            HashSet<String> value = cur.getValue();
            if (key.isEmpty()) {
                continue;
            }
            //acc为接入号
            for (String acc : key) {
                ArrayList<Device> devices = allAccessCodeDevices.get(acc);
                HashMap<String, Integer> order = new HashMap<>();
                for (int i = 0; i < devices.size(); i++) {
                    order.put(devices.get(i).getId(), i);
                }
                int tag = Integer.MAX_VALUE;
                for (String id : value) {
                    if (order.get(id) < tag) {
                        tag  = order.get(id);
                    }
                }
                if (tag < devices.size()) {
                    resAccessCodeDevices.put(key, devices.get(tag));
                }
                break;
            }
        }
    }

    public void getLastSegment(HashMap<HashSet<String>, HashSet<String>> sameAccesscodeSegments, HashMap<HashSet<String>, FiberCable> resAccessCodeFiberCableSegments, HashMap<String, ArrayList<FiberCable>> allAccessCodeFiberCableSegments) {
        for (Map.Entry<HashSet<String>, HashSet<String>> cur : sameAccesscodeSegments.entrySet()) {
            HashSet<String> key = cur.getKey();  //一样的接入号清单set
            HashSet<String> value = cur.getValue();   //一样的资源清单set
            if (key.isEmpty()) {
                continue;
            }
            //acc为接入号
            for (String acc : key) {
                ArrayList<FiberCable> segments = allAccessCodeFiberCableSegments.get(acc);
                HashMap<String, Integer> order = new HashMap<>();
                for (int i = 0; i < segments.size(); i++) {
                    order.put(segments.get(i).getId(), i);
                    //光缆段ID，序号
                }
                int tag = Integer.MAX_VALUE;
                for (String id : value) {
                    if (order.get(id) < tag) {
                        tag  = order.get(id);
                    }
                }
                //tag一直往前移动
                if (tag < segments.size()) {
                    resAccessCodeFiberCableSegments.put(key, segments.get(tag));
                }
                break;
            }
        }
    }

    public void recurCircuits(JSONArray mstpCircuit, Set<String> fiberRouteID, Set<String> fiberRouteCode){
        for (int i = 0; i < mstpCircuit.size(); i++) {
            JSONObject jsonObject = mstpCircuit.getJSONObject(i);
            String code = jsonObject.getString("code");
            if ("1131200002".equals(jsonObject.getString("spec_id")) && !fiberRouteCode.contains(code)) {
                fiberRouteCode.add(code);
                fiberRouteID.add(jsonObject.getString("id"));
                continue;
            } else if (!ObjectUtil.isEmpty(jsonObject.getJSONArray("children"))) {
                recurCircuits(jsonObject.getJSONArray("children"), fiberRouteID, fiberRouteCode);
            }
        }
    }

    @Override
    public JSONObject queryObstaclepoints(List<String> accessCodes, String sharingCode,Boolean isGroup, String city) {
        HashMap<String, ArrayList<Device>> allAccessCodeDevices = new HashMap<>();
        HashMap<String, ArrayList<FiberCable>> allAccessCodeFiberCableSegments = new HashMap<>();
        HashMap<HashSet<String>, Device> resAccessCodeDevices = new HashMap<>();
        HashMap<HashSet<String>, FiberCable> resAccessCodeFiberCableSegments = new HashMap<>();
        HashMap<String, ArrayList<String>> allAccessCodeDeviceID = new HashMap<>();
        HashMap<String, ArrayList<String>> allAccessCodeFiberCableSegmentID = new HashMap<>();
        HashMap<String, String> circuitSegmentIDAccessCode = new HashMap<>();
        HashMap<String, ArrayList<String>> allAccessCodeFiberCableSegmentCode = new HashMap<>();

        HashMap<String, Device> allAccessCodeOLTDevice = new HashMap<>();
        JSONObject result = new JSONObject();

        for (int i = 0; i < accessCodes.size(); i++) {
            String accessCode = accessCodes.get(i);
            //如果不是互联网专线或宽带，走单独的查光缆段逻辑，不排序
            JSONObject productInfo = obsLocationDao.queryProductInfo(accessCode, sharingCode);
            if (!ObjectUtil.isEmpty(productInfo)) {
                String busiType = productInfo.getString("code");
                String crmProductID = productInfo.getString("crm_product_id");
                if (!ObjectUtil.isEmpty(busiType) && !busiType.equals("14") && !busiType.equals("9")
                        && !busiType.equals("881") && !busiType.equals("6600118000")) {
//                    PageResponse<FiberCable> digitalCircuitFiberCableSegmentsResp = obsLocationDao.queryDigitalCircuitFiberCableSegment(crmProductID, 100, 1, sharingCode);
                    JSONObject req = new JSONObject();
                    req.put("access_code", accessCode);
                    req.put("areaCode", city);
                    JSONObject mstp = mstpService.mstp(req);
                    JSONArray mstpLinks = mstp.getJSONArray("links");
                    if (ObjectUtil.isEmpty(mstpLinks)) {
                        continue;
                    }
                    Set<String> fiberRouteID = new HashSet<>();
                    Set<String> fiberRouteCode = new HashSet<>();
                    recurCircuits(mstpLinks, fiberRouteID, fiberRouteCode);
                    ArrayList<FiberCable> digitalCircuitFiberCableSegments = new ArrayList<>();
                    for (String curRouteID : fiberRouteID) {
                        String curLinkID = curRouteID;
                        PageResponse<Link> linkPageResponse = obsLocationDao.queryFiber(curLinkID, 20, 1, sharingCode);
                        List<Link> curFiber = linkPageResponse.getData();
                        for (Link cl: curFiber) {
                            PageResponse<FiberCable> fiberCablePageResponse = obsLocationDao.queryFiberCableSegment(cl.getId(), 20, 1, sharingCode);
                            digitalCircuitFiberCableSegments.addAll(fiberCablePageResponse.getData());
                        }
                    }
                    allAccessCodeFiberCableSegments.put(accessCode, digitalCircuitFiberCableSegments);
                    ArrayList<String> circuitSegmentIDs = new ArrayList<>();
                    for (int j = 0; j < digitalCircuitFiberCableSegments.size(); j++) {
                        FiberCable curFiberCable = digitalCircuitFiberCableSegments.get(j);
                        circuitSegmentIDs.add(curFiberCable.getId());
                        circuitSegmentIDAccessCode.put(digitalCircuitFiberCableSegments.get(j).getId(), accessCode);
                    }
//                    if (!ObjectUtil.isEmpty(circuitSegmentIDs)) {
//                        allCircuitAccessCodeFiberCableSegmentID.put(accessCode, circuitSegmentIDs);
//                    }
                    continue;
                }
            }
            List<ArrayList> sorted = sortDeviceAndFiberSegmentByAccessCode(accessCode, sharingCode);
            if (!ObjectUtil.isEmpty(sorted)) {
                ArrayList<Device> deviceList = sorted.get(0);
                ArrayList<FiberCable> segmentList = sorted.get(1);
                allAccessCodeDevices.put(accessCode, deviceList);
                allAccessCodeFiberCableSegments.put(accessCode, segmentList);
                ArrayList<String> deviceIDs = new ArrayList<>();
                ArrayList<String> segmentIDs = new ArrayList<>();
                for (int j = 0; j < deviceList.size(); j++) {
                    Device curDevice = deviceList.get(j);
                    deviceIDs.add(curDevice.getId());
                    if ("1028200001".equals(curDevice.getSpecId())) {
                        allAccessCodeOLTDevice.put(accessCode, curDevice);
                    }
                 }
                for (int j = 0; j < segmentList.size(); j++) {
                    segmentIDs.add(segmentList.get(j).getId());
                }
                allAccessCodeDeviceID.put(accessCode, deviceIDs);
                allAccessCodeFiberCableSegmentID.put(accessCode, segmentIDs);
//                if (!ObjectUtil.isEmpty(segmentIDs)) {
//                    allCircuitAccessCodeFiberCableSegmentID.put(accessCode, segmentIDs);
//                }
            }
        }
        //如果需要分组的话，就提供分组之后的数据
        if(isGroup){
            HashMap<HashSet<String>, HashSet<String>> sameAccesscodeDevices = getSameDevice(allAccessCodeDeviceID, allAccessCodeOLTDevice);
            HashMap<HashSet<String>, HashSet<String>> sameAccesscodeSegments = getFiberCableSegment(allAccessCodeFiberCableSegmentID);

            getLastDevice(sameAccesscodeDevices, resAccessCodeDevices, allAccessCodeDevices);
            getLastSegment(sameAccesscodeSegments, resAccessCodeFiberCableSegments, allAccessCodeFiberCableSegments);

            JSONArray deviceResp = new JSONArray();
            JSONArray segMentResp = new JSONArray();
            for (Map.Entry<HashSet<String>, Device> cur : resAccessCodeDevices.entrySet()){
                HashSet<String> key = cur.getKey();
                Device value = cur.getValue();
                JSONObject tem = new JSONObject();
                if (!ObjectUtil.isEmpty(value)) {
                    tem.putAll(JSONObject.parseObject(JSON.toJSONString(value)));
                }
                if (!ObjectUtil.isEmpty(key)) {
                    ArrayList<String> accs = new ArrayList<>();
                    accs.addAll(key);
                    tem.put("access_code", accs);
                    tem.put("access_code_count", key.size());
                    deviceResp.add(tem);
//                String accs = key.toString();
                }
            }
            for (Map.Entry<HashSet<String>, FiberCable> cur : resAccessCodeFiberCableSegments.entrySet()){
                HashSet<String> key = cur.getKey();
                FiberCable value = cur.getValue();
                JSONObject tem = new JSONObject();
                if (!ObjectUtil.isEmpty(value)) {
                    tem.putAll(JSONObject.parseObject(JSON.toJSONString(value)));
                    if (circuitSegmentIDAccessCode.containsKey(value.getId())) {
                        key.add(circuitSegmentIDAccessCode.get(value.getId()));
                    }
                }
                if (!ObjectUtil.isEmpty(key)) {
                    ArrayList<String> accs = new ArrayList<>();
                    accs.addAll(key);
                    tem.put("access_code", accs);
                    tem.put("access_code_count", key.size());
                    segMentResp.add(tem);
                }
            }

            result.put("device", deviceResp);
            result.put("segment", segMentResp);
            result.put("sameAccesscodeSegments", sameAccesscodeSegments );
            result.put("sameAccesscodeDevices", sameAccesscodeDevices);
        }
        result.put("allAccessCodeFiberCableSegments", allAccessCodeFiberCableSegments);


        return result;
    }


    public JSONObject queryObstaclepointsV2(List<String> accessCodes, String sharingCode,Boolean isGroup) {
        JSONObject result = new JSONObject();


        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_cfs_2_pon").setShardingCode(sharingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cfs","access_code_s",accessCodes)).build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);



        List<JSONObject> cfsList = graphService.filterNodeByLabel(data,"cfs");
        graphService.buildTree(data, "cfs","光缆段","cableSegmentList");
        graphService.mapProperty(data, "cfs", "cableSegmentList", c->JSONObject.parseObject(JSONObject.toJSONString(c)));
        Map<String,List<JSONObject>> allAccessCodeFiberCableSegments = new HashMap<>();
        for (JSONObject cfs : cfsList) {
            String accessCode = cfs.getString("access_code");
            if (ObjectUtil.isNotEmpty(cfs.getJSONArray("cableSegmentList"))) {
                List<JSONObject> cableSegmentList = cfs.getJSONArray("cableSegmentList").toJavaList(JSONObject.class);
                allAccessCodeFiberCableSegments.put(accessCode, cableSegmentList);
            }

        }

        result.put("allAccessCodeFiberCableSegments",allAccessCodeFiberCableSegments);

        JSONObject topo = ponService.buildTopo(data);

        sortAllFiberCableSegments(allAccessCodeFiberCableSegments,topo,"access_code","cfs");
        List<JSONObject> groups = buildLabelNameGroups(allAccessCodeFiberCableSegments,"access_code","cfs");
        result.put("groups", groups);

        List<JSONObject> topSegments = queryTopSegments(groups);
        result.put("segment", topSegments);

        result.put("topo", topo);


        return result;
    }




    public JSONObject queryObstaclepointsbyalarm(JSONArray poninfo, String sharingCode) {
        JSONObject result = new JSONObject();



        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_res_by_alarm").setShardingCode(sharingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("pon_port","poninfo",poninfo)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);
//        graphService.buildTree(data,"PON端口","光路","光路");
//        graphService.buildTree(data,"光路","设备","设备");  //有源设备，OLT，ONU  OBD
//        graphService.buildTree(data,"光路","局向光纤","局向光纤");  //ODF   GJ
//        graphService.buildTree(data,"局向光纤","设备","设备");  //ODF   GJ
//        graphService.buildTree(data,"局向光纤","光缆","光缆");  //ODF   GJ
//        graphService.buildTree(data,"局向光纤","光缆段","光缆段");
//        graphService.buildTree(data,"光缆段","管道段","管道段");

//        List<JSONObject> pontree = graphService.filterNodeByLabel(data,"PON端口");


        graphService.buildTree(data,"光缆段","管道段","pipe_segment");
//        graphService.buildTree(data,"PON端口","光路","opt_road");
//        graphService.buildTree(data,"PON端口","光缆","cable");
//        graphService.buildTree(data,"光缆段","设备","device");

        
        List<JSONObject> ponportList = graphService.filterNodeByLabel(data,"PON端口");
        graphService.buildTree(data, "PON端口","光缆段","cableSegmentList");
        graphService.mapProperty(data, "PON端口", "cableSegmentList", c->JSONObject.parseObject(JSONObject.toJSONString(c)));
        Map<String,List<JSONObject>> allPonportFiberCableSegments = new HashMap<>();
        for (JSONObject ponport : ponportList) {
            String ponportid = ponport.getString("id");
            if (ObjectUtil.isNotEmpty(ponport.getJSONArray("cableSegmentList"))) {
                List<JSONObject> cableSegmentList = ponport.getJSONArray("cableSegmentList").toJavaList(JSONObject.class);
                allPonportFiberCableSegments.put(ponportid, cableSegmentList);

            }

        }

        result.put("allPonportFiberCableSegments",allPonportFiberCableSegments);

        JSONObject topo = ponService.buildAlarmTopo(data);

        sortAllFiberCableSegments(allPonportFiberCableSegments,topo,"pon_code","pon端口");

        List<JSONObject> groups = buildLabelNameGroups(allPonportFiberCableSegments,"pon_code","PON端口");
        result.put("groups", groups);

//        List<JSONObject> topSegments = queryTopSegments(groups);
//        result.put("segment", topSegments);

        result.put("topo", topo);


        return result;
    }



    // 计算所有光缆段距离用户的距离,数值越小说明离用户越近
    void sortAllFiberCableSegments(Map<String,List<JSONObject>> allAccessCodeFiberCableSegments, JSONObject topo,String LabelName,String NodeName) {
        List<JSONObject> links = topo.getJSONArray("links").toJavaList(JSONObject.class);
        List<JSONObject> cableSegments = links.stream().filter(l->ObjectUtil.isNotEmpty(l.getString("label_name")) && l.getString("label_name").equals("光缆段")).collect(Collectors.toList());
        Map<String,JSONObject> cableSegmentMap = new HashMap<>();
        for (JSONObject cableSegment : cableSegments) {
            cableSegmentMap.put(cableSegment.getString("gid"), cableSegment);
        }
        Set<String> allAccessCodes = allAccessCodeFiberCableSegments.keySet();
        for (String accessCode : allAccessCodes) {
            List<JSONObject> cableSegmentList = allAccessCodeFiberCableSegments.get(accessCode);
            for (JSONObject cableSegment : cableSegmentList) {
                JSONObject newCableSegment  =cableSegmentMap.get(cableSegment.getString("gid"));
                if (newCableSegment == null) {
                    log.info("cableSegment {} not found", cableSegment.getString("gid"));
                }
                try {
                    cableSegment.put("seq", newCableSegment.getInteger("seq"));
                    cableSegment.put( LabelName+"_count", newCableSegment.getInteger(NodeName+"_num"));
                    cableSegment.put( LabelName,newCableSegment.getJSONArray(NodeName+"_list").toJavaList(JSONObject.class).stream().map(c->c.getString(LabelName)).collect(Collectors.toSet()));
                }catch (Exception e ){
                    log.info("cableSegment seq or _num or _list is empty ", cableSegment.getString("gid"));

                    continue;
                }

            }

            cableSegmentList.sort((a,b)-> {
                if (ObjectUtil.isNotEmpty(a.getInteger("seq")) && ObjectUtil.isNotEmpty(b.getInteger("seq"))) {
                    return a.getInteger("seq") - b.getInteger("seq");
                } else {
                    return 0;
                }

            });

        }
    }

    // 查询最有可能的光缆段
    //labelName代表字段的名称，例如access_code、pon_code  ，nodeName代表一树一图的节点名称，例如cfs PON端口
    List<JSONObject> buildLabelNameGroups(Map<String,List<JSONObject>> allAccessCodeFiberCableSegments, String labelName ,String nodeName) {
        List<JSONObject> groups = new ArrayList<>();
        Set<String> accessCodes = allAccessCodeFiberCableSegments.keySet();
        for (String accessCode : accessCodes) {
            List<JSONObject> cableSegments = allAccessCodeFiberCableSegments.get(accessCode);
            for (JSONObject cableSegment : cableSegments) {
                addSegmentToGroup(groups,cableSegment,labelName);
            }
        }
        for (JSONObject group : groups) {
            List<JSONObject> groupCableSegmentList = (List<JSONObject>) group.getJSONArray("cableSegmentList").toJavaList(JSONObject.class);
            groupCableSegmentList = groupCableSegmentList.stream().distinct().collect(Collectors.toList());
            groupCableSegmentList.sort((a,b)->{
                int a_access_code_count = a.getInteger(labelName+"_count");
                int b_access_code_count = b.getInteger(labelName+"_count");
                int a_seq = ObjectUtil.isNotEmpty(a.getInteger("seq")) ? a.getInteger("seq") : 0;
                int b_seq = ObjectUtil.isNotEmpty(b.getInteger("seq")) ? b.getInteger("seq") : 0;

                if (a_access_code_count!=b_access_code_count ) {
                    return b_access_code_count - a_access_code_count;
                }else if (a_seq!=b_seq) {
                    return a_seq-b_seq;
                }else{
                    return 0;
                }
            });
            group.put("cableSegmentList", groupCableSegmentList);
        }
        queryTopSegments(groups);
        return groups;
    }

    @SuppressWarnings("unchecked")
    void addSegmentToGroup(List<JSONObject> groups, JSONObject cableSegment,String LabelName) {
        Set<String> cableSegmentAccessCode = (Set<String>) cableSegment.get(LabelName);


        for (JSONObject group : groups) {
            Set<String> groupAccessCode = (Set<String>) group.get(LabelName);
            List<JSONObject> groupCableSegmentList = (List<JSONObject>) group.getJSONArray("cableSegmentList").toJavaList(JSONObject.class);

            Set<String> intersection = new HashSet<>(groupAccessCode);
            if(ObjectUtil.isNotNull(cableSegmentAccessCode)){
                intersection.retainAll(cableSegmentAccessCode);
            }
            else {
                continue;
            }
            if (!intersection.isEmpty()) {
                groupAccessCode.addAll(cableSegmentAccessCode);
                groupCableSegmentList.add(cableSegment);
                group.put("cableSegmentList", groupCableSegmentList);
                return;
            }
        }
        JSONObject group = new JSONObject();
        group.put(LabelName,cableSegmentAccessCode);
        group.put("cableSegmentList",Arrays.asList(cableSegment));
        groups.add(group);

    }



    List<JSONObject> queryTopSegments(List<JSONObject> groups) {
        List<JSONObject> topSegments = new ArrayList<>();
        for (JSONObject group : groups) {
            topSegments.add(group.getJSONArray("cableSegmentList").toJavaList(JSONObject.class).get(0));
        }
        return topSegments;

    }
}
