package com.telecom.nrm.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.controller.CustLinkController;
import com.telecom.nrm.controller.CustViewMemberController;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.CustViewMemberDao;
import com.telecom.nrm.dao.LifeCircuitDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.LifeCircuitService;
import com.telecom.nrm.utils.HttpClientUtil;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LifeCircuitServiceImpl implements LifeCircuitService {


    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;


    @Autowired
    LifeCircuitDao lifeCircuitDao;

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    CustLinkController custLinkController;

    @Autowired
    CustViewMemberController custViewMemberController;

    public List<JSONObject> getCircuitPairByCustView(List<JSONObject> custViewMemberList, String ds){
        List<String> codeList = custViewMemberList.stream().map(custViewMember -> custViewMember.getString("circuit_code")).collect(Collectors.toList());
        JSONObject param = new JSONObject();
        param.put("codes", codeList);
        PageResponse<JSONObject> circuitPairPage =  custLinkInterfaceDao.getCircuit_pair(param ,10000,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
        List<JSONObject> circuitPairData = circuitPairPage.getData();
        JSONObjectUtil.convertBigNumberToString(circuitPairData);
        circuitPairData.removeIf(circuitPair -> Objects.isNull(circuitPair.getString("result")));
        return circuitPairData;
    }
    public List<JSONObject> getCircuitPairByCustView(JSONObject custViewMember, String ds){
        JSONObject param = new JSONObject();
        param.put("codes", custViewMember.getString("circuit_code"));
        PageResponse<JSONObject> circuitPairPage =  custLinkInterfaceDao.getCircuit_pair(param ,10000,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
        List<JSONObject> circuitPairData = circuitPairPage.getData();
        JSONObjectUtil.convertBigNumberToString(circuitPairData);
        circuitPairData.removeIf(circuitPair -> Objects.isNull(circuitPair.getString("result")));
        return circuitPairData;
    }


    public JSONObject lifeCircuit_statistic_dictionary(JSONObject jsonObject) {
        JSONObject result = new JSONObject();
        jsonObject.put("result",null);
        result.put("totalCount", lifeCircuitDao.pm_busi_user_scene_member_state_statistic_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        jsonObject.put("result","同管道");
        result.put("samePipeSementCount", lifeCircuitDao.pm_busi_user_scene_member_state_statistic_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        jsonObject.put("result","同光缆");
        result.put("sameCableCount", lifeCircuitDao.pm_busi_user_scene_member_state_statistic_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        jsonObject.put("result","无接入光路");
        result.put("noAccessOptCount", lifeCircuitDao.pm_busi_user_scene_member_state_statistic_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        jsonObject.put("result","正常");
        result.put("normal", lifeCircuitDao.pm_busi_user_scene_member_state_statistic_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        jsonObject.put("result","已忽略异常");
        result.put("ignoreCount", lifeCircuitDao.pm_busi_user_scene_member_state_statistic_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        return result;
    }

    public JSONObject lifeCircuit_statistic_his_dictionary(JSONObject jsonObject) {
        JSONObject result = new JSONObject();
        result.put("totalCount", lifeCircuitDao.pm_busi_user_scene_member_statistic_his_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        jsonObject.put("result","无接入光路");
        result.put("noAccessOptCount", lifeCircuitDao.pm_busi_user_scene_member_statistic_his_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        jsonObject.put("result","正常");
        result.put("normal", lifeCircuitDao.pm_busi_user_scene_member_statistic_his_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB));
        return result;
    }


    public Integer white_insert(JSONObject jsonObject){
        return lifeCircuitDao.pm_pipesegment_white_insert(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public Integer white_delete(JSONObject jsonObject){
        return lifeCircuitDao.pm_pipesegment_white_delete(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public List<JSONObject> white_query(JSONObject jsonObject){
        return lifeCircuitDao.pm_pipesegment_white_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }


    public Integer insert_white(JSONObject jsonObject){
        jsonObject.put("deal_result","正常");
        return lifeCircuitDao.pm_busi_user_scene_member_result_deal_update(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }


    public void setdownload(List<JSONObject> result, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("地市");
        header.createCell(1).setCellValue("客户名称");
        header.createCell(2).setCellValue("业务号码");
        header.createCell(3).setCellValue("电路编号");
        header.createCell(4).setCellValue("光路编码");
        header.createCell(5).setCellValue("光路名称");
        header.createCell(6).setCellValue("电路备注");
        header.createCell(7).setCellValue("成对业务号码");
        header.createCell(8).setCellValue("成对电路编号");
        header.createCell(9).setCellValue("成对光路编码");
        header.createCell(10).setCellValue("成对光路名称");
        header.createCell(11).setCellValue("成对电路备注");
        header.createCell(12).setCellValue("检测结果");
        header.createCell(13).setCellValue("备注");

        // 填充数据
        int rowIndex = 1;
        for (int i =0; i< result.size();i++) {
            XSSFRow row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(result.get(i).getString("areacode"));
            row.createCell(1).setCellValue(result.get(i).getString("custName"));
            row.createCell(2).setCellValue(result.get(i).getString("accessCode"));
            row.createCell(3).setCellValue(result.get(i).getString("aCode"));
            row.createCell(4).setCellValue(result.get(i).getString("aOptCode"));
            row.createCell(5).setCellValue(result.get(i).getString("aOptRoad"));
            row.createCell(6).setCellValue(result.get(i).getString("aNotes"));
            row.createCell(7).setCellValue(result.get(i).getString("zAccessCode"));
            row.createCell(8).setCellValue(result.get(i).getString("zCode"));
            row.createCell(9).setCellValue(result.get(i).getString("zOptCode"));
            row.createCell(10).setCellValue(result.get(i).getString("zOptRoad"));
            row.createCell(11).setCellValue(result.get(i).getString("zNotes"));
            row.createCell(12).setCellValue(result.get(i).getString("result"));
            row.createCell(13).setCellValue(result.get(i).getString("note"));
        }
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=list.xlsx");

        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    public String lifeListDeal(JSONObject jsonObject){
        List<JSONObject> value = JSONObjectUtil.jsonObjectArrayToList(jsonObject.getJSONArray("value"));
        List<String> codes = new ArrayList<>();
        for(int i =0;i<value.size();i++){
            codes.add(value.get(i).getString("accessNumber"));
        }
        JSONObject buf = new JSONObject();
        buf.put("codes",codes);
        System.out.println("jsonObject" +buf +","+NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        for(int i =0;i<30;i++){
            PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCustLink(buf ,100,i,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
            System.out.println("pageResponse.getData()" +pageResponse.getData().size());

            List<JSONObject> dataSource = pageResponse.getData();
            String sceneId = jsonObject.getString("scene_id");

            for (JSONObject d: dataSource) {

                d.put("service_id", d.getString("service_id"));
                d.put("id", "");
                d.put("scene_id", sceneId);
                d.put("a_node_name", d.getString("a_address_name"));
                d.put("z_node_name", d.getString("z_address_name"));
                if (ObjectUtil.isEmpty(d.get("service_type_id"))) {
                    d.put("service_type_id", 2105L);
                }
                if (ObjectUtil.isEmpty(d.get("service_type_name"))) {
                    d.put("service_type_name", "数字电路出租");
                }
                if (ObjectUtil.isEmpty(d.get("area_id"))) {
                    d.put("area_id", new BigDecimal("321122930000000000000014"));
                }
                Integer result = custViewMemberDao.save(d, NRMConstants.SHARDING_GRAPH_DB);
                System.out.println("d" +result+","+d);

            }
        }
        return String.valueOf(codes);
    }


    /*public String lifeListDeal_1(JSONObject jsonObject,HttpServletResponse response){
        List<JSONObject> value =JSONObjectUtil.jsonObjectArrayToList( jsonObject.getJSONArray("value"));
        String codes = "";
        *//*System.out.println("value.size()"+value.size());
        List<String> code_s = new ArrayList<>();
        for(JSONObject i : value){
            codes = codes +"'"+i.getString("accessNumber")+"',";
            code_s.add(i.getString("accessNumber"));
        }
        System.out.println(codes);


        for(JSONObject i : value){
            String access_code = i.getString("accessNumber");
            System.out.println("当前接入号"+access_code);
            JSONObject custList = new JSONObject();
            custList.put("ds",jsonObject.getString("ds"));
            custList.put("access_code",access_code);
            BiyiPageRequest page = new BiyiPageRequest();
            page.setPage(1);
            page.setSize(10000);
            BiyiPageResult<JSONObject> result =  custLinkController.getList(custList, page);
            List<JSONObject> result_list = result.getData();
            System.out.println("----"+result_list);

            JSONObject  current_js= new JSONObject();
            current_js.put("dataSource",result_list);
            List<String> result_list_ids = new ArrayList<>();
            for(JSONObject circuit : result_list){
                result_list_ids.add(circuit.getString("id"));
            }
            current_js.put("selectedRowKeys", result_list_ids);
            current_js.put("scene_id", jsonObject.getString("scene_id"));
            custViewMemberController.saveList(current_js,  response);
        }
*//*
        return codes;
    }*/



    public JSONObject getLifeList(JSONObject jsonObject) throws IOException {
        //String result = HttpRequestsUtils.sendPost("http://sjgxpt.telecomjs.com:8090/dataway/api/cqt_ivy_db/wir/common/zhzz/getSpecialLineDetail",getCommonHeader(), jsonObject);
        //return JSONObject.parseObject(result);
        String flow_create_url = "http://sjgxpt.telecomjs.com:8090/dataway/api/cqt_ivy_db/wir/common/zhzz/getSpecialLineDetail";
        URL url = new URL(flow_create_url);
        String result= HttpClientUtil.doPostByProperty(flow_create_url,  JSONObject.toJSONString(jsonObject), getCommonHeader());
        return JSONObject.parseObject(result);
    }

    public Map<String, String> getCommonHeader() {
        Map<String, String> header = new HashMap<>();
        header.put("appKey", "2a5e6524f24b1248");
        header.put("appSecret", "de52eb292a5e6524f24b124801c231c4");
        return header;
    }



}
