package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.GraphDao;
import com.telecom.nrm.dao.GraphSceneDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.GraphScene;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.service.GraphSceneService;
import com.telecom.nrm.service.GraphService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GraphSceneServiceImpl implements GraphSceneService {

    @Autowired
    GraphSceneDao graphSceneDao;

    @Autowired
    GraphService graphService;

    @Autowired
    GraphDao graphDao;


    public GraphScene getById (Long id) {
        GraphScene scene = graphSceneDao.get(id, NRMConstants.SHARDING_GRAPH_DB);
        Graph graph = getGraph(scene);
        scene.setGraph(graph);
        return scene;
    }

    @Override
    public GraphScene getByCode(String code) {
        GraphScene scene = graphSceneDao.getByCode(code, NRMConstants.SHARDING_GRAPH_DB);
        Graph graph = getGraph(scene);
        scene.setGraph(graph);
        return scene;
    }


    Graph getGraph(GraphScene scene) {
        String startId = scene.getStartNode();
        Node startNode = graphDao.getNode(startId);
        Graph graph  = null;
        if (ObjectUtil.isEmpty(scene.getGraphData())) {
            graph = new Graph();
        }else{
            graph = JSONObject.parseObject(scene.getGraphData(), Graph.class);
        }
        graph.addNode(startNode);
        // return graph;
        return graphService.reload(graph);
    }
}
