package com.telecom.nrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.CustViewMemberDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustViewMemberServiceImpl implements CustViewMemberService {
    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    CircuitSingleService circuitSingleService;

    @Autowired
    OptGroupService optGroupService;
    @Autowired
    OptRoadPairService roadPairService;

    @Autowired
    LifeCircuitService lifeCircuitService;

    public JSONObject circuit_analyze(JSONObject currentCircuit, JSONObject request){
        List<String> codes = new ArrayList();
        codes.add(currentCircuit.getString("circuit_code"));
        log.debug("当前电路"+codes);
        JSONObject need_save = new JSONObject();
        need_save.put("member_id", request.getString("id"));
        JSONObject  result = new JSONObject();
        //System.out.println("判断地安陆市否有光路");

        JSONObject query_mstp_circuits = new JSONObject();
        query_mstp_circuits.put("codes", Arrays.asList(currentCircuit.getString("circuit_code").split("；")));
        query_mstp_circuits.put("shardingCode", NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        query_mstp_circuits.put("ds", request.getString("ds"));
        result = circuitSingleService.queryMSTPCircuitToPipeSegment(query_mstp_circuits);


        log.info("need_save"+result);
        need_save.put("result",roadPairService.result2format(result));
        String service_type_name = currentCircuit.getString("service_type_name");
        if(service_type_name.contains("云计算虚拟产品") || service_type_name.contains("安全专线") || service_type_name.contains("云堤") || service_type_name.contains("云专网节点")){
            need_save.put("result","正常");
        }
        List<JSONObject> circuits_cm_link = new ArrayList<>();
        currentCircuit.put("circuit_no", currentCircuit.getString("circuit_code"));
        JSONObject pageResponse = custLinkInterfaceDao.rm_service_query(currentCircuit, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(currentCircuit.get("area_name")));
        if(pageResponse !=null || !Objects.isNull(pageResponse)){
            circuits_cm_link.add(pageResponse);
        }
        log.info("当前电路"+pageResponse+","+circuits_cm_link+","+currentCircuit);

       /* if(access_opt_count == 1){
            need_save.put("result","单接入光路");
        }*/
        if(!ObjectUtil.isNotEmpty(circuits_cm_link)){
            need_save.put("result","已拆机");
        }
        /*
        if(count_samePipeSegments == 0 && count_sameCables ==0 && filteredSamePipeSegmentOptPair.size()!=0 && filteredSameCableOptPair.size() !=0){
            lifeCircuitService.insert_white(currentCircuit);
        }
         */
        JSONObject result_opt = getOptCode2Road(result);
        need_save.put("opt_code", result_opt.getString("optCode"));
        need_save.put("opt_road", result_opt.getString("optRoad"));

        String snapshot = new cn.hutool.json.JSONObject(result).toJSONString(0);
        need_save.put("snapshot", snapshot);
        log.info("need_save {}", need_save);
        custLinkInterfaceDao.pm_busi_user_scene_member_result_insert(need_save, NRMConstants.SHARDING_GRAPH_DB);
        result.put("circuits_pair", codes);
        result.put("title", CollectionUtil.join(codes,","));
        result.put("check_result", need_save.getString("result"));
        return result;
    }

    public JSONObject circuit_analyze_get(JSONObject currentCircuit, JSONObject request) {

        String id= request.getString("id");
        boolean cached = request.getBoolean("cached");
        if (cached) {
            JSONObject result = custLinkInterfaceDao.pm_busi_user_scene_member_result_get(id, NRMConstants.SHARDING_GRAPH_DB);
            if (cached && ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.getJSONObject("snapshot"))  ) {
                JSONObject snapshot = result.getJSONObject("snapshot");
                snapshot.put("check_result", result.getString("result"));
                return snapshot;
            }else{
                return circuit_analyze(currentCircuit, request);
            }
        }else{
            return circuit_analyze(currentCircuit, request);
        }



    }





    public JSONObject getOptCode2Road(JSONObject result){
        // System.out.println("入参"+result.getJSONObject("optRoads"));
        List<JSONObject> optRoadList = JSONObjectUtil.jsonObjectArrayToList(result.getJSONArray("optRoads"));
        String opt_code ="";
        String opt_road ="";
        if(ObjectUtil.isNotEmpty(optRoadList)){
            for(JSONObject optRoad: optRoadList){
                if(opt_code.equals("")){opt_code = optRoad.getString("code");}else{opt_code = opt_code + "," + optRoad.getString("code");}
                if(opt_road.equals("")){opt_road = optRoad.getString("code")+optRoad.getString("name");} else{opt_road = opt_road+ "," +optRoad.getString("code")+optRoad.getString("name");}
            }
        }
        JSONObject result_opt = new JSONObject();
        result_opt.put("optCode", opt_code);
        result_opt.put("optRoad", opt_road);
        return result_opt;
    }








    //@LogAnnotation(interfaceName = "生命线业务隐患检测")
    public JSONObject risk_analyze(JSONObject request){

        PageResponse<JSONObject> pageResponse =  custViewMemberDao.queryList(request ,10000,1, NRMConstants.SHARDING_GRAPH_DB);
        JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
        List<JSONObject> pair_list_data =pageResponse.getData();
        JSONObject result = new JSONObject();
        if(ObjectUtil.isNotEmpty(pair_list_data)){
            JSONObject current_circuits_pair = pair_list_data.get(0);
            log.info("当前检查的电路L:"+current_circuits_pair);
            result= circuit_analyze_get(current_circuits_pair, request);
        }
        return result;
    }
}
