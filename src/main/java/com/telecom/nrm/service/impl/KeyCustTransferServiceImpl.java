package com.telecom.nrm.service.impl;

import com.telecom.da.client.domain.Page;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.Constant;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.KeyCustTransferDao;
import com.telecom.nrm.entity.*;
import com.telecom.nrm.service.KeyCustTransferService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KeyCustTransferServiceImpl implements KeyCustTransferService {

    @Autowired
    private KeyCustTransferDao keyCustTransferDao;

    @Override
    public PageResult queryCmPortList(PortQueryDTO portQueryDTO) {
        if (portQueryDTO == null) {
            return PageResult.fail("查询端口失败！入参为空");
        }
        if (StringUtils.isEmpty(portQueryDTO.getId())
                && StringUtils.isEmpty(portQueryDTO.getName())
                && StringUtils.isEmpty(portQueryDTO.getNmIp())) {
            return PageResult.fail("查询端口失败！端口名称/端口id必须填写一个");
        }
        if (StringUtils.isEmpty(portQueryDTO.getDeviceId())
                && StringUtils.isEmpty(portQueryDTO.getNmIp())) {
            return PageResult.fail("查询端口失败！设备管理IP/设备ID必须填写一个");
        }
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(portQueryDTO.getCityCode());
        if (shardingEnum == null) {
            log.error("查询失败！城市编码：{}，未找到对应的分库", portQueryDTO.getCityCode());
            return PageResult.fail("查询端口失败！请联系管理员");
        }

        PageResponse<CmPort> cmPortResponse
                = keyCustTransferDao.queryCmPortByPage(portQueryDTO, shardingEnum.getBcShardingCode());
        PageResult pageResult = new PageResult();
        if (cmPortResponse != null) {
            List<CmPort> data = cmPortResponse.getData();
            Page pageInfo = cmPortResponse.getPageInfo();
            pageResult = PageResult.ok(data
                    , String.valueOf(pageInfo.getTotalCount())
                    , String.valueOf(pageInfo.getPageSize())
                    , String.valueOf(pageInfo.getCurrentPage()));
        } else {
            log.error("查询端口失败! Api平台返回空值");
            pageResult = PageResult.fail("查询端口失败！请联系管理员");
        }
        return pageResult;
    }

    @Override
    public PageResult queryCmDeviceList(EntityQueryDTO entityQueryDTO) {
        if (entityQueryDTO == null) {
            return PageResult.fail("查询设备失败！入参为空");
        }
        if (StringUtils.isEmpty(entityQueryDTO.getId())
                && StringUtils.isEmpty(entityQueryDTO.getName())
                && StringUtils.isEmpty(entityQueryDTO.getNmIp())) {
            return PageResult.fail("查询设备失败！设备名称/设备管理IP/设备id必须填写一个");
        }
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(entityQueryDTO.getCityCode());
        if (shardingEnum == null) {
            log.error("查询失败！城市编码：{}，未找到对应的分库", entityQueryDTO.getCityCode());
            return PageResult.fail("查询设备失败！请联系管理员");
        }

        PageResponse<CmDevice> cmDeviceResponse
                = keyCustTransferDao.queryCmDeviceList(entityQueryDTO, shardingEnum.getBcShardingCode());
        PageResult pageResult = new PageResult();
        if (cmDeviceResponse != null) {
            List<CmDevice> data = cmDeviceResponse.getData();
            Page pageInfo = cmDeviceResponse.getPageInfo();
            pageResult = PageResult.ok(data
                    , String.valueOf(pageInfo.getTotalCount())
                    , String.valueOf(pageInfo.getPageSize())
                    , String.valueOf(pageInfo.getCurrentPage()));
        } else {
            log.error("查询设备失败! Api平台返回空值");
            pageResult = PageResult.fail("查询设备失败！请联系管理员");
        }
        return pageResult;
    }

    @Override
    public Result queryWavelengthChannel(WaveQueryDTO waveQueryDTO) {
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(waveQueryDTO.getCityCode());
        if (shardingEnum == null) {
            log.error("查询光复用段波道失败！城市编码：{}，未找到对应的分库", waveQueryDTO.getCityCode());
            return Result.fail("查询光复用段波道失败！请联系管理员");
        }
        List<WavelengthChannel> WavelengthChannels = new ArrayList<>();
        try {
            WavelengthChannels
                    = keyCustTransferDao.queryWavelengthChannel(waveQueryDTO, shardingEnum.getBcShardingCode());
        } catch (Exception e) {
            log.error("查询光复用段波道失败! Api平台报错：{}", e);
            return Result.fail("查询光复用段波道失败！请联系管理员");
        }
        return Result.ok(WavelengthChannels);
    }

    @Override
    public Result queryOpticalRoute(Map<String, String> param) {
        String ppmCode = MapUtils.getString(param, "ppmCode");
        String optRoadCode = MapUtils.getString(param, "optRoadCode");
        String optRoadId = MapUtils.getString(param, "optRoadId");
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (StringUtils.isEmpty(optRoadCode) && StringUtils.isEmpty(optRoadId)) {
            return Result.fail("本地光路ID/本地光路编码/本地光缆名称不能同时为空！");
        }

        if (shardingEnum == null) {
            log.error("查询光路路由失败！城市编码：{}，未找到对应的分库", ppmCode);
            return Result.fail("查询光路路由失败！请联系管理员");
        }
        String bcShardingCode = shardingEnum.getBcShardingCode();
        List<CmLink> optRoads = new ArrayList<>();
        try {
            CmLink cmLink = new CmLink();
            if (StringUtils.isNotEmpty(optRoadId)) {
                cmLink.setId(new BigDecimal(optRoadId));
            } else {
                cmLink.setCode(optRoadCode);
            }
            cmLink.setSpecId(Constant.ResType.FIBER_OPTICAL);
            optRoads = keyCustTransferDao.queryCmLink(cmLink, bcShardingCode);

        } catch (Exception e) {
            log.error("查询光路失败! Api平台报错：{}", e);
            return Result.fail("查询光路路由失败！请联系管理员");
        }
        if (CollectionUtils.isEmpty(optRoads)) {
            try {
                Map<String, String> optCableNameMap = new HashMap<>();
                optCableNameMap.put("optCableName", optRoadCode);
                optRoads = keyCustTransferDao.queryOptRoadByOptCableName(optCableNameMap, bcShardingCode);

            } catch (Exception e) {
                log.error("根据光缆名称查询光路失败! Api平台报错：{}", e);
                return Result.fail("查询光路路由失败！请联系管理员");
            }
        }
        if (CollectionUtils.isEmpty(optRoads)) {
            log.error("查询光路失败! 未找到对应的光路");
            return Result.ok(null);
        }
        List<OptRoadInfo> optRoadInfos = new ArrayList<>();
        for (CmLink optRoad : optRoads) {
            OptRoadInfo optRoadInfo = new OptRoadInfo();

            optRoadInfo.setOptRoadId(optRoad.getId().toString());
            optRoadInfo.setOptRoadCode(optRoad.getCode());
            optRoadInfo.setOptRoadName(optRoad.getName());
            optRoadInfo.setDeviceIdA(optRoad.getAPhysicDeviceId().toString());
            optRoadInfo.setDeviceIdZ(optRoad.getZPhysicDeviceId().toString());

            optRoadInfo.setPortIdA(optRoad.getAPortId().toString());
            optRoadInfo.setPortIdZ(optRoad.getZPortId().toString());

            StringBuilder textRoute = new StringBuilder();
            try {
                textRoute = this.queryTextRoute(optRoad, bcShardingCode);
            } catch (Exception e) {
                return Result.fail("查询光路路由失败！请联系管理员");
            }
            //补充局站信息
            Map<String, CmDevice> deviceMap = new HashMap<>();
            try {
                deviceMap = this.queryDevicesByOptRoads(optRoads, bcShardingCode);
            } catch (Exception e) {
                return Result.fail(e.getMessage());
            }

            CmDevice aDevice = (CmDevice) MapUtils.getObject(deviceMap, optRoad.getAPhysicDeviceId().toString());
            CmDevice zDevice = (CmDevice) MapUtils.getObject(deviceMap, optRoad.getZPhysicDeviceId().toString());
            if (aDevice != null) {
                String tmlId = aDevice.getTmlId();
                if (StringUtils.isNotEmpty(tmlId)) {
                    optRoadInfo.setStationIdA(tmlId);
                }
            }
            if (zDevice != null) {
                String tmlId = zDevice.getTmlId();
                if (StringUtils.isNotEmpty(tmlId)) {
                    optRoadInfo.setStationIdZ(zDevice.getTmlId());
                }
            }
            optRoadInfo.setTextRoute(this.supplyTmlInfo4TextRoute(textRoute, aDevice, zDevice));
            optRoadInfos.add(optRoadInfo);
        }


        return Result.ok(optRoadInfos);
    }


    @Override
    public Result queryOptMultiSection(Map<String, String> param) {

        String ppmCode = MapUtils.getString(param, "cityCode");
        String multiSegName = MapUtils.getString(param, "multiSegName");
        if (StringUtils.isEmpty(multiSegName)) {
            return Result.fail("复用段名称不能为空！");
        }
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);

        if (shardingEnum == null) {
            log.error("查询光复用段失败！城市编码：{}，未找到对应的分库", ppmCode);
            return Result.fail("查询光复用段失败！请联系管理员");
        }
        String bcShardingCode = shardingEnum.getBcShardingCode();
        List<CmLink> optMultiSection = new ArrayList<>();
        try {
            CmLink cmLink = new CmLink();
            cmLink.setName(multiSegName);
            cmLink.setSpecId(Constant.ResType.DWDM_MULTI_SEG);
            optMultiSection = keyCustTransferDao.queryCmLink(cmLink, bcShardingCode);

        } catch (Exception e) {
            log.error("查询光路失败! Api平台报错：{}", e);
            return Result.fail("查询光路路由失败！请联系管理员");
        }
        List<OptMultiSegInfo> optMultiSegInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(optMultiSection)) {
            for (CmLink cmLink : optMultiSection) {
                OptMultiSegInfo optMultiSegInfo = new OptMultiSegInfo();
                optMultiSegInfo.setMultiSegId(cmLink.getId().toString());
                optMultiSegInfo.setMultiSegName(cmLink.getName());
                BigDecimal aPortId = cmLink.getAPortId();
                if (aPortId != null) {
                    optMultiSegInfo.setPortIdA(aPortId.toString());
                }
                BigDecimal zPortId = cmLink.getZPortId();
                if (zPortId != null) {
                    optMultiSegInfo.setPortIdZ(zPortId.toString());
                }
                BigDecimal aPhysicDeviceId = cmLink.getAPhysicDeviceId();
                if (aPhysicDeviceId != null) {
                    optMultiSegInfo.setDeviceIdA(aPhysicDeviceId.toString());
                }
                BigDecimal zhysicDeviceId = cmLink.getZPhysicDeviceId();
                if (zhysicDeviceId != null) {
                    optMultiSegInfo.setDeviceIdZ(zhysicDeviceId.toString());
                }
                BigDecimal aTmlId = cmLink.getATmlId();
                if (aTmlId != null) {
                    optMultiSegInfo.setStationIdA(aTmlId.toString());
                }
                BigDecimal zTmlId = cmLink.getZTmlId();
                if (zTmlId != null) {
                    optMultiSegInfo.setStationIdZ(zTmlId.toString());
                }
                optMultiSegInfos.add(optMultiSegInfo);
            }
        }

        return Result.ok(optMultiSegInfos);
    }

    @Override
    public Result queryOptRoadByMultiSegId(Map<String, String> param) {
        String ppmCode = MapUtils.getString(param, "ppmCode");
        String multiSegId = MapUtils.getString(param, "multiSegId");
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (shardingEnum == null) {
            log.error("查询光复用段路由失败！城市编码：{}，未找到对应的分库", ppmCode);
            return Result.fail("查询光复用段路由失败！请联系管理员");
        }
        String bcShardingCode = shardingEnum.getBcShardingCode();
        List<CmLink> optMultiSegList = new ArrayList<>();
        try {
            CmLink cmLink = new CmLink();
            cmLink.setId(new BigDecimal(multiSegId));
            cmLink.setSpecId(Constant.ResType.DWDM_MULTI_SEG);
            optMultiSegList = keyCustTransferDao.queryCmLink(cmLink, bcShardingCode);
        } catch (Exception e) {
            log.error("查询光复用段失败! Api平台报错：{}", e);
            return Result.fail("查询光复用段路由失败！请联系管理员");
        }
        if (CollectionUtils.isEmpty(optMultiSegList)) {
            log.error("未查询到光复用段");
            return Result.ok(null);
        }
        CmLink optMultiSeg = optMultiSegList.get(0);
        List<CmLink> optRoads = new ArrayList<>();
        try {
            Map<String, String> multiSegIdMap = new HashMap<>();
            multiSegIdMap.put("multiSegId", multiSegId);
            optRoads = keyCustTransferDao.queryOptRoadByMultiSegId(multiSegIdMap, bcShardingCode);

        } catch (Exception e) {
            log.error("根据光复用段查询光路失败! Api平台报错：{}", e);
            return Result.fail("查询光复用段路由失败！请联系管理员");
        }
        if (CollectionUtils.isEmpty(optRoads)) {
            log.error("光复用段中查询光路失败! 未找到对应的光路");
            return Result.ok(null);
        }
        List<OptMultiSegRouteInfo> optMultiSegRouteInfoList = new ArrayList<>();
        for (CmLink optRoad : optRoads) {
            OptMultiSegRouteInfo optMultiSegRouteInfo = new OptMultiSegRouteInfo();
            optMultiSegRouteInfo.setMultiSegId(optMultiSeg.getId().toString());
            optMultiSegRouteInfo.setMultiSegName(optMultiSeg.getName());
            StringBuilder textRouteBuilder = new StringBuilder();
            try {
                textRouteBuilder = this.queryTextRoute(optRoad, bcShardingCode);
            } catch (Exception e) {
                return Result.fail("查询光路路由失败！请联系管理员");
            }
            //补充局站信息
            Map<String, CmDevice> deviceMap = new HashMap<>();
            try {
                deviceMap = this.queryDevicesByOptRoads(optRoads, bcShardingCode);
            } catch (Exception e) {
                return Result.fail(e.getMessage());
            }

            CmDevice aDevice = (CmDevice) MapUtils.getObject(deviceMap, optRoad.getAPhysicDeviceId().toString());
            CmDevice zDevice = (CmDevice) MapUtils.getObject(deviceMap, optRoad.getZPhysicDeviceId().toString());
            String textRoute = this.supplyTmlInfo4TextRoute(textRouteBuilder, aDevice, zDevice);
            optMultiSegRouteInfo.setTextRoute(textRoute);
            optMultiSegRouteInfoList.add(optMultiSegRouteInfo);
        }
        return Result.ok(optMultiSegRouteInfoList);
    }

    @Override
    public Result queryRelayInfo(Map<String, String> param) {
        String ppmCode = MapUtils.getString(param, "cityCode");
        String relayName = MapUtils.getString(param, "relayName");
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (shardingEnum == null) {
            log.error("查询中继失败！城市编码：{}，未找到对应的分库", ppmCode);
            return Result.fail("查询中继失败！请联系管理员");
        }
        String bcShardingCode = shardingEnum.getBcShardingCode();
        List<CmLink> relayList = new ArrayList<>();
        try {
            CmLink cmLink = new CmLink();
            cmLink.setName(relayName);
            relayList = keyCustTransferDao.queryCmLink(cmLink, bcShardingCode);
        } catch (Exception e) {
            log.error("查询中继失败! Api平台报错：{}", e);
            return Result.fail("查询中继失败！请联系管理员");
        }
        if (CollectionUtils.isEmpty(relayList)) {
            log.error("未查询到中继");
            return Result.ok(null);
        }
        List<RelayInfo> relayInfos = new ArrayList<>();
        for (CmLink relay : relayList) {
            RelayInfo relayInfo = new RelayInfo();
            relayInfo.setRelayId(relay.getId().toString());
            relayInfo.setRelayName(relay.getName());
            BigDecimal aPortId = relay.getAPortId();
            if (aPortId != null) {
                relayInfo.setPortIdA(aPortId.toString());
            }
            BigDecimal zPortId = relay.getZPortId();
            if (zPortId != null) {
                relayInfo.setPortIdZ(zPortId.toString());
            }
            BigDecimal aPhysicDeviceId = relay.getAPhysicDeviceId();
            if (aPhysicDeviceId != null) {
                relayInfo.setDeviceIdA(aPhysicDeviceId.toString());
            }
            BigDecimal zhysicDeviceId = relay.getZPhysicDeviceId();
            if (zhysicDeviceId != null) {
                relayInfo.setDeviceIdZ(zhysicDeviceId.toString());
            }
            BigDecimal aTmlId = relay.getATmlId();
            if (aTmlId != null) {
                relayInfo.setStationIdA(aTmlId.toString());
            }
            BigDecimal zTmlId = relay.getZTmlId();
            if (zTmlId != null) {
                relayInfo.setStationIdZ(zTmlId.toString());
            }
            relayInfos.add(relayInfo);
        }
        return Result.ok(relayInfos);
    }

    @Override
    public Result queryRelayRoute(RelayRouteQueryDTO relayRouteQueryDTO) {

        String ppmCode = relayRouteQueryDTO.getPpmCode();
        String relayId = relayRouteQueryDTO.getRelayId();
        String portIdA = relayRouteQueryDTO.getPortIdA();
        String portIdZ = relayRouteQueryDTO.getPortIdZ();
        if ((StringUtils.isEmpty(portIdA) || StringUtils.isEmpty(portIdZ)) && StringUtils.isEmpty(relayId)) {
            log.error("查询中继路由失败！中继链路ID/中继AZ端口不能同时为空");
            return Result.fail("中继链路ID/中继AZ端口不能同时为空");
        }
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(ppmCode);
        if (shardingEnum == null) {
            log.error("查询中继路由失败！城市编码：{}，未找到对应的分库", ppmCode);
            return Result.fail("查询中继路由失败！请联系管理员");
        }
        String bcShardingCode = shardingEnum.getBcShardingCode();
        List<CmLink> relayList = new ArrayList<>();
        try {
            CmLink cmLink = new CmLink();
            if (StringUtils.isNotEmpty(relayId)) {
                cmLink.setId(new BigDecimal(relayId));
            }
            if (StringUtils.isNotEmpty(portIdA) && StringUtils.isNotEmpty(portIdZ)) {
                cmLink.setAPortId(new BigDecimal(portIdA));
                cmLink.setZPortId(new BigDecimal(portIdZ));
            }
            relayList = keyCustTransferDao.queryCmLink(cmLink, bcShardingCode);
        } catch (Exception e) {
            log.error("查询中继失败! Api平台报错：{}", e);
            return Result.fail("查询中继失败！请联系管理员");
        }
        relayList
                = relayList.stream()
                .filter(cmLink
                        -> cmLink.getSpecId().equals(Constant.ResType.MS_OTN_SINGLE_BAND)
                        || cmLink.getSpecId().equals(Constant.ResType.DWDM_OPTICAL_CHANNEL)
                        || cmLink.getSpecId().equals(Constant.ResType.SDH_MULTI_SEG)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relayList)) {
            log.error("未查询到中继");
            return Result.ok(null);
        }
        List<RelayRouteInfo> relayRouteInfoList = new ArrayList<>();

        for (CmLink relay : relayList) {
            List<CmLink> optRoadRouteList
                    = this.queryNextLinks(relay.getId().toString(), Constant.ResType.OPTICAL_LINK_ROUTE, bcShardingCode, 5);
            if (CollectionUtils.isEmpty(optRoadRouteList)) {
                log.error("查询中继路由失败！当前中继链路下没有找到光链路路由");
                continue;
            }
            List<BigDecimal> optRoadRouteIds = optRoadRouteList.stream().map(CmLink::getId).collect(Collectors.toList());
            List<CmLink> optRoads = new ArrayList<>();
            try {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("lowerLinkIds", optRoadRouteIds);
                paramMap.put("upperLinkSpecId", Constant.ResType.FIBER_OPTICAL);
                optRoads = keyCustTransferDao.queryUpperLinkByLowerLink(paramMap, bcShardingCode);

            } catch (Exception e) {
                log.error("根据光链路路由查询光路失败! Api平台报错：{}", e);
                return Result.fail("查询中继路由失败！请联系管理员");
            }
            if (CollectionUtils.isEmpty(optRoads)) {
                log.error("通过光链路路由查询光路失败! 未找到对应的光路, 光链路路由Id：{}", optRoadRouteIds);
                continue;
            }
            for (CmLink optRoad : optRoads) {
                RelayRouteInfo relayRouteInfo = new RelayRouteInfo();
                relayRouteInfo.setRelayId(relay.getId().toString());
                relayRouteInfo.setRelayName(relay.getName());
                StringBuilder textRouteBuilder = new StringBuilder();
                try {
                    textRouteBuilder = this.queryTextRoute(optRoad, bcShardingCode);
                } catch (Exception e) {
                    return Result.fail("查询光路路由失败！请联系管理员");
                }
                //补充局站信息
                Map<String, CmDevice> deviceMap = new HashMap<>();
                try {
                    deviceMap = this.queryDevicesByOptRoads(optRoads, bcShardingCode);
                } catch (Exception e) {
                    return Result.fail(e.getMessage());
                }

                CmDevice aDevice = (CmDevice) MapUtils.getObject(deviceMap, optRoad.getAPhysicDeviceId().toString());
                CmDevice zDevice = (CmDevice) MapUtils.getObject(deviceMap, optRoad.getZPhysicDeviceId().toString());
                String textRoute = this.supplyTmlInfo4TextRoute(textRouteBuilder, aDevice, zDevice);
                relayRouteInfo.setTextRoute(textRoute);
                relayRouteInfoList.add(relayRouteInfo);
            }
        }
        return Result.ok(relayRouteInfoList);
    }

    @Override
    public Result occupyResInfo(SyncOccupyResInfoDTO syncOccupyResInfoDTO) {
        String deficiencyResName = syncOccupyResInfoDTO.getDeficiencyResName();
        String deficiencyResType = syncOccupyResInfoDTO.getDeficiencyResType();
        String provinceCode = syncOccupyResInfoDTO.getProvinceCode();
        String deficiencyResId = syncOccupyResInfoDTO.getDeficiencyResId();
        String cityCode = syncOccupyResInfoDTO.getCityCode();
        List<OccupyResInfo> occupyResInfoList = syncOccupyResInfoDTO.getOccupyResInfoList();
        List<OccupyResInfoDO> occupyResInfoDOList = new ArrayList<>();
        for (OccupyResInfo occupyResInfo : occupyResInfoList) {
            OccupyResInfoDO occupyResInfoDO = new OccupyResInfoDO();
            occupyResInfoDO.setDeficiencyResType(deficiencyResType);
            occupyResInfoDO.setDeficiencyResName(deficiencyResName);
            occupyResInfoDO.setProvinceCode(provinceCode);
            occupyResInfoDO.setDeficiencyResId(deficiencyResId);
            occupyResInfoDO.setCityCode(cityCode);
            BeanUtils.copyProperties(occupyResInfo, occupyResInfoDO);
            OccupyPortInfo portInfoA = occupyResInfo.getPortInfoA();
            OccupyPortInfo portInfoZ = occupyResInfo.getPortInfoZ();
            if (portInfoA != null) {
                occupyResInfoDO.setAPortId(portInfoA.getPortId());
                occupyResInfoDO.setAPortName(portInfoA.getPortName());
                occupyResInfoDO.setADeviceId(portInfoA.getDeviceId());
                occupyResInfoDO.setADeviceName(portInfoA.getDeviceName());
                occupyResInfoDO.setAStationId(portInfoA.getStationId());
                occupyResInfoDO.setAStationName(portInfoA.getStationName());
            }
            if (portInfoZ != null) {
                occupyResInfoDO.setZPortId(portInfoZ.getPortId());
                occupyResInfoDO.setZPortName(portInfoZ.getPortName());
                occupyResInfoDO.setZDeviceId(portInfoZ.getDeviceId());
                occupyResInfoDO.setZDeviceName(portInfoZ.getDeviceName());
                occupyResInfoDO.setZStationId(portInfoZ.getStationId());
                occupyResInfoDO.setZStationName(portInfoZ.getStationName());
            }
            occupyResInfoDOList.add(occupyResInfoDO);
        }
        try {
            Map<String, List> occupyResInfoDOMap = new HashMap<>();
            occupyResInfoDOMap.put("occupyResInfoDOs",occupyResInfoDOList);
            keyCustTransferDao.occupyResInfo(occupyResInfoDOMap, "ds_graph_js");
        } catch (Exception e) {
            log.error("同步资源占用失败! Api平台报错：{}", e);
            throw new RuntimeException("同步资源占用失败！请联系管理员");
        }

        return Result.ok("接收成功");
    }

    @Override
    public Result queryBcInfoList(List<BcInfoQueryDTO> queryBcInfo) {
        List<BcInfoDTO> bcInfoDTOS = new ArrayList<>();
        for (BcInfoQueryDTO bcInfoQueryDTO : queryBcInfo) {
            bcInfoDTOS.addAll(this.queryBcInfo(bcInfoQueryDTO));
        }
        return Result.ok(bcInfoDTOS);
    }

    private List<BcInfoDTO> queryBcInfo(BcInfoQueryDTO bcInfoQueryDTO) {
        List<BcInfoDTO> bcInfoDTOS = new ArrayList<>();
        List<OccupyResInfoDO> occupyResInfoDOS = new ArrayList<>();
        try {
            OccupyResInfoDO occupyResInfoDO = new OccupyResInfoDO();
            occupyResInfoDO.setDeficiencySegStationId(bcInfoQueryDTO.getStationId());
            occupyResInfoDO.setDeficiencySegNextStationId(bcInfoQueryDTO.getNextStationId());
            occupyResInfoDOS = keyCustTransferDao.queryBcInfo(occupyResInfoDO, "ds_graph_js");
        } catch (Exception e) {
            log.error("查询占用资源失败! Api平台报错：{}", e);
            throw new RuntimeException("同步资源占用失败！请联系管理员");
        }
        if (CollectionUtils.isEmpty(occupyResInfoDOS)) {
            return bcInfoDTOS;
        }
        for (OccupyResInfoDO occupyResInfoDO : occupyResInfoDOS) {
            BcInfoDTO bcInfoDTO = new BcInfoDTO();
            bcInfoDTO.setBcSegId(bcInfoQueryDTO.getBcSegId());
            String occupyResPId = occupyResInfoDO.getOccupyResPId();
            bcInfoDTO.setResId(occupyResPId);
            bcInfoDTO.setResName(occupyResInfoDO.getOccupyResPName());
            bcInfoDTO.setResCode(occupyResInfoDO.getOccupyResPCode());
            bcInfoDTO.setResType(occupyResInfoDO.getOccupyResType());
            String cityCode = occupyResInfoDO.getCityCode();
            ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(cityCode);
            if (shardingEnum == null) {
                log.error("查询中继路由失败！城市编码：{}，未找到对应的分库", cityCode);
                continue;
            }
            String bcShardingCode = shardingEnum.getBcShardingCode();
            String aPortId = occupyResInfoDO.getAPortId();
            String zPortId = occupyResInfoDO.getZPortId();
            List<CmLink> cmLinks = new ArrayList<>();
            try {
                CmLink cmLink = new CmLink();
                cmLink.setSpecId(Constant.ResType.FIBER_OPTICAL);
                cmLink.setAPortId(new BigDecimal(aPortId));
                cmLink.setZPortId(new BigDecimal(zPortId));
                cmLinks = keyCustTransferDao.queryCmLink(cmLink, bcShardingCode);
            } catch (Exception e) {
                log.error("查询光路失败! Api平台报错：{}", e);
            }
            if (CollectionUtils.isNotEmpty(cmLinks)) {
                StringBuilder textRoute = this.queryTextRoute(cmLinks.get(0), bcShardingCode);
                bcInfoDTO.setTextRoute(textRoute.toString());
            }
            OccupyPortInfo aPortInfo = new OccupyPortInfo();
            aPortInfo.setPortId(aPortId);
            aPortInfo.setPortName(occupyResInfoDO.getAPortName());
            aPortInfo.setDeviceId(occupyResInfoDO.getADeviceId());
            aPortInfo.setDeviceName(occupyResInfoDO.getADeviceName());
            aPortInfo.setStationId(occupyResInfoDO.getAStationId());
            aPortInfo.setStationName(occupyResInfoDO.getAStationName());
            bcInfoDTO.setPortInfoA(aPortInfo);
            OccupyPortInfo zPortInfo = new OccupyPortInfo();
            zPortInfo.setPortId(zPortId);
            zPortInfo.setPortName(occupyResInfoDO.getZPortName());
            zPortInfo.setDeviceId(occupyResInfoDO.getZDeviceId());
            zPortInfo.setDeviceName(occupyResInfoDO.getZDeviceName());
            zPortInfo.setStationId(occupyResInfoDO.getZStationId());
            zPortInfo.setStationName(occupyResInfoDO.getZStationName());
            bcInfoDTO.setPortInfoA(zPortInfo);
            bcInfoDTOS.add(bcInfoDTO);
        }
        return bcInfoDTOS;
    }

    public StringBuilder queryTextRoute(CmLink optRoad, String bcShardingCode) {
        List<CmLink> crossConnectList = new ArrayList<>();
        try {
            Map<String, BigDecimal> optRoadIdMap = new HashMap<>();
            optRoadIdMap.put("optRoadId", optRoad.getId());
            crossConnectList = keyCustTransferDao.queryCrossConnectByOptRoadId(optRoadIdMap, bcShardingCode);
        } catch (Exception e) {
            log.error("根据光路ID查询关联下面所有的跳接失败! Api平台报错：{}", e);
            throw new RuntimeException("查询光路路由失败！请联系管理员");
        }
        BigDecimal aPortId = optRoad.getAPortId();
        String aPhysicDeviceName = optRoad.getAPhysicDeviceName();
        String optRoadCode = optRoad.getCode();
        StringBuilder textRoute = new StringBuilder();
        while (true) {
            textRoute.append(aPhysicDeviceName);
            BigDecimal finalAPortId = aPortId;
            List<CmLink> currentLinks
                    = crossConnectList.stream()
                    .filter(crossConnect -> crossConnect.getAPortId().equals(finalAPortId)
                            || crossConnect.getZPortId().equals(finalAPortId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currentLinks) || aPortId.equals(optRoad.getZPortId())) {
                break;
            }
            CmLink cmLink = currentLinks.get(0);
            //获取和当前链路同设备的链路
            BigDecimal aPhysicDeviceId = cmLink.getAPhysicDeviceId();
            BigDecimal zPhysicDeviceId = cmLink.getZPhysicDeviceId();
            List<CmLink> currentSameDeviceLinks
                    = crossConnectList.stream()
                    .filter(crossConnect -> (crossConnect.getAPhysicDeviceId().equals(aPhysicDeviceId)
                            || crossConnect.getAPhysicDeviceId().equals(zPhysicDeviceId)) && (crossConnect.getZPhysicDeviceId().equals(aPhysicDeviceId)
                            || crossConnect.getZPhysicDeviceId().equals(zPhysicDeviceId))).collect(Collectors.toList());
            List<Long> specIds = currentSameDeviceLinks.stream().map(CmLink::getSpecId).distinct().collect(Collectors.toList());
            textRoute.append("《");
            if (specIds.size() == 1 && !specIds.get(0).equals(Constant.ResType.OFFICE_FIBER)) {
                textRoute.append("跳接");
            }
            //如果是局向光纤
            if (specIds.size() == 1 && specIds.get(0).equals(Constant.ResType.OFFICE_FIBER)) {
                textRoute.append(optRoadCode);
                textRoute.append("[");
                //查询光缆
                try {
                    Map<String, BigDecimal> optFiberIdMap = new HashMap<>();
                    optFiberIdMap.put("optFiberId", cmLink.getId());
                    List<CmLink> optCables
                            = keyCustTransferDao.queryOptCableByOptFiber(optFiberIdMap, bcShardingCode);
                    if (CollectionUtils.isNotEmpty(optCables)) {
                        String optCableCode = optCables.get(0).getCode();
                        textRoute.append(optCableCode);
                    }
                    textRoute.append("(");
                    for (CmLink optFiber : currentSameDeviceLinks) {
                        textRoute.append(optFiber.getCode());
                        textRoute.append("&");
                    }
                    textRoute.delete(textRoute.length() - 1, textRoute.length());
                    textRoute.append(")");
                } catch (Exception e) {
                    log.error("根据局向光纤查询光缆失败! Api平台报错：{}", e);
                    continue;
                }
                //拼接局向光纤
                textRoute.append("]");

            }
            //当通过以设备为起始查询时，可以查到多种规格的跳接，所以需要再次判断，这里已经改为通过端口查询，所以去除了
//            if (specIds.size() > 1) {
//                currentSameDeviceLinks
//                        = currentSameDeviceLinks.stream()
//                        .filter(crossConnect -> !crossConnect.getSpecId().equals(Constant.ResType.OFFICE_FIBER)
//                                && crossConnect.getAPortId().equals(finalAPortId)
//                                && crossConnect.getZPortId().equals(finalAPortId))
//                        .collect(Collectors.toList());
//                cmLink = currentSameDeviceLinks.get(0);
//                if (CollectionUtils.isNotEmpty(currentSameDeviceLinks)) {
//                    textRoute.append("跳接");
//                }
//            }
            textRoute.append("》");
            if (aPortId.equals(cmLink.getAPortId())
                    && !aPortId.equals(cmLink.getZPortId())) {
                aPortId = cmLink.getZPortId();
                aPhysicDeviceName = cmLink.getZPhysicDeviceName();
            } else if (aPortId.equals(cmLink.getZPortId())
                    && !aPortId.equals(cmLink.getAPortId())) {
                aPortId = cmLink.getAPortId();
                aPhysicDeviceName = cmLink.getAPhysicDeviceName();
            } else if (aPortId.equals(cmLink.getAPortId())
                    && aPortId.equals(cmLink.getZPortId())) {
                //不需要处理
            }
            crossConnectList.removeAll(currentSameDeviceLinks);
        }

        return textRoute;
    }

    public String supplyTmlInfo4TextRoute(StringBuilder textRoute, CmDevice aDevice, CmDevice zDevice) {
        //补充局站信息
        StringBuilder resultBuilder = new StringBuilder();
        if (aDevice != null) {
            String tmlId = aDevice.getTmlId();
            if (StringUtils.isNotEmpty(tmlId)) {
                resultBuilder.append(aDevice.getTmlName());
            }
        }
        resultBuilder.append("【").append(textRoute).append("】");
        if (zDevice != null) {
            String tmlId = zDevice.getTmlId();
            if (StringUtils.isNotEmpty(tmlId)) {
                resultBuilder.append(zDevice.getTmlName()).toString();
            }

        }
        return resultBuilder.toString();
    }

    public Map<String, CmDevice> queryDevicesByOptRoads(List<CmLink> optRoads, String bcShardingCode) {
        List<BigDecimal> deviceIds
                = optRoads.stream().map(CmLink::getAPhysicDeviceId).collect(Collectors.toList());
        deviceIds.addAll(optRoads.stream().map(CmLink::getZPhysicDeviceId).collect(Collectors.toList()));

        Map<String, CmDevice> deviceMap = new HashMap<>();
        try {
            Map<String, List<BigDecimal>> deviceIdsMap = new HashMap<>();
            deviceIdsMap.put("ids", deviceIds);
            List<CmDevice> cmDevices = keyCustTransferDao.queryDeviceByIds(deviceIdsMap, bcShardingCode);
            if (CollectionUtils.isNotEmpty(cmDevices)) {
                deviceMap
                        = cmDevices.stream().collect(Collectors.toMap(CmDevice::getId, device -> device));

            }
        } catch (Exception e) {
            log.error("根据设备ID查询关联下面所有的局站失败! Api平台报错：{}", e);
            throw new RuntimeException("查询站点信息失败！请联系管理员");
        }
        return deviceMap;

    }

    /**
     * 根据上级链路递归查找指定规格的下级链路
     */
    public List<CmLink> queryNextLinks(String upperLinkId, Long lowerLinkSpecId, String bcShardingCode, int deep) {

        List<CmLink> lowerLinks = new ArrayList<>();
        List<CmLink> needLowerLinks = new ArrayList<>();
        if (deep == 0) {
            return needLowerLinks;
        }
        try {
            Map<String, String> upperLinkIdMap = new HashMap<>();
            upperLinkIdMap.put("upperLinkId", upperLinkId);
            lowerLinks = keyCustTransferDao.queryLowerLinkByUpperLink(upperLinkIdMap, bcShardingCode);
        } catch (Exception e) {
            log.error("查询下级链路失败失败! Api平台报错：{}", e);
            throw new RuntimeException("查询下级链路失败！请联系管理员");
        }
        if (CollectionUtils.isEmpty(lowerLinks)) {
            return needLowerLinks;
        }
        needLowerLinks =  lowerLinks.stream().filter(cmLink -> cmLink.getSpecId().equals(lowerLinkSpecId)).collect(Collectors.toList());
        List<CmLink> upperLinkHasLowerList = lowerLinks.stream().filter(cmLink
                -> !cmLink.getSpecId().equals(lowerLinkSpecId)
                && cmLink.getSpecId().equals(Constant.ResType.SOFT_PATCHEING)
                && !cmLink.getSpecId().equals(Constant.ResType.HARD_PATCHEING)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(upperLinkHasLowerList)) {
            return needLowerLinks;
        }
        for (CmLink cmLink : upperLinkHasLowerList) {
            needLowerLinks.addAll(this.queryNextLinks(cmLink.getId().toString(), lowerLinkSpecId, bcShardingCode, deep--));
        }
        return needLowerLinks;
    }
}
