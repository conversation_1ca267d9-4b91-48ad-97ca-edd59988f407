package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.DualRouteStatisticsDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.DualRouteStatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 双路由光路检测运营统计服务实现类
 * 
 * <AUTHOR> Generated
 * @date 2025-06-16
 */
@Service
public class DualRouteStatisticsServiceImpl implements DualRouteStatisticsService {

    private static final Logger log = LoggerFactory.getLogger(DualRouteStatisticsServiceImpl.class);

    @Autowired
    private DualRouteStatisticsDao dualRouteStatisticsDao;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public JSONObject getMonthlyStatistics(JSONObject param) {
        try {
            log.info("获取双路由月度运营统计数据，参数: {}", param);
            
            // 参数验证和处理
            JSONObject processedParam = validateAndProcessParams(param);
            String shardingCode = processedParam.getString("shardingCode");
            
            // 调用DAO查询数据
            JSONObject result = dualRouteStatisticsDao.getDualRouteMonthlyStatistics(processedParam, shardingCode);

            // 在Java层计算汇总数据
            if (result.getBoolean("success") && result.containsKey("data")) {
                JSONObject data = result.getJSONObject("data");
                JSONArray monthlyStats = data.getJSONArray("monthlyStats");

                // 计算汇总指标
                JSONObject summary = calculateSummaryStatistics(monthlyStats);
                data.put("summary", summary);
            }

            log.info("双路由月度统计查询完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取双路由月度统计数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 计算汇总统计数据
     */
    private JSONObject calculateSummaryStatistics(JSONArray monthlyStats) {
        int totalRemediation = 0;
        int totalConfirmation = 0;
        int totalImprovement = 0;
        int totalDeterioration = 0;
        int totalChecks = 0;

        for (int i = 0; i < monthlyStats.size(); i++) {
            JSONObject record = monthlyStats.getJSONObject(i);
            totalRemediation += record.getIntValue("remediation_count");
            totalConfirmation += record.getIntValue("confirmation_count");
            totalImprovement += record.getIntValue("improvement_count");
            totalDeterioration += record.getIntValue("deterioration_count");
            totalChecks += record.getIntValue("total_checks");
        }

        JSONObject summary = new JSONObject();
        summary.put("totalRemediation", totalRemediation);
        summary.put("totalConfirmation", totalConfirmation);
        summary.put("totalImprovement", totalImprovement);
        summary.put("totalDeterioration", totalDeterioration);
        summary.put("totalChecks", totalChecks);

        // 计算比例
        if (totalChecks > 0) {
            summary.put("remediationRate", Math.round(totalRemediation * 100.0 / totalChecks * 100) / 100.0);
            summary.put("improvementRate", Math.round(totalImprovement * 100.0 / totalChecks * 100) / 100.0);
            summary.put("deteriorationRate", Math.round(totalDeterioration * 100.0 / totalChecks * 100) / 100.0);
        } else {
            summary.put("remediationRate", 0.0);
            summary.put("improvementRate", 0.0);
            summary.put("deteriorationRate", 0.0);
        }

        return summary;
    }

    /**
     * 计算趋势汇总数据
     */
    private JSONObject calculateTrendSummary(JSONArray monthlyTrend) {
        int totalDetections = 0;
        int totalProblems = 0;
        double avgProblemRate = 0.0;

        for (int i = 0; i < monthlyTrend.size(); i++) {
            JSONObject record = monthlyTrend.getJSONObject(i);
            totalDetections += record.getIntValue("total_detections");
            totalProblems += record.getIntValue("problem_detections");
        }

        if (totalDetections > 0) {
            avgProblemRate = Math.round(totalProblems * 100.0 / totalDetections * 100) / 100.0;
        }

        JSONObject summary = new JSONObject();
        summary.put("totalDetections", totalDetections);
        summary.put("totalProblems", totalProblems);
        summary.put("avgProblemRate", avgProblemRate);
        summary.put("analysisMonths", monthlyTrend.size());

        return summary;
    }

    @Override
    public JSONObject getTrendAnalysis(JSONObject param) {
        try {
            log.info("获取双路由趋势分析数据，参数: {}", param);
            
            // 参数验证和处理
            JSONObject processedParam = validateAndProcessParams(param);
            String shardingCode = processedParam.getString("shardingCode");
            
            // 调用DAO查询数据
            JSONObject result = dualRouteStatisticsDao.getDualRouteTrendAnalysis(processedParam, shardingCode);

            // 在Java层计算趋势汇总数据
            if (result.getBoolean("success") && result.containsKey("data")) {
                JSONObject data = result.getJSONObject("data");
                JSONArray monthlyTrend = data.getJSONArray("monthlyTrend");

                // 计算趋势汇总指标
                JSONObject summary = calculateTrendSummary(monthlyTrend);
                data.put("summary", summary);
            }

            log.info("双路由趋势分析查询完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取双路由趋势分析数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return errorResult;
        }
    }





    @Override
    public JSONObject getChangeStatistics(JSONObject param) {
        try {
            log.info("获取双路由变化统计数据，参数: {}", param);

            // 参数验证和处理
            JSONObject processedParam = validateAndProcessParams(param);
            String shardingCode = processedParam.getString("shardingCode");

            // 智能设置历史记录限制
            Integer historyLimit = processedParam.getInteger("historyLimit");
            String groupId = processedParam.getString("groupId");

            if (historyLimit == null) {
                if (groupId != null && !groupId.trim().isEmpty()) {
                    // 单个光路组查询时，默认查看更多历史记录
                    historyLimit = 20;
                    log.info("单个光路组查询，设置historyLimit为20");
                } else {
                    // 全量统计时，只查看最新的变化
                    historyLimit = 2;
                    log.info("全量统计查询，设置historyLimit为2");
                }
                processedParam.put("historyLimit", historyLimit);
            } else {
                // 对用户设置的historyLimit进行合理性检查
                if (groupId == null || groupId.trim().isEmpty()) {
                    // 全量查询时，限制historyLimit不要太大，避免性能问题
                    if (historyLimit > 5) {
                        log.warn("全量查询时historyLimit设置过大({}), 调整为5", historyLimit);
                        historyLimit = 5;
                        processedParam.put("historyLimit", historyLimit);
                    }
                } else {
                    // 单个光路组查询时，限制最大值
                    if (historyLimit > 50) {
                        log.warn("单个光路组查询时historyLimit设置过大({}), 调整为50", historyLimit);
                        historyLimit = 50;
                        processedParam.put("historyLimit", historyLimit);
                    }
                }
            }

            log.info("最终使用的historyLimit: {}, groupId: {}", historyLimit, groupId);

            // 调用DAO查询数据
            JSONObject result = dualRouteStatisticsDao.getDualRouteChangeStatistics(processedParam, shardingCode);

            log.info("双路由变化统计查询完成");
            return result;
        } catch (Exception e) {
            log.error("获取双路由变化统计数据失败", e);
            return createErrorResponse("获取变化统计数据失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject getRiskObjects(JSONObject param) {
        try {
            log.info("获取双路由风险对象，参数: {}", param);
            
            // 参数验证和处理
            JSONObject processedParam = validateAndProcessParams(param);
            String shardingCode = processedParam.getString("shardingCode");
            
            // 调用DAO查询数据
            JSONObject result = dualRouteStatisticsDao.getDualRouteRiskObjects(processedParam, shardingCode);
            
            log.info("双路由风险对象查询完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取双路由风险对象失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public JSONObject getKpiSummary(JSONObject param) {
        try {
            log.info("获取双路由KPI汇总，参数: {}", param);
            
            // 参数验证和处理
            JSONObject processedParam = validateAndProcessParams(param);
            String shardingCode = processedParam.getString("shardingCode");
            
            // 处理时间范围参数
            String timeRange = param.getString("timeRange");
            if (StringUtils.hasText(timeRange)) {
                setTimeRangeParams(processedParam, timeRange);
            }
            
            // 调用DAO查询数据
            JSONObject result = dualRouteStatisticsDao.getDualRouteKpiSummary(processedParam, shardingCode);
            
            log.info("双路由KPI汇总查询完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取双路由KPI汇总失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public JSONObject getAreaComparison(JSONObject param) {
        try {
            log.info("获取双路由地区对比分析，参数: {}", param);
            
            // 参数验证和处理
            JSONObject processedParam = validateAndProcessParams(param);
            String shardingCode = processedParam.getString("shardingCode");
            
            // 调用DAO查询数据
            JSONObject result = dualRouteStatisticsDao.getDualRouteAreaComparison(processedParam, shardingCode);
            
            log.info("双路由地区对比分析查询完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取双路由地区对比分析失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public JSONObject getProblemAnalysis(JSONObject param) {
        try {
            log.info("获取双路由问题类型分析，参数: {}", param);
            
            // 参数验证和处理
            JSONObject processedParam = validateAndProcessParams(param);
            String shardingCode = processedParam.getString("shardingCode");
            
            // 调用DAO查询数据
            JSONObject result = dualRouteStatisticsDao.getDualRouteProblemAnalysis(processedParam, shardingCode);
            
            log.info("双路由问题类型分析查询完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取双路由问题类型分析失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public JSONObject getExportData(JSONObject param) {
        try {
            log.info("获取双路由导出数据，参数: {}", param);
            
            // 参数验证和处理
            JSONObject processedParam = validateAndProcessParams(param);
            String shardingCode = processedParam.getString("shardingCode");
            
            // 调用DAO查询数据
            JSONObject result = dualRouteStatisticsDao.getDualRouteExportData(processedParam, shardingCode);
            
            log.info("双路由导出数据查询完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取双路由导出数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public JSONObject validateAndProcessParams(JSONObject param) {
        if (param == null) {
            param = new JSONObject();
        }
        
        JSONObject processedParam = new JSONObject();
        processedParam.putAll(param);
        
        // 处理分库代码
        String shardingCode = param.getString("shardingCode");
        if (!StringUtils.hasText(shardingCode)) {
            // 如果没有提供分库代码，使用默认分库代码
            // 注意：在实际使用中，shardingCode应该由调用方明确指定
            shardingCode = NRMConstants.SHARDING_CODE;
            processedParam.put("shardingCode", shardingCode);
        }

        // 移除areaCode参数，因为通过shardingCode已经确定了查询的分库
        // SQL层面会查询该分库下的所有数据，不需要再按areaCode筛选
        processedParam.remove("areaCode");
        
        // 处理时间参数
        String startDate = param.getString("startDate");
        String endDate = param.getString("endDate");
        
        if (StringUtils.hasText(startDate)) {
            processedParam.put("startDate", formatDateParam(startDate, false));
        }
        
        if (StringUtils.hasText(endDate)) {
            processedParam.put("endDate", formatDateParam(endDate, true));
        }
        
        // 处理分页参数
        Integer pageSize = param.getInteger("pageSize");
        Integer currentPage = param.getInteger("currentPage");
        
        if (pageSize == null || pageSize <= 0) {
            processedParam.put("pageSize", 20);
        }
        
        if (currentPage == null || currentPage <= 0) {
            processedParam.put("currentPage", 1);
        }
        
        return processedParam;
    }

    @Override
    public String formatDateParam(String dateStr, boolean isEndDate) {
        if (!StringUtils.hasText(dateStr)) {
            return null;
        }

        try {
            // 如果已经是完整的日期时间格式，直接返回
            if (dateStr.length() == 19 && dateStr.contains(" ")) {
                return dateStr;
            }

            // 如果只有日期部分，补充时间部分
            if (dateStr.length() == 10) {
                if (isEndDate) {
                    return dateStr + " 23:59:59";
                } else {
                    return dateStr + " 00:00:00";
                }
            }

            // 其他格式尝试解析
            Date date = DATE_FORMAT.parse(dateStr);
            return DATE_FORMAT.format(date);

        } catch (Exception e) {
            log.warn("日期格式化失败: {}", dateStr, e);
            return dateStr;
        }
    }

    @Override
    public Double calculateRate(Integer numerator, Integer denominator) {
        if (denominator == null || denominator == 0) {
            return 0.0;
        }

        if (numerator == null) {
            numerator = 0;
        }

        double rate = (double) numerator / denominator * 100;
        return Math.round(rate * 100.0) / 100.0; // 保留2位小数
    }

    @Override
    public JSONObject generateOperationalReport(JSONObject param) {
        try {
            log.info("生成双路由运营统计报告，参数: {}", param);

            JSONObject report = new JSONObject();

            // 获取月度统计数据
            JSONObject monthlyStats = getMonthlyStatistics(param);

            // 获取趋势分析数据
            JSONObject trendAnalysis = getTrendAnalysis(param);

            // 组装报告
            report.put("reportTitle", "双路由光路检测运营统计报告");
            report.put("generateTime", DATE_FORMAT.format(new Date()));
            report.put("queryParams", param);
            report.put("monthlyStatistics", monthlyStats);
            report.put("trendAnalysis", trendAnalysis);

            // 生成报告摘要
            JSONObject summary = generateReportSummary(monthlyStats, trendAnalysis);
            report.put("summary", summary);

            log.info("双路由运营统计报告生成完成");
            return report;

        } catch (Exception e) {
            log.error("生成双路由运营统计报告失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "报告生成失败: " + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public String getMetricTypeDescription(String metricType) {
        if (!StringUtils.hasText(metricType)) {
            return "未知";
        }

        switch (metricType.toLowerCase()) {
            case "remediation":
                return "问题整改";
            case "confirmation":
                return "状态确认";
            case "improvement":
                return "状态改善";
            case "deterioration":
                return "状态恶化";
            default:
                return "其他变化";
        }
    }

    @Override
    public String getSeverityDescription(String severity) {
        if (!StringUtils.hasText(severity)) {
            return "未知";
        }

        switch (severity.toLowerCase()) {
            case "severe":
                return "严重问题";
            case "moderate":
                return "一般问题";
            case "normal":
                return "正常状态";
            default:
                return "未知状态";
        }
    }

    /**
     * 设置时间范围参数
     *
     * @param param 参数对象
     * @param timeRange 时间范围 ('1m', '3m', '6m', '1y')
     */
    private void setTimeRangeParams(JSONObject param, String timeRange) {
        Calendar calendar = Calendar.getInstance();
        Date endDate = calendar.getTime();

        switch (timeRange.toLowerCase()) {
            case "1m":
                calendar.add(Calendar.MONTH, -1);
                break;
            case "3m":
                calendar.add(Calendar.MONTH, -3);
                break;
            case "6m":
                calendar.add(Calendar.MONTH, -6);
                break;
            case "1y":
                calendar.add(Calendar.YEAR, -1);
                break;
            default:
                calendar.add(Calendar.MONTH, -3); // 默认3个月
                break;
        }

        Date startDate = calendar.getTime();

        param.put("startDate", DATE_FORMAT.format(startDate));
        param.put("endDate", DATE_FORMAT.format(endDate));
    }

    /**
     * 生成报告摘要
     *
     * @param monthlyStats 月度统计数据
     * @param trendAnalysis 趋势分析数据
     * @return 报告摘要
     */
    private JSONObject generateReportSummary(JSONObject monthlyStats, JSONObject trendAnalysis) {
        JSONObject summary = new JSONObject();

        try {
            // 从月度统计中提取关键指标
            if (monthlyStats.getBoolean("success") && monthlyStats.containsKey("data")) {
                JSONObject monthlyData = monthlyStats.getJSONObject("data");
                if (monthlyData.containsKey("summary")) {
                    JSONObject monthlySummary = monthlyData.getJSONObject("summary");
                    summary.put("totalRemediation", monthlySummary.getInteger("totalRemediation"));
                    summary.put("totalConfirmation", monthlySummary.getInteger("totalConfirmation"));
                    summary.put("totalImprovement", monthlySummary.getInteger("totalImprovement"));
                    summary.put("totalDeterioration", monthlySummary.getInteger("totalDeterioration"));
                    summary.put("remediationRate", monthlySummary.getDouble("remediationRate"));
                }
            }

            // 从趋势分析中提取关键指标
            if (trendAnalysis.getBoolean("success") && trendAnalysis.containsKey("data")) {
                JSONObject trendData = trendAnalysis.getJSONObject("data");
                if (trendData.containsKey("summary")) {
                    JSONObject trendSummary = trendData.getJSONObject("summary");
                    summary.put("avgProblemRate", trendSummary.getDouble("avgProblemRate"));
                    summary.put("analysisMonths", trendSummary.getInteger("analysisMonths"));
                }
            }

            // 生成结论和建议
            summary.put("conclusion", generateConclusion(summary));
            summary.put("recommendations", generateRecommendations(summary));

        } catch (Exception e) {
            log.warn("生成报告摘要时出现异常", e);
        }

        return summary;
    }

    /**
     * 生成结论
     */
    private String generateConclusion(JSONObject summary) {
        StringBuilder conclusion = new StringBuilder();

        Double remediationRate = summary.getDouble("remediationRate");
        Double avgProblemRate = summary.getDouble("avgProblemRate");

        if (remediationRate != null && remediationRate > 80) {
            conclusion.append("整改效率较高，");
        } else if (remediationRate != null && remediationRate > 60) {
            conclusion.append("整改效率中等，");
        } else {
            conclusion.append("整改效率有待提升，");
        }

        if (avgProblemRate != null && avgProblemRate < 10) {
            conclusion.append("系统运行状态良好。");
        } else if (avgProblemRate != null && avgProblemRate < 20) {
            conclusion.append("系统运行状态一般。");
        } else {
            conclusion.append("系统问题较多，需要重点关注。");
        }

        return conclusion.toString();
    }

    /**
     * 生成建议
     */
    private String generateRecommendations(JSONObject summary) {
        StringBuilder recommendations = new StringBuilder();

        Double remediationRate = summary.getDouble("remediationRate");
        Double avgProblemRate = summary.getDouble("avgProblemRate");

        if (remediationRate != null && remediationRate < 70) {
            recommendations.append("1. 加强问题整改流程管理，提高整改效率；");
        }

        if (avgProblemRate != null && avgProblemRate > 15) {
            recommendations.append("2. 增加预防性维护，减少问题发生率；");
        }

        recommendations.append("3. 建立定期巡检机制，及时发现和处理潜在问题；");
        recommendations.append("4. 加强人员培训，提升运维技能水平。");

        return recommendations.toString();
    }

    /**
     * 创建错误响应
     */
    private JSONObject createErrorResponse(String message) {
        JSONObject errorResult = new JSONObject();
        errorResult.put("success", false);
        errorResult.put("message", message);
        return errorResult;
    }
}
