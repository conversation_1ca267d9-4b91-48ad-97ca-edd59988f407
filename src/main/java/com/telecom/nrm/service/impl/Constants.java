package com.telecom.nrm.service.impl;

import com.google.common.collect.ImmutableMap;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class Constants {

    /**
     * 配置各种接入方式支持的产品
     */
    public static final Map<String, List<String>> accessModeMapProduct = new ImmutableMap.Builder<String,List<String>>()
            .put("102079", Arrays.asList("6600118000","2008078003","6600582000","6600409131")) // SDH接入 互联网专线、IP虚拟专网、云专网2.0、云专网3.0
            .put("102084",Arrays.asList("2008078005")) // SDH接入 支持 SDH产品
            .put("114150", Arrays.asList("2008078006")) // MSTP接入 支持 MSTP产品
            .put("114131", Arrays.asList("6600328000")) // IPRAN接入 支持 IPAN产品
            .build();

    /**
     * 产品类型对应名称配置
     */
    public static final Map<String,String> productIdMapName = new ImmutableMap.Builder<String,String>()
            .put("6600328000", "IPRAN")
            .put("2008078003","IP虚拟专网")
            .put("2008078006","MSTP")
            .put("6600562000","OTN精品专线")
            .put("2008078005","SDH")
            .put("6600582000","云专网2.0")
            .put("6600409131","云专网3.0")
            .put("6600118000","互联网专线")
            .build();

    public static final Map<String,String> productCodeMap = new ImmutableMap.Builder<String,String>()
            .put("14", "6600118000")
            .put("12", "6600118000")

            .put("6600582000", "6600582000")


            .put("6600802000", "6600802000")
            .put("6600328000", "6600328000")

            .put("100001116", "6600582000")
            .put("100001120", "6600582000")
            .put("100001121", "6600582000")
            .put("40", "2008078005")
            .build();


    public static  final Map<String,String> accessModeIdMapName = new ImmutableMap.Builder<String,String>()
            .put("102079", "FTTH接入")
            .put("102081","FTTO接入")
            .put("102083","LAN接入")
            .put("102084","SDH接入")
            .put("114131","IPRAN接入")
            .put("114134","OTN接入")
            .put("114150","MSTP接入")
            .put("102076","FTTB+LAN接入")
            .put("102077","FTTB接入")
            .put("102089","传输接入")
            .put("102091","光纤接入")
            .build();
}
