package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.dao.GraphDao;
import com.telecom.nrm.dao.GraphPathDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.GraphScene;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.service.GraphService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import java.util.UUID;

@Service
public class GraphServiceImpl implements GraphService {

    @Autowired(required = true)
    GraphDao graphDao;

    @Autowired(required = true)
    GraphPathDao graphPathDao;



    public boolean checkNoCircle (Graph graph) {
        Set<Node> visitSet = new HashSet<>();
        // Graph g = JSONObject.parseObject(JSONObject.toJSONString(graph),Graph.class);
        Graph g = graph.clone();
        while (true) {
            Set<Node> firstNodes=g.findFirstNodes();
            if (firstNodes.size()==0) {
                break;
            }
            for (Node n: firstNodes) {
                visitSet.add(n);
                g.deleteNode(n);
            }
        }
        if (g.getNodes().size()>0) {
            return false; // 存在环，不通过
        } else {
            return true;
        }
    }

    public List<Node> walkNodes(Graph graph) {
        List<Node> visitList = new ArrayList<>();
        // Graph g = JSONObject.parseObject(JSONObject.toJSONString(graph),Graph.class);
        Graph g = graph.clone();
        while (true) {
            Set<Node> firstNodes=g.findFirstNodes();
            if (firstNodes.size()==0) {
                break;
            }
            for (Node n: firstNodes) {
                visitList.add(n);
                g.deleteNode(n);
            }
        }
        return visitList;
    }

    public boolean checkStartNode (Graph graph, String startNode) {
        Set<Node> nodeSet= graph.findFirstNodes();
        if (nodeSet.size()!=1) {
            return false; // 起点必须有且只有一个
        }
        for (Node n: nodeSet) {
            if (n.getId().equals(startNode)) {
                return true;
            }
        }
        return false;
    }

    public Graph reload(Graph graph) {
        Graph newGraph = new Graph();
        Set<Node> nodes = graphDao.reloadNodes(graph.getNodes());
        Set<Edge> edges = graphDao.reloadEdges(graph.getEdges());
        newGraph.setEdges(edges);
        newGraph.setNodes(nodes);
        return newGraph;
    }

    @Override
    public Graph parsePaths(String[] paths) {
        return graphPathDao.parseGraph(paths);
    }

    @Override
    public JSONObject getData(Graph graph) {
        JSONObject result =new JSONObject();
        List<Node> graphNodes = this.walkNodes(graph);
        List<Edge> graphEdges= new ArrayList<>();
        graphEdges.addAll(graph.getEdges());

        List<JSONObject> nodes = new ArrayList<>();
        for (Node graphNode: graphNodes) {
            List<JSONObject> data = graphNode.getData();
            for (JSONObject d:data) {
                d.put("label_code", graphNode.getCode());
                d.put("label_name", graphNode.getName());
                d.put("gid", graphNode.getCode()+"$$"+d.getString("id"));
                nodes.add(d);
            }
        }

        List<JSONObject> relations = new ArrayList<>();
        for (Edge edge: graphEdges) {
            List<JSONObject> data = edge.getData();
            for (JSONObject d:data) {
                if (!edge.isReverse()) {

                    d.put("a_gid", edge.getAId()+"$$"+d.getString("a_id"));
                    d.put("b_gid", edge.getBId()+"$$" +d.getString("b_id"));
                }else{
                    d.put("a_gid", edge.getBId()+"$$" +d.getString("b_id"));
                    d.put("b_gid", edge.getAId()+"$$" +d.getString("a_id"));
                }
                relations.add(d);
            }
        }
        result.put("nodes", nodes);
        result.put("relations", relations);
        return JSONObject.parseObject(JSONObject.toJSONString(result));
    }

    public JSONObject getPaths(JSONObject data, List<String> labels) {
        JSONObject result = new JSONObject();

        int length = labels.size();
        List<JSONObject> allPaths = new ArrayList<>();
        Set<String> nodeIds = new HashSet<>();
        for (int i=0;i<length-1;i++) {
            String start= labels.get(i);
            String end = labels.get(i+1);
            List<JSONObject> paths = getPaths(data, start, end);
            Map<String, JSONObject> pathMap = new HashMap<>();
            for (JSONObject p:paths) {
                pathMap.put(p.getString("source_id") + "_"+p.getString("target_id"),p);
                nodeIds.add(p.getString("source_id"));
                nodeIds.add(p.getString("target_id"));
            }
            allPaths.addAll(pathMap.values());
        }
        List<JSONObject> nodes = JSONObjectUtil.jsonObjectArrayToList(data.getJSONArray("nodes"));
        /*
        nodes = nodes.stream().filter(n-> {
            for (JSONObject p: allPaths) {
                if (p.getString("source_id").equals(n.getString("gid")) || p.getString("target_id").equals(n.getString("gid"))) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
         */
        nodes=nodes.stream().filter(n->nodeIds.contains(n.getString("gid"))).collect(Collectors.toList());
        result.put("nodes", nodes);
        result.put("paths", allPaths);
        return result;
    }

    public List<JSONObject> filterNodeByLabel(JSONObject data,String labelName) {
        List<JSONObject> resultList = new ArrayList<>();
        List<JSONObject> list = JSONObjectUtil.jsonObjectArrayToList(data.getJSONArray("nodes"));
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i) == null || list.get(i).getString("label_name") == null) {
                    continue;
                }
                if (list.get(i).getString("label_name").equals(labelName)) {
                    resultList.add(list.get(i));
                }
            }
        }

//        List<JSONObject> resultList  = (JSONObjectUtil.jsonObjectArrayToList(data.getJSONArray("nodes")))
//                .stream().filter(d->d.getString("label_name").equals(labelName)).collect(Collectors.toList());
        return resultList;
    }

    Map<String,JSONObject> listToMap(List<JSONObject> list) {
        Map<String,JSONObject> map = new HashMap<>();
        for (JSONObject o: list) {
            map.put(o.getString("gid"),o);
        }
        return map;
    }

    private List<JSONObject> getPaths(JSONObject data, String start, String end) {
        List<JSONObject> nodes = JSONObjectUtil.jsonObjectArrayToList(data.getJSONArray("nodes"));
        Map<String,JSONObject> idMapNode = listToMap(nodes);
        List<JSONObject> relations = JSONObjectUtil.jsonObjectArrayToList(data.getJSONArray("relations"));

        Map<String,List<JSONObject>> aMapRelations = relations.stream().collect(Collectors.groupingBy(d->d.getString("a_gid")));

        List<JSONObject> fromNodes = nodes.stream().filter(n->n.getString("label_name").equals(start)).collect(Collectors.toList());
        Set<String> fromNodeIds = fromNodes.stream().map(n->n.getString("gid")).collect(Collectors.toSet());
        List<JSONObject> nextRelations = relations.stream().filter(r->fromNodeIds.contains(r.getString("a_gid"))).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(nextRelations)) nextRelations = new ArrayList<>();
        List<JSONObject> paths = new ArrayList<>();
        for (JSONObject r:nextRelations) {
            JSONObject path = new JSONObject();
            path.put("source_id", r.getString("a_gid"));
            path.put("target_id", r.getString("b_gid"));
            path.put("walked", false);
            paths.add(path);
        }
        getPaths(paths, aMapRelations);

        List<JSONObject> finded = new ArrayList<>();
        for (JSONObject path: paths) {
            String targetId = path.getString("target_id");
            JSONObject endNode = idMapNode.get(targetId);
            if (ObjectUtil.isNotEmpty(endNode) && endNode.getString("label_name").equals(end)) {
                finded.add(path);
            }
        }
        return finded;
    }

    private void getPaths (List<JSONObject> paths, Map<String,List<JSONObject>> aMapRelations) {
        List<JSONObject> unWalkedPaths = paths.stream().filter(p->!p.getBoolean("walked")).collect(Collectors.toList());

        int nextNum=0;
        for (JSONObject p: unWalkedPaths) {
            p.put("walked",true);
            String fromNodeId = p.getString("target_id");
            // List<JSONObject> nextRelations = relations.stream().filter(r->fromNodeId.equals(r.getString("a_gid"))).collect(Collectors.toList());
            List<JSONObject> nextRelations = aMapRelations.get(fromNodeId);
            if (ObjectUtil.isEmpty(nextRelations)) continue;
            for (JSONObject r:nextRelations) {
                JSONObject path = new JSONObject();
                path.put("source_id", p.getString("source_id"));
                path.put("target_id", r.getString("b_gid"));
                path.put("walked", false);
                paths.add(path);
                nextNum++;
            }
        }
        if (nextNum>0) {
            getPaths(paths,aMapRelations);
        }
    }


    public void buildTree (JSONObject data, String parentLabel,String childLabel, String propertyName) {
        List<JSONObject> parentList = this.filterNodeByLabel(data,parentLabel);
        Map<String,JSONObject> idMapParent = listToMap(parentList);
        List<JSONObject> childList = this.filterNodeByLabel(data,childLabel);
        Map<String,JSONObject> idMapChild = listToMap(childList);
        List<JSONObject> parent2childList =JSONObjectUtil.jsonObjectArrayToList(this.getPaths(data, Arrays.asList(parentLabel,childLabel)).getJSONArray("paths"));
        for (JSONObject parent2child: parent2childList) {
            String sourceId = parent2child.getString("source_id");
            String targetId = parent2child.getString("target_id");
            JSONObject parent = idMapParent.get(sourceId);
            JSONObject child = idMapChild.get(targetId);
            JSONArray children = parent.getJSONArray(propertyName);
            if (ObjectUtil.isEmpty(children)) {
                children = new JSONArray();
                parent.put(propertyName, children);
            }
            children.add(child);

        }
    }

    public void buildReverseTree (JSONObject data, String parentLabel,String childLabel, String propertyName) {
        List<JSONObject> parentList = this.filterNodeByLabel(data,parentLabel);
        Map<String,JSONObject> idMapParent = listToMap(parentList);
        List<JSONObject> childList = this.filterNodeByLabel(data,childLabel);
        Map<String,JSONObject> idMapChild = listToMap(childList);
        List<JSONObject> parent2childList = JSONObjectUtil.jsonObjectArrayToList(this.getPaths(data, Arrays.asList(parentLabel,childLabel)).getJSONArray("paths"));
        for (JSONObject parent2child: parent2childList) {
            String sourceId = parent2child.getString("source_id");
            String targetId = parent2child.getString("target_id");
            JSONObject parent = idMapParent.get(sourceId);
            JSONObject child = idMapChild.get(targetId);
            JSONArray parents = child.getJSONArray(propertyName);
            if (ObjectUtil.isEmpty(parents)) {
                parents = new JSONArray();
                child.put(propertyName, parents);
            }
            parents.add(parent);

        }
    }

    public void mapProperty (JSONObject data, String label, String property, Function<JSONObject,JSONObject> mapFun) {
        List<JSONObject> nodeList = filterNodeByLabel(data,label);

        for (JSONObject d: nodeList) {

            if(d.get(property) instanceof List) {
                List<JSONObject> valueList = JSONObjectUtil.jsonObjectArrayToList(d.getJSONArray(property));
                if(ObjectUtil.isEmpty(valueList)) {
                    valueList = new ArrayList<>();
                }
                List<JSONObject> newValueList = valueList.stream().map(mapFun).collect(Collectors.toList());
                d.put(property,newValueList);
            }else if (d.get(property) instanceof Map){
                d.put(property,mapFun.apply(d.getJSONObject(property)));
            }
        }
    }

    public String findStart(Set<Edge> edges){
        Map<String, Integer> inDegree = new HashMap<>();

        // 初始化入度计数
        for (Edge edge : edges) {
            inDegree.put(edge.getSource(), inDegree.getOrDefault(edge.getSource(), 0));
            inDegree.put(edge.getTarget(), inDegree.getOrDefault(edge.getTarget(), 0) + 1);
        }

        // 找出所有入度为0的节点
        String startSource = "" ;
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {

                startSource = entry.getKey();
            }
        }

        return startSource;


    }






}
