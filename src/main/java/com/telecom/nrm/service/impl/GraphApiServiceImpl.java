package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.DaClient;
import com.telecom.da.client.DaRequest;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.GraphScene;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.api.GraphFilter;
import com.telecom.nrm.domain.graph.api.GraphParam;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphSceneService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.print.attribute.HashAttributeSet;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Slf4j
public class GraphApiServiceImpl implements GraphApiService {

    @Autowired
    GraphService graphService;

    @Autowired
    GraphSceneService graphSceneService;

    @Autowired
    DaClient client;

    @Override
    public Graph doApi(GraphRequest request) {
        GraphParam param = request.getBusinessParam();
        Map<String,GraphFilter> filters = request.getFilters();
        // String cjType=param.getCjType();

        Graph graph = new Graph();

        if (ObjectUtil.isNotNull(param.getApiId())) {
            GraphScene scene = graphSceneService.getByCode(param.getApiId());
            graph = scene.getGraph();
            if (!graphService.checkStartNode(graph,scene.getStartNode())) {
                throw new RuntimeException("起点只能有1个，必须是"+scene.getStartNode());
            }
        } else if (ObjectUtil.isNotEmpty(param.getPaths())) {
            graph = graphService.parsePaths(param.getPaths()) ;
        }





        if (!graphService.checkNoCircle(graph)) {
            throw new RuntimeException("不可成环");
        }



        JSONObject whereParam = request.getWhereParam();
        if (!whereParam.containsKey("shardingCode")) {
            whereParam.put("shardingCode", request.getBusinessParam().getShardingCode());
        }
        List<Node> nodes = graphService.walkNodes(graph);
        for (Node n: nodes) {
            walkNode(graph,n,whereParam,filters);
            walkOutEdges(graph,n, whereParam);
        }
        convertNumericToString(graph);
        return graph;
    }


    void convertNumericToString (Graph graph ) {
        Set<Node> nodes= graph.getNodes();
        for (Node n: nodes) {
            List<JSONObject> dataList= n.getData();
            for (JSONObject d: dataList) {
                convertNumericToString(d);
            }
        }
        Set<Edge> edges= graph.getEdges();
        for (Edge e: edges) {
            List<JSONObject> dataList= e.getData();
            for (JSONObject d: dataList) {
                convertNumericToString(d);
            }
        }
    }

    static void convertNumericToString(JSONObject o) {
        Set<String> keys = o.keySet();

        for (String key: keys) {
            Object value = o.get(key);
            if (value instanceof java.math.BigDecimal || value instanceof  java.math.BigInteger) {
                String valueString = value.toString();
                o.put(key, valueString);
            }
        }

    }

    boolean isObjectEmpty(JSONObject o) {
        boolean empty = true;
        if (ObjectUtil.isEmpty(o)) {
            return true;
        }
        Set<String> keys = o.keySet();
        for (String key: keys) {
            Object value = o.get(key);
            if (ObjectUtil.isNotEmpty(value)) {
                empty = false;
                break;
            }
        }
        return empty;
    }

    public void walkNode (Graph g, Node n, JSONObject whereParam, Map<String,GraphFilter> filters) {
        Set<Edge> inEdges = g.getInEdges(n.getId());
        List<String> ids = new ArrayList();
        int i=0;
        for (Edge e: inEdges) {
            List<JSONObject> edgeData = e.getData();
            if (ObjectUtil.isEmpty(edgeData)) edgeData=new ArrayList<>();
            String field = e.isReverse()?"a_id":"b_id";
            List<String> outputIds = edgeData.stream().map(d->d.getString(field)).collect(Collectors.toList());
            if (i==0) {
                ids.addAll(outputIds);
            }else if (n.getMethod().equals("intersection")) {
                // 取交集
                ids = ids.stream().filter(id -> outputIds.stream().anyMatch(id::equals)).collect(Collectors.toList());
                ids = ids;
            }else if (n.getMethod().equals("union")) {
                // 取并集
                ids = Stream.concat(ids.stream(), outputIds.stream())
                        .distinct()
                        .collect(Collectors.toList());
            }
            i++;

        }

        JSONObject nodeParam = whereParam.getJSONObject(n.getId());

        /*
        if (n.getApiCode().equals("nrm.query_node_device")) {
            // pageSize = 10;
            log.info("walk node param {} ",nodeParam);
        }

         */

        if (isObjectEmpty(nodeParam)) {
            nodeParam = new JSONObject();
            nodeParam.put("is_empty", true);
        }
        if (!nodeParam.containsKey("shardingCode")) {
            nodeParam.put("shardingCode", whereParam.getString("shardingCode"));
        }
        pageParam(nodeParam);
        if (ObjectUtil.isNotEmpty(ids)){
            // 如果传入了id，则将id作为参数
            nodeParam.put("ids", ids);
            nodeParam.put("is_empty", false);
            fetchData(n, nodeParam,g,filters);
        }else if (g.findFirstNodes().stream().map(Node::getId).collect(Collectors.toList()).contains(n.getId())) {

            // 如果是起点,则需要查询
            if (isObjectEmpty(nodeParam)) {
                n.setData(new ArrayList<>());
            }else {
                fetchData(n, nodeParam,g,filters);
            }


        }else{
            // 针对不是起点,且没有ID,则不需要查询
            n.setData(new ArrayList<>());

        }

        Set<Object> dataIds = new HashSet<>();
        if (ObjectUtil.isNotEmpty(n.getData())) {
            n.getData().forEach(d -> {
                dataIds.add(d.get("id"));
            });
        }

        // 删除入边中目标节点不存在的数据
        for (Edge e: inEdges) {
            List<JSONObject> edgeData = e.getData();
            if (ObjectUtil.isEmpty(edgeData)) edgeData=new ArrayList<>();
            String field = e.isReverse()?"a_id":"b_id";
            List<JSONObject> newData= edgeData.stream().filter(d->{
                return dataIds.contains(d.get(field));
            }).collect(Collectors.toList());
            e.setData(newData);

        }
    }

    public void walkOutEdges (Graph g, Node n,  JSONObject whereParam) {
        Set<Edge> outEdges = g.getOutEdges(n.getId());
        for (Edge e: outEdges) {
            JSONObject edgeParam = new JSONObject();
            String key = e.isReverse()?"b":"a";
            List<JSONObject> nodeData = n.getData();
            nodeData = nodeData.stream().map(d->{JSONObject o = new JSONObject();o.put("id", d.get("id"));o.put("spec_id",d.get("spec_id"));return o;}).collect(Collectors.toList());
            edgeParam.put(key, nodeData);
            edgeParam.put("shardingCode", whereParam.getString("shardingCode"));
            pageParam(edgeParam);

            if (ObjectUtil.isEmpty(edgeParam.get("a")) && ObjectUtil.isEmpty(edgeParam.get("b"))) {
                e.setData(new ArrayList<>());
                return;
            } else if (ObjectUtil.isEmpty(edgeParam.get("a"))) {
                edgeParam.remove("a");
            } else if (ObjectUtil.isEmpty(edgeParam.get("b"))) {
                edgeParam.remove("b");
            }
            fetchData(e,edgeParam,g);

        }
    }




    public void pageParam(JSONObject param) {
        param.put("pageSize", 50000);
        param.put("currentPage",1);
    }

    public void fetchData(Node n, JSONObject param,Graph g,Map<String,GraphFilter> filters) {

        String shardingCode = n.getDataSourceType().equals("city")?param.getString("shardingCode"):n.getDataSourceType();

        /*
        @SuppressWarnings("all")
        DaRequest<List<JSONObject>> request =client.buildRequest().returnType(List.class,JSONObject.class).param(param).
                apiCode(n.getApiCode()).
                shardingCode(shardingCode).
                version(n.getApiVersion()).build();

         */
        log.info("do api {} ", n.getApiCode());

        List<JSONObject> data = new ArrayList<>();


        if (!param.keySet().contains("ids")) {
            @SuppressWarnings("all")
            DaRequest<List<JSONObject>> request =client.buildRequest().returnType(List.class,JSONObject.class).param(param).
                    apiCode(n.getApiCode()).
                    shardingCode(shardingCode).
                    version(n.getApiVersion()).build();
            List<JSONObject> result = request.doApi();
            if (ObjectUtil.isNotEmpty(result)) {
                data .addAll(result);
            }

        }else{
            JSONArray idList = param.getJSONArray("ids");

            int paramTotal = idList.size();
            int pageSize = 1000;



            int pageNum  = (paramTotal/pageSize)+1;
            log.info("do api {}, 参数个数 {}", n.getApiCode(), paramTotal);

            for (int pageIndex=0;pageIndex < pageNum;pageIndex++) {
                int start = pageIndex*pageSize;
                int end = Math.min(start + pageSize, paramTotal);
                if (end<=start) break;
                List idListNew = idList.subList(start, end);
                param.put("ids",idListNew);
                log.info("do api {}, 从 {} 到 {} 个数 {}", n.getApiCode(), start,end,idListNew.size());

                /*
                if (n.getApiCode().equals("nrm.query_node_ether_link")) {
                    // pageSize = 10;
                    log.info("param {} ",param.toJSONString());
                }

                 */
                //
                if (n.getApiCode().equals("nrm.query_node_device")) {
                    // pageSize = 10;
                    log.info("param {} ",param.toJSONString());
                }
                @SuppressWarnings("all")

                DaRequest<List<JSONObject>> request = client.buildRequest().returnType(List.class,JSONObject.class).param(param).
                        apiCode(n.getApiCode()).
                        shardingCode(shardingCode).
                        version(n.getApiVersion()).build();
                List<JSONObject> queryResult = request.doApi();
                if (!ObjectUtil.isEmpty(queryResult)) {
                    data.addAll(queryResult);
                }
            }
        }


        if (ObjectUtil.isEmpty(data)) data=new ArrayList<>();
        log.info("{}返回{}条记录",n.getApiCode(),data.size());

        // todo 增加对data的去重复的操作,以减少一些数据异常

        if (ObjectUtil.isNotEmpty(data)) {
            String labelCode = n.getCode();
            GraphFilter filter=filters.get(labelCode);
            if (ObjectUtil.isEmpty(filter)) {
                n.setData(data);
            }else{
                n.setData(data);
                data = filter.filter(data,g);
                n.setData(data);
            }
        }else {
            n.setData(new ArrayList<>());
        }

    }

    public void fetchData(Edge e, JSONObject p,Graph g) {
        JSONObject param = new JSONObject();
        param.putAll(p);
        String shardingCode = e.getDataSourceType().equals("city")?param.getString("shardingCode"):e.getDataSourceType();


        log.info("do api {}", e.getApiCode());

        if (e.getApiCode().equals("nrm.query_edge_link_end_with_device")) {
            log.info("param {} ",param.toJSONString());
        }

        JSONArray paramList = new JSONArray();
        if (ObjectUtil.isNotEmpty(param.getJSONArray("a"))) {
            paramList = param.getJSONArray("a");
            param.put("direction","a");
        }
        if (ObjectUtil.isNotEmpty(param.getJSONArray("b"))) {
            paramList = param.getJSONArray("b");
            param.put("direction","b");
        }

        List<JSONObject> data = new ArrayList<>();
        int paramTotal = paramList.size();
        log.info("do api {}, 参数个数 {}", e.getApiCode(), paramTotal);
        int pageSize = 500;
        int pageNum  = (paramTotal/pageSize)+1;
        for (int pageIndex=0;pageIndex < pageNum;pageIndex++) {
            int start = pageIndex*pageSize;
            int end = Math.min(start + pageSize, paramTotal);
            if (end<=start) break;
            param.put(param.getString("direction"),paramList.subList(start, end));
            log.info("do api {}, 从 {} 到 {}", e.getApiCode(), start,end);
            @SuppressWarnings("all")
            DaRequest<List<JSONObject>> request = client.buildRequest().returnType(List.class,JSONObject.class).param(param).
                    apiCode(e.getApiCode()).
                    shardingCode(shardingCode).
                    version(e.getApiVersion()).build();
            List<JSONObject> queryResult = request.doApi();
            if (!ObjectUtil.isEmpty(queryResult)) {
                data.addAll(queryResult);
            }
        }

        // log.info("request param : {}", param);



        if (ObjectUtil.isEmpty(data)) data=new ArrayList<>();
        log.info("{} 返回{}条记录",e.getApiCode(),data.size());

        if (ObjectUtil.isNotEmpty(data)) {
            e.setData(data);
        }else {
            e.setData(new ArrayList<>());
        }
    }
}
