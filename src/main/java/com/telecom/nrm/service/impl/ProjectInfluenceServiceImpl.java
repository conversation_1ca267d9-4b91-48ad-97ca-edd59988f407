package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.controller.InfluenceAnalysisController;
import com.telecom.nrm.controller.OptGroupController;
import com.telecom.nrm.dao.CutoverDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.ProjectInfluenceService;
import com.telecom.nrm.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProjectInfluenceServiceImpl implements ProjectInfluenceService {
    @Autowired
    CutoverDao cutoverDao;

    @Autowired
    OptGroupController optGroupController;

    @Autowired
    GraphService graphService;

    @Autowired
    InfluenceAnalysisController influenceAnalysisController;

    @Autowired
    GraphApiService graphApiService;

    static String ACTION_INFLUENCE = "1";
    static String ACTION_PRE_PON_CHECK = "2";
    static String ACTION_PROCESS_PON_CHECK = "3";
    static String ACTION_POST_PON_CHECK = "4";
    static Integer ROW_DATA_VALID = 1;


    public List<JSONObject> resourceInfluenceRoute (JSONObject res, String city) {
        String entitySpecId = res.getString("entity_spec_id");
        String cableCode = res.getString("entity_code");
        JSONObject param = new JSONObject();
        String datasource = "ds_odso_"+city;

        if (ObjectUtil.isNotEmpty(entitySpecId) && entitySpecId.equals("1121000002"))  {
            // 光缆段
            //
            param.put("code", cableCode);
            PageResponse<JSONObject> pageResponse = cutoverDao.getInfluenceRouteByCableCode(param, 100000, 1, datasource);
            // int totalPage = pageResponse.getPageInfo().getTotalPage();
            List<JSONObject> result = pageResponse.getData();
            return result;
        } else if (ObjectUtil.isNotEmpty(entitySpecId) && entitySpecId.equals("1211200002"))  {

            // 光缆影响分析
            param.put("code", cableCode);
            PageResponse<JSONObject> pageResponse = cutoverDao.getInfluenceRouteByCableNetCode(param, 100000, 1, datasource);
            List<JSONObject> result = pageResponse.getData();
            return result;
        } else if (ObjectUtil.isNotEmpty(entitySpecId) && entitySpecId.equals("1020200006"))  {

            // OBD影响分析
            param.put("code", cableCode);
            PageResponse<JSONObject> pageResponse = cutoverDao.getInfluenceRouteByOBD(param, 100000, 1, datasource);
            List<JSONObject> result = pageResponse.getData();
            return result;
        } else if (ObjectUtil.isNotEmpty(entitySpecId) && entitySpecId.equals("1028200001")) {
            //OLT
            param.put("code", cableCode);
            PageResponse<JSONObject> pageResponse = cutoverDao.getInfluenceRouteByOBD(param, 100000, 1, datasource);
            List<JSONObject> result = pageResponse.getData();
            return result;
        }
        return new ArrayList<>();
    }

    /**
     * 分析光路影响范围
     * @param request
     * @param batchNo
     * @return
     */
    public List<JSONObject> influenceRoute(@RequestBody JSONObject request, String batchNo) {
        List<JSONObject> routeResp = new ArrayList<JSONObject>();
        String city = request.getString("city");
        String project_id = request.getString("project_id");


        List<JSONObject> resourceList = cutoverDao.listProjectRes(request, NRMConstants.SHARDING_GRAPH_DB);

        if (ObjectUtil.isEmpty(city) || ObjectUtil.isEmpty(resourceList)) {
            return routeResp;
        }

        for (int i = 0; i < resourceList.size(); i++) {
            JSONObject jsonObject = resourceList.get(i);
            List<JSONObject> result = resourceInfluenceRoute(jsonObject, city);
            routeResp.addAll(result);
        }
        for (JSONObject jsonObject : routeResp) {
            jsonObject.put("project_id", project_id);
            jsonObject.put("batch_no", batchNo);
            jsonObject.put("row_status" ,1);
        }
        switchRouteAZ(routeResp);
        cutoverDao.deletePrProjectRoute(project_id, NRMConstants.SHARDING_GRAPH_DB);
        PageUtils.doSomethingByPage(v->{cutoverDao.insertPrProjectRoute(v, NRMConstants.SHARDING_GRAPH_DB);}, routeResp,1000); // 向数据库提交数据需要分页
        return routeResp;
    }

    void switchRouteAZ(List<JSONObject> routeList) {
        for (JSONObject jsonObject : routeList) {
            String jx_a_device_code = jsonObject.getString("jx_a_device_code");
            String jx_a_device_name = jsonObject.getString("jx_a_device_name");
            String jx_a_port_code = jsonObject.getString("jx_a_port_code");
            String jx_z_device_code = jsonObject.getString("jx_z_device_code");
            String jx_z_device_name = jsonObject.getString("jx_z_device_name");
            String jx_z_port_code = jsonObject.getString("jx_z_port_code");

            if (ObjectUtil.isNotEmpty(jx_a_device_code) && jx_a_device_code.compareTo(jx_z_device_code) >0 ) {
                jsonObject.put("jx_a_device_code", jx_z_device_code);
                jsonObject.put("jx_a_device_name", jx_z_device_name);
                jsonObject.put("jx_a_port_code", jx_z_port_code);
                jsonObject.put("jx_z_device_code", jx_a_device_code);
                jsonObject.put("jx_z_device_name", jx_a_device_name);
                jsonObject.put("jx_z_port_code", jx_a_port_code);
            }

            String a_device_code = jsonObject.getString("a_device_code");
            String a_device_name = jsonObject.getString("a_device_name");
            String a_device_ip = jsonObject.getString("a_device_ip");
            String a_port_code = jsonObject.getString("a_port_code");
            String a_device_type = jsonObject.getString("a_device_type");
            String a_port_type = jsonObject.getString("a_port_type");

            String z_device_code = jsonObject.getString("z_device_code");
            String z_device_name = jsonObject.getString("z_device_name");
            String z_device_ip = jsonObject.getString("z_device_ip");
            String z_port_code = jsonObject.getString("z_port_code");
            String z_device_type = jsonObject.getString("z_device_type");
            String z_port_type = jsonObject.getString("z_port_type");

            if (ObjectUtil.isNotEmpty(a_device_code) && ObjectUtil.isNotEmpty(z_device_code) && a_device_code.compareTo(z_device_code) >0 ) {

                jsonObject.put("a_device_code", z_device_code);
                jsonObject.put("a_device_name", z_device_name);
                jsonObject.put("a_device_ip", z_device_ip);
                jsonObject.put("a_port_code", z_port_code);
                jsonObject.put("a_device_type", z_device_type);
                jsonObject.put("a_port_type", z_port_type);

                jsonObject.put("z_device_code", a_device_code);
                jsonObject.put("z_device_name", a_device_name);
                jsonObject.put("z_device_ip", a_device_ip);
                jsonObject.put("z_port_code", a_port_code);
                jsonObject.put("z_device_type", a_device_type);
                jsonObject.put("z_port_type", a_port_type);
            }

        }
    }


    /**
     * 通过一树一图查找光路和CFS的关系，并且写入到 pm_opt_road_carry_cfs 中间表
     * @param request
     */
    public void influenceRouteCarryCFS (JSONObject request) {


        PageResponse<JSONObject> routePage = cutoverDao.queryPrProjectRoute(request, 100000, 1, NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> routeList = routePage.getData();
        if (ObjectUtil.isEmpty(routeList)) return;
        List<String> routeCodes = routeList.stream().map(r->r.getString("route_code")).distinct().collect(Collectors.toList());
        String city = request.getString("city");

        influenceRouteCarryCFS(routeCodes,city);
    }

    public void influenceRouteCarryCFS (List<String> routeCodes, String city) {
        // log.info("routeCodes: {}", routeCodes);
        String shardingCode = "ds_bc_o3_"+ city.toLowerCase();
        String odsoShardingCode = "ds_odso_"+ city.toLowerCase();

        List<JSONObject> carryList = new ArrayList<>();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("opt_road_effect").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("opt_road","codes",routeCodes)).build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject graphData = graphService.getData(graph);
        graphService.buildReverseTree(graphData, "光路", "cfs", "opt_road_list");
        List<JSONObject> cfsList = graphService.filterNodeByLabel(graphData, "cfs");

        for (JSONObject cfs: cfsList) {
            if (ObjectUtil.isEmpty(cfs.getJSONArray("opt_road_list"))) continue;
            List<JSONObject> roadList = cfs.getJSONArray("opt_road_list").toJavaList(JSONObject.class);

            for (JSONObject road: roadList) {
                JSONObject carry = new JSONObject();
                carry.put("opt_road_code", road.getString("code"));
                carry.put("crm_product_id", cfs.getString("crm_product_id"));
                carry.put("access_code", cfs.getString("access_code"));
                carry.put("circuit_code", cfs.getString("link_code"));
                carryList.add(carry);
            }
        }


        cutoverDao.deletePmOptRoadCarryCFS(routeCodes,  odsoShardingCode);


        PageUtils.doSomethingByPage((v->{cutoverDao.insertPmOptRoadCarryCFS(v,  odsoShardingCode);}), carryList,1000);
    }

    /**
     *
     * 分析业务影响范围
     * @param request
     * @return
     *
     *
     *
     */
    public JSONObject influenceBusiness(@RequestBody JSONObject request, String batchNo) {
        JSONObject response = new JSONObject();
        if (ObjectUtils.isEmpty(request)) {
            response.put("result", "success");
            return response;
        }
        String projectId = request.getString("project_id");
        if (ObjectUtils.isEmpty(projectId)) {
            response.put("result", "fail");
            return response;
        }
        String city = request.getString("city");



        cutoverDao.updatePrProjectServiceRowStatusInvalid(projectId, ACTION_INFLUENCE, NRMConstants.SHARDING_GRAPH_DB); // 将以前分析的结果更新为0 ”无效“
        try{
            List<JSONObject> cutoverResources = cutoverDao.listProjectRes(request, NRMConstants.SHARDING_GRAPH_DB);
            Map<String,JSONObject> segmentCoreMapRoute = this.getCableSegmentCoreMapRoute(request);
            Map<String,JSONObject> netCoreMapRoute = this.getCableNetCoreMapRoute(request);
            if (ObjectUtil.isNotEmpty(cutoverResources)) {
                for (int i = 0; i < cutoverResources.size(); i++) {
                    JSONObject res = cutoverResources.get(i);
                    String entityName = res.getString("entity_name");
                    String entitySpecCode = res.getString("entity_spec_code");
                    String entityCode = res.getString("entity_code");
                    String entityID = res.getString("entity_id");
                    String dev_ip = res.getString("dev_ip");
                    JSONObject influenceRequest = new JSONObject();
                    influenceRequest.put("devType", entitySpecCode);
                    influenceRequest.put("devCode", entityCode);
                    influenceRequest.put("city", city);
                    if (!ObjectUtils.isEmpty(dev_ip)) {
                        influenceRequest.put("ip_addr", dev_ip);
                    }
                    JSONObject body = influenceAnalysisController.getResourceinFluence(influenceRequest);
                    if (!ObjectUtil.isEmpty(body)) {
                        JSONArray cfsInfluenceList = body.getJSONArray("cfs_influence");
                        for (int j = 0; j < cfsInfluenceList.size(); j++) {
                            JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(j);

                            if (ObjectUtil.isNotEmpty(dev_ip)) {
                                cfsInfluence.put("dev_ip", dev_ip);
                            }

                            /*
                            String serviceType = cfsInfluence.getString("customer_level_name");
                            if (ObjectUtil.isEmpty(serviceType)) {
                                cfsInfluence.put("customer_type", "公众");
                            } else {
                                cfsInfluence.put("customer_type", "政企");
                            }
                             */

                            String custTpId = cfsInfluence.getString("cust_tp_id");
                            String custOrgTpId = cfsInfluence.getString("cust_org_tp_id");
                            if (ObjectUtil.isNotEmpty(custOrgTpId) && custOrgTpId.equals("2")) {
                                cfsInfluence.put("customer_type", "政企");
                            }else if (ObjectUtil.isNotEmpty(custTpId) && (custTpId.equals("7")|| custTpId.equals("6") || custTpId.equals("9"))) {
                                cfsInfluence.put("customer_type", "公众");
                            } else {
                                cfsInfluence.put("customer_type", "政企");
                            }

                            cfsInfluence.put("entity_name", entityName);
                            cfsInfluence.put("entity_code", entityCode);
                            cfsInfluence.put("entity_id", entityID);
                            // cfsInfluence.
                            if (ObjectUtils.isEmpty(cfsInfluence.getString("scene_name"))) {
                                cfsInfluence.put("is_lifecycle", "否");
                            } else {
                                cfsInfluence.put("is_lifecycle", "是");
                            }
                            cfsInfluence.put("batch_no", batchNo);
                            cfsInfluence.put("project_id", projectId);
                            //actin为1表示插入影响面结果
                            cfsInfluence.put("action", ACTION_INFLUENCE);
                            cfsInfluence.put("row_status", ROW_DATA_VALID); // 插入数据为有效

                            JSONObject optRoute = null;
                            if (entitySpecCode.equals("光缆段") || entitySpecCode.equals("cbl_sect")) {
                                optRoute = segmentCoreMapRoute.get(entityCode+"_"+cfsInfluence.getString("line_no"));
                            } else if (entitySpecCode.equals("光缆") || entitySpecCode.equals("net_code")) {
                                optRoute = netCoreMapRoute.get(entityCode+"_"+cfsInfluence.getString("line_no"));
                            }
                            if (ObjectUtil.isNotEmpty(optRoute)) {
                                cfsInfluence.put("a_cable_device_name", optRoute.getString("jx_a_device_name"));
                                cfsInfluence.put("a_cable_port_code", optRoute.getString("jx_a_port_code"));
                                cfsInfluence.put("z_cable_device_name", optRoute.getString("jx_z_device_name"));
                                cfsInfluence.put("z_cable_port_code", optRoute.getString("jx_z_port_code"));

                                cfsInfluence.put("a_road_device_name", optRoute.getString("a_device_name"));
                                cfsInfluence.put("a_road_port_code", optRoute.getString("a_port_code"));
                                cfsInfluence.put("z_road_device_name", optRoute.getString("z_device_name"));
                                cfsInfluence.put("z_road_port_code", optRoute.getString("z_port_code"));
                            }
                        }

                        if (ObjectUtil.isNotEmpty(cfsInfluenceList)) {
                            PageUtils.doSomethingByPage(part->{
                                cutoverDao.insertPrProjectService(part, NRMConstants.SHARDING_GRAPH_DB);
                            }, cfsInfluenceList.toJavaList(JSONObject.class), 1000);
                        }

                    }
                }
            }

        }catch (Exception ex){
            throw ex;
        }


        // 运行状态设置为1

        response.put("result", "success");
        return response;
        /*
        PageResponse<JSONObject> influenceScopeByProjectId = cutoverDao.getInfluenceScopeByProjectId(batchNo, 10000, 1, NRMConstants.SHARDING_GRAPH_DB);
        return influenceScopeByProjectId.getData();

         */
    }

    Map<String,JSONObject> getCableSegmentCoreMapRoute (JSONObject request) {
        PageResponse<JSONObject> route = cutoverDao.queryPrProjectRoute(request, 100000, 1, NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> list = route.getData();
        Map<String,JSONObject> result = new HashMap<>();
        for (JSONObject o: list) {
            String cableCode = o.getString("cable_code");
            String lineCode = o.getString("line_code");
            if (ObjectUtil.isNotEmpty(cableCode) && ObjectUtil.isNotEmpty(lineCode)) {
                String key = cableCode + "_" + lineCode;
                result.put(key, o);
            }
        }
        return result;
    }

    Map<String,JSONObject> getCableNetCoreMapRoute (JSONObject request) {
        PageResponse<JSONObject> route = cutoverDao.queryPrProjectRoute(request, 100000, 1, NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> list = route.getData();
        Map<String,JSONObject> result = new HashMap<>();
        for (JSONObject o: list) {
            String cableCode = o.getString("net_code");
            String lineCode = o.getString("jx_fiber_code");
            if (ObjectUtil.isNotEmpty(cableCode) && ObjectUtil.isNotEmpty(lineCode)) {
                String key = cableCode + "_" + lineCode;
                result.put(key, o);
            }
        }
        return result;
    }



    /**
     * 影响光路保护组查询
     * @param request
     * @param batchNo
     * @return
     */
    public List<JSONObject> influenceOptGroup( JSONObject request, String batchNo) {
        // todo
        String project_id = request.getString("project_id");
        PageResponse<JSONObject> cutoverOptRoutePage = cutoverDao.queryPrProjectRoute(request, 100000, 1, NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> cutoverOptRouteList = cutoverOptRoutePage.getData();
        Set<String> cutOverRoadCodes = cutoverOptRouteList.stream().map(d->d.getString("route_code")).collect(Collectors.toSet());
        log.info("cutOverRoadCodes: {}", cutOverRoadCodes);
        List<JSONObject> optGroupList = new ArrayList<>();
        String city = request.getString("city");
        if (ObjectUtil.isNotEmpty(cutOverRoadCodes)) {
            JSONObject queryParam = new JSONObject();
            queryParam.put("opt_code_s", cutOverRoadCodes);
            queryParam.put("ds", city);
            BiyiPageRequest pageRequest = new BiyiPageRequest();
            pageRequest.setPage(1);
            pageRequest.setSize(100000);
            BiyiPageResult<JSONObject> optGroupPage = optGroupController.pageQuery(queryParam, pageRequest);
            optGroupList = optGroupPage.getData();
        }
        List<JSONObject> projectOptGroupList =new ArrayList<>();
        for (JSONObject group : optGroupList) {
            JSONObject projectOptGroup = new JSONObject();
            projectOptGroup.put("project_id", project_id);
            projectOptGroup.put("batch_no", batchNo);
            projectOptGroup.put("action" ,"1");
            projectOptGroup.put("opt_group_id", group.getString("id"));
            projectOptGroup.put("opt_group_code", group.getString("code"));
            projectOptGroup.put("opt_group_name", group.getString("name"));
            projectOptGroup.put("check_result", group.getString("check_result"));
            List<JSONObject> members = group.getJSONArray("members").toJavaList(JSONObject.class);
            List<String> optCodeList = members.stream().map(d->d.getString("code")).collect(Collectors.toList());
            List<String> effectOptCodeList = optCodeList.stream().filter(cutOverRoadCodes::contains).collect(Collectors.toList());

            String optCodes = String.join(",", optCodeList);
            String effectOptCodes = String.join(",", effectOptCodeList);
            projectOptGroup.put("opt_codes", optCodes);
            projectOptGroup.put("effect_opt_codes", effectOptCodes);
            int optCodeSize = optCodeList.size();
            projectOptGroup.put("opt_code_num", optCodeList.size());
            int effectOptCodeSize = effectOptCodeList.size();
            projectOptGroup.put("effect_opt_code_num", effectOptCodeSize);
            projectOptGroup.put("risk_check_result", group.getString("checkResult"));
            String cutoverCheckResult = "";
            if (effectOptCodeSize == 0) {
                cutoverCheckResult = "无阻断";
            }else if (effectOptCodeSize < optCodeSize) {
                cutoverCheckResult = "半阻";
            }else {
                cutoverCheckResult = "全阻";
            }
            projectOptGroup.put("cutover_check_result", cutoverCheckResult);
            projectOptGroup.put("row_status", 1);
            projectOptGroupList.add(projectOptGroup);
        }
        cutoverDao.deletePrProjectOptGroup(project_id, NRMConstants.SHARDING_GRAPH_DB);
        if (ObjectUtil.isNotEmpty(projectOptGroupList)) {
            cutoverDao.insertPrProjectOptGroup(projectOptGroupList, NRMConstants.SHARDING_GRAPH_DB);
        }


        return projectOptGroupList;
    }
}
