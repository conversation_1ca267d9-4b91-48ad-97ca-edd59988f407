package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
// import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.domain.topo.TopoMode;
import com.telecom.nrm.service.CommonCircuitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CommonCircuitServiceImpl implements CommonCircuitService {


    public JSONObject sortCircuitTopology(List<JSONObject> devices, List<JSONObject> links , TopoMode mode,String startId) {

        List<JSONObject> rootLinks = links;

        Set<String> parentIds = new HashSet<>();

        checkCycle(parentIds,rootLinks);


        sortRootLinks(rootLinks, startId);


        List<JSONObject> sortedDevices = sortDevices(devices ,rootLinks, mode);



        JSONObject result = new JSONObject();
        result.put("nodes", sortedDevices);
        result.put("links", rootLinks);

        return result;
    }

    public JSONObject sortCircuitTopology(List<JSONObject> devices, List<JSONObject> links ,List<JSONObject> linkRelations, TopoMode mode, String startId) {

        /*
        links = links.stream().filter(d->
        {
            if (ObjectUtil.isEmpty(d.getString("a_physic_device_id")) || ObjectUtil.isEmpty(d.getString("z_physic_device_id"))) {
                return false; // 过滤掉AZ端设备为空的链路
            } else if (d.getString("a_physic_device_id").equals(d.getString("z_physic_device_id"))) {
                return false; // 过滤掉AZ端相等的链路
            } else {
                return true;
            }
        }).collect(Collectors.toList());

         */
        links.removeIf(d->ObjectUtil.isNotEmpty(d.getString("a_physic_device_id")) && ObjectUtil.isNotEmpty(d.getString("z_physic_device_id")) && d.getString("a_physic_device_id").equals(d.getString("z_physic_device_id")));


        linkRelations.removeIf(r->ObjectUtil.isEmpty(r.getString("lower_link_id")));
        linkRelations.removeIf(r->ObjectUtil.isEmpty(r.getString("upper_link_id")));

        updateParentId( links, linkRelations);

        // links.removeIf(d-> ObjectUtil.isEmpty(d.getString("a_physic_device_id")) || ObjectUtil.isEmpty(d.getString("z_physic_device_id")));

        links.stream().filter(d->ObjectUtil.isEmpty(d.getString("a_physic_device_id"))).forEach(d->{
            d.put("a_physic_device_id","0");
        });

        links.stream().filter(d->ObjectUtil.isEmpty(d.getString("z_physic_device_id"))).forEach(d->{
            d.put("z_physic_device_id","9999");
        });

        links.removeIf(d->d.getString("a_physic_device_id").equals(d.getString("z_physic_device_id")));
        List<JSONObject> rootLinks = links.stream().filter(d->ObjectUtil.isEmpty(d.getString("parent_id"))).collect(Collectors.toList());
        rootLinks.forEach(l->{updateChildrenNum(l);l.put("layer",0);});

        return sortCircuitTopology(devices,rootLinks,mode, startId);

    }

    // 对顶层链路进行排序对齐
    void sortRootLinks(List<JSONObject> links, String startId) {
        if (ObjectUtil.isEmpty(links)) return;
        if (links.size()<=1) return;
        computeRootLinkOrder(links,startId,"", new ArrayList<>(), TopoMode.NET,0);
        /*
        JSONObject firstLink = links.get(0);
        int size = links.size();
        for (int i=1;i< size;i++) {
            JSONObject l = links.get(i);

            if (l.getString("a_physic_device_id").equals(firstLink.getString("z_physic_device_id"))) {
                swapAZ(l);

            }else if  (l.getString("z_physic_device_id").equals(firstLink.getString("a_physic_device_id"))) {
                swapAZ(l);

            }

        }

         */

    }

    /*
    void removeCycleParents(List<JSONObject> links) {


        for (JSONObject l: links) {
            Integer layer = l.getInteger("layer");
            List<JSONObject> children = l.getJSONArray("children");
            if (ObjectUtil.isNotEmpty(children)) {
                boolean removed = children.removeIf(c->{
                    // c= idMapLink.get(c.getString("id"));
                    Integer childLayer = c.getInteger("layer");
                    return childLayer<=layer; // 子节点的层次一定要大于父节点
                });
                if (removed) {
                    log.info("removed:"+l.getString("id"));
                }
            }

            l.put("children", children);

        }

    }
    */


    void checkCycle (Set<String> parentIds,List<JSONObject> links) {
        for (JSONObject l: links) {
            parentIds.add(l.getString("id"));
            log.info("checkCycle:"+l.getString("id"));
            JSONArray children = (l.getJSONArray("children"));

            if (ObjectUtil.isNotEmpty(children)) {
                // log.info("checkCycle length:"+children.size());
                Map<String,JSONObject> childrenMap = new HashMap<>();
                for (int i = 0; i < children.size(); i++) {
                    JSONObject child = children.getJSONObject(i);
                    childrenMap.put(child.getString("id"),child);

                }


                for (String parentId: parentIds) {
                    childrenMap.remove(parentId);
                }
                children = new JSONArray();
                children.addAll(childrenMap.values());





                // log.info("开始 check");
                checkCycle(parentIds, children);
                // l.remove("children");
                l.put("children", children);
            }
            parentIds.remove(l.getString("id"));
        }
    }




    void updateChildrenNum (JSONObject link) {
        JSONArray children = link.getJSONArray("children");
        if (ObjectUtil.isEmpty(children)) {
            link.put("children_num", 1);
        }else{
            if (ObjectUtil.isEmpty(link.getInteger("children_num"))) {
                for (int i = 0; i < children.size(); i++) {
                    JSONObject child = children.getJSONObject(i);
                    updateChildrenNum(child);
                }

                int childrenNum = 0;
                for (int i = 0; i < children.size(); i++) {
                    JSONObject child = children.getJSONObject(i);
                    childrenNum+=(child.getInteger("children_num"));
                }

                link.put("children_num", childrenNum);
            }


        }

    }
    Map<String,JSONObject> listToMap(List<JSONObject> list,String key) {
        Map<String,JSONObject> map = new HashMap<>();
        for (JSONObject o: list) {
            map.put(o.getString(key),o);
        }
        return map;
    }
    /**
     * 设置链路的上下级关系
     * @param links
     * @param linkRelations
     */
    public void updateParentId(List<JSONObject> links, List<JSONObject> linkRelations) {
        // if (ObjectUtil.isEmpty(lowerToUpperLinks)) return;

        Map<String,List<JSONObject>> lowerToUpperLinks = linkRelations.stream().collect(Collectors.groupingBy(d->d.getString("lower_link_id")));
        Map<String, JSONObject> linkMap = listToMap(links, "id");

        for (JSONObject l : links) {
            List<JSONObject> upperList = lowerToUpperLinks.get(l.getString("id"));
            if (ObjectUtil.isEmpty(upperList)) continue;
            for (JSONObject upper : upperList) {
                Integer layer = Optional.ofNullable(l.getInteger("layer")).orElse(99);
                String parentId = upper.getString("upper_link_id");
                l.put("parent_id", upper.getString("upper_link_id"));

                JSONObject parentLink = linkMap.get(parentId);
                if (ObjectUtil.isEmpty(parentLink)) continue;
                l.put("parent_code", parentLink.getString("code"));
                Integer upperLayer = upper.getInteger("layer");
                if (upperLayer<layer) {
                    l.put("layer", upperLayer);
                }
                // l.put("a_physic_device_id", upper.getString("a_ne_id"));
                // l.put("z_physic_device_id", upper.getString("z_ne_id"));

                /*
                l.put("a_ne_id", upper.getString("a_ne_id"));
                l.put("z_ne_id", upper.getString("z_ne_id"));
                l.put("a_port_id", upper.getString("a_port_id"));
                l.put("z_port_id", upper.getString("z_port_id"));
                l.put("seg_no", upper.getInteger("seg_no"));
                l.put("grp_no", upper.getInteger("grp_no"));
                l.put("order_no", upper.getInteger("order_no"));
                 */

                // List<JSONObject> children= parentLink.getJSONArray("children");
                if (ObjectUtil.isEmpty(parentLink.getJSONArray("children"))) {
                    // children = new ArrayList<>();
                    parentLink.put("children", new ArrayList<>());
                }
                JSONArray children = parentLink.getJSONArray("children");

                if (ObjectUtil.isEmpty(l.getString("a_physic_device_id"))) {
                    l.put("a_physic_device_id",parentLink.getString("a_physic_device_id"));
                }

                if (ObjectUtil.isEmpty(l.getString("z_physic_device_id"))) {
                    l.put("z_physic_device_id",parentLink.getString("z_physic_device_id"));
                }

                children.add(l);
            }
        }
    }


    /**
     * 交换链路的AZ端
     * @param l
     */
    public void swapAZ(JSONObject l ) {
        String aPhysicDeviceId = l.getString("a_physic_device_id");
        String aPortCode = l.getString("a_port_code");
        String aName = l.getString("a_name");
        String aPortId = l.getString("a_port_id");
        String aPortSpecId = l.getString("a_port_spec_id");
        String zPhysicDeviceId = l.getString("z_physic_device_id");
        String zPortCode = l.getString("z_port_code");
        String zName = l.getString("z_name");
        String zPortId = l.getString("z_port_id");
        String zPortSpecId = l.getString("z_port_spec_id");


        l.put("a_physic_device_id", zPhysicDeviceId);
        l.put("a_port_code", zPortCode);
        l.put("a_name" , zName);
        l.put("a_port_id", zPortId);
        l.put("a_port_spec_id", zPortSpecId);
        l.put("z_physic_device_id", aPhysicDeviceId);
        l.put("z_port_code", aPortCode);
        l.put("z_name" , aName);
        l.put("z_port_id", aPortId);
        l.put("z_port_spec_id", aPortSpecId);
    }


    /**
     * 根据链路的AZ端对节点排序
     * @param devices
     * @param rootLinks
     * @return
     */
    /**
     * 根据链路的AZ端对节点排序
     * @param devices
     * @param rootLinks
     * @return
     */
    List<JSONObject> sortDevices (List<JSONObject> devices,List<JSONObject> rootLinks, TopoMode mode) {
        // 计算节点的顺序

        Map<String,JSONObject> idMapDevice = new HashMap();
        for (JSONObject d: devices) idMapDevice.put(d.getString("id"), d);

        List<String> deviceIds= new ArrayList<>();
        for (JSONObject root: rootLinks) {
            // computeNodeOrder(deviceIds,root);
            List<String> ids = computeNodeOrder(root,  mode);
            deviceIds.addAll(ids);
        }

        deviceIds = deviceIds.stream().distinct().collect(Collectors.toList());

        List<JSONObject> sortedDevice = new ArrayList<>();

        for (String id: deviceIds) {
            JSONObject d = idMapDevice.get(id);
            if (ObjectUtil.isEmpty(d)) {
                d = new JSONObject();
                d.put("id",id);
                d.put("name", "省外设备");
            }
            sortedDevice.add(d);
        }
        // sortedByRoom(sortedDevice);
        int i=0;
        for (JSONObject d:sortedDevice) {
            d.put("seq",i);
            i++;
        }
        return sortedDevice;
    }

    void sortedByRoom(List<JSONObject> devices) {
        int length = devices.size();
        for (int k=0;k<length;k++) {
            for (int j = length - 1; j >= 0; j--) {
                if (j - 1 > 0) {
                    JSONObject device = devices.get(j);
                    JSONObject previous = devices.get(j - 1);
                    for (int i = 0; i < j - 1; i++) {
                        JSONObject firstRoomDevice = devices.get(i);
                        if (ObjectUtil.isNotEmpty(device.getString("facility_id"))
                                && device.getString("facility_id").equals(firstRoomDevice.getString("facility_id"))
                                && !device.getString("facility_id").equals(previous.getString("facility_id"))) {
                            insertQueue(devices, i + 1, j);
                            break;
                        }
                    }
                }
            }
        }
    }

    void insertQueue (List<JSONObject> devices, int from, int to) {
        JSONObject toObject = devices.get(to);
        for (int i=to;i>from;i--) {
            devices.set(i, devices.get(i-1));
        }
        devices.set(from, toObject);

    }



    /**
     * 计算节点的序号
     * @param link
     * @return
     */
    public List<String> computeNodeOrder(JSONObject link,TopoMode mode) {
        List<String> nodeIds = new ArrayList<>();
        String startId = link.getString("a_physic_device_id");
        String endId = link.getString("z_physic_device_id");
        computeNodeOrder(Arrays.asList(link),startId,endId, nodeIds, mode,link.getInteger("layer"));
        return nodeIds;
    }


    /**
     * 递归计算节点的序号
     * @param links
     * @param startId
     * @param endId
     *
     */
    List<JSONObject> computeNodeOrder(List<JSONObject> links, String startId,String endId, List<String> allNodeIds, TopoMode mode, int layer) {
        List<JSONObject> result = new ArrayList<>();

        Set<String> nodeIds = new HashSet<>();
        appendNodeId(allNodeIds, startId); // 将起始节点加入节点列表
        // appendNodeFromSameRoom(nodeIds, startId, idMapDevice,bottomLinks); // 将启示节点同机房的设备加入节点列表
        List<String> currentIds = Arrays.asList(startId);
        // String currentId = startId;
        List<JSONObject> linksCopy = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(links)) {
            linksCopy  = links.stream().collect(Collectors.toList());
        }else{
            return result;
        }
        while (true) {
            List<String> nextIds= new ArrayList<>();
            List<JSONObject> firstLinks = new ArrayList<>();

            List<JSONObject> resultLinks = computeFirstLinks(linksCopy, currentIds,endId,  mode,layer); // 查这着起始链路
            firstLinks.addAll(resultLinks);
            if (ObjectUtil.isEmpty(firstLinks)) break;
            Set<String> firstLinkIds = new HashSet<>();
            while (!ObjectUtil.isEmpty(firstLinks)) {
                // 从最长的链路开始遍历起
                JSONObject l = firstLinks.get(0);
                if (firstLinks.size()>1) {
                    int maxChildrenNum = l.getInteger("children_num");
                    for (JSONObject fl: firstLinks) {
                        int childrenNum = fl.getInteger("children_num");
                        if (childrenNum> maxChildrenNum || (l.getString("z_physic_device_id").equals(endId)
                                && !fl.getString("z_physic_device_id").equals(endId)

                        )) {
                            // 取最长的并且尚未到终点的链路
                            maxChildrenNum = childrenNum;
                            // log.info("最长的:"+fl.getString("id"));
                            l = fl;
                        }
                    }
                }


                String aPhysicDeviceId = l.getString("a_physic_device_id");
                String zPhysicDeviceId = l.getString("z_physic_device_id");



                nodeIds.add(zPhysicDeviceId);

                /*
                    将起始链路的起始节点加入列表,起始链路的终止节点作为下一跳继续查找
                */
                //if (!l.getString("spec_id").equals("断点")) {
                List<JSONObject> children = computeNodeOrder((List<JSONObject>) (l.get("children")), aPhysicDeviceId, zPhysicDeviceId, allNodeIds, mode,(layer+1));
                l.put("children", children);
                //}



                result.add(l);

                appendNodeId(nextIds, zPhysicDeviceId);
                firstLinkIds.add(l.getString("id"));
                String linkId = l.getString("id");
                firstLinks.removeIf(fl->fl.getString("id").equals(linkId));

            }


            //}
            // currentId = nextId;



            /*
            从链路列表中剔除起始链路
             */
            linksCopy.removeIf(fl->firstLinkIds.contains(fl.getString("id")));

            currentIds = nextIds;

        }

        // appendNodeId(nodeIds, endId);

        appendEndNodeId(allNodeIds, endId);

        return result;


        // appendNodeFromSameRoom(nodeIds, endId, idMapDevice); // 将启示节点同机房的设备加入节点列表
    }


    List<JSONObject> computeRootLinkOrder(List<JSONObject> links, String startId,String endId, List<String> allNodeIds, TopoMode mode, int layer) {
        List<JSONObject> result = new ArrayList<>();

        Set<String> nodeIds = new HashSet<>();
        appendNodeId(allNodeIds, startId); // 将起始节点加入节点列表
        List<String> currentIds = Arrays.asList(startId);
        // String currentId = startId;
        List<JSONObject> linksCopy = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(links)) {
            linksCopy  = links.stream().collect(Collectors.toList());
        }else{
            return result;
        }
        while (true) {
            List<String> nextIds= new ArrayList<>();
            List<JSONObject> firstLinks = new ArrayList<>();

            List<JSONObject> resultLinks = computeFirstLinks(linksCopy, currentIds,endId,  mode,layer); // 查这着起始链路
            firstLinks.addAll(resultLinks);
            if (ObjectUtil.isEmpty(firstLinks)) break;
            Set<String> firstLinkIds = new HashSet<>();
            while (!ObjectUtil.isEmpty(firstLinks)) {
                // 从最长的链路开始遍历起
                JSONObject l = firstLinks.get(0);
                if (firstLinks.size()>1) {
                    int maxChildrenNum = l.getInteger("children_num");
                    for (JSONObject fl: firstLinks) {
                        int childrenNum = fl.getInteger("children_num");
                        if (childrenNum> maxChildrenNum || (l.getString("z_physic_device_id").equals(endId)
                                && !fl.getString("z_physic_device_id").equals(endId)

                        )) {
                            // 取最长的并且尚未到终点的链路
                            maxChildrenNum = childrenNum;
                            // log.info("最长的:"+fl.getString("id"));
                            l = fl;
                        }
                    }
                }


                String aPhysicDeviceId = l.getString("a_physic_device_id");
                String zPhysicDeviceId = l.getString("z_physic_device_id");



                nodeIds.add(zPhysicDeviceId);


                result.add(l);

                appendNodeId(nextIds, zPhysicDeviceId);
                firstLinkIds.add(l.getString("id"));
                String linkId = l.getString("id");
                firstLinks.removeIf(fl->fl.getString("id").equals(linkId));

            }


            //}
            // currentId = nextId;



            /*
            从链路列表中剔除起始链路
             */
            linksCopy.removeIf(fl->firstLinkIds.contains(fl.getString("id")));

            currentIds = nextIds;

        }

        // appendNodeId(nodeIds, endId);

        appendEndNodeId(allNodeIds, endId);

        return result;


        // appendNodeFromSameRoom(nodeIds, endId, idMapDevice); // 将启示节点同机房的设备加入节点列表
    }

    void appendNodeFromSameRoom(List<String> nodeIds, String startId, Map<String,JSONObject> idMapDevice, List<JSONObject> bottomLinks) {

        JSONObject startDevice = idMapDevice.get(startId);
        String startFacilityId = startDevice.getString("facility_id");
        List<String> ids = new ArrayList<>();
        if (ObjectUtil.isEmpty(startFacilityId)) return;
        for (String id: idMapDevice.keySet()) {
            JSONObject device = idMapDevice.get(id);
            String facilityId = device.getString("facility_id");
            if (id.equals(startId)) continue;
            if (ObjectUtil.isEmpty(facilityId)) continue;
            if (facilityId.equals(startFacilityId)) {
                ids.add(id);
            }
        }
        List<String> startIds = new ArrayList<>();
        startIds.add(startId);

        while (!ids.isEmpty()) {
            List<String> appendIds = new ArrayList<>();
            for (String sid: startIds) {
                for (String tid: ids) {
                    if (isLink(sid,tid,bottomLinks)) {
                        appendIds.add(tid);
                    }
                }
            }


            if (ObjectUtil.isEmpty(appendIds)) {
                appendIds.add(ids.get(0));
            }

            ids = ids.stream().filter(id->{
                for (String appendId: appendIds) {
                    if (appendId.equals(id)) {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());
            for (String appendId: appendIds) {
                startIds.add(appendId);
            }
        }

        for (String id: startIds) {
            appendNodeId(nodeIds,id);
        }
    }

    boolean isLink(String aid,String bid,List<JSONObject> links) {
        for (JSONObject l: links) {
            if (aid.equals(l.getString("a_physic_device_id")) && bid.equals(l.getString("z_physic_device_id"))) {
                return true;
            }
            if (bid.equals(l.getString("a_physic_device_id")) && aid.equals(l.getString("z_physic_device_id"))) {
                return true;
            }
        }
        return false;
    }



    public Integer getSideNum (List<JSONObject> links) {
        Set<String> sides = new HashSet<>();
        if (ObjectUtil.isEmpty(links)) return 0;
        for (JSONObject l: links) {
            String aId = l.getString("a_physic_device_id");
            String bId = l.getString("z_physic_device_id");

            if(ObjectUtil.isNull(aId) || ObjectUtil.isNull(bId)){
                continue;
            }
            if (aId.compareTo(bId)<0) {
                String sid=aId+"-"+bId;
                sides.add(sid);
            }else{
                String sid=bId+"-"+aId;
                sides.add(sid);
            }
        }
        return sides.size();
    }

    public Map<String,List<JSONObject>> getDevice2Links(List<JSONObject> links) {
        Map<String,List<JSONObject>> device2Links = new HashMap<>();
        if (ObjectUtil.isNotEmpty(links)) {
            for (JSONObject l1: links) {
                String aPhysicDeviceId = l1.getString("a_physic_device_id");
                String zPhysicDeviceId = l1.getString("z_physic_device_id");

                List<String> deviceIds = Arrays.asList(aPhysicDeviceId,zPhysicDeviceId);
                for (String deviceId: deviceIds) {
                    if (device2Links.containsKey(deviceId)) {
                        device2Links.get(deviceId).add(l1);
                    }else{
                        List<JSONObject> deviceLinks = new ArrayList<>();
                        deviceLinks.add(l1);
                        device2Links.put(deviceId,deviceLinks);
                    }
                }
            }
        }


        return device2Links;
    }


    /**
     *
     * @param links
     * @param currentIds
     * @return
     */
    public List<JSONObject> computeFirstLinks (List<JSONObject> links, List<String> currentIds, String endId, TopoMode mode,int layer) {

        Map<String,List<JSONObject>> device2Links = getDevice2Links(links);
        List<JSONObject> firstLinks = new ArrayList<>();
        List<JSONObject> waitFirstLinks = new ArrayList<>();
        if (ObjectUtil.isEmpty(links)) {
            links = new ArrayList<>();
        }

        // 查询从当前点触发的链路
        {
            final List<String> uniqueCurrentIds = currentIds.stream().distinct().collect(Collectors.toList());
            while(!uniqueCurrentIds.isEmpty()) {
                String currentId = uniqueCurrentIds.get(0);
                List<JSONObject> firstLinksFounded = links.stream().filter(l->{
                    String aPhysicDeviceId=l.getString("a_physic_device_id");
                    // JSONObject aDevice = idMapDevice.get(aPhysicDeviceId);
                    // String aFacilityId = aDevice.getString("facility_id");
                    String zPhysicDeviceId=l.getString("z_physic_device_id");
                    // JSONObject zDevice = idMapDevice.get(zPhysicDeviceId);
                    // String zFacilityId = zDevice.getString("facility_id");
                    // JSONObject startDevice  = idMapDevice.get(currentId);
                    // String startFacilityId = startDevice.getString("facility_id");

                    if (ObjectUtil.isNotEmpty(currentId) && ObjectUtil.isNotEmpty(endId) && currentId.equals(endId)) {
                        return false;
                    }


                    else  {
                        if (ObjectUtil.isNotEmpty(aPhysicDeviceId) && aPhysicDeviceId.equals(currentId)) {
                            return true;
                        }

                        if (ObjectUtil.isNotEmpty(zPhysicDeviceId) && zPhysicDeviceId.equals(currentId)) {
                            swapAZ(l);
                            return true;
                        }
                    }

                    return false;

                }).collect(Collectors.toList());

                // 如果有多个节点正在检索，当前的节点有超过1个出度,则等另外一个节点遍历完,用来处理OTN的环等待
                if (uniqueCurrentIds.size()>1 && getSideNum(device2Links.get(currentId))>1) {
                    waitFirstLinks.addAll(firstLinksFounded);
                } else {
                    firstLinks.addAll(firstLinksFounded);
                }
                uniqueCurrentIds.remove(currentId);
            }
        }

        if (ObjectUtil.isEmpty(firstLinks)) {
            // 如果只有1个出度的节点都找不到起始链路，则将等待链路作为找到链路
            firstLinks.addAll(waitFirstLinks);
        } else if (layer == 0) {
            firstLinks.addAll(waitFirstLinks);
        }


        // 找到断点链路，将断点链路作为起始链路
        boolean hasBreakPoint = false;

        if (firstLinks.size()==0 && links.size()>0) {
            Set<String> deviceIds = device2Links.keySet();
            for (String id: deviceIds) {
                List<JSONObject> deviceLinks = device2Links.get(id);
                if (getSideNum(deviceLinks)==1) {
                    for (JSONObject deviceLink: deviceLinks) {
                        if (!id.equals(endId)) {
                            if (deviceLink.getString("z_physic_device_id").equals(id)) {
                                // 如果断点在z端,则调换
                                swapAZ(deviceLink);
                            }
                            firstLinks.add(deviceLink);
                            hasBreakPoint = true;
                        }

                    }
                }
            }
        }

        if (mode==TopoMode.LINK && firstLinks.size()>0 && hasBreakPoint) {
            // 如果是链路模式,则获取第1个断点，如果是网络模式，从所有的断点出发
            JSONObject l = firstLinks.get(0);
            firstLinks = firstLinks.stream().filter(fl->fl.getString("a_physic_device_id").equals(l.getString("a_physic_device_id"))
            && fl.getString("z_physic_device_id").equals(l.getString("z_physic_device_id"))
            ).collect(Collectors.toList());
        }


        // 针对组网模式,如果找到断点,则向链路中自动补偿中断链路
        List<JSONObject> errorLinks = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(firstLinks) && mode==TopoMode.NET && hasBreakPoint) {
            // 如果找到中间的断点
            final List<String> uniqueCurrentIds = currentIds.stream().distinct().collect(Collectors.toList());
            for (String currentId: uniqueCurrentIds) {
                for (JSONObject firstLink: firstLinks) {
                    String zPhysicDeviceId = firstLink.getString("a_physic_device_id");
                    JSONObject l = new JSONObject();
                    l.put("a_physic_device_id", currentId);
                    l.put("z_physic_device_id", zPhysicDeviceId);
                    l.put("id", UUID.randomUUID());
                    l.put("gid",l.getString("id"));
                    l.put("spec_id","断点");
                    l.put("spec_name","断点");
                    l.put("name","");
                    l.put("code","");
                    l.put("children_num",0);
                    l.put("layer",layer);
                    errorLinks.add(l);
                }
            }

        }else if ( ObjectUtil.isEmpty(links) && mode==TopoMode.NET && !hasBreakPoint) {
            // 如果最后一个点是断点

            final List<String> uniqueCurrentIds = currentIds.stream().distinct().collect(Collectors.toList());
            for (String currentId: uniqueCurrentIds) {

                if (!currentId.equals(endId)) {
                    log.info("需要添加："+currentId+"-"+endId+"-"+layer);

                    JSONObject l = new JSONObject();
                    l.put("a_physic_device_id", currentId);
                    l.put("z_physic_device_id", endId);
                    l.put("id", UUID.randomUUID());
                    l.put("gid",l.getString("id"));
                    l.put("spec_id","断点");
                    l.put("spec_name","断点");
                    l.put("name","");
                    l.put("code","");
                    l.put("children_num",0);
                    l.put("layer",layer);
                    errorLinks.add(l);

                }


            }


        }

        if (ObjectUtil.isNotEmpty(errorLinks) && mode==TopoMode.NET ) {
            firstLinks.clear();
            firstLinks.addAll(errorLinks);
        }







        // 如果一条也找不到,则随机找一条链路
        if (firstLinks.size()==0 && links.size()>0){
            JSONObject l = links.get(0);
            String aPhysicDeviceId=l.getString("a_physic_device_id");
            // JSONObject aDevice = idMapDevice.get(aPhysicDeviceId);
            /*
            String aFacilityId = "";
            if (ObjectUtil.isNotEmpty(aDevice)) {
                aFacilityId = aDevice.getString("facility_id");
            }

             */

            // JSONObject endDevice = idMapDevice.get(endId);
            /*
            String endFacilityId = "";
            if(ObjectUtil.isNotEmpty(endDevice)) {
                endFacilityId = endDevice.getString("facility_id");
            }

             */

            if (ObjectUtil.isNotEmpty(aPhysicDeviceId) && aPhysicDeviceId.equals(endId)) {
                swapAZ(l);
            }
            /*
            else if (ObjectUtil.isNotEmpty(aFacilityId) &&  aFacilityId.equals(endFacilityId)) {
                swapAZ(l);
            }

             */


            firstLinks = Arrays.asList(l);
        }




        /*
        List<JSONObject> firstLinksDebug = firstLinks.stream().map(l->{
            JSONObject result =new JSONObject();
            result.put("a_id", l.getString("a_physic_device_id"));
            result.put("z_id", l.getString("z_physic_device_id"));
            // result.put("a_name", idMapDevice.get(l.getString("a_physic_device_id")).getString("name"));
            // result.put("z_name", idMapDevice.get(l.getString("z_physic_device_id")).getString("name"));
            return result;
        }).collect(Collectors.toList());


        List<JSONObject> allLinksDebug = links.stream().map(l->{
            JSONObject result =new JSONObject();
            result.put("a_id", l.getString("a_physic_device_id"));
            result.put("z_id", l.getString("z_physic_device_id"));
            // result.put("a_name", idMapDevice.get(l.getString("a_physic_device_id")).getString("name"));
            // result.put("z_name", idMapDevice.get(l.getString("z_physic_device_id")).getString("name"));
            return result;
        }).collect(Collectors.toList());
        */
        return firstLinks;

    }

    void appendNodeId(List<String> nodeIds, String id) {
        /*
        if (nodeIds.contains(id)) {
            nodeIds.remove(id);
        }
        nodeIds.add(id);
        */
        if (!nodeIds.contains(id)) {
            nodeIds.add(id);
        }

    }

    void appendEndNodeId (List<String> nodeIds, String id) {
        // String firstId=nodeIds.get(0);
        // if (id.equals(firstId)) return;
        if (nodeIds.contains(id)) {
            nodeIds.remove(id);
        }
        nodeIds.add(id);
    }
}
