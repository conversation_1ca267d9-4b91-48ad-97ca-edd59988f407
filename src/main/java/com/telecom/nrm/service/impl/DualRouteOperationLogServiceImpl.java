package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.DualRouteOperationLogDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.DualRouteOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 双路由管理操作日志服务实现类
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */

@Service
public class DualRouteOperationLogServiceImpl implements DualRouteOperationLogService {
    private static final Logger log = LoggerFactory.getLogger(DualRouteOperationLogServiceImpl.class);

    @Autowired
    private DualRouteOperationLogDao dualRouteOperationLogDao;


    @Override
    public boolean recordOperationLog(String operationType, String objectType, Long objectId,
                                     String operationDescription, String areaCode,
                                     String operatorId, String operatorName,
                                     HttpServletRequest request) {
        return recordOperationLogFull(operationType, objectType, objectId, null, null,
                                     operationDescription, null, null, areaCode,
                                     operatorId, operatorName, request);
    }

    @Override
    public boolean recordOperationLogFull(String operationType, String objectType, Long objectId,
                                         String objectCode, String objectName, String operationDescription,
                                         JSONObject beforeData, JSONObject afterData, String areaCode,
                                         String operatorId, String operatorName, HttpServletRequest request) {

        // 优先使用前端传入的用户信息，如果没有则从JWT获取
        String finalOperatorId = operatorId;
        String finalOperatorName = operatorName;

        if (operatorId == null || operatorName == null) {
            try {

                JwtUser jwtUser = SecurityContext.getJwtUser();
                if (jwtUser != null) {
                    if (operatorId == null) {
                        finalOperatorId = jwtUser.getId();
                    }
                    if (operatorName == null) {
                        finalOperatorName = jwtUser.getUsername();
                    }
                    log.info("📋 从JWT获取用户信息: operatorId={}, operatorName={}", finalOperatorId, finalOperatorName);
                }
            } catch (Exception e) {
                log.warn("从JWT获取用户信息失败: {}", e.getMessage());
                if (finalOperatorId == null) finalOperatorId = "UNKNOWN";
                if (finalOperatorName == null) finalOperatorName = "UNKNOWN";
            }
        }

        log.info("🎯 开始记录业务操作日志: operationType={}, objectType={}, objectId={}, objectCode={}, operatorName={}, areaCode={}",
                operationType, objectType, objectId, objectCode, finalOperatorName, areaCode);

        try {
            // 构建业务日志参数，包含重要的审计字段
            JSONObject logParam = buildSimpleLogParam(operationType, objectType, objectId, objectCode, objectName,
                                                     operationDescription, areaCode, finalOperatorId, finalOperatorName);

            // 填充修改前后数据和变更字段
            if (beforeData != null) {
                String beforeDataStr = beforeData.toJSONString();
                logParam.put("before_data", beforeDataStr);
                log.info("📋 记录修改前数据: {}", beforeDataStr);
            } else {
                logParam.put("before_data", null);
                log.info("📋 修改前数据为空");
            }

            if (afterData != null) {
                String afterDataStr = afterData.toJSONString();
                logParam.put("after_data", afterDataStr);
                log.info("📋 记录修改后数据: {}", afterDataStr);
            } else {
                logParam.put("after_data", null);
                log.info("📋 修改后数据为空");
            }

            // 计算变更字段
            if (beforeData != null && afterData != null) {
                try {
                    String changedFields = calculateChangedFields(beforeData, afterData);
                    logParam.put("changed_fields", changedFields);
                    log.info("📋 变更字段: {}", changedFields);
                } catch (Exception e) {
                    log.warn("计算变更字段失败: {}", e.getMessage());
                    logParam.put("changed_fields", "计算失败");
                }
            }

            // 记录请求参数
            if (request != null) {
                try {
                    JSONObject requestParams = buildRequestParams(request);
                    if (requestParams != null) {
                        logParam.put("request_params", requestParams.toJSONString());
                        log.info("📋 请求参数记录完成: method={}, url={}",
                                requestParams.getString("method"), requestParams.getString("url"));
                    }
                } catch (Exception e) {
                    log.warn("记录请求参数失败，跳过请求参数记录: {}", e.getMessage());
                    logParam.put("request_params", null);
                }
            }

            log.info("📋 完整业务日志参数构建完成，包含字段: {}", logParam.keySet());
            log.info("📋 准备插入数据库的参数: before_data={}, after_data={}",
                    logParam.getString("before_data"), logParam.getString("after_data"));

            // 获取分库编码
            String shardingCode = getShardingCodeByAreaCode(areaCode);
            log.info("🗄️ 分库编码映射: {} -> {}", areaCode, shardingCode);

            // 调用DAO插入日志
            log.info("💾 开始插入数据库...");
            Integer result = dualRouteOperationLogDao.insertOperationLog(logParam, shardingCode);
            log.info("💾 数据库插入结果: {}", result);

            boolean success = result != null && result > 0;
            if (success) {
                log.info("✅ 业务操作日志记录成功: operationType={}, objectType={}, objectId={}, result={}",
                        operationType, objectType, objectId, result);
            } else {
                log.warn("⚠️ 业务操作日志记录失败: operationType={}, objectType={}, objectId={}, result={}",
                        operationType, objectType, objectId, result);
            }

            return success;

        } catch (Exception e) {
            log.error("❌ 记录业务操作日志异常: operationType={}, objectType={}, objectId={}",
                     operationType, objectType, objectId, e);
            return false;
        }
    }

    @Override
    public PageResponse<JSONObject> queryOperationLog(JSONObject queryParams, Integer pageSize,
                                                     Integer currentPage, String shardingCode) {
        try {
            return dualRouteOperationLogDao.queryOperationLog(queryParams, pageSize, currentPage, shardingCode);
        } catch (Exception e) {
            log.error("查询操作日志失败", e);
            return new PageResponse<>();
        }
    }

    @Override
    public JSONObject queryOperationLogDetail(Long logId, String shardingCode) {
        try {
            log.info("🔍 [Service] 查询操作日志详情:");
            log.info("   日志ID: {}", logId);
            log.info("   分库编码: {}", shardingCode);

            JSONObject param = new JSONObject();
            param.put("id", logId);

            log.info("🔍 [Service] 调用DAO查询，参数: {}", param.toJSONString());

            JSONObject daoResult = dualRouteOperationLogDao.queryOperationLogDetail(param, shardingCode);

            log.info("🔍 [Service] DAO查询原始结果:");
            log.info("   结果类型: {}", daoResult != null ? daoResult.getClass().getSimpleName() : "null");
            log.info("   结果大小: {}", daoResult != null ? daoResult.size() : 0);

            if (daoResult != null && !daoResult.isEmpty()) {
                log.info("   包含字段: {}", daoResult.keySet());

                // 检查DataQL返回的标准格式
                Boolean success = daoResult.getBoolean("success");
                String message = daoResult.getString("message");
                Object data = daoResult.get("data");

                log.info("🔍 [Service] DataQL标准响应解析:");
                log.info("   success: {}", success);
                log.info("   message: {}", message);
                log.info("   data类型: {}", data != null ? data.getClass().getSimpleName() : "null");

                if (success != null && success) {
                    if (data instanceof List) {
                        List<?> dataList = (List<?>) data;
                        log.info("   data数组长度: {}", dataList.size());

                        if (!dataList.isEmpty() && dataList.get(0) instanceof JSONObject) {
                            JSONObject detailRecord = (JSONObject) dataList.get(0);
                            log.info("✅ [Service] 找到日志详情记录:");
                            log.info("   记录ID: {}", detailRecord.get("id"));
                            log.info("   操作类型: {}", detailRecord.get("operation_type"));
                            log.info("   操作时间: {}", detailRecord.get("operation_time"));
                            log.info("   操作人: {}", detailRecord.get("operator_name"));

                            return detailRecord;
                        } else {
                            log.warn("⚠️ [Service] 数据数组为空或格式不正确");
                            return new JSONObject();
                        }
                    } else if (data instanceof JSONObject) {
                        JSONObject detailRecord = (JSONObject) data;
                        log.info("✅ [Service] 找到日志详情记录:");
                        log.info("   记录ID: {}", detailRecord.get("id"));
                        log.info("   操作类型: {}", detailRecord.get("operation_type"));

                        return detailRecord;
                    } else {
                        log.warn("⚠️ [Service] data字段类型不正确: {}", data != null ? data.getClass() : "null");
                        return new JSONObject();
                    }
                } else {
                    log.warn("⚠️ [Service] DataQL查询失败: {}", message);
                    return new JSONObject();
                }
            } else {
                log.warn("⚠️ [Service] DAO返回结果为空");
                return new JSONObject();
            }

        } catch (Exception e) {
            log.error("❌ [Service] 查询操作日志详情失败: logId={}, shardingCode={}", logId, shardingCode, e);
            return new JSONObject();
        }
    }

    @Override
    public List<JSONObject> queryOperationLogStatistics(JSONObject queryParams, String shardingCode) {
        try {
            return dualRouteOperationLogDao.queryOperationLogStatistics(queryParams, shardingCode);
        } catch (Exception e) {
            log.error("查询操作日志统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Integer batchRecordOperationLog(List<JSONObject> logList, String shardingCode) {
        try {
            return dualRouteOperationLogDao.batchInsertOperationLog(logList, shardingCode);
        } catch (Exception e) {
            log.error("批量记录操作日志失败", e);
            return 0;
        }
    }

    @Override
    public Integer cleanupExpiredLogs(Integer retentionDays, String shardingCode) {
        try {
            JSONObject param = new JSONObject();
            param.put("retention_days", retentionDays);
            return dualRouteOperationLogDao.cleanupExpiredLogs(param, shardingCode);
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            return 0;
        }
    }

    /**
     * 构建完整的日志参数 - 包含所有字段
     */
    private JSONObject buildFullLogParam(String operationType, String objectType, Long objectId,
                                        String objectCode, String objectName, String operationDescription,
                                        JSONObject beforeData, JSONObject afterData, String areaCode,
                                        String operatorId, String operatorName, HttpServletRequest request) {
        long startTime = System.currentTimeMillis();
        JSONObject logParam = new JSONObject();

        // 操作人信息
        logParam.put("operator_id", operatorId);
        logParam.put("operator_name", operatorName);
        logParam.put("operator_ip", getClientIpAddress(request));
        logParam.put("session_id", request != null ? request.getSession().getId() : null);

        // 操作对象信息
        logParam.put("object_type", objectType);
        logParam.put("object_id", objectId);
        logParam.put("object_code", objectCode);
        logParam.put("object_name", objectName);

        // 操作信息
        logParam.put("operation_type", operationType);
        logParam.put("operation_module", "光路组管理");
        logParam.put("operation_description", operationDescription);

        // 地区信息
        logParam.put("area_code", areaCode);
        logParam.put("area_name", getAreaNameByCode(areaCode));
        logParam.put("speciality", "传输");

        // 数据变更信息
        logParam.put("before_data", beforeData != null ? beforeData.toJSONString() : null);
        logParam.put("after_data", afterData != null ? afterData.toJSONString() : null);
        logParam.put("changed_fields", calculateChangedFields(beforeData, afterData));

        // 操作结果
        logParam.put("operation_result", "success");
        logParam.put("error_message", null);

        // 请求信息
        logParam.put("request_params", buildRequestParams(request));
        logParam.put("response_data", null);

        // 性能信息
        long executionTime = System.currentTimeMillis() - startTime;
        logParam.put("execution_time_ms", executionTime);

        // 备注
        logParam.put("remark", null);

        return logParam;
    }

    /**
     * 构建简化的业务日志参数 - 保留重要的审计字段
     */
    private JSONObject buildSimpleLogParam(String operationType, String objectType, Long objectId,
                                          String objectCode, String objectName, String operationDescription,
                                          String areaCode, String operatorId, String operatorName) {
        long startTime = System.currentTimeMillis();
        JSONObject logParam = new JSONObject();

        log.info("📝 构建业务日志参数: operationType={}, objectType={}, objectId={}", operationType, objectType, objectId);

        // 核心业务信息
        logParam.put("operation_type", operationType);
        logParam.put("operation_description", operationDescription);
        logParam.put("operation_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 操作对象信息（保护组相关）
        logParam.put("object_type", objectType);
        logParam.put("object_id", objectId);
        logParam.put("object_code", objectCode);
        logParam.put("object_name", objectName);

        // 操作人信息
        logParam.put("operator_id", operatorId);
        logParam.put("operator_name", operatorName);

        // 地区信息
        logParam.put("area_code", areaCode);
        logParam.put("area_name", getAreaNameByCode(areaCode));

        // 业务模块
        logParam.put("operation_module", "双路由管理");
        logParam.put("speciality", "传输");

        // 操作结果
        logParam.put("operation_result", "success");

        // 重要的审计字段 - 保留空值，后续可以填充
        logParam.put("before_data", null);  // 修改前数据
        logParam.put("after_data", null);   // 修改后数据
        logParam.put("changed_fields", null); // 变更字段
        logParam.put("request_params", null);  // 请求参数
        logParam.put("response_data", null);   // 响应数据

        // 性能信息
        long executionTime = System.currentTimeMillis() - startTime;
        logParam.put("execution_time_ms", executionTime);

        // 其他字段
        logParam.put("error_message", null);
        logParam.put("remark", null);

        log.info("📝 业务日志参数构建完成，包含字段: {}", logParam.keySet());

        return logParam;
    }

    /**
     * 构建请求参数信息 - 重点记录POST请求body信息
     */
    private JSONObject buildRequestParams(HttpServletRequest request) {
        if (request == null) {
            return null;
        }

        JSONObject requestParams = new JSONObject();

        try {
            // 基本请求信息
            String method = request.getMethod() != null ? request.getMethod() : "";
            String url = request.getRequestURL() != null ? request.getRequestURL().toString() : "";
            String contentType = request.getContentType() != null ? request.getContentType() : "";

            requestParams.put("method", method);
            requestParams.put("url", url);
            requestParams.put("content_type", contentType);

            // 对于POST请求，尝试获取请求体内容
            if ("POST".equalsIgnoreCase(method) && contentType.contains("application/json")) {
                try {
                    // 从请求属性中获取请求体（需要在Controller中预先设置）
                    Object requestBodyAttr = request.getAttribute("REQUEST_BODY");
                    if (requestBodyAttr != null) {
                        if (requestBodyAttr instanceof String) {
                            // 尝试解析JSON字符串
                            try {
                                JSONObject bodyJson = JSONObject.parseObject((String) requestBodyAttr);
                                requestParams.put("request_body", bodyJson);
                            } catch (Exception e) {
                                requestParams.put("request_body", requestBodyAttr.toString());
                            }
                        } else {
                            requestParams.put("request_body", requestBodyAttr.toString());
                        }
                    } else {
                        // 如果没有预设的请求体，记录URL参数
                        JSONObject urlParams = new JSONObject();
                        if (request.getParameterMap() != null) {
                            for (String paramName : request.getParameterMap().keySet()) {
                                String[] paramValues = request.getParameterValues(paramName);
                                if (paramValues != null && paramValues.length > 0) {
                                    if (paramValues.length == 1) {
                                        urlParams.put(paramName, paramValues[0]);
                                    } else {
                                        urlParams.put(paramName, paramValues);
                                    }
                                }
                            }
                        }
                        if (!urlParams.isEmpty()) {
                            requestParams.put("url_parameters", urlParams);
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取POST请求体失败: {}", e.getMessage());
                    requestParams.put("request_body_error", "获取请求体失败: " + e.getMessage());
                }
            } else {
                // 非POST请求或非JSON请求，记录URL参数
                JSONObject urlParams = new JSONObject();
                if (request.getParameterMap() != null) {
                    for (String paramName : request.getParameterMap().keySet()) {
                        String[] paramValues = request.getParameterValues(paramName);
                        if (paramValues != null && paramValues.length > 0) {
                            if (paramValues.length == 1) {
                                urlParams.put(paramName, paramValues[0]);
                            } else {
                                urlParams.put(paramName, paramValues);
                            }
                        }
                    }
                }
                if (!urlParams.isEmpty()) {
                    requestParams.put("url_parameters", urlParams);
                }
            }

            log.info("📋 构建请求参数完成: method={}, url={}, hasBody={}",
                    method, url, requestParams.containsKey("request_body"));

        } catch (Exception e) {
            log.warn("构建请求参数失败: {}", e.getMessage());
            requestParams.put("method", "POST");
            requestParams.put("url", "");
            requestParams.put("error", "构建请求参数失败: " + e.getMessage());
        }

        return requestParams;
    }

    /**
     * 计算变更字段
     */
    private String calculateChangedFields(JSONObject beforeData, JSONObject afterData) {
        if (beforeData == null || afterData == null) {
            return "";
        }

        try {
            Set<String> changedFields = new HashSet<>();

            // 检查afterData中的字段
            for (String key : afterData.keySet()) {
                Object afterValue = afterData.get(key);
                Object beforeValue = beforeData.get(key);

                if (!Objects.equals(beforeValue, afterValue)) {
                    changedFields.add(key);
                }
            }

            // 检查beforeData中被删除的字段
            for (String key : beforeData.keySet()) {
                if (!afterData.containsKey(key)) {
                    changedFields.add(key);
                }
            }

            return String.join(",", changedFields);

        } catch (Exception e) {
            log.warn("计算变更字段时发生异常: {}", e.getMessage());
            return "计算异常";
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 根据地市编码获取分库编码
     */
    private String getShardingCodeByAreaCode(String areaCode) {
        if (StringUtils.hasText(areaCode)) {
            String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaCode);
            if (shardingCode != null) {
                return shardingCode;
            }
        }
        // 默认返回无锡的分库编码
        return NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get("无锡");
    }

    /**
     * 根据地市编码获取地市名称
     */
    private String getAreaNameByCode(String areaCode) {
        // 简单映射，实际项目中可能需要查询字典表
        if ("wx".equals(areaCode)) return "无锡";
        if ("nj".equals(areaCode)) return "南京";
        if ("sz".equals(areaCode)) return "苏州";
        if ("xz".equals(areaCode)) return "徐州";
        if ("cz".equals(areaCode)) return "常州";
        if ("nt".equals(areaCode)) return "南通";
        if ("lyg".equals(areaCode)) return "连云港";
        if ("ha".equals(areaCode)) return "淮安";
        if ("yc".equals(areaCode)) return "盐城";
        if ("yz".equals(areaCode)) return "扬州";
        if ("zj".equals(areaCode)) return "镇江";
        if ("tz".equals(areaCode)) return "泰州";
        if ("sq".equals(areaCode)) return "宿迁";
        return areaCode;
    }



}
