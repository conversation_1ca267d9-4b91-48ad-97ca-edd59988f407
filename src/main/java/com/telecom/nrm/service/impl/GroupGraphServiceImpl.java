package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.service.AbstractGroupGraph;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.GroupGraphService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
@Slf4j
public class GroupGraphServiceImpl extends AbstractGroupGraph
        implements GroupGraphService {

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;


    @Override
    public JSONObject doApi(GraphRequest request) {
        JSONObject result = new JSONObject();
        Graph graph = graphApiService.doApi(request);
        if (null != graph && CollectionUtils.isNotEmpty(graph.getEdges())) {
            Set<Edge> edges = graph.getEdges();

        }
        return result;
    }

    public JSONObject buildGraph(JSONObject jsonObject) {
        super.buildGraph(new JSONObject(), "11");
        return null;
    }
}
