package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.QueryCfsEffectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QueryCfsEffectServiceImpl implements QueryCfsEffectService {

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    Map<String,JSONObject> listToMap(List<JSONObject> list) {
        Map<String,JSONObject> map = new HashMap<>();
        for (JSONObject o: list) {
            map.put(o.getString("gid"),o);
        }
        return map;
    }

    @Override
    public JSONObject findPipeEffect(List<String> codeList, String areaCode) {

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_pipe_effect").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("pipe_segment","codes",codeList)).build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);

        List<JSONObject> pipeSegmentList = graphService.filterNodeByLabel(data,"管道段");
        log.info("完成获取管道段");
        graphService.buildTree(data, "管道段","光缆段", "cable_segment_list");
        log.info("完成管道段-光缆段");

        List<JSONObject> cfsList = graphService.filterNodeByLabel(data,"光缆段影响业务宽表");


        Map<String,List<JSONObject>> sectToCfsMap = cfsList.stream().collect(Collectors.groupingBy(d->d.getString("cbl_sect_id")));

        List<JSONObject> segmentList = graphService.filterNodeByLabel(data,"光缆段");

        for (JSONObject segment:segmentList) {
            String cblSegId = segment.getString("id");
            List<JSONObject> segmentCfsList = sectToCfsMap.get(cblSegId);
            segment.put("cfs_list", segmentCfsList);
        }
        log.info("完成光缆段-CFS");


        JSONObject result = new JSONObject();
        result.put("pipeSegmentList", pipeSegmentList);
        return result;
    }

    @Override
    public JSONObject findCblSectEffect(List<String> codeList, String areaCode) {

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_cbl_sect_effect").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cable_segment","codes",codeList)).build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);

        List<JSONObject> cfsList = graphService.filterNodeByLabel(data,"光缆段影响业务宽表");

        Map<String,List<JSONObject>> sectToCfsMap = cfsList.stream().collect(Collectors.groupingBy(d->d.getString("cbl_sect_id")));

        List<JSONObject> segmentList = graphService.filterNodeByLabel(data,"光缆段");

        for (JSONObject segment:segmentList) {
            String cblSegId = segment.getString("id");
            List<JSONObject> segmentCfsList = sectToCfsMap.get(cblSegId);
            segment.put("cfs_list", segmentCfsList);
        }
        log.info("完成光缆段-CFS");
        JSONObject result = new JSONObject();
        result.put("cblSegmentList", segmentList);
        return result;
    }









}
