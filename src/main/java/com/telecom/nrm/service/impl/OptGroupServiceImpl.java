package com.telecom.nrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.controller.GraphSceneController;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import lombok.val;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OptGroupServiceImpl implements OptGroupService {
    private final Logger log = LoggerFactory.getLogger(GraphSceneController.class);


    @Autowired
    GraphService graphService;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    OptGroupDao optGroupDao;

    @Autowired
    OptRoadPairService optRoadPairService;

    @Autowired
    LifeCircuitService lifeCircuitService;








    public JSONObject opt_group_elements_analyze(List<JSONObject> optGroupElements, JSONObject optGroup, JSONObject previous_risk_check){
        // 添加optGroup空值检查
        if (optGroup == null) {
            log.error("optGroup参数为null，无法进行分析");
            throw new IllegalArgumentException("optGroup参数不能为null");
        }

        String ds = !Objects.isNull(optGroup.getString("ds")) ? optGroup.getString("ds") : "无锡";
        JSONObject need_save = new JSONObject();

        // 安全获取speciality字段，避免空指针异常
        String speciality = Optional.ofNullable(optGroup.getString("speciality")).orElse("");
        if((speciality.equals("数据")||speciality.equals("视频")) && optGroupElements.size()>2){
            optRoadPairService.opt_pair_route_no_analyse(optGroupElements,ds);
        }

        JSONObject result =optRoadPairService.opt_pair_analyse(optGroupElements, ds);
        need_save.put("check_result", result.getString("check_result"));
        need_save.put("object_id", Optional.ofNullable(optGroup.getString("id")).orElse(""));
        need_save.put("object_type", "optGroup");
        need_save.put("exception_info", result.getString("exception_info"));
        need_save.put("route_num", result.getString("route_num"));

        cn.hutool.json.JSONObject huJSONObject=new cn.hutool.json.JSONObject(result);
        String snapshot =  huJSONObject.toJSONString(0);
        need_save.put("snapshot", snapshot);


        // 获取上次检测结果
        String previous_check_result = "";
        String previous_exception_info = "";
        String deal_result = "";
        if(!Objects.isNull(previous_risk_check)){
            previous_check_result = Optional.ofNullable(previous_risk_check.getString("check_result")).orElse("");
            previous_exception_info = Optional.ofNullable(previous_risk_check.getString("exception_info")).orElse("");
            deal_result = Optional.ofNullable(previous_risk_check.getString("deal_result")).orElse("");
        }

        need_save.put("previous_check_result",previous_check_result);
        String exception_info = Optional.ofNullable(need_save.getString("exception_info")).orElse("");
        String check_result = Optional.ofNullable(need_save.getString("check_result")).orElse("");


        if(deal_result.startsWith("待整改(")){
            pm_risk_check_his_insert(previous_risk_check, ds);
        }
        if(deal_result.startsWith("忽略异常(")){
            need_save.put("deal_result", deal_result);
        } else{
            if (ObjectUtil.isNotEmpty(previous_check_result) && ObjectUtil.isNotEmpty(check_result) && !check_result.equals(previous_check_result) && check_result.equals("正常")) {
                need_save.put("deal_result", "已整改");
            } else if (ObjectUtil.isNotEmpty(check_result) && !check_result.equals("正常")) {
                need_save.put("deal_result", "待整改");
            }
        }
        if (!exception_info.equals(previous_exception_info) || !check_result.equals(previous_check_result)) {

            log.info("need_save"+need_save);
            Integer his_insert = pm_risk_check_his_insert(previous_risk_check, ds);
        }
        optGroupDao.pm_risk_check_insert(need_save, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
        result.put("check_result", need_save.getString("check_result"));
        result.put("exception_info", need_save.getString("exception_info"));
        return result;
    }




    public JSONObject get_previous_risk_check(JSONObject param){
        String sharding_code= NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(param.getString("ds"));
        JSONObject jsonObject= new JSONObject();
        PageResponse<JSONObject> pageResponse = optGroupDao.pm_risk_check_query(param, 1,1,  sharding_code);
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            jsonObject = pageResponse.getData().get(0);
        }

        return jsonObject;
    }

    public Integer get_previous_risk_check_and_his_save(JSONObject param){
        JSONObject previous_risk_check = get_previous_risk_check(param);
        Integer result = 0;
        if(ObjectUtil.isNotNull(previous_risk_check)){
            result=  pm_risk_check_his_insert(previous_risk_check, param.getString("ds"));
        }
        return result;
    }

    public JSONObject get_previous_risk_check_his(JSONObject param){
        String sharding_code= NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(param.getString("ds"));
        JSONObject jsonObject = optGroupDao.pm_risk_check_his_query(param,  sharding_code);
        return jsonObject;
    }

    public List<JSONObject> pm_risk_check_his_time_list_query(JSONObject param){
        String sharding_code= NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(param.getString("ds"));
        List<JSONObject> list = optGroupDao.pm_risk_check_his_time_list_query(param, sharding_code);
        return list;
    }

    public Integer pm_risk_check_his_insert(JSONObject param, String ds){
        String sharding_code= NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds);
        return optGroupDao.pm_risk_check_his_insert(param, sharding_code);
    }



    public JSONObject risk_analyze_previous(JSONObject request) {
        request.put("group_id", request.getString("id"));
        //request.put("name", request.getString("name")); 这里透传了光路保护组的名称作为查询条件 ,修改api时请注意
        List<JSONObject> optGroupElements =  optGroupDao.pm_opt_road_group_element_query(request , NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        JSONObject result=new JSONObject();
        log.info("optGroupElements, {}" , optGroupElements);
        //if(ObjectUtil.isNotEmpty(optGroup)) {
        request.put("object_id", request.getString("group_id"));
        request.put("object_type", "optGroup");
        boolean cached = request.getBoolean("cached");

        if (cached) {
            JSONObject previous_risk_check = get_previous_risk_check(request);
            if (cached && ObjectUtil.isNotEmpty(previous_risk_check) && ObjectUtil.isNotEmpty(previous_risk_check.getJSONObject("snapshot")) ) {
                result = previous_risk_check.getJSONObject("snapshot");
                result.put("check_result", previous_risk_check.getString("check_result"));
                result.put("exception_info", previous_risk_check.getString("exception_info"));
                return result;
            } else {
                return risk_analyze(request);
            }
        }else{
            return risk_analyze(request);
        }

        // result=  opt_pair_analyze(optGroupElements, request, get_previous_risk_check(request));
        //}
        // return result;
    }

    public JSONObject risk_analyze(JSONObject request) {
        request.put("group_id", request.getString("id"));

        List<JSONObject> optGroupElements;

        // 检查是否为隐患整改临时检测
        if (request.containsKey("checkType") && "hazard_remediation".equals(request.getString("checkType"))) {
            // 隐患整改场景：使用传入的光路编码构建临时光路组元素
            optGroupElements = buildTempOptGroupElements(request);
            log.info("隐患整改临时光路组元素: {}", optGroupElements);
        } else {
            // 正常场景：从数据库查询光路组元素
            optGroupElements = optGroupDao.pm_opt_road_group_element_query(request, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        }

        JSONObject result = new JSONObject();
        log.info("optGroupElements, {}", optGroupElements);

        request.put("object_id", request.getString("group_id"));
        request.put("object_type", "optGroup");

        // 对于临时检测，不查询历史记录
        JSONObject previousRiskCheck = null;
        if (!"hazard_remediation".equals(request.getString("checkType"))) {
            previousRiskCheck = get_previous_risk_check(request);
        }

        // 对于隐患整改临时检测，使用简化的分析逻辑
        if ("hazard_remediation".equals(request.getString("checkType"))) {
            result = opt_group_elements_analyze_temp(optGroupElements, request);
        } else {
            result = opt_group_elements_analyze(optGroupElements, request, previousRiskCheck);
        }
        return result;
    }

    /**
     * 构建临时光路组元素（用于隐患整改检测）
     */
    private List<JSONObject> buildTempOptGroupElements(JSONObject request) {
        List<JSONObject> tempElements = new ArrayList<>();

        // 从请求中获取光路编码列表
        Object opticalPathsObj = request.get("opticalPaths");
        if (opticalPathsObj instanceof List) {
            List<?> opticalPaths = (List<?>) opticalPathsObj;

            for (int i = 0; i < opticalPaths.size(); i++) {
                Object pathObj = opticalPaths.get(i);
                String pathCode = null;

                if (pathObj instanceof String) {
                    pathCode = (String) pathObj;
                } else if (pathObj instanceof JSONObject) {
                    JSONObject pathJson = (JSONObject) pathObj;
                    pathCode = pathJson.getString("code");
                }

                if (pathCode != null && !pathCode.trim().isEmpty()) {
                    JSONObject element = new JSONObject();
                    element.put("code", pathCode.trim());
                    element.put("name", pathCode.trim());
                    element.put("route_no", i + 1); // 路由序号
                    element.put("group_id", request.getString("id"));
                    element.put("attribute", "数据"); // 默认数据专业
                    element.put("state", "在用"); // 默认在用状态
                    tempElements.add(element);
                }
            }
        }

        log.info("构建的临时光路组元素: {}", tempElements);
        return tempElements;
    }

    /**
     * 临时光路组元素分析（用于隐患整改检测，不保存到数据库）
     */
    private JSONObject opt_group_elements_analyze_temp(List<JSONObject> optGroupElements, JSONObject optGroup) {
        // 添加optGroup空值检查
        if (optGroup == null) {
            log.error("optGroup参数为null，无法进行临时分析");
            throw new IllegalArgumentException("optGroup参数不能为null");
        }

        String ds = Optional.ofNullable(optGroup.getString("ds")).orElse("无锡");

        // 将地市代码转换为中文名称（opt_pair_analyse方法需要中文地市名称）
        String cityName = getCityNameFromAreaCode(ds);
        if (cityName == null) {
            log.warn("未找到地市 {} 对应的中文名称，使用默认值: 无锡", ds);
            cityName = "无锡";
        }

        log.info("原始地市代码: {}, 转换后的中文地市名称: {}", ds, cityName);

        // 调用光路对分析服务进行风险检测 - 传递中文地市名称
        JSONObject result = optRoadPairService.opt_pair_analyse(optGroupElements, cityName);

        // 为临时检测添加额外信息
        result.put("checkType", "hazard_remediation");
        result.put("tempGroup", true);
        result.put("groupId", Optional.ofNullable(optGroup.getString("id")).orElse(""));
        result.put("groupName", Optional.ofNullable(optGroup.getString("group_name")).orElse(""));
        result.put("opticalPathCount", optGroupElements.size());

        log.info("隐患整改临时检测结果: {}", result);
        return result;
    }

    /**
     * 从地市代码获取中文地市名称
     */
    private String getCityNameFromAreaCode(String areaCode) {
        if (areaCode == null) {
            return null;
        }

        // 如果已经是中文名称，直接返回
        if (areaCode.length() > 3 && !areaCode.contains("_")) {
            return areaCode;
        }

        // 根据简写代码映射到中文名称
        switch (areaCode.toLowerCase()) {
            case "nj": return "南京";
            case "sz": return "苏州";
            case "nt": return "南通";
            case "yz": return "扬州";
            case "lyg": return "连云港";
            case "cz": return "常州";
            case "wx": return "无锡";
            case "sq": return "宿迁";
            case "zj": return "镇江";
            case "yc": return "盐城";
            case "tz": return "泰州";
            case "ha": return "淮安";
            case "xz": return "徐州";
            default:
                // 如果是分片代码格式，提取地市简写再转换
                if (areaCode.startsWith("ds_bc_o3_")) {
                    String cityCode = areaCode.substring("ds_bc_o3_".length());
                    return getCityNameFromAreaCode(cityCode);
                }
                return null;
        }
    }

    public JSONObject risk_analyze_his(JSONObject request) {
        request.put("group_id", request.getString("id"));
        List<JSONObject> optGroupElements =  optGroupDao.pm_opt_road_group_element_query(request , NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        request.put("object_id", request.getString("group_id"));
        request.put("object_type", "optGroup");
        JSONObject previous_risk_check = get_previous_risk_check_his(request);
        if (ObjectUtil.isNotEmpty(previous_risk_check) && ObjectUtil.isNotEmpty(previous_risk_check.getJSONObject("snapshot"))) {
            String str =JSON.toJSONString(previous_risk_check.getJSONObject("snapshot"));
            JSONObject result = JSONObject.parseObject(str);
            result.put("check_result", previous_risk_check.getString("check_result"));
            result.put("exception_info", previous_risk_check.getString("exception_info"));
            return result;
        } else if (ObjectUtil.isNotEmpty(previous_risk_check)) {
            JSONObject result = new JSONObject();
            result.put("check_result", previous_risk_check.getString("check_result"));
            result.put("exception_info", previous_risk_check.getString("exception_info"));
            return result;
        } else {
            JSONObject result = new JSONObject();
            result.put("check_result", "无上次检测结果");
            return result;
        }
        // return result;
    }

















    public JSONObject optPair_statistic_dictionary(JSONObject jsonObject) {
        JSONObject result = new JSONObject();
        result.put("totalCount", optGroupDao.pm_opt_road_group_count(jsonObject,  NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));

        List<JSONObject> staticResult = optGroupDao.pm_opt_road_group_state_statistic_query(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if (ObjectUtil.isNotEmpty(staticResult)) {
            for (JSONObject group : staticResult) {
                result.put(group.getString("check_result"), group.getInteger("data_count"));
            }
        }

        /*
        result.put("checked", optGroupDao.pm_opt_road_group_state_statistic_query(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","同管道");
        result.put("samePipeSementCount", optGroupDao.pm_opt_road_group_state_statistic_query(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","同光缆");
        result.put("sameCableCount", optGroupDao.pm_opt_road_group_state_statistic_query(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","正常");
        result.put("normal", optGroupDao.pm_opt_road_group_state_statistic_query(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","已忽略异常");
        result.put("ignoreCount", optGroupDao.pm_opt_road_group_state_statistic_query(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        */

        return result;
    }

    public void opt_group_download_response(List<JSONObject> result, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("地市");
        header.createCell(1).setCellValue("区县");
        header.createCell(2).setCellValue("专业");
        header.createCell(3).setCellValue("光路组ID");
        header.createCell(4).setCellValue("光路组名称");
        header.createCell(5).setCellValue("光路组编码");
        header.createCell(6).setCellValue("相关客户");
        header.createCell(7).setCellValue("双路由数量");
        header.createCell(8).setCellValue("检测结果");
        header.createCell(9).setCellValue("处理结果");
        header.createCell(10).setCellValue("备注");
        header.createCell(11).setCellValue("集约化派单");
        header.createCell(12).setCellValue("人工确认");
        header.createCell(13).setCellValue("维护人员");
        header.createCell(14).setCellValue("光路编码");
        header.createCell(15).setCellValue("光路名称");
        header.createCell(16).setCellValue("局内光路");
        header.createCell(17).setCellValue("状态");
        header.createCell(18).setCellValue("IOIM工单");
        header.createCell(19).setCellValue("路由序号");
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=list.xlsx");

        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = response.getOutputStream();
        // 填充数据
        int rowIndex = 1;
        for (int i =0; i< result.size();i++) {
            JSONObject opt_group= result.get(i);
            int opt_group_member_size = opt_group.getJSONArray("members").size();
            if(opt_group_member_size == 0){
                XSSFRow row = sheet.createRow(rowIndex++);
                row_cell_create(row, opt_group);
            }else{
                for(int j = 0;j< opt_group_member_size ;j++){
                    XSSFRow row = sheet.createRow(rowIndex++);
                    JSONObject current_fiber = opt_group.getJSONArray("members").getJSONObject(j);
                    row_cell_create(row, opt_group);
                    row.createCell(14).setCellValue(current_fiber.getString("code"));
                    row.createCell(15).setCellValue(current_fiber.getString("name"));
                    row.createCell(16).setCellValue(current_fiber.getString("attribute"));
                    row.createCell(17).setCellValue(current_fiber.getString("state"));
                    row.createCell(18).setCellValue(current_fiber.getString("iom_order"));
                    row.createCell(19).setCellValue(current_fiber.getString("route_no"));
                }
            }
        }
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    public void row_cell_create(XSSFRow row, JSONObject opt_group){
        row.createCell(0).setCellValue(opt_group.getString("areaName"));
        row.createCell(1).setCellValue(opt_group.getString("leafRegion"));
        row.createCell(2).setCellValue(opt_group.getString("speciality"));
        row.createCell(3).setCellValue(opt_group.getString("id"));
        row.createCell(4).setCellValue(opt_group.getString("name"));
        row.createCell(5).setCellValue(opt_group.getString("code"));
        row.createCell(6).setCellValue(opt_group.getString(""));
        row.createCell(7).setCellValue(opt_group.getString("routeNum"));
        row.createCell(8).setCellValue(opt_group.getString("result"));
        row.createCell(9).setCellValue(opt_group.getString("dealResult"));
        row.createCell(10).setCellValue(opt_group.getString("note"));
        row.createCell(11).setCellValue(opt_group.getString(""));
        row.createCell(12).setCellValue(opt_group.getString("confirmOp"));
        row.createCell(13).setCellValue(opt_group.getString(""));
    }

    public Integer updateOptRiskDealResult (JSONObject jsonObject){
        return optGroupDao.pm_risk_check_deal_update(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("areaName")));
    }

    @Override
    public JSONObject queryOptGroupDetail(JSONObject param) {
        try {
            log.info("Service层查询光路保护组详情: id={}, ds={}", param.getString("id"), param.getString("ds"));

            String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(param.getString("ds"));
            JSONObject result = optGroupDao.queryOptGroupDetailById(param, shardingCode);

            log.info("Service层查询光路保护组详情完成: id={}, result={}", param.getString("id"), result != null ? "成功" : "失败");

            return result;
        } catch (Exception e) {
            log.error("Service层查询光路保护组详情失败: id={}", param.getString("id"), e);
            return null;
        }
    }

}
