package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.dao.OptViewDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GisService;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.OptViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OptViewServiceImpl implements OptViewService {

    @Autowired
    private OptViewDao optViewDao;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    OptGroupDao optGroupDao;

    @Autowired
    GisService gisService;

    /**
     * 声明本地线程变量
     */
    public static ThreadLocal<Map<String, Object>> threadLocal = new ThreadLocal<>();


    @Override
    public void computeDoubleRoute(JSONObject jsonParame) {
        /**
         * 1. 查询所有光缆段 查询局站数据
         * 2. 遍历光缆段，计算两两光缆段是否符合双路由规则
         * 3. 按照光缆id_id拼接，将结果入库
         */
        //List<JSONObject> jsonObjectOptList = optViewDao.fiber_opt_net(new JSONObject(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
        List<JSONObject> jsonObjectLinkList = optViewDao.fiber_opt_link(new JSONObject(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
       // JSONObject optLink11 = jsonObjectLinkList.stream().filter(item -> item.getString("code").equals("250JY.DECTJ-250GL.LJ000/ZJG03")).findFirst().orElse(null);
        //JSONObject optLink21 = jsonObjectLinkList.stream().filter(item -> item.getString("code").equals("250JY.DECTJ-250GL.LJ000/ZJG04")).findFirst().orElse(null);
        //JSONObject result1 = compareOptLink(optLink11, optLink21, jsonParame);
        //log.info("result:{}", result1);
        //if (true) {
        //    return;
        //}
        try {
            threadLocal.set(new HashMap<>());
            for (int i = 0; i < jsonObjectLinkList.size(); i++) {
                JSONObject optLink1 = jsonObjectLinkList.get(i);
                for (int j = i + 1; j < jsonObjectLinkList.size(); j++) {
                    JSONObject optLink2 = jsonObjectLinkList.get(j);
                    log.info("optLink1:{},optLink2:{}", optLink1, optLink2);
                    JSONObject result = compareOptLink(optLink1, optLink2, jsonParame);
                    log.info("result:{}", result);
                    JSONObject inserData = new JSONObject();

                    Integer linkId1 = optLink1.getInteger("cable_id");
                    Integer linkId2 = optLink2.getInteger("cable_id");
                    //按照从小到大拼接
                    String linkId = linkId1 < linkId2 ? fillZero(linkId1, 9) + fillZero(linkId2, 9) : fillZero(linkId2, 9) + fillZero(linkId1, 9);
                    inserData.put("object_id", linkId);
                    inserData.put("object_type", "fiberCable");
                    inserData.put("check_result", result.getBoolean("isDoubleRoute") ? "正常" : "");
                    JSONObject snapshot = new JSONObject();
                    if (result.getBoolean("isDoubleRoute")) {
                        snapshot.put("samePipeSegments", result.getJSONArray("samePipeSegments"));
                        snapshot.put("count_samePipeSegments", result.getJSONArray("samePipeSegments").size());
                    }
                    inserData.put("snapshot", snapshot.toJSONString());
                    //{\"sameCables_d\":[],\"cables\":[],\"count_sameCables\":0,\"count_samePipeSegments\":0,\"optRoads\":[{\"z_facility_code\":\"OLD_250AZQ.YNL00Z01\",\"code\":\"F1411111492\",\"gid\":\"opt_road$$321256300025800001993772\",\"a_port_id\":\"321251840000000000003474\",\"a_physic_device_id\":\"321252500000000000002196\",\"a_port_code\":\"1/14(SLQ4(3500))/002(双向光口)发\",\"z_physic_device_id\":\"321252500000000000004935\",\"z_port_code\":\"6/7(MCUD2)/001(双向光口)发\",\"spec_id\":1132400006,\"z_site_id\":\"321252770000000000000157\",\"name\":\"云南路-ZY-MSAP155-06(ASON1)/246-云南路-3500-2/622M01\",\"z_port_id\":\"321251840000000000003473\",\"a_facility_id\":\"321251070000000000078394\",\"z_facility_id\":\"321251070000000000089067\",\"id\":\"321256300025800001993772\",\"a_site_id\":\"321252770000000000000157\",\"a_facility_code\":\"250GL.YNL00Z01\",\"label_name\":\"光路\",\"label_code\":\"opt_road\"},{\"z_facility_code\":\"OLD_250AZQ.YNL00Z01\",\"code\":\"F1411111925\",\"gid\":\"opt_road$$321256300025800001993785\",\"a_port_id\":\"321251840000000000002273\",\"a_physic_device_id\":\"321252500000000000002196\",\"a_port_code\":\"1/5(SLQ4(3500))/002(双向光口)发\",\"z_physic_device_id\":\"321252500000000000004935\",\"z_port_code\":\"6/8(MCUD2)/001(双向光口)发\",\"spec_id\":1132400006,\"z_site_id\":\"321252770000000000000157\",\"name\":\"云南路-ZY-MSAP155-06(ASON1)/246-云南路-3500-2/622M02\",\"z_port_id\":\"321251840000000000002267\",\"a_facility_id\":\"321251070000000000078394\",\"z_facility_id\":\"321251070000000000089067\",\"id\":\"321256300025800001993785\",\"a_site_id\":\"321252770000000000000157\",\"a_facility_code\":\"250GL.YNL00Z01\",\"label_name\":\"光路\",\"label_code\":\"opt_road\"}],\"devices\":[{\"manufactor_id\":\"321143130000000000000912\",\"code\":\"246-云南路-3500-2\",\"address\":\"旧_[标准]云南路/云南路3楼/综合机房\",\"gid\":\"device$$321252500000000000002196\",\"model_id\":\"321143140000000000000572\",\"facility_name\":\"云南路3层综合机房\",\"manufactor_name\":\"华为\",\"pos_y\":\"32.065828\",\"pos_x\":\"118.771707\",\"model_name\":\"OptiX OSN3500\",\"spec_id\":1022200001,\"name\":\"246-云南路-3500-2\",\"facility_id\":\"321251070000000000078394\",\"id\":\"321252500000000000002196\",\"spec_name\":\"SDH设备\",\"label_name\":\"设备\",\"label_code\":\"device\"},{\"manufactor_id\":\"321143130000000000000644\",\"code\":\"云南路-ZY-MSAP155-06(ASON1)\",\"address\":\"旧_[标准]云南路/云南路3楼/综合机房\",\"gid\":\"device$$321252500000000000004935\",\"device_type\":\"大型MSAP设备\",\"model_id\":\"321143140000000000012063\",\"facility_name\":\"旧_[标准]云南路/云南路3楼/综合机房\",\"manufactor_name\":\"北京正有\",\"pos_y\":\"32.065828\",\"pos_x\":\"118.771707\",\"model_name\":\"MSAP-ZY155M\",\"spec_id\":1022200002,\"device_type_id\":81704821,\"name\":\"云南路-ZY-MSAP155-06(ASON1)\",\"facility_id\":\"321251070000000000089067\",\"id\":\"321252500000000000004935\",\"spec_name\":\"MSAP设备\",\"label_name\":\"设备\",\"label_code\":\"device\"}],\"cableWhiteCount\":0,\"samePipeSegments_d\":[],\"sameCables\":[],\"ds\":\"南京\",\"samePipeSegments\":[],\"sameoptRoads\":[],\"whiteCount\":0,\"opt_code_list\":[\"F1411111492\",\"F1411111925\"],\"pipeSegments\":[]}
                    inserData.put("exception_info", "");
                    inserData.put("deal_result", "");
                    inserData.put("previous_check_result", "");
                    log.info("inserData:{}", inserData);
                    //100000003100000029
                    optGroupDao.pm_risk_check_insert(inserData, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
                }
            }
        } finally {
            threadLocal.remove();
        }
    }

    @Override
    public JSONObject getDoubleRoute(JSONObject jsonParame) {
        //需要返回的数据
        List<List<JSONObject>> resp = new ArrayList<>();
        try{
            threadLocal.set(new HashMap<>());
            //设置方案数量
            jsonParame.put("num", 20);
            //获得当前选中的方案
            List<Integer> cableIds = jsonParame.getJSONArray("cableIds").toJavaList(Integer.class);
            //根据az查询路由方案
            List<JSONObject> jsonPlanList = optViewDao.fiber_opt_link_scheme(jsonParame, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
            //log.info("jsonPlanList:{}", jsonPlanList);
            //查询所有光缆
            List<JSONObject> jsonCableList = optViewDao.fiber_opt_link(jsonParame, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
            //将当前选中的光缆筛选出来
            List<JSONObject> listPlan1 = cableIds.stream().map(cableId -> jsonCableList.stream().filter(jsonCable -> jsonCable.getInteger("cable_id").equals(cableId)).findFirst().orElse(null)).collect(Collectors.toList());
            //log.info("listPlan1:{}",listPlan1);
            //查询所有局站
            List<JSONObject> jsonStationList = optViewDao.fiber_opt_net(jsonParame, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
            //log.info("jsonStationList:{}",jsonStationList);
            //jsonPlanList 按照path_id 进行分组
            List<List<JSONObject>> jsonPlanGroupList = jsonPlanList.stream().collect(Collectors.groupingBy(jsonPlan -> jsonPlan.getInteger("path_id"))).values().stream().collect(Collectors.toList());
            //获得方案1的所有局站列表
            List<JSONObject> jsonStationList1 = queryStationByOpts(listPlan1, jsonStationList);
            //log.info("jsonStationList1:{}",jsonStationList1);
            //遍历每一个方案
            outerLoop:
            for (int i = 0; i < jsonPlanGroupList.size(); i++) {
                List<JSONObject> jsonPlanGroup = jsonPlanGroupList.get(i);
                Integer beginId = null;
                Integer endId = null;
                List<List<JSONObject>> pathList = new ArrayList<>();
                //log.info("jsonPlanGroup:{}", jsonPlanGroup);
                //两个方案不容许出现相同的 除了头尾以外的其他节点
                List<Integer> planNodeList = jsonStationList1.stream().map(jsonNode -> jsonNode.getInteger("seq")).collect(Collectors.toList());
                //去掉开始结束节点
                planNodeList = planNodeList.stream().filter(item-> !Objects.equals(item, jsonParame.getInteger("beginNodeId")) && !Objects.equals(item, jsonParame.getInteger("endNodeId"))).collect(Collectors.toList());
                //获得当前方案的所有节点 并去重复
                List<Integer> jsonNodeList = jsonPlanGroup.stream().map(jsonNode -> jsonNode.getInteger("node")).collect(Collectors.toList());
                jsonNodeList = jsonNodeList.stream().filter(item-> !Objects.equals(item, jsonParame.getInteger("beginNodeId")) && !Objects.equals(item, jsonParame.getInteger("endNodeId"))).collect(Collectors.toList());
                //两个方案的节点不能有重复
                List<Integer> finalPlanNodeList = planNodeList;
                //log.info("planNodeList:{},jsonNodeList:{}", planNodeList, jsonNodeList);
                if (jsonNodeList.stream().anyMatch(finalPlanNodeList::contains)) {
                    continue;
                }

                for (int j = 0; j < jsonPlanGroup.size(); j++) {
                    JSONObject jsonNode = jsonPlanGroup.get(j);
                    if (j > 0) {
                        endId = jsonNode.getInteger("node");
                        //log.info("beginId:{},endId:{}", beginId, endId);
                        List<JSONObject> optLinkList = queryOptLinkByNode(beginId, endId, jsonStationList, jsonCableList);
                        pathList.add(optLinkList);
                    }
                    beginId = endId == null ? jsonNode.getInteger("node") : endId;
                }
                //计算所有的组合方案
                List<List<JSONObject>> combinationList = getCombination(pathList);
                for (int k = 0; k < combinationList.size(); k++) {
                    List<JSONObject> listPlan2 = combinationList.get(k);
                    //打印方案二
                    StringBuilder sb = new StringBuilder();
                    for (int l = 0; l < listPlan2.size(); l++) {
                        JSONObject ttt = listPlan2.get(l);
                        sb.append("【");
                        sb.append(ttt.getString("start_station"));
                        sb.append("-->");
                        sb.append(ttt.getString("end_station"));
                        sb.append(":");
                        sb.append(ttt.getString("code"));
                        sb.append("】  ");
                    }
                    log.info("方案信息:{}", sb);
                    JSONObject result = comparePlanV2(listPlan1, listPlan2, jsonParame.getString("ds"));
                    if (result.getBoolean("isDoubleRoute")) {
                        listPlan2.get(0).put("samePipeSegments", result.getJSONArray("samePipeSegments"));
                        log.info("listPlan2:{}", listPlan2);
                        resp.add(listPlan2);
                    }
                    //满足10条数据 则结束
                    if (resp.size() == 10) {
                        break outerLoop;
                    }
                }
            }
        }catch (Exception e){
            log.error("getDoubleRoute error",e);
        }finally {
            threadLocal.remove();
        }

        JSONObject meta = new JSONObject();
        meta.put("paths", resp);
        return meta;
    }

    @Override
    public JSONObject getOptLinkScheme(JSONObject jsonObject) {
        Integer num = jsonObject.getInteger("num");
        //需要返回的数据
        List<List<JSONObject>> resp = new ArrayList<>();
        //根据az 和数量获得方案
        List<JSONObject> jsonPlanList = optViewDao.fiber_opt_link_scheme(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        log.info("jsonPlanList:{}", jsonPlanList);
        //查询所有光缆
        List<JSONObject> jsonCableList = optViewDao.fiber_opt_link(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        //查询所有局站
        List<JSONObject> jsonStationList = optViewDao.fiber_opt_net(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        //jsonPlanList 按照path_id 进行分组
        List<List<JSONObject>> jsonPlanGroupList = jsonPlanList.stream().collect(Collectors.groupingBy(jsonPlan -> jsonPlan.getInteger("path_id"))).values().stream().collect(Collectors.toList());
        //遍历每一个方案
        outerLoop:
        for (int i = 0; i < jsonPlanGroupList.size(); i++) {
            List<JSONObject> jsonPlanGroup = jsonPlanGroupList.get(i);
            Integer beginId = null;
            Integer endId = null;
            List<List<JSONObject>> pathList = new ArrayList<>();
            for (int j = 0; j < jsonPlanGroup.size(); j++) {
                JSONObject jsonNode = jsonPlanGroup.get(j);
                if (j > 0) {
                    endId = jsonNode.getInteger("node");
                    List<JSONObject> optLinkList = queryOptLinkByNode(beginId, endId, jsonStationList, jsonCableList);
                    pathList.add(optLinkList);
                }
                beginId = endId == null ? jsonNode.getInteger("node") : endId;
            }
            //计算所有的组合方案
            List<List<JSONObject>> combinationList = getCombination(pathList);
            for (int k = 0; k < combinationList.size(); k++) {
                List<JSONObject> listPlan2 = combinationList.get(k);
                resp.add(listPlan2);
                //满足10条数据 则结束
                if (resp.size() == num) {
                    break outerLoop;
                }
            }
        }
        JSONObject meta = new JSONObject();
        meta.put("paths", resp);
        return meta;
    }

    @Override
    public JSONObject checkDoubleRoute(List<String> plan1, List<String> plan2, String ds) {
        JSONObject r = new JSONObject();
        JSONObject jsonParame = new JSONObject();
        jsonParame.put("ds", ds);
        //1、判断 两个集合是否有交叉
        boolean hasCommon = plan1.stream().anyMatch(plan2::contains);
        List<JSONObject> pipeList2 = queryPipeByOptCodes2(plan2, jsonParame);
        r.put("planPipeSegments",pipeList2);
        r.put("cables",plan2.stream().map(item->jsonParame.getJSONObject(item)).collect(Collectors.toList()));
        if (hasCommon) {
            r.put("isDoubleRoute", false);
            return r;
        }
        //2、获得每种方案的 管道数据
        List<JSONObject> pipeList1 = queryPipeByOptCodes2(plan1, jsonParame);

        //查询重复的管道段
        List<JSONObject> duplicatePipeSegments = findDuplicatePipeSegments(pipeList1, pipeList2);
        r.put("samePipeSegments", duplicatePipeSegments);

        //不存在重复的管道段
        if (duplicatePipeSegments.isEmpty()) {
            r.put("isDoubleRoute", true);
            return r;
        }
        //不关心是否有相同的局站，只要管道在两条光缆属于500米10人井
        r.put("isDoubleRoute", otherStation(pipeList1, pipeList2, duplicatePipeSegments));
        return r;
    }

    @Override
    public JSONObject checkDoubleRouteV2(List<String> plan1, List<String> plan2, String ds) {
        JSONObject r = new JSONObject();
        JSONObject jsonParame2 = new JSONObject();
        jsonParame2.put("ds", ds);
        List<JSONObject> pipeList2 = queryPipeByOptCodes2V2(plan2, jsonParame2);
        //1、判断 两个集合是否有交叉
        JSONObject jsonParame = new JSONObject();
        jsonParame.put("ds", ds);
        //2、获得每种方案的 管道数据
        List<JSONObject> pipeList1 = queryPipeByOptCodes2V2(plan1, jsonParame);
        boolean hasCommon = plan1.stream().anyMatch(plan2::contains);
        //放置管道数据
        r.put("planPipeSegments", pipeList2);
        r.put("cables",plan2.stream().map(item->jsonParame2.getJSONObject(item)).collect(Collectors.toList()));
        if (hasCommon) {
            r.put("isDoubleRoute", false);
            r.put("samePipeSegments", findDuplicatePipeSegments(pipeList1, pipeList2));
            return r;
        }


        //判断只是有一端重合
        if (pipeList1.isEmpty() || pipeList2.isEmpty()) {
            r.put("isDoubleRoute", false);
            return r;
        }

        if (!pipeList1.get(0).getString("code").equals(pipeList2.get(0).getString("code")) && pipeList1.get(0).getString("code").equals(pipeList2.get(pipeList2.size() - 1).getString("code"))&&
                pipeList1.get(pipeList1.size() - 1).getString("code").equals(pipeList2.get(0).getString("code")) && !pipeList1.get(pipeList1.size() - 1).getString("code").equals(pipeList2.get(pipeList2.size() - 1).getString("code"))){
            r.put("isDoubleRoute", false);
            return r;
        }
        //查询重复的管道段
        List<JSONObject> duplicatePipeSegments = findDuplicatePipeSegments(pipeList1, pipeList2);
        r.put("samePipeSegments", duplicatePipeSegments);

        //不存在重复的管道段
        if (duplicatePipeSegments.isEmpty()) {
            r.put("isDoubleRoute", true);
            return r;
        }
        //不关心是否有相同的局站，只要管道在两条光缆属于500米10人井
        r.put("isDoubleRoute", otherStation(pipeList1, pipeList2, duplicatePipeSegments));
        return r;
    }

    @Override
    public JSONObject queryOptLinkInfo(List<String> plan, String ds) {
        JSONObject r = new JSONObject();
        JSONObject jsonParame = new JSONObject();
        jsonParame.put("ds", ds);
        List<JSONObject> pipeList = queryPipeByOptCodes2V2(plan, jsonParame);
        r.put("cables",plan.stream().map(item->jsonParame.getJSONObject(item)).collect(Collectors.toList()));
        return r;
    }

    /**
     * 通过光缆集合查询 排好续的管道集合
     * @param plan1
     * @param jsonParame
     * @return
     */
    private List<JSONObject> queryPipeByOptCodes2(List<String> plan1, JSONObject jsonParame) {
        List<JSONObject> pipeList = new ArrayList<>();
        //进行缓存查找
        Map<String, Object> threadLocalMap = threadLocal.get();
        List<String> waitQueryList = new ArrayList<>();
        for (int i = 0; i < plan1.size(); i++) {
            String optCode = plan1.get(i);
            if (threadLocalMap.containsKey(optCode)) {
                JSONObject jsonObject = (JSONObject) threadLocalMap.get(optCode);
                pipeList.addAll(jsonObject.getJSONArray("pipe_segments").toJavaList(JSONObject.class));
                jsonParame.put(optCode, jsonObject);
            } else {
                waitQueryList.add(optCode);
            }
        }
        if (!waitQueryList.isEmpty()) {
            List<JSONObject> optList = queryPipeByOptCodes(waitQueryList, jsonParame);
            for (int i = 0; i < optList.size(); i++) {
                JSONObject jsonObject = optList.get(i);
                JSONObject save = new JSONObject();
                log.info("jsonObject:{}", jsonObject);
                List<JSONObject> ps = new ArrayList<>();
                if (jsonObject.getJSONArray("pipe_segments")!=null){
                    ps = jsonObject.getJSONArray("pipe_segments").toJavaList(JSONObject.class);
                    pipeList.addAll(ps);
                }
                save.put("pipe_segments",ps);
                save.put("name",jsonObject.getString("name"));
                save.put("code",jsonObject.getString("code"));
                save.put("length",calculatePipeLength(ps));
                jsonParame.put(jsonObject.getString("code"), save);
                threadLocalMap.put(jsonObject.getString("code"), save);
            }
        }
        pipeList = sortPipeList2(pipeList);
        return pipeList;
    }

    /**
     * 通过光缆集合查询 排好续的管道集合(v2)
     * @param plan1
     * @param jsonParame
     * @return
     */
    private List<JSONObject> queryPipeByOptCodes2V2(List<String> plan1, JSONObject jsonParame) {
        List<JSONObject> pipeList = new ArrayList<>();
        //进行缓存查找
        Map<String, Object> threadLocalMap = threadLocal.get();
        List<String> waitQueryList = new ArrayList<>();
        for (int i = 0; i < plan1.size(); i++) {
            String optCode = plan1.get(i);
            if (threadLocalMap.containsKey(optCode)) {
                JSONObject jsonObject = (JSONObject) threadLocalMap.get(optCode);
                pipeList.addAll(jsonObject.getJSONArray("pipe_segments").toJavaList(JSONObject.class));
                jsonParame.put(optCode, jsonObject);
            } else {
                waitQueryList.add(optCode);
            }
        }
        if (!waitQueryList.isEmpty()) {
            List<JSONObject> optList = queryPipeByOptCodeV2(waitQueryList, jsonParame);
            for (int i = 0; i < optList.size(); i++) {
                JSONObject jsonObject = optList.get(i);
                JSONObject save = new JSONObject();
                log.info("jsonObject:{}", jsonObject);
                List<JSONObject> ps = new ArrayList<>();
                if (jsonObject.getJSONArray("pipe_segments")!=null){
                    ps = jsonObject.getJSONArray("pipe_segments").toJavaList(JSONObject.class);
                    pipeList.addAll(ps);
                }
                save.put("pipe_segments",ps);
                save.put("name",jsonObject.getString("name"));
                save.put("code",jsonObject.getString("code"));
                save.put("length",calculatePipeLength(ps));
                jsonParame.put(jsonObject.getString("code"), save);
                threadLocalMap.put(jsonObject.getString("code"), save);
            }
        }
        pipeList = sortPipeList2(pipeList);
        return pipeList;
    }

    /**
     * 根据两个节点查询所有的光缆
     */
    private List<JSONObject> queryOptLinkByNode(Integer nodeId1, Integer nodeId2, List<JSONObject> jsonStationList, List<JSONObject> jsonCableList) {
        //根据节点id查询节点对象
        JSONObject node1 = jsonStationList.stream().filter(station -> station.getInteger("seq").equals(nodeId1)).findFirst().orElse(null);
        JSONObject node2 = jsonStationList.stream().filter(station -> station.getInteger("seq").equals(nodeId2)).findFirst().orElse(null);
        //根据局站名字查找光缆
        List<JSONObject> optLinkList = jsonCableList.stream().filter(optLink -> {
            String startStation = optLink.getString("start_station");
            String endStation = optLink.getString("end_station");
            return (startStation.equals(node1.getString("name")) && endStation.equals(node2.getString("name"))) ||
                    (startStation.equals(node2.getString("name")) && endStation.equals(node1.getString("name")));
        }).collect(Collectors.toList());
        return optLinkList;
    }

    /**
     * 将二维【【1，2】，【3，4】，【6，7】...】 按照每一位进行组合获得所有的组合 例如：【1，3，6】，【1，3，7】，【1，4，6】，【1，4，7】，【2，3，6】，【2，3，7】，【2，4，6】，【2，4，7】
     */
    private List<List<JSONObject>> getCombination(List<List<JSONObject>> list) {
        List<List<JSONObject>> result = new ArrayList<>();
        if (list.isEmpty()) {
            return result;
        }

        // 初始化结果列表，首先保存第一个数组的所有元素作为组合的基础
        for (JSONObject i : list.get(0)) {
            List<JSONObject> initial = new ArrayList<>();
            initial.add(i);
            result.add(initial);
        }

        // 迭代处理后续的数组
        for (int i = 1; i < list.size(); i++) {
            List<List<JSONObject>> newResult = new ArrayList<>();
            for (List<JSONObject> combination : result) {
                for (JSONObject j : list.get(i)) {
                    List<JSONObject> newCombination = new ArrayList<>(combination);
                    newCombination.add(j);
                    newResult.add(newCombination);
                }
            }
            result = newResult;
        }
        return result;
    }

    /**
     * 比较两个方案是否符合双路由
     */
    private JSONObject comparePlan(List<JSONObject> listPlan1, List<JSONObject> listPlan2, String ds) {
        JSONObject r = new JSONObject();
        //1、遍历两个集合是否有重复的光缆段
        //2、查询数据库是否符合双路由规则
        List<List<JSONObject>> listList = new ArrayList<>();
        listList.add(listPlan1);
        listList.add(listPlan2);
        List<List<JSONObject>> getCombination = getCombination(listList);
        List<List<JSONObject>> t = getCombination.stream().filter(item -> item.get(0).getInteger("cable_id").equals(item.get(1).getInteger("cable_id"))).collect(Collectors.toList());
        //存在重复的光缆段
        if (!t.isEmpty()) {
            r.put("isDoubleRoute", false);
            return r;
        }
        //获得两条光缆的ID组合
        List<Long> optCodes = getCombination.stream().map(item -> {
            Integer linkId1 = item.get(0).getInteger("cable_id");
            Integer linkId2 = item.get(1).getInteger("cable_id");
            //按照从小到大拼接
            String linkId = linkId1 < linkId2 ? fillZero(linkId1, 9) + fillZero(linkId2, 9) : fillZero(linkId2, 9) + fillZero(linkId1, 9);
            return Long.valueOf(linkId);
        }).collect(Collectors.toList());
        JSONObject queryParam = new JSONObject();
        queryParam.put("objectIds", optCodes);
        queryParam.put("objectType", "fiberCable");
        List<JSONObject> compareResult = optViewDao.fiber_opt_net_double_route(queryParam, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
        compareResult = compareResult == null ? new ArrayList<>() : compareResult;
        //数据库数据部分数据不存在 则该方案不符合双路由规则
        if (compareResult.size() != optCodes.size()) {
            log.info("数据库数据部分数据不存在 则该方案不符合双路由规则");
            r.put("isDoubleRoute", false);
            return r;
        }
        //数据库数据部分数据不符合双路由规则 则该方案不符合双路由规则
        List<JSONObject> errorList = compareResult.stream().filter(item -> !item.getString("check_result").equals("正常")).collect(Collectors.toList());
        if (!errorList.isEmpty()) {
            log.info("数据库数据部分数据不符合双路由规则 则该方案不符合双路由规则");
            r.put("isDoubleRoute", false);
            return r;
        }
        r.put("isDoubleRoute", true);
        r.put("samePipeSegments", handleSamePipeSements(compareResult));
        return r;
    }

    /**
     * 新算法技术两个路由集合是否符合双路由
     * @param listPlan1
     * @param listPlan2
     * @param ds
     * @return
     */
    private JSONObject comparePlanV2(List<JSONObject> listPlan1, List<JSONObject> listPlan2, String ds) {
        //1、遍历两个集合是否有重复的光缆段
        //2、查询数据库是否符合双路由规则
        List<String> plan1 = listPlan1.stream().map(item -> item.getString("code")).collect(Collectors.toList());
        List<String> plan2 = listPlan2.stream().map(item -> item.getString("code")).collect(Collectors.toList());
        return checkDoubleRouteV2(plan1, plan2, ds);
    }

    /**
     * 对相关管道数据进行处理 获得人＃名称
     * @param compareResult
     * {"samePipeSegments":[{"code":"NJ/250QH.ZD37*-250QH.YL-8*","gid":"pipe_segment$$321251130000000000151701","a_code":"250QH.ZD37*","a_pos_y":"32.042986","a_spec_id":1010100003,"z_code":"250QH.YL-8*","z_name":"延龄-8*","z_spec_id":1010100003,"spec_id":1111000001,"name":"NJ/中东37*-延龄-8*","a_pos_x":"118.788020","a_facility_id":"321251070000000000159965","z_facility_id":"321251070000000000217667","z_pos_y":"32.042700","id":"321251130000000000151701","z_pos_x":"118.787854","a_name":"中东37*","label_name":"管道段","label_code":"pipe_segment"},{"code":"延龄5#-局前2","gid":"pipe_segment$$321251130000000000200167","a_code":"局前2","a_pos_y":"32.041301","a_spec_id":1010100003,"z_code":"延龄5#","z_name":"延龄5#","z_spec_id":1010100003,"spec_id":1111000001,"name":"延龄5#-局前2","a_pos_x":"118.787585","a_facility_id":"321251070000000000221465","z_facility_id":"321251070000000000246954","z_pos_y":"32.041407","id":"321251130000000000200167","z_pos_x":"118.787208","a_name":"局前2","label_name":"管道段","label_code":"pipe_segment"}],"count_samePipeSegments":2}
     */
    private List<String> handleSamePipeSements(List<JSONObject> compareResult){
        List<String> r = new ArrayList<>();
        for (JSONObject item : compareResult) {
            String snapshotStr = item.getString("snapshot");
            JSONObject snapshot = JSONObject.parseObject(snapshotStr, JSONObject.class);
            //获得所有的的a_name 和z_name 的数组 然后去重复
            List<String> allName = new ArrayList<>();
            List<JSONObject> samePipeSegments = snapshot.getJSONArray("samePipeSegments").toJavaList(JSONObject.class);
            for (JSONObject pipeSegment : samePipeSegments) {
                allName.add(pipeSegment.getString("a_name"));
                allName.add(pipeSegment.getString("z_name"));
            }
            r.addAll(allName.stream().distinct().collect(Collectors.toList()));
        }
        return r.stream().distinct().collect(Collectors.toList());
    }


    /**
     * 对比两个光缆段是否符合双路由规则
     */
    private JSONObject compareOptLink(JSONObject optLink1, JSONObject optLink2, JSONObject jsonParame) {
        JSONObject r = new JSONObject();
        //根据光缆段id查询管道数据
        List<JSONObject> pipesList1 = findPipeListByOptLink(optLink1, jsonParame);
        List<JSONObject> pipesList2 = findPipeListByOptLink(optLink2, jsonParame);
        //查询重复的管道段
        List<JSONObject> duplicatePipeSegments = findDuplicatePipeSegments(pipesList1, pipesList2);
        r.put("samePipeSegments", duplicatePipeSegments);
        //不存在重复的管道段
        if (duplicatePipeSegments.isEmpty()) {
            r.put("isDoubleRoute", true);
            return r;
        }
        //不关心是否有相同的局站，只要管道在两条光缆属于500米10人井
        r.put("isDoubleRoute", otherStation(pipesList1, pipesList2, duplicatePipeSegments));
        //1、头尾局站都一样
        //2、只有一个重复的管道段
        //3、没有相同的局站
        //List<String> station1 = Arrays.asList(optLink1.getString("start_station"), optLink1.getString("end_station"));
        //List<String> station2 = Arrays.asList(optLink2.getString("start_station"), optLink2.getString("end_station"));
        //计算两个集合的交集
        //Set<String> commonStations = station1.stream().filter(station2::contains).collect(Collectors.toSet());
        //station1 和 station2无重复 表示头尾不重叠
        //if (commonStations.isEmpty()) {
        //    r.put("isDoubleRoute", false);
        //    return r;
        //}
        //有2个交集
        //if (commonStations.size() == 2) {
        //    r.put("isDoubleRoute", isSameStation(pipesList1, pipesList2, duplicatePipeSegments));
        //    return r;
        // }
        //有1个交集
        //if (commonStations.size() == 1) {
        //   r.put("isDoubleRoute", isOneCommonStation(optLink1, optLink2, commonStations.iterator().next(), pipesList1, pipesList2, duplicatePipeSegments));
        //}
        return r;
    }

    /**
     * 通过光缆对象，来查下管道数据 并进行排序
     */
    private List<JSONObject> findPipeListByOptLink(JSONObject optLink, JSONObject jsonParame) {
        //先通过本地缓存来查找 如果没有则查询数据库
        Map<String, Object> localCache = threadLocal.get();
        if (localCache.containsKey(optLink.getString("code"))) {
            return (List<JSONObject>) localCache.get(optLink.getString("code"));
        }
        List<String> optCodes = Arrays.asList(optLink.getString("code"));
        //查询管道数据
        List<JSONObject> optList = queryPipeByOptCodes(optCodes, jsonParame);
        List<JSONObject> pipesList = optList == null || optList.isEmpty() ||  optList.get(0).getJSONArray("pipe_segments")==null? new ArrayList<>() : optList.get(0).getJSONArray("pipe_segments").toJavaList(JSONObject.class);
        pipesList = sortPipeList2(pipesList);
        localCache.put(optLink.getString("code"), pipesList);
        return pipesList;
    }

    /**
     * 处理 头尾相同的场景
     * param pipesList1 管道
     * param pipesList2 管道
     * param duplicatePipeSegments 重复的管道段
     */
    private boolean isSameStation(List<JSONObject> pipesList1, List<JSONObject> pipesList2, List<JSONObject> duplicatePipeSegments) {
        for (JSONObject duplicatePipe : duplicatePipeSegments) {
            //正向找
            boolean forward = isPipeSegmentValid(pipesList1, true, duplicatePipe);
            //反向找
            boolean opposite = isPipeSegmentValid(pipesList1, false, duplicatePipe);
            if (!forward && !opposite) {
                return false;
            }
            forward = isPipeSegmentValid(pipesList2, true, duplicatePipe);
            opposite = isPipeSegmentValid(pipesList2, false, duplicatePipe);
            if (!forward && !opposite) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理 不关心公共点，只要管道在两条光缆属于500米10人井
     * param pipesList1 管道
     * param pipesList2 管道
     * param duplicatePipeSegments 重复的管道段
     */
    private boolean otherStation(List<JSONObject> pipesList1, List<JSONObject> pipesList2, List<JSONObject> duplicatePipeSegments) {
        for (JSONObject duplicatePipe : duplicatePipeSegments) {
            boolean r1 = isPipeSegmentValid(pipesList1, true, duplicatePipe) || isPipeSegmentValid(pipesList1, false, duplicatePipe);
            boolean r2 = isPipeSegmentValid(pipesList2, true, duplicatePipe) || isPipeSegmentValid(pipesList2, false, duplicatePipe);
            if (!r1 || !r2) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理 有一个公共点
     * param optLink1 光缆段1
     * param optLink2 光缆段2
     * param same 公共点
     * param pipesList1 管道
     * param pipesList2 管道
     * param duplicatePipeSegments 重复的管道段
     */
    private boolean isOneCommonStation(JSONObject optLink1, JSONObject optLink2, String same, List<JSONObject> pipesList1, List<JSONObject> pipesList2, List<JSONObject> duplicatePipeSegments) {
        Boolean isBegin1 = optLink1.getString("start_station").equals(same);
        Boolean isBegin2 = optLink2.getString("start_station").equals(same);
        for (JSONObject duplicatePipe : duplicatePipeSegments) {
            boolean r1 = isPipeSegmentValid(pipesList1, isBegin1, duplicatePipe);
            if (!r1) {
                return false;
            }
            boolean r2 = isPipeSegmentValid(pipesList2, isBegin2, duplicatePipe);
            if (!r2) {
                return false;
            }
        }
        return true;
    }

    /**
     * 查询两个管道列表 重复的管道段
     * param pipeList1 管道
     * param pipeList2 管道
     */
    private List<JSONObject> findDuplicatePipeSegments(List<JSONObject> pipesList1, List<JSONObject> pipesList2) {
        List<JSONObject> result = new ArrayList<>();
        for (JSONObject pipe1 : pipesList1) {
            for (JSONObject pipe2 : pipesList2) {
                if (pipe1.getString("code").equals(pipe2.getString("code"))) {
                    pipe1.put("length", calculatePipeLength(pipe1));
                    result.add(pipe1);
                }
            }
        }
        return result;
    }

    /**
     * 将int数据补全到指定位数 首位位1：中间位为0：末尾为num
     */
    private String fillZero(int num, int length) {
        // 将末尾的数字a转换为字符串
        String aStr = String.valueOf(num);
        // 计算中间0的数量
        int zeroCount = length - 1 - aStr.length();
        // 创建StringBuilder并构建字符串
        StringBuilder result = new StringBuilder();
        result.append('1'); // 首位是1
        if (zeroCount > 0) {
            for (int i = 0; i < zeroCount; i++) {
                result.append('0'); // 中间补0
            }
        }
        result.append(aStr); // 末尾是a
        return result.toString();
    }

    /**
     * 通过管道列表计算总长度
     */
    private double calculatePipeLength(List<JSONObject> pipeList) {
        //计算总长度
        BigDecimal totalLength = new BigDecimal(0);
        for (JSONObject pipe : pipeList) {
            //计算管道段的长度
            double pipeLength = calculatePipeLength(pipe);
            totalLength = totalLength.add(new BigDecimal(pipeLength));
        }
        //保留两位小数
        totalLength = totalLength.setScale(2, RoundingMode.HALF_UP);
        return totalLength.doubleValue();
    }

    /**
     * 根据光缆编号列表 批量查询管道数据
     */
    private List<JSONObject> queryPipeByOptCodes(List<String> optCodes, JSONObject jsonParame) {
        String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds"));
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_cable_pipe").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cable", "codes",
                        optCodes)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);
        graphService.buildTree(data, "光缆", "管道段", "pipe_segments");
        List<JSONObject> cableList = graphService.filterNodeByLabel(data, "光缆");
        return cableList;
    }

    /**
     * 根据光缆编号列表 批量查询管道数据（v2）
     * @param optCodes
     * @param jsonParame
     * @return
     */
    private List<JSONObject> queryPipeByOptCodeV2(List<String> optCodes, JSONObject jsonParame){
        JSONObject query = new JSONObject();
        query.put("codes", optCodes);
        List<JSONObject> jsonObjectList = optViewDao.fiber_opt_net_pipe(query, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
        log.info("queryPipeByOptCodeV2:jsonObjectList:{}",jsonObjectList);
        if (jsonObjectList == null || jsonObjectList.isEmpty()) {
            log.warn("没有查询到管道数据:{}",query);
            jsonObjectList = new ArrayList<>();
        }
        //按照 cable_code进行分组
        Map<String,List<JSONObject>> group = jsonObjectList.stream().collect(Collectors.groupingBy(item->item.getString("cable_code")));
        List<JSONObject> result = new ArrayList<>();
        for (int i = 0; i < optCodes.size(); i++) {
            String optCode = optCodes.get(i);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code",optCode);
            jsonObject.put("name","");
            jsonObject.put("pipe_segments",group.get(optCode)==null?new ArrayList<>():group.get(optCode));
            result.add(jsonObject);
        }
        return result;
    }

    /**
     * 计算 当前管道段 离站点是否满足500米 并且10个人＃
     *
     * @Param pipeList 管道列表
     * @Param isBegin 是否是起始站点
     * @Param pipe 管道段
     */
    private boolean isPipeSegmentValid(List<JSONObject> pipeList, Boolean isBegin, JSONObject pipe) {
        //计算管道段离站点的距离
        //根据 isBegin 选择正向或者反向遍历pipeList
        //对 pipeList 进行反转
        if (!isBegin) {
            Collections.reverse(pipeList);
        }
        //计算总长度
        BigDecimal totalLength = new BigDecimal(0);
        for (int i = 0; i < pipeList.size(); i++) {
            JSONObject pipeSegment = pipeList.get(i);
            //计算管道段的长度
            double pipeLength = calculatePipeLength(pipeSegment);
            totalLength = totalLength.add(new BigDecimal(pipeLength));
            //超过500米
            if (totalLength.compareTo(new BigDecimal(500)) > 0) {
                return false;
            }
            //超过10个＃(一个管道段 头尾各有一个＃)
            if (i + 1 + 1 > 10) {
                return false;
            }
            if (pipeSegment.getString("code").equals(pipe.getString("code"))) {
                return true;
            }
        }
        return true;
    }

    /**
     * 根据经纬度坐标 计算管道的长度
     */
    private double calculatePipeLength(JSONObject pipeSegment) {
        Double a_pos_x = pipeSegment.getDouble("a_pos_x");
        Double a_pos_y = pipeSegment.getDouble("a_pos_y");
        Double z_pos_x = pipeSegment.getDouble("z_pos_x");
        Double z_pos_y = pipeSegment.getDouble("z_pos_y");
        return gisService.haversine(a_pos_y, a_pos_x, z_pos_y, z_pos_x);
    }

    /**
     * 根据管道段列表进行排序
     */
    private List<JSONObject> sortPipeList(List<JSONObject> pipeList) {
        /**
         * {
         *   "code": "250NQ-集庆路(南)#7-集庆路(南)#8",
         *   "gid": "pipe_segment$$321251130000000000102439",
         *   "a_code": "集庆路(南)#7",
         *   "a_pos_y": "32.020649",
         *   "a_spec_id": 1010100003,
         *   "z_code": "集庆路(南)#8",
         *   "z_name": "集庆路(南)#8",
         *   "z_spec_id": 1010100003,
         *   "spec_id": 1111000001,
         *   "name": "250NQ-集庆路(南)#7-集庆路(南)#8",
         *   "a_pos_x": "118.772382",
         *   "a_facility_id": "321251070000000000161390",
         *   "z_facility_id": "321251070000000000170022",
         *   "z_pos_y": "32.020530",
         *   "id": "321251130000000000102439",
         *   "z_pos_x": "118.771755",
         *   "a_name": "集庆路(南)#7",
         *   "label_name": "管道段",
         *   "label_code": "pipe_segment"
         * }
         */
        List<JSONObject> result = new ArrayList<>();
        JSONObject currentPipeSegment = pipeList.get(0);
        result.add(currentPipeSegment);
        String x = currentPipeSegment.getString("z_pos_x");
        String y = currentPipeSegment.getString("z_pos_y");
        List<JSONObject> newPipeList = pipeList;
        //向后找
        while (true) {
            JSONObject obj = currentPipeSegment;
            //移除当前的管道段
            newPipeList = newPipeList.stream().filter(item -> !item.getString("id").equals(obj.getString("id"))).collect(Collectors.toList());
            Object[] pipeSegmentByXY = findPipeSegmentByXY(newPipeList, x, y, 2);
            if (pipeSegmentByXY == null) {
                break;
            }
            String aOrZ = (String) pipeSegmentByXY[1];
            if (aOrZ.equals("a")) {
                x = ((JSONObject) pipeSegmentByXY[0]).getString("z_pos_x");
                y = ((JSONObject) pipeSegmentByXY[0]).getString("z_pos_y");
            } else {
                x = ((JSONObject) pipeSegmentByXY[0]).getString("a_pos_x");
                y = ((JSONObject) pipeSegmentByXY[0]).getString("a_pos_y");
            }
            currentPipeSegment = (JSONObject) pipeSegmentByXY[0];
            result.add(currentPipeSegment);
        }
        //向前找
        currentPipeSegment = pipeList.get(0);
        x = currentPipeSegment.getString("a_pos_x");
        y = currentPipeSegment.getString("a_pos_y");
        while (true) {
            JSONObject obj = currentPipeSegment;
            //移除当前的管道段
            newPipeList = newPipeList.stream().filter(item -> !item.getString("id").equals(obj.getString("id"))).collect(Collectors.toList());
            Object[] pipeSegmentByXY = findPipeSegmentByXY(newPipeList, x, y, 2);
            if (pipeSegmentByXY == null) {
                break;
            }
            String aOrZ = (String) pipeSegmentByXY[1];
            if (aOrZ.equals("a")) {
                x = ((JSONObject) pipeSegmentByXY[0]).getString("z_pos_x");
                y = ((JSONObject) pipeSegmentByXY[0]).getString("z_pos_y");
            } else {
                x = ((JSONObject) pipeSegmentByXY[0]).getString("a_pos_x");
                y = ((JSONObject) pipeSegmentByXY[0]).getString("a_pos_y");
            }
            currentPipeSegment = (JSONObject) pipeSegmentByXY[0];
            result.add(0, currentPipeSegment);
        }
        return result;
    }

    /**
     * 对管道段排序 当出现首位不连情况将采用 最近距离策略来连接
     */
    private List<JSONObject> sortPipeList2(List<JSONObject> pipeList) {
        if (pipeList == null || pipeList.isEmpty()) {
            return pipeList;
        }
        handleRepairXY(pipeList);
        List<JSONObject> result = new ArrayList<>();
        JSONObject currentPipeSegment = pipeList.get(0);
        result.add(currentPipeSegment);
        List<JSONObject> newPipeList = pipeList;
        String[] aPoint = new String[]{currentPipeSegment.getString("a_pos_x"), currentPipeSegment.getString("a_pos_y")};
        String[] zPoint = new String[]{currentPipeSegment.getString("z_pos_x"), currentPipeSegment.getString("z_pos_y")};
        while (!newPipeList.isEmpty()) {
            JSONObject obj = currentPipeSegment;
            newPipeList = newPipeList.stream().filter(item -> !item.getString("code").equals(obj.getString("code"))).collect(Collectors.toList());
            //先通过坐标 向后找
            Object[] pipeSegmentByXY = findPipeSegmentByXY(newPipeList, zPoint[0], zPoint[1], 2);
            //向后找到了
            if (pipeSegmentByXY != null) {
                String aOrZ = (String) pipeSegmentByXY[1];
                if (aOrZ.equals("a")) {
                    zPoint[0] = ((JSONObject) pipeSegmentByXY[0]).getString("z_pos_x");
                    zPoint[1] = ((JSONObject) pipeSegmentByXY[0]).getString("z_pos_y");
                } else {
                    zPoint[0] = ((JSONObject) pipeSegmentByXY[0]).getString("a_pos_x");
                    zPoint[1] = ((JSONObject) pipeSegmentByXY[0]).getString("a_pos_y");
                }
                currentPipeSegment = (JSONObject) pipeSegmentByXY[0];
                result.add(currentPipeSegment);
                continue;
            }
            //向后找不到 则向前找
            pipeSegmentByXY = findPipeSegmentByXY(newPipeList, aPoint[0], aPoint[1], 2);
            //向前找到了
            if (pipeSegmentByXY != null) {
                String aOrZ = (String) pipeSegmentByXY[1];
                if (aOrZ.equals("a")) {
                    aPoint[0] = ((JSONObject) pipeSegmentByXY[0]).getString("z_pos_x");
                    aPoint[1] = ((JSONObject) pipeSegmentByXY[0]).getString("z_pos_y");
                } else {
                    aPoint[0] = ((JSONObject) pipeSegmentByXY[0]).getString("a_pos_x");
                    aPoint[1] = ((JSONObject) pipeSegmentByXY[0]).getString("a_pos_y");
                }
                currentPipeSegment = (JSONObject) pipeSegmentByXY[0];
                result.add(0, currentPipeSegment);
                continue;
            }
            if (newPipeList.isEmpty()) {
                break;
            }
            //向后向前都找不到了
            //向后找最近的管道段
            Object[] zNearestPipeSegment = findNearestPipeSegment(newPipeList, zPoint[0], zPoint[1]);
            //向前找最近的管道段
            Object[] aNearestPipeSegment = findNearestPipeSegment(newPipeList, aPoint[0], aPoint[1]);
            //判断哪个更近
            double zDistance = (double) zNearestPipeSegment[2];
            double aDistance = (double) aNearestPipeSegment[2];
            if (zDistance < aDistance) {
                String aOrZ = (String) zNearestPipeSegment[1];
                if (aOrZ.equals("a")) {
                    zPoint[0] = ((JSONObject) zNearestPipeSegment[0]).getString("z_pos_x");
                    zPoint[1] = ((JSONObject) zNearestPipeSegment[0]).getString("z_pos_y");
                } else {
                    zPoint[0] = ((JSONObject) zNearestPipeSegment[0]).getString("a_pos_x");
                    zPoint[1] = ((JSONObject) zNearestPipeSegment[0]).getString("a_pos_y");
                }
                currentPipeSegment = (JSONObject) zNearestPipeSegment[0];
                result.add(currentPipeSegment);
            } else {
                String aOrZ = (String) aNearestPipeSegment[1];
                if (aOrZ.equals("a")) {
                    aPoint[0] = ((JSONObject) aNearestPipeSegment[0]).getString("z_pos_x");
                    aPoint[1] = ((JSONObject) aNearestPipeSegment[0]).getString("z_pos_y");
                } else {
                    aPoint[0] = ((JSONObject) aNearestPipeSegment[0]).getString("a_pos_x");
                    aPoint[1] = ((JSONObject) aNearestPipeSegment[0]).getString("a_pos_y");
                }
                currentPipeSegment = (JSONObject) aNearestPipeSegment[0];
                result.add(0, currentPipeSegment);
            }

        }
        return result;
    }

    /**
     * 数据补充，当a或者z不存在时，对数据进行补充成一样的数据
     */
    private void handleRepairXY(List<JSONObject> pipeList){
        handleRemoveXY(pipeList);
        for (JSONObject pipeSegment : pipeList) {
            String a_pos_x = pipeSegment.getString("a_pos_x");
            String a_pos_y = pipeSegment.getString("a_pos_y");
            String z_pos_x = pipeSegment.getString("z_pos_x");
            String z_pos_y = pipeSegment.getString("z_pos_y");
            if (a_pos_x == null || a_pos_y == null) {
                pipeSegment.put("a_pos_x", z_pos_x);
                pipeSegment.put("a_pos_y", z_pos_y);
            }
            if (z_pos_x == null || z_pos_y == null) {
                pipeSegment.put("z_pos_x", a_pos_x);
                pipeSegment.put("z_pos_y", a_pos_y);
            }
        }
    }
    /**
     * 数据删除，当a或者z不存在时，移除List<JSONObject>中的数据
     */
    private void handleRemoveXY(List<JSONObject> pipeList) {
        Iterator<JSONObject> iterator = pipeList.iterator();
        while (iterator.hasNext()) {
            JSONObject pipeSegment = iterator.next();
            String a_pos_x = pipeSegment.getString("a_pos_x");
            String a_pos_y = pipeSegment.getString("a_pos_y");
            String z_pos_x = pipeSegment.getString("z_pos_x");
            String z_pos_y = pipeSegment.getString("z_pos_y");
            if (a_pos_x == null || a_pos_y == null || z_pos_x == null || z_pos_y == null) {
                iterator.remove();
            }
        }
    }

    /**
     * 根据xy 查找管道段
     * param pipeList 管道段列表
     * param x x坐标
     * param y y坐标
     * param type 类型 0:头 1:尾,2:头尾
     */
    private Object[] findPipeSegmentByXY(List<JSONObject> pipeList, String x, String y, Integer type) {
        for (JSONObject pipeSegment : pipeList) {
            String a_pos_x = pipeSegment.getString("a_pos_x");
            String a_pos_y = pipeSegment.getString("a_pos_y");
            String z_pos_x = pipeSegment.getString("z_pos_x");
            String z_pos_y = pipeSegment.getString("z_pos_y");
            if (type == 0) {
                if (a_pos_x.equals(x) && a_pos_y.equals(y)) {
                    return new Object[]{pipeSegment, "a"};
                }
            }
            if (type == 1) {
                if (z_pos_x.equals(x) && z_pos_y.equals(y)) {
                    return new Object[]{pipeSegment, "z"};
                }
            }
            if (type == 2) {
                if (a_pos_x.equals(x) && a_pos_y.equals(y)) {
                    return new Object[]{pipeSegment, "a"};
                }
                if (z_pos_x.equals(x) && z_pos_y.equals(y)) {
                    return new Object[]{pipeSegment, "z"};
                }
            }
        }
        return null;
    }

    /**
     * 根据 xy 对比管道 查找最近的管道段
     * return [],0:管道段,1:管道段的a或者z,2:距离
     */
    private Object[] findNearestPipeSegment(List<JSONObject> pipeList, String x, String y) {
        double minDistance = Double.MAX_VALUE;
        JSONObject nearestPipeSegment;
        Object[] result = new Object[3];
        for (JSONObject pipeSegment : pipeList) {
            String a_pos_x = pipeSegment.getString("a_pos_x");
            String a_pos_y = pipeSegment.getString("a_pos_y");
            String z_pos_x = pipeSegment.getString("z_pos_x");
            String z_pos_y = pipeSegment.getString("z_pos_y");
            double adistance = gisService.haversine(Double.parseDouble(a_pos_y), Double.parseDouble(a_pos_x), Double.parseDouble(y), Double.parseDouble(x));
            double zdistance = gisService.haversine(Double.parseDouble(z_pos_y), Double.parseDouble(z_pos_x), Double.parseDouble(y), Double.parseDouble(x));
            //获得 a,z 两个点到当前点的最小距离
            double distance = Math.min(adistance, zdistance);
            if (distance < minDistance) {
                minDistance = distance;
                nearestPipeSegment = pipeSegment;
                result[0] = nearestPipeSegment;
                result[1] = adistance < zdistance ? "a" : "z";
                result[2] = minDistance;
            }
        }
        return result;
    }
    /**
     * 根据光缆列表查询所关联的局站列表
     */
    private List<JSONObject> queryStationByOpts(List<JSONObject> optLinks , List<JSONObject> jsonStationList) {
        //遍历optLinks
        List<JSONObject> outStationList = new ArrayList<>();
        for (JSONObject optLink : optLinks) {
            String startStationCode = optLink.getString("start_station_code");
            String endStationCode = optLink.getString("end_station_code");
            List<JSONObject> inStationList = jsonStationList.stream().filter(station -> station.getString("no").equals(startStationCode) || station.getString("no").equals(endStationCode)).collect(Collectors.toList());
            //添加到 输出中并去掉重复
            outStationList.addAll(inStationList);
        }
        return outStationList.stream().distinct().collect(Collectors.toList());
    }


}
