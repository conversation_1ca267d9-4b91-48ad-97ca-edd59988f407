package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.SplitterDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.SplitterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SplitterServiceImpl implements SplitterService {

    @Autowired
    SplitterDao splitterDao;

    @Override
    public JSONObject getDeviceStatistics(JSONObject queryParam) {
        JSONObject result = new JSONObject();
        
        try {
            String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(
                ObjectUtil.isEmpty(queryParam.getString("ds")) ? "南京" : queryParam.getString("ds")
            );
            
            // 获取总数统计（这里复用现有的统计接口）
            Integer totalCount = splitterDao.querySplitterDeviceCount(queryParam, shardingCode);
            result.put("totalDevices", totalCount != null ? totalCount : 0);
            
            // 计算各状态的设备数量（简化处理，实际应该根据实占率范围统计）
            result.put("normalDevices", 0);    // 正常设备（实占率 < 70%）
            result.put("attentionDevices", 0); // 注意设备（实占率 70-95%）
            result.put("alarmDevices", 0);     // 告警设备（实占率 > 95%）
            
            // 最近更新时间
            result.put("lastUpdateTime", LocalDateTime.now().format(
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            ));
            
        } catch (Exception e) {
            log.error("getDeviceStatistics error", e);
            result.put("totalDevices", 0);
            result.put("normalDevices", 0);
            result.put("attentionDevices", 0);
            result.put("alarmDevices", 0);
            result.put("lastUpdateTime", LocalDateTime.now().format(
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            ));
        }
        
        return result;
    }

    @Override
    public List<JSONObject> convertToDeviceDataList(List<JSONObject> apiDataList) {
        if (ObjectUtil.isEmpty(apiDataList)) {
            return new ArrayList<>();
        }
        
        return apiDataList.stream()
            .map(this::convertSingleDeviceData)
            .collect(Collectors.toList());
    }

    /**
     * 转换单个设备数据
     */
    private JSONObject convertSingleDeviceData(JSONObject apiData) {
        JSONObject deviceData = new JSONObject();
        
        try {
            // 字段映射：API字段 -> 前端字段
            deviceData.put("设备编码", apiData.getString("code"));
            deviceData.put("区域", apiData.getString("leaf_region"));
            deviceData.put("小区入库时间", apiData.getString("create_date"));
            deviceData.put("覆盖的工程级的线路到达房间数", apiData.getInteger("Rooms"));
            deviceData.put("分光器数", apiData.getInteger("obd"));
            deviceData.put("分光器容量", apiData.getInteger("all_capacity"));
            deviceData.put("分光器空闲数", apiData.getInteger("free_capacity"));
            deviceData.put("ftth终端数", apiData.getInteger("ftth"));
            
            // 处理实占率
            String utilizationRateStr = apiData.getString("utilization_rate");
            Integer occupancyRate = parseOccupancyRate(utilizationRateStr);
            deviceData.put("实占率", occupancyRate);
            
            // 计算健康状态
            String healthStatus = calculateHealthStatus(occupancyRate);
            deviceData.put("健康状态", healthStatus);
            
            // 预测字段（暂时为空）
            deviceData.put("预测实占率", null);
            deviceData.put("预测空闲数", null);
            deviceData.put("预测状态", null);
            
        } catch (Exception e) {
            log.error("convertSingleDeviceData error for: {}", apiData, e);
            // 发生异常时返回基本结构，避免前端报错
            deviceData.put("设备编码", apiData.getString("code"));
            deviceData.put("区域", "");
            deviceData.put("实占率", 0);
            deviceData.put("健康状态", "normal");
        }
        
        return deviceData;
    }

    @Override
    public String calculateHealthStatus(Integer occupancyRate) {
        if (occupancyRate == null || occupancyRate < 0) {
            return "normal";
        }
        
        // 健康状态判断规则：实占率>95%告警，70-95%注意，<70%正常
        if (occupancyRate > 95) {
            return "alarm";      // 告警
        } else if (occupancyRate >= 70) {
            return "attention";  // 注意
        } else {
            return "normal";     // 正常
        }
    }

    @Override
    public Integer parseOccupancyRate(String rateStr) {
        if (ObjectUtil.isEmpty(rateStr)) {
            return 0;
        }
        
        try {
            // 去掉可能存在的%号和空格
            String numericStr = rateStr.replace("%", "").trim();
            
            // 转换为数值并四舍五入到整数
            double rate = Double.parseDouble(numericStr);
            return (int) Math.round(rate);
            
        } catch (NumberFormatException e) {
            log.warn("无法解析实占率字符串: '{}', 使用默认值0", rateStr);
            return 0;
        }
    }

    @Override
    public JSONObject getDevicePrediction(String deviceCode) {
        // TODO: 后续对接预测服务时实现
        log.info("getDevicePrediction for deviceCode: {}", deviceCode);
        
        JSONObject prediction = new JSONObject();
        prediction.put("hasData", false);
        prediction.put("设备编码", deviceCode);
        prediction.put("message", "预测功能暂未启用");
        
        return prediction;
    }

    @Override
    public List<JSONObject> batchGetDevicePrediction(List<String> deviceCodes) {
        // TODO: 后续对接预测服务时实现
        log.info("batchGetDevicePrediction for {} devices", deviceCodes.size());
        
        return deviceCodes.stream()
            .map(code -> {
                JSONObject prediction = new JSONObject();
                prediction.put("设备编码", code);
                prediction.put("预测实占率", null);
                prediction.put("预测空闲数", null);
                prediction.put("预测状态", null);
                return prediction;
            })
            .collect(Collectors.toList());
    }
}