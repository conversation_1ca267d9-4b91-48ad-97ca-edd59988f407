package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.DaClient;
import com.telecom.da.client.DaProxyFactory;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.dao.PmNetWorkLogDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.entity.CmLinkNet;
import com.telecom.nrm.service.CircuitAnalysisService;
import com.telecom.nrm.service.IPRanJsService;
import com.telecom.nrm.service.MstpService;
import com.telecom.nrm.service.OTNService;
import com.telecom.nrm.thread.audit.AbstractCircuitAuditService;
import com.telecom.nrm.thread.audit.AuditExcelDTO;
import com.telecom.nrm.utils.ExcelUtil;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ArrayStack;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.ConditionType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Data
@Slf4j
public class CircuitAnalysisServiceImpl extends AbstractCircuitAuditService
        implements CircuitAnalysisService {

    @Autowired
    CircuitAnalysisDao circuitAnalysisDao;

    @Autowired
    private OTNService otnService;


    @Autowired
    MstpService mstpService;
//    @Autowired
//    private IPRanJsService ipRanJsService;


    public static void main(String[] args) {
        DaClient client = new DaClient("http://nrm.oss.telecomjs.com:39049/daas-gray-api/interface", "net_resource_integration");// 测试
        DaProxyFactory factory = new DaProxyFactory(client);
        PmNetWorkLogDao pmNetWorkLogDao = (PmNetWorkLogDao) factory.getInstance(PmNetWorkLogDao.class);
        CircuitAnalysisDao dao = (CircuitAnalysisDao) factory.getInstance(CircuitAnalysisDao.class);
        CircuitAnalysisServiceImpl circuitAnalysisService = new CircuitAnalysisServiceImpl();
        circuitAnalysisService.setCircuitAnalysisDao(dao);
        List<JSONObject> circuit=new ArrayList<>();
        for (String sheetName : AbstractCircuitAuditService.sheetNames) {
            Sheet sheet = ExcelUtil.getXSSFWorkbookSheet(AbstractCircuitAuditService.inputFilePath, sheetName);
            if (sheet != null) {
                Row row = sheet.getRow(0);
                int codeIndex = 0, typeIndex = 0;
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    String value = row.getCell(i).getStringCellValue();
                    if ("电路代号".equals(value)) {
                        codeIndex = i;

                    }
                    if ("电路类别".equals(value)) {
                        typeIndex = i;
                        break;
                    }
                }
                for (int i = 1; i < sheet.getLastRowNum(); i++) {
                    Row row1 = sheet.getRow(i);
                    if (row1 != null ) {
                        Cell code = row1.getCell(codeIndex);
                        Cell type = row1.getCell(typeIndex);
                        JSONObject jsonObject = new JSONObject();
                        if (code != null) {
                            jsonObject.put("code", row1.getCell(codeIndex).getStringCellValue());
                        }
                        if (type != null) {
                            jsonObject.put("type", row1.getCell(typeIndex).getStringCellValue());
                            if (jsonObject.containsKey("code")) {
                                circuit.add(jsonObject);
                            }
                        }
                    }

                }
            }
        }
        circuit=circuit.stream().filter(e->"数字电路".equals(e.getString("type"))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(circuit)) {
            log.info("电路数量为" + circuit.size());
            int totalSize = circuit.size();
            int threadNum = AbstractCircuitAuditService.threadNum;
            int batchSize = (totalSize + threadNum - 1) / threadNum; // 计算每批处理的数量
            List<Future<List<AuditExcelDTO>>> futures = new ArrayList<>(threadNum);
            // 提交任务
            for (int i = 0; i < totalSize; i += batchSize) {
                int end = Math.min(i + batchSize, totalSize);
                CircuitAnalysisServiceImpl abstractCircuitAuditService = new CircuitAnalysisServiceImpl();
                abstractCircuitAuditService.setQuery(circuit.subList(i, end));
                abstractCircuitAuditService.setStart(i);
                abstractCircuitAuditService.setEnd(end - 1);
                abstractCircuitAuditService.setTotalSize(totalSize);
                abstractCircuitAuditService.setCircuitAnalysisDao(dao);
                Future<List<AuditExcelDTO>> future = AbstractCircuitAuditService.executorService.submit(abstractCircuitAuditService);
                futures.add(future);
            }
            // 收集结果
            for (Future<List<AuditExcelDTO>> future : futures) {
                try {
                    List<AuditExcelDTO> result = future.get();

                } catch (Exception e) {
                    // 处理异常
                    e.printStackTrace();
                }
            }
            AbstractCircuitAuditService.executorService.shutdown();

        }
    }


    @Override
    public void analysisCircuit(String code,String accessCode, String areaCode) {
        try {
            JSONObject mstpQuery = new JSONObject();
            ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByPmCode(areaCode);
            mstpQuery.put("areaCode", shardingEnum.toString().toLowerCase(Locale.ROOT));
            mstpQuery.put("code", code);
////            JSONObject mstpResult = mstpService.mstp(mstpQuery);
////            JSONObject mstpResult = null;
////            try {
////                Map<String, String> header = new HashMap<>();
////                String res = HttpRequestsUtils.sendPost("http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/mstp", header, mstpQuery);
////                mstpResult = JSONObject.parseObject(res);
////            } catch (Exception e) {
////                log.error("getMstpResult error:{}", e);
////                throw new RuntimeException("接口异常" + "getMstpResult error" + e.getMessage());
////            }
//
////            JSONObject mstpResult = super.mstpQuery(mstpQuery);
//            JSONObject param = new JSONObject();
//            param.put("code", code);
//            int type = 0;
//            JSONObject mstpResult = new JSONObject();
//            // 再通过接入号查询
//            if (StringUtils.isNotEmpty(accessCode) && CollectionUtils.isEmpty(mstpResult.getJSONArray("links"))) {
//                Map<String, String> header = new HashMap<>();
//                mstpQuery.put("access_code", accessCode);
//                mstpQuery.put("code", null);
//                String res = HttpRequestsUtils.sendPost("http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/mstp", header, mstpQuery);
//                mstpResult = JSONObject.parseObject(res);
//            }
//
//
//            if (CollectionUtils.isEmpty(mstpResult.getJSONArray("links"))) {
//                mstpResult = this.circuitAnalysisDao.queryResAllLinkByCode(param,shardingEnum.getBcShardingCode());
//            }
//
//            if (CollectionUtils.isEmpty(mstpResult.getJSONArray("links"))) {
//                Map<String, String> header = new HashMap<>();
//                mstpQuery.put("areaCode", shardingEnum.toString().toLowerCase(Locale.ROOT));
//                mstpQuery.put("code", code);
//                mstpQuery.remove("access_code");
//                String res = HttpRequestsUtils.sendPost("http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/mstp", header, mstpQuery);
//                mstpResult = JSONObject.parseObject(res);
//            }
//
//
//
//
//
//            if (null != mstpResult) {
//                JSONArray links = mstpResult.getJSONArray("links");
//                JSONArray RecResult = new JSONArray();
//                super.getLinkArrayRec(links, RecResult);
//                saveByLinks(shardingEnum, RecResult, code);
//            }
            // 整合数据
            JSONObject cjTypeParam = new JSONObject();
            cjTypeParam.put("code", code); // 电路代号
            JSONObject cjTypeData = this.circuitAnalysisDao.queryCjtype(cjTypeParam, NRMConstants.SHARDING_GRAPH_DB);
            if (cjTypeData == null || CollectionUtils.isEmpty(cjTypeData.getJSONArray("data"))) {
                throw new RuntimeException("无法确认cjtype");
            }
            JSONArray data = cjTypeData.getJSONArray("data");
            for (int i = data.size() - 1; i >= 0; i--) {
                if (!data.getJSONObject(i).getString("citycode").equals(areaCode)) {
                    data.remove(i); // 移除无关的
                }
            }
            JSONObject circuit = new JSONObject();
            circuit.put("citySuburbId",areaCode);
            circuit.put("code", code);
//            ipRanJsService.queryiPRanJsPre4(100,1,data.getJSONObject(0).getString("cjtype"),circuit);
            saveException(code, null, null, areaCode, "成功");

        } catch (Exception e) {
            log.error("analysisCircuit error", e);
            throw new RuntimeException(e.getMessage());


        }


    }


    public void saveIpByDeviceIds(ShardingEnum shardingEnum, Set<BigDecimal> deviceIds, JSONObject apiQuery) {
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            apiQuery.clear();
            apiQuery.put("ids", deviceIds);
            JSONObject rmNumber = circuitAnalysisDao.queryResNumberByDeviceIds(apiQuery, shardingEnum.getBcShardingCode());
            JSONArray numbers = rmNumber.getJSONArray("numbers");
            JSONArray relas = rmNumber.getJSONArray("numbers");
            apiQuery.clear();
            apiQuery.put("numbers", numbers);
            apiQuery.put("relas", relas);
            circuitAnalysisDao.saveNetNumbersByDevice(apiQuery, NRMConstants.SHARDING_CODE);

        }
    }

    public void saveRmServiceAndRelation(Set<BigDecimal> ids,ShardingEnum shardingEnum){
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        JSONObject  jsonObject =new JSONObject();
        jsonObject.put("ids",ids);
        JSONObject   rmService = circuitAnalysisDao.queryResServiceInfo(jsonObject,shardingEnum.getBcShardingCode());
        if (rmService !=null){
            jsonObject.clear();
            jsonObject.put("services",rmService.getJSONArray("services"));
            jsonObject.put("entitys",rmService.getJSONArray("entitys"));
            circuitAnalysisDao.saveNetServiceInfo(jsonObject,NRMConstants.SHARDING_CODE);
        }

    }


    public void saveByLinks(ShardingEnum shardingEnum, JSONArray RecResult, String accessCode) {
        if (CollectionUtils.isNotEmpty(RecResult)) {
            RecResult.removeIf(e -> "断点".equals(((JSONObject) e).getString("spec_id")));
            List<BigDecimal> linkIds = (List<BigDecimal>) RecResult.stream().map(e -> ((JSONObject) e).getBigDecimal("id")).collect(Collectors.toList());
            JSONObject queryJson = new JSONObject();
            JSONObject inputJson = new JSONObject();


            // 保存link
            queryJson.put("ids", linkIds);
            JSONObject link = circuitAnalysisDao.queryResLinksByIds(queryJson, shardingEnum.getBcShardingCode());
            List<BigDecimal> routeIds = (List<BigDecimal>) RecResult.stream().filter(e -> ((JSONObject) e).getString("spec_id").equals("1131200002"))
                    .map(e -> ((JSONObject) e).getBigDecimal("id")).collect(Collectors.toList());
            JSONObject linkRoute =null;
            if (CollectionUtils.isNotEmpty(routeIds)){
                queryJson.put("ids", routeIds);
                linkRoute = circuitAnalysisDao.queryResOptRoadByRoutes(queryJson, shardingEnum.getBcShardingCode());

            }
          JSONArray insertLinkJsonArr = new JSONArray();
            JSONArray insertRelaJsonArr = new JSONArray();
            if (linkRoute != null) {
                // 保存光缆段等等
                List<BigDecimal> jxgxIds = new ArrayList<>();
                JSONArray linkArr = linkRoute.getJSONArray("links");
                JSONArray linkRelas = linkRoute.getJSONArray("linkRelas");
                if (linkArr != null) {
                    insertLinkJsonArr.addAll(linkArr);
                    for (Object o : linkArr) {
                        JSONObject jx = (JSONObject) o;
                        if ("1131100003".equals(jx.getString("spec_id"))) {
                            jxgxIds.add(jx.getBigDecimal("id"));
                        }
                    }
                }
                if (linkRelas != null) {
                    insertRelaJsonArr.addAll(linkRelas);
                    for (Object o : linkRelas) {
                        JSONObject jx = (JSONObject) o;
                        if ("1131100003".equals(jx.getString("spec_id"))) {
                            jxgxIds.add(jx.getBigDecimal("id"));
                        }
                    }
                }
//                if (CollectionUtils.isNotEmpty(jxgxIds)) {
//                    queryJson.put("ids", jxgxIds);
//                    JSONObject jxgx = circuitAnalysisDao.queryResMicroRouteByJxgx(queryJson, shardingEnum.getBcShardingCode());
//                    inputJson.put("nets", jxgx.getJSONArray("nets"));
//                    inputJson.put("netEntitys", jxgx.getJSONArray("netEntitys"));
//                    inputJson.put("linkCables", jxgx.getJSONArray("linkCables"));
//                    inputJson.put("cables", jxgx.getJSONArray("cables"));
//                    inputJson.put("cableRelas", jxgx.getJSONArray("cableRelas"));
//                    inputJson.put("cableNodes", jxgx.getJSONArray("cableNodes"));
//                    circuitAnalysisDao.saveNetCables(inputJson, NRMConstants.SHARDING_CODE);
//                    inputJson.clear();
//                }

            }
            if (null != link) {
                JSONArray linkArray = link.getJSONArray("links");
                JSONArray relation = link.getJSONArray("linkRelas");
                JSONArray lowerLinkRelas = link.getJSONArray("lowerLinkRelas"); // 添加下层链路信息
                JSONArray lowerLinks = link.getJSONArray("lowerLinks");
                JSONArray upperLinks = link.getJSONArray("upperLinks");
                if (CollectionUtils.isNotEmpty(linkArray)) {
                    insertLinkJsonArr.addAll(linkArray);
                }
                if (CollectionUtils.isNotEmpty(relation)) {
                    insertRelaJsonArr.addAll(relation);
                }
                if (CollectionUtils.isNotEmpty(lowerLinks)) {
                    insertLinkJsonArr.addAll(lowerLinks);
                }
                if (CollectionUtils.isNotEmpty(upperLinks)) {
                    insertLinkJsonArr.addAll(upperLinks);
                }
                if (CollectionUtils.isNotEmpty(lowerLinkRelas)) {
                    insertRelaJsonArr.addAll(lowerLinkRelas);
                }
            }
            //对link根据ID去重插入
            if (CollectionUtils.isNotEmpty(insertLinkJsonArr)) {

                List<BigDecimal> portIds = (List<BigDecimal>) insertLinkJsonArr.stream()
                        .flatMap(e -> Stream.of(
                                ((JSONObject) e).getBigDecimal("z_port_id"),
                                ((JSONObject) e).getBigDecimal("a_port_id")))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                List<BigDecimal> deviceIds = (List<BigDecimal>) insertLinkJsonArr.stream()
                        .flatMap(e -> Stream.of(
                                ((JSONObject) e).getBigDecimal("z_physic_device_id"),
                                ((JSONObject) e).getBigDecimal("a_physic_device_id")))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                insertLinkJsonArr = new JSONArray((List) insertLinkJsonArr.stream().distinct().collect(Collectors.toList()));

                queryJson.put("ids", portIds);
                // 保存设备端口
                JSONObject ports = circuitAnalysisDao.queryResPortsByIds(queryJson, shardingEnum.getBcShardingCode());
                deviceIds = deviceIds.stream().distinct().collect(Collectors.toList());
                queryJson.put("ids", deviceIds);
                saveIpByDeviceIds(shardingEnum, new HashSet<>(deviceIds), queryJson);
                queryJson.put("ids", deviceIds);
                JSONObject devices = circuitAnalysisDao.queryResDeviceDetailPortsByDevice(queryJson, shardingEnum.getBcShardingCode());
                inputJson.put("ports", ports.getJSONArray("ports"));
                inputJson.put("devices", devices.getJSONArray("devices"));
                inputJson.put("wares", devices.getJSONArray("wares"));
                inputJson.put("deviceWareRelas", devices.getJSONArray("deviceWareRelas"));
                circuitAnalysisDao.saveNetDevices(inputJson, NRMConstants.SHARDING_CODE);
                inputJson.clear();

                // 保存局站 机房
                JSONObject station = circuitAnalysisDao.queryResStationInfosByDevice(queryJson, shardingEnum.getBcShardingCode());
                inputJson.put("facilitys", station.getJSONArray("facilitys"));
                inputJson.put("areas", station.getJSONArray("areas"));
                circuitAnalysisDao.saveNetStationInfos(inputJson, NRMConstants.SHARDING_CODE);
                inputJson.clear();
            }
            if (CollectionUtils.isNotEmpty(insertRelaJsonArr)) {
                insertRelaJsonArr = new JSONArray((List) insertRelaJsonArr.stream().distinct().collect(Collectors.toList()));

            }
            if (StringUtils.isNotEmpty(accessCode)) {
                inputJson.put("circuit_code", accessCode);
            }
            String topLinkId = null;
            String transLinksId = null;
            for (int i = 0; i < insertLinkJsonArr.size(); i++) {
                insertLinkJsonArr.getJSONObject(i).put("region_id",new BigDecimal(shardingEnum.getO3()));
                insertLinkJsonArr.getJSONObject(i).put("circuit_code",accessCode);
                if (insertLinkJsonArr.getJSONObject(i).getString("spec_id").equals("1132292019")) {
                    insertLinkJsonArr.getJSONObject(i).put("spec_id", 1132200014);
                }
                if (insertLinkJsonArr.getJSONObject(i).getString("spec_id").equals("1132200005")) {
                    insertLinkJsonArr.getJSONObject(i).put("spec_id", 1132200006);
                }
                if (insertLinkJsonArr.getJSONObject(i).getString("a_text_route") == null ) {
                    insertLinkJsonArr.getJSONObject(i).put("a_text_route", "无");
                }
                if (insertLinkJsonArr.getJSONObject(i).getString("spec_id").equals("1132100021")) {
                    insertLinkJsonArr.getJSONObject(i).put("code", accessCode);
                }
                if (insertLinkJsonArr.getJSONObject(i).getString("spec_id").equals("1132100021") ) {
                    topLinkId = insertLinkJsonArr.getJSONObject(i).getString("id");
                }
                if (insertLinkJsonArr.getJSONObject(i).getString("spec_id").equals("1132100021") ) {
                    topLinkId = insertLinkJsonArr.getJSONObject(i).getString("id");
                }
                if (insertLinkJsonArr.getJSONObject(i).getString("spec_id").equals("1132200013") ) {
                    transLinksId = insertLinkJsonArr.getJSONObject(i).getString("id");
                }
            }
            if (StringUtils.isEmpty(topLinkId)) {
                topLinkId = transLinksId;
            }
            for (int i = 0; i < insertRelaJsonArr.size(); i++) {
                insertRelaJsonArr.getJSONObject(i).put("region_id",new BigDecimal(shardingEnum.getO3()));
                insertRelaJsonArr.getJSONObject(i).put("circuit_code",accessCode);


            }
            //
            if (StringUtils.isNotEmpty(accessCode)) {
                queryJson.put("code", accessCode);
                queryJson.put("id", new BigDecimal(topLinkId));
                JSONObject jsonObject = this.circuitAnalysisDao.queryResServiceByCircuit(queryJson, shardingEnum.getBcShardingCode());
                if (CollectionUtils.isEmpty(jsonObject.getJSONArray("services"))) {
                    jsonObject = this.circuitAnalysisDao.queryResServiceByCircuitId(queryJson, shardingEnum.getBcShardingCode());
                }
                circuitAnalysisDao.saveNetServiceInfo(jsonObject, NRMConstants.SHARDING_CODE);
            }

            inputJson.put("links", insertLinkJsonArr);
            inputJson.put("linkRelas", insertRelaJsonArr);
            inputJson.put("circuit_code", accessCode);
            circuitAnalysisDao.saveNetLinks(inputJson, NRMConstants.SHARDING_CODE);
        }
    }

    public void saveException(String circutCode, String message, String type, String area, String result) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", type);
        jsonObject.put("code", circutCode);
        jsonObject.put("area", area);
        jsonObject.put("result", result);
        jsonObject.put("message", message);
        try {
            circuitAnalysisDao.circuitAnalysesReport(jsonObject, NRMConstants.SHARDING_CODE);
        } catch (Exception e) {
            log.error("", e);
        }

    }


    /**
     * 根据OTN电路编码，查询网管路由并入库在网资
     * @param code
     */
    @Override
    public void createOtn(String code) {
        //查询OTN路由信息
        JSONObject request = new JSONObject();
        request.put("code", code);
        request.put("areaCode", "nj"); // OTN不重要
        //判断电路名称内有几个地市（省内）
        List<String> areaIds = getCitysFromCircuit(code);
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(String.valueOf(areaIds.get(0)));
        // 图库有是否会连接不上，直接调用环境下 本机测试代码
        JSONObject result = null;
        try {
            Map<String, String> header = new HashMap<>();
            String res = HttpRequestsUtils.sendPost("http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/otn", header, request);
            result = JSONObject.parseObject(res);
        } catch (Exception e) {
            log.error("getMstpResult error:{}", e);
            throw new RuntimeException("接口异常" + "getMstpResult error" + e.getMessage());
        }

//        JSONObject result = otnService.queryOTNRoute(request);

        if (ObjectUtils.isEmpty(result)) {
            throw new RuntimeException("OTN路由信息为空");
        }

        JSONArray links = result.get("links") == null ? null : result.getJSONArray("links");
        if (ObjectUtils.isEmpty(links)) {
            throw new RuntimeException("OTN路由信息links节点为空");
        }

        JSONArray nodes = result.get("nodes") == null ? null : result.getJSONArray("nodes");
        if (ObjectUtils.isEmpty(nodes)) {
            throw new RuntimeException("OTN路由信息nodes节点为空");
        }
        // ODU层,包含ODU->OCH->OMS->OTS->光路、硬跳等
        JSONArray oduLinks = this.prePareOtnOduRoute(links, false);
        // TODO 遗留：本省双端的需要截取，待实现

        // 获取端点设备同时截取目标odu路由信息
        Map<String, String> endDeviceInfo = this.prePareOtnEndDeviceRoute(nodes, oduLinks);
        String startNeId = MapUtils.getString(endDeviceInfo,"startNeId", null);
        String endNeId = MapUtils.getString(endDeviceInfo,"endNeId", null);;
        String startPtpId = MapUtils.getString(endDeviceInfo,"startPtpId", null);
        String endPtpId = MapUtils.getString(endDeviceInfo,"endPtpId", null);;
        JSONObject channel = this.prePareOtnChannel(startNeId, endNeId,startPtpId,endPtpId,code); // 根据ODU起始和终止，构建通道层
        channel.put("children", oduLinks); // 形成完整结构,即channel
        // 根据要求，如果OCH下面是OMS， 需要在OCH 和OMS之间插入波道
        String wareChannelCode = "波道";
        this.prePareWareChannel( channel,wareChannelCode,1);
        this.ignoreOtu( channel,1); // 不需要OTU
        List<JSONObject> mapResLinks = new ArrayList<>(); // 记录映射的目标资源链路信息
        List<JSONObject> mapResLinkRelas = new ArrayList(); // 记录映射的目标资源链路关系信息
        JSONObject deviceInfos = new JSONObject(); // 记录需要保存的设备信息(设备 板卡等结构信息)
        JSONObject stationInfos = new JSONObject(); // 位置信息（局站、机房等）
        JSONObject cableInfos = new JSONObject();  // 记录需要保存的管线信息
        JSONObject linkInfos = new JSONObject();  // 记录需要保存的管线信息
        // 生成映射链路信息
        this.prePareResLinkComponent(areaIds.get(0),code,0L, 0L,channel,mapResLinks,mapResLinkRelas,0);
        // 加入光路相关组成信息
        this.appendOptRoadInfo(areaIds.get(0),mapResLinks, mapResLinkRelas);
        linkInfos.put("links", mapResLinks);
        linkInfos.put("linkRelas", mapResLinkRelas);
        linkInfos.put("circuit_code", code);

        // 生成设备信息和位置信息
        this.preResDeviceExtendInfos(areaIds.get(0),mapResLinks,deviceInfos,stationInfos);

//        this.prepareCableInfos(areaIds.get(0), mapResLinks,cableInfos);


        this.circuitAnalysisDao.saveNetLinks(linkInfos,NRMConstants.SHARDING_CODE);
        this.circuitAnalysisDao.saveNetDevices(deviceInfos,NRMConstants.SHARDING_CODE);
        this.circuitAnalysisDao.saveNetCables(cableInfos,NRMConstants.SHARDING_CODE);
        this.circuitAnalysisDao.saveNetStationInfos(stationInfos,NRMConstants.SHARDING_CODE);

        // 保存cjtype
        /**
         * {
         *   "code": "123",
         *   "cjtype": "OTN",
         *   "cityCode": "812",
         *   "circuit_type": "OTN2"
         * }
         */
        JSONObject cjType = new JSONObject();
        cjType.put("code", code);
        cjType.put("cjtype","政企OTN接入");
        cjType.put("cityCode",shardingEnum.getPpmCode());
        cjType.put("circuit_type","OTN精品专线");
        this.circuitAnalysisDao.saveCjType(cjType , NRMConstants.SHARDING_GRAPH_DB);

    }

    // 移除OTU层
    private void ignoreOtu(JSONObject route, int index) {
        // 递归深度控制，防止死循环
        if (index >= 10 ){
            return ;
        }
        // 如果到OMS等 结束
        if (route.getString("spec_name").equals("OMS") ||
                route.getString("spec_name").equals("OCH") ||
                route.getString("spec_name").equals("波道") ||
                route.getString("spec_name").equals("OTS") ||
                route.getString("spec_name").equals("断点")||
                route.getString("spec_name").equals("拓扑连接")) {
            return;
        }
        // 目标是ODU的下层
        if (route.getString("spec_name").indexOf("ODU") >= 0) {
            JSONArray children = route.getJSONArray("children");
            if (CollectionUtils.isEmpty(children)) {
                return ;
            }
            for (int i =  children.size() - 1; i >= 0; i--) {
                if (children.getJSONObject(i).getString("spec_name").indexOf("OTU") >= 0) {
                    JSONArray ochs = children.getJSONObject(i).getJSONArray("children");
                    if (ochs !=null && ochs.size() >= 0) {
                        // 默认只有一个OCH ，如果多个需要将所有添加到route的children里
                        children.set(i, ochs.getJSONObject(0));
                    } else {
                        children.remove(i);
                    }
                    continue;
                }

            }
        }

        if (route.getJSONArray("children") !=null && route.getJSONArray("children").size() > 0) {
            for (int i = 0; i < route.getJSONArray("children").size(); i++) {
                ignoreOtu(route.getJSONArray("children").getJSONObject(i), index + 1);
            }
        }

    }

    /**
     *查询缆线信息
     * @param regionId
     * @param links
     * @param cableInfos
     */
    private void prepareCableInfos(String regionId,List<JSONObject> links,  JSONObject cableInfos) {
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(String.valueOf(regionId));
        JSONObject queryJson = new JSONObject();
        Set<BigDecimal> jxgxIds = new HashSet<>();
        links.forEach( link -> {
            if (link.getLong("spec_id").longValue() == 1131100003L) {
                jxgxIds.add(new BigDecimal(link.getString("id")));
            }

        });

        queryJson.put("ids", jxgxIds);
        JSONObject cables = circuitAnalysisDao.queryResMicroRouteByJxgx(queryJson, shardingEnum.getBcShardingCode());
        cableInfos.put("nets", cables.getJSONArray("nets"));
        cableInfos.put("netEntitys", cables.getJSONArray("netEntitys"));
        cableInfos.put("linkCables", cables.getJSONArray("linkCables"));
        cableInfos.put("cables", cables.getJSONArray("cables"));
        cableInfos.put("cableRelas", cables.getJSONArray("cableRelas"));
        cableInfos.put("cableNodes", cables.getJSONArray("cableNodes"));
    }


    private void preResDeviceExtendInfos(String regionId, List<JSONObject> mapResLinks,JSONObject deviceInfos,JSONObject cableInfos) {
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(String.valueOf(regionId));
        JSONObject queryJson = new JSONObject();
        Set<BigDecimal> portIds = new HashSet<>();
        Set<BigDecimal> deviceIds = new HashSet<>();
        mapResLinks.forEach( link -> {
            if (link.getString("a_port_id") != null) {
                portIds.add(new BigDecimal(link.getString("a_port_id")));
            }
            if (link.getString("z_port_id") != null) {
                portIds.add(new BigDecimal(link.getString("z_port_id")));
            }
            if (link.getString("a_physic_device_id") != null) {
                deviceIds.add(new BigDecimal(link.getString("a_physic_device_id")));
            }
            if (link.getString("z_physic_device_id") != null) {
                deviceIds.add(new BigDecimal(link.getString("z_physic_device_id")));
            }
        });
        // 查询端口
        queryJson.put("ids", portIds);
        JSONObject ports = circuitAnalysisDao.queryResPortsByIds(queryJson, shardingEnum.getBcShardingCode());
        // 查询设备
        queryJson.put("ids", deviceIds);
        JSONObject devices = circuitAnalysisDao.queryResDeviceDetailPortsByDevice(queryJson, shardingEnum.getBcShardingCode());

        // 保存设备结构信息
        deviceInfos.put("ports", ports.getJSONArray("ports"));
        deviceInfos.put("devices", devices.getJSONArray("devices"));
        deviceInfos.put("wares", devices.getJSONArray("wares"));
        deviceInfos.put("deviceWareRelas", devices.getJSONArray("deviceWareRelas"));

        // 继续保存位置信息
        queryJson.put("ids", deviceIds);
        JSONObject station = circuitAnalysisDao.queryResStationInfosByDevice(queryJson, shardingEnum.getBcShardingCode());
        cableInfos.put("facilitys", station.getJSONArray("facilitys"));
        cableInfos.put("areas", station.getJSONArray("areas"));

    }

    /**
     * 添加光路信息
     * @param regionId
     * @param mapResLinks
     * @param mapResLinkRelas
     */
    private void appendOptRoadInfo(String regionId, List<JSONObject> mapResLinks, List<JSONObject> mapResLinkRelas) {
        // 从链路关系拿到光链路路由，进行查询关联链路和保存
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(String.valueOf(regionId));
        JSONObject queryJson = new JSONObject();
        List<BigDecimal> routeIds = new ArrayList<>();
        for (int i = 0; i < mapResLinkRelas.size(); i++) {
            if (mapResLinkRelas.get(i).getLong("lower_link_spec_id").longValue() == 1131200002L) {
                routeIds.add(new BigDecimal((mapResLinkRelas.get(i).getString("lower_link_id"))));
            }
        }
        queryJson.put("ids", routeIds);
        if (routeIds.size() == 0) {
            return;
        }
        JSONObject resLInks = circuitAnalysisDao.queryResOptRoadByRoutes(queryJson, shardingEnum.getBcShardingCode());
        mapResLinks.addAll(JSONObjectUtil.jsonObjectArrayToList(resLInks.getJSONArray("links")));
        mapResLinkRelas.addAll(JSONObjectUtil.jsonObjectArrayToList(resLInks.getJSONArray("linkRelas")));
    }


    private JSONObject prePareOtnChannel(String startNeId, String endNeId, String aPtpId,String zPtpId, String code) {
        JSONObject jsonObject = constructDefaultOtnRoute(code, code, "通道", startNeId,endNeId,aPtpId,zPtpId);
        return jsonObject;
    }

    /**
     * 构建资源需要的链路模型
     * @param route
     * @param resLinks
     * @param resLinkRelas
     */
    private void prePareResLinkComponent(String regionId,String code, long parentId,long parentSpecId, JSONObject route, List<JSONObject> resLinks, List<JSONObject> resLinkRelas, int index) {
        if (route  == null) {
            return ;
        }
        // TODO 理论上如果路由很多 可能会出现丢失
        if (index >= 50) {
            return ;
        }
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(String.valueOf(regionId));
        SecureRandom random = new SecureRandom();
        String specName = route.getString("spec_name");
        String routeCode = route.getString("code");
        long specId = translateSpecId(specName);
        // 是光路
        if ("拓扑连接".equals(specName) && routeCode.startsWith("F")) {
            // 通过光路查询光链路路由
            // 如果已经是处理过的，则无需再次处理
            for (int i = 0; i < resLinkRelas.size(); i++) {
                if (resLinkRelas.get(i).containsKey("code") && routeCode.equalsIgnoreCase((resLinkRelas.get(i).getString("code")))) {
                    route = null; // 移除当前对象
                    return;
                }
            }

            JSONObject param = new JSONObject();
            param.put("code", routeCode);
            // 返回光链路路由
            JSONObject routeResult =  this.circuitAnalysisDao.queryResRouteByOptRoad(param,shardingEnum.getBcShardingCode());
            if (CollectionUtils.isNotEmpty(routeResult.getJSONArray("routes"))) {
                // 保存和光链路路由上下级关系
                JSONArray routes = routeResult.getJSONArray("routes");
                createRelaLinks(code, parentId, parentSpecId, resLinkRelas, random, routeCode, routes);
                return; // 结束
            }
        }
        // 网管和资源设备和端口id映射关系
        Map<String,BigDecimal> ne2ResDevice = new HashMap<>();
        Map<String,BigDecimal> ne2ResPort = new HashMap<>();
        JSONObject link = new JSONObject();
        // 部分网管数据冗余
        link.put("route_id", route.getString("id"));
        link.put("a_ne_id", route.getString("a_ne_id"));
        link.put("a_ptp_id", route.getString("a_ptp_id"));
        link.put("z_ne_id", route.getString("z_ne_id"));
        link.put("z_ptp_id", route.getString("z_ptp_id"));
        link.put("circuit_code", code); // 电路编码 用于记录那些数据是那个电路生成
        // 资源数据

        long id =  random.nextLong() & 0x7FFFFFFFFFL;
        link.put("id", id);
        link.put("spec_id", specId);
        link.put("name", route.getString("name"));
        link.put("code", route.getString("name"));
        link.put("region_id", new BigDecimal(regionId));
        link.put("sharding_id",  new BigDecimal(regionId));
        if (!ne2ResDevice.containsKey(route.getString("a_ne_id"))) {
            ne2ResDevice.put(route.getString("a_ne_id"),this.mapOtnDevice(route.getString("a_ne_id"),shardingEnum.getBcShardingCode()));
        }
        if (!ne2ResDevice.containsKey(route.getString("z_ne_id"))) {
            ne2ResDevice.put(route.getString("z_ne_id"),this.mapOtnDevice(route.getString("z_ne_id"),shardingEnum.getBcShardingCode()));
        }

        link.put("a_physic_device_id", MapUtils.getObject(ne2ResDevice,route.getString("a_ne_id"), 0L));
        link.put("z_physic_device_id", MapUtils.getObject(ne2ResDevice,route.getString("z_ne_id"), 0L));


        if (ne2ResPort.containsKey(route.getString("a_ptp_id"))) {
            ne2ResPort.put(route.getString("a_ptp_id"),this.mapOtnPort(route.getString("a_ptp_id"),shardingEnum.getBcShardingCode()));

        }
        if (ne2ResPort.containsKey(route.getString("z_ptp_id"))) {
            ne2ResPort.put(route.getString("a_ptp_id"),this.mapOtnPort(route.getString("a_ptp_id"),shardingEnum.getBcShardingCode()));

        }
        link.put("a_port_id", MapUtils.getObject(ne2ResPort,route.getString("a_ptp_id"), 0L));
        link.put("z_port_id", MapUtils.getObject(ne2ResPort,route.getString("z_ptp_id"), 0L));
        resLinks.add(link);
        String seq = route.getString("seg_no") + "." +  route.getString("grp_no") + "." + route.getString("order_no");

        // 链路关系
        if (parentId > 0) {
            JSONObject linkRela = new JSONObject();
            linkRela.put("id", random.nextLong() & 0x7FFFFFFFFFL);
            linkRela.put("spec_id",1130311310000L);
            linkRela.put("upper_link_id",parentId);
            linkRela.put("upper_link_spec_id",parentSpecId);
            linkRela.put("lower_link_id",id);
            linkRela.put("lower_link_spec_id",specId);
            linkRela.put("circuit_code", code);
            linkRela.put("seq",seq);
            resLinkRelas.add(linkRela);
        }

        JSONArray children = route.getJSONArray("children");
        // 对children进行移除方向处理 分析:1是方向，2是正向
        for (int i = children.size() - 1; i >= 0 ; i--) {
            if ("1".equals( children.getJSONObject(i).getString("grp_no"))) {
                // 检查是否存在方向2，如果存在则删除当前,
                for (Object child : children) {
                    JSONObject c = (JSONObject) child;
                    if (c.getString("a_ne_id")!=null && c.getString("a_ne_id").equals(children.getJSONObject(i).getString("z_ne_id")) &&
                            c.getString("z_ne_id") !=null && c.getString("z_ne_id").equals(children.getJSONObject(i).getString("a_ne_id"))) {
                        children.remove(i);
                        break;
                    }
                }
            }

        }


        if (children !=null &&children.size() > 0) {
            for (int i = children.size() - 1; i >= 0 ; i--) {
                prePareResLinkComponent(regionId, code,id,specId,
                        children.getJSONObject(i),
                resLinks,
                resLinkRelas,
                index + 1);
            }
        }

    }
    @Override
    public void createRelaLinks(String code, long parentId, long parentSpecId, List<JSONObject> resLinkRelas, SecureRandom random, String routeCode, JSONArray routes) {
        for ( Object r : routes) {
            JSONObject linkRela = new JSONObject();
            linkRela.put("id", random.nextLong() & 0x7FFFFFFFFFL);
            linkRela.put("code", routeCode);
            linkRela.put("spec_id",1130311310000L);
            linkRela.put("upper_link_id", parentId);
            linkRela.put("upper_link_spec_id", parentSpecId);
            linkRela.put("lower_link_id",new BigDecimal(((JSONObject)r).getString("id")));
            linkRela.put("lower_link_spec_id",1131200002L); // 光链路路由
            linkRela.put("circuit_code", code);
            resLinkRelas.add(linkRela);
        }
    }

    @Override
    public void syncResByIp(String ip, String areaCode) {

    }

    private long translateSpecId(String name) {
        if (name.equals("通道")) {
            return 1132200001L;
        }
        if (name.indexOf("ODU")>=0) {
            return 1132300005L;
        }
        if (name.equals("OCH")) {
            return 1132200002L;
        }
        if (name.equals("波道")) {
            return 1132300002L;
        }
        if (name.equals("OMS")) {
            return 1132100010L;
        }
        if (name.equals("OTS")) {
            return 1132100019L;
        }
        if (name.equals("断点")) {
            return 1131100001L;
        }
        // 实际上如果编号是FXX 则应该是光路
        if (name.equals("拓扑连接")) {
            return 1131100001L;
        }
        return 0;
    }

    /**
     * 映射OTN设备在资源中实例
     * @param neId
     * @return
     */
    private BigDecimal  mapOtnDevice(String neId, String shardingCode) {

        if (neId == null) {
            return null;
        }

        JSONObject otnDevParam = new JSONObject();
        Set<String> deviceIds = new HashSet<>();
        deviceIds.add(neId);
        otnDevParam.put("deviceIds", deviceIds);
        PageResponse<JSONObject> otnDevInfo = circuitAnalysisDao.queryTransNeByParam(otnDevParam, deviceIds.size(), 0,
                NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> otnDevList = otnDevInfo.getData();
        if (otnDevList.size() == 0) {
            return null;
        }
        String name = otnDevList.get(0).getString("aliasname");
        if ("15359-南京云南路-R".equals(name)) {
            name = "15359-南京云南路-R-集团政企OTN";
        }
        if ("15437-南京马鞍山-R".equals(name)) {
            name = "15437-南京马鞍山-R-集团政企OTN";
        }


        JSONObject bcDevParam = new JSONObject();
        Set<String> otnDevNames = new HashSet<>();
        otnDevNames.add(name);
        bcDevParam.put("deviceNames", otnDevNames);
        PageResponse<JSONObject> bcDevInfo = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                shardingCode);
        if (CollectionUtils.isNotEmpty(bcDevInfo.getData())) {
            return new BigDecimal( bcDevInfo.getData().get(0).getString("id"));
        } else {
            // 主要针对南京的OTN设备，匹配率很低
            otnDevNames.clear();
            otnDevNames.add(name.replace("(OptiX OSN 9600)","").replace("(OptiX OSN 9600 U32)","") + "-集团政企OTN");
            bcDevParam.put("deviceNames", otnDevNames);
            bcDevInfo = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                    shardingCode);
            if (CollectionUtils.isNotEmpty(bcDevInfo.getData())) {
                return new BigDecimal( bcDevInfo.getData().get(0).getString("id"));
            }
        }
        otnDevNames.clear();
        otnDevNames.add(name.replace("(OptiX OSN 9600)","").replace("(OptiX OSN 9600 U32)","") );
        bcDevParam.put("deviceNames", otnDevNames);
        bcDevInfo = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                shardingCode);
        if (CollectionUtils.isNotEmpty(bcDevInfo.getData())) {
            return new BigDecimal( bcDevInfo.getData().get(0).getString("id"));
        }

        return null;
    }

    /**
     * 映射OTN端口在资源中实例
     * @param ptpId
     * @return
     */
    private BigDecimal mapOtnPort(String ptpId, String shardingCode) {
        // TODO 目前基本匹配不上
        return null;
    }


    /**
     * 在OCH和OMS之间添加波道
     * @param route
     * @param index
     */
    private void prePareWareChannel(JSONObject route, String wareChannelCode ,int index) {
        // 递归深度控制，防止死循环
        if (index >= 10 ){
            return ;
        }
        // 如果到OMS等 结束
        if (route.getString("spec_name").equals("OMS") ||
                route.getString("spec_name").equals("波道") ||
                route.getString("spec_name").equals("OTS") ||
                route.getString("spec_name").equals("断点")||
                route.getString("spec_name").equals("拓扑连接")) {
            return;
        }
        // 目标是OCH
        if (route.getString("spec_name").equals("OCH")) {
            JSONArray children = route.getJSONArray("children");
            if (CollectionUtils.isEmpty(children)) {
                return ;
            }
            for (int i = 0; i < children.size(); i++) {
                if (!children.getJSONObject(i).getString("spec_name").equals("OMS")) {
                    continue;
                }
                // 添加波道
                JSONObject jsonObject = constructDefaultOtnRoute(
                        wareChannelCode,
                        wareChannelCode,
                        "波道",
                        children.getJSONObject(i).getString("a_ne_id"),
                        children.getJSONObject(i).getString("z_ne_id"),
                        children.getJSONObject(i).getString("a_ptp_id"),
                        children.getJSONObject(i).getString("z_ptp_id")
                        );
                jsonObject.put("seg_no", children.getJSONObject(i).getString("seg_no"));
                jsonObject.put("grp_no", children.getJSONObject(i).getString("grp_no"));
                jsonObject.put("order_no", children.getJSONObject(i).getString("order_no"));
                jsonObject.put("children", new JSONArray());
                jsonObject.getJSONArray("children").add(children.getJSONObject(i));
                children.set(i, jsonObject);
                continue;
            }
        }

        if (route.getJSONArray("children") !=null && route.getJSONArray("children").size() > 0) {
            for (int i = 0; i < route.getJSONArray("children").size(); i++) {
                prePareWareChannel(route.getJSONArray("children").getJSONObject(i),wareChannelCode, index + 1);
            }
        }

    }


    /**
     *
     * @param name
     * @param code
     * @param aNeId
     * @param zNeId
     * @param aPtpId
     * @param zPtpId
     * @return
     */
    private JSONObject constructDefaultOtnRoute(String name, String code,  String specName,
                                     String aNeId, String zNeId, String aPtpId, String zPtpId) {
        JSONObject linkObj = new JSONObject();
        linkObj.put("id", "v-"+UUID.randomUUID());
        linkObj.put("name", name);
        linkObj.put("code", code);
        linkObj.put("spec_name",specName);
        linkObj.put("a_ne_id", aNeId);
        linkObj.put("a_ptp_id", aPtpId);
        linkObj.put("z_ne_id", zNeId);
        linkObj.put("z_ptp_id", zPtpId);




        return linkObj;
    }


    /**
     * 根据一树一图结果分析截取ODU链路信息
     * @param links
     * @return
     */
    public JSONArray prePareOtnOduRoute( JSONArray links, boolean firstOdu) {
        JSONArray otnRoutes = null;
        // 第一层只有一个，必须要去掉
        otnRoutes = (JSONArray) JSONArray.parse(links.getJSONObject(0).getJSONArray("children").toString());
        if (otnRoutes == null || otnRoutes.size() <=0) {
            return otnRoutes;
        }

        // 继续如果第一层是SDH低阶路径，则只保留一各，其他都是重复（根据实际数据观察）
        if (otnRoutes.getJSONObject(0).getString("spec_name").equals("ODUflex")) {
            otnRoutes = (JSONArray) JSONArray.parse(links.getJSONObject(0).getJSONArray("children").toString());
        }

        // 样例数据：北京苏州ZQOTN00032
        // 继续如果第一层是SDH低阶路径，则只保留一各，其他都是重复（根据实际数据观察）
        if (otnRoutes.getJSONObject(0).getString("spec_name").equals("SDH低阶路径")) {
            for (int i = otnRoutes.size() - 1; i >= 0; i--) {
                if (i != 0) {
                    otnRoutes.remove(i);
                }
            }
        }

        // 是否MS
        boolean ms = false;
        for (int i = 0; i < otnRoutes.size(); i++) {
            if (otnRoutes.getJSONObject(i).getString("spec_name").indexOf("MS")>=0 ) {
                ms = true;
            }
        }
        // 不是MS
        if (!ms) {
            otnRoutes = otnRoutes.getJSONObject(0).getJSONArray("children");
            if (otnRoutes == null || otnRoutes.size() <=0) {
                return otnRoutes;
            }

            // 如果是SDH啥 则继续向下钻取，集团不需要该层
            for (int i = 0; i < otnRoutes.size(); i++) {
                if (otnRoutes.getJSONObject(i).getString("spec_name").indexOf("SDH")>=0) {
                    // 取最后一个
                    // TODO: 有两个是代表什么，主备？ 从MS就开始主备
                    JSONArray rs = otnRoutes.getJSONObject(i).getJSONArray("children");
                    otnRoutes.set(i,rs.getJSONObject(0));
                    if (rs.size() > 1) {
                        for (int k = 1;k <=rs.size() -1; k++) {
                            otnRoutes.add(i+k,rs.getJSONObject(k));
                        }

                    }
                }
            }
        }

        // 如果是MS 则继续向下钻取，集团不需要该层
        for (int i = 0; i < otnRoutes.size(); i++) {
            if (otnRoutes.getJSONObject(i).getString("spec_name").indexOf("MS")>=0 && otnRoutes.getJSONObject(i).getJSONArray("children")!=null && otnRoutes.getJSONObject(i).getJSONArray("children").size() > 0) {
                otnRoutes.set(i,otnRoutes.getJSONObject(i).getJSONArray("children").getJSONObject(0));
            }
        }
        boolean rs = false;
        // 如果是RS 则继续向下钻取，集团不需要该层
        for (int i = 0; i < otnRoutes.size(); i++) {
            if (otnRoutes.getJSONObject(i).getString("spec_name").indexOf("RS")>=0) {
                if (otnRoutes.getJSONObject(i).getJSONArray("children")!=null && otnRoutes.getJSONObject(i).getJSONArray("children").size() > 0) {
                    otnRoutes.set(i,otnRoutes.getJSONObject(i).getJSONArray("children").getJSONObject(0));
                }
                rs = true;
            }
        }

        // 如果有RS 则继续下一层才是真正ODU层
        if (rs && !firstOdu) {
            JSONArray odu4sTemp = new JSONArray();
            // 继续替换
            for (int i = 0; i < otnRoutes.size(); i++) {
                if (otnRoutes.getJSONObject(i).getJSONArray("children") != null && otnRoutes.getJSONObject(i).getJSONArray("children").size() > 0) {
                    odu4sTemp.addAll(otnRoutes.getJSONObject(i).getJSONArray("children"));
                } else {
                    odu4sTemp.add(otnRoutes.get(i));
                }

            }
            otnRoutes = odu4sTemp;
        }

        return otnRoutes;
    }

    private Map<String, String> prePareOtnEndDeviceRoute(JSONArray nodes,JSONArray links) {
        Map<String, String> endDeviceIds = new HashMap<>();
        List<String> borderDeviceIds = new ArrayList<>(); //记录长途设备，1000
        String startNeId = null; // 起始网元设备
        // 分析：截至到第一次出现的1000的设备，入参中路由是有相对顺序的
        String endNeId = null; // 终止网元设备 上长途之前的设备
        String startPtpId = null;
        String endPtpId = null;

        for (int i = 0; i < nodes.size(); i++) {
            JSONObject node = nodes.getJSONObject(i);
            if ("1000".equals(node.getString("res_domain_id"))) {
                borderDeviceIds.add(node.getString("id"));
            }
            if ( node.getString("code")!=null && node.getString("code").indexOf("CPE") >= 0) {
                startNeId = node.getString("id");
            }
        }
        // 对路由进行顺序调整 如果起始设备不是第一个则倒叙下
        if (!startNeId.equals(links.getJSONObject(0).getString("a_ne_id"))  &&
                !startNeId.equals(links.getJSONObject(0).getString("z_ne_id"))) {
            for (int i = 0; i < links.size() / 2; i++) {
                JSONObject temp = links.getJSONObject(i);
                links.set(i,links.getJSONObject(links.size() - 1 - i));
                links.set(links.size() - 1 - i, temp);

            }
            CollectionUtils.reverseArray(links.toArray());
        }
        for (int i = 0; i < links.size(); i++) {
            String aNeId = links.getJSONObject(i).getString("a_ne_id");
            String zNeId = links.getJSONObject(i).getString("z_ne_id");
            if (startNeId.equals(aNeId)) {
                startPtpId = links.getJSONObject(i).getString("a_ptp_id");
            }
            if (startNeId.equals(zNeId)) {
                startPtpId = links.getJSONObject(i).getString("z_ptp_id");
            }

            if (borderDeviceIds.contains(aNeId) || borderDeviceIds.contains(zNeId)) {
                if (borderDeviceIds.contains(aNeId)) {
                    endNeId = aNeId;
                    endPtpId = links.getJSONObject(i).getString("a_ptp_id");
                } else {
                    endNeId = zNeId;
                    endPtpId = links.getJSONObject(i).getString("z_ptp_id");
                }
                break;
            }
        }
        // 倒叙找到第一次出现终止设备时终止，删除
        boolean success = false;
        for (int i =links.size() - 1; i >= 0; i--) {
            String aNeId = links.getJSONObject(i).getString("a_ne_id");
            String zNeId = links.getJSONObject(i).getString("z_ne_id");
            if (StringUtils.isEmpty(aNeId)) {
                aNeId = links.getJSONObject(i).getString("a_physic_device_id");
            }
            if (StringUtils.isEmpty(zNeId)) {
                zNeId = links.getJSONObject(i).getString("z_physic_device_id");
            }
            links.remove(i);
            if (endNeId.equalsIgnoreCase(aNeId) || endNeId.equals(zNeId)) {
                // 对下一条进行判断，可能和当前删除形成主备
                if ( i - 1 >=0) {
                    if (aNeId.equals(links.getJSONObject(i-1).getString("a_ne_id")) &&
                            zNeId.equals(links.getJSONObject(i-1).getString("z_ne_id")) ) {
                        links.remove( i - 1);
                    }else if (zNeId.equals(links.getJSONObject(i-1).getString("a_ne_id")) &&
                            aNeId.equals(links.getJSONObject(i-1).getString("z_ne_id")) ) {
                        links.remove( i - 1);
                    }
                }
                // 如果再向上找到和当前不连续则认为是错误的 再正向重新找
                int lsize = links.size();
                if (links.getJSONObject(lsize -1).getString("a_ne_id") == null ||
                        links.getJSONObject(lsize -1).getString("z_ne_id") == null) {
                    success = false;

                } else if (
                        !links.getJSONObject(lsize - 1).getString("a_ne_id").equals(endNeId) &&
                                !links.getJSONObject(lsize - 1).getString("z_ne_id").equals(endNeId)
                ) {

                    success = false;

                } else {
                    // 追加判断最后一段时候都是1000
                    if (borderDeviceIds.contains(links.getJSONObject(lsize - 1).getString("a_ne_id")) &&
                            borderDeviceIds.contains(links.getJSONObject(lsize - 1).getString("z_ne_id")  )) {
                        links.remove(links.size() - 1);
                    }
                    success = true;
                }

                break;
            }
        }
        if (!success) {
            int endIndex = 0;
            for (int i = 0; i <= links.size() - 1; i++) {
                String aNeId = links.getJSONObject(i).getString("a_ne_id");
                String zNeId = links.getJSONObject(i).getString("z_ne_id");
                if (aNeId.equals(endNeId) || zNeId.equals(endNeId)) {
                    endIndex = i;
                    if ( i < links.size() - 1) {
                        if (aNeId.equals(links.getJSONObject(i+1).getString("a_ne_id")) &&
                                zNeId.equals(links.getJSONObject(i+1).getString("z_ne_id")) ) {
                        }else if (zNeId.equals(links.getJSONObject(i+1).getString("a_ne_id")) &&
                                aNeId.equals(links.getJSONObject(i+1).getString("z_ne_id")) ) {
                            endIndex  = endIndex +1;
                        }
                    }
                    for (int k =links.size() - 1 ;k >=endIndex + 1;k--) {
                        links.remove(k);
                    }
                    break;

                }
            }
        }
        endDeviceIds.put("startNeId", startNeId);
        endDeviceIds.put("endNeId", endNeId);
        endDeviceIds.put("startPtpId", startPtpId);
        endDeviceIds.put("endPtpId", endPtpId);
        return endDeviceIds;
    }




    @Override
    public List<AuditExcelDTO> call() throws Exception {
        System.out.println(String.format("总数量为%s 当前线程为%s,起始行为%s,结束行为%s", super.getTotalSize(),
                Thread.currentThread().getName(), super.getStart(), super.getEnd()));
        List<JSONObject> circuit = super.getQuery();
        for (JSONObject s : circuit) {
            this.analysisCircuit(s.getString("code"),null, s.getString("type"));
        }
        return null;
    }

}
