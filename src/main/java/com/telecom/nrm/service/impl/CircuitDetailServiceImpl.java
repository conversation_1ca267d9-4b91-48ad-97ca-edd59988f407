package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CircuitDetailDao;
import com.telecom.nrm.service.CircuitDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class CircuitDetailServiceImpl implements CircuitDetailService {

    @Autowired
    CircuitDetailDao circuitDetailDao;

    @Override
    public PageResponse<JSONObject> circuitDetailQuery(JSONObject jsonObject, String ds) {
        return circuitDetailDao.circuitDetailQuery(jsonObject, ds);
    }
}
