package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.domain.topo.TopoMode;
import com.telecom.nrm.service.*;
import com.telecom.nrm.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service


@Slf4j
public class MstpServiceImpl implements MstpService {

    @Autowired
    OTNService otnService;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    CommonCircuitService commonCircuitService;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    CircuitAnalysisDao circuitAnalysisDao;


    @Override
    public JSONObject mstp(JSONObject request) {
        // JSONObject result = new JSONObject();
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");
        String access_code = request.getString("access_code");

        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_mstp_topo").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("zq_service","code",code))
                .appendWhereBodyItem(new GraphWhereBodyItem("zq_service","access_code",access_code))
                .appendWhereBodyItem(new GraphWhereBodyItem("ether_link","is_up","1"))

                .appendFilter("ether_link",(dataList,g)->{
                        // 如果以太网链路对应的政企业务无MSTP电路,则添加该以太网链路
                        JSONObject graphData =   graphService.getData(g);
                        graphService.buildReverseTree(graphData, "政企业务", "以太网链路", "zq_services");
                        graphService.buildTree(graphData,"政企业务","mstp电路","mstp_circuits");
                        dataList = graphService.filterNodeByLabel(graphData,"以太网链路");
                        List<JSONObject> filteredList = new ArrayList<>();
                        boolean isAccessProduct = false;
                        for (JSONObject d: dataList) {

                            int mstpNum =0;
                            List<JSONObject> zqServiceList = d.getJSONArray("zq_services").toJavaList(JSONObject.class);
                            for (JSONObject zqService: zqServiceList) {
                                if(ObjectUtil.isNotEmpty(zqService.getJSONArray("mstp_circuits"))) {
                                    mstpNum+=zqService.getJSONArray("mstp_circuits").size();
                                    if (zqService.getString("service_type_name").equals("专线光纤宽带")) {
                                        isAccessProduct = true;
                                    }
                                }

                            }
                            if (isAccessProduct) {
                                filteredList.add(d);
                            }
                        }
                        return filteredList;
                    })

                .appendFilter("switch_up_link",(dataList,g)->{
                    // 如果以太网链路对应的政企业务无MSTP电路,则添加该以太网链路
                    JSONObject graphData =   graphService.getData(g);
                    graphService.buildReverseTree(graphData, "政企业务", "交换机上联链路", "zq_services");
                    graphService.buildTree(graphData,"政企业务","mstp电路","mstp_circuits");
                    dataList = graphService.filterNodeByLabel(graphData,"交换机上联链路");
                    List<JSONObject> filteredList = new ArrayList<>();
                    boolean isAccessProduct = false;
                    for (JSONObject d: dataList) {

                        int mstpNum =0;
                        List<JSONObject> zqServiceList = d.getJSONArray("zq_services").toJavaList(JSONObject.class);
                        for (JSONObject zqService: zqServiceList) {
                            if(ObjectUtil.isNotEmpty(zqService.getJSONArray("mstp_circuits"))) {
                                mstpNum+=zqService.getJSONArray("mstp_circuits").size();
                                if (zqService.getString("service_type_name").equals("专线光纤宽带")) {
                                    isAccessProduct = true;
                                }
                            }

                        }
                        if (isAccessProduct) {
                            filteredList.add(d);
                        }
                    }
                    return filteredList;
                })


        .build();

        Graph graph = graphApiService.doApi(graphRequest);

        Set<Node> nodes = graph.getNodes();
        Set<Edge> edges = graph.getEdges();

        Node cfsNode = nodes.stream().filter(n->n.getId().equals("cfs")).collect(Collectors.toList()).get(0);

        if (ObjectUtil.isNotEmpty(cfsNode.getData())) {

            Node deviceNode = nodes.stream().filter(n -> n.getId().equals("device")).collect(Collectors.toList()).get(0);

            Node userTerminalNode = nodes.stream().filter(n -> n.getId().equals("user_terminal")).collect(Collectors.toList()).get(0);
            Node linkNode = nodes.stream().filter(n -> n.getId().equals("link")).collect(Collectors.toList()).get(0);
            Edge linkCarryLinkEdge = edges.stream().filter(e -> e.getAId().equals("logic_link") && e.getBId().equals("mstp_circuit") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);

            Edge linkCarryPonLinkEdge = edges.stream().filter(e -> e.getAId().equals("logic_link") && e.getBId().equals("pon_link") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);

            Edge linkCarryEtherLinkEdge = edges.stream().filter(e -> e.getAId().equals("logic_link") && e.getBId().equals("ether_link") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);
            Edge linkCarrySwitchUpLinkEdge = edges.stream().filter(e -> e.getAId().equals("logic_link") && e.getBId().equals("switch_up_link") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);


            List<JSONObject> devices = deviceNode.getData();
            List<JSONObject> links = linkNode.getData();
            List<JSONObject> linkRelations = linkCarryLinkEdge.getData();
            linkRelations.addAll(linkCarryPonLinkEdge.getData());
            linkRelations.addAll(linkCarryEtherLinkEdge.getData());
            linkRelations.addAll(linkCarrySwitchUpLinkEdge.getData());
            String startId = "";
            List<JSONObject> userTerminals = userTerminalNode.getData();
            if (ObjectUtil.isNotEmpty(userTerminals)) {
                startId = userTerminals.get(0).getString("id");
            }

            JSONObject result = commonCircuitService.sortCircuitTopology(devices, links, linkRelations, TopoMode.NET, startId);
            JSONObject cfs = new JSONObject();
            cfs.put("data", cfsNode.getData());

            result.put("cfs", cfs);
            result.put("areaId", ShardingEnum.getShardingEnumByBc(shardingCode).getO3());
            result.put("cityCode", ShardingEnum.getShardingEnumByBc(shardingCode).getPpmCode());
            return result;
        }else{
            return mstpByLink(request);
        }
    }

    public JSONObject mstpByLink(JSONObject request) {
        // JSONObject result = new JSONObject();
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");
        String access_code = request.getString("access_code");

        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_mstp_link_topo").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("mstp_circuit","code",code))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);

        Set<Node> nodes = graph.getNodes();
        Set<Edge> edges = graph.getEdges();




        Node deviceNode = nodes.stream().filter(n -> n.getId().equals("device")).collect(Collectors.toList()).get(0);
        Node linkNode = nodes.stream().filter(n -> n.getId().equals("link")).collect(Collectors.toList()).get(0);
        Node mstpCircuitNode = nodes.stream().filter(n -> n.getId().equals("mstp_circuit")).collect(Collectors.toList()).get(0);
        Edge linkCarryLinkEdge = edges.stream().filter(e -> e.getAId().equals("logic_link") && e.getBId().equals("mstp_circuit") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);



        List<JSONObject> devices = deviceNode.getData();
        List<JSONObject> links = linkNode.getData();
        List<JSONObject> linkRelations = linkCarryLinkEdge.getData();

        List<JSONObject> mstpCircuits = mstpCircuitNode.getData();

        if (ObjectUtil.isNotEmpty(mstpCircuits)) {
            JSONObject mstpCircuit = mstpCircuits.get(0);
            String startId = mstpCircuit.getString("a_physic_device_id");
            JSONObject result = commonCircuitService.sortCircuitTopology(devices, links, linkRelations, TopoMode.NET, startId);

            result.put("areaId", ShardingEnum.getShardingEnumByBc(shardingCode).getO3());
            result.put("cityCode", ShardingEnum.getShardingEnumByBc(shardingCode).getPpmCode());
            return result;
        }else{
            return new JSONObject();
        }



    }


    // 用于对政企电路进行排序提取起点和终点设备信息
    public JSONObject getMSTPTopology(Graph graph, String code) {
        JSONObject data = graphService.getData(graph);
        Set<Edge> edges = graph.getEdges();
        graphService.buildTree(data, "政企业务","设备", "device_list");
        graphService.buildTree(data, "政企业务","链路", "link_list");
        Edge linkCarryLinkEdge = edges.stream().filter(e->e.getAId().equals("logic_link") && e.getBId().equals("mstp_circuit") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);

        List<JSONObject> circuitList = graphService.filterNodeByLabel(data, "政企业务");

        List<JSONObject> circuitFindedList = circuitList.stream().filter(c->c.getString("code").equals(code)).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(circuitFindedList)) {
            JSONObject circuit = circuitFindedList.get(0);
            List<JSONObject> devices = new ArrayList<>();
            if(!Objects.isNull(circuit.getJSONArray("device_list"))) circuit.getJSONArray("device_list").toJavaList(JSONObject.class);
            List<JSONObject> links = new ArrayList<>();
            if(!Objects.isNull(circuit.getJSONArray("link_list")))circuit.getJSONArray("link_list").toJavaList(JSONObject.class);
            // Set<Edge> edges = graph.getEdges();
            List<JSONObject> linkRelations = linkCarryLinkEdge.getData();

            JSONObject result = commonCircuitService.sortCircuitTopology(devices,links,linkRelations, TopoMode.NET, "");
            return result;
        }else{
            return new JSONObject();
        }

    }

    Function<JSONObject,JSONObject> mapFun = (d)->{
        JSONObject result = new JSONObject();
        Set<String> keys = d.keySet();
        if (ObjectUtil.isNotEmpty(keys)) {
            // log.info("keys={}",keys.toString());
            for (String key: keys) {
                if (! (d.get(key) instanceof Collection)) {
                    result.put(key,d.get(key));
                };
            }
        }
        return result;
    };



    public JSONObject queryMSTPToPipeSegment(JSONObject request) {
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");
        String all = request.getString("all");
        String access_code = request.getString("access_code");
        if (code != null && code.indexOf("OTN") >= 0) {
            JSONObject route =  queryOtnToPipeSegment(request);
            return this.analisysRoutePipeSegment(route);
        }
        if ("Y".equalsIgnoreCase(all)) {
            try {
                JSONObject route = BeanUtils.getBean(IpranService.class).ipran(request);
                return this.analisysRoutePipeSegment(route);
            } catch (IOException e) {
                e.printStackTrace();
            }

        }

        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_mstp_2_pipesegment").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("mstp_circuit","code",code))
                .appendWhereBodyItem(new GraphWhereBodyItem("mstp_circuit","access_code",access_code)).build();

        Graph graph = graphApiService.doApi(graphRequest);

        JSONObject data = graphService.getData(graph);

        List<JSONObject> pipeSegmentList = graphService.filterNodeByLabel(data,"管道段");
        List<JSONObject> roomList = graphService.filterNodeByLabel(data,"机房");
        List<JSONObject> deviceList = graphService.filterNodeByLabel(data,"设备");

        // 反转树找到管道段下所有光路
        // 正向树找到mstp下面所有的管道段,管道段在上一次构建反转树的过程中附带了光路信息
        //逻辑电路同理
        graphService.buildReverseTree(data, "光路", "管道段", "opt_road_list");
        graphService.mapProperty(data,"管道段", "opt_road_list", mapFun);

        graphService.buildTree(data,"mstp电路","管道段", "pipe_segment_list");
        graphService.mapProperty(data, "mstp电路", "pipe_segment_list",mapFun);

        graphService.buildTree(data,"逻辑链路","管道段", "pipe_segment_list");
        graphService.mapProperty(data, "逻辑链路", "pipe_segment_list",mapFun);

        List<JSONObject> mstpCircuitList = graphService.filterNodeByLabel(data,"mstp电路");
        List<JSONObject> logicLinkList = graphService.filterNodeByLabel(data,"逻辑链路");

        List<JSONObject> linkList = new ArrayList<>();
        linkList.addAll(mstpCircuitList);
        linkList.addAll(logicLinkList);



        deviceAddAlarmInfo(deviceList);
        JSONObject result = new JSONObject();
        result.put("pipe_segments", pipeSegmentList);
        result.put("rooms", roomList);
        result.put("links", linkList);
        result.put("devices", deviceList);

        return result;
    }

    /**
     * 解析光纤路由
     * @param route
     * @return
     */
    private JSONObject analisysRoutePipeSegment(JSONObject route) {
        /**
         * 1、分析出光路信息
         * 2、根据光路查询管线
         */
        JSONObject resut = new JSONObject();
        resut.put("rooms", new JSONArray());
        resut.put("roomLinks", new JSONArray());
        resut.put("devices", new JSONArray());
        resut.put("pipe_segments", new JSONArray());
        JSONArray roomLinks = new JSONArray();
        JSONArray links = route.getJSONArray("links");
        JSONArray nodes = route.getJSONArray("nodes");
        Map<String,String> device2Facility = new HashMap<>(); // 设备和机房映射
        Map<String,String> device2City = new HashMap<>(); // 设备和城市
        Map<String,String> road2Region = new HashMap<>(); // 设备和地区映射
        Set routeRooms = new HashSet<BigDecimal>();
        for (int i = 0; i < nodes.size(); i++) {
            device2City.put(nodes.getJSONObject(i).getString("id"),nodes.getJSONObject(i).getString("city"));
            device2Facility.put(nodes.getJSONObject(i).getString("id"),nodes.getJSONObject(i).getString("facility_id"));
        }
        // 钻取到最底层
        JSONArray components = new JSONArray();
        for (int i = 0; i < links.size(); i++) {
            analisysComponent(links.getJSONObject(i), components);
        }
        Set<String> optIds =  new HashSet<>();
        // 根据链路组成分析光路 和机房联系信息
        for (int i = 0; i < components.size(); i++) {
            String aDeviceId = components.getJSONObject(i).getString("a_physic_device_id");
            String zDeviceId = components.getJSONObject(i).getString("z_physic_device_id");
            String specId = components.getJSONObject(i).getString("spec_id");
            String specName = components.getJSONObject(i).getString("spec_name");
            String code = components.getJSONObject(i).getString("code");
            String name = components.getJSONObject(i).getString("name");
            String id = components.getJSONObject(i).getString("id");
            String regionId = components.getJSONObject(i).getString("region_id");
            // regionID 如果没有需要考虑
            if (StringUtils.isEmpty(regionId)) {
                // 321142060000000031624566
                if (StringUtils.isEmpty(id)){
                    continue;
                }
                regionId = this.analysisRegionId(id);

            }


            if (device2Facility.get(aDeviceId) != null && device2Facility.get(zDeviceId) != null ) {
                JSONObject roomLink = new JSONObject();
                roomLink.put("a_room_id", device2Facility.get(aDeviceId).toString());
                roomLink.put("z_room_id", device2Facility.get(zDeviceId).toString());
                roomLinks.add(roomLink);
                routeRooms.add(new BigDecimal(device2Facility.get(aDeviceId).toString()));
                routeRooms.add(new BigDecimal(device2Facility.get(aDeviceId).toString()));
                //         321251070000000000083701 321191070000000000022214
            }
            // 光路分析 光链路路由， 拓扑连接、光路， 只看这两种
            if ("1131200002".equals(specId)) {
                // 光链路路由, 查询光路
                JSONObject param = new JSONObject();
                param.put("id", new BigDecimal(id));
                JSONObject roads = this.circuitAnalysisDao.queryResOptRoadByRoute(param, ShardingEnum.getShardingEnumByO3(regionId).getBcShardingCode());
                if (roads.getJSONArray("data") != null) {
                    for (int i1 = 0; i1 < roads.getJSONArray("data").size(); i1++) {
                        optIds.add( roads.getJSONArray("data").getJSONObject(i1).getString("id"));
                        road2Region.put(roads.getJSONArray("data").getJSONObject(i1).getString("id"),  ShardingEnum.getShardingEnumByO3(regionId).getBcShardingCode());
                    }
                }
            } else if ("拓扑连接".equals(specName) && name.startsWith("F")) {
                // OTN的需要确认地区
                String otnCity = device2City.get(aDeviceId);
                if (StringUtils.isEmpty(otnCity)) {
                    continue;
                }
                regionId = ShardingEnum.getShardingEnumByRegionName(otnCity).getO3();
                JSONObject param = new JSONObject();
                param.put("code", name);
                JSONObject otnRoutes = this.circuitAnalysisDao.queryResRouteByOptRoad(param, ShardingEnum.getShardingEnumByO3(regionId).getBcShardingCode());
                if (otnRoutes.containsKey("routes") &&  otnRoutes.getJSONArray("routes").size() > 0) {
                    for (int i1 = 0; i1 < otnRoutes.getJSONArray("routes").size(); i1++) {
                        optIds.add( otnRoutes.getJSONArray("routes").getJSONObject(i1).getString("opt_road_id"));
                        road2Region.put(otnRoutes.getJSONArray("routes").getJSONObject(i1).getString("opt_road_id"),  ShardingEnum.getShardingEnumByO3(regionId).getBcShardingCode());

                    }
                }

            } else if  ("1132400006".equals(specId)) {
                optIds.add(id);
                road2Region.put(id,  ShardingEnum.getShardingEnumByO3(regionId).getBcShardingCode());

            }
        }

        Iterator<String> iterator = optIds.iterator();
        while (iterator.hasNext()) {
            String optRoadId = iterator.next();
            JSONObject data = null;
            // 添加缓存 ID 全省唯一
            String shardingCode = road2Region.get(optRoadId);
            JSONObject cacheGraph = BeanUtils.getBean(IpranService.class).queryCache(optRoadId, "opt_2_pipesegment");
            if (cacheGraph != null) {
                data = cacheGraph;
            } else {

                // 调用图谱API
                GraphRequestBuilder builder = new GraphRequestBuilder();
                GraphRequest graphRequest = builder.setApiId("opt_2_pipesegment").setShardingCode(shardingCode)
                        .appendWhereBodyItem(new GraphWhereBodyItem("opt_road","id",optRoadId)).build();
                Graph graph = graphApiService.doApi(graphRequest);
                data = graphService.getData(graph);
                BeanUtils.getBean(IpranService.class).saveCache(data,optRoadId,"opt_2_pipesegment",optRoadId);
            }
            for (int i = data.getJSONArray("nodes").size() - 1; i >=0 ; i--) {
                if (data.getJSONArray("nodes").get(i) == null) {
                    data.getJSONArray("nodes").remove(i);
                }
            }
            graphService.buildReverseTree(data, "光路", "管道段", "opt_road_list");

            List<JSONObject> pipeSegmentList = graphService.filterNodeByLabel(data,"管道段");
            List<JSONObject> roomList = graphService.filterNodeByLabel(data,"机房");
            List<JSONObject> deviceList = graphService.filterNodeByLabel(data,"设备");
            deviceAddAlarmInfo(deviceList);
            resut.getJSONArray("pipe_segments").addAll(pipeSegmentList);
            resut.getJSONArray("rooms").addAll(roomList);
            resut.getJSONArray("devices").addAll(deviceList);
            if (routeRooms.size() >0) {
                JSONObject param = new JSONObject();
                param.put("ids",routeRooms);
                JSONArray tempRooms = this.circuitAnalysisDao.queryNodeRoom(param, shardingCode);
                if (tempRooms != null) {
                    for (int i = 0; i < tempRooms.size(); i++) {
                        tempRooms.getJSONObject(i).put("id",tempRooms.getJSONObject(i).getString("id") );
                    }
                }
                resut.getJSONArray("rooms").addAll(tempRooms);
            }





        }
        JSONObject fakeRoomLink = new JSONObject();
        fakeRoomLink.put("a_room_id", "321141070000000000034366");
        fakeRoomLink.put("z_room_id","321252500000000000833327");
        roomLinks.add(fakeRoomLink);

        resut.getJSONArray("roomLinks").addAll(roomLinks);
        return resut;
    }

    private String analysisRegionId(String id) {
        if (id.startsWith("32125")) {
            // 南京 1
            return  "321122930000000000000006";
        }
        if (id.startsWith("32111")) {
            // 镇江 1
            return  "321122930000000000000037";
        }
        if (id.startsWith("32119")) {
            // 常州
            return  "321122930000000000000069";
        }
        if (id.startsWith("32110")) {
            // 无锡
            return  "321122930000000000000013";
        }
        if (id.startsWith("32112")) {
            // 苏州 1
            return  "321122930000000000000008";
        }
        if (id.startsWith("32113")) {
            // 南通 1
            return  "321122930000000000000024";
        }
        if (id.startsWith("32123")) {
            // 泰州
            return  "321122930000000000000079";
        }
        if (id.startsWith("32114")) {
            // 扬州
            return  "321122930000000000000021";
        }
        if (id.startsWith("32116")) {
            // 徐州
            return  "321122930000000000000046";
        }
        if (id.startsWith("32117")) {
            // ha
            return  "321122930000000000000056";
        }
        if (id.startsWith("32127")) {
            // sq
            return  "321122930000000000000073";
        }
        if (id.startsWith("32118")) {
            // lyg 1
            return  "321122930000000000000063";
        }
        if (id.startsWith("32115")) {
            // yc 1
            return  "321122930000000000000017";
        }
        return null;
    }

    private void analisysComponent(JSONObject link, JSONArray components )  {
        if (link == null ) {
            return;
        }
        JSONArray children = link.getJSONArray("children");
        if (CollectionUtils.isEmpty(children)) {
            components.add(link);
            return;
        } else {
            for (int i = 0; i < children.size(); i++) {
                analisysComponent(children.getJSONObject(i), components);
            }
        }

    }

    public JSONObject queryOtnToPipeSegment(JSONObject request) {
        JSONObject otnRoute = otnService.queryOTNRoute(request);
        otnRoute = translateOtn(otnRoute);
        return otnRoute;
    }

    private JSONObject translateOtn(JSONObject longOtnRoute) {
        JSONObject route = new JSONObject();
        JSONArray otnLinks = longOtnRoute.getJSONArray("links");
        String otnLinksContent = JSONArray.toJSONString(otnLinks);
        JSONArray otnDevices = longOtnRoute.getJSONArray("nodes");
        for (int i = 0; i < otnDevices.size(); i++) {
            String id = otnDevices.getJSONObject(i).getString("id");
            JSONObject param = new JSONObject();
            param.put("neId", id);
            JSONObject device = circuitAnalysisDao.queryResDeviceOTN(param, NRMConstants.SHARDING_GRAPH_DB);
            String resId = null;
            String city = null;
            if (device!=null && CollectionUtils.isNotEmpty(device.getJSONArray("data"))) {
                resId = device.getJSONArray("data").getJSONObject(0).getString("oss_device_id");
                city = device.getJSONArray("data").getJSONObject(0).getString("city");
            }

            if (StringUtils.isNotEmpty(resId)) {
                // 添加机房ID
                param.put("deviceId", new BigDecimal(resId));
                JSONObject facility = circuitAnalysisDao.queryResFacilityByDeviceId(param, ShardingEnum.getShardingEnumByRegionName(city).getBcShardingCode());
                if (facility != null && CollectionUtils.isNotEmpty(facility.getJSONArray("data"))) {
                    otnDevices.getJSONObject(i).put("facility_id", facility.getJSONArray("data").getJSONObject(0).getString("facility_id"));
                }
                otnLinksContent = otnLinksContent.replace(id, resId);
                otnDevices.getJSONObject(i).put("ne_id", id);
                otnDevices.getJSONObject(i).put("id", resId);
                otnDevices.getJSONObject(i).put("city", city);
                otnDevices.getJSONObject(i).put("region", ShardingEnum.getShardingEnumByRegionName(city).getPpmCode());
            }
        }
        route.put("nodes", otnDevices);
        route.put("links", JSONArray.parseArray(otnLinksContent));
        return route;
    }


    public void deviceAddAlarmInfo(List<JSONObject> devices) {
        Map<String, String> query = new HashMap();
        try {
            if (CollectionUtils.isNotEmpty(devices)) {
                for (JSONObject device : devices) {
                    query.put("name", device.getString("name"));
                    if ("省外设备".equals(device.getString("name"))) {
                        continue;
                    }
                    JSONObject device_name_query = circuitAnalysisDao.queryAlarmInfoByDeviceCode(query, NRMConstants.SHARDING_GRAPH_DB);
                    if (null != device_name_query && CollectionUtils.isNotEmpty(device_name_query.getJSONArray("data"))) {
                        device.put("alarm", "Y");
                    } else {
                        query.put("name", device.getString("code"));
                        JSONObject device_code_query = circuitAnalysisDao.queryAlarmInfoByDeviceCode(query, NRMConstants.SHARDING_GRAPH_DB);
                        if (null != device_code_query && CollectionUtils.isNotEmpty(device_code_query.getJSONArray("data"))) {
                            device.put("alarm", "Y");
                        } else {
                            device.put("alarm", "N");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }


    //新增：单电路隐患分析
    public JSONObject queryMSTPCircuit(JSONObject request) {
        List codes = request.getJSONArray("codes");
        String shardingCode=request.getString("shardingCode");


        GraphRequestBuilder builder = new GraphRequestBuilder();

        GraphRequest graphRequest = builder.setApiId("query_mstp_2_pipesegment").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("mstp_circuit","codes",codes))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);

        JSONObject data = graphService.getData(graph);

        List<JSONObject> pipeSegmentList = graphService.filterNodeByLabel(data,"管道段");
        List<JSONObject> cableList = graphService.filterNodeByLabel(data,"光缆");
        List<JSONObject> optRoadList = graphService.filterNodeByLabel(data,"光路");

        graphService.buildReverseTree(data,"光路", "管道段","opt_road_list");
        graphService.buildReverseTree(data,"光路", "光缆","opt_road_list");
        //graphService.mapProperty(data,"管道段", "opt_road_list", mapFun);


        JSONObject result = new JSONObject();

        result.put("optRoads",optRoadList);

        result.put("pipeSegments", pipeSegmentList);
        result.put("cables", cableList);



        List<JSONObject> samePipeSegmentList = pipeSegmentList.stream().filter(p->p.getJSONArray("opt_road_list").size()>1 && object_list_count(p.getJSONArray("opt_road_list")).size()>1).collect(Collectors.toList());
        List<JSONObject> sameCableList = cableList.stream().filter(p->p.getJSONArray("opt_road_list").size()>1 && object_list_count(p.getJSONArray("opt_road_list")).size()>1).collect(Collectors.toList());

        result.put("samePipeSegments", samePipeSegmentList);
        result.put("sameCables", sameCableList);
        List<JSONArray> opt_road_list = samePipeSegmentList.stream().map(p->p.getJSONArray("opt_road_list")).distinct().collect(Collectors.toList());

        List<JSONArray> opt_road_list_cable = sameCableList.stream().map(p->p.getJSONArray("opt_road_list")).distinct().collect(Collectors.toList());



        List<JSONArray> opt_road_list_distinct = new ArrayList<>();
        for(int i =0;i<opt_road_list.size();i++){
            JSONArray opt_road_1 = opt_road_list.get(i);
            if(opt_road_list_distinct.size() ==0){
                opt_road_list_distinct.add(opt_road_1);
            }else{
                if(containTest_list(opt_road_1, opt_road_list_distinct)== false){
                    opt_road_list_distinct.add(opt_road_1);
                }
            }
        }


        List<JSONArray> opt_road_list_cable_distinct = new ArrayList<>();
        for(int i =0;i<opt_road_list_cable.size();i++){
            JSONArray opt_road_1 = opt_road_list_cable.get(i);
            if(opt_road_list_cable_distinct.size() ==0){
                opt_road_list_cable_distinct.add(opt_road_1);
            }else{
                if(containTest_list(opt_road_1, opt_road_list_cable_distinct)== false){
                    opt_road_list_cable_distinct.add(opt_road_1);
                }
            }

        }

        List<JSONObject> samePipeSegmentList_distinct = new ArrayList<>();


        for(int i =0;i<opt_road_list_distinct.size();i++){
            JSONArray opt_road_current = opt_road_list_distinct.get(i);
            JSONObject current_opt_road_pipe = new JSONObject();
            current_opt_road_pipe.put("opt_road_list", opt_road_current);
            current_opt_road_pipe.put("pipeSegments",containTest_all(opt_road_current, samePipeSegmentList));
            samePipeSegmentList_distinct.add(current_opt_road_pipe);
        }

        List<JSONObject> sameCables_distinct = new ArrayList<>();
        for(int i =0;i<opt_road_list_cable_distinct.size();i++){
            JSONArray opt_road_current = opt_road_list_cable_distinct.get(i);
            JSONObject current_opt_road_cable = new JSONObject();
            current_opt_road_cable.put("opt_road_list", opt_road_current);
            current_opt_road_cable.put("sameCables",containTest_all(opt_road_current, sameCableList));
            sameCables_distinct.add(current_opt_road_cable);
        }

        for(int i =0;i<samePipeSegmentList_distinct.size();i++){
            samePipeSegmentList_distinct.get(i).put("result", getResult_opt(JSONObjectUtil.jsonObjectArrayToList(samePipeSegmentList_distinct.get(i).getJSONArray("opt_road_list"))));
            samePipeSegmentList_distinct.get(i).put("count_pipleSegmentList", samePipeSegmentList_distinct.get(i).getJSONArray("pipeSegments").size());

        }
        for(int i =0;i<sameCables_distinct.size();i++){
            sameCables_distinct.get(i).put("result", getResult_opt(JSONObjectUtil.jsonObjectArrayToList(sameCables_distinct.get(i).getJSONArray("opt_road_list"))));
        }
        result.put("samePipeSegments_d", samePipeSegmentList_distinct);
        result.put("sameCables_d", sameCables_distinct);

        return result;
    }

    /**
     * 获取双端进行组装
     * @param local
     * @param request
     * @return
     */
    @Override
    public JSONObject unionCityRoute(JSONObject local, JSONObject request) {
        return null;



    }


    //将opt_road_list 转成 a_code, z_code, a_opt_code, z_opt_code,且a_code < z_code
    public JSONObject getResult(List<JSONObject> opt_road_list){
        String a_code = opt_road_list.get(0).getJSONArray("mstp_circuits").getJSONObject(0).getString("code");
        String a_opt_code = opt_road_list.get(0).getString("code") + "("+opt_road_list.get(0).getString("name") +")";
        String z_code = opt_road_list.get(1).getJSONArray("mstp_circuits").getJSONObject(0).getString("code");
        if(z_code.equals(a_code)){
            if(opt_road_list.get(1).getJSONArray("mstp_circuits").size()>1){
                z_code = opt_road_list.get(1).getJSONArray("mstp_circuits").getJSONObject(1).getString("code");
            }else if(opt_road_list.get(0).getJSONArray("mstp_circuits").size()>1) {
                a_code = opt_road_list.get(0).getJSONArray("mstp_circuits").getJSONObject(1).getString("code");
            }
        }
        String z_opt_code = opt_road_list.get(1).getString("code")+ "("+opt_road_list.get(1).getString("name") +")";;
        if(a_code.compareTo(z_code)>0){
            String t_code = a_code;
            a_code = z_code;
            z_code = t_code;
            t_code = a_opt_code;
            a_opt_code = z_opt_code;
            z_opt_code = t_code;
        }
        JSONObject result = new JSONObject();
        result.put("a_code",a_code);
        result.put("z_code", z_code);
        result.put("a_opt_code",a_opt_code);
        result.put("z_opt_code", z_opt_code);
        return result;
    }

    //新增：获取对应光路
    public JSONObject getResult_opt(List<JSONObject> opt_road_list){
        String a_opt_code = opt_road_list.get(0).getString("code") + "("+opt_road_list.get(0).getString("name") +")";
        String z_opt_code = opt_road_list.get(1).getString("code")+ "("+opt_road_list.get(1).getString("name") +")";;
        JSONObject result = new JSONObject();
        result.put("a_opt_code",a_opt_code);
        result.put("z_opt_code", z_opt_code);
        return result;
    }




    public  List<JSONObject> object_list_count(JSONArray t1){
        List<JSONObject> result_list = new ArrayList<>();
        for(int i =0;i<t1.size();i++){
            if(result_list.size() ==0){
                result_list.add(t1.getJSONObject(i));
            }else{
                if(containTest_list(t1.getJSONObject(i), result_list) == false){
                    result_list.add(t1.getJSONObject(i));
                }
            }
        }
        return result_list;
    }




    public boolean containTest(JSONObject t1, JSONArray t2){
        boolean result= false;
        for(int i =0;i<t2.size();i++){
            if(t1.getString("code").equals(t2.getJSONObject(i).getString("code"))){
                result = true;
                break;
            }
        }
        return result;
    }
    public boolean containTest(JSONArray t1, JSONArray t2){
        boolean result = false;
        int count =0;
        for(int i =0;i<t1.size();i++,count++){
            if(containTest(t1.getJSONObject(i), t2)== false){
                break;
            }
        }
        if(count>=t1.size()){
            result = true;
        }
        return result;
    }

    public boolean containTest_list(JSONArray t1, List<JSONArray> t2){
        boolean result = false;
        int count =0;
        for(int i =0;i<t2.size();i++,count++){
            if(containTest(t1, t2.get(i))==true){
                break;
            }
        }
        if(count< t2.size()){
            result = true;
        }
        return result;
    }

    public boolean containTest_list(JSONObject t1, List<JSONObject> t2){
        boolean result = false;
        for(int i =0;i<t2.size();i++){
            if(t1.getString("code").equals(t2.get(i).getString("code"))){
                result = true;
                break;
            }
        }
        return result;
    }

    public List<JSONObject> containTest_all(JSONArray t1, List<JSONObject> t2){
        List<JSONObject> result = new ArrayList<>();
        for(int i =0;i<t2.size();i++){
            if(containTest(t1, t2.get(i).getJSONArray("opt_road_list"))&& containTest_list(t2.get(i), result) == false){
                result.add(t2.get(i));
            }
        }
        return result;
    }

    public List<JSONObject> getFirstLastDevice(Graph graph, String code){

        List<JSONObject> devices_result =  JSONObjectUtil.jsonObjectArrayToList( getMSTPTopology(graph,code).getJSONArray("nodes"));
        if(devices_result==null || Objects.isNull(devices_result)){
            devices_result = new ArrayList<>();
        }
        List<JSONObject> result = new ArrayList<>();
        JSONObject currentnode = new JSONObject();
        int count =0;
        for(int i =0;i<devices_result.size();i++){
            if(devices_result.get(i).getString("seq").equals(count+"") && !Objects.isNull(devices_result.get(i).getString("pos_x"))){
                currentnode = devices_result.get(i);
                currentnode.put("position", "起点");
                result.add(currentnode);
                break;
            }else {count++;continue;}
        }
        count =devices_result.size()-1;
        for(int i =devices_result.size()-1 ;i>=0;i--){
            if(devices_result.get(i).getString("seq").equals(count+"")  && !Objects.isNull(devices_result.get(i).getString("pos_x"))) {
                currentnode = devices_result.get(i);
                currentnode.put("position", "终点");
                if(((!Objects.isNull(currentnode.getString("facility_name")) &&currentnode.getString("facility_name").contains("用户") )|| (!Objects.isNull(currentnode.getString("address"))&&currentnode.getString("address").contains("用户"))) && result.size()>0) {
                    currentnode.put("position", "起点");
                    result.get(0).put("position", "终点");
                }
                result.add(currentnode);
                break;
            }else {count--;continue;}
        }

        return result;
    }

    public List<JSONObject> getFirstLastDevice(Graph graph, List<String> codes){
        List<JSONObject> result = new ArrayList<>();
        for(int i =0;i<codes.size();i++){
            List<JSONObject> jsonObject = getFirstLastDevice(graph, codes.get(i));
            if(!Objects.isNull(jsonObject) && jsonObject!=null){
                result.addAll(jsonObject);
            }
        }
        return result;
    }


}
