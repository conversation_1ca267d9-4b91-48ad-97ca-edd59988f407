package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OptRoadPairServiceImpl implements OptRoadPairService {

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    FrontHoleService frontHoleService;

    @Autowired
    OptGroupDao optGroupDao;

    public List<JSONObject> findSameList(List<JSONObject> list1, List<JSONObject> list2) {
        Map<String,JSONObject> map1 = new HashMap<>();
        for (JSONObject l : list1) {
            map1.put(l.getString("id"),l);
        }
        Set<String> ids_1 = list1.stream().map(d->d.getString("id")).collect(Collectors.toSet());
        Set<String> ids_2 = list2.stream().map(d->d.getString("id")).collect(Collectors.toSet());
        ids_1.retainAll(ids_2);
        List<JSONObject> result = new ArrayList<>();
        for (String id : ids_1) {
            result.add(map1.get(id));
        }
        return result;
    }

    public JSONObject buildOptRoadPair(JSONObject optRoad1,JSONObject optRoad2) {

        JSONObject optRoadPair = new JSONObject();

        optRoadPair.put("a_opt_code", optRoad1.getString("code")+"("+optRoad1.getString("name")+")");
        optRoadPair.put("z_opt_code", optRoad2.getString("code")+"("+optRoad2.getString("name")+")");
        List<JSONObject> pipeSegments1  = Optional.ofNullable(optRoad1.getJSONArray("pipe_segment_list")).orElse(new JSONArray()).toJavaList(JSONObject.class);
        List<JSONObject> pipeSegments2  = Optional.ofNullable(optRoad2.getJSONArray("pipe_segment_list")).orElse(new JSONArray()).toJavaList(JSONObject.class);
        List<JSONObject> samePipeSegments  = findSameList(pipeSegments1, pipeSegments2);
        optRoadPair.put("same_pipe_segment_list", samePipeSegments);

        List<JSONObject> cables1  = Optional.ofNullable(optRoad1.getJSONArray("cable_list")).orElse(new JSONArray()).toJavaList(JSONObject.class);
        List<JSONObject> cables2  = Optional.ofNullable(optRoad2.getJSONArray("cable_list")).orElse(new JSONArray()).toJavaList(JSONObject.class);
        List<JSONObject> sameCables  = findSameList(cables1, cables2);
        optRoadPair.put("same_cable_list", sameCables);
        optRoadPair.put("opt_road_list",Arrays.asList(optRoad1,optRoad2));
        return optRoadPair;
    }

    public List<JSONObject> getSamePipeSegments_d(List<JSONObject> optRoadPairList){
        List<JSONObject> samePipeSegments_d = new ArrayList<>();
        for (JSONObject optRoadPair : optRoadPairList) {
            JSONObject o = new JSONObject();
            o.put("result", optRoadPair);
            o.put("pipeSegments", optRoadPair.getJSONArray("same_pipe_segment_list").toJavaList(JSONObject.class));
            o.put("opt_road_list", optRoadPair.getJSONArray("opt_road_list").toJavaList(JSONObject.class));
            if (ObjectUtil.isNotEmpty(optRoadPair.getJSONArray("same_pipe_segment_list"))) {
                samePipeSegments_d.add(o);
            }
        }
        return samePipeSegments_d;
    }

    public List<JSONObject> getSameCables_d(List<JSONObject> optRoadPairList) {
        List<JSONObject> sameCables_d = new ArrayList<>();
        for (JSONObject optRoadPair : optRoadPairList) {
            JSONObject o = new JSONObject();
            o.put("result", optRoadPair);
            o.put("sameCables", optRoadPair.getJSONArray("same_cable_list").toJavaList(JSONObject.class));
            o.put("opt_road_list", optRoadPair.getJSONArray("opt_road_list").toJavaList(JSONObject.class));
            // if (optRoadPair.getJSONArray("same_cable_list").size()>0) {
            if (ObjectUtil.isNotEmpty(optRoadPair.getJSONArray("same_cable_list"))) {
                sameCables_d.add(o);
            }

        }
        return sameCables_d;
    }


    public void Result_dealt(JSONObject result){
        int count_samePipeSegments = 0;

        List<JSONObject> samePipeSegmentsOptPairList = result.getJSONArray("samePipeSegments_d").toJavaList(JSONObject.class); // 同管道的光路对
        List<JSONObject> sameCableOptPairList = result.getJSONArray("sameCables_d").toJavaList(JSONObject.class);


        Set<String> white_codes = get_white_codes(result);

        int whiteCount = 0;
        // 计算同管道个数
        for(JSONObject optRoutePair : samePipeSegmentsOptPairList){
            int count_pipleSegmentList = 0;
            int count_frontPipeSegmentList = 0;
            log.info("optRoutePair---"+optRoutePair);



            List<JSONObject> pipeSegmentList = optRoutePair.getJSONArray("pipeSegments").toJavaList(JSONObject.class);
            for(JSONObject pipeSegment : pipeSegmentList){
                boolean isFront = false;
                if (ObjectUtil.isNotEmpty(pipeSegment) && ObjectUtil.isNotEmpty(pipeSegment.get("is_front"))) {
                    isFront = pipeSegment.getBoolean("is_front");
                }


                /*
                不允许忽略

                if(white_codes.contains(pipeSegment.getString("code"))) {
                    pipeSegment.put("code", pipeSegment.getString("code") + "【已忽略】");
                    whiteCount++;
                } else if (isFront) {
                    pipeSegment.put("code", pipeSegment.getString("code") + "【局前井】");
                    count_frontPipeSegmentList ++;
                }else{
                    count_pipleSegmentList++;
                }

                 */
                if (isFront) {
                    pipeSegment.put("code", pipeSegment.getString("code") + "【局前井】");
                    count_frontPipeSegmentList ++;
                }else{
                    count_pipleSegmentList++;
                }

            }
            optRoutePair.put("count_pipleSegmentList", count_pipleSegmentList);
            optRoutePair.put("count_frontPipeSegmentList", count_frontPipeSegmentList);
            count_samePipeSegments += count_pipleSegmentList;
        }
        result.put("count_samePipeSegments", count_samePipeSegments);
        int cableWhiteCount = 0;
        int count_sameCables = 0;
        for (JSONObject sameCable: sameCableOptPairList) {
            log.info("sameCable---"+sameCable);
            for(JSONObject cable : sameCable.getJSONArray("sameCables").toJavaList(JSONObject.class)){
                if(white_codes.contains(cable.getString("code"))){
                    cable.put("code", cable.getString("code")+"   ----已忽略");
                    cableWhiteCount++;
                }
                else count_sameCables++;
            }
        }
        result.put("count_sameCables", count_sameCables);
        result.put("whiteCount", whiteCount);
        result.put("cableWhiteCount", cableWhiteCount);
    }

    @Override
    public JSONObject queryOptRoadByFNumber(String fCode, String ds) {
        JSONObject optRoadGroupMember = new JSONObject();
        optRoadGroupMember.put("route_no",fCode);
        optRoadGroupMember.put("code",fCode);
        List<JSONObject> optGroupElements = new ArrayList<>();
        optGroupElements.add(optRoadGroupMember);
        return queryOptRoadsToPipeSegment(optGroupElements, ds);
    }

    public Set<String> get_white_codes(JSONObject request){
        PageResponse<JSONObject> pageResponse_pipleSegment =  custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_query(new JSONObject() ,100000,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        JSONObjectUtil.convertBigNumberToString(pageResponse_pipleSegment.getData());
        Set<String> pipeSegment_white_code = pageResponse_pipleSegment.getData().stream().map(d->d.getString("code")).collect(Collectors.toSet());
        return pipeSegment_white_code;
    }

    public JSONObject queryOptRoadsToPipeSegment(List<JSONObject> optGroupElements, String ds) {

        String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds);
        // List<JSONObject> optGroupElements = (List<JSONObject>) request.get("optGroupElements");
        List<String> codes = new ArrayList<>();
        optGroupElements.forEach(t->codes.add(t.getString("code")));

        Map<String,String> routeNoMap = new HashMap<>();
        optGroupElements.forEach(t->{
            String routeNo = t.getString("route_no");
            routeNoMap.put(t.getString("code"), routeNo);
        });


        // String shardingCode=request.getString("shardingCode");


        GraphRequestBuilder builder = new GraphRequestBuilder();

        GraphRequest graphRequest = builder.setApiId("query_opt_roads_2_pipesegment").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("opt_road","codes",codes))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);

        List<JSONObject> pipeSegmentList = graphService.filterNodeByLabel(data,"管道段");
        List<JSONObject> deviceList = graphService.filterNodeByLabel(data,"设备");
        List<JSONObject> cableList = graphService.filterNodeByLabel(data,"光缆");
        List<JSONObject> jxgxList = graphService.filterNodeByLabel(data,"局向光纤");
        // List<JSONObject> localFiberOptList = graphService.filterNodeByLabel(data,"局向光纤");
        List<JSONObject> optRoadList = graphService.filterNodeByLabel(data,"光路");

        frontHoleService.computeFrontHole(pipeSegmentList,deviceList); // 计算局前井

        graphService.buildReverseTree(data,"光路", "管道段","opt_road_list");
        graphService.mapProperty(data, "管道段","opt_road_list", mapFun);
        graphService.buildReverseTree(data,"光路", "光缆","opt_road_list");
        graphService.mapProperty(data,"光缆", "opt_road_list", mapFun);

        graphService.buildTree(data,"局向光纤", "管道段","pipe_segment_list");
        graphService.mapProperty(data,"局向光纤", "pipe_segment_list", mapFun);


        graphService.buildTree(data,"光路","管道段","pipe_segment_list");
        graphService.mapProperty(data,"光路", "pipe_segment_list", mapFun);
        graphService.buildTree(data,"光路","光缆","cable_list");
        graphService.mapProperty(data,"光路", "cable_list", mapFun);

        JSONObject result = new JSONObject();
        result.put("optRoads",optRoadList);
        List<JSONObject> sameoptRoads = optRoadList.stream().filter(optRoad->ObjectUtil.isNotEmpty(optRoad.getJSONArray("mstp_circuits"))&& optRoad.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        result.put("sameoptRoads", sameoptRoads);
        result.put("pipeSegments", pipeSegmentList);
        setupJXGQPos(jxgxList,deviceList);
        jxgxList = jxgxList.stream().filter(jx->jx.getBoolean("empty")).collect(Collectors.toList());
        result.put("jxgxList", jxgxList);
        result.put("cables", cableList);
        result.put("devices", deviceList);
        List<JSONObject> samePipeSegmentList = pipeSegmentList.stream().filter(p-> ObjectUtil.isNotEmpty(p.getJSONArray("opt_road_list"))&& p.getJSONArray("opt_road_list").size()>1).collect(Collectors.toList());
        List<JSONObject> sameCableList = cableList.stream().filter(p->ObjectUtil.isNotEmpty(p.getJSONArray("opt_road_list"))&& p.getJSONArray("opt_road_list").size()>1 ).collect(Collectors.toList());
        result.put("samePipeSegments", samePipeSegmentList); //
        result.put("sameCables", sameCableList);



        // 计算光路对
        List<JSONObject> optRoadPairList = new ArrayList<>();
        for (JSONObject optRoad1 : optRoadList) {
            for (JSONObject optRoad2 : optRoadList) {
                String aOptCode = optRoad1.getString("code");
                String zOptCode = optRoad2.getString("code");
                String aRouteNo = routeNoMap.get(aOptCode);
                String zRouteNo = routeNoMap.get(zOptCode);
                if (aRouteNo.equals(zRouteNo)) continue; // 如果2条光路属于同一个路由
                if (aOptCode.compareTo(zOptCode)>=0) continue;  // A光路编码>Z光路编码的时候不生成光路对,防止重复生成光路对
                JSONObject optRoadPair = buildOptRoadPair(optRoad1, optRoad2);
                optRoadPairList.add(optRoadPair);
            }
        }

        // 计算光路对重复管道段
        result.put("samePipeSegments_d", getSamePipeSegments_d(optRoadPairList));


        // 计算光路对重复光路
        result.put("sameCables_d", getSameCables_d(optRoadPairList));



        //新加的，光路清单
        List<String> opt_code_list = optRoadList.stream().map(p->p.getString("code")).collect(Collectors.toList());
        result.put("opt_code_list",opt_code_list);
        result.put("ds",ds);
        Result_dealt(result);
        return result;
    }

    Function<JSONObject,JSONObject> mapFun = (d)->{
        JSONObject result = new JSONObject();
        Set<String> keys = d.keySet();
        if (ObjectUtil.isNotEmpty(keys)) {
            // log.info("keys={}",keys.toString());
            for (String key: keys) {
                if (! (d.get(key) instanceof Collection)) {
                    result.put(key,d.get(key));
                };
            }
        }
        return result;
    };

    void setupJXGQPos(List<JSONObject> jxgxList,List<JSONObject> deviceList) {
        Map<String,JSONObject> deviceMap=new HashMap<>();
        for (JSONObject device: deviceList) {
            deviceMap.put(device.getString("id"), device);
        }

        for (JSONObject jxgx: jxgxList) {
            JSONObject aDevice = deviceMap.get(jxgx.getString("a_physic_device_id"));
            JSONObject zDevice = deviceMap.get(jxgx.getString("z_physic_device_id"));
            jxgx.put("a_pos_x", ObjectUtil.isNull(aDevice)||ObjectUtil.isNull(aDevice.getDouble("pos_x"))||ObjectUtil.isEmpty(aDevice.getDouble("pos_x"))? "0":aDevice.getDouble("pos_x"));
            jxgx.put("a_pos_y", ObjectUtil.isNull(aDevice)||ObjectUtil.isNull(aDevice.getDouble("pos_y"))? "0":aDevice.getDouble("pos_y"));
            jxgx.put("z_pos_x", ObjectUtil.isNull(zDevice)||ObjectUtil.isNull(zDevice.getString("pos_x"))||ObjectUtil.isEmpty(zDevice.getDouble("pos_x"))? "0":zDevice.getDouble("pos_x"));
            jxgx.put("z_pos_y", ObjectUtil.isNull(zDevice)||ObjectUtil.isNull(zDevice.getDouble("pos_y"))? "0":zDevice.getDouble("pos_y"));
            if (ObjectUtil.isEmpty(jxgx.get("pipe_segment_list"))) {
                jxgx.put("empty", true);
            }else{
                jxgx.put("empty", false);
            }
        }
    }



    public void intoR_common(List<JSONObject> samePipeSegments_d, List<List<JSONObject>> result){
        if(samePipeSegments_d.size()>0) {
            //对于每个同管道组
            for (JSONObject samePipeSegment : samePipeSegments_d) {
                List<JSONObject> opt_road_list = samePipeSegment.getJSONArray("opt_road_list");
                if (result.size() == 0) {
                    result.add(opt_road_list);
                    continue;
                }else{
                    int flag = 0;
                    for (JSONObject opt_road : opt_road_list) {
                        //结果组如果为空，则直接加
                        //如果组内已有数据，则遍历每个光路，如果有相同的光路编码，则当前组直接加组内所有元素
                        for (List<JSONObject> result_group : result) {
                            for (JSONObject result_group_element : result_group) {
                                if (opt_road.getString("code").equals(result_group_element.getString("code"))) {
                                    result_group.addAll(opt_road_list);
                                    flag = 1;
                                    break;
                                }
                            }
                            if(flag == 1){
                                break;
                            }
                        }
                        if(flag == 1){
                            break;
                        }
                    }
                    if(flag == 0){
                        result.add(opt_road_list);
                    }
                }
            }
        }
    }

    public int getRouteNum(List<JSONObject> samePipeSegments_d, List<JSONObject> sameCables_d, List<JSONObject> optList){
        List<List<JSONObject>> result = new ArrayList<>();
        intoR_common(samePipeSegments_d, result);
        intoR_common(sameCables_d,result);
        if(optList.size()>0){
            for(JSONObject opt : optList){
                int flag = 0;
                for (List<JSONObject> result_group : result) {
                    for (JSONObject result_group_element : result_group) {
                        if (opt.getString("code").equals(result_group_element.getString("code"))) {
                            flag = 1;
                            break;
                        }
                    }
                    if(flag == 1){
                        break;
                    }
                }
                if(flag == 0){
                    List<JSONObject> currentOpt = new ArrayList<>();
                    currentOpt.add(opt);
                    result.add(currentOpt);
                }
            }
        }
        return result.size();
    }

    public List<List<JSONObject>> getRoute_Num(List<JSONObject> samePipeSegments_d, List<JSONObject> sameCables_d, List<JSONObject> optList){
        List<List<JSONObject>> result = new ArrayList<>();
        if(sameCables_d.size() ==0){
            return result;
        }
        intoR_common(samePipeSegments_d, result);
        intoR_common(sameCables_d,result);
        return result;
    }

    public JSONObject opt_pair_analyse(List<JSONObject> optGroupElements, String ds) {
        JSONObject result = new JSONObject();
        result.put("ds", ds);

        // 1. 基础验证
        BasicValidationResult validation = performBasicValidation(optGroupElements);
        if (!validation.isValid()) {
            result.put("check_result", validation.getErrorType());
            result.put("exception_info", validation.getErrorMessage());
            return result;
        }

        // 2. 预处理光路组元素
        List<JSONObject> processedElements = preprocessOptGroupElements(optGroupElements);

        // 3. 路由数量检查
        long routeCount = countDistinctRoutes(processedElements);
        if (routeCount <= 1) {
            result.put("check_result", "单路由");
            result.put("exception_info", "单路由");
            return result;
        }

        // 4. 获取网络拓扑数据
        JSONObject topologyData = queryOptRoadsToPipeSegment(processedElements, ds);

        // 5. 分析网络拓扑，检测各种问题
        NetworkAnalysisResult analysisResult = analyzeNetworkTopology(topologyData);

        // 6. 生成最终结果
        result.putAll(topologyData);
        result.put("check_result", analysisResult.getFinalResult());
        result.put("exception_info", analysisResult.getExceptionInfo());

        return result;
    }

    /**
     * 基础验证结果类
     */
    private static class BasicValidationResult {
        private boolean valid;
        private String errorType;
        private String errorMessage;

        public BasicValidationResult(boolean valid, String errorType, String errorMessage) {
            this.valid = valid;
            this.errorType = errorType;
            this.errorMessage = errorMessage;
        }

        public boolean isValid() { return valid; }
        public String getErrorType() { return errorType; }
        public String getErrorMessage() { return errorMessage; }
    }

    /**
     * 执行基础验证
     */
    private BasicValidationResult performBasicValidation(List<JSONObject> optGroupElements) {
        if (Objects.isNull(optGroupElements) || !ObjectUtil.isNotEmpty(optGroupElements) || optGroupElements.isEmpty()) {
            return new BasicValidationResult(false, "无光路", "无光路");
        }

        if (optGroupElements.size() == 1) {
            return new BasicValidationResult(false, "单光路", "单光路");
        }

        return new BasicValidationResult(true, null, null);
    }

    /**
     * 预处理光路组元素，设置默认路由号
     */
    private List<JSONObject> preprocessOptGroupElements(List<JSONObject> optGroupElements) {
        optGroupElements.forEach(element -> {
            String routeNo = element.getString("route_no");
            if (ObjectUtil.isEmpty(routeNo)) {
                element.put("route_no", element.getString("code"));
            }
        });
        return optGroupElements;
    }

    /**
     * 统计不同路由数量
     */
    private long countDistinctRoutes(List<JSONObject> optGroupElements) {
        return optGroupElements.stream()
                .map(e -> e.getString("route_no"))
                .distinct()
                .count();
    }

    public void opt_pair_route_no_analyse(List<JSONObject> optGroupElements, String ds) {

        Map<String, JSONObject> optGroupElementsMap = optGroupElements.stream()
                .collect(Collectors.toMap(
                        json -> json.getString("code"),
                        json -> json));

        // 记录用户手动设置的路由号，避免被自动分配覆盖
        Map<String, String> userDefinedRouteNos = new HashMap<>();
        optGroupElements.forEach(element -> {
            String code = element.getString("code");
            String routeNo = element.getString("route_no");
            // 如果路由号不为空且不等于光路编码，则认为是用户手动设置的
            if (ObjectUtil.isNotEmpty(routeNo) && !routeNo.equals(code)) {
                userDefinedRouteNos.put(code, routeNo);
                log.info("检测到用户手动设置的路由号: 光路编码={}, 路由号={}", code, routeNo);
            }
        });

        JSONObject result =new JSONObject();

        result.put("ds", ds);
        if(ObjectUtil.isNotNull(optGroupElements) && ObjectUtil.isNotEmpty(optGroupElements) && optGroupElements.size()!=0){
            optGroupElements.forEach(t->{
                String routeNo = t.getString("route_no");
                if (ObjectUtil.isEmpty(routeNo)) {
                    t.put("route_no", t.getString("code"));
                }
            });
            optGroupElements.stream().map(e->e.getString("route_no")).distinct().count();
            result = queryOptRoadsToPipeSegment(optGroupElements, ds);
            List<List<JSONObject>> route_no_result = getRoute_Num(result.getJSONArray("samePipeSegments_d"), result.getJSONArray("sameCables_d"), result.getJSONArray("optRoads"));
            for(int i =0;i<route_no_result.size();i++){
                for(JSONObject route : route_no_result.get(i)){
                    JSONObject currentRoute = optGroupElementsMap.get(route.getString("code"));
                    if(ObjectUtil.isNotNull(currentRoute)){
                        String code = currentRoute.getString("code");

                        // 检查是否有用户手动设置的路由号
                        if (userDefinedRouteNos.containsKey(code)) {
                            // 保留用户手动设置的路由号，不进行自动分配
                            String userRouteNo = userDefinedRouteNos.get(code);
                            currentRoute.put("route_no", userRouteNo);
                            log.info("保留用户手动设置的路由号: 光路编码={}, 路由号={}", code, userRouteNo);
                        } else {
                            // 只对未手动设置的光路进行自动路由号分配
                            currentRoute.put("route_no", i+1);
                            log.info("自动分配路由号: 光路编码={}, 路由号={}", code, i+1);
                        }

                        currentRoute.put("opt_code", code);
                        optGroupDao.pm_opt_road_group_element_update(currentRoute,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                        optGroupElementsMap.remove(code);
                    }
                }
            }
            if(route_no_result.size() !=0){
                optGroupElementsMap.forEach((k, v) -> {
                    // 对于未在路由分析结果中的光路，也要检查是否有用户手动设置的路由号
                    if (userDefinedRouteNos.containsKey(k)) {
                        // 保留用户手动设置的路由号
                        String userRouteNo = userDefinedRouteNos.get(k);
                        v.put("route_no", userRouteNo);
                        log.info("保留未在路由分析结果中的光路的用户手动路由号: 光路编码={}, 路由号={}", k, userRouteNo);
                    } else {
                        // 对于未手动设置的光路，设置为null
                        v.put("route_no", null);
                        log.info("清空未在路由分析结果中的光路的自动路由号: 光路编码={}", k);
                    }
                    v.put("opt_code", v.getString("code"));
                    optGroupDao.pm_opt_road_group_element_update(v,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                });
            }
        }
    }

    /**
     * 网络分析结果类
     */
    private static class NetworkAnalysisResult {
        private String finalResult;
        private String exceptionInfo;
        private JSONObject baseProblemCounts;
        private JSONObject exemptionTags;

        public NetworkAnalysisResult(String finalResult, String exceptionInfo,
                                   JSONObject baseProblemCounts, JSONObject exemptionTags) {
            this.finalResult = finalResult;
            this.exceptionInfo = exceptionInfo;
            this.baseProblemCounts = baseProblemCounts;
            this.exemptionTags = exemptionTags;
        }

        public String getFinalResult() { return finalResult; }
        public String getExceptionInfo() { return exceptionInfo; }
        public JSONObject getBaseProblemCounts() { return baseProblemCounts; }
        public JSONObject getExemptionTags() { return exemptionTags; }
    }

    /**
     * 分析网络拓扑，检测各种问题
     */
    private NetworkAnalysisResult analyzeNetworkTopology(JSONObject topologyData) {
        // 1. 检测基础问题
        BaseProblemDetector detector = new BaseProblemDetector();
        JSONObject baseProblemCounts = detector.detectBaseProblems(topologyData);

        // 2. 判断豁免情况
        ExemptionAnalyzer analyzer = new ExemptionAnalyzer();
        JSONObject exemptionTags = analyzer.analyzeExemptions(baseProblemCounts, topologyData);

        // 3. 计算最终结果
        ResultCalculator calculator = new ResultCalculator();
        String finalResult = calculator.calculateFinalResult(baseProblemCounts, exemptionTags);

        // 4. 生成异常信息描述
        ExceptionInfoGenerator generator = new ExceptionInfoGenerator();
        String exceptionInfo = generator.generateExceptionInfo(baseProblemCounts, exemptionTags, topologyData);

        return new NetworkAnalysisResult(finalResult, exceptionInfo, baseProblemCounts, exemptionTags);
    }

    /**
     * 基础问题检测器
     */
    private static class BaseProblemDetector {
        public JSONObject detectBaseProblems(JSONObject topologyData) {
            JSONObject problemCounts = new JSONObject();

            JSONArray optRoads = topologyData.getJSONArray("optRoads");
            JSONArray sameCables_d = topologyData.getJSONArray("sameCables_d");
            JSONArray samePipeSegments_d = topologyData.getJSONArray("samePipeSegments_d");

            // 检测缺管道问题
            int missingPipeCount = detectMissingPipeProblems(optRoads);
            problemCounts.put("problem_missing_pipe", missingPipeCount);

            // 检测同光缆问题
            int sameCableCount = detectSameCableProblems(sameCables_d);
            problemCounts.put("problem_same_cable", sameCableCount);

            // 检测同管道问题
            int samePipeCount = detectSamePipeProblems(samePipeSegments_d);
            problemCounts.put("problem_same_pipe", samePipeCount);

            return problemCounts;
        }

        private int detectMissingPipeProblems(JSONArray optRoads) {
            if (ObjectUtil.isEmpty(optRoads)) return 0;

            int count = 0;
            for (int i = 0; i < optRoads.size(); i++) {
                JSONObject optRoad = optRoads.getJSONObject(i);
                List<JSONObject> pipeSegmentList = JSONObjectUtil.jsonObjectArrayToList(
                    optRoad.getJSONArray("pipe_segment_list"));
                if (ObjectUtil.isEmpty(pipeSegmentList)) {
                    count++;
                }
            }
            return count;
        }

        private int detectSameCableProblems(JSONArray sameCables_d) {
            if (ObjectUtil.isEmpty(sameCables_d)) return 0;

            int count = 0;
            for (int i = 0; i < sameCables_d.size(); i++) {
                JSONObject cableGroup = sameCables_d.getJSONObject(i);
                JSONArray sameCables = cableGroup.getJSONArray("sameCables");
                if (ObjectUtil.isNotEmpty(sameCables)) {
                    count += sameCables.size();
                }
            }
            return count;
        }

        private int detectSamePipeProblems(JSONArray samePipeSegments_d) {
            if (ObjectUtil.isEmpty(samePipeSegments_d)) return 0;

            int count = 0;
            for (int i = 0; i < samePipeSegments_d.size(); i++) {
                JSONObject pipeGroup = samePipeSegments_d.getJSONObject(i);
                List<JSONObject> pipeSegments = pipeGroup.getJSONArray("pipeSegments").toJavaList(JSONObject.class);
                if (ObjectUtil.isNotEmpty(pipeSegments)) {
                    count += pipeSegments.size();
                }
            }
            return count;
        }
    }

    /**
     * 豁免分析器
     */
    private static class ExemptionAnalyzer {
        public JSONObject analyzeExemptions(JSONObject baseProblemCounts, JSONObject topologyData) {
            JSONObject exemptions = new JSONObject();

            // 分析局内光路豁免
            String indoorOpticalExemption = analyzeIndoorOpticalExemption(topologyData);
            if (ObjectUtil.isNotEmpty(indoorOpticalExemption)) {
                exemptions.put("exemption_indoor_optical", indoorOpticalExemption);
            }

            // 分析局前井豁免
            String frontWellExemption = analyzeFrontWellExemption(topologyData);
            if (ObjectUtil.isNotEmpty(frontWellExemption)) {
                exemptions.put("exemption_front_well", frontWellExemption);
            }

            // 分析局内光缆豁免（待实现）
            String indoorCableExemption = analyzeIndoorCableExemption(topologyData);
            if (ObjectUtil.isNotEmpty(indoorCableExemption)) {
                exemptions.put("exemption_indoor_cable", indoorCableExemption);
            }

            return exemptions;
        }

        private String analyzeIndoorOpticalExemption(JSONObject topologyData) {
            JSONArray optRoads = topologyData.getJSONArray("optRoads");
            if (ObjectUtil.isEmpty(optRoads)) return null;

            List<String> indoorOpticalPaths = new ArrayList<>();
            for (int i = 0; i < optRoads.size(); i++) {
                JSONObject optRoad = optRoads.getJSONObject(i);
                String aSiteId = optRoad.getString("a_site_id");
                String zSiteId = optRoad.getString("z_site_id");

                if (ObjectUtil.isNotEmpty(aSiteId) && ObjectUtil.isNotEmpty(zSiteId) && aSiteId.equals(zSiteId)) {
                    indoorOpticalPaths.add(optRoad.getString("code"));
                }
            }

            return indoorOpticalPaths.isEmpty() ? null : String.join(",", indoorOpticalPaths);
        }

        private String analyzeFrontWellExemption(JSONObject topologyData) {
            JSONArray samePipeSegments_d = topologyData.getJSONArray("samePipeSegments_d");
            if (ObjectUtil.isEmpty(samePipeSegments_d)) return null;

            List<String> frontWellExemptions = new ArrayList<>();
            for (int i = 0; i < samePipeSegments_d.size(); i++) {
                JSONObject optPair = samePipeSegments_d.getJSONObject(i);
                int count_frontPipeSegmentList = Optional.ofNullable(
                    optPair.getInteger("count_frontPipeSegmentList")).orElse(0);

                if (count_frontPipeSegmentList > 0 && count_frontPipeSegmentList <= 10) {
                    JSONArray opt_road_list = optPair.getJSONArray("opt_road_list");
                    String optRoadCodes = formatOptRoadList(opt_road_list);
                    frontWellExemptions.add(optRoadCodes + "(共" + count_frontPipeSegmentList + "个局前井)");
                }
            }

            return frontWellExemptions.isEmpty() ? null : String.join(";", frontWellExemptions);
        }

        private String analyzeIndoorCableExemption(JSONObject topologyData) {
            // TODO: 实现局内光缆豁免判断逻辑
            // 需要判断同光缆是否在同一局站内
            return null;
        }

        private String formatOptRoadList(JSONArray optRoadList) {
            if (ObjectUtil.isEmpty(optRoadList)) return "";

            StringBuilder result = new StringBuilder();
            for (int j = 0; j < optRoadList.size(); j++) {
                result.append(optRoadList.getJSONObject(j).getString("code"));
                if (j < optRoadList.size() - 1) {
                    result.append("和");
                }
            }
            return result.toString();
        }
    }

    /**
     * 结果计算器
     */
    private static class ResultCalculator {
        public String calculateFinalResult(JSONObject baseProblemCounts, JSONObject exemptionTags) {
            List<String> finalProblems = new ArrayList<>();

            // 检查缺管道问题
            int missingPipeCount = baseProblemCounts.getIntValue("problem_missing_pipe");
            if (missingPipeCount > 0) {
                finalProblems.add("缺管道");
            }

            // 检查同光缆问题 - 考虑局内光缆豁免
            int sameCableCount = baseProblemCounts.getIntValue("problem_same_cable");
            if (sameCableCount > 0) {
                String indoorCableExemption = exemptionTags.getString("exemption_indoor_cable");
                if (ObjectUtil.isEmpty(indoorCableExemption)) {
                    finalProblems.add("同光缆");
                }
            }

            // 检查同管道问题 - 考虑局前井豁免
            int samePipeCount = baseProblemCounts.getIntValue("problem_same_pipe");
            if (samePipeCount > 0) {
                String frontWellExemption = exemptionTags.getString("exemption_front_well");
                if (ObjectUtil.isEmpty(frontWellExemption)) {
                    finalProblems.add("同管道");
                }
            }

            // 检查局内光路豁免标签
            String indoorOpticalExemption = exemptionTags.getString("exemption_indoor_optical");
            if (ObjectUtil.isNotEmpty(indoorOpticalExemption)) {
                finalProblems.add("局内光路");
            }

            // 检查局前井豁免标签
            String frontWellExemption = exemptionTags.getString("exemption_front_well");
            if (ObjectUtil.isNotEmpty(frontWellExemption)) {
                finalProblems.add("同局前井");
            }

            // 返回最终结果
            if (finalProblems.isEmpty()) {
                return "正常";
            } else if (finalProblems.size() == 1) {
                return finalProblems.get(0);
            } else {
                return String.join("+", finalProblems);
            }
        }
    }

    /**
     * 异常信息生成器
     */
    private static class ExceptionInfoGenerator {
        public String generateExceptionInfo(JSONObject baseProblemCounts, JSONObject exemptionTags, JSONObject topologyData) {
            StringBuilder info = new StringBuilder();

            // 生成基础问题描述
            appendBaseProblemInfo(info, baseProblemCounts, topologyData);

            // 生成豁免标签描述
            appendExemptionInfo(info, exemptionTags);

            return info.length() > 0 ? info.toString() : "正常";
        }

        /**
         * 生成原子化问题明细列表
         */
        public List<JSONObject> generateAtomicProblemDetails(JSONObject baseProblemCounts, JSONObject exemptionTags, JSONObject topologyData) {
            List<JSONObject> problemDetails = new ArrayList<>();

            // 生成基础问题明细
            addBaseProblemDetails(problemDetails, topologyData);

            return problemDetails;
        }

        /**
         * 添加基础问题明细
         */
        private void addBaseProblemDetails(List<JSONObject> problemDetails, JSONObject topologyData) {
            // 缺管道问题明细
            addMissingPipeDetails(problemDetails, topologyData);

            // 同光缆问题明细
            addSameCableDetails(problemDetails, topologyData);

            // 同管道问题明细
            addSamePipeDetails(problemDetails, topologyData);
        }

        /**
         * 添加缺管道问题明细
         */
        private void addMissingPipeDetails(List<JSONObject> problemDetails, JSONObject topologyData) {
            JSONArray optRoads = topologyData.getJSONArray("optRoads");
            if (ObjectUtil.isEmpty(optRoads)) return;

            for (int i = 0; i < optRoads.size(); i++) {
                JSONObject optRoad = optRoads.getJSONObject(i);
                List<JSONObject> pipeSegmentList = JSONObjectUtil.jsonObjectArrayToList(
                    optRoad.getJSONArray("pipe_segment_list"));

                if (ObjectUtil.isEmpty(pipeSegmentList)) {
                    // 检查是否为局内光路
                    String aSiteId = optRoad.getString("a_site_id");
                    String zSiteId = optRoad.getString("z_site_id");
                    boolean isIndoorOptical = ObjectUtil.isNotEmpty(aSiteId) && ObjectUtil.isNotEmpty(zSiteId) && aSiteId.equals(zSiteId);

                    JSONObject detail = buildProblemDetail(
                        "MISSING_PIPE", "OPTICAL_PATH",
                        optRoad.getString("id"), optRoad.getString("code"), optRoad.getString("name"),
                        "[\"" + optRoad.getString("code") + "\"]", 1,
                        optRoad.getString("code") + "缺管道",
                        false, false, isIndoorOptical, 0,
                        isIndoorOptical ? "起终点在同一局站，可豁免" : null
                    );
                    problemDetails.add(detail);
                }
            }
        }

        /**
         * 添加同光缆问题明细
         */
        private void addSameCableDetails(List<JSONObject> problemDetails, JSONObject topologyData) {
            JSONArray sameCables_d = topologyData.getJSONArray("sameCables_d");
            if (ObjectUtil.isEmpty(sameCables_d)) return;

            for (int i = 0; i < sameCables_d.size(); i++) {
                JSONObject cableGroup = sameCables_d.getJSONObject(i);
                JSONArray optRoadList = cableGroup.getJSONArray("opt_road_list");
                JSONArray sameCables = cableGroup.getJSONArray("sameCables");

                if (ObjectUtil.isNotEmpty(sameCables)) {
                    for (int j = 0; j < sameCables.size(); j++) {
                        JSONObject cable = sameCables.getJSONObject(j);
                        String opticalPaths = buildOpticalPathsJson(optRoadList);
                        String description = formatOptRoadList(optRoadList) + "共用光缆" + cable.getString("code");

                        // TODO: 检查是否为局内光缆（需要根据光缆的起终点局站判断）
                        boolean isIndoorCable = false; // 暂时设为false，后续实现

                        JSONObject detail = buildProblemDetail(
                            "SAME_CABLE", "CABLE",
                            cable.getString("id"), cable.getString("code"), cable.getString("name"),
                            opticalPaths, 1, description,
                            false, isIndoorCable, false, 0,
                            isIndoorCable ? "光缆在同一局站内，可豁免" : null
                        );
                        problemDetails.add(detail);
                    }
                }
            }
        }

        /**
         * 添加同管道问题明细
         */
        private void addSamePipeDetails(List<JSONObject> problemDetails, JSONObject topologyData) {
            JSONArray samePipeSegments_d = topologyData.getJSONArray("samePipeSegments_d");
            if (ObjectUtil.isEmpty(samePipeSegments_d)) return;

            for (int i = 0; i < samePipeSegments_d.size(); i++) {
                JSONObject pipeGroup = samePipeSegments_d.getJSONObject(i);
                JSONArray opt_road_list = pipeGroup.getJSONArray("opt_road_list");
                List<JSONObject> pipeSegments = pipeGroup.getJSONArray("pipeSegments").toJavaList(JSONObject.class);
                int count_frontPipeSegmentList = pipeGroup.getIntValue("count_frontPipeSegmentList");

                if (ObjectUtil.isNotEmpty(pipeSegments)) {
                    for (JSONObject pipeSegment : pipeSegments) {
                        String opticalPaths = buildOpticalPathsJson(opt_road_list);
                        String description = formatOptRoadList(opt_road_list) + "共用管道段" + pipeSegment.getString("code");

                        // 检查是否为局前井
                        boolean isFrontWell = pipeSegment.getBooleanValue("is_front");
                        String exemptionReason = null;
                        if (isFrontWell && count_frontPipeSegmentList <= 10) {
                            exemptionReason = "局前井数量" + count_frontPipeSegmentList + "个，在豁免范围内(≤10)";
                        }

                        JSONObject detail = buildProblemDetail(
                            "SAME_PIPE", "PIPE_SEGMENT",
                            pipeSegment.getString("id"), pipeSegment.getString("code"), pipeSegment.getString("name"),
                            opticalPaths, 1, description,
                            isFrontWell, false, false, count_frontPipeSegmentList,
                            exemptionReason
                        );
                        problemDetails.add(detail);
                    }
                }
            }
        }

        /**
         * 构建光路编码JSON数组
         */
        private String buildOpticalPathsJson(JSONArray optRoadList) {
            if (ObjectUtil.isEmpty(optRoadList)) return "[]";

            List<String> codes = new ArrayList<>();
            for (int i = 0; i < optRoadList.size(); i++) {
                codes.add(optRoadList.getJSONObject(i).getString("code"));
            }
            return "[\"" + String.join("\", \"", codes) + "\"]";
        }

        /**
         * 构建问题明细对象
         */
        private JSONObject buildProblemDetail(String problemType, String resourceType,
                                            String resourceId, String resourceCode, String resourceName,
                                            String relatedOpticalPaths, Integer problemCount, String description,
                                            boolean isFrontWell, boolean isIndoorCable, boolean isIndoorOptical,
                                            int frontWellCount, String exemptionReason) {
            JSONObject detail = new JSONObject();
            detail.put("problem_type", problemType);
            detail.put("resource_type", resourceType);
            detail.put("resource_id", resourceId);
            detail.put("resource_code", resourceCode);
            detail.put("resource_name", resourceName);
            detail.put("related_optical_paths", relatedOpticalPaths);
            detail.put("problem_count", problemCount != null ? problemCount : 1);
            detail.put("problem_description", description);
            detail.put("is_front_well", isFrontWell);
            detail.put("is_indoor_cable", isIndoorCable);
            detail.put("is_indoor_optical", isIndoorOptical);
            detail.put("front_well_count", frontWellCount);
            detail.put("exemption_reason", exemptionReason);
            return detail;
        }

        private void appendBaseProblemInfo(StringBuilder info, JSONObject baseProblemCounts, JSONObject topologyData) {
            // 缺管道问题描述
            int missingPipeCount = baseProblemCounts.getIntValue("problem_missing_pipe");
            if (missingPipeCount > 0) {
                appendMissingPipeInfo(info, topologyData);
            }

            // 同光缆问题描述
            int sameCableCount = baseProblemCounts.getIntValue("problem_same_cable");
            if (sameCableCount > 0) {
                appendSameCableInfo(info, topologyData);
            }

            // 同管道问题描述
            int samePipeCount = baseProblemCounts.getIntValue("problem_same_pipe");
            if (samePipeCount > 0) {
                appendSamePipeInfo(info, topologyData);
            }
        }

        private void appendExemptionInfo(StringBuilder info, JSONObject exemptionTags) {
            String indoorOptical = exemptionTags.getString("exemption_indoor_optical");
            if (ObjectUtil.isNotEmpty(indoorOptical)) {
                info.append("局内光路: ").append(indoorOptical).append("; ");
            }

            String frontWell = exemptionTags.getString("exemption_front_well");
            if (ObjectUtil.isNotEmpty(frontWell)) {
                info.append("局前井豁免: ").append(frontWell).append("; ");
            }

            String indoorCable = exemptionTags.getString("exemption_indoor_cable");
            if (ObjectUtil.isNotEmpty(indoorCable)) {
                info.append("局内光缆豁免: ").append(indoorCable).append("; ");
            }
        }

        private void appendMissingPipeInfo(StringBuilder info, JSONObject topologyData) {
            JSONArray optRoads = topologyData.getJSONArray("optRoads");
            if (ObjectUtil.isEmpty(optRoads)) return;

            for (int i = 0; i < optRoads.size(); i++) {
                JSONObject optRoad = optRoads.getJSONObject(i);
                List<JSONObject> pipeSegmentList = JSONObjectUtil.jsonObjectArrayToList(
                    optRoad.getJSONArray("pipe_segment_list"));
                if (ObjectUtil.isEmpty(pipeSegmentList)) {
                    info.append(optRoad.getString("code")).append("缺管道; ");
                }
            }
        }

        private void appendSameCableInfo(StringBuilder info, JSONObject topologyData) {
            JSONArray sameCables_d = topologyData.getJSONArray("sameCables_d");
            if (ObjectUtil.isEmpty(sameCables_d)) return;

            for (int i = 0; i < sameCables_d.size(); i++) {
                JSONObject cableGroup = sameCables_d.getJSONObject(i);
                JSONArray optRoadList = cableGroup.getJSONArray("opt_road_list");
                JSONArray sameCables = cableGroup.getJSONArray("sameCables");
                if (ObjectUtil.isNotEmpty(sameCables)) {
                    info.append(formatOptRoadList(optRoadList))
                        .append("共").append(sameCables.size())
                        .append("同光缆; ");
                }
            }
        }

        private void appendSamePipeInfo(StringBuilder info, JSONObject topologyData) {
            JSONArray samePipeSegments_d = topologyData.getJSONArray("samePipeSegments_d");
            if (ObjectUtil.isEmpty(samePipeSegments_d)) return;

            for (int i = 0; i < samePipeSegments_d.size(); i++) {
                JSONObject pipeGroup = samePipeSegments_d.getJSONObject(i);
                JSONArray opt_road_list = pipeGroup.getJSONArray("opt_road_list");
                int count_pipleSegmentList = pipeGroup.getIntValue("count_pipleSegmentList");
                int count_frontPipeSegmentList = pipeGroup.getIntValue("count_frontPipeSegmentList");

                if (count_pipleSegmentList > 0) {
                    info.append(formatOptRoadList(opt_road_list))
                        .append("共").append(count_pipleSegmentList)
                        .append("同管道; ");
                }

                if (count_frontPipeSegmentList > 0) {
                    info.append(formatOptRoadList(opt_road_list))
                        .append("共").append(count_frontPipeSegmentList)
                        .append("同局前井; ");
                }
            }
        }

        private String formatOptRoadList(JSONArray optRoadList) {
            if (ObjectUtil.isEmpty(optRoadList)) return "";

            StringBuilder result = new StringBuilder();
            for (int j = 0; j < optRoadList.size(); j++) {
                result.append(optRoadList.getJSONObject(j).getString("code"));
                if (j < optRoadList.size() - 1) {
                    result.append("和");
                }
            }
            return result.toString();
        }
    }

    // 保留原有方法以兼容其他调用
    private String optRoadListFormat(JSONArray optRoadList) {
        return new ExceptionInfoGenerator().formatOptRoadList(optRoadList);
    }

    // 保留原有方法以兼容其他调用
    public String result2format(JSONObject result) {
        ExceptionInfoGenerator generator = new ExceptionInfoGenerator();
        return generator.generateExceptionInfo(new JSONObject(), new JSONObject(), result);
    }
}
