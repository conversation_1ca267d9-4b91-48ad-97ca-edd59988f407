package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustomerDao;
import com.telecom.nrm.dao.ProductDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.ResTreeParamDto;
import com.telecom.nrm.domain.WhereBody;
import com.telecom.nrm.service.CustomerService;
import com.telecom.nrm.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    @Autowired
    private CustomerDao customerDao;

    public JSONObject queryCustomerByCircuit(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("circuitId".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitId", body.getPropertyValue());
            } else if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  customerDao.queryCustomerByCircuit(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("custId", obj.getString("cust_id"));
                node.put("custName", obj.getString("cust_name"));
                node.put("custCode", obj.getString("cust_code"));
                node.put("custLevel", obj.getString("cust_level"));
                node.put("custContact", obj.getString("cust_contact"));
                node.put("address", obj.getString("address"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }

    public JSONObject queryCustomerByCircuitOrCust(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("custId".equals(body.getPropertyLabel())) {
                jsonObject.put("custId", body.getPropertyValue());
            } else if ("custName".equals(body.getPropertyLabel())) {
                jsonObject.put("custName", body.getPropertyValue());
            } else if ("custAccount".equals(body.getPropertyLabel())) {
                jsonObject.put("custAccount", body.getPropertyValue());
            } else if ("cirNo".equals(body.getPropertyLabel())) {
                jsonObject.put("cirNo", body.getPropertyValue());
            } else if ("hasNextLevelCust".equals(body.getPropertyLabel())) {
//                jsonObject.put("hasNextLevelCust", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  customerDao.queryCustomerByCircuit(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("custName", obj.getString("cust_name"));
                node.put("custContact", obj.getString("cust_contact"));
                node.put("address", obj.getString("address"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }
}
