package com.telecom.nrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.LifeCircuitDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
@Service
@Slf4j
public class CircuitPairServiceImpl implements CircuitPairService {
    @Autowired
    MstpService mstpService;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    GraphService graphService;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    ProtectCircuitCheckService protCircuitCheckService;

    @Autowired
    OptRoadPairService optRoadPairService;


    @Autowired
    LifeCircuitDao lifeCircuitDao;

    @Autowired
    OptGroupDao optGroupDao;

    @Autowired
    OptGroupService optGroupService;


    public  JSONObject queryMSTPCircuitPairToPipeSegment( List<String> codes,JSONObject request){

        JSONObject result =new JSONObject();
        JSONObject query_mstp_circuits = new JSONObject();
        query_mstp_circuits.put("codes", codes);
        query_mstp_circuits.put("shardingCode", NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        result = queryMSTPCircuitPairToPipeSegment(query_mstp_circuits);
        List<JSONObject> samePipeSegments_d = result.getJSONArray("samePipeSegments_d").toJavaList(JSONObject.class);
        // List<JSONObject> samePipeSegments = result.getJSONArray("samePipeSegments");

        PageResponse<JSONObject> pageResponse_pipleSegment =  custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_query(new JSONObject() ,100000,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        JSONObjectUtil.convertBigNumberToString(pageResponse_pipleSegment.getData());
        Set<String> pipeSegment_white_code = pageResponse_pipleSegment.getData().stream().map(d->d.getString("code")).collect(Collectors.toSet());



        int count_samePipeSegments = 0;

        // 计算同管道个数
        for(JSONObject optRoutePair : samePipeSegments_d){
            int count_pipleSegmentList = 0;
            for(JSONObject pipeSegment : optRoutePair.getJSONArray("pipeSegments").toJavaList(JSONObject.class)){
                if(pipeSegment_white_code.contains(pipeSegment.getString("code"))) {
                    pipeSegment.put("code", pipeSegment.getString("code") + "   ----已忽略");
                }
                else{
                    count_pipleSegmentList++;
                }
            }
            optRoutePair.put("count_pipleSegmentList", count_pipleSegmentList);
            count_samePipeSegments+=count_pipleSegmentList;

        }
        result.put("count_samePipeSegments", count_samePipeSegments);
        // result.put("samePipeSegments_d", samePipeSegments_d);
        int count_sameCables = 0;
        List<JSONObject> sameCables_d = result.getJSONArray("sameCables_d").toJavaList(JSONObject.class);
        for (JSONObject sameCable: sameCables_d) {
            count_sameCables+=sameCable.getJSONArray("sameCables").size();
        }
        result.put("count_sameCables", count_sameCables);

        return result;
    }

    public String result2format(JSONObject current_circuits_pair, JSONObject result){
        List<JSONObject> samePipeSegments_d = result.getJSONArray("samePipeSegments_d").toJavaList(JSONObject.class);
        List<JSONObject> sameCables_d = result.getJSONArray("sameCables_d").toJavaList(JSONObject.class);
        String result_p = "";
        if(ObjectUtil.isNotEmpty(sameCables_d)){
            for(JSONObject optRoadPair: sameCables_d) {
                for (int j = 0; j < optRoadPair.getJSONArray("opt_road_list").size(); j++) {
                    JSONObject optRoad = optRoadPair.getJSONArray("opt_road_list").getJSONObject(j);
                    result_p = result_p + optRoad.getString("code");
                    if (j < optRoadPair.getJSONArray("opt_road_list").size()-1) {
                        result_p = result_p + "和";
                    }
                }
                result_p = result_p + "共" + optRoadPair.getJSONArray("sameCables").size() + "光缆;" + "\n";
            }
        }
        if(ObjectUtil.isNotEmpty(samePipeSegments_d)){
            for(JSONObject optRoadPair: samePipeSegments_d) {
                for (int j = 0; j < optRoadPair.getJSONArray("opt_road_list").size(); j++) {
                    result_p = result_p + optRoadPair.getJSONArray("opt_road_list").getJSONObject(j).getString("code");
                    if (j < optRoadPair.getJSONArray("opt_road_list").size()-1) {
                        result_p = result_p + "和";
                    }
                }
                // result_p = result_p + "共計" + samePipeSegments_d.getJSONObject(i).getJSONArray("pipeSegments").size() + "同管道;" + "\n";
                result_p = result_p + "共" + optRoadPair.getString("count_pipleSegmentList") + "管道;" + "\n";
            }

        }
        if(result_p=="") {
            result_p = "正常";
        }
        return result_p;
    }

    Function<JSONObject,JSONObject> mapFun = (d)->{
        JSONObject result = new JSONObject();
        Set<String> keys = d.keySet();
        if (ObjectUtil.isNotEmpty(keys)) {
            // log.info("keys={}",keys.toString());
            for (String key: keys) {
                if (! (d.get(key) instanceof Collection)) {
                    result.put(key,d.get(key));
                };
            }
        }
        return result;
    };

    public JSONObject circuits_pair_analyze(JSONObject current_circuits_pair, JSONObject request){
        List<String> codes = new ArrayList<String>();
        JSONObject need_save = new JSONObject();

        if(ObjectUtil.isNotNull(current_circuits_pair.getString("aCode"))){
            codes.add(current_circuits_pair.getString("aCode"));
            need_save.put("a_code",current_circuits_pair.getString("aCode"));
        }
        if(ObjectUtil.isNotNull(current_circuits_pair.getString("zCode")) ) {
            codes.add(current_circuits_pair.getString("zCode"));
            need_save.put("z_code",current_circuits_pair.getString("zCode"));
        }
        JSONObject result = queryMSTPCircuitPairToPipeSegment(codes, request);

        List<JSONObject> samePipeSegments_d = JSONObjectUtil.jsonObjectArrayToList(result.getJSONArray("samePipeSegments_d"));
        samePipeSegments_d.removeIf(samePipeSegment -> samePipeSegment.getString("count_pipleSegmentList").equals("0"));

        result.put("title", CollectionUtil.join(codes,","));
        result.put("circuits_pair",codes);
        need_save.put("circuits_p", current_circuits_pair.getString("aCode")+","+current_circuits_pair.getString("zCode"));
        if(codes.size()<2){
            need_save.put("result_p","主备电路不完整");
        }else{
            need_save.put("result_p",result2format(current_circuits_pair, result));
        }

        JSONObject currentCircuit = new JSONObject();
        List<JSONObject> circuits_cm_link = new ArrayList<>();
        currentCircuit.put("circuit_code", Objects.isNull(current_circuits_pair.getString("aCode"))?current_circuits_pair.getString("zCode"): current_circuits_pair.getString("aCode"));
        log.info("当前电路"+current_circuits_pair+","+currentCircuit);
        String areaCode = current_circuits_pair.getString("areacode").trim();
        String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaCode);
        JSONObject pageResponse = lifeCircuitDao.cm_link_query(currentCircuit, shardingCode);
        if(pageResponse ==null || Objects.isNull(pageResponse.getString("data"))){
            need_save.put("result_p","已拆机");
        }
        log.info("need_save:"+need_save);
        custLinkInterfaceDao.tmp_result_shuangluyou_insert(need_save, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        String risk_type = protCircuitCheckService.getrisktype(result.getJSONArray("samePipeSegments_d").toJavaList(JSONObject.class),
                result.getJSONArray("sameCables_d").toJavaList(JSONObject.class), result.getJSONArray("samedevices").toJavaList(JSONObject.class));
        need_save.put("risk_type",risk_type);
        String risk_desc = protCircuitCheckService.getriskdesc(result.getJSONArray("samePipeSegments_d").toJavaList(JSONObject.class),
                result.getJSONArray("sameCables_d").toJavaList(JSONObject.class), result.getJSONArray("samedevices").toJavaList(JSONObject.class));
        need_save.put("risk_desc",risk_desc);
        custLinkInterfaceDao.tmp_cm_link_service_customer_1_update(need_save, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        result.put("need_save",need_save);
        return result;
    }



    void buildTrees(JSONObject data) {
        graphService.buildReverseTree(data,"政企业务", "管道段","mstp_circuits");
        graphService.mapProperty(data,"管道段", "mstp_circuits", mapFun);

        graphService.buildReverseTree(data,"政企业务", "光路","mstp_circuits");
        graphService.mapProperty(data,"光路", "mstp_circuits", mapFun);

        graphService.buildReverseTree(data,"政企业务", "光缆","mstp_circuits");
        graphService.mapProperty(data,"光缆", "mstp_circuits", mapFun);

        graphService.buildReverseTree(data,"光路", "管道段","opt_road_list");
        graphService.mapProperty(data, "管道段","opt_road_list", mapFun);

        graphService.buildReverseTree(data,"光路", "光缆","opt_road_list");
        graphService.mapProperty(data,"光缆", "opt_road_list", mapFun);

        // graphService.buildTree(data,"局向光纤", "管道段","pipe_segment_list");


        graphService.buildReverseTree(data,"政企业务", "设备","mstp_circuits");


        graphService.buildTree(data,"政企业务","光路","opt_road_list");
        graphService.buildTree(data,"光路","管道段","pipe_segment_list");
        graphService.mapProperty(data,"光路", "pipe_segment_list", mapFun);
        graphService.buildTree(data,"光路","光缆","cable_list");
        graphService.mapProperty(data,"光路", "cable_list", mapFun);
        graphService.buildReverseTree(data,"端口", "板卡","mstp_circuits");
        graphService.mapProperty(data,"板卡", "mstp_circuits", mapFun);
    }




    public JSONObject queryMSTPCircuitPairToPipeSegment(JSONObject request) {
        List<String> codes = (List<String>) JSONObjectUtil.jsonStringArrayToList(request.getJSONArray("codes"));
        String shardingCode=request.getString("shardingCode");



        GraphRequestBuilder builder = new GraphRequestBuilder();

        //shuangluyou
        GraphRequest graphRequest = builder.setApiId("query_mstp_shuangluyou").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("zq_service","codes",codes))
                .appendWhereBodyItem(new GraphWhereBodyItem("user_terminal","spec_id_s",Arrays.asList("1028492006","1028400006","1029400004")))
                .appendFilter("ether_link",(dataList,g)->{
                    // 如果以太网链路对应的政企业务无MSTP电路,则添加该以太网链路
                    JSONObject graphData =   graphService.getData(g);
                    graphService.buildReverseTree(graphData, "政企业务", "以太网链路", "zq_services");
                    graphService.buildTree(graphData,"政企业务","mstp电路","mstp_circuits");
                    dataList = graphService.filterNodeByLabel(graphData,"以太网链路");
                    List<JSONObject> filteredList = new ArrayList<>();
                    for (JSONObject d: dataList) {
                        int mstpNum =0;
                        List<JSONObject> zqServiceList = d.getJSONArray("zq_services").toJavaList(JSONObject.class);
                        for (JSONObject zqService: zqServiceList) {
                            mstpNum+=zqService.getJSONArray("mstp_circuits").size();
                        }
                        if (mstpNum==0) {
                            filteredList.add(d);
                        }
                    }
                    return filteredList;
                })
                .build();

        Graph graph = graphApiService.doApi(graphRequest);

        JSONObject data = graphService.getData(graph);

        List<JSONObject> pipeSegmentList = graphService.filterNodeByLabel(data,"管道段");
        List<JSONObject> roomList = graphService.filterNodeByLabel(data,"机房");
        List<JSONObject> deviceList = graphService.filterNodeByLabel(data,"设备");
        List<JSONObject> cableList = graphService.filterNodeByLabel(data,"光缆");
        List<JSONObject> localFiberOptList = graphService.filterNodeByLabel(data,"局向光纤");
        List<JSONObject> optRoadList = graphService.filterNodeByLabel(data,"光路");
        List<JSONObject> circuitList = graphService.filterNodeByLabel(data,"政企业务");

        List<JSONObject> cardBoardList = graphService.filterNodeByLabel(data,"板卡");

        buildTrees(data);


        JSONObject result = new JSONObject();
        result.put("optRoads",optRoadList);
        List<JSONObject> sameoptRoads = optRoadList.stream().filter(optRoad->ObjectUtil.isNotEmpty(optRoad.getJSONArray("mstp_circuits"))&& optRoad.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        result.put("sameoptRoads", sameoptRoads);
        result.put("pipeSegments", pipeSegmentList);
        result.put("cables", cableList);
        result.put("devices", deviceList);
        result.put("rooms", roomList);
        result.put("cardBoards", cardBoardList);
        List<JSONObject> localFiberOpts = localFiberOptList.stream().filter(p-> ObjectUtil.isNotEmpty(p.getJSONArray("pipe_segment_list")) ).collect(Collectors.toList());
        result.put("localFiberOpts", localFiberOpts);
//---------------------------------------------

        List<JSONObject> samePipeSegmentList = pipeSegmentList.stream().filter(p-> ObjectUtil.isNotEmpty(p.getJSONArray("mstp_circuits"))&& p.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        List<JSONObject> sameCableList = cableList.stream().filter(p->ObjectUtil.isNotEmpty(p.getJSONArray("mstp_circuits"))&& p.getJSONArray("mstp_circuits").size()>1 ).collect(Collectors.toList());
        List<JSONObject> sameOptList = optRoadList.stream().filter(p-> ObjectUtil.isNotEmpty (p.getJSONArray("mstp_circuits"))&& p.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        List<JSONObject> samedeviceList = deviceList.stream().filter(p-> ObjectUtil.isNotEmpty (p.getJSONArray("mstp_circuits"))&& p.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        List<JSONObject> sameCardBoardList = cardBoardList.stream().filter(p-> ObjectUtil.isNotEmpty (p.getJSONArray("mstp_circuits"))&&p.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());


        // sameCableList = sameCableList.stream().filter(p->p.getJSONArray("opt_road_list").size()>1 &&p.getJSONArray("opt_road_list").size()<3).collect(Collectors.toList());
        // samePipeSegmentList = samePipeSegmentList.stream().filter(p->p.getJSONArray("opt_road_list").size()>1 &&p.getJSONArray("opt_road_list").size()<3 ).collect(Collectors.toList());

        result.put("samedevices", samedeviceList); // 同设备
        result.put("samePipeSegments", samePipeSegmentList); //
        result.put("sameCables", sameCableList);
        result.put("sameOptList", sameOptList);
        result.put("sameCardBoard", sameCardBoardList);//同板卡
        result.put("devices", deviceList);
        result.put("devices_d_new", mstpService.getFirstLastDevice(graph,codes));


        // 计算光路对
        List<JSONObject> optRoadPairList = new ArrayList<>();
        if (codes.size()>=2 && circuitList.size()>=2) {
            // JSONObject c
            JSONObject circuit_1 = circuitList.stream().filter(c -> c.getString("code").equals(codes.get(0))).findFirst().orElse(new JSONObject());
            JSONObject circuit_2 = circuitList.stream().filter(c -> c.getString("code").equals(codes.get(1))).findFirst().orElse(new JSONObject());
            List<JSONObject> optRoadList1 = JSONObjectUtil.jsonObjectArrayToList(circuit_1.getJSONArray("opt_road_list")) ;// Optional.ofNullable(circuit_1.getJSONArray("opt_road_list")).orElse(new JSONArray());
            List<JSONObject> optRoadList2 = JSONObjectUtil.jsonObjectArrayToList(circuit_2.getJSONArray("opt_road_list")) ;//

            for (JSONObject optRoad1 : optRoadList1) {
                for (JSONObject optRoad2 : optRoadList2) {
                    String aOptCode = optRoad1.getString("code");
                    String zOptCode = optRoad2.getString("code");
                    if (aOptCode.equals(zOptCode)) continue;  // 如果两条电路同光路,则不生成光路对
                    JSONObject optRoadPair = optRoadPairService.buildOptRoadPair(optRoad1, optRoad2);
                    optRoadPair.put("a_code", codes.get(0));
                    optRoadPair.put("z_code", codes.get(1));
                    optRoadPairList.add(optRoadPair);
                }
            }
        }
        // 计算光路对重复管道段
        result.put("samePipeSegments_d", optRoadPairService.getSamePipeSegments_d(optRoadPairList));

        // 计算光路对重复光路
        result.put("sameCables_d", optRoadPairService.getSameCables_d(optRoadPairList));



        //新加的，光路清单
        List<String> opt_code_list = optRoadList.stream().map(p->p.getString("code")).collect(Collectors.toList());
        result.put("opt_code_list",opt_code_list);
        return result;
    }



    @LogAnnotation(interfaceName = "电路组隐患检测")
    public JSONObject risk_analyze(JSONObject request){
        request.put("id", request.getString("id"));
        log.info("request---"+request);
        if(ObjectUtil.isNotEmpty(request.getString("areacode"))){
            request.put("ds", request.getString("areacode"));
        }
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuit_pair(request ,1000,1,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds").trim()));
        JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
        List<JSONObject> pair_list_data =pageResponse.getData();
        JSONObject result = new JSONObject();
        if(ObjectUtil.isNotEmpty(pair_list_data)) {
            JSONObject current_circuits_pair = pair_list_data.get(0);
            if(ObjectUtil.isNotEmpty(request.getString("areacode"))){
                request.put("ds", request.getString("areacode").trim());
            }
            result = circuits_pair_analyze(current_circuits_pair, request);
        }
        result.put("ds", request.getString("ds"));
        result.put("id", request.getString("id"));
        result = saveSnapshot(result);
        return result;
    }

    @LogAnnotation(interfaceName = "电路组隐患检测")
    public JSONObject risk_analyze_get(JSONObject request){

        if(ObjectUtil.isNotEmpty(request.getString("areacode"))){
            request.put("ds", request.getString("areacode"));
        }

        String id = request.getString("id");

        JSONObject cacheParam  = new JSONObject();
        cacheParam.put("object_id", id);
        cacheParam.put("object_type", "CircuitPair");
        cacheParam.put("ds", request.getString("ds"));

        boolean cached = request.getBoolean("cached");

        if (cached) {
            JSONObject previous_risk_check = optGroupService.get_previous_risk_check(cacheParam);
            if (ObjectUtil.isNotEmpty(previous_risk_check) && ObjectUtil.isNotEmpty(previous_risk_check.getJSONObject("snapshot")) && cached) {
                JSONObject result  = previous_risk_check.getJSONObject("snapshot");
                result.put("check_result", previous_risk_check.getString("check_result"));
                result.put("exception_info", previous_risk_check.getString("exception_info"));
                log.info("snapshot, {}", result);
                return result;
            }else{
                JSONObject result = risk_analyze(request);
                return result;
            }
        }else{
            JSONObject result = risk_analyze(request);
            return result;
        }


        // JSONObject result = risk_analyze(request);
        // saveSnapshot(result);


    }

    public JSONObject saveSnapshot(JSONObject result) {

        JSONObject needSave = result.getJSONObject("need_save");


        String riskType = needSave.getString("result_p");
        String riskDesc = needSave.getString("result_p");

        // need_save.put("check_result","无光路");
        needSave.put("object_id", result.getString("id"));
        needSave.put("object_type", "CircuitPair");
        needSave.put("check_result", riskType);
        needSave.put("exception_info", riskDesc);

        String snapshot = (new cn.hutool.json.JSONObject(result)).toJSONString(0);
        needSave.put("snapshot", snapshot);

        result.put("check_result", riskType);
        result.put("exception_info", riskDesc);

        optGroupDao.pm_risk_check_insert(needSave, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(result.getString("ds")));

        return result;
    }


}
