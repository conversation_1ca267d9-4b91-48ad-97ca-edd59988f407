package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.ProtCircuitCheckDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.CustLinkService;
import com.telecom.nrm.service.ProtectCircuitCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.hutool.core.util.StrUtil.trim;

@Service
public class ProtectCircuitCheckServiceImpl implements ProtectCircuitCheckService {

    @Autowired
    ProtCircuitCheckDao protCircuitCheckDao;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    CustLinkService custLinkService;

    public JSONObject getSeq(String circuit_code, JSONObject result, String aProvince, String zProvince){
        String A_area_code = getAreaName(result.getJSONObject("data").getJSONObject("A").getString("area_code"));
        if(aProvince.equals("江苏") && zProvince.equals("江苏")){
            if(circuit_code.indexOf(A_area_code)>0&& circuit_code.length()<17){
                JSONObject A = result.getJSONObject("data").getJSONObject("A");
                result.getJSONObject("data").put("A", result.getJSONObject("data").getJSONObject("Z"));
                result.getJSONObject("data").put("Z", A);
            }else if(circuit_code.indexOf(A_area_code)<0&& circuit_code.length()<17){
                JSONObject A = result.getJSONObject("data").getJSONObject("A");
                result.getJSONObject("data").put("A", result.getJSONObject("data").getJSONObject("Z"));
                result.getJSONObject("data").put("Z", A);
            }
        }else if(!aProvince.equals("江苏")){
            JSONObject A = result.getJSONObject("data").getJSONObject("A");
            result.getJSONObject("data").put("A", result.getJSONObject("data").getJSONObject("Z"));
            result.getJSONObject("data").put("Z", A);
        }
        return result;
    }

    public String getAreaName(String area_code){
        String area_name = "";
        switch (trim(area_code)){
            case "8320200": area_name = "无锡";break;
            case "8320100": area_name = "南京";break;
            case "8320300": area_name = "徐州";break;
            case "8320400": area_name = "常州";break;
            case "8320500": area_name = "苏州";break;
            case "8320600": area_name = "南通";break;
            case "8320700": area_name="连云港";break;
            case "8320800": area_name ="淮安";break;
            case "8320900": area_name = "盐城";break;
            case "8321000": area_name ="扬州";break;
            case "8321100": area_name ="镇江";break;
            case "8321200": area_name ="泰州";break;
            case "8321300": area_name = "宿迁";break;
        }
        return area_name;
    }

    public String getAreaCode(String area_name){
        String area_code = "";
        switch (trim(area_name)){
            case "无锡": area_code = "8320200";break;
            case "江阴": area_code = "8320200";break;
            case "南京": area_code = "8320100";break;
            case "徐州": area_code = "8320300";break;
            case "常州": area_code = "8320400";break;
            case "苏州": area_code = "8320500";break;
            case "南通": area_code = "8320600";break;
            case "连云港": area_code="8320700";break;
            case "淮安": area_code ="8320800";break;
            case "盐城": area_code = "8320900";break;
            case "扬州": area_code ="8321000";break;
            case "镇江": area_code ="8321100";break;
            case "泰州": area_code ="8321200";break;
            case "宿迁": area_code = "8321300";break;
        }
        return area_code;
    }

    public JSONObject getrisk(JSONObject cur_result){
        JSONObject result = new JSONObject();
        String is_risk = "";
        String risk_type = getrisktype(JSONObjectUtil.jsonObjectArrayToList(cur_result.getJSONArray("samePipeSegments_d")), JSONObjectUtil.jsonObjectArrayToList(cur_result.getJSONArray("sameCables_d")), JSONObjectUtil.jsonObjectArrayToList(cur_result.getJSONArray("samedevices")));
        String risk_desc = getriskdesc(JSONObjectUtil.jsonObjectArrayToList(cur_result.getJSONArray("samePipeSegments_d")), JSONObjectUtil.jsonObjectArrayToList(cur_result.getJSONArray("sameCables_d")), JSONObjectUtil.jsonObjectArrayToList(cur_result.getJSONArray("samedevices")));
        if(risk_type!="" && risk_type !=null){
            is_risk = "是";
        }else is_risk = "否";
        result.put("is_risk", is_risk);
        result.put("risk_type", risk_type);
        result.put("risk_desc", risk_desc);
        return result;
    }

    public String getrisk(String risk_type){
        String is_risk = "";
        if(!Objects.isNull(risk_type)&&!risk_type.equals("") && risk_type !=null){
            is_risk = "是";
        }else {is_risk = "否";}
        return is_risk;
    }

    public String getrisktype(List<JSONObject> samePipeSegments_d, List<JSONObject> sameCables_d, List<JSONObject> samedevices){
        String risk_type = "";
        if((!Objects.isNull(sameCables_d) && sameCables_d!=null && sameCables_d.size()>0) || (!Objects.isNull(samePipeSegments_d) && samePipeSegments_d!=null && samePipeSegments_d.size()>0)){
            risk_type = "网络架构不足";
        }
        if(!Objects.isNull(samedevices) && samedevices!=null && samedevices.size() >0){
            if(risk_type == ""){
                risk_type = "设备配置不足";
            }else risk_type = risk_type+","+"设备配置不足";
        }
        return risk_type;
    }

    public String getriskdesc(List<JSONObject> samePipeSegments_d, List<JSONObject> sameCables_d, List<JSONObject> samedevices){
        String risk_desc = "";
        if(!Objects.isNull(sameCables_d) && sameCables_d!=null && sameCables_d.size()>0){
            String risk_desc_cable = "";
            for(JSONObject sameCable: sameCables_d){
                String risk_desc_cable_buf = sameCable.getJSONObject("result").getString("a_opt_code")+"-"+(sameCable.getJSONObject("result").getString("a_code")!=null?sameCable.getJSONObject("result").getString("a_code"):"")+"和"
                        +sameCable.getJSONObject("result").getString("z_opt_code")+"-"+(sameCable.getJSONObject("result").getString("z_code")!=null?sameCable.getJSONObject("result").getString("z_code"):"") +"共计"+sameCable.getJSONArray("sameCables").size()+"同光缆,同光缆清单：";
                for(int j =0;j<sameCable.getJSONArray("sameCables").size();j++){
                    if(j ==0){
                        risk_desc_cable_buf = risk_desc_cable_buf+sameCable.getJSONArray("sameCables").getJSONObject(j).getString("code");
                    }else{
                        risk_desc_cable_buf = risk_desc_cable_buf+","+sameCable.getJSONArray("sameCables").getJSONObject(j).getString("code");
                    }
                }
                if(risk_desc_cable ==""){
                    risk_desc_cable = risk_desc_cable_buf;
                }else{
                    risk_desc_cable = risk_desc_cable +";"+ risk_desc_cable_buf;
                }
            }
            if(risk_desc_cable!="") risk_desc = risk_desc_cable;
        }
        if(!Objects.isNull(samePipeSegments_d) && samePipeSegments_d!=null && samePipeSegments_d.size()>0){
            String risk_desc_pipe = "";
            for(int i =0;i<samePipeSegments_d.size();i++){
                String risk_desc_pipe_buf = samePipeSegments_d.get(i).getJSONObject("result").getString("a_opt_code")+"-"+(samePipeSegments_d.get(i).getJSONObject("result").getString("a_code")!=null? samePipeSegments_d.get(i).getJSONObject("result").getString("a_code"):"")+"和"
                        +samePipeSegments_d.get(i).getJSONObject("result").getString("z_opt_code")+"-"+(samePipeSegments_d.get(i).getJSONObject("result").getString("z_code")!=null?samePipeSegments_d.get(i).getJSONObject("result").getString("z_code"):"")+"共计"+samePipeSegments_d.get(i).getString("count_pipleSegmentList")+"同管道,同管道清单：";
                for(int j =0;j<samePipeSegments_d.get(i).getJSONArray("pipeSegments").size();j++){
                    if(j ==0){
                        risk_desc_pipe_buf = risk_desc_pipe_buf+samePipeSegments_d.get(i).getJSONArray("pipeSegments").getJSONObject(j).getString("code");
                    }else{
                        risk_desc_pipe_buf = risk_desc_pipe_buf+","+samePipeSegments_d.get(i).getJSONArray("pipeSegments").getJSONObject(j).getString("code");
                    }
                }
                if(i ==0){
                    risk_desc_pipe = risk_desc_pipe_buf;
                }else{
                    risk_desc_pipe = risk_desc_pipe +";"+ risk_desc_pipe_buf;
                }
            }
            if(risk_desc!="") risk_desc = risk_desc +","+ risk_desc_pipe; else{risk_desc =  risk_desc_pipe;}
        }
        if(!Objects.isNull(samedevices) && samedevices!=null && samedevices.size() >0){
            String risk_desc_dev = "共计"+samedevices.size()+"同设备，同设备清单包括：";
            for(int i = 0;i<samedevices.size();i++){
                if(i==0) risk_desc_dev = risk_desc_dev +samedevices.get(i).getString("code");
                else risk_desc_dev = risk_desc_dev +","+samedevices.get(i).getString("code");
            }
            if(risk_desc!="") risk_desc = risk_desc + ";"+risk_desc_dev; else{risk_desc =  risk_desc_dev;}
        }
        return risk_desc;
    }

    public JSONObject getInterface(String code, String msg, String a_area_code, String z_area_code, String a_is_risk,String z_is_risk, String a_risk_type,String z_risk_type, String a_risk_desc, String z_risk_desc){
        JSONObject result = new JSONObject();
        result.put("code", code);
        result.put("msg", msg);
        JSONObject data = new JSONObject();
        JSONObject A = new JSONObject();
        A.put("area_code", a_area_code);
        A.put("is_risk", a_is_risk);
        A.put("risk_type", a_risk_type);
        A.put("risk_desc", a_risk_desc);
        data.put("A", A);
        JSONObject Z = new JSONObject();
        Z.put("area_code", z_area_code);
        Z.put("is_risk", z_is_risk);
        Z.put("risk_type", z_risk_type);
        Z.put("risk_desc", z_risk_desc);
        data.put("Z", Z);
        result.put("data", data);
        return result;
    }

    public JSONObject getInterface(String code, String msg){
        JSONObject result = new JSONObject();
        result.put("code", code);
        result.put("msg", msg);
        return result;
    }


    public List<String> getAreaFromInput(String input){
        List<String> result = new ArrayList<>();
        if(input.contains("南京")){
            result.add("南京");
        }
        if(input.contains("苏州")){
            result.add("苏州");
        }
        if(input.contains("盐城")){
            result.add("盐城");
        }
        if(input.contains("泰州")){
            result.add("泰州");
        }
        if(input.contains("连云港")){
            result.add("连云港");
        }
        if(input.contains("宿迁")){
            result.add("宿迁");
        }
        if(input.contains("淮安")){
            result.add("淮安");
        }
        if(input.contains("常州")){
            result.add("常州");
        }
        if(input.contains("扬州")){
            result.add("扬州");
        }
        if(input.contains("徐州")){
            result.add("徐州");
        }
        if(input.contains("无锡")){
            result.add("无锡");
        }
        if(input.contains("镇江")){
            result.add("镇江");
        }
        if(input.contains("南通")){
            result.add("南通");
        }
        if(input.contains("江阴")){
            result.add("无锡");
        }
        if(input.contains("仪征")){
            result.add("镇江");
        }
        return result;
    }

    public Integer reportSave(String intf_code,String request_header, String request_body, String response_body, String response_header){
        JSONObject message = new JSONObject();
        message.put("intf_code",intf_code);
        message.put("request_header", request_header);
        message.put("request_body", request_body);
        message.put("response_body", response_body);
        message.put("response_header",response_header);
        return protCircuitCheckDao.graph_pm_interface_log_insert(message, NRMConstants.SHARDING_GRAPH_DB);
    }

    public List<JSONObject> getPairCircuit(JSONObject param){
        List<JSONObject> circuits = new ArrayList<>();
        for(String ds :  NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.keySet()){
            PageResponse<JSONObject> pageResponse = custLinkInterfaceDao.getCircuit_pair(param, 100, 1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
            if(pageResponse !=null || Objects.isNull(pageResponse)){
                JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
                circuits.addAll(pageResponse.getData());
            }
        }
        return circuits;
    }


    //获取遗留的地区
    public List<String> getLeaveArea(List<JSONObject> circuits, List<String> areaList){
        List<String> result = new ArrayList<>();
        for(int i = 0 ;i<areaList.size();i++){
            if(containTest_list(areaList.get(i), circuits)==false){
                result.add(areaList.get(i));
            }
        }
        return result;
    }

    public List<JSONObject> getLeaveArea_circuit(List<JSONObject> circuits, List<JSONObject> circuitsFSC){
        List<JSONObject> result = new ArrayList<>();
        if(Objects.isNull(circuitsFSC) || circuitsFSC ==null || circuitsFSC.size()<=0){
            return result;
        }
        for(int i = 0 ;i<circuitsFSC.size();i++){
            if(containTest_list(circuitsFSC.get(i).getString("areacode"), circuits)==false){
                result.add(circuitsFSC.get(i));
            }
        }
        return result;
    }

    public boolean containTest_list(String t1, List<JSONObject> t2){
        boolean result = false;
        for(int i =0;i<t2.size();i++){
            if(trim(t1).equals(trim(t2.get(i).getString("areacode")))){
                result = true;
                break;
            }
        }
        return result;
    }

    public List<JSONObject> getCircuitFSC(JSONObject param){
        List<JSONObject> circuits = new ArrayList<>();
        for(String ds :  NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.keySet()){
            JSONObject pageResponse = custLinkInterfaceDao.cm_Link_code_query(param, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
            if(pageResponse !=null || !Objects.isNull(pageResponse)){
                circuits.add(pageResponse);
                pageResponse.put("areacode", ds);
            }
        }
        return circuits;
    }

    public Integer savePairCircuits(String a_code, String z_code, String risk_type, String risk_desc,String ds){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("a_code", a_code);
        jsonObject.put("z_code", z_code);
        jsonObject.put("risk_type", risk_type);
        jsonObject.put("risk_desc", risk_desc);
        jsonObject.put("areacode", ds);
        return custLinkInterfaceDao.pair_circuit_input_insert_jt(jsonObject,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));


    }
}
