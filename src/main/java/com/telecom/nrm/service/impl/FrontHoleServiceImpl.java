package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.FrontHoleService;
import com.telecom.nrm.service.GisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FrontHoleServiceImpl implements FrontHoleService {
    @Autowired
    GisService gisService;

    /**
     * 计算管道段是否为局前井
     * @param pipeSegmentList
     * @param deviceList
     */
    public void computeFrontHole(List<JSONObject> pipeSegmentList, List<JSONObject> deviceList) {
        for (JSONObject pipeSegment : pipeSegmentList) {
            Double a_pos_x = pipeSegment.getDouble("a_pos_x");
            Double a_pos_y = pipeSegment.getDouble("a_pos_y");
            Double z_pos_x = pipeSegment.getDouble("z_pos_x");
            Double z_pos_y = pipeSegment.getDouble("z_pos_y");
            boolean isFront = false;
            for (JSONObject device : deviceList) {
                Double pos_x=  device.getDouble("pos_x");
                Double pos_y=  device.getDouble("pos_y");

                if (ObjectUtil.isNotEmpty(a_pos_x) && ObjectUtil.isNotEmpty(a_pos_y) && ObjectUtil.isNotEmpty(pos_x) && ObjectUtil.isNotEmpty(pos_y)) {
                    double distance = gisService.haversine(a_pos_y, a_pos_x, pos_y, pos_x);
                    if (distance<=500) {
                        isFront = true;
                        break;
                    }
                }

                if (ObjectUtil.isNotEmpty(z_pos_x) && ObjectUtil.isNotEmpty(z_pos_y) && ObjectUtil.isNotEmpty(pos_x) && ObjectUtil.isNotEmpty(pos_y)) {
                    double distance = gisService.haversine(z_pos_y, z_pos_x, pos_y, pos_x);
                    if (distance<=500) {
                        isFront = true;
                        break;
                    }
                }
            }
            pipeSegment.put("is_front",isFront);

        }
        log.info("is_front 计算完毕");
    }
}
