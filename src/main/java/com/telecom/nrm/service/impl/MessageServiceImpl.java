package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.MessageService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 消息发送服务实现类
 */
@Service
@Slf4j
public class MessageServiceImpl implements MessageService {

    @Value("${weixin.api.url:http://jsjteop.telecomjs.com:8764/wechat_robot/sendMessage}")
    private String weixinApiUrl;

    @Value("${weixin.api.appId:392dde9ac50f1add84a673c74258e3c8}")
    private String weixinAppId;

    @Value("${weixin.api.appKey:cf16e48a251423c443d2b7609103c113}")
    private String weixinAppKey;

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public JSONObject sendWeixinMessage(JSONObject params) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-APP-ID", weixinAppId);
        headers.set("X-APP-KEY", weixinAppKey);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);

        System.out.println("发送微信");
        System.out.println(request.getBody());
   

        
        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(weixinApiUrl, request, String.class);

        System.out.println(response.toString());
        return JSON.parseObject(response.getBody());
    }

  
} 