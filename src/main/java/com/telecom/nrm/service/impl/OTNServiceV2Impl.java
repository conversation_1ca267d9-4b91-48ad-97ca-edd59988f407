package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.domain.topo.TopoMode;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class OTNServiceV2Impl implements OTNService {

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    CommonCircuitService commonCircuitService;

    @Autowired
    IpranService ipranService;

    @Autowired
    MstpService mstpService;

    @Override
    public JSONObject queryOTNRoute(JSONObject request) {

        String code = request.getString("code");
        String access_code = request.getString("access_code");

        JSONObject cacheQuery =new JSONObject();
        cacheQuery.put("access_code",access_code);
        cacheQuery.put("circuit_code",code);
        cacheQuery.put("area_code","OTN");
        JSONObject cacheResult = ipranService.queryCache(cacheQuery);
        if (null != cacheResult && CollectionUtils.isNotEmpty(cacheResult.getJSONArray("nodes"))) {
            JSONArray devices = cacheResult.getJSONArray("nodes");
            List<JSONObject> result =new ArrayList<>();
            result= JSONObject.parseArray(devices.toString(),JSONObject.class);
            mstpService.deviceAddAlarmInfo((result));
            cacheResult.put("nodes",result);
            return cacheResult;
        }
        JSONObject result = queryOTNRoute_1(request);

        List<JSONObject> nodes = JSONObjectUtil.jsonObjectArrayToList(result.getJSONArray("nodes"));
        if (ObjectUtil.isEmpty(nodes)) {
            result = queryOTNRoute_2(request);
        }

        ipranService.saveCache(result,access_code,"OTN",code);
        JSONArray  devices = result.getJSONArray("nodes");
        if (CollectionUtils.isNotEmpty(devices)) {
            mstpService.deviceAddAlarmInfo((JSONObject.parseArray(devices.toString(), JSONObject.class)));
        }
        return result;
    }


    public JSONObject queryOTNRoute_1(JSONObject request) {
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");


        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_otn_circuit_route").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("otn_circuit","code",code))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);

        return graphToTOPO(graph);
    }

    public JSONObject queryOTNRoute_2(JSONObject request) {
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");


        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_otn_circuit_route_2").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("otn_circuit_route","code",code))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);


        return graphToTOPO(graph);
    }


    public JSONObject graphToTOPO (Graph graph) {
        Set<Node> nodes = graph.getNodes();
        Set<Edge> edges = graph.getEdges();
        Node deviceNode = nodes.stream().filter(n->n.getId().equals("otn_device")).collect(Collectors.toList()).get(0);
        Node linkNode = nodes.stream().filter(n->n.getId().equals("otn_link")).collect(Collectors.toList()).get(0);
        Edge linkCarryLinkEdge = edges.stream().filter(e->e.getAId().equals("otn_link") && e.getBId().equals("otn_circuit_route") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);


        List<JSONObject> devices = deviceNode.getData();
        List<JSONObject> links = linkNode.getData();
        List<JSONObject> linkRelations = linkCarryLinkEdge.getData();

        linkRelations.removeIf(d->d.getString("upper_link_id").equals(d.getString("lower_link_id")));

        JSONObject result = commonCircuitService.sortCircuitTopology(devices,links,linkRelations, TopoMode.NET, "");
        return result;
    }










}
