package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CutoverDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.ProjectService;
import com.telecom.nrm.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class ProjectServiceImpl implements ProjectService {
    @Autowired
    CutoverDao cutoverDao;


    @Override
    public JSONObject saveCutOverProject(JSONObject params) {
        JSONObject info = cutoverDao.saveCutOverProject(params, NRMConstants.SHARDING_GRAPH_DB);
        saveProjectRes(params, info);
        return info;
    }

    public JSONObject saveProject(@RequestBody JSONObject params) {
        JSONObject info = cutoverDao.saveProject(params, NRMConstants.SHARDING_GRAPH_DB);
        saveProjectRes(params, info);
        return info;
    }

    void saveProjectRes(JSONObject params, JSONObject info) {
        if (ObjectUtil.isNotEmpty(params.getJSONArray("resList"))) {
            JSONArray resList = params.getJSONArray("resList");
            JSONObject resListParam = new JSONObject();
            for (int i = 0; i < resList.size(); i++) {
                JSONObject o = resList.getJSONObject(i);
                o.put("project_id", info.getString("id"));
                String entitySpecCode = o.getString("entity_spec_code");
                if (ObjectUtil.isNotEmpty(entitySpecCode)) {
                    if (entitySpecCode.equals("cbl_sect") || entitySpecCode.equals("光缆段")) {
                        o.put("entity_spec_id", "1121000002");
                        o.put("entity_spec_name", "光缆段");
                    }else if (entitySpecCode.equals("net_code") || entitySpecCode.equals("光缆")) {
                        o.put("entity_spec_id", "1211200002");
                        o.put("entity_spec_name", "光缆");
                    }else if (entitySpecCode.equals("opt_road") || entitySpecCode.equals("光路") || entitySpecCode.equals("fiber_busi_node")) {
                        o.put("entity_spec_id", "1132400006");
                    }
                }
                if (ObjectUtil.isEmpty(o.getString("res_role"))) {
                    o.put("res_role", "source");
                }
            }
            resListParam.put("project_id", info.getString("id"));
            resListParam.put("resList", resList);

            cutoverDao.saveProjectRes(resListParam, NRMConstants.SHARDING_GRAPH_DB);
        }else{
            JSONObject resListParam = new JSONObject();
            resListParam.put("project_id", info.getString("id"));
            resListParam.put("resList", new ArrayList<>());
            cutoverDao.deleteProjectRes(resListParam, NRMConstants.SHARDING_GRAPH_DB);
        }
    }

    public String generateBatchNo() {
        long timestamp = System.currentTimeMillis();
        int randomPart = (int) (Math.random() * 10000);
        return "" + timestamp + randomPart;
    }


}
