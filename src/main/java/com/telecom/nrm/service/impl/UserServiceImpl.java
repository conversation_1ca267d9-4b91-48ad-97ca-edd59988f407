package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.UserDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    UserDao userDao;

    public List<JSONObject> get_user_info(JSONObject param){
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setSize(1000);
        pageable.setPage(1);
        PageResponse<JSONObject> queryUsers = userDao.userPageQuery(param, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return queryUsers.getData();
    }
}
