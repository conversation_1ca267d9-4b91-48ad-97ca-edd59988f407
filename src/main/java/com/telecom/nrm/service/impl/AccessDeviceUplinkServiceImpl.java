package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.controller.GraphSceneController;
import com.telecom.nrm.dao.AccessDeviceUplinkDao;/*
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;*/
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.AccessDeviceUplinkService;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.OptRoadPairService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.PrintStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class AccessDeviceUplinkServiceImpl implements AccessDeviceUplinkService
{
    private final Logger log = LoggerFactory.getLogger(GraphSceneController.class);
    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    GraphService graphService;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    OptRoadPairService optRoadPairService;

    @Autowired
    AccessDeviceUplinkDao accessDeviceUplinkDao;



    public JSONObject opt_pair_analyze(List<JSONObject> accessDevice, JSONObject request){

        List<String> codes = new ArrayList();
        accessDevice.stream().forEach(t->codes.add(t.getString("code")));


        String ds = request.getString("ds");
        List<JSONObject> optRoadGroupMembers = new ArrayList<>();
        int routeNo=1;
        for (String code : codes) {
            JSONObject optRoadGroupMember = new JSONObject();
            optRoadGroupMember.put("code", code);
            optRoadGroupMember.put("route_no", routeNo);
            optRoadGroupMembers.add(optRoadGroupMember);
        }
        JSONObject result =optRoadPairService.opt_pair_analyse(optRoadGroupMembers,ds);

        JSONObject need_save = new JSONObject();
        need_save.put("object_id", accessDevice.get(0).getString("group_id"));
        need_save.put("object_type", "optGroup");
        need_save.put("check_result",result.getString("check_result"));
        log.info("need_save"+need_save);
        accessDeviceUplinkDao.pm_risk_check_insert(need_save, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        return result;
    }

    public JSONObject risk_analyze(JSONObject request) {
        request.put("group_id", request.getString("id"));
        List<JSONObject> accessDevice =  accessDeviceUplinkDao.pm_opt_road_group_element_query_details(request , NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
        JSONObject result=new JSONObject();
        log.info("accessDevice:"+accessDevice);
        if(ObjectUtil.isNotEmpty(accessDevice)) {
            result=  opt_pair_analyze(accessDevice, request);
        }
        return result;
    }



    public JSONObject optPair_statistic_dictionary(JSONObject jsonObject) {

        JSONObject result = new JSONObject();

        result.put("totalCount", accessDeviceUplinkDao.pm_opt_road_group_count_device(new JSONObject(),  NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));

        List<JSONObject> staticResult = accessDeviceUplinkDao.pm_opt_road_group_state_statistic_query_device(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));

        for (JSONObject group : staticResult) {
            result.put(group.getString("check_result"), group.getInteger("data_count"));
        }
        /*
        jsonObject.put("result","同管道");
        result.put("samePipeSementCount", accessDeviceUplinkDao.pm_opt_road_group_state_statistic_query_device(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","同光缆");
        result.put("sameCableCount", accessDeviceUplinkDao.pm_opt_road_group_state_statistic_query_device(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","正常");
        result.put("normal", accessDeviceUplinkDao.pm_opt_road_group_state_statistic_query_device(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","已忽略异常");
        result.put("ignoreCount", accessDeviceUplinkDao.pm_opt_road_group_state_statistic_query_device(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        jsonObject.put("result","已整改");
        result.put("checkNormalCount", accessDeviceUplinkDao.pm_opt_road_group_state_statistic_query_device(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))));
        */
        return result;
    }

}
