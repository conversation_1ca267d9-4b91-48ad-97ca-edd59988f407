package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.domain.topo.TopoMode;
import com.telecom.nrm.domain.topo.TopoSorter;
import com.telecom.nrm.service.CommonCircuitService;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.PONService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class PONServiceImpl implements PONService {

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    CommonCircuitService commonCircuitService;

    Set<String> terminalSpecIds = Arrays.asList("1041192016","1041192017","1041192018","1041192019","1041192020","1041192021",
            "1041192024","1049992010","3699","3702","2954","1024192001","1024192002","1024192003","1024192004","1024192005","1024192006",
            "1024192007","1026192002","1021192001","1021192002","1021192003","1021192004",
            "1021192005","1021192006","1021192007","1029892031","1029892069","1029892070","1029892071","1029892072","1029892073","1029892074",
            "1029892075","1029892076","1029892077","1029892078","1029892079","1028392006","3156","3360","2497","2498","2499","9310391028",
            "9310391029","3782","4133","4094","4322","1028492014","1028492026","1028492027","1028492028","1028492029","1028492030","1028492031",
            "1028492032","1028492033","1028492037","1028492048","1028492049","1028492050","1028492051","1028492052","1028492053","1028492056",
            "3938","3939","3940","1028492074","1028492075","1028492078","1028492079","1028492089","1028492090","1028492091","1028492092",
            "9310292011","3943","4146","4147","4354","4355","2023192376","1026192001","3831","3832","4388","3836","3024","3240","2883","3385").stream().collect(Collectors.toSet());


    @Override
    public JSONObject getResourceTree(JSONObject request) {
        String areaCode = request.getString("areaCode");
        String accessCodes = request.getString("accessCodes");
        List<String> access_code_s = Arrays.asList( accessCodes.split(","));
        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_cfs_2_pon").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cfs","access_code_s",access_code_s)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);
        JSONObject result = graphService.getPaths(data, Arrays.asList("cfs","PON链路","光路","光缆段"));
        return result;
    }

    String convertId(String gid) {
        gid = gid.replaceAll("olt\\$\\$","");
        gid = gid.replaceAll("user_terminal\\$\\$","");
        gid = gid.replaceAll("access_device\\$\\$","");
        gid = gid.replaceAll("device\\$\\$","");
        return gid;
    }

    void convertNodeGId(List<JSONObject> data) {
        data.forEach(d->{
            d.put("gid", convertId(d.getString("gid")));
        });
    }

    void convertRelationGId(List<JSONObject> data) {
        data.forEach(d->{
            d.put("source_id", convertId(d.getString("source_id")));
            d.put("target_id", convertId(d.getString("target_id")));
        });
    }

    Map<String,JSONObject> listToMap(List<JSONObject> list) {
        Map<String,JSONObject> map = new HashMap<>();
        for (JSONObject o: list) {
            map.put(o.getString("gid"),o);
        }
        return map;
    }

    boolean isNodeExists (List<JSONObject> nodes, JSONObject d) {
        if(ObjectUtil.isEmpty(nodes)) return false;
        List<String> ids = nodes.stream().map(n->n.getString("gid")).collect(Collectors.toList());
        if(ObjectUtil.isEmpty(ids)) return false;
        if (ObjectUtil.isEmpty(d)) return false;
        if (ids.contains(d.getString("gid"))) {
            return true;
        }else{
            return false;
        }
    }

    boolean isLinkExists (List<JSONObject> links, JSONObject l) {
        for (JSONObject l1: links) {
            if (l.getString("gid").equals(l1.getString("gid"))) {
                return true;
            }else if (ObjectUtil.isNotEmpty(l.getString("source_id")) && ObjectUtil.isNotEmpty(l.getString("target_id"))
                    && ObjectUtil.isNotEmpty(l1.getString("source_id")) && ObjectUtil.isNotEmpty(l1.getString("target_id"))
                    && l.getString("source_id").equals(l1.getString("source_id")) &&
                    l.getString("target_id").equals(l1.getString("target_id"))
                    &&l.getString("spec_id").equals("1131100001")
            ){
                return true;
            }else if (ObjectUtil.isNotEmpty(l.getString("source_id")) && ObjectUtil.isNotEmpty(l.getString("target_id"))
                    && ObjectUtil.isNotEmpty(l1.getString("source_id")) && ObjectUtil.isNotEmpty(l1.getString("target_id")) &&
                    l.getString("source_id").equals(l1.getString("target_id")) &&
                    l.getString("target_id").equals(l1.getString("source_id"))
                &&l.getString("spec_id").equals("1131100001")
            ){
                return true;
            }

        }
        return false;
    }

    void addToNodes (List<JSONObject> nodes, JSONObject d) {
        if (!isNodeExists(nodes,d) && ObjectUtil.isNotEmpty(d)) {
            nodes.add(d);
        }
    }

    void addToLinks (List<JSONObject> links, List<JSONObject> nodes,Map<String,JSONObject> idMapDevice,JSONObject l) {
        String sourceId = l.getString("a_physic_device_id");
        String targetId = l.getString("z_physic_device_id");

        if(ObjectUtil.isNull(sourceId) || ObjectUtil.isNull(targetId)){
            return;
        }
        l.put("source_id", sourceId);
        l.put("target_id", targetId);



        if (sourceId.equals(targetId)) return;
        if (!isLinkExists(links,l)) {
            JSONObject source = idMapDevice.get(sourceId);
            JSONObject target = idMapDevice.get(targetId);
            addToNodes(nodes, source);
            addToNodes(nodes, target);
            links.add(l);
        }
    }




    /**
     *
     * @param request
     * @return
     */
    public JSONObject getCableSegmentTree(JSONObject request) {
        String areaCode = request.getString("areaCode");
        JSONArray access_code_s = request.getJSONArray("accessCodes");
        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_cfs_2_pon").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cfs","access_code_s",access_code_s)).build();
        Graph graph = graphApiService.doApi(graphRequest);


        JSONObject result = buildTopo(graphService.getData(graph));


        sortPipeSegments(result,graphService.getData(graph)); // pqc

        return result;
    }

    public JSONObject buildTopo(JSONObject data) {
        JSONObject result = new JSONObject();
        // JSONObject data = graphService.getData(graph);
        List<JSONObject> nodes = new ArrayList<>();
        List<JSONObject> links = new ArrayList<>();
        List<JSONObject> cfsList  = graphService.filterNodeByLabel(data,"cfs");
        Map<String,JSONObject> idMapCFS =listToMap(cfsList);



        List<JSONObject> accessDevicesList  = graphService.filterNodeByLabel(data,"接入设备");

        nodes.addAll(cfsList);
        nodes.addAll(accessDevicesList);
        List<JSONObject> accessPaths = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("cfs","用户终端","接入设备")).getJSONArray("paths"));



        List<JSONObject> oltList  =graphService.filterNodeByLabel(data, "olt");
        List<JSONObject> deviceList  = graphService.filterNodeByLabel(data,"设备");

        List<JSONObject> userTerminalList = graphService.filterNodeByLabel(data,"用户终端");

        List<JSONObject> etherLinkList = graphService.filterNodeByLabel(data,"以太网链路");
        List<JSONObject> ether2roadList = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("以太网链路","光路")).getJSONArray("paths"));

        List<JSONObject> mstpLinkList = graphService.filterNodeByLabel(data,"mstp电路").stream().distinct().collect(Collectors.toList());
        List<JSONObject> mstp2roadList = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("mstp电路","光路")).getJSONArray("paths"));


        List<JSONObject> ponLinkList  =graphService.filterNodeByLabel(data,"PON链路");
        List<JSONObject> optRoadList  = graphService.filterNodeByLabel(data,"光路");
        List<JSONObject> pon2roadList = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("PON链路","光路")).getJSONArray("paths"));


        List<JSONObject> jxgxList  = graphService.filterNodeByLabel(data,"局向光纤");
        jxgxList=jxgxList.stream().filter(jx->!jx.getString("a_physic_device_id").equals(jx.getString("z_physic_device_id"))).collect(Collectors.toList());
        List<JSONObject> road2JxgxList = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("光路","局向光纤")).getJSONArray("paths"));



        graphService.buildTree(data, "光缆段","管道段","pipelineList");

        List<JSONObject> cableSegmentList  = graphService.filterNodeByLabel(data,"光缆段");
        List<JSONObject> jxgx2CableSegmentList = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("局向光纤","光缆段")).getJSONArray("paths"));

        graphService.buildReverseTree(data,"cfs","光缆段","cfs_list");

        graphService.mapProperty(data,"光缆段","cfs_list", (c)->{
            return JSONObject.parseObject(JSONObject.toJSONString(c));
        });




        for (JSONObject cableSegment: cableSegmentList) {
            cableSegment.put("a_physic_device_id", cableSegment.getString("a_device_id"));
            cableSegment.put("z_physic_device_id", cableSegment.getString("z_device_id"));
            List<JSONObject> relaCfsList =JSONObjectUtil.jsonObjectArrayToList( cableSegment.getJSONArray("cfs_list"));
            cableSegment.put("cfs_num", relaCfsList.size());

        }

        convertNodeGId(userTerminalList);
        convertNodeGId(accessDevicesList);
        convertNodeGId(oltList);
        convertNodeGId(deviceList);

        nodes.addAll(userTerminalList);
        Map<String,JSONObject> idMapOlt = listToMap(oltList);
        Map<String,JSONObject> idMapUserTerminal = listToMap(userTerminalList);

        convertRelationGId(accessPaths);

        // 如果下层有光路，则剔除接入链路
        Map<String,JSONObject> idMapDevice = listToMap(deviceList);
        idMapDevice.putAll(listToMap(userTerminalList));
        // idMapDevice.putAll(listToMap(accessDevicesList));
        // idMapDevice.putAll(listToMap(oltList));

        Map<String,String> terminalMapAccess = new HashMap<String,String>();
        for (JSONObject p:accessPaths) {
            String sourceId = p.getString("source_id");
            String targetId  = p.getString("target_id");
            boolean hasOptRoad = false;
            for (JSONObject f: optRoadList) {
                String aPhysicDeviceId=Optional.ofNullable(f.getString("a_physic_device_id")).orElse("");
                String zPhysicDeviceId=Optional.ofNullable(f.getString("z_physic_device_id")).orElse("");
                if (sourceId.equals(aPhysicDeviceId) && targetId.equals(zPhysicDeviceId) || sourceId.equals(zPhysicDeviceId) && targetId.equals(zPhysicDeviceId)) {
                    hasOptRoad = true;
                }
            }
            if (hasOptRoad) continue;
            JSONObject aDevice = idMapDevice.get(p.getString("source_id"));
            JSONObject bDevice = idMapDevice.get(p.getString("target_id"));

            terminalMapAccess.put(sourceId,targetId);

            // links.add(p);

            if (ObjectUtil.isNotEmpty(aDevice) && ObjectUtil.isNotEmpty(bDevice) && terminalSpecIds.contains(aDevice.getString("spec_id")) && terminalSpecIds.contains(bDevice.getString("spec_id")) ) {
                continue; // 如果接入path是终端连接终端,则不生成链路
            };

            String id = UUID.randomUUID().toString();
            p.put("id",id);
            p.put("gid",id);


            links.add(p);
        }
        // links.addAll(accessPaths);
        sortEtherLink(etherLinkList, idMapUserTerminal);
        sortPONLink(ponLinkList, idMapOlt);

        sortChildrenLinks(etherLinkList, optRoadList, ether2roadList );
        sortMSTPChildrenLinks(mstpLinkList, optRoadList, mstp2roadList,idMapDevice,terminalMapAccess);
        sortChildrenLinks(ponLinkList, optRoadList, pon2roadList );
        sortChildrenLinks(optRoadList, jxgxList, road2JxgxList );
        repairOptRoads(optRoadList, idMapDevice);
        sortChildrenLinks(jxgxList, cableSegmentList, jxgx2CableSegmentList);





        // 返回光缆段
        for (JSONObject jx: jxgxList) {
            if (ObjectUtil.isEmpty(jx.getJSONArray("children"))) {
                addToLinks(links,nodes,idMapDevice,jx);
            }else{
                List<JSONObject> segmentList = JSONObjectUtil.jsonObjectArrayToList(jx.getJSONArray("children"));
                for (JSONObject seg: segmentList) {
                    addToLinks(links,nodes,idMapDevice,seg);
                }
            }
        }

        result.put("nodes",nodes);
        result.put("links",links);

        TopoSorter sorter = new TopoSorter(result);
        sorter.sort("cfs");

        return result;
    }



    public JSONObject buildAlarmTopo(JSONObject data) {
        JSONObject result = new JSONObject();
        // JSONObject data = graphService.getData(graph);
        List<JSONObject> nodes = new ArrayList<>();
        List<JSONObject> links = new ArrayList<>();
        List<JSONObject> ponportList  = graphService.filterNodeByLabel(data,"PON端口");
        List<JSONObject> oltList  = graphService.filterNodeByLabel(data,"olt");
        List<JSONObject> deviceList  = graphService.filterNodeByLabel(data,"设备");
        List<JSONObject> ponLinkList  =graphService.filterNodeByLabel(data,"PON链路");
        List<JSONObject> optRoadList  = graphService.filterNodeByLabel(data,"光路");
        List<JSONObject> jxgxList  = graphService.filterNodeByLabel(data,"局向光纤");
        List<JSONObject> cableSegmentList  = graphService.filterNodeByLabel(data,"光缆段");


        nodes.addAll(oltList);
        nodes.addAll(ponportList);

        List<JSONObject> olt2ponportPaths = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("olt","PON端口")).getJSONArray("paths"));
        links.addAll(olt2ponportPaths);

        List<JSONObject> pon2roadPaths = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("PON链路","光路")).getJSONArray("paths"));
        List<JSONObject> road2JxgxPaths = JSONObjectUtil.jsonObjectArrayToList(graphService.getPaths(data, Arrays.asList("光路","局向光纤")).getJSONArray("paths"));
        List<JSONObject> jxgx2CableSegmentList = JSONObjectUtil.jsonObjectArrayToList( graphService.getPaths(data, Arrays.asList("局向光纤","光缆段")).getJSONArray("paths"));


        jxgxList=jxgxList.stream().filter(jx->!jx.getString("a_physic_device_id").equals(jx.getString("z_physic_device_id"))).collect(Collectors.toList());


        graphService.buildTree(data, "光缆段","管道段","pipelineList");
        graphService.buildReverseTree(data,"PON端口","光缆段","pon端口_list");

        graphService.mapProperty(data,"光缆段","PON端口", (c)->{
            return JSONObject.parseObject(JSONObject.toJSONString(c));
        });


        for (JSONObject cableSegment: cableSegmentList) {
            cableSegment.put("a_physic_device_id", cableSegment.getString("a_device_id"));
            cableSegment.put("z_physic_device_id", cableSegment.getString("z_device_id"));
            List<JSONObject> relaCfsList = JSONObjectUtil.jsonObjectArrayToList(cableSegment.getJSONArray("pon端口_list"));
            cableSegment.put("pon端口_num", relaCfsList.size());

        }
        convertNodeGId(oltList);
        convertNodeGId(deviceList);

        Map<String,JSONObject> idMapOlt = listToMap(oltList);


        // 如果下层有光路，则剔除接入链路
        Map<String,JSONObject> idMapDevice = listToMap(deviceList);



        // links.addAll(accessPaths);
        sortPONLink(ponLinkList, idMapOlt);  //1


        sortChildrenLinks(ponLinkList, optRoadList, pon2roadPaths ); //1
        sortChildrenLinks(optRoadList, jxgxList, road2JxgxPaths);  //1
        repairOptRoads(optRoadList, idMapDevice);    //1
        sortChildrenLinks(jxgxList, cableSegmentList, jxgx2CableSegmentList); //1





        //过滤局向光纤，如果没有下面的链路，就造一条
        for (JSONObject jx: jxgxList) {
            if (ObjectUtil.isEmpty(jx.getJSONArray("children"))) {
                //TODO:修改两端的id
                addToLinks(links,nodes,idMapDevice,jx);
            }else{
                List<JSONObject> segmentList = JSONObjectUtil.jsonObjectArrayToList(jx.getJSONArray("children"));
                for (JSONObject seg: segmentList) {
                    addToLinks(links,nodes,idMapDevice,seg);
                }
            }
        }

        result.put("nodes",nodes);
        result.put("links",links);

        TopoSorter sorter = new TopoSorter(result);
        sorter.sort("PON端口");

        return result;
    }


    public void sortcableSegmentList(List<JSONObject> cableSegmentList, List<JSONObject> deviceList){


        mapFacilityIds(cableSegmentList,deviceList);


        // 第一层循环遍历第一个列表
        for (JSONObject cableSegment : cableSegmentList) {

            sortPipelines(cableSegment);

        }


    }



    void sortPipeSegments(JSONObject result,JSONObject graphData) {

        //-----------------------------------------------以下为peiqicheng添加-----------------------------------------------------
//        先列出需要的字段：
//        accs_nbr_no: bseSect.accs_nbr_no,
//                cable_id: bseSect.cbl_id,
//                cable_no: bseSect.cable_no,
//                cable_name: bseSect.cable_name,
//                cable_spec_id: bseSect.cable_spec_id,
//                cbl_sect_id: bseSect.cbl_sect_id,
//                cbl_sect_no: bseSect.cbl_sect_no,
//                cbl_sect_name: bseSect.cbl_sect_name,
//                cbl_sect_spec_id: bseSect.cbl_sect_spec_id,
//                bse_sect_id: bseSect.bse_sect_id,
//                bse_sect_no: bseSect.bse_sect_no,
//                bse_sect_name: bseSect.bse_sect_name,
//                bse_sect_spec_id: bseSect.bse_sect_spec_id,
//                a_bse_eqp_id: bseSect.a_bse_eqp_id,
//                a_bse_eqp_no: bseSect.a_bse_eqp_no,
//                a_bse_eqp_name: bseSect.a_bse_eqp_name,
//                a_bse_eqp_spec_id: bseSect.a_bse_eqp_spec_id,
//                a_bse_eqp_x: bseSect.aposx,               // 映射aposx到a_bse_eqp_x
//                a_bse_eqp_y: bseSect.aposy,               // 映射aposy到a_bse_eqp_y
//                z_bse_eqp_id: bseSect.z_bse_eqp_id,
//                z_bse_eqp_no: bseSect.z_bse_eqp_no,
//                z_bse_eqp_name: bseSect.z_bse_eqp_name,
//                z_bse_eqp_spec_id: bseSect.z_bse_eqp_spec_id,
//                z_bse_eqp_x: bseSect.zposx,                // 映射zposx到z_bse_eqp_x
//                z_bse_eqp_y: bseSect.zposy,                // 映射zposy到z_bse_eqp_y
//
//
//        List<JSONObject> pipelineList  =graphService.filterNodeByLabel(data,"管道段");
//        graphService.buildTree(data, "cfs","管道段","pipelist");
//        List<JSONObject> cfsListpqc  =graphService.filterNodeByLabel(data,"cfs");


//        graphService.buildTree(data, "cfs","管道段","cfslist");
        // JSONObject graphData = JSONObject.parseObject(JSONObject.toJSONString(graphService.getData(graph)));
        graphService.buildTree(graphData, "cfs","光缆段","cablesectList");
        graphService.buildTree(graphData, "光缆段","管道段","pipelineList");
        List<JSONObject> deviceList  = graphService.filterNodeByLabel(graphData,"设备");
        List<JSONObject> cfstreelist  =graphService.filterNodeByLabel(graphData,"cfs");
        List<JSONObject> cableSegmentList  = graphService.filterNodeByLabel(graphData,"光缆段");


        result.put("cfstreelist",cfstreelist);
        result.put("deviceList",deviceList);

        //给cableSegmentList增加facility_id
        mapFacilityIds(cableSegmentList,deviceList);


        // 第一层循环遍历第一个列表
        for (JSONObject cableSegment : cableSegmentList) {

            //todo:就这里需要完成
            sortPipelines(cableSegment);

        }
    }







    public static void swapAFieldsWithZFields(JSONObject jsonObject) {
        // 遍历JSONObject中的所有键值对
        for (String key : jsonObject.keySet()) {
            // 检查字段名是否以"a_"开头
            if (key.startsWith("a_")) {
                // 构造对应的z_字段名
                String zKey = key.replaceFirst("^a_", "z_");
                // 检查对应的z_字段是否存在
                if (jsonObject.containsKey(zKey)) {
                    // 交换a_和z_字段的值
                    Object temp = jsonObject.get(key);
                    jsonObject.put(key, jsonObject.get(zKey));
                    jsonObject.put(zKey, temp);
                }
            }
        }
    }

    public static void sortPipelines(JSONObject cableSegment) {
        //function sortPipelines(pipelines, initialFacilityId, initialRefPosX, initialRefPosY):
        //

        List<JSONObject> sortedPipelines  = new ArrayList<>();
        JSONArray pipelineArray = cableSegment.getJSONArray("pipelineList");
        JSONObject initialFacility = new JSONObject();
        initialFacility.put("facility_id", cableSegment.getString("a_facility_id"));
        initialFacility.put("pos_x", cableSegment.getString("a_pos_x"));
        initialFacility.put("pos_y", cableSegment.getString("a_pos_y"));

        //如果找不到一个，那就直接结束
        if(ObjectUtil.isNull(pipelineArray) || pipelineArray.size() == 0){
            return ;
        }


        //提取第一段pipeline
        JSONObject startPipeline = findPipelineWithFacility(pipelineArray, initialFacility);

        //如果找不到一个，那就直接结束
        if(startPipeline.isEmpty()){
            return ;
        }

        sortedPipelines.add(startPipeline);
        pipelineArray.remove(startPipeline);


        while(!pipelineArray.isEmpty()){

            JSONObject lastSortedPipeline = sortedPipelines.get(sortedPipelines.size()-1);
            JSONObject nextFacility = new JSONObject();


            nextFacility.put("facility_id", lastSortedPipeline.getString("z_facility_id"));
            nextFacility.put("pos_x", lastSortedPipeline.getString("z_pos_x"));
            nextFacility.put("pos_y", lastSortedPipeline.getString("z_pos_y"));


            //如果找不到一个，那就直接结束
            if(ObjectUtil.isNull( nextFacility.get("pos_x")) || ObjectUtil.isNull(nextFacility.get("pos_y") )){
                //递归找到上一个有坐标的点
                JSONObject lastPointHasPOS = findlastPointHasPOS(sortedPipelines);

                nextFacility.put("pos_x",  lastPointHasPOS.get("pos_x"));
                nextFacility.put("pos_y",  lastPointHasPOS.get("pos_y"));


            }


            JSONObject nextPipeline = findPipelineWithFacility(pipelineArray, nextFacility);

            sortedPipelines.add(nextPipeline);
            pipelineArray.remove(nextPipeline);

        }

        cableSegment.remove("pipelineList");
        cableSegment.put("sortedPipelines",sortedPipelines);

    }

    public static JSONObject findlastPointHasPOS(List<JSONObject> sortedPipelines){
        JSONObject point = new JSONObject();
        for (int i = sortedPipelines.size() - 1; i >= 0; i--) {
            JSONObject pipeline = sortedPipelines.get(i);

            // 检查是否包含所有必要的坐标字段
            if (ObjectUtil.isNotNull(pipeline.get("z_pos_x")) && ObjectUtil.isNotNull(pipeline.get("z_pos_y")) ){

                point.put("pos_x",pipeline.get("z_pos_x"));
                point.put("pos_y",pipeline.get("z_pos_y"));

                return point;

            }else if(ObjectUtil.isNotNull(pipeline.get("a_pos_x")) && ObjectUtil.isNotNull(pipeline.get("a_pos_y"))){

                point.put("pos_x",pipeline.get("a_pos_x"));
                point.put("pos_y",pipeline.get("a_pos_y"));

                return point;

            }

        }
        // 如果没有找到完整坐标的点，则返回null
        return null;

    }





    //找到完美匹配的或者最接近的
    public static JSONObject findPipelineWithFacility(JSONArray pipelines,JSONObject initialFacility){
        JSONObject closestPipeline = new JSONObject();
        double closestDistance = Double.MAX_VALUE;


        String facilityId = initialFacility.getString("facility_id");

        for(int i=0;i<pipelines.size();i++){
            JSONObject pipeline = pipelines.getJSONObject(i);
            String aFacilityId = pipeline.getString("a_facility_id");
            String zFacilityId = pipeline.getString("z_facility_id");
            JSONObject closestPoint = null;
            double distance;

            // 检查支撑设施完美匹配的
            if (ObjectUtil.equal(aFacilityId,facilityId)) {
                return pipeline; // No need for AZ swap
            } else if ((ObjectUtil.equal(zFacilityId, facilityId)) ){
                swapAFieldsWithZFields(pipeline);
                return pipeline;
            }

            // 如果找不到完美匹配的，就找最接近的

            double posX = 0;
            double posY = 0;
            try{
                posX = Double.parseDouble(initialFacility.getString("pos_x"));
                posY = Double.parseDouble(initialFacility.getString("pos_y"));
            }catch (Exception ex) {
                log.debug(ex.getMessage());
            }

            JSONObject aPoint = getPoint(pipeline, "a");
            JSONObject zPoint = getPoint(pipeline, "z");

            if(ObjectUtil.isNotNull( aPoint.getDouble("pos_x"))&&ObjectUtil.isNotNull( aPoint.getDouble("pos_y"))&&
                    ObjectUtil.isNotNull( zPoint.getDouble("pos_x"))&&ObjectUtil.isNotNull( zPoint.getDouble("pos_y"))){

                double aDistance = calculateDistance(posX, posY, aPoint.getDouble("pos_x"), aPoint.getDouble("pos_y"));
                double zDistance = calculateDistance(posX, posY, zPoint.getDouble("pos_x"), zPoint.getDouble("pos_y"));

                if (aDistance < zDistance) {
                    distance = aDistance;
                    closestPoint = aPoint;
                } else {
                    distance = zDistance;
                    closestPoint = zPoint;
                }

                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestPipeline = pipeline;
                    if (closestPoint.getString("end").equals("z")) {
                        swapAFieldsWithZFields(closestPipeline);
                    }
                }

            }

        }
        return closestPipeline;
    }


    private static JSONObject getPoint(JSONObject pipeline, String end) {
        JSONObject point = new JSONObject();
        point.put("pos_x", pipeline.getDouble(end + "_pos_x"));
        point.put("pos_y", pipeline.getDouble(end + "_pos_y"));
        point.put("end", end);
        return point;
    }




    // 计算两点之间的距离的函数（欧几里得距离）
    private static double calculateDistance(double x1, double y1, double x2, double y2) {
        double dx = x2 - x1;
        double dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    }


    //给cableSegmentList里面的cableSegment加上facility_id和坐标
    public static void mapFacilityIds(List<JSONObject> cableSegmentList, List<JSONObject> deviceList) {
        // 创建一个设备ID到facility_id的映射
        Map<String, String> deviceIdToFacilityIdMap = new HashMap<>();
        Map<String, String> deviceIdToPosXMap = new HashMap<>();
        Map<String, String> deviceIdToPosYMap = new HashMap<>();

        for (JSONObject device : deviceList) {
            String deviceId = device.getString("id");
            String facilityId = device.getString("facility_id");

            String PosX = device.getString("pos_x");
            String PosY = device.getString("pos_y");

            deviceIdToFacilityIdMap.put(deviceId, facilityId);
            deviceIdToPosXMap.put(deviceId, PosX);
            deviceIdToPosYMap.put(deviceId, PosY);
        }

        // 遍历cableSegmentList，更新facility_id
        for (JSONObject cableSegment : cableSegmentList) {
            String aDeviceId = cableSegment.getString("a_device_id");
            String zDeviceId = cableSegment.getString("z_device_id");

            // 设置a_facility_id和z_facility_id
            String aFacilityId = deviceIdToFacilityIdMap.get(aDeviceId);
            if (aFacilityId != null) {
                cableSegment.put("a_facility_id", aFacilityId);
            }
            String zFacilityId = deviceIdToFacilityIdMap.get(zDeviceId);
            if (zFacilityId != null) {
                cableSegment.put("z_facility_id", zFacilityId);
            }

            String aPosX = deviceIdToPosXMap.get(aDeviceId);
            if (aPosX != null) {
                cableSegment.put("a_pos_x", aPosX);
            }
            String aPosY = deviceIdToPosYMap.get(aDeviceId);
            if (aPosY != null) {
                cableSegment.put("a_pos_y", aPosY);
            }

            String zPosX = deviceIdToPosXMap.get(zDeviceId);
            if (zPosX != null) {
                cableSegment.put("z_pos_x", zPosX);
            }
            String zPosY = deviceIdToPosYMap.get(zDeviceId);
            if (zPosY != null) {
                cableSegment.put("z_pos_y", zPosY);
            }


        }
    }














    // 修复局向光纤反向的问题
    void repairOptRoads (List<JSONObject> optRoadList, Map<String,JSONObject> idMapDevice) {
        for (JSONObject optRoad: optRoadList) {
            repairOptRoad(optRoad, idMapDevice);
        }
    }

    void repairOptRoad (JSONObject optRoad, Map<String,JSONObject> idMapDevice) {
        List<JSONObject> children = JSONObjectUtil.jsonObjectArrayToList(optRoad.getJSONArray("children"));
        if (ObjectUtil.isEmpty(children)) return;
        int length = children.size();
        for (int i=0;i<length;i++) {
            JSONObject jxgx = children.get(i);
            if (jxgx.getString("spec_id").equals("1131100003")) {
                // 找到局向光纤
                String aId = jxgx.getString("a_physic_device_id");
                String zId = jxgx.getString("z_physic_device_id");
                JSONObject aDevice = idMapDevice.get(aId);
                JSONObject zDevice = idMapDevice.get(zId);
                if (aDevice.getString("spec_id").equals("1020200001") && zDevice.getString("spec_id").equals("1020200002")) {
                    // 如果局向光纤A端是ODF架,Z端是光交,则反向
                    swapAZ(jxgx);
                    if (i-1>=0) {
                        JSONObject previous = children.get(i-1);
                        if (previous.getString("spec_id").equals("1131100001")) {
                            // 更新上一条链路为硬跳
                            previous.put("z_physic_device_id", jxgx.get("a_physic_device_id"));
                            previous.put("z_port_code", jxgx.get("a_port_code"));
                            previous.put("z_name" , jxgx.get("a_name"));
                            previous.put("z_port_id", jxgx.get("a_port_id"));
                        }
                    }
                    if ((i+1)<length) {
                        JSONObject next = children.get(i+1);
                        if (next.getString("spec_id").equals("1131100001")) {
                            // 更新上一条链路为硬跳
                            next.put("a_physic_device_id", jxgx.get("z_physic_device_id"));
                            next.put("a_port_code", jxgx.get("z_port_code"));
                            next.put("a_name" , jxgx.get("z_name"));
                            next.put("a_port_id", jxgx.get("z_port_id"));
                        }
                    }
                }
            }
        }
    }

    void sortPONLink (List<JSONObject> ponLinkList, Map<String,JSONObject> idMapOlt) {
        for (JSONObject ponLink: ponLinkList) {
            if (ObjectUtil.isNotEmpty(idMapOlt.get(ponLink.getString("a_physic_device_id")))) {
                // 将olt
                swapAZ(ponLink);
            }
        }
    }

    void sortEtherLink (List<JSONObject> etherLinkList, Map<String,JSONObject> idMapUserTerminal) {
        for (JSONObject etherLink: etherLinkList) {
            if (ObjectUtil.isNotEmpty(idMapUserTerminal.get(etherLink.getString("z_physic_device_id")))) {
                // 将olt
                swapAZ(etherLink);
            }
        }
    }

    void sortChildrenLinks (List<JSONObject> parentLinkList,List<JSONObject> childrenLinkList, List<JSONObject> relationList ) {
        Map<String,JSONObject> idMapChildLink = listToMap(childrenLinkList);
        Map<String,List<JSONObject>> parent2childrenMap =  relationList.stream().collect(Collectors.groupingBy(d->d.getString("source_id")));
        for (JSONObject parentLink: parentLinkList) {
            List<JSONObject> parent2children = parent2childrenMap.get(parentLink.getString("gid"));
            if (ObjectUtil.isEmpty(parent2children)) parent2children =new ArrayList<>();
            List<JSONObject> children = new ArrayList<>();
            for (JSONObject parent2child: parent2children) {
                String roadId= parent2child.getString("target_id");
                JSONObject road = idMapChildLink.get(roadId);
                if (ObjectUtil.isNotEmpty(road)) {
                    children.add(road);
                }
            }

            parentLink.put("children", children);


            String startId = parentLink.getString("a_physic_device_id");
            String endId = parentLink.getString("z_physic_device_id");

            children = sortLink(children, Arrays.asList(startId),endId);
            parentLink.put("children", children);

            // log.info("children");
        }
    }

    void sortMSTPChildrenLinks (List<JSONObject> parentLinkList,List<JSONObject> childrenLinkList, List<JSONObject> relationList,Map<String,JSONObject> idMapDevice,Map<String,String> terminalMapAccess) {
        Map<String,JSONObject> idMapChildLink = listToMap(childrenLinkList);
        Map<String,List<JSONObject>> parent2childrenMap =  relationList.stream().collect(Collectors.groupingBy(d->d.getString("source_id")));
        for (JSONObject parentLink: parentLinkList) {
            List<JSONObject> parent2children = parent2childrenMap.get(parentLink.getString("gid"));
            if (ObjectUtil.isEmpty(parent2children)) parent2children =new ArrayList<>();
            List<JSONObject> children = new ArrayList<>();
            for (JSONObject parent2child: parent2children) {
                String roadId= parent2child.getString("target_id");
                JSONObject road = idMapChildLink.get(roadId);
                if (ObjectUtil.isNotEmpty(road)) {
                    children.add(road);
                }
            }

            parentLink.put("children", children);


            String startId = parentLink.getString("a_physic_device_id");
            String endId = parentLink.getString("z_physic_device_id");

            String startAccessId= terminalMapAccess.get(startId);

            if (ObjectUtil.isEmpty(startAccessId)) {
                startAccessId = startId;
            }

            String endAccessId= terminalMapAccess.get(endId);

            children = sortLink(children, Arrays.asList(startAccessId,endAccessId),"");
            parentLink.put("children", children);

            // log.info("children");
        }
    }

    List<JSONObject> sortLink(List<JSONObject> links, List<String> startIds, String endId) {

        List<String> currentIds = startIds;
        List<JSONObject> linksCopy ;
        int layer = 0;

        if (ObjectUtil.isNotEmpty(links) && links.size()>0) {
            linksCopy  = new ArrayList<>(links);

        }else{
            return links;
        }


        List<JSONObject> children = new ArrayList<>();
        while (true) {
            Set<String> nextIds= new HashSet<>();
            Set<String> firstLinkIds = new HashSet<>();
            List<JSONObject> firstLinks = getFirstLinks(linksCopy, currentIds, endId,layer);
            if (ObjectUtil.isEmpty(firstLinks)) break;
            for (JSONObject firstLink:firstLinks) {
                // String aPhysicDeviceId = firstLink.getString("a_physic_device_id");
                String zPhysicDeviceId = firstLink.getString("z_physic_device_id");
                nextIds.add(zPhysicDeviceId);
                firstLinkIds.add(firstLink.getString("id"));
            }
            children.addAll(firstLinks);
            /*
                从链路列表中剔除起始链路
            */
            linksCopy.removeIf(fl->firstLinkIds.contains(fl.getString("id")));
            currentIds = new ArrayList<>(nextIds);
        }
        return children;

    }

    List<JSONObject> getFirstLinks (List<JSONObject> links, List<String> currentIds, String endId, int layer) {

        return commonCircuitService.computeFirstLinks(links, currentIds, endId, TopoMode.LINK,layer);

    }







    void swapAZ(JSONObject  l ) {
        String aPhysicDeviceId = l.getString("a_physic_device_id");
        String aPortCode = l.getString("a_port_code");
        String aName = l.getString("a_name");
        String aPortId = l.getString("a_port_id");
        String zPhysicDeviceId = l.getString("z_physic_device_id");
        String zPortCode = l.getString("z_port_code");
        String zName = l.getString("z_name");
        String zPortId = l.getString("z_port_id");
        l.put("a_physic_device_id", zPhysicDeviceId);
        l.put("a_port_code", zPortCode);
        l.put("a_name" , zName);
        l.put("a_port_id", zPortId);
        l.put("z_physic_device_id", aPhysicDeviceId);
        l.put("z_port_code", aPortCode);
        l.put("z_name" , aName);
        l.put("z_port_id", aPortId);
    }

    public JSONObject queryLOID2CFS(JSONObject request) {
        String areaCode = request.getString("areaCode");
        List<String> loids = JSONObjectUtil.jsonStringArrayToList(request.getJSONArray("loids"));
        if (ObjectUtil.isEmpty(loids)) {
            return new JSONObject();
        }
        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_loid_2_cfs").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("access_device","loids",loids)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);

        graphService.buildTree(data,"接入设备","cfs","cfs");
        List<JSONObject> proInfoList = graphService.filterNodeByLabel(data,"接入设备");
        JSONObject result = new JSONObject();




        result.put("pro_info_list", proInfoList);
        return result;

    }




}
