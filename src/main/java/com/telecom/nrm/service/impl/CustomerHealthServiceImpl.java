package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.telecom.nrm.dao.CustomerDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.CustomerHealthService;
import com.telecom.nrm.service.OptViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.telecom.nrm.service.impl.OptViewServiceImpl.threadLocal;

@Service
@Slf4j
public class CustomerHealthServiceImpl implements CustomerHealthService {

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private OptViewService optViewService;

    @Override
    public JSONObject computeFiberOptSecurity(JSONObject jsonObject) {
        //光缆安全率=符合双路由的光缆数量/总量*0.5+光缆空闲率平均值*0.5
        JSONObject data = new JSONObject();
        //查询客户光缆数据
        List<JSONObject> cablesList =  customerDao.getCustomerCablesList(jsonObject , NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        cablesList = cablesList == null ? new ArrayList<>() : cablesList;
        //查询查询客户光缆空闲数据
        List<JSONObject> customerCablesFree =  customerDao.getCustomerCablesFree(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));;
        customerCablesFree = customerCablesFree == null ? new ArrayList<>() : customerCablesFree;
        Integer doubleRouteCables = computeDoubleRouteCables(cablesList,jsonObject.getString("ds"));
        BigDecimal part1=BigDecimal.ZERO;
        if (!cablesList.isEmpty()) {
            part1 = new BigDecimal(doubleRouteCables).divide(new BigDecimal(cablesList.size()), 2, BigDecimal.ROUND_HALF_UP);
        }
        //计算光缆空闲率平均值
        BigDecimal part2 = BigDecimal.ZERO;
        if (!customerCablesFree.isEmpty()) {
            //unused_capacity为未使用
            // capacity为总容量
            BigDecimal sum = customerCablesFree.stream().map(e -> {
                BigDecimal unusedCapacity = e.getBigDecimal("unused_capacity");
                BigDecimal capacity = e.getBigDecimal("capacity");
                if (capacity.compareTo(BigDecimal.ZERO) == 0) {
                    return BigDecimal.ZERO;
                }
                return unusedCapacity.divide(capacity, 2, BigDecimal.ROUND_HALF_UP);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            part2 = sum.divide(new BigDecimal(customerCablesFree.size()), 2, BigDecimal.ROUND_HALF_UP);
        }
        //计算光缆安全率
        BigDecimal fiberOptSecurity = part1.multiply(new BigDecimal("0.8")).add(part2.multiply(new BigDecimal("0.2")));
        data.put("fiberOptSecurity", fiberOptSecurity);
        return data;
    }

    @Override
    public JSONObject computeDeviceResourceAbility(JSONObject jsonObject) {
        JSONObject data = new JSONObject();
        //查询查询客户光缆空闲数据
        List<JSONObject> customerCablesFree =  customerDao.getCustomerCablesFree(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));;
        customerCablesFree = customerCablesFree == null ? new ArrayList<>() : customerCablesFree;
        //计算光缆空闲率平均值
        BigDecimal part2 = BigDecimal.ZERO;
        if (!customerCablesFree.isEmpty()) {
            //unused_capacity为未使用
            // capacity为总容量
            BigDecimal sum = customerCablesFree.stream().map(e -> {
                BigDecimal unusedCapacity = e.getBigDecimal("unused_capacity");
                BigDecimal capacity = e.getBigDecimal("capacity");
                if (capacity.compareTo(BigDecimal.ZERO) == 0) {
                    return BigDecimal.ZERO;
                }
                return unusedCapacity.divide(capacity, 2, BigDecimal.ROUND_HALF_UP);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            part2 = sum.divide(new BigDecimal(customerCablesFree.size()*2), 2, BigDecimal.ROUND_HALF_UP);
        }
        List<JSONObject> objectList = customerDao.getResourceDevices(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        objectList = objectList == null ? new ArrayList<>() : objectList;
        /**
         * {
         *     "trmnl_spec_name": "波分设备",
         *     "trmnl_lgc_eqp_id": "321250000000000547955944",
         *     "trmnl_name": "14302-南京建邺区江东中路399号邮储银行江苏分行-1800V-03-独享-CPE",
         *     "unused_capacity": 7520,
         *     "trmnl_code": "14302-南京建邺区江东中路399号邮储银行江苏分行-1800V-03-独享-CPE",
         *     "capacity": 17480
         * }
         */
        //计算设备资源能力
        BigDecimal part1 = BigDecimal.ZERO;
        if (!objectList.isEmpty()) {
            BigDecimal sum = objectList.stream().map(e -> {
                BigDecimal unusedCapacity = e.getBigDecimal("unused_capacity");
                BigDecimal capacity = e.getBigDecimal("capacity");
                if (capacity.compareTo(BigDecimal.ZERO) == 0) {
                    return BigDecimal.ZERO;
                }
                return unusedCapacity.divide(capacity, 2, BigDecimal.ROUND_HALF_UP);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            part1 = sum.divide(new BigDecimal(objectList.size()*2), 2, BigDecimal.ROUND_HALF_UP);
        }
        //资源能力=光缆空闲率平均值+设备空闲率平均值
        BigDecimal deviceResourceAbility = part1.add(part2);
        data.put("deviceResourceAbility", deviceResourceAbility);
        return data;
    }

    @Override
    public JSONObject computeBusinessResourceAbility(JSONObject jsonObject) {
        JSONObject data = new JSONObject();
        Integer cablesCount = 0;
        List<JSONObject> pageResponse2 =  customerDao.queryCustomerDevice2(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if (pageResponse2 != null) {
            cablesCount = cablesCount+pageResponse2.size();
        }
        //Integer cablesCount =  customerDao.getCustomerCablesCount(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        Integer doubleRouteCount =  customerDao.getCustomerDoubleRouteCount(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if (cablesCount == 0) {
            data.put("businessResourceAbility", 0);
            return data;
        }
        //业务资源能力=符合双路由的光缆数量/总量
        BigDecimal businessResourceAbility = new BigDecimal(doubleRouteCount).divide(new BigDecimal(cablesCount), 2, BigDecimal.ROUND_HALF_UP);
        data.put("businessResourceAbility", businessResourceAbility);
        return data;
    }

    /**
     * 根据光缆列表计算双路由得光缆
     */
    private Integer computeDoubleRouteCables(List<JSONObject> cablesList,String ds) {
        int total =0;
        try{
            threadLocal.set(new HashMap<>());
            //遍历光缆列表，两两对比
            for (int i = 0; i < cablesList.size(); i++) {
                JSONObject cable1 = cablesList.get(i);
                log.info("cable1:{}",cable1);
                for (int j = i + 1; j < cablesList.size(); j++) {
                    JSONObject cable2 = cablesList.get(j);
                    log.info("cable2:{}",cable2);
                    List<String> plan1 = Lists.newArrayList(cable1.getString("cable_code"));
                    List<String> plan2 = Lists.newArrayList(cable2.getString("cable_code"));
                    //判断两条光缆是否符合双路由
                    JSONObject result = optViewService.checkDoubleRouteV2(plan1, plan2, ds);
                    log.info("p1:{},p2:{}result:{}",cable1.getString("cable_code"),cable2.getString("cable_code"),result);
                    if (result.getBoolean("isDoubleRoute")) {
                        total++;
                        break;
                    }
                }
            }
        }finally {
            threadLocal.remove();
        }
        return total;
    }
}
