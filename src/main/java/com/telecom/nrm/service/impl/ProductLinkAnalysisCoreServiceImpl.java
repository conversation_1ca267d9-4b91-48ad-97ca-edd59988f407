package com.telecom.nrm.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.telecom.common.util.SnowFlakeGenerateIdUtils;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.dao.OTNDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.entity.PmProductLinkAnalysis;
import com.telecom.nrm.entity.PmProductLinkAnalysisRule;
import com.telecom.nrm.service.ProductLinkAnalysisCoreService;
import com.telecom.nrm.utils.HttpRequestsUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.cxf.common.util.StringUtils;
import org.elasticsearch.action.admin.indices.analyze.AnalyzeRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProductLinkAnalysisCoreServiceImpl implements ProductLinkAnalysisCoreService {

    @Resource
    private CircuitAnalysisDao circuitAnalysisDao;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    OTNDao otnDao; 



    /**
     * 链路稽核
     * 校验失败抛出异常， 成功无异常
     * @param linkAnalysis
     */
    public void analysis(PmProductLinkAnalysis linkAnalysis) {
        try {
            if (linkAnalysis == null) {
                throw new RuntimeException("稽核信息不存在");
            }
            String linkType = linkAnalysis.getProductType();
            if (StringUtils.isEmpty(linkType)) {
                throw new RuntimeException("无法确认产品类型");
            }

            if (linkAnalysis.getLinkId() == null) {
                throw new RuntimeException("无法确认顶层链路Id");
            }

            ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(String.valueOf(linkAnalysis.getRegionId()));
            // 添加检查接入号时候还存在，如果不存在则认为已经拆机,更新信息
            JSONObject param = new JSONObject();
            param.put("access_code", linkAnalysis.getAccessCode());
            PageResponse<PmProductLinkAnalysis> result =
                    this.circuitAnalysisDao.selectAnalysisInfoByAccessCode(param, 1, 1, shardingEnum.getBcShardingCode());
            if (CollectionUtils.isEmpty(result.getData()))
            {
                linkAnalysis.setProductState("拆机");
                linkAnalysis.setMsg("拆机");
                // 拆机
            } else {
                linkAnalysis.setACityId(result.getData().get(0).getACityId());
                linkAnalysis.setZCityId(result.getData().get(0).getZCityId());
                linkAnalysis.setAProvinceId(result.getData().get(0).getAProvinceId());
                linkAnalysis.setZProvinceId(result.getData().get(0).getZProvinceId());
                linkAnalysis.setARegionId(result.getData().get(0).getARegionId());
                linkAnalysis.setZRegionId(result.getData().get(0).getZRegionId());
                linkAnalysis.setProductState("正常");
                linkAnalysis.setLinkId(result.getData().get(0).getLinkId());
                linkAnalysis.setLinkCode(result.getData().get(0).getLinkCode());
                linkAnalysis.setUpdateDate(result.getData().get(0).getUpdateDate());
            }
            this.circuitAnalysisDao.refreshLinkAnalysisData(linkAnalysis,"ds_graph_js");
            if ("拆机".equalsIgnoreCase(linkAnalysis.getProductState())) {
                linkAnalysis.setCheckState("S");
                linkAnalysis.setProvinceAuditState("S");
                linkAnalysis.setMsg("拆机");
                this.circuitAnalysisDao.saveLinkAnalysisData(linkAnalysis,"ds_graph_js");
                return;
            }



            // 初始的链路
            this.initAnalysisLinks(linkAnalysis, shardingEnum);

            if ("数字电路".equals(linkType)) {
                this.analysisDigital(linkAnalysis,shardingEnum);
            } else if (linkType.indexOf("IP虚拟") >= 0) {
                this.analysisIpVirtual(linkAnalysis,shardingEnum);
            } else if (linkType.indexOf("云专网") >= 0) {
                this.analysisCloud(linkAnalysis,shardingEnum);
            } else {
                throw new RuntimeException("未实现的电路类型:"+linkType);
            }
            if (linkAnalysis.getCjtype().indexOf("IPRAN") >= 0) {

                this.initIpranTop(linkAnalysis,shardingEnum, false);
                if (linkAnalysis.getInitLinks().size() == 0) {
                    throw new RuntimeException("初始化IPRAN topo信息失败");
                }

            }
            // 先保存一次
            this.circuitAnalysisDao.saveLinkAnalysisData(linkAnalysis,"ds_graph_js");
            this.initAnalysisExtend(linkAnalysis,shardingEnum);
            this.buildData(linkAnalysis);
            try {
                this.provinceCheck(linkAnalysis); // 省内校验
                // 省内通过
                if ("S".equalsIgnoreCase(linkAnalysis.getProvinceAuditState()) && linkAnalysis.getProvinceSuccessDate() == null) {
                    circuitAnalysisDao.saveProvinceSuccessCheckDate(linkAnalysis,"ds_graph_js");
                }
                this.circuitAnalysisDao.saveLinkAnalysisData(linkAnalysis,"ds_graph_js"); // 先保存一次否则可能无法进行集团稽核
                this.jtCheck(linkAnalysis); // 集团校验
                if ("S".equalsIgnoreCase(linkAnalysis.getBasicCheck()) && linkAnalysis.getBasicSuccessDate() == null) {
                    circuitAnalysisDao.saveBasicSuccessCheckDate(linkAnalysis,"ds_graph_js");
                }
            } catch (Exception e) {
                e.printStackTrace();
                linkAnalysis.setCheckState("E");
                linkAnalysis.setMsg(e.getMessage());
            }
            this.circuitAnalysisDao.saveLinkAnalysisData(linkAnalysis,"ds_graph_js");
        } catch (Exception e) {
            e.printStackTrace();
            linkAnalysis.setMsg(e.getMessage());
            linkAnalysis.setCheckState("E");
            this.circuitAnalysisDao.saveLinkAnalysisData(linkAnalysis,"ds_graph_js");
            throw new RuntimeException(e.getMessage());
        }
        finally {
            this.createDefaultFakeTopo(linkAnalysis);
        }

    }

    private void createDefaultFakeTopo(PmProductLinkAnalysis linkAnalysis) {
        if ("拆机".equalsIgnoreCase(linkAnalysis.getProductState())) {
            return;
        }
        if ("S".equalsIgnoreCase(linkAnalysis.getBasicCheck())) {
            return; //稽核通过的无需构造
        }
        // 根据电路编码查询cjtye  如果有则不处理，否则构建
        // code_cjtype  code_results
        JSONObject cjtypeParam = new JSONObject();
        ShardingEnum shardingEnumByO3 = ShardingEnum.getShardingEnumByO3(String.valueOf(linkAnalysis.getRegionId()));
        cjtypeParam.put("code", linkAnalysis.getLinkCode());
        cjtypeParam.put("regionId",shardingEnumByO3.getPpmCode());
        String type = null;
        if (linkAnalysis.getProductType().indexOf("数字电路") >= 0) {
            type = "MSTP";
        }
        if (linkAnalysis.getProductType().indexOf("云专网") >= 0) {
            type = "云专网2.0";
        }
        if (linkAnalysis.getProductType().indexOf("虚拟") >= 0) {
            type = "IP虚拟专网";
        }
        cjtypeParam.put("type", type);

        List<JSONObject> cjtypes = this.circuitAnalysisDao.graph_query_cjtype_by_code_region(cjtypeParam,1,1,"ds_graph_js").getData();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(cjtypes)) {
            return ;
        }


        // 创建results
        List<JSONObject> exampleResults = this.circuitAnalysisDao.graph_query_results_by_type_region(cjtypeParam,1,1, "ds_graph_js").getData();
        if (CollectionUtils.isEmpty(exampleResults)) {
            return;
        }

        // 创建cjtype
        JSONObject cjType = new JSONObject();
        cjType.put("code", linkAnalysis.getLinkCode());
        cjType.put("cjtype",exampleResults.get(0).getString("cjtype"));
        cjType.put("cityCode",shardingEnumByO3.getPpmCode());
        cjType.put("type",type);
        cjType.put("accessCode",linkAnalysis.getAccessCode());
        this.circuitAnalysisDao.saveCjType(cjType , NRMConstants.SHARDING_GRAPH_DB);





        JSONObject codeResult = new JSONObject();
        codeResult.put("code", linkAnalysis.getLinkCode());
        codeResult.put("regionId",shardingEnumByO3.getPpmCode());
        codeResult.put("results",exampleResults.get(0).getString("results"));
        codeResult.put("oldCode",exampleResults.get(0).getString("code"));
        this.circuitAnalysisDao.addCodeResults(codeResult , NRMConstants.SHARDING_GRAPH_DB);

        return ;


    }

    @Override
    public void resetAuditState() {
        circuitAnalysisDao.reset_audit_check_state(new JSONObject(),"ds_graph_js");
    }

    private void provinceCheck(PmProductLinkAnalysis linkAnalysis) {
        linkAnalysis.setProvinceAuditState("S");
        //针对数字电路
        this.breakpointCheck(linkAnalysis);
        // MSTP检查
        JSONObject result = JSONObject.parseObject(linkAnalysis.getData());
        JSONArray links = result.getJSONObject("data").getJSONArray("links");
        if ("光纤接入CN2".equalsIgnoreCase(linkAnalysis.getCjtype()))  {
            for (int i = 0; i < linkAnalysis.getInitLinks().size(); i++) {
                if (linkAnalysis.getInitLinks().getJSONObject(i).getString("spec_id").equalsIgnoreCase("1131200002")) {
                    // 光链路路由
                    JSONObject linkOptRoad = linkAnalysis.getInitLinks().getJSONObject(i);
                    // 找到upper_link_id是它的
                    JSONArray routes = new JSONArray();
                    for (int j = 0; j < linkAnalysis.getInitLinks().size();j++) {
                        if (linkOptRoad.getString("id").equalsIgnoreCase(
                                linkAnalysis.getInitLinks().getJSONObject(j).getString("upper_link_id")
                        )) {
                            routes.add(linkAnalysis.getInitLinks().getJSONObject(j));
                        }
                    }
                    if (!this.checkOptroad(routes)) {
                        linkAnalysis.setProvinceAuditState("E");
                        throw new RuntimeException("光路路由异常,存在分叉情况,光路编码:" +linkOptRoad.getString("code"));
                    }
                }
            }
        }
        // 是否硬跳
        this.mstpJump(linkAnalysis);
        if (!this.checkWithRule(linkAnalysis)) {
            linkAnalysis.setProvinceAuditState("E");
            throw new RuntimeException("路由异常,未找到匹配的组网模型,当前组网模型:"+linkAnalysis.getNetText());
        }

    }

    private boolean checkWithRule(PmProductLinkAnalysis linkAnalysis) {
        // 根据链路规则进行稽核是否存在某个组网方式下
        /***
         ** 根据产品类型和cjtype 如果找到配置，则进行模式匹配，否则默认通过
         **/
        List<PmProductLinkAnalysisRule> rules = new ArrayList<>();
        // 添加检查接入号时候还存在，如果不存在则认为已经拆机,更新信息
        JSONObject param = new JSONObject();
        param.put("cjtype", linkAnalysis.getCjtype());
        param.put("productType", linkAnalysis.getProductType());
        PageResponse<PmProductLinkAnalysisRule> rulesData =
                this.circuitAnalysisDao.selectAnalysisRuleByProductCjtype(param, 100, 1, "ds_graph_js");
        rules = rulesData.getData();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(rules)) {
            return true;
        }
        String text = calculateLinkText(linkAnalysis);
        linkAnalysis.setNetText(text);
        for (int i = 0; i < rules.size(); i++) {
            if (!StringUtils.isEmpty(rules.get(i).getLongType())) {
                // 长途类型也要匹配
                if (!rules.get(i).getLongType().equalsIgnoreCase(linkAnalysis.getLongDistanceType())) {
                    continue;
                }
            }

            if (rules.get(i).getRouteMatch().equalsIgnoreCase(text)) {

                return true;
            }
            if (rules.get(i).getRouteMatch().equalsIgnoreCase(reverseRoute(text))) {
                return true;
            }

        }
        return false;
    }

    private String reverseRoute(String text) {
        String[] split = text.split("-");
        StringBuffer res = new StringBuffer();
        for (int i = split.length - 1; i >=0 ; i--) {
            res.append(split[i]);
            if (i > 0) {
                res.append("-");
            }
        }
        return res.toString();
    }

    /**
     * 计算链路的组网文本
     * @param linkAnalysis
     * @return
     */
    private String calculateLinkText(PmProductLinkAnalysis linkAnalysis) {
        StringBuffer netText = new StringBuffer();
        List<String> linkIds = new ArrayList<>() ; // 被遍历过的
        List<JSONObject> routes = new ArrayList<>();
        Set<String> routeTexts = new HashSet<>();// 格式：A设备ID-链路类型-Z设备ID，  A<Z
        for (int i = 0; i < linkAnalysis.getInitLinks().size(); i++) {
            JSONObject _link = linkAnalysis.getInitLinks().getJSONObject(i);
            linkIds.add(_link.getString("id"));
            String upperLinkId = _link.getString("upper_link_id");
            if (String.valueOf(linkAnalysis.getLinkId()).equalsIgnoreCase(upperLinkId)) {
                routes.add(_link);
            }
        }
        // 生成路由文本，解决掉双路由重复问题
        for (int i = 0; i < routes.size(); i++) {
            JSONObject route = routes.get(i);
            BigDecimal aDeviceId  = route.getBigDecimal("a_device_id");
            BigDecimal zDeviceId  = route.getBigDecimal("z_device_id");
            String spec  = route.getString("spec");
            if (aDeviceId.compareTo(zDeviceId) < 0) {
                routeTexts.add(String.valueOf(aDeviceId) + "-" + spec + "-" + String.valueOf(zDeviceId));
            } else {
                routeTexts.add(String.valueOf(zDeviceId) + "-" + spec + "-" + String.valueOf(aDeviceId));
            }
        }
        /**
         * 1、对路由进行排序
         */
        // 检查出现一次的设备
        List<String> oneceDevices = new ArrayList<>();
        for (int i = 0; i < routes.size(); i++) {
            JSONObject route = routes.get(i);
            String deviceId  = route.getString("a_device_id");
            boolean once = true;
            for (int j = 0; j< routes.size(); j++) {
                if ( i == j) {
                    continue;
                }
                if (routes.get(j).getString("a_device_id").equalsIgnoreCase(deviceId) ||
                        routes.get(j).getString("z_device_id").equalsIgnoreCase(deviceId)
                ) {
                    once = false;
                    break;
                }
            }
            if (once) {
                oneceDevices.add(deviceId);
            }
        }
        for (int i = 0; i < routes.size(); i++) {
            JSONObject route = routes.get(i);
            String deviceId  = route.getString("z_device_id");
            boolean once = true;
            for (int j = 0; j< routes.size(); j++) {
                if ( i == j) {
                    continue;
                }
                if (routes.get(j).getString("a_device_id").equalsIgnoreCase(deviceId) ||
                        routes.get(j).getString("z_device_id").equalsIgnoreCase(deviceId)
                ) {
                    once = false;
                    break;
                }
            }
            if (once) {
                oneceDevices.add(deviceId);
            }
        }
        String startDeviceId = oneceDevices.get(0);  // 以该节点进行排序
        List<String> rList = routeTexts.stream().collect(Collectors.toList());
        List<String> sortList = new ArrayList<>();// 排序后的结果
        while (rList.size() >0) {
            boolean exist = false;
            for (int i = 0; i < rList.size(); i++) {
                String rt = rList.get(i);
                if (rt.indexOf(startDeviceId) >= 0) {
                    exist = true;
                    String[] split = rt.split("-");
                    rList.remove(rt);

                    if (split[2].equalsIgnoreCase(split[0])) {
                        break;
                    }

                    if (rt.startsWith(startDeviceId)) {
                        sortList.add(rt);
                        startDeviceId = split[2];
                    } else {
                        sortList.add(split[2] + "-" + split[1] + "-" + split[0]);
                        startDeviceId = split[0];
                    }
                    break;
                }
            }
            if (!exist) {
                break; // 出现异常，无法找到
            }
        }
        Map<String,String> keyObject = new HashMap<>(); // 用于设备类型获取
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            JSONObject _device = linkAnalysis.getInitDevices().getJSONObject(i);
            keyObject.put(_device.getString("id"), _device.getString("spec"));
        }
        // 排序后生成文本
        for (int i = 0; i < sortList.size(); i++) {
            String text = sortList.get(i);
            String[] split = text.split("-");
            String aSpec = MapUtils.getString(keyObject, split[0],"");
            String zSpec = MapUtils.getString(keyObject, split[2],"");
            if (i == 0) {
                //  起始行情况。终端-MSAP 终端-光电，
                boolean appendStart = false;
                if (!StringUtils.isEmpty(aSpec) && aSpec.indexOf("终端") <0 ) {
                    this.appendRouteText(netText, aSpec);
                    appendStart = true;
                }
                if (!StringUtils.isEmpty(zSpec) && zSpec.indexOf("终端") <0 ) {
                    if (appendStart) {
                        this.appendRouteText(netText, split[1]);
                    }
                    this.appendRouteText(netText, zSpec);
                }
            } else if (i == sortList.size() - 1) {
                // 最后要看是不是终端考虑要不要末端
                if (!StringUtils.isEmpty(zSpec) && zSpec.indexOf("终端") <0 ) {
                    this.appendRouteText(netText, split[1]);
                    this.appendRouteText(netText, zSpec);
                }
            } else {
                // 中间的就直接拼接
                if (!StringUtils.isEmpty(zSpec) && zSpec.indexOf("终端") <0 ) {
                    this.appendRouteText(netText, split[1]);
                    this.appendRouteText(netText, zSpec);
                } else if (!"硬跳".equalsIgnoreCase(split[1]) && !"软跳".equalsIgnoreCase(split[1])) {
                    this.appendRouteText(netText, split[1]);
                }

            }

        }



        //
        return netText.toString();
    }

    private void appendRouteText(StringBuffer sb, String text) {
        if (StringUtils.isEmpty(sb.toString())) {
            sb.append(text);
        } else  if (sb.toString().endsWith("-")) {
            sb.append(text);
        } else {
            sb.append("-").append(text);
        }
    }


    /**
     * 是否存在两个MSTP不是同机房，但是硬跳的
     * @param linkAnalysis
     * @return
     */
    private void mstpJump(PmProductLinkAnalysis linkAnalysis) {
        // 排除IPRAN 可能MSAP作为U
        if (linkAnalysis.getCjtype().indexOf("IPRAN") >= 0 ) {
            return;
        }
        Map<String,JSONObject> keyObject = new HashMap<>(); // 用于对象映射记录
        int msapCount = 0; // 记录MSAP数量
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            JSONObject _device = linkAnalysis.getInitDevices().getJSONObject(i);
            keyObject.put(_device.getString("id"), _device);
            if ("1022200002".equalsIgnoreCase(_device.getString("spec_id"))) {
                msapCount++;
            }
        }
//        if (linkAnalysis.getCjtype().equalsIgnoreCase("MSTP接入")) {
//            if (msapCount <= 1) {
//                linkAnalysis.setProvinceAuditState("E");
//                throw new RuntimeException("判断为MSTP接入,但是路由中没有或者只有一个MSAP设备,路由不完整");
//            }
//        }

        for (int i = 0; i < linkAnalysis.getInitLinks().size(); i++) {
            JSONObject link = linkAnalysis.getInitLinks().getJSONObject(i);
            String aDeviceId = link.getString("a_device_id");
            String zDeviceId = link.getString("z_device_id");
            if ("1131100001".equalsIgnoreCase(link.getString("org_spec_id"))) {
                //校验MSAP 和 SDH
                if (keyObject.containsKey(aDeviceId) && keyObject.containsKey(zDeviceId)) {
                    JSONObject aDevice = (JSONObject)keyObject.get(aDeviceId);
                    JSONObject zDevice = (JSONObject)keyObject.get(zDeviceId);
                    String aFacilityId = aDevice.getString("facility_id");
                    String zFacilityId = zDevice.getString("facility_id");
                    String aSpecId = aDevice.getString("spec_id");
                    String zSpecId = zDevice.getString("spec_id");
                    if (aFacilityId != null && zFacilityId != null && !aFacilityId.equalsIgnoreCase(zFacilityId)) {
                        // 跨机房跳接 1022200002:MSAP
                        if (("1022200002".equalsIgnoreCase(aSpecId)) &&
                                ("1022200002".equalsIgnoreCase(zSpecId)) ) {
                            linkAnalysis.setProvinceAuditState("E");
                            throw new RuntimeException("存在跨机房硬跳,设备分别为:"+aDevice.getString("name") +" 和 " + zDevice.getString("name"));
                        }
                    }
                }
            }
        }
        //
    }

    /**
     * 检查光路是否分叉
     * @return
     */

    private boolean checkOptroad(JSONArray routes) {
        // 检查出现一次的设备次数
        Set<String>  oneceDevices = new HashSet<>();
        for (int i = 0; i < routes.size(); i++) {
            JSONObject route = routes.getJSONObject(i);
            String deviceId  = route.getString("a_device_id");
            boolean once = true;
            for (int j = 0; j< routes.size(); j++) {
                if ( i == j) {
                    continue;
                }
                if (routes.getJSONObject(j).getString("a_device_id").equalsIgnoreCase(deviceId) ||
                        routes.getJSONObject(j).getString("z_device_id").equalsIgnoreCase(deviceId)
                ) {
                    once = false;
                    break;
                }
            }
            if (once) {
                oneceDevices.add(deviceId);
            }
        }
        for (int i = 0; i < routes.size(); i++) {
            JSONObject route = routes.getJSONObject(i);
            String deviceId  = route.getString("z_device_id");
            boolean once = true;
            for (int j = 0; j< routes.size(); j++) {
                if ( i == j) {
                    continue;
                }
                if (routes.getJSONObject(j).getString("a_device_id").equalsIgnoreCase(deviceId) ||
                        routes.getJSONObject(j).getString("z_device_id").equalsIgnoreCase(deviceId)
                ) {
                    once = false;
                    break;
                }
            }
            if (once) {
                oneceDevices.add(deviceId);
            }
        }
        if (oneceDevices.size() == 2) {
            return true;
        }
        return false;
    }
    // 断点校验
    private void breakpointCheck(PmProductLinkAnalysis linkAnalysis) {
        JSONObject result = JSONObject.parseObject(linkAnalysis.getData());
        JSONArray links = result.getJSONObject("data").getJSONArray("links");
        JSONArray nodes = result.getJSONObject("data").getJSONArray("nodes");
        JSONArray relations = result.getJSONObject("data").getJSONArray("relations");
        JSONArray topLinks = new JSONArray();
        JSONArray jumpLinks = new JSONArray();
        JSONArray terminals = new JSONArray();
        JSONArray topRelations= new JSONArray(); // 以太网下面那层是和有断点
        List<String> lowerLinkIds= new ArrayList<>(); // 以太网下面那层是和有断点
        JSONObject topLink = null;
        for (int i1 = 0; i1 < links.size(); i1++) {
            if (links.getJSONObject(i1).getString("gid").equals(String.valueOf(linkAnalysis.getLinkId()))) {
                topLink = links.getJSONObject(i1);
            }
            if (links.getJSONObject(i1).getString("linkType").equals("软跳")) {
                jumpLinks.add(links.getJSONObject(i1));
            }
            if (links.getJSONObject(i1).getString("linkType").equals("硬跳")) {
                jumpLinks.add(links.getJSONObject(i1));
            }
        }
        // 判断是否有直连
        for (int i = 0; i < jumpLinks.size(); i++) {
            JSONObject jump = jumpLinks.getJSONObject(i);
            if (jump.getString("a_device_id").equalsIgnoreCase(topLink.getString("a_device_id")) &&
                    jump.getString("z_device_id").equalsIgnoreCase(topLink.getString("z_device_id"))) {
                if (!"IPRAN接入".equalsIgnoreCase(linkAnalysis.getCjtype())) {
                    linkAnalysis.setProvinceAuditState("E");
                    throw new RuntimeException("路由存在直连情况");
                }


            }
            if (jump.getString("a_device_id").equalsIgnoreCase(topLink.getString("z_device_id")) &&
                    jump.getString("z_device_id").equalsIgnoreCase(topLink.getString("a_device_id"))) {
                linkAnalysis.setProvinceAuditState("E");
                if (!"IPRAN接入".equalsIgnoreCase(linkAnalysis.getCjtype())) {
                    linkAnalysis.setProvinceAuditState("E");
                    throw new RuntimeException("路由存在直连情况");
                }
            }
        }
        // 检查断点
        for (int i1 = 0; i1 < relations.size(); i1++) {
            if (relations.getJSONObject(i1).getString("z_entity_id").equals(topLink.getString("gid")) &&
                    relations.getJSONObject(i1).getString("a_nodeLabels").indexOf("链路") >=0 &&
                    relations.getJSONObject(i1).getString("relationtype").equals("承载")) {
                lowerLinkIds.add(relations.getJSONObject(i1).getString("a_entity_id"));
            }
        }
        for (int i1 = 0; i1 < links.size(); i1++) {
            if (lowerLinkIds.contains(links.getJSONObject(i1).getString("gid"))) {
                topRelations.add(links.getJSONObject(i1));
            }
        }
        // 确认是否存在端点,检查设备出现一次数量
        Set<String>  oneceDevices = new HashSet<>();
        for (int i1 = 0; i1 < topRelations.size(); i1++) {
            String aDeviceId = topRelations.getJSONObject(i1).getString("a_device_id");
            boolean flag = false;
            for (int i2 = 0; i2 < topRelations.size(); i2++) {
                if (i2 == i1) {
                    continue;
                }
                if ( topRelations.getJSONObject(i2).getString("a_device_id").equals(aDeviceId)) {
                    flag = true;
                }
                if ( topRelations.getJSONObject(i2).getString("z_device_id").equals(aDeviceId)) {
                    flag = true;
                }

            }
            if (!flag) {
                oneceDevices.add(aDeviceId);
            }
        }
        for (int i1 = 0; i1 < topRelations.size(); i1++) {
            String aDeviceId = topRelations.getJSONObject(i1).getString("z_device_id");
            boolean flag = false;
            for (int i2 = 0; i2 < topRelations.size(); i2++) {
                if (i2 == i1) {
                    continue;
                }
                if ( topRelations.getJSONObject(i2).getString("a_device_id").equals(aDeviceId)) {
                    flag = true;
                }
                if ( topRelations.getJSONObject(i2).getString("z_device_id").equals(aDeviceId)) {
                    flag = true;
                }

            }
            if (!flag) {
                oneceDevices.add(aDeviceId);
            }
        }
        if (oneceDevices.size() > 2) {
            linkAnalysis.setProvinceAuditState("E");
            throw new RuntimeException("路由存在断点位置");
        }

    }

    /**
     * 集团基础和增强稽核
     * @param linkAnalysis
     */
    private void jtCheck(PmProductLinkAnalysis linkAnalysis) {
        String circuitType = linkAnalysis.getProductType();
        if ("数字电路".equals(circuitType)) {
            circuitType = "MSTP";
        }
        if ("IP虚拟网".equals(circuitType)) {
            circuitType = "IP虚拟专网";
        }
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(String.valueOf(linkAnalysis.getRegionId()));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", "712c84c87a5fe5a1fca04f2c9f81a991");
        headers.add("X-APP-KEY", "9e09b9e30ba77508a7ad08f1c5db8550");
        JSONObject param = new JSONObject();
        param.put("circuitCode", linkAnalysis.getLinkCode() + "_TEST"); // 调用集团，集团 又会调用省内接口，但是省内接口有假数据，通过_TEST在省内接口控制 ：IPRANJSSERVICEIMPL.java
        param.put("accessCode", linkAnalysis.getAccessCode());
        param.put("circuitType", circuitType);
        param.put("areaCode",shardingEnum.getPpmCode());
        param.put("isThroughBackBone", "0");
        param.put("azEnded", "0");

        // 创建HttpEntity
        HttpEntity<Object> request = new HttpEntity<>(param, headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    "http://************:20501/serviceAgent/rest/cnrdtreegraph/circuit/circuitValidate",
                    HttpMethod.POST,
                    request,
                    String.class
            );
            linkAnalysis.setBasicCheck("E");
            linkAnalysis.setEnhanceCheck("E");
            linkAnalysis.setCheckState("E");
            String body = responseEntity.getBody();
            JSONObject result = JSONObject.parseObject(body);
            String code = result.getString("code");
            if ("200".equals(code)) {
                // 基础校验
                if ("1".equals(result.getString("basicTreeFormation"))) {
                    linkAnalysis.setBasicCheck("S");
                    linkAnalysis.setCheckState("S");
                    linkAnalysis.setMsg("集团基础校验成功");

                } else {
                    linkAnalysis.setBasicCheck("E");
                    linkAnalysis.setCheckState("E");
                    linkAnalysis.setMsg("集团:"+ result.getString("basicErrorMsg"));
                }
                // 增强校验
                if ("1".equals(result.getString("enhanceTreeFormation"))) {
                    linkAnalysis.setEnhanceCheck("S");
                    linkAnalysis.setMsg("集团增强校验成功");
                } else {
                    linkAnalysis.setEnhanceCheck("E");
//                    linkAnalysis.setMsg(result.getString("enhanceErrorMsg"));
                }

//                if ("S".equals(linkAnalysis.getBasicCheck()) &&"S".equals(linkAnalysis.getEnhanceCheck())) {
//                    linkAnalysis.setCheckState("S");
//                    linkAnalysis.setMsg("稽核成功");
//                }
            } else {
                linkAnalysis.setBasicCheck("E");
                linkAnalysis.setCheckState("E");
                linkAnalysis.setMsg("集团：" + result.getString("message"));

            }
            linkAnalysis.setJtMsg(body);

        } catch (Exception e) {
            e.printStackTrace();
            linkAnalysis.setCheckState("E");
            throw new RuntimeException("调用集团稽核接口失败," + e.getMessage());
        }

    }

    /**
     * 生成报文
     * @param linkAnalysis
     */
    private void buildData(PmProductLinkAnalysis linkAnalysis) {
        JSONArray links = new JSONArray();
        JSONArray nodes = new JSONArray();
        JSONArray relations = new JSONArray();
        JSONObject data = new JSONObject();

        Map<String,JSONObject> keyObject = new HashMap<>(); // 用于对象映射记录
        /***********************************************LINKS**************************************************/
        for (int i = 0; i < linkAnalysis.getInitLinks().size(); i++) {
            JSONObject _link = linkAnalysis.getInitLinks().getJSONObject(i);
            _link.put("org_spec_id", _link.getString("spec_id")); // 原始的规格

            // MSTP
            if (_link.getString("spec_id").equals("1132292019")) {
                _link.put("spec_id", "1132200014");
                _link.put("spec", "MSTP/MSAP路由段");
            }
            if (_link.getString("spec_id").equals("1132200005")) {
                _link.put("spec_id", "1132200006");
                _link.put("spec", "SDH路由段/槽路");
            }

            if (_link.getString("spec_id").equals("1132100001")) {
                _link.put("spec_id", "1131100001");
                _link.put("spec", "硬跳");
            }
            JSONObject link = new JSONObject();
            link.put("gid", _link.getString("id"));
            link.put("code", _link.getString("code"));
            link.put("name", _link.getString("name"));
            link.put("z_device_id", _link.getString("z_device_id"));
            link.put("a_device_id", _link.getString("a_device_id"));
            link.put("z_port_id", _link.getString("z_port_id"));
            link.put("a_port_id", _link.getString("a_port_id"));
            link.put("spec_id", _link.getString("spec_id"));
            link.put("linkType", _link.getString("spec"));
            link.put("z_tml_id", _link.getString("z_tml_id"));
            link.put("a_tml_id", _link.getString("a_tml_id"));
            link.put("leaf_region_id", _link.getString("leaf_region_id"));
            link.put("using_state_id", _link.getString("using_state_id"));
            link.put("id", _link.getString("id"));



            JSONArray nodeLabels = new JSONArray();
            nodeLabels.add("链路");
            nodeLabels.add(_link.getString("spec"));
            link.put("nodeLabels",nodeLabels);

            keyObject.put(_link.getString("id"), link);
            links.add(link);
        }
        boolean isIpran = this.isIpran(linkAnalysis);
        /***********************************************NODES**************************************************/
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            JSONObject _device = linkAnalysis.getInitDevices().getJSONObject(i);
            JSONObject device = new JSONObject();
            device.put("id",_device.getString("id"));
            device.put("facility_id",_device.getString("facility_id"));
            device.put("name",_device.getString("name"));
            device.put("code",_device.getString("code"));
            device.put("notes",_device.getString("notes"));
            device.put("tml_id",_device.getString("tml_id"));
            device.put("manufactor_id",_device.getString("manufactor_id"));
            device.put("is_prov", "1");
            device.put("leaf_region_id",_device.getString("leaf_region_id"));
            device.put("latitude",_device.getString("latitude"));
            device.put("longitude",_device.getString("longitude"));
            device.put("model_id",_device.getString("model_id"));
            device.put("spec_id",_device.getString("spec_id"));
            device.put("single_capacity","100399");
            device.put("work_way_id",_device.getString("work_way_id"));
            if (!StringUtils.isEmpty(_device.getString("ip"))) {
                device.put("nm_ip",_device.getString("ip"));
            }

            JSONArray nodeLabels = new JSONArray();
            nodeLabels.add("设备");
            nodeLabels.add(_device.getString("spec"));
            nodeLabels.add(_device.getString("role_type") == null?"无":_device.getString("role_type"));
            // 添加IPRAN处理
            String roleId = _device.getString("network_role_id");
            if ("108240".equals(roleId)) {
                nodeLabels.add("U设备");
            }
            if ("102279".equals(roleId)) {
                nodeLabels.add("网ASBR");
            }
            if ("108243".equals(roleId)) {
                nodeLabels.add("A设备");
            }
            if ("108244".equals(roleId)) {
                nodeLabels.add("B设备");
            }

            device.put("nodeLabels",nodeLabels);

            keyObject.put(_device.getString("id"), device);

            nodes.add(device);


        }

        for (int i = 0; i < linkAnalysis.getInitIps().size(); i++) {
            JSONObject _ip = linkAnalysis.getInitIps().getJSONObject(i);
            JSONObject ip = new JSONObject();
            ip.put("id",_ip.getString("id"));
            ip.put("name",_ip.getString("name"));
            ip.put("code",_ip.getString("code"));
            ip.put("spec_id",_ip.getString("spec_id"));
            JSONArray nodeLabels = new JSONArray();
            nodeLabels.add("码号");
            nodeLabels.add("IP地址");
            nodeLabels.add("业务管理IP");
            nodeLabels.add("管理IP");
            ip.put("nodeLabels",nodeLabels);
            keyObject.put(_ip.getString("id"), ip);
            nodes.add(ip);
       }

        for (int i = 0; i < linkAnalysis.getInitPorts().size(); i++) {
            JSONObject _port = linkAnalysis.getInitPorts().getJSONObject(i);
            JSONObject port = new JSONObject();
            port.put("id",_port.getString("id"));
            port.put("nm_code",_port.getString("nm_code"));
            port.put("code",_port.getString("code"));
            port.put("name",_port.getString("name"));
            port.put("spec_id",_port.getString("spec_id"));
            port.put("using_state_id",_port.getString("using_state_id"));
            port.put("port_type_id",_port.getString("port_type_id"));
            port.put("photoelectric_type_id",_port.getString("photoelectric_type_id"));
            port.put("send_receive_type_id",_port.getString("send_receive_type_id"));
            port.put("max_deal_rate_id",_port.getString("max_deal_rate_id"));
            port.put("alias",_port.getString("alias"));
            port.put("device_id",_port.getString("device_id"));
            port.put("ware_id",_port.getString("ware_id"));
            port.put("up_down_id",_port.getString("up_down_id"));
            port.put("seq",_port.getString("seq"));
            port.put("real_config_rate_id",_port.getString("real_config_rate_id"));
            JSONArray nodeLabels = new JSONArray();
            nodeLabels.add("端口");
            nodeLabels.add(_port.getString("spec"));
            port.put("nodeLabels",nodeLabels);

            keyObject.put(_port.getString("id"), port);

            nodes.add(port);
        }

        // 机房
        for (int i = 0; i < linkAnalysis.getInitRooms().size(); i++) {
            JSONObject _room = linkAnalysis.getInitRooms().getJSONObject(i);
            JSONObject room = new JSONObject();
            room.put("id",_room.getString("id"));
            room.put("code",_room.getString("code"));
            room.put("name",_room.getString("name"));
            room.put("spec_id",_room.getString("spec_id"));
            room.put("tml_id",_room.getString("tml_id"));
            room.put("longitude",_room.getString("longitude"));
            room.put("latitude",_room.getString("latitude"));
            room.put("maintainer_tele","1");
            JSONArray nodeLabels = new JSONArray();
            nodeLabels.add("机房");
            nodeLabels.add(_room.getString("spec"));
            room.put("nodeLabels",nodeLabels);
            keyObject.put(_room.getString("id"), room);

            nodes.add(room);
        }

        // 局站
        for (int i = 0; i < linkAnalysis.getInitStations().size(); i++) {
            JSONObject _tml = linkAnalysis.getInitStations().getJSONObject(i);
            JSONObject tml = new JSONObject();
            tml.put("id",_tml.getString("id"));
            tml.put("code",_tml.getString("code"));
            tml.put("name",_tml.getString("name"));
            tml.put("spec_id",_tml.getString("spec_id"));
            tml.put("area_type_id",_tml.getString("area_type_id"));
            tml.put("leaf_region_id",_tml.getString("leaf_region_id"));
            tml.put("longitude",_tml.getString("longitude"));
            tml.put("latitude",_tml.getString("latitude"));
            tml.put("coordinate_x",_tml.getString("coordinate_x"));
            tml.put("coordinate_y",_tml.getString("coordinate_y"));

            JSONArray nodeLabels = new JSONArray();
            nodeLabels.add("局站");
            nodeLabels.add(_tml.getString("code"));
            tml.put("nodeLabels",nodeLabels);
            keyObject.put(_tml.getString("id"), tml);
            nodes.add(tml);
        }

        for (int i = 0; i < linkAnalysis.getInitAddresses().size(); i++) {
            JSONObject _address = linkAnalysis.getInitAddresses().getJSONObject(i);
            JSONObject address = new JSONObject();
            address.put("id",_address.getString("id"));
            address.put("code",_address.getString("code"));
            address.put("name",_address.getString("name"));
            address.put("spec_id",_address.getString("spec_id"));
            JSONArray nodeLabels = new JSONArray();
            nodeLabels.add("标准地址");
            address.put("nodeLabels",nodeLabels);
            keyObject.put(_address.getString("id"), address);
            nodes.add(address);

        }

        // cfs
        if (linkAnalysis.getCfs() != null && !StringUtils.isEmpty(linkAnalysis.getCfs().getString("id"))) {
            JSONObject cfs = new JSONObject();
            cfs.put("id",linkAnalysis.getCfs().getString("id"));
            cfs.put("code",linkAnalysis.getCfs().getString("code"));
            cfs.put("name",linkAnalysis.getCfs().getString("name"));
            cfs.put("spec_id",linkAnalysis.getCfs().getString("spec_id"));
            cfs.put("using_state_id",linkAnalysis.getCfs().getString("using_state_id"));
            cfs.put("access_mode_id","114131");
            cfs.put("area_id",linkAnalysis.getCfs().getString("area_id"));
            JSONArray nodeLabels = new JSONArray();
            nodeLabels.add("产品服务");
            nodeLabels.add("固定入网专线CFS");
            cfs.put("nodeLabels",nodeLabels);
            keyObject.put(linkAnalysis.getCfs().getString("id"), cfs);
            nodes.add(cfs);
        }



        /****************************************relation***************************************************/
        //  链路和备 端口的依赖关系
        for (int i = 0; i < linkAnalysis.getInitLinks().size(); i++) {
            JSONObject _link = linkAnalysis.getInitLinks().getJSONObject(i);
            if (  keyObject.get(_link.getString("a_device_id")) == null) {
                linkAnalysis.setProvinceAuditState("E");
                throw new RuntimeException("链路中A端设备ID:"+_link.getString("a_device_id")+"在设备表中不存在,链路ID:" + _link.getString("id") );
            }
            if (  keyObject.get(_link.getString("z_device_id")) == null) {
                linkAnalysis.setProvinceAuditState("E");
                throw new RuntimeException("链路中Z端设备ID:"+_link.getString("z_device_id")+"在设备表中不存在,链路ID:" + _link.getString("id") );
            }

            if (!isIpran) {
                if (  keyObject.get(_link.getString("a_port_id")) == null) {
                    linkAnalysis.setProvinceAuditState("E");
                    JSONObject tempDevice = keyObject.get(_link.getString("a_device_id"));
                    JSONObject otherDevice = keyObject.get(_link.getString("z_device_id"));
                    JSONObject otherPort = keyObject.get(_link.getString("z_port_id"));
                    throw new RuntimeException("路由中经过设备:"+tempDevice.getString("name") +",但是其端口在设备上查询不到," +
                            "找不到端口ID:"+_link.getString("a_port_id")+"。其对端设备是:" +otherDevice.getString("name")+",端口:"+otherPort.getString("code"));
                }
                if (  keyObject.get(_link.getString("z_port_id")) == null) {
                    linkAnalysis.setProvinceAuditState("E");
                    JSONObject tempDevice = keyObject.get(_link.getString("z_device_id"));
                    JSONObject otherDevice = keyObject.get(_link.getString("a_device_id"));
                    JSONObject otherPort = keyObject.get(_link.getString("a_port_id"));
                    throw new RuntimeException("路由中经过设备:"+tempDevice.getString("name") +",但是其端口在设备上查询不到," +
                            "找不到端口ID:"+_link.getString("z_port_id")+"。其对端设备是:" +otherDevice.getString("name")+",端口:"+otherPort.getString("code"));
                }
            }


            JSONObject aDeviceRelation = new JSONObject();
            aDeviceRelation.put("relationtype", "A端逻辑终结于");
            aDeviceRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
            aDeviceRelation.put("a_spec_id", keyObject.get(_link.getString("id")).getString("spec_id"));
            aDeviceRelation.put("a_entity_id", keyObject.get(_link.getString("id")).getString("id"));
            aDeviceRelation.put("a_nodeLabels", keyObject.get(_link.getString("id")).getJSONArray("nodeLabels"));
            aDeviceRelation.put("z_spec_id", keyObject.get(_link.getString("a_device_id")).getString("spec_id"));
            aDeviceRelation.put("z_entity_id", keyObject.get(_link.getString("a_device_id")).getString("id"));
            aDeviceRelation.put("z_nodeLabels", keyObject.get(_link.getString("a_device_id")).getJSONArray("nodeLabels"));
            relations.add(aDeviceRelation);

            JSONObject zDeviceRelation = new JSONObject();
            zDeviceRelation.put("relationtype", "Z端逻辑终结于");
            zDeviceRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
            zDeviceRelation.put("a_spec_id", keyObject.get(_link.getString("id")).getString("spec_id"));
            zDeviceRelation.put("a_entity_id", keyObject.get(_link.getString("id")).getString("id"));
            zDeviceRelation.put("a_nodeLabels", keyObject.get(_link.getString("id")).getJSONArray("nodeLabels"));

            zDeviceRelation.put("z_spec_id", keyObject.get(_link.getString("a_device_id")).getString("spec_id"));
            zDeviceRelation.put("z_entity_id", keyObject.get(_link.getString("a_device_id")).getString("id"));
            zDeviceRelation.put("z_nodeLabels", keyObject.get(_link.getString("a_device_id")).getJSONArray("nodeLabels"));
            relations.add(zDeviceRelation);


            //============================端口======================================================
            if (_link.getString("a_port_id") != null) {
                JSONObject aPortRelation = new JSONObject();
                aPortRelation.put("relationtype", "A端终结于");
                aPortRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
                aPortRelation.put("a_spec_id", keyObject.get(_link.getString("id")).getString("spec_id"));
                aPortRelation.put("a_entity_id", keyObject.get(_link.getString("id")).getString("id"));
                aPortRelation.put("a_nodeLabels", keyObject.get(_link.getString("id")).getJSONArray("nodeLabels"));

                aPortRelation.put("z_spec_id", keyObject.get(_link.getString("a_port_id")).getString("spec_id"));
                aPortRelation.put("z_entity_id", keyObject.get(_link.getString("a_port_id")).getString("id"));
                aPortRelation.put("z_nodeLabels", keyObject.get(_link.getString("a_port_id")).getJSONArray("nodeLabels"));
                relations.add(aPortRelation);
            }

            if (_link.getString("z_port_id") != null) {
                JSONObject zPortRelation = new JSONObject();
                zPortRelation.put("relationtype", "Z端终结于");
                zPortRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
                zPortRelation.put("a_spec_id", keyObject.get(_link.getString("id")).getString("spec_id"));
                zPortRelation.put("a_entity_id", keyObject.get(_link.getString("id")).getString("id"));
                zPortRelation.put("a_nodeLabels", keyObject.get(_link.getString("id")).getJSONArray("nodeLabels"));

                zPortRelation.put("z_spec_id", keyObject.get(_link.getString("z_port_id")).getString("spec_id"));
                zPortRelation.put("z_entity_id", keyObject.get(_link.getString("z_port_id")).getString("id"));
                zPortRelation.put("z_nodeLabels", keyObject.get(_link.getString("z_port_id")).getJSONArray("nodeLabels"));
                relations.add(zPortRelation);
            }



            // 链路承载关系
            String upperLinkId = _link.getString("upper_link_id");
            if (!StringUtils.isEmpty(upperLinkId) && !"0".equals(upperLinkId)) {
                JSONObject loadRelation = new JSONObject();
                loadRelation.put("relationtype", "承载");
                loadRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
                loadRelation.put("a_spec_id", keyObject.get(_link.getString("id")).getString("spec_id"));
                loadRelation.put("a_entity_id", keyObject.get(_link.getString("id")).getString("id"));
                loadRelation.put("a_nodeLabels", keyObject.get(_link.getString("id")).getJSONArray("nodeLabels"));
                loadRelation.put("z_spec_id", keyObject.get(upperLinkId).getString("spec_id"));
                loadRelation.put("z_entity_id", keyObject.get(upperLinkId).getString("id"));
                loadRelation.put("z_nodeLabels", keyObject.get(upperLinkId).getJSONArray("nodeLabels"));
                relations.add(loadRelation);
            }


        }

        // 设备端口的关系
        for (int i = 0; i < linkAnalysis.getInitPorts().size(); i++) {

            JSONObject _port = linkAnalysis.getInitPorts().getJSONObject(i);
            if (keyObject.get(_port.getString("device_id")) == null) {
                continue;
            }
            JSONObject devicePortRelation = new JSONObject();
            devicePortRelation.put("relationtype", "包含");
            devicePortRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
            devicePortRelation.put("a_spec_id", keyObject.get(_port.getString("device_id")).getString("spec_id"));
            devicePortRelation.put("a_entity_id", keyObject.get(_port.getString("device_id")).getString("id"));
            devicePortRelation.put("a_nodeLabels", keyObject.get(_port.getString("device_id")).getJSONArray("nodeLabels"));
            devicePortRelation.put("z_spec_id", keyObject.get(_port.getString("id")).getString("spec_id"));
            devicePortRelation.put("z_entity_id", keyObject.get(_port.getString("id")).getString("id"));
            devicePortRelation.put("z_nodeLabels", keyObject.get(_port.getString("id")).getJSONArray("nodeLabels"));
            relations.add(devicePortRelation);
        }


        // 设备和机房、机房和局站、局站和地址、链路和产品服务、产品服务和标准地址、设备和ip
        // 设备和机房局站的关系
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            JSONObject _device = linkAnalysis.getInitDevices().getJSONObject(i);
            if (StringUtils.isEmpty(_device.getString("facility_id"))) {
                continue;
            }
            JSONObject deviceRoomRelation = new JSONObject();
            deviceRoomRelation.put("relationtype", "容纳");
            deviceRoomRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
            deviceRoomRelation.put("a_spec_id", keyObject.get(_device.getString("facility_id")).getString("spec_id"));
            deviceRoomRelation.put("a_entity_id", keyObject.get(_device.getString("facility_id")).getString("id"));
            deviceRoomRelation.put("a_nodeLabels", keyObject.get(_device.getString("facility_id")).getJSONArray("nodeLabels"));
            deviceRoomRelation.put("z_spec_id", keyObject.get(_device.getString("id")).getString("spec_id"));
            deviceRoomRelation.put("z_entity_id", keyObject.get(_device.getString("id")).getString("id"));
            deviceRoomRelation.put("z_nodeLabels", keyObject.get(_device.getString("id")).getJSONArray("nodeLabels"));
            relations.add(deviceRoomRelation);
        }

        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            JSONObject _device = linkAnalysis.getInitDevices().getJSONObject(i);
            if (StringUtils.isEmpty(_device.getString("tml_id"))) {
                continue;
            }
            JSONObject deviceTmlRelation = new JSONObject();
            deviceTmlRelation.put("relationtype", "位于");
            deviceTmlRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
            deviceTmlRelation.put("a_spec_id", keyObject.get(_device.getString("id")).getString("spec_id"));
            deviceTmlRelation.put("a_entity_id", keyObject.get(_device.getString("id")).getString("id"));
            deviceTmlRelation.put("a_nodeLabels", keyObject.get(_device.getString("id")).getJSONArray("nodeLabels"));
            deviceTmlRelation.put("z_spec_id", keyObject.get(_device.getString("tml_id")).getString("spec_id"));
            deviceTmlRelation.put("z_entity_id", keyObject.get(_device.getString("tml_id")).getString("id"));
            deviceTmlRelation.put("z_nodeLabels", keyObject.get(_device.getString("tml_id")).getJSONArray("nodeLabels"));
            relations.add(deviceTmlRelation);
        }

        for (int i = 0; i < linkAnalysis.getInitIps().size(); i++) {
            JSONObject _ip = linkAnalysis.getInitIps().getJSONObject(i);
            JSONObject ipDeviceRelation = new JSONObject();
            ipDeviceRelation.put("relationtype", "绑定");
            ipDeviceRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
            ipDeviceRelation.put("a_spec_id", keyObject.get(_ip.getString("device_id")).getString("spec_id"));
            ipDeviceRelation.put("a_entity_id", keyObject.get(_ip.getString("device_id")).getString("id"));
            ipDeviceRelation.put("a_nodeLabels", keyObject.get(_ip.getString("device_id")).getJSONArray("nodeLabels"));
            ipDeviceRelation.put("z_spec_id", keyObject.get(_ip.getString("id")).getString("spec_id"));
            ipDeviceRelation.put("z_entity_id", keyObject.get(_ip.getString("id")).getString("id"));
            ipDeviceRelation.put("z_nodeLabels", keyObject.get(_ip.getString("id")).getJSONArray("nodeLabels"));
            relations.add(ipDeviceRelation);
        }

        // cfs 和地址
        for (int i = 0; i < linkAnalysis.getInitAddresses().size(); i++) {
            JSONObject _address = linkAnalysis.getInitAddresses().getJSONObject(i);
            JSONObject ipDeviceRelation = new JSONObject();
            ipDeviceRelation.put("relationtype", "位于");
            ipDeviceRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
            ipDeviceRelation.put("a_spec_id", keyObject.get(String.valueOf(linkAnalysis.getServiceId())).getString("spec_id"));
            ipDeviceRelation.put("a_entity_id", keyObject.get(String.valueOf(linkAnalysis.getServiceId())).getString("id"));
            ipDeviceRelation.put("a_nodeLabels", keyObject.get(String.valueOf(linkAnalysis.getServiceId())).getJSONArray("nodeLabels"));
            ipDeviceRelation.put("z_spec_id", keyObject.get(_address.getString("id")).getString("spec_id"));
            ipDeviceRelation.put("z_entity_id", keyObject.get(_address.getString("id")).getString("id"));
            ipDeviceRelation.put("z_nodeLabels", keyObject.get(_address.getString("id")).getJSONArray("nodeLabels"));
            relations.add(ipDeviceRelation);
        }



        // 电路和cfs
        JSONObject linkcfsRelation = new JSONObject();
        linkcfsRelation.put("relationtype", "使用");
        linkcfsRelation.put("gid", SnowFlakeGenerateIdUtils.generateNextId());
        linkcfsRelation.put("a_spec_id", keyObject.get(String.valueOf(linkAnalysis.getServiceId())).getString("spec_id"));
        linkcfsRelation.put("a_entity_id", keyObject.get(String.valueOf(linkAnalysis.getServiceId())).getString("id"));
        linkcfsRelation.put("a_nodeLabels", keyObject.get(String.valueOf(linkAnalysis.getServiceId())).getJSONArray("nodeLabels"));
        linkcfsRelation.put("z_spec_id", keyObject.get(String.valueOf(linkAnalysis.getLinkId())).getString("spec_id"));
        linkcfsRelation.put("z_entity_id", keyObject.get(String.valueOf(linkAnalysis.getLinkId())).getString("id"));
        linkcfsRelation.put("z_nodeLabels", keyObject.get(String.valueOf(linkAnalysis.getLinkId())).getJSONArray("nodeLabels"));
        relations.add(linkcfsRelation);



        data.put("links", links);
        data.put("nodes", nodes);
        data.put("relations", relations);
        JSONObject result = new JSONObject();
        result.put("data", data);
        result.put("code", "0");
        result.put("nextPage", true);
        result.put("pageSize", 100);
        result.put("pageNum", 0);
        SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        result.put("backTime", s.format(new Date()));
        result.put("message", "逻辑重构,按照新逻辑返回");
        cn.hutool.json.JSONObject huResult=new cn.hutool.json.JSONObject(result);
        linkAnalysis.setData(huResult.toJSONString(0));

    }

    private boolean isIpran(PmProductLinkAnalysis linkAnalysis) {
        if (linkAnalysis.getAccessCode().indexOf("IPRAN") >= 0 ) {
            return true;
        }

        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
           JSONObject device =  linkAnalysis.getInitDevices().getJSONObject(i);
           if ("108243".equals(device.getString("network_role_id")) ) {
               return true;
           }
           if ("108240".equals(device.getString("network_role_id")) ) {
               return true;
           }
           if ("108244".equals(device.getString("network_role_id")) ) {
               return true;
           }
        }
        return false;
    }

    private void analysisCloud(PmProductLinkAnalysis linkAnalysis,ShardingEnum shardingEnum) {
        if (this.isIpran(linkAnalysis)) {
            linkAnalysis.setCjtype("IPRAN接入CN2");
        }
        else if (this.hasMstp(linkAnalysis)){
            linkAnalysis.setCjtype("MSTP/MSAP接入CN2");
        } else {
            linkAnalysis.setCheckState("E");
            linkAnalysis.setCjtype("无法识别");
        }

    }

    private boolean hasMstp(PmProductLinkAnalysis linkAnalysis) {
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            if (linkAnalysis.getInitDevices().getJSONObject(i).getString("spec").equalsIgnoreCase("MSAP设备")) {
                return true;
            }
        }
        return false;
    }

    private boolean hasSDH(PmProductLinkAnalysis linkAnalysis) {
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            if (linkAnalysis.getInitDevices().getJSONObject(i).getString("spec").equalsIgnoreCase("SDH设备")) {
                return true;
            }
        }
        return false;
    }

    private boolean hasOTN(PmProductLinkAnalysis linkAnalysis) {
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            if (linkAnalysis.getInitDevices().getJSONObject(i).getString("spec").equalsIgnoreCase("波分设备")) {
                return true;
            }
        }
        return false;
    }

    private boolean hasBras(PmProductLinkAnalysis linkAnalysis) {
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            if (linkAnalysis.getInitDevices().getJSONObject(i).getString("spec").equalsIgnoreCase("宽带接入设备")) {
                return true;
            }
            if (linkAnalysis.getInitDevices().getJSONObject(i).getString("spec").equalsIgnoreCase("SR")) {
                return true;
            }
            if (linkAnalysis.getInitDevices().getJSONObject(i).getString("code").indexOf("ASBR") >= 0) { // 补充的编码识别
                return true;
            }
            if ("普通核心路由器".equalsIgnoreCase(linkAnalysis.getInitDevices().getJSONObject(i).getString("role_type"))) {
                return true;
            }
        }
        return false;
    }

    private boolean hasSwitch(PmProductLinkAnalysis linkAnalysis) {
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            if (linkAnalysis.getInitDevices().getJSONObject(i).getString("spec").indexOf("交换机")>=0) {
                return true;
            }
        }
        return false;
    }


    private void analysisIpVirtual(PmProductLinkAnalysis linkAnalysis,ShardingEnum shardingEnum) {

        if (this.hasSwitch(linkAnalysis) ) {
            linkAnalysis.setCjtype("光纤接入CN2");
        } else if (this.hasBras(linkAnalysis) ) {
            linkAnalysis.setCjtype("城域网接入CN2");
        } else if (this.hasOTN(linkAnalysis) ) {
            linkAnalysis.setCjtype("政企OTN接入CN2");
        } else if (this.isIpran(linkAnalysis)) {
            linkAnalysis.setCjtype("IPRAN接入CN2");
        }else if (this.hasSDH(linkAnalysis)){
            linkAnalysis.setCjtype("SDH接入CN2");
        } else if (this.hasMstp(linkAnalysis)){
            linkAnalysis.setCjtype("MSTP/MSAP接入CN2");
        } else {
            linkAnalysis.setCheckState("E");
            linkAnalysis.setCjtype("无法识别");
//            throw new RuntimeException("无法识别的接入方式");
        }

    }

    /**
     *
     * @param linkAnalysis
     * @param shardingEnum
     */
    private void initIpranTop(PmProductLinkAnalysis linkAnalysis, ShardingEnum shardingEnum,boolean init) {
        /**
         * {
         *     "a1_ip": "************",
         *     "a2_ip": null,
         *     "a3_ip": "",
         *     "a4_ip": "",
         *     "access_code": "南通CTVPN19429E",
         *     "b1_ip": "************",
         *     "b2_ip": "************",
         *     "b3_ip": null,
         *     "b4_ip": null,
         *     "bussiness_code": null,
         *     "m_vcid": "31061355",
         *     "s_vcid": "32061355",
         *     "source_ip": "************",
         *     "source_port": "eth 1/3",
         *     "terminal_ip": "************",
         *     "terminal_port": "GigabitEthernet1/1/1.3006"
         * }
         */
        String url = "http://**************:7070/LineRouteQuery?bussiness_code=" + linkAnalysis.getLinkCode();
        String ipran = restTemplate.getForObject(url,String.class);
        JSONObject ipranTop = JSONObject.parseObject(ipran);

        JSONObject source = null;
        JSONObject a1 = null;
        JSONObject a2 = null;
        JSONObject a3 = null;
        JSONObject a4 = null;
        JSONObject b1 = null;
        JSONObject b2 = null;
        JSONObject b3 = null;
        JSONObject b4 = null;
        JSONObject terminal = null;
        JSONObject param = new JSONObject();
        if (!StringUtils.isEmpty(ipranTop.getString("source_ip"))) {
            param.put("ip", ipranTop.getString("source_ip"));
            source = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
            if (source == null) {
                linkAnalysis.setProvinceAuditState("E");
                throw new RuntimeException("根据source ip未查询到资源设备信息,ip:"+ ipranTop.getString("source_ip"));
            }
        }
        if (!StringUtils.isEmpty(ipranTop.getString("a1_ip"))) {
            param.put("ip", ipranTop.getString("a1_ip"));
            a1 = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
        }
        if (!StringUtils.isEmpty(ipranTop.getString("a2_ip"))) {
            param.put("ip", ipranTop.getString("a2_ip"));
            a2 = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
        }
        if (!StringUtils.isEmpty(ipranTop.getString("a3_ip"))) {
            param.put("ip", ipranTop.getString("a3_ip"));
            a3 = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
        }
        if (!StringUtils.isEmpty(ipranTop.getString("a4_ip"))) {
            param.put("ip", ipranTop.getString("a4_ip"));
            a4 = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
        }
        if (!StringUtils.isEmpty(ipranTop.getString("b1_ip"))) {
            param.put("ip", ipranTop.getString("b1_ip"));
            b1 = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
        }
        if (!StringUtils.isEmpty(ipranTop.getString("b2_ip"))) {
            param.put("ip", ipranTop.getString("b2_ip"));
            b2 = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
        }
        if (!StringUtils.isEmpty(ipranTop.getString("b3_ip"))) {
            param.put("ip", ipranTop.getString("b3_ip"));
            b3 = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
        }
        if (!StringUtils.isEmpty(ipranTop.getString("b4_ip"))) {
            param.put("ip", ipranTop.getString("b4_ip"));
            b4 = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
        }
        if (!StringUtils.isEmpty(ipranTop.getString("terminal_ip"))) {
            param.put("ip", ipranTop.getString("terminal_ip"));
            terminal = this.queryDeviceByIp(param,shardingEnum.getBcShardingCode());
            if (terminal == null) {
                linkAnalysis.setProvinceAuditState("E");
                throw new RuntimeException("terminal ip未查询到资源设备信息,ip:"+ ipranTop.getString("terminal_ip"));
            }
        }
        if (source == null || terminal == null) {
            linkAnalysis.setProvinceAuditState("E");
            if (!init && linkAnalysis.getProductType().indexOf("云专网") >= 0 ) {
                // 初始化再调用
                JSONObject initParam = new JSONObject();
                initParam.put("sourceIp", null);
                initParam.put("sourcePort", null);
                initParam.put("circuitCode", linkAnalysis.getLinkCode());
                JSONObject ipranDevice = null;

                for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
                    JSONObject device =  linkAnalysis.getInitDevices().getJSONObject(i);
                    if ("108243".equals(device.getString("network_role_id")) ) {
                        ipranDevice = device;
                        break ;
                    }
                    if ("108240".equals(device.getString("network_role_id")) ) {
                        ipranDevice = device;
                        break ;
                    }
                    if ("108244".equals(device.getString("network_role_id")) ) {
                        ipranDevice = device;
                        break ;
                    }
                }
                if (ipranDevice !=null) {
                    this.initAnalysisExtend(linkAnalysis,shardingEnum); // 初始化IP之类信息
                    for (int i = 0; i < linkAnalysis.getInitIps().size(); i++) {
                        if (linkAnalysis.getInitIps().getJSONObject(i).getString("device_id").equalsIgnoreCase(ipranDevice.getString("id"))) {
                            initParam.put("sourceIp", linkAnalysis.getInitIps().getJSONObject(i).getString("code"));
                        }
                    }
                    for (int i = 0; i < linkAnalysis.getInitPorts().size(); i++) {
                        if (linkAnalysis.getInitPorts().getJSONObject(i).getString("device_id").equalsIgnoreCase(ipranDevice.getString("id"))) {
                            initParam.put("sourcePort", linkAnalysis.getInitPorts().getJSONObject(i).getString("code"));
                        }
                    }

                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);

                    // 创建HttpEntity
                    HttpEntity<Object> request = new HttpEntity<>(initParam, headers);

                    ResponseEntity<String> responseEntity = restTemplate.exchange(
                            "http://*************:8000/ChangeCloudLineNo",
                            HttpMethod.POST,
                            request,
                            String.class
                    );
                    String s = responseEntity.getBody();

                    JSONObject initResult = JSONObject.parseObject(s);
                    if ("FAIL".equalsIgnoreCase(initResult.getString("state"))) {
                        throw new RuntimeException("获取IPRAN信息失败,初始化失败:" + s);
                    }else {
                        this.initIpranTop(linkAnalysis,shardingEnum, true);
                        return;
                    }
                }
            }


            throw new RuntimeException("获取IPRAN信息失败,接口返回:" + ipran);
        }
        /**
         * 1、 Source 连接， A1,A2  如果A1为空就source 连接b1, 如果A2为空则source连接B2
         * 2 A1,A2 交叉连接B1,B2， B1,B2分别连接terminal
         * 3 暂时不考虑B3 B4
         */
        linkAnalysis.getInitLinks().clear();
        linkAnalysis.getInitDevices().clear();
        linkAnalysis.getInitDevices().add(a1);
        linkAnalysis.getInitDevices().add(a2);
        linkAnalysis.getInitDevices().add(a3);
        linkAnalysis.getInitDevices().add(a4);
        linkAnalysis.getInitDevices().add(b1);
        linkAnalysis.getInitDevices().add(b2);
        linkAnalysis.getInitDevices().add(b3);
        linkAnalysis.getInitDevices().add(b4);
        linkAnalysis.getInitDevices().add(source);
        linkAnalysis.getInitDevices().add(terminal);
        for (int i =  linkAnalysis.getInitDevices().size() - 1; i >=0; i--) {
            if ( linkAnalysis.getInitDevices().get(i) == null) {
                linkAnalysis.getInitDevices().remove(i);
            }

        }

        // 顶层链路
        JSONObject topLink = buildIpranLink(linkAnalysis.getAccessCode(),source,terminal, null);
        linkAnalysis.setLinkId(topLink.getBigDecimal("id"));
        topLink.put("spec","以太网链路");
        topLink.put("spec_id","1132100021");
        linkAnalysis.getInitLinks().add(topLink);

        if (b1 != null && terminal!=null) {
            JSONObject ipranLink = buildIpranLink("B1-TERMINAL",b1,terminal,linkAnalysis.getLinkId());
            linkAnalysis.getInitLinks().add(ipranLink);
        }
        if (b2 != null && terminal!=null) {
            JSONObject ipranLink = buildIpranLink("B2-TERMINAL",b2,terminal,linkAnalysis.getLinkId());
            linkAnalysis.getInitLinks().add(ipranLink);
        }

        if (source != null && a1 != null) {
            JSONObject ipranLink = buildIpranLink("SOURCE-A1",source,a1, null);
            linkAnalysis.getInitLinks().add(ipranLink);
        }
        if (source != null && a2 != null) {
            JSONObject ipranLink = buildIpranLink("SOURCE-A2",source,a2,null);
            linkAnalysis.getInitLinks().add(ipranLink);
        }
        if (source != null && a1 == null && a2 == null && b1!=null) {
            JSONObject ipranLink = buildIpranLink("SOURCE-B1",source,b1,null);
            linkAnalysis.getInitLinks().add(ipranLink);
        }
        if (source != null && a1 == null && a2 == null  && b2!=null) {
            JSONObject ipranLink = buildIpranLink("SOURCE-B2",source,b2,null);
            linkAnalysis.getInitLinks().add(ipranLink);
        }
        if (a1 != null && b1!=null) {
            JSONObject ipranLink = buildIpranLink("A1-B1",a1,b1,null);
            linkAnalysis.getInitLinks().add(ipranLink);
        }
        if (a1 != null && b2!=null) {
            JSONObject ipranLink = buildIpranLink("A1-B2",a1,b2,null);
            linkAnalysis.getInitLinks().add(ipranLink);
        }
        if (a2 != null && b1!=null) {
            JSONObject ipranLink = buildIpranLink("A1-B1",a1,b1,null);
            linkAnalysis.getInitLinks().add(ipranLink);
        }
        if (a2 != null && b2!=null) {
            JSONObject ipranLink = buildIpranLink("A1-B2",a1,b2,null);
            linkAnalysis.getInitLinks().add(ipranLink);
        }


    }

    private JSONObject buildIpranLink(String code,JSONObject a, JSONObject z,BigDecimal upperLinkId) {
        JSONObject link = new JSONObject();
        link.put("id",SnowFlakeGenerateIdUtils.generateNextId());
        link.put("code",code);
        link.put("name",code);
        link.put("spec_id",1132300043);
        link.put("spec","动态伪线");
        link.put("a_device_id",a.getString("id"));
        link.put("z_device_id",z.getString("id"));
        link.put("leaf_region_id",a.getString("leaf_region_id"));
        link.put("upper_link_id",upperLinkId);
        return link;
    }

    private JSONObject queryDeviceByIp(JSONObject param, String bcShardingCode) {
       JSONObject result = this.circuitAnalysisDao.queryDeviceByIp(param,bcShardingCode);
       if (CollectionUtils.isNotEmpty(result.getJSONArray("devices"))) {
           result.getJSONArray("devices").getJSONObject(0).put("ip",param.getString("ip"));
           return result.getJSONArray("devices").getJSONObject(0);
       }
       return null;
    }



    private void analysisDigital(PmProductLinkAnalysis linkAnalysis,ShardingEnum shardingEnum) {
        linkAnalysis.setCjtype("MSTP接入"); // 默认
        if (this.hasOTN(linkAnalysis)) {
            linkAnalysis.setCjtype("政企OTN接入");
        }
        // 混合组网是MSTP，前面如果是OTN，这里再判断是否有MSTP
        if (this.hasMstp(linkAnalysis)) {
            linkAnalysis.setCjtype("MSTP接入");
        }

        // 判断是否是SDH， 如果有SDH设备就是SDH
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            if (linkAnalysis.getInitDevices().getJSONObject(i).getString("spec").equalsIgnoreCase("SDH设备")) {
                linkAnalysis.setCjtype("SDH接入");
                break;
            }
        }
        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            if ("U".equalsIgnoreCase(linkAnalysis.getInitDevices().getJSONObject(i).getString("role_type"))) {
                linkAnalysis.setCjtype("传输IPRAN接入");
                break;
            }
            if ("A".equalsIgnoreCase(linkAnalysis.getInitDevices().getJSONObject(i).getString("role_type"))) {
                linkAnalysis.setCjtype("传输IPRAN接入");
                break;
            }
            if ("B".equalsIgnoreCase(linkAnalysis.getInitDevices().getJSONObject(i).getString("role_type"))) {
                linkAnalysis.setCjtype("传输IPRAN接入");
                break;
            }
        }




    }

    /**
     * 初始化设备和链路信息

     */
    private void initAnalysisLinks(PmProductLinkAnalysis linkAnalysis,ShardingEnum shardingEnum) {
        Set<BigDecimal> deviceIds = new HashSet<>();
        Set<BigDecimal> portIds = new HashSet<>();
        JSONObject param = new JSONObject();
        param.put("linkId", linkAnalysis.getLinkId());
        JSONObject allLinks = circuitAnalysisDao.queryResAllLinkByTopId(param,shardingEnum.getBcShardingCode());

        linkAnalysis.setInitLinks( allLinks.getJSONArray("links"));
        for (int i = 0; i < linkAnalysis.getInitLinks().size(); i++) {
            deviceIds.add(linkAnalysis.getInitLinks().getJSONObject(i).getBigDecimal("a_device_id"));
            deviceIds.add(linkAnalysis.getInitLinks().getJSONObject(i).getBigDecimal("z_device_id"));
            if (linkAnalysis.getInitLinks().getJSONObject(i).getBigDecimal("a_port_id") != null) {
                portIds.add(linkAnalysis.getInitLinks().getJSONObject(i).getBigDecimal("a_port_id"));
            }
            if (linkAnalysis.getInitLinks().getJSONObject(i).getBigDecimal("z_port_id") != null) {
                portIds.add(linkAnalysis.getInitLinks().getJSONObject(i).getBigDecimal("z_port_id"));
            }
        }
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            param.put("ids",deviceIds);
            linkAnalysis.setInitDevices( circuitAnalysisDao.queryResDeviceByIds(param,shardingEnum.getBcShardingCode()).getJSONArray("devices"));
            param.put("ids",portIds);
            linkAnalysis.setInitPorts(circuitAnalysisDao.queryResPortByIds(param,shardingEnum.getBcShardingCode()).getJSONArray("ports"));

        }

    }


    /**
     * 机房、局站、标准地址、IP、cfs
     * @param linkAnalysis
     * @param shardingEnum
     */
    private void initAnalysisExtend(PmProductLinkAnalysis linkAnalysis,ShardingEnum shardingEnum) {
        JSONObject param = new JSONObject();
        Set<BigDecimal> deviceIds = new HashSet<>();
        Set<BigDecimal> roomIds = new HashSet<>();
        Set<BigDecimal> addressIds = new HashSet<>();
        Set<BigDecimal> tmlIds = new HashSet<>(); // 局站
        Set<BigDecimal> wareIds = new HashSet<>(); // 板卡

        if (linkAnalysis.getAProvinceId() != null
                && linkAnalysis.getZProvinceId() != null
                && (!String.valueOf(linkAnalysis.getAProvinceId()).equals(String.valueOf(linkAnalysis.getZProvinceId())))) {
            linkAnalysis.setLongDistanceType("跨省");
        } else if (linkAnalysis.getARegionId() != null
                && linkAnalysis.getZRegionId() != null
                && (String.valueOf(linkAnalysis.getARegionId()).equals(String.valueOf(linkAnalysis.getZRegionId())))) {
            linkAnalysis.setLongDistanceType("本地");
        } else if (linkAnalysis.getAProvinceId() != null
                && linkAnalysis.getZProvinceId() != null
                && (String.valueOf(linkAnalysis.getAProvinceId()).equals(String.valueOf(linkAnalysis.getZProvinceId())))) {
            if (linkAnalysis.getARegionId() != null
                    && linkAnalysis.getZRegionId() != null
                    && (!String.valueOf(linkAnalysis.getARegionId()).equals(String.valueOf(linkAnalysis.getZRegionId())))) {
                linkAnalysis.setLongDistanceType("省内");
            }
        }   else if (hasSameLinkRegions(linkAnalysis)) {
            linkAnalysis.setLongDistanceType("省内");
        }

        for (int i = 0; i < linkAnalysis.getInitPorts().size(); i++) {
            JSONObject _port = linkAnalysis.getInitPorts().getJSONObject(i);
            if (!StringUtils.isEmpty(_port.getString("ware_id"))) {
                wareIds.add(new BigDecimal(_port.getString("ware_id")));
            }
        }

        for (int i = 0; i < linkAnalysis.getInitDevices().size(); i++) {
            JSONObject _device = linkAnalysis.getInitDevices().getJSONObject(i);
            if (!StringUtils.isEmpty(_device.getString("facility_id"))) {
                roomIds.add(new BigDecimal(_device.getString("facility_id")));
            }
            if (!StringUtils.isEmpty(_device.getString("tml_id"))) {
                tmlIds.add(new BigDecimal(_device.getString("tml_id")));
            }
            deviceIds.add(new BigDecimal(_device.getString("id")));
        }
        linkAnalysis.setInitRooms( new JSONArray());
        if (CollectionUtils.isNotEmpty(roomIds)) {
            param.put("ids",roomIds);
            linkAnalysis.setInitRooms(circuitAnalysisDao.queryResRoomsByIds(param,shardingEnum.getBcShardingCode()).getJSONArray("rooms"));
            // 添加机房对应局站
            for (int i = 0; i < linkAnalysis.getInitRooms().size(); i++) {
                JSONObject _room = linkAnalysis.getInitRooms().getJSONObject(i);
                if (!StringUtils.isEmpty(_room.getString("tml_id"))) {
                    tmlIds.add(new BigDecimal(_room.getString("tml_id")));
                }
            }
        }
        linkAnalysis.setInitStations( new JSONArray());
        if (CollectionUtils.isNotEmpty(tmlIds)) {
            param.put("ids",tmlIds);
            linkAnalysis.setInitStations(circuitAnalysisDao.queryResTmlByIds(param,shardingEnum.getBcShardingCode()).getJSONArray("tmls"));
        }
//        // ip
        param.put("ids",deviceIds);
        linkAnalysis.setInitIps(circuitAnalysisDao.queryResIpByDeviceIds(param,shardingEnum.getBcShardingCode()).getJSONArray("ips"));
        // cfs
        param.put("id",linkAnalysis.getServiceId());
        linkAnalysis.setCfs(circuitAnalysisDao.queryCfsByServiceId(param,shardingEnum.getBcShardingCode()).getJSONArray("cfs").getJSONObject(0));
        if (linkAnalysis.getCfs() == null || StringUtils.isEmpty(linkAnalysis.getCfs().getString("id"))) {
            throw new RuntimeException("产品服务信息缺失");
        }
        // 标准地址
        if (linkAnalysis.getCfs().getString("standard_address_id") !=null) {
            addressIds.add(new BigDecimal(linkAnalysis.getCfs().getString("standard_address_id")));
        }

        param.put("ids",addressIds);
        linkAnalysis.setInitAddresses(new JSONArray());
        if (CollectionUtils.isNotEmpty(addressIds)) {
            linkAnalysis.setInitAddresses(circuitAnalysisDao.queryResAddressByIds(param,shardingEnum.getBcShardingCode()).getJSONArray("addresses"));

        }

        linkAnalysis.setInitWares( new JSONArray());

    }

    /**
     * 判断是否有多条,如果有这是省内
     * @param linkAnalysis
     * @return
     */
    private boolean hasSameLinkRegions(PmProductLinkAnalysis linkAnalysis) {
        if (StringUtils.isEmpty(linkAnalysis.getLinkCode())) {
            return false;
        }
        JSONObject param = new JSONObject();
        param.put("linkCode", linkAnalysis.getLinkCode());
        PageResponse<PmProductLinkAnalysis> pageResponse = otnDao.select_pm_product_link_analysis_by_condition(param
                , 10, 1, "ds_graph_js");
        return pageResponse.getData().size() > 1;
    }

}
