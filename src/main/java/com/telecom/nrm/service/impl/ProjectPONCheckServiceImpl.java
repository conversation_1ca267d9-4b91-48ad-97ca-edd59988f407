package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.controller.GroupFaultRapidPositioningController;
import com.telecom.nrm.dao.CutoverDao;
import com.telecom.nrm.dao.GroupFaultRapidPositioningDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.ProjectPONCheckService;
import com.telecom.nrm.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProjectPONCheckServiceImpl implements ProjectPONCheckService {

    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @Autowired
    CutoverDao cutoverDao;

    @Autowired
    GroupFaultRapidPositioningController groupFaultRapidPositioningController;

    @Autowired
    GroupFaultRapidPositioningDao groupFaultRapidPositioningDao;

    public JSONObject ponCheck (JSONObject request, String action, List<JSONObject> targetPonList, String batchNo) {

        JSONObject result = new JSONObject();

        if (ObjectUtils.isEmpty(request)) {
            result.put("status", "fail");
            result.put("message", "缺少参数");
            return result;
        }
        String projectId = request.getString("project_id");
        String city = request.getString("city");
        String dataSource = "ds_odso_"+city;

        List<JSONObject> actionList = cutoverDao.listProjectRunningAction(request, NRMConstants.SHARDING_GRAPH_DB);
        if (ObjectUtil.isNotEmpty(actionList)) {
            result.put("status", "fail");
            result.put("message", "当前有任务正在运行");
            return result;
        }

        boolean isPatch = false;
        if (ObjectUtil.isNotEmpty(request.getString("is_patch")) && request.getString("is_patch").equals("1")) {
            isPatch = true;
        }
        final boolean isPatchFinal = isPatch;


        PageResponse<JSONObject> influenceProdPageByProjectId = cutoverDao.getInfluenceProdIDByProjectId(projectId, 10000, 1, NRMConstants.SHARDING_GRAPH_DB); // 查询项目影响业务
        List<JSONObject> influenceProdList = influenceProdPageByProjectId.getData();
        if (ObjectUtils.isEmpty(influenceProdList)) {
            result.put("status", "fail");
            result.put("message", "影响业务范围为空,无法检测");
            return result;
        }

        // 根据产品查询OLT、PON口、分光器及端口信息
        JSONArray productLst = new JSONArray();
        PageUtils.doSomethingByPage(part->{
            List<BigDecimal> influenceProdIdList = new ArrayList<>();
            for (JSONObject jsonObject: part) {
                influenceProdIdList.add(new BigDecimal(jsonObject.getString("crm_product_id")));
            }
            PageResponse<JSONObject> productPage = cutoverDao.getRelatePonByProdId(influenceProdIdList, 10000, 1, dataSource);
            List<JSONObject> prodListPart = productPage.getData();
            productLst.addAll(prodListPart);
        },influenceProdList,1000 );


        JSONArray ponList = distinctPONPort(productLst);
        ponList.addAll(targetPonList);

        if (ObjectUtils.isEmpty(ponList)) {
            result.put("status", "fail");
            result.put("message", "没有关联的PON口，无法检测");
            return result;
        }

        List<JSONObject> sucessPortList = cutoverDao.listSuccessPonPort(projectId,action,NRMConstants.SHARDING_GRAPH_DB);
        if (isPatch) {
            // 如果部分验证
            ponList = filterFailPonPort(ponList,sucessPortList);
        }
        log.info("ponInfo: {}", ponList);
        request.put("poninfo", ponList);

        if (ObjectUtils.isEmpty(ponList)) {
            result.put("status", "fail");
            result.put("message", "所有端口均已检测,无需重复发起");
            return result;
        }

        //生成批次号
        // String batchNo = generateBatchNo();
        JSONObject actionStatus = new JSONObject();
        actionStatus.put("batch_no", batchNo);
        actionStatus.put("status", "0");
        actionStatus.put("project_id", projectId);
        cutoverDao.insertActionStatus(actionStatus, NRMConstants.SHARDING_GRAPH_DB);
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    if (ObjectUtil.isEmpty(request.getJSONArray("poninfo"))) {
                        return;
                    }
                    JSONObject tempresult = groupFaultRapidPositioningController.multiplePonCheck(request);

                    JSONArray originalResult = new JSONArray();
                    if(ObjectUtil.isNotEmpty(tempresult.getJSONArray("ponCheckCombinePonTree"))){

                        originalResult =tempresult.getJSONArray("ponCheckCombinePonTree"); // 发起检测

                    }
                    JSONObject ipMap = tempresult.getJSONObject("ipmap");

                    JSONArray poncheckResult = handlePonCheckResult(originalResult, batchNo, projectId); // 提取PON检测结果
                    poncheckResult.addAll(handleUncheckResult(productLst, poncheckResult, batchNo, projectId)); // 在检测结果中补充没有检测到的产品
                    handleProductInfo(productLst, poncheckResult,action); // 设置产品的分光器及端口属性
                    handleNMIP(JSONObjectUtil.jsonObjectArrayToList(poncheckResult), city, ipMap);//更新OLT的网管IP属性
                    if (!isPatchFinal) {
                        cutoverDao.updatePrProjectServiceRowStatusInvalid(projectId, action, NRMConstants.SHARDING_GRAPH_DB);
                    }else{
                        cutoverDao.deleteFailPonCheck(projectId, action, NRMConstants.SHARDING_GRAPH_DB); // 删除不在线的OLTPON口
                    }

                    if (ObjectUtil.isNotEmpty(poncheckResult)) {
                        cutoverDao.insertPoncheckResult(poncheckResult, NRMConstants.SHARDING_GRAPH_DB);
                        cutoverDao.deleteOfflineDupPonCheck(projectId, action, NRMConstants.SHARDING_GRAPH_DB);
                    }

                }catch (Exception ex) {
                    log.error(ex.getMessage(),ex);
                } finally{
                    cutoverDao.updateActionStatus(batchNo, NRMConstants.SHARDING_GRAPH_DB);
                }
            }
        });
        result.put("status", "success");
        return result;
    }

    public JSONArray distinctPONPort(JSONArray ponList){
        Map<String, JSONObject> map = new HashMap<>();
        int size = ponList.size();
        for (int i=0; i<size; i++) {
            JSONObject prod = ponList.getJSONObject(i);
            JSONObject pon = new JSONObject();
            pon.put("pon_code", prod.getString("pon_code"));
            pon.put("ip", prod.getString("ip"));
            map.put(pon.getString("ip")+"-"+pon.getString("pon_code"), pon);
        }
        Collection<JSONObject> distinctPonList = map.values();
        JSONArray distinctPonArray = new JSONArray();
        distinctPonArray.addAll(distinctPonList);
        return distinctPonArray;
    }

    public JSONArray filterFailPonPort (JSONArray ponList, List<JSONObject> sucessPortList) {

        JSONArray filteredPonList = new JSONArray();
        int ponTotal = ponList.size();
        for (int i=0; i<ponTotal; i++) {
            JSONObject pon = ponList.getJSONObject(i);
            boolean isExists = false;
            if (ObjectUtil.isEmpty(sucessPortList)) sucessPortList = new ArrayList<>();
            for (JSONObject successPort: sucessPortList) {
                String ip = successPort.getString("olt_ip");
                String ponCode = successPort.getString("olt_port_code");
                if (ip.equals(pon.getString("ip")) && ponCode.equals(pon.getString("pon_code"))) {
                    isExists = true;
                    break;
                }
            }
            if (!isExists) {
                // 如果上次运行没有成功,则将端口添加为待检测端口
                filteredPonList.add(pon);
            }
        }
        return filteredPonList;
    }

    private JSONArray handlePonCheckResult(JSONArray jsonArray, String batchNo, String projectId) {
        JSONArray res = new JSONArray();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject curDevInfo = jsonArray.getJSONObject(i);
            String msg = curDevInfo.getString("msg");
            if (!"调用成功".equals(msg)) {
                continue;
            }
            String resDevIp = curDevInfo.getString("res_dev_ip");
            String devIp = curDevInfo.getString("devip");
            // String frame = curDevInfo.getString("frame");
            // String slot = curDevInfo.getString("slot");
            // String port = curDevInfo.getString("port");
            String curPonCode = curDevInfo.getString("port_code");
            // String curPonCode = formatPonCode(port, slot, frame);
            JSONArray accessDeviceList = curDevInfo.getJSONArray("access_device");
            if (ObjectUtils.isEmpty(accessDeviceList)){
                continue;
            }
            for (int j = 0; j < accessDeviceList.size(); j++) {
                JSONObject perRes = accessDeviceList.getJSONObject(j);
                JSONArray cfs = perRes.getJSONArray("cfs");
                if (ObjectUtil.isEmpty(cfs)) {continue;}
                for (int k = 0; k < cfs.size(); k++) {
                    JSONObject curProd = cfs.getJSONObject(k);
                    JSONObject curCheckRes = new JSONObject();
                    curCheckRes.put("access_device_code", perRes.getString("code"));
                    curCheckRes.put("access_device_code_loid", perRes.getString("loid"));
                    if (!ObjectUtil.isEmpty(perRes.getString("runState"))) {
                        curCheckRes.put("online_status", perRes.getString("runState"));
                    } else {
                        curCheckRes.put("online_status", "unknown");
                    }

                    if (!ObjectUtil.isEmpty(perRes.getString("rxPower"))) {
                        curCheckRes.put("rx_power", perRes.getString("rxPower"));
                    } else {
                        curCheckRes.put("rx_power", "unknown");
                    }
                    curCheckRes.put("olt_ip", resDevIp);
                    curCheckRes.put("olt_nm_ip", devIp);
                    curCheckRes.put("olt_port_code", curPonCode);
                    curCheckRes.put("crm_product_id", curProd.getString("crm_product_id"));
                    //actin为2表示插入pon口检测结果
                    // curCheckRes.put("action", 2);
                    curCheckRes.put("batch_no", batchNo);
                    curCheckRes.put("project_id", projectId);
                    curCheckRes.put("row_status", 1);
                    curCheckRes.put("service_type", curProd.getString("pro_spec"));
                    curCheckRes.put("access_code", curProd.getString("access_code"));
                    res.add(curCheckRes);
                }
            }
        }
        return res;
    }

    private JSONArray handleUncheckResult(JSONArray productList,JSONArray ponCheckResult, String batchNo, String projectId) {
        Set<String> productIdList = new HashSet<>();
        int ponCheckResultSize = ponCheckResult.size();
        for (int i = 0; i < ponCheckResultSize; i++) {
            JSONObject checkObject = ponCheckResult.getJSONObject(i);
            productIdList.add(checkObject.getString("crm_product_id"));
        }
        JSONArray result = new JSONArray();
        int productListSize = productList.size();
        for (int i = 0; i < productListSize; i++) {
            JSONObject curProd = productList.getJSONObject(i);
            if (!productIdList.contains(curProd.getString("prod_id"))) {
                JSONObject returnObject = new JSONObject();

                returnObject.put("olt_ip", curProd.getString("ip"));
                returnObject.put("olt_port_code", curProd.getString("pon_code"));
                returnObject.put("crm_product_id", curProd.getString("prod_id"));
                returnObject.put("online_status", "unknown");

                // action为2表示插入pon口检测结果
                // returnObject.put("action", 2);
                returnObject.put("batch_no", batchNo);
                returnObject.put("project_id", projectId);
                returnObject.put("row_status", 1);


                returnObject.put("obd_name", curProd.getString("obd_name"));
                returnObject.put("obd_code", curProd.getString("obd_code"));
                returnObject.put("obd_port_code", curProd.getString("obd_port_code"));
                result.add(returnObject);
            }
        }

        return result;

    }

    private void handleProductInfo(JSONArray productList, JSONArray ponCheckResult, String action) {

        Set<String> obdList =new HashSet<>(); // 计算已经得出OBD的产品
        int ponCheckResultSize = ponCheckResult.size();
        for (int i = 0; i < ponCheckResultSize; i++) {
            JSONObject curCheckResult = ponCheckResult.getJSONObject(i);
            if (ObjectUtil.isNotEmpty(curCheckResult.getString("obd_port_code"))) {
                // 如果已经得出了OBD,则计算OBD列表
                obdList.add(curCheckResult.getString("crm_product_id") + "-" + curCheckResult.getString("obd_code") + "-" + curCheckResult.getString("obd_port_code"));
            }
        }


        int productListSize = productList.size();
        Map<String, JSONObject> productMap = new HashMap<>();
        for (int i = 0; i < productListSize; i++) {
            JSONObject curProd = productList.getJSONObject(i);
            if (!obdList.contains(curProd.getString("prod_id") +"-"+curProd.getString("obd_code")+"-"+curProd.getString("obd_port_code"))) {
                productMap.put(curProd.getString("prod_id"), curProd);
            }

        }

        for (int i = 0; i < ponCheckResultSize; i++) {
            JSONObject curCheckResult = ponCheckResult.getJSONObject(i);
            curCheckResult.put("action", action);
            String productId = curCheckResult.getString("crm_product_id");
            JSONObject curProd = productMap.get(productId);
            if (ObjectUtil.isNotEmpty(curProd)  && ObjectUtil.isEmpty(curCheckResult.getString("obd_port_code"))) {
                curCheckResult.put("obd_name", curProd.getString("obd_name"));
                curCheckResult.put("obd_code", curProd.getString("obd_code"));
                curCheckResult.put("obd_port_code", curProd.getString("obd_port_code"));
            }
        }

    }

    Map<String,String> fetchOltNMIPMap(List<JSONObject> ponCheckResults, String city) {
        List<String> oltIPs = ponCheckResults.stream().map(d->d.getString("olt_ip")).collect(Collectors.toList());
        JSONObject ipparam = new JSONObject();
        ipparam.put("loopback_ips", oltIPs);
        PageResponse<JSONObject> response = groupFaultRapidPositioningDao.pageQueryPONManageIP(ipparam,1000,1,"ds_odso_"+city);

        List<JSONObject> queryData= response.getData();

        Map<String,String> ipmap = new HashMap<>();
        if(ObjectUtil.isNotEmpty(queryData)) {

            log.info("ipresult" + queryData);
            JSONArray resultArray = new JSONArray();
            for (int i = 0; i < queryData.size(); i++) {
                // 获取当前的JSONObject对象
                JSONObject jsonObject = queryData.get(i);
                // 从JSONObject中提取loopback_ip_2nd字段的值
                String loopbackIp2nd = jsonObject.getString("loopback_ip_2nd");
                String loopbackIp = jsonObject.getString("loopback_ip");
                ipmap.put(loopbackIp, loopbackIp2nd);
            }
        }
        return ipmap;
    }

    void handleNMIP(List<JSONObject> ponCheckResults, String city, JSONObject ipMap) {

        Map<String,String> ipMap2 = fetchOltNMIPMap(ponCheckResults,city);

        for (JSONObject ponCheckResult : ponCheckResults) {
            if (ObjectUtil.isEmpty(ponCheckResult.getString("olt_nm_ip")) && ObjectUtil.isNotEmpty(ponCheckResult.getString("olt_ip"))) {
                String oltNmIP=ipMap2.get(ponCheckResult.getString("olt_ip"));
                ponCheckResult.put("olt_nm_ip",oltNmIP);
            }
        }
    }

    /*
    private String formatPonCode(String port, String slot, String frame) {
        StringBuilder sb = new StringBuilder();
        if (port.length() < 2) {
            sb.append('0'); // 补0
        }
        sb.append(port);
        String formatPort = sb.toString();
        StringBuilder sb1 = new StringBuilder();
        if (slot.length() < 2) {
            sb1.append('0'); // 补0
        }
        sb1.append(slot);
        String formatSlot = sb1.toString();
        StringBuilder sb2 = new StringBuilder();
        if (frame.length() < 2) {
            sb2.append('0'); // 补0
        }
        sb2.append(frame);
        String formatFrame = sb2.toString();
        return formatFrame + "/" + formatSlot + "/" + formatPort;
    }

     */


}
