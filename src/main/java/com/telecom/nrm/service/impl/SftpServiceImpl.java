package com.telecom.nrm.service.impl;

import com.jcraft.jsch.*;
import com.telecom.nrm.config.SftpProperties;
import com.telecom.nrm.service.SftpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Properties;

/**
 * SFTP服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Service
@Slf4j
public class SftpServiceImpl implements SftpService {
    
    @Autowired
    private SftpProperties sftpProperties;
    
    /**
     * 获取SFTP连接
     */
    private ChannelSftp getSftpChannel() throws JSchException {
        JSch jsch = new JSch();
        Session session = jsch.getSession(sftpProperties.getSftpuser(),
                                         sftpProperties.getSftpserver(), 
                                         sftpProperties.getSftpport());
        session.setPassword(sftpProperties.getSftppassword());
        
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);

        try {
            session.connect();
        } catch (JSchException e) {
            throw new RuntimeException(e);
        }

        Channel channel = session.openChannel("sftp");
        channel.connect();
        
        return (ChannelSftp) channel;
    }
    
    /**
     * 关闭SFTP连接
     */
    private void closeSftpChannel(ChannelSftp channelSftp) {
        if (channelSftp != null) {
            try {
                if (channelSftp.getSession() != null) {
                    channelSftp.getSession().disconnect();
                }
                channelSftp.disconnect();
            } catch (Exception e) {
                log.warn("关闭SFTP连接时发生异常: {}", e.getMessage());
            }
        }
    }
    
    @Override
    public boolean uploadFile(String userWorkNumber, String fileName, InputStream inputStream) {
        ChannelSftp channelSftp = null;
        try {
            log.info("🚀 开始上传文件到SFTP服务器: userWorkNumber={}, fileName={}", userWorkNumber, fileName);
            
            // 获取SFTP连接
            channelSftp = getSftpChannel();
            
            // 构建目标路径：/统一工号/文件名
            String targetDir = sftpProperties.getOutputpath() + "/" + userWorkNumber;
            String targetPath = targetDir + "/" + fileName;
            
            log.info("📁 目标路径: {}", targetPath);
            
            // 创建目录（如果不存在）
            createDirectoryIfNotExists(channelSftp, targetDir);
            
            // 上传文件
            channelSftp.put(inputStream, targetPath);
            
            log.info("✅ 文件上传成功: {}", targetPath);
            return true;
            
        } catch (Exception e) {
            log.error("❌ 文件上传失败: userWorkNumber={}, fileName={}, error={}", 
                     userWorkNumber, fileName, e.getMessage(), e);
            return false;
        } finally {
            closeSftpChannel(channelSftp);
        }
    }
    
    /**
     * 创建目录（如果不存在）
     */
    private void createDirectoryIfNotExists(ChannelSftp channelSftp, String dirPath) throws SftpException {
        try {
            // 尝试进入目录
            channelSftp.cd(dirPath);
            log.info("📁 目录已存在: {}", dirPath);
        } catch (SftpException e) {
            // 目录不存在，创建目录
            log.info("📁 创建目录: {}", dirPath);
            
            // 分层创建目录
            String[] dirs = dirPath.split("/");
            String currentPath = "";
            
            for (String dir : dirs) {
                if (dir.isEmpty()) continue;
                
                currentPath += "/" + dir;
                try {
                    channelSftp.cd(currentPath);
                } catch (SftpException ex) {
                    channelSftp.mkdir(currentPath);
                    channelSftp.cd(currentPath);
                    log.info("📁 创建子目录: {}", currentPath);
                }
            }
        }
    }
    
    @Override
    public boolean testConnection() {
        ChannelSftp channelSftp = null;
        try {
            log.info("🔍 测试SFTP连接: server={}:{}, user={}", 
                    sftpProperties.getSftpserver(), 
                    sftpProperties.getSftpport(), 
                    sftpProperties.getSftpuser());
            
            channelSftp = getSftpChannel();
            
            // 测试列出根目录
            channelSftp.ls(sftpProperties.getOutputpath());
            
            log.info("✅ SFTP连接测试成功");
            return true;
            
        } catch (Exception e) {
            log.error("❌ SFTP连接测试失败: {}", e.getMessage(), e);
            return false;
        } finally {
            closeSftpChannel(channelSftp);
        }
    }
}
