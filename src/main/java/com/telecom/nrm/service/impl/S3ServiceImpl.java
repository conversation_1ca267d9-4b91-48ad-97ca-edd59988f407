package com.telecom.nrm.service.impl;


import com.amazonaws.services.s3.model.*;
import com.telecom.nrm.service.S3Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.amazonaws.services.s3.model.ObjectListing;

import java.io.*;
import java.util.List;

import com.amazonaws.services.s3.AmazonS3;

import java.io.File;

@Service
public class S3ServiceImpl implements S3Service {
    private final Logger log = LoggerFactory.getLogger(S3ServiceImpl.class);
    @Autowired(required = true)
    AmazonS3 s3Connection ;





    public List<Bucket> listBuckets () {

        List<Bucket> buckets = s3Connection.listBuckets();
        /*
        for (Bucket bucket : buckets) {
            log.info(bucket.getName() + "\t" +
                    StringUtils.fromDate(bucket.getCreationDate()));
        }
         */
        return buckets;
    }

    @Override
    public String readFile (String bucketName,String fileName) throws IOException {
        // S3Object s3Object=s3Connection.getObject(new GetObjectRequest("oss-wzrh","iboc/net-resource-integration/ipman/2022043028457091072_2022042275607588864_18.121.128.20_HW_NE8000.20220503000001.932"));
        // S3Object s3Object=s3Connection.getObject(new GetObjectRequest(bucketName,fileName));
        InputStream is = readStream(bucketName, fileName);
        InputStreamReader reader = new InputStreamReader(is);
        BufferedReader br = new BufferedReader(reader);
        String line = null;
        StringBuffer sb =new StringBuffer();
        while(( line=br.readLine())!=null) {//不知道文件中有多少行数据，用while循环
            sb.append(line);
        }
        return sb.toString();
    }
    @Override
    public InputStream readStream (String bucketName,String fileName) {



        S3Object s3Object=s3Connection.getObject(new GetObjectRequest(bucketName,fileName));
        InputStream is = s3Object.getObjectContent();
        return is;
    }


    @Override
    public void writeStream(String bucketName, String fileName, InputStream inputStream) {
        try {
            log.info("Starting to write file - bucket: {}, file: {}", bucketName, fileName);

            // 创建临时文件
            File tempFile = File.createTempFile("s3upload", null);
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                // 直接复制输入流到临时文件
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }

            // 设置元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(tempFile.length());
            metadata.setContentType(determineContentType(fileName));

            // 上传到S3
            try (FileInputStream fis = new FileInputStream(tempFile)) {
                PutObjectRequest putRequest = new PutObjectRequest(bucketName, fileName, fis, metadata);
                PutObjectResult result = s3Connection.putObject(putRequest);
                log.info("File uploaded successfully: {}, size: {} bytes", fileName, tempFile.length());
            }

            // 清理临时文件
            tempFile.delete();

        } catch (Exception e) {
            log.error("Failed to write stream - bucket: {}, file: {}, error: {}",
                    bucketName, fileName, e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private String determineContentType(String fileName) {
        String extension = fileName.toLowerCase();
        if (extension.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (extension.endsWith(".doc")) {
            return "application/msword";
        } else if (extension.endsWith(".pdf")) {
            return "application/pdf";
        } else if (extension.endsWith(".txt")) {
            return "text/plain";
        } else {
            return "application/octet-stream";
        }
    }

    @Override
    public ObjectMetadata getObjectMetadata(String bucketName, String fileName) {
        return s3Connection.getObjectMetadata(bucketName, fileName);
    }

    @Override
    public ObjectListing listObjects(String bucketName, String prefix) {
        return s3Connection.listObjects(bucketName, prefix);
    }

    @Override
    public void deleteObject(String bucketName, String fileName) {
        try {
            log.info("删除S3对象 - bucket: {}, file: {}", bucketName, fileName);
            s3Connection.deleteObject(bucketName, fileName);
            log.info("S3对象删除成功: {}", fileName);
        } catch (Exception e) {
            log.error("删除S3对象失败 - bucket: {}, file: {}, error: {}",
                    bucketName, fileName, e.getMessage());
            throw new RuntimeException("删除S3对象失败: " + e.getMessage(), e);
        }
    }



}
