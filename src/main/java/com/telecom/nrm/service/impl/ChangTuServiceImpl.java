package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.domain.topo.TopoMode;
import com.telecom.nrm.service.ChangTuService;
import com.telecom.nrm.service.CommonCircuitService;
import com.telecom.nrm.service.GraphApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ChangTuServiceImpl implements ChangTuService {

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    CommonCircuitService commonCircuitService;


    @Override
    public JSONObject queryChangTuRoute(JSONObject request) {
        String areaCode=request.getString("areaCode");
        String code = request.getString("code");


        if (ObjectUtil.isEmpty(areaCode)) areaCode="nj";

        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_changtu_circuit").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("changtu_circuit","code",code))
                .build();

        Graph graph = graphApiService.doApi(graphRequest);

        // todo return null
        return graphToTOPO(graph);
    }

    public JSONObject graphToTOPO (Graph graph) {
        Set<Node> nodes = graph.getNodes();
        Set<Edge> edges = graph.getEdges();
        Node deviceNode = nodes.stream().filter(n->n.getId().equals("changtu_device")).collect(Collectors.toList()).get(0);
        Node linkNode = nodes.stream().filter(n->n.getId().equals("changtu_link")).collect(Collectors.toList()).get(0);
        Edge linkCarryLinkEdge = edges.stream().filter(e->e.getAId().equals("changtu_link") && e.getBId().equals("changtu_circuit") && e.getType().equals("carry")).collect(Collectors.toList()).get(0);


        List<JSONObject> devices = deviceNode.getData();
        List<JSONObject> links = linkNode.getData();
        List<JSONObject> linkRelations = linkCarryLinkEdge.getData();

        linkRelations.removeIf(d->  ObjectUtil.isNotEmpty(d.getString("upper_link_id")) && ObjectUtil.isNotEmpty(d.getString("lower_link_id")) &&  d.getString("upper_link_id").equals(d.getString("lower_link_id")));

        JSONObject result = commonCircuitService.sortCircuitTopology(devices,links,linkRelations, TopoMode.NET, "");
        return result;
    }
}
