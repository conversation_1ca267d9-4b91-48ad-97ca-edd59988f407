package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.DaClient;
import com.telecom.da.client.DaRequest;
import com.telecom.nrm.controller.S3Controller;
import com.telecom.nrm.entity.ApiException;
import com.telecom.nrm.entity.TypeReference;
import com.telecom.nrm.service.UnifiedApiService;
import lombok.extern.java.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.text.DateFormat;

@Service
public class UnifiedApiServiceImpl implements UnifiedApiService {

    @Autowired
    private DaClient daClient;

    private static final Logger log = LoggerFactory.getLogger(UnifiedApiService.class);


    @Override
    public <T> T executeApi(JSONObject request, Class<T> responseType) {
        return doExecute(request, responseType);
    }

    @Override
    public <T> T executeApi(JSONObject request, TypeReference<T> typeRef) {
        return doExecute(request, typeRef.getType());
    }

    private <T> T doExecute(JSONObject request, Type responseType) {
        validateRequest(request);

        try {
            // 构建基础请求参数
            final String apiCode = extractRequiredField(request, "apiCode");
            final String version = request.getString("version");
            final String shardingCode = request.getString("shardingCode");
            final JSONObject params = request.getJSONObject("param");


            log.info(apiCode);

            log.info(request.toString());


            // 构建并执行请求
            DaRequest<String> daRequest = daClient.buildRequest()
                    .returnType(String.class) // 统一返回字符串
                    .apiCode(apiCode)
                    .version(version)
                    .shardingCode(shardingCode)
                    .param(params)
                    .build();

            String rawResponse = daRequest.doApi();

            log.info(rawResponse);

            return parseResponse(rawResponse, responseType);
        } catch (JSONException e) {
            throw new ApiException("请求参数解析失败", e);
        } catch (Exception e) {
            throw new ApiException("API调用异常", e);
        }
    }

    private <T> T parseResponse(String rawResponse, Type responseType) {
        try {
            // 使用Fastjson反序列化
            return JSON.parseObject(rawResponse, responseType);
        } catch (Exception e) {
            throw new ApiException("响应解析失败", e);
        }
    }

    private void validateRequest(JSONObject request) {
        if (request == null) {
            throw new ApiException("请求不能为null");
        }
        checkRequiredField(request, "apiCode");
        checkRequiredField(request, "param");
    }

    private String extractRequiredField(JSONObject request, String field) {
        if (!request.containsKey(field)) {
            throw new ApiException("缺少必要字段: " + field);
        }
        return request.getString(field);
    }

    private void checkRequiredField(JSONObject request, String field) {
        if (!request.containsKey(field)) {
            throw new ApiException("缺少必要字段: " + field);
        }
    }
}