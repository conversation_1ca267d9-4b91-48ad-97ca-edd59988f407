package com.telecom.nrm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.OTNDao;
import com.telecom.nrm.domain.IdcOperationLogBO;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.entity.*;
import com.telecom.nrm.service.AuditService;
import com.telecom.nrm.service.IdcAuditService;
import com.telecom.nrm.service.ProductLinkAnalysisCoreService;
import com.telecom.nrm.service.DocumentExportService;
import com.telecom.nrm.utils.ExcelUtil;
import com.telecom.nrm.dto.ExportResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.activation.MimetypesFileTypeMap;
import javax.mail.internet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import javax.mail.*;

import com.telecom.nrm.utils.ExceptionUtil;

@Service
@Slf4j
public class AuditServiceImpl implements AuditService {
    private final Logger logger = LoggerFactory.getLogger(AuditServiceImpl.class);

    static Integer pageSize = 1000;

    @Autowired
    OTNDao otnDao;

    @Autowired
    IdcAuditService idcAuditService;

    private final static String queryRecentInfoForIdcTaskUrl = "http://nrm.oss.telecomjs.com:39049/daas-gray-api/interface?api_code=" +
            "queryRecentInfoForIdcTask&version=V20250327104600760&sharding_code=ds_idc_dsm";

    private final static String queryIdcTaskInfoByNameUrl = "http://nrm.oss.telecomjs.com:39049/daas-gray-api/interface?api_code=" +
            "queryIdcTaskInfoByName&version=V20250327151801937&sharding_code=ds_idc_dsm";

    private final static Map<String, String> IDC_TASK_MAP = new LinkedHashMap<>();
    static {
        IDC_TASK_MAP.put("upLoadNetWorkDeviceAll2SFTP", "按日上传设备清单任务");
        IDC_TASK_MAP.put("saveDeviceLogsForFtp", "按日解析FTP的设备操作日志任务");
        IDC_TASK_MAP.put("portIdentificationTask", "定时识别用户意图任务");
        IDC_TASK_MAP.put("doAuditDailyTask", "定时IDC端口操作稽核任务");
        IDC_TASK_MAP.put("reviewAuditDailyTask", "定时IDC端口操作稽核复核任务");
    }

    @Override
    public List<PmProductLinkAnalysis> selectAll() {
        List<PmProductLinkAnalysis> result = new ArrayList<>();
        for (ShardingEnum value : ShardingEnum.values()) {
            String bcCode = value.getBcShardingCode();
            if (!"-1".equals(bcCode)) {
                List<PmProductLinkAnalysis> list = selectByRegion(bcCode);
                if (CollectionUtils.isNotEmpty(list)) {
                    if (CollectionUtils.isNotEmpty(list)) {
                        result.addAll(list);
                        this.batchInsertPmProductLinkAnalysis(list);
                    }
                }
            }
        }
        return result;
    }

    @Autowired
    ProductLinkAnalysisCoreService productLinkAnalysisCoreService;

    @Autowired
    DocumentExportService documentExportService;

    @Override
    public void autoAnalysis() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("checkState", "I");
        PageResponse<PmProductLinkAnalysis> result = otnDao.select_pm_product_link_analysis_by_condition(jsonObject
                , 10, 1, "ds_graph_js");
        if (null != result && CollectionUtils.isNotEmpty(result.getData())) {
            List<PmProductLinkAnalysis> analyses = result.getData();
            for (PmProductLinkAnalysis analysis : analyses) {
                try {
                    productLinkAnalysisCoreService.analysis(analysis);
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }

            }
        }
    }

    @Override
    public void batchInsertPmProductLinkAnalysis(List<PmProductLinkAnalysis> pmProductLinkAnalyses) {
        if (CollectionUtils.isEmpty(pmProductLinkAnalyses)) return;
        for (PmProductLinkAnalysis pmProductLinkAnalysis : pmProductLinkAnalyses) {
            BigDecimal regionID = pmProductLinkAnalysis.getRegionId();
            if (null != regionID) {
                ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(regionID.toString());
                if (null != shardingEnum) pmProductLinkAnalysis.setRegionName(shardingEnum.getRegionName());
            }

        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", pmProductLinkAnalyses);
        otnDao.save_batch_pm_product_link_analysis(jsonObject, "ds_graph_js");
    }

    @Override
    public List<PmProductLinkAnalysis> selectByRegion(String bcCode) {
        List<PmProductLinkAnalysis> dataList = new ArrayList<>();
        System.out.println("地市:"+bcCode+"开始处理....");
        PageResponse<PmProductLinkAnalysis> result = otnDao.select_all_audit_order(new JSONObject(), pageSize, 1, bcCode);
        System.out.println("地市:"+bcCode+"处理结果"+result.getData().size());
        if (result != null && CollectionUtils.isNotEmpty(result.getData())) {
            // 直接将第一页的数据添加到列表中
            dataList.addAll(result.getData());
            // 获取总页数
            int totalPage = result.getPageInfo() != null ? result.getPageInfo().getTotalPage() : 1;
            // 循环查询剩余的页面数据
            for (int currentPage = 2; currentPage <= totalPage; currentPage++) {
                PageResponse<PmProductLinkAnalysis> resultRec = otnDao.select_all_audit_order(new JSONObject(), pageSize, currentPage, bcCode);
                if (resultRec != null && CollectionUtils.isNotEmpty(resultRec.getData())) {
                    dataList.addAll(resultRec.getData());
                }
            }
        }
        return dataList;
    }

    @Override
    public void exportCommon(String json, HttpServletResponse response) throws IOException {
        List<PmProductLinkAnalysis> dataList = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        PageResponse<PmProductLinkAnalysis> result = otnDao.select_pm_product_link_analysis_by_condition(jsonObject
                , pageSize, 1, "ds_graph_js");
        if (result != null && CollectionUtils.isNotEmpty(result.getData())) {
            // 直接将第一页的数据添加到列表中
            dataList.addAll(result.getData());
            // 获取总页数
            int totalPage = result.getPageInfo() != null ? result.getPageInfo().getTotalPage() : 1;
            // 循环查询剩余的页面数据
            for (int currentPage = 2; currentPage <= totalPage; currentPage++) {
                PageResponse<PmProductLinkAnalysis> resultRec = otnDao.select_pm_product_link_analysis_by_condition(jsonObject
                        , pageSize, currentPage, "ds_graph_js");
                if (resultRec != null && CollectionUtils.isNotEmpty(resultRec.getData())) {
                    dataList.addAll(resultRec.getData());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<PmProductLinkAnalysisDTO> list = new ArrayList<>();
            for (PmProductLinkAnalysis pmProductLinkAnalysis : dataList) {
                PmProductLinkAnalysisDTO pmProductLinkAnalysisDTO = new PmProductLinkAnalysisDTO();
                BeanUtil.copyProperties(pmProductLinkAnalysis, pmProductLinkAnalysisDTO);
                if (pmProductLinkAnalysisDTO.getMsg() != null) {
                    if (pmProductLinkAnalysisDTO.getMsg().length() >=400) {
                        pmProductLinkAnalysisDTO.setMsg(pmProductLinkAnalysisDTO.getMsg().substring(0,400) + "......内容过多,更多可至页面查询");
                    }
                }
                list.add(pmProductLinkAnalysisDTO);
            }
            ExcelUtil.download2("稽核数据", list, response, PmProductLinkAnalysisDTO.class);
        } else {
            ExcelUtil.download2("稽核数据", new ArrayList<>(), response, PmProductLinkAnalysisDTO.class);
        }

    }

    @Override
    public ExportResponseDTO exportToDocumentSecurity(String json) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("🚀 [审计导出] 开始审计数据导出到文档安全系统");
            log.info("📋 [审计导出] 请求参数: {}", json);

            // 1. 查询审计数据
            log.info("🔍 [审计导出] 步骤1: 开始查询审计数据");
            List<PmProductLinkAnalysis> dataList = queryAuditData(json);
            log.info("📊 [审计导出] 步骤1完成: 查询到 {} 条审计数据", dataList.size());

            // 2. 转换数据格式
            log.info("🔄 [审计导出] 步骤2: 开始转换数据格式");
            List<Map<String, Object>> exportData = convertAuditData(dataList);
            log.info("✅ [审计导出] 步骤2完成: 转换了 {} 条数据记录", exportData.size());

            // 3. 定义列
            log.info("📝 [审计导出] 步骤3: 定义Excel列结构");
            List<String> columns = Arrays.asList(
                "接入号:accessCode",
                "地市:regionName",
                "产品类型:productType",
                "电路编号:linkCode",
                "CJ类型:cjtype",
                "基础校验:basicCheck",
                "增强校验:enhanceCheck",
                "更新时间:updateDate",
                "稽核状态:checkState",
                "稽核结果:msg"
            );
            log.info("📋 [审计导出] 步骤3完成: 定义了 {} 个列", columns.size());

            // 4. 使用通用导出服务
            log.info("📤 [审计导出] 步骤4: 开始调用文档导出服务");
            ExportResponseDTO result = documentExportService.exportToDocumentSecurity(
                exportData, columns, "审计数据", "审计数据导出",
                "审计管理", "/api/audit/export", "审计数据导出"
            );
            log.info("🎯 [审计导出] 步骤4完成: 文档导出服务调用结果 - 成功: {}", result.isSuccess());

            if (result.isSuccess()) {
                result.setDataCount(exportData.size());
                long endTime = System.currentTimeMillis();
                log.info("✅ [审计导出] 导出成功完成!");
                log.info("📁 [审计导出] 文件名: {}", result.getFileName());
                log.info("📊 [审计导出] 数据量: {} 条", exportData.size());
                log.info("⏱️ [审计导出] 总耗时: {} ms", (endTime - startTime));
            } else {
                log.error("❌ [审计导出] 导出失败: {}", result.getMessage());
            }

            return result;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("💥 [审计导出] 导出异常，耗时: {} ms", (endTime - startTime));
            log.error("💥 [审计导出] 异常信息: {}", e.getMessage(), e);
            return ExportResponseDTO.failure("导出失败: " + e.getMessage());
        }
    }

    /**
     * 查询审计数据
     */
    private List<PmProductLinkAnalysis> queryAuditData(String json) {
        log.info("🔍 [审计查询] 开始解析查询参数");
        List<PmProductLinkAnalysis> dataList = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        log.info("📋 [审计查询] 解析后的查询条件: {}", jsonObject);

        log.info("🔍 [审计查询] 开始查询第1页数据，每页 {} 条", pageSize);
        PageResponse<PmProductLinkAnalysis> result = otnDao.export_pm_product_link_analysis_by_condition(jsonObject
                , pageSize, 1, "ds_graph_js");

        if (result != null && CollectionUtils.isNotEmpty(result.getData())) {
            // 直接将第一页的数据添加到列表中
            dataList.addAll(result.getData());
            log.info("📊 [审计查询] 第1页查询完成，获得 {} 条数据", result.getData().size());

            // 获取总页数
            int totalPage = result.getPageInfo() != null ? result.getPageInfo().getTotalPage() : 1;
            long totalCount = result.getPageInfo() != null ? result.getPageInfo().getTotalCount() : 0;
            log.info("📈 [审计查询] 总页数: {}, 总记录数: {}", totalPage, totalCount);

            // 循环查询剩余的页面数据
            for (int currentPage = 2; currentPage <= totalPage; currentPage++) {
                log.info("🔍 [审计查询] 开始查询第 {} 页数据", currentPage);
                PageResponse<PmProductLinkAnalysis> resultRec = otnDao.export_pm_product_link_analysis_by_condition(jsonObject
                        , pageSize, currentPage, "ds_graph_js");
                if (resultRec != null && CollectionUtils.isNotEmpty(resultRec.getData())) {
                    dataList.addAll(resultRec.getData());
                    log.info("📊 [审计查询] 第 {} 页查询完成，获得 {} 条数据", currentPage, resultRec.getData().size());
                } else {
                    log.warn("⚠️ [审计查询] 第 {} 页查询结果为空", currentPage);
                }
            }
        } else {
            log.warn("⚠️ [审计查询] 第1页查询结果为空或null");
        }

        log.info("✅ [审计查询] 查询完成，总共获得 {} 条审计数据", dataList.size());
        return dataList;
    }

    /**
     * 转换审计数据格式
     */
    private List<Map<String, Object>> convertAuditData(List<PmProductLinkAnalysis> dataList) {
        log.info("🔄 [数据转换] 开始转换审计数据格式，输入数据量: {}", dataList.size());
        List<Map<String, Object>> data = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(dataList)) {
            int processedCount = 0;
            int truncatedMsgCount = 0;

            for (PmProductLinkAnalysis pmProductLinkAnalysis : dataList) {
                try {
                    PmProductLinkAnalysisDTO pmProductLinkAnalysisDTO = new PmProductLinkAnalysisDTO();
                    BeanUtil.copyProperties(pmProductLinkAnalysis, pmProductLinkAnalysisDTO);

                    // 处理过长的稽核结果信息
                    if (pmProductLinkAnalysisDTO.getMsg() != null) {
                        if (pmProductLinkAnalysisDTO.getMsg().length() >= 400) {
                            pmProductLinkAnalysisDTO.setMsg(pmProductLinkAnalysisDTO.getMsg().substring(0, 400) + "......内容过多,更多可至页面查询");
                            truncatedMsgCount++;
                        }
                    }

                    Map<String, Object> row = new HashMap<>();
                    row.put("accessCode", pmProductLinkAnalysisDTO.getAccessCode());
                    row.put("regionName", pmProductLinkAnalysisDTO.getRegionName());
                    row.put("productType", pmProductLinkAnalysisDTO.getProductType());
                    row.put("linkCode", pmProductLinkAnalysisDTO.getLinkCode());
                    row.put("cjtype", pmProductLinkAnalysisDTO.getCjtype());
                    row.put("basicCheck", pmProductLinkAnalysisDTO.getBasicCheck());
                    row.put("enhanceCheck", pmProductLinkAnalysisDTO.getEnhanceCheck());
                    row.put("updateDate", pmProductLinkAnalysisDTO.getUpdateDate());
                    row.put("checkState", pmProductLinkAnalysisDTO.getCheckState());
                    row.put("msg", pmProductLinkAnalysisDTO.getMsg());

                    data.add(row);
                    processedCount++;

                    // 每处理1000条记录输出一次进度
                    if (processedCount % 1000 == 0) {
                        log.info("📊 [数据转换] 已处理 {} / {} 条记录", processedCount, dataList.size());
                    }

                } catch (Exception e) {
                    log.error("❌ [数据转换] 转换第 {} 条记录时出错: {}", processedCount + 1, e.getMessage());
                    log.error("❌ [数据转换] 出错的数据: {}", pmProductLinkAnalysis);
                }
            }

            log.info("✅ [数据转换] 转换完成");
            log.info("📊 [数据转换] 成功转换: {} 条记录", processedCount);
            log.info("✂️ [数据转换] 截取过长消息: {} 条记录", truncatedMsgCount);
        } else {
            log.warn("⚠️ [数据转换] 输入数据为空，无需转换");
        }

        log.info("🎯 [数据转换] 最终输出数据量: {}", data.size());
        return data;
    }

    public JSONObject queryOperationAuditCount(JSONObject param) throws Exception {
        param.put("startDate", param.getString("开始时间"));
        param.put("endDate", param.getString("截至时间"));
        param.put("regionNames", param.getJSONArray("地市"));
        param.put("portName", param.getString("端口名称"));
        param.put("deviceIp", param.getString("设备IP"));
        param.put("userName", param.getString("用户名"));
        param.put("auditStatus", 1);
        param.put("offset", 0);
        PageResponse<PmPortNetworkOperationAudit> operationAuditInfo = otnDao.queryOperationAuditInfo(param, 10000, 1,
                "ds_idc_js");
        // 获取稽核规则列表，返回去重的风险级别和风险分类
        PageResponse<JSONObject> risks = this.otnDao.queryRiskDescrition(param, 10000, 1, "ds_idc_js");
        List<JSONObject> riskViewData = risks.getData();
        Map<String,Integer> riskTypeStatistic = new HashMap<>();

        List<PmPortNetworkOperationAudit> operationAuditList = operationAuditInfo.getData();

        int totalCount = 0;
        int openPortCount = 0;
        int closePortCount = 0;
        int lowRiskCount = 0;
        int highRiskCount = 0;
        int midRiskCount = 0;
        int noRiskCount = 0;
        int notBuildByOrderCount = 0;
        int noOrderBuildCount = 0;
        if (CollectionUtils.isNotEmpty(operationAuditList)) {
            for (PmPortNetworkOperationAudit pmPortNetworkOperationAudit : operationAuditList) {
                totalCount++;
                if ("打开端口".equals(pmPortNetworkOperationAudit.getOperationType())) {
                    openPortCount++;
                }
                if ("关闭端口".equals(pmPortNetworkOperationAudit.getOperationType())) {
                    closePortCount++;
                }
                if ("低".equals(pmPortNetworkOperationAudit.getRiskLevel())) {
                    lowRiskCount++;
                }
                if ("高".equals(pmPortNetworkOperationAudit.getRiskLevel())) {
                    highRiskCount++;
                }
                if ("中".equals(pmPortNetworkOperationAudit.getRiskLevel())) {
                    midRiskCount++;
                }
                if ("按单施工".equals(pmPortNetworkOperationAudit.getRiskResult())) {
                    noRiskCount++;
                }
                if ("未按单施工".equals(pmPortNetworkOperationAudit.getRiskResult())) {
                    notBuildByOrderCount++;
                }
                if ("无工单施工".equals(pmPortNetworkOperationAudit.getRiskResult())) {
                    noOrderBuildCount++;
                }
                if (StringUtils.isNotEmpty(pmPortNetworkOperationAudit.getDefineRiskType() )) {
                    if (!riskTypeStatistic.containsKey(pmPortNetworkOperationAudit.getDefineRiskType() )) {
                        riskTypeStatistic.put(pmPortNetworkOperationAudit.getDefineRiskType() ,0);
                    }
                    int riskTypeCount = MapUtils.getInteger(riskTypeStatistic, pmPortNetworkOperationAudit.getDefineRiskType(), 0 )  + 1;
                    riskTypeStatistic.put(pmPortNetworkOperationAudit.getDefineRiskType() ,riskTypeCount);
                }

            }
        }
        List<JSONObject> retRiskViewData = new ArrayList<>();
        for (int i = 0; i < riskViewData.size(); i++) {
            String riskType = riskViewData.get(i).getString("riskAnalysis");
            String risk = riskViewData.get(i).getString("risk");
            riskViewData.get(i).put("count", MapUtils.getInteger(riskTypeStatistic,riskType,0));
            //不为0的再展示
            if (0 != MapUtils.getInteger(riskTypeStatistic,riskType,0) && !"无风险".equals(risk)) {
                retRiskViewData.add(riskViewData.get(i));
            }
        }
        JSONObject result = new JSONObject();
        result.put("totalCount", totalCount);
        result.put("openPortCount", openPortCount);
        result.put("closePortCount", closePortCount);
        result.put("lowRiskCount", lowRiskCount);
        result.put("highRiskCount", highRiskCount);
        result.put("midRiskCount", midRiskCount);
        result.put("noRiskCount", noRiskCount);
        result.put("notBuildByOrderCount", notBuildByOrderCount);
        result.put("noOrderBuildCount", noOrderBuildCount);
        result.put("riskViewData",retRiskViewData);
        return result;
    }

    public List<JSONObject> queryOperationAuditDetail(JSONObject param, boolean needPage) throws Exception {
        param.put("startDate", param.getString("开始时间"));
        param.put("endDate", param.getString("截至时间"));
        param.put("regionNames", param.getJSONArray("地市"));
        param.put("portName", param.getString("端口名称"));
        param.put("deviceIp", param.getString("设备IP"));
        param.put("userName", param.getString("用户名"));
        param.put("auditStatus", 1);
        param.put("offset", 0);
        PageResponse<PmPortNetworkOperationAudit> operationAuditInfo = otnDao.queryOperationAuditInfo(param, 10000, 1,
                "ds_idc_js");
        List<PmPortNetworkOperationAudit> operationAuditList = operationAuditInfo.getData();

        List<JSONObject> result = new ArrayList<>();
        //要求过滤无风险数据
        List<PmPortNetworkOperationAudit> pmPortNetworkOperationAuditLists = operationAuditList.stream().filter(o -> !"无风险".equals(o.getRisk())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pmPortNetworkOperationAuditLists)) {
            //分组
            Map<Integer, List<PmPortNetworkOperationAudit>> operationAuditMap = pmPortNetworkOperationAuditLists.stream().
                    collect(Collectors.groupingBy(audit -> audit.getGroupId() != null ? audit.getGroupId() : -1));

            for (Map.Entry<Integer, List<PmPortNetworkOperationAudit>> entry : operationAuditMap.entrySet()) {
                Integer groupId = entry.getKey();
                List<PmPortNetworkOperationAudit> list = entry.getValue();

                List<JSONObject> resList = this.createResOperationAuditList(list, needPage);
                
                JSONObject obj = new JSONObject();
                obj.put("riskAnalysis", list.get(0).getRiskAnalysis());
                obj.put("riskDesc", list.get(0).getDesc());
                obj.put("riskResult", list.get(0).getRiskResult());
                obj.put("riskLevel", list.get(0).getRiskLevel());
                obj.put("suggest", list.get(0).getSuggest());
                obj.put("groupId", groupId);
                obj.put("count", list.size());
                obj.put("detail", resList);
                if ("低".equals(list.get(0).getRiskLevel())) {
                    obj.put("seq", 2);
                } else if ("中".equals(list.get(0).getRiskLevel())) {
                    obj.put("seq", 3);
                } else if ("高".equals(list.get(0).getRiskLevel())) {
                    obj.put("seq", 4);
                } else {
                    obj.put("seq", 1);
                }
                result.add(obj);
            }

            //排序
            result.sort(Comparator.comparing((JSONObject obj) -> obj.getInteger("seq")).reversed());
        }

        return result;
    }

    private List<JSONObject> createResOperationAuditList(List<PmPortNetworkOperationAudit> list, boolean needPage) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //分页
        if (needPage) {
            list = list.stream().skip(0).limit(10).collect(Collectors.toList());
        }

        List<JSONObject> resList = new ArrayList<>();
        for (PmPortNetworkOperationAudit pmPortNetworkOperationAudit : list) {
            JSONObject obj = new JSONObject();
            obj.put("regionName", pmPortNetworkOperationAudit.getRegionName());
            obj.put("deviceIp", pmPortNetworkOperationAudit.getDeviceIp());
            obj.put("portName", pmPortNetworkOperationAudit.getPortName());
            obj.put("operationName", pmPortNetworkOperationAudit.getOperatorName());
            obj.put("deptName", pmPortNetworkOperationAudit.getOperatorFullDept());
            obj.put("operationType", pmPortNetworkOperationAudit.getOperationType());
            if (pmPortNetworkOperationAudit.getOperationDate() != null) {
                obj.put("operationDate", sdf.format(pmPortNetworkOperationAudit.getOperationDate()));
            }
            obj.put("reviewStatusDesc", pmPortNetworkOperationAudit.getReviewStatusDesc());
            obj.put("reviewRisk", pmPortNetworkOperationAudit.getReviewRisk());
            obj.put("portStatus", pmPortNetworkOperationAudit.getPortStatus());
            obj.put("crmCustOrderCode", pmPortNetworkOperationAudit.getCrmCustOrderCode());
            obj.put("psoType", pmPortNetworkOperationAudit.getPsoType());
            obj.put("dispatchStatus", pmPortNetworkOperationAudit.getDispatchStatus());
            obj.put("opAuditRemark", pmPortNetworkOperationAudit.getOpAuditRemark());
            obj.put("id", String.valueOf(pmPortNetworkOperationAudit.getId()));
            obj.put("busiAnalysis", pmPortNetworkOperationAudit.getBusiAnalysis());
            obj.put("itAnalysis", pmPortNetworkOperationAudit.getItAnalysis());
            obj.put("reviewDate", pmPortNetworkOperationAudit.getReviewDate()==null? "":sdf.format(pmPortNetworkOperationAudit.getReviewDate()));
            obj.put("auditDate", pmPortNetworkOperationAudit.getAuditDate()==null?"":sdf.format(pmPortNetworkOperationAudit.getAuditDate()));
            obj.put("operatorRegionName", pmPortNetworkOperationAudit.getOperatorRegionName());
            obj.put("reviewStatus", pmPortNetworkOperationAudit.getReviewStatus());
            obj.put("reviewPsoType", pmPortNetworkOperationAudit.getReviewPsoType());
            if ("有风险".equals(pmPortNetworkOperationAudit.getReviewRisk())) {
                obj.put("reviewRiskAnalysis", pmPortNetworkOperationAudit.getReviewRiskAnalysis());
            }
            //判断风险消除原因，有风险才会有
            if ("有风险".equals(pmPortNetworkOperationAudit.getRisk()) && pmPortNetworkOperationAudit.getReviewStatus()!=null) {
                if ("打开端口".equals(pmPortNetworkOperationAudit.getOperationType()) && "关闭端口".equals(pmPortNetworkOperationAudit.getReviewOperationType())) {
                    obj.put("reviewCleanResult", "关闭端口消除风险");
                } else if ("1".equals(String.valueOf(pmPortNetworkOperationAudit.getReviewStatus()))) {
                    //复核通过
                    String psoType = StringUtils.isNotEmpty(pmPortNetworkOperationAudit.getReviewPsoType())?pmPortNetworkOperationAudit.getReviewPsoType():pmPortNetworkOperationAudit.getPsoType();
                    if (StringUtils.isEmpty(psoType)) {
                        obj.put("reviewCleanResult", "复核结果为低风险，无需再次复核");
                    } else {
                        switch (psoType) {
                            case "割接":
                                obj.put("reviewCleanResult", "补割接单消除风险");
                                break;
                            case "中继开通":
                                obj.put("reviewCleanResult", "补中继单消除风险");
                                break;
                            case "中继端口释放":
                                obj.put("reviewCleanResult", "补中继单消除风险");
                                break;
                            default:
                                obj.put("reviewCleanResult", "补开通单消除风险");
                                break;

                        }
                    }
                } else if ("-1".equals(String.valueOf(pmPortNetworkOperationAudit.getReviewStatus()))) {
                    //无需复核
                    obj.put("reviewCleanResult", "低风险无需复核");
                }
            }
            resList.add(obj);
        }

        return resList;
    }

    public List<JSONObject> queryOperationAuditDetailPage(JSONObject param) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<JSONObject> resList = new ArrayList<>();

        param.put("startDate", param.getString("开始时间"));
        param.put("endDate", param.getString("截至时间"));
        param.put("regionNames", param.getJSONArray("地市"));
        param.put("portName", param.getString("端口名称"));
        param.put("deviceIp", param.getString("设备IP"));
        param.put("userName", param.getString("用户名"));
        param.put("auditStatus", 1);
        int offset = (param.getInteger("currentPage") - 1) * param.getInteger("pageSize");
        param.put("offset", offset);

        PageResponse<PmPortNetworkOperationAudit> operationAuditInfo = otnDao.queryOperationAuditInfo(param, param.getInteger("pageSize"),
                param.getInteger("currentPage"), "ds_idc_js");
        List<PmPortNetworkOperationAudit> operationAuditList = operationAuditInfo.getData();
        for (PmPortNetworkOperationAudit pmPortNetworkOperationAudit : operationAuditList) {
            JSONObject obj = new JSONObject();

            obj.put("regionName", pmPortNetworkOperationAudit.getRegionName());
            obj.put("deviceIp", pmPortNetworkOperationAudit.getDeviceIp());
            obj.put("portName", pmPortNetworkOperationAudit.getPortName());
            obj.put("operationName", pmPortNetworkOperationAudit.getOperatorName());
            obj.put("deptName", pmPortNetworkOperationAudit.getOperatorFullDept());
            obj.put("operationType", pmPortNetworkOperationAudit.getOperationType());
            if (pmPortNetworkOperationAudit.getOperationDate() != null) {
                obj.put("operationDate", sdf.format(pmPortNetworkOperationAudit.getOperationDate()));
            }
            obj.put("reviewStatusDesc", pmPortNetworkOperationAudit.getReviewStatusDesc());
            obj.put("reviewRisk", pmPortNetworkOperationAudit.getReviewRisk());
            resList.add(obj);
        }

        return resList;
    }

    public List<JSONObject> queryIdcTaskMonitorInfo() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<JSONObject> list = new ArrayList<>();

        String res = this.executeDaasGrayApi(queryRecentInfoForIdcTaskUrl, new JSONObject());
        logger.info("查询最近IDC任务信息返回：" + res);

        JSONObject resultObj = JSON.parseObject(res);
        if ("0000".equals(resultObj.getString("resultCode"))) {
            JSONObject response = resultObj.getJSONObject("response");
            if (response != null && response.size() > 0) {
                JSONArray data = response.getJSONArray("data");
                if (CollectionUtils.isNotEmpty(data)) {
                    List<IdcOperationLogBO> idcOperationLogList = JSON.parseArray(data.toString(), IdcOperationLogBO.class);

                    //分组
                    Map<String, List<IdcOperationLogBO>> idcOperationLogMap = idcOperationLogList.stream().
                            collect(Collectors.groupingBy(IdcOperationLogBO::getOperationName));

                    for (Map.Entry<String, List<IdcOperationLogBO>> entry : idcOperationLogMap.entrySet()) {
                        String key = entry.getKey();
                        List<IdcOperationLogBO> value = entry.getValue();

                        Date operationTime = null;
                        String result = null;
                        String errorMsg = null;
                        for (IdcOperationLogBO idcOperationLogBO : value) {
                            if ("执行开始".equals(idcOperationLogBO.getOperationDescription())) {
                                operationTime = idcOperationLogBO.getOperationDate();
                            }
                            if ("执行异常".equals(idcOperationLogBO.getOperationDescription())) {
                                errorMsg = idcOperationLogBO.getExceptionMessage();
                            }
                        }
                        if (StringUtils.isNotEmpty(errorMsg)) {
                            result = "异常";
                        } else {
                            result = "成功";
                        }

                        JSONObject obj = new JSONObject();
                        obj.put("taskName", IDC_TASK_MAP.get(key));
                        if (operationTime != null) {
                            obj.put("operationTime", sdf.format(operationTime));
                        }
                        obj.put("result", result);
                        obj.put("errorMsg", errorMsg);
                        list.add(obj);
                    }
                }
            }
        }
        return list;
    }

    private List<JSONObject> resoveIdcTaskLog(String res) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<JSONObject> list = new ArrayList<>();

        JSONObject resultObj = JSON.parseObject(res);
        if ("0000".equals(resultObj.getString("resultCode"))) {
            JSONObject response = resultObj.getJSONObject("response");
            if (response != null && response.size() > 0) {
                JSONArray data = response.getJSONArray("data");
                if (CollectionUtils.isNotEmpty(data)) {
                    List<IdcOperationLogBO> idcOperationLogList = JSON.parseArray(data.toString(), IdcOperationLogBO.class);
                    for (IdcOperationLogBO idcOperationLogBO : idcOperationLogList) {
                        if (StringUtils.isEmpty(idcOperationLogBO.getTraceId())) {
                            idcOperationLogBO.setTraceId("1");
                        }
                    }

                    //分组
                    Map<String, List<IdcOperationLogBO>> idcOperationLogMap = idcOperationLogList.stream().
                            collect(Collectors.groupingBy(IdcOperationLogBO::getTraceId));

                    for (Map.Entry<String, List<IdcOperationLogBO>> entry : idcOperationLogMap.entrySet()) {
                        List<IdcOperationLogBO> value = entry.getValue();

                        Date startTime = null;
                        Date endTime = null;
                        Long intervalTime = null;
                        for (IdcOperationLogBO idcOperationLogBO : value) {
                            if ("执行开始".equals(idcOperationLogBO.getOperationDescription())) {
                                startTime = idcOperationLogBO.getOperationDate();
                            }
                            if ("执行结束".equals(idcOperationLogBO.getOperationDescription())) {
                                endTime = idcOperationLogBO.getOperationDate();
                            }
                        }

                        if (startTime != null && endTime != null) {
                            intervalTime = (endTime.getTime() - startTime.getTime());
                        }

                        JSONObject obj = new JSONObject();
                        if (startTime != null) {
                            obj.put("startTime", sdf.format(startTime));
                        }
                        obj.put("intervalTime", intervalTime);
                        obj.put("traceId", entry.getKey());
                        list.add(obj);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(list)) {
            list.sort(Comparator.comparing((JSONObject obj) -> obj.getDate("startTime")));
        }

        return list;
    }

    public List<JSONObject> queryIdcTaskForSaveDeviceLogs() throws Exception {
        JSONObject param = new JSONObject();
        param.put("operationName", "saveDeviceLogsForFtp");
        String res = this.executeDaasGrayApi(queryIdcTaskInfoByNameUrl, param);
        logger.info("按日解析FTP的设备操作日志任务信息返回：" + res);

        List<JSONObject> list = this.resoveIdcTaskLog(res);

        return list;
    }

    public List<JSONObject> queryIdcTaskForPortIdentification() throws Exception {
        JSONObject param = new JSONObject();
        param.put("operationName", "portIdentificationTask");
        String res = this.executeDaasGrayApi(queryIdcTaskInfoByNameUrl, param);
        logger.info("定时识别用户意图任务信息返回：" + res);

        List<JSONObject> list = this.resoveIdcTaskLog(res);

        return list;
    }

    public List<JSONObject> queryIdcTaskForAuditDaily() throws Exception {
        JSONObject param = new JSONObject();
        param.put("operationName", "doAuditDailyTask");
        String res = this.executeDaasGrayApi(queryIdcTaskInfoByNameUrl, param);
        logger.info("定时IDC端口操作稽核任务信息返回：" + res);

        List<JSONObject> list = this.resoveIdcTaskLog(res);

        return list;
    }

    private String executeDaasGrayApi(String url, JSONObject jsonParams) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String result = "";
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(180 * 1000)
                .setConnectionRequestTimeout(180 * 1000).setSocketTimeout(180 * 1000).setRedirectsEnabled(true).build();
        httpPost.setConfig(requestConfig);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("app_id", "net_resource_integration");
        try {
            httpPost.setEntity(new StringEntity(jsonParams.toJSONString(), ContentType.create("application/json", "utf-8")));
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                result = EntityUtils.toString(response.getEntity());
                return result;
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("resultCode", "0001");
                jsonObject.put("resultMsg", "接口返回失败");
                return jsonObject.toJSONString();
            }
        } catch (Exception e) {
            logger.error("接口调用异常", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("resultCode", "0001");
            jsonObject.put("resultMsg", e.toString());
            return jsonObject.toJSONString();
        } finally {
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public List<IdcAuditRiskDictvalue> queryAuditRiskDictvaluePage() throws Exception {
        List<IdcAuditRiskDictvalue> idcAuditRiskDictvalues = new ArrayList<>();

        JSONObject param = new JSONObject();
        param.put("pageSize", 100);
        param.put("offset", 0);
        JSONObject res = otnDao.queryAuditRiskDictvaluePage(param, "ds_idc_js");

        int count = res.getJSONArray("count").getJSONObject(0).getInteger("count");
        JSONArray data = res.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(data)) {
            idcAuditRiskDictvalues = JSON.parseArray(data.toString(), IdcAuditRiskDictvalue.class);
            for (IdcAuditRiskDictvalue idcAuditRiskDictvalue : idcAuditRiskDictvalues) {
                if (idcAuditRiskDictvalue.getExistOrder() != null) {
                    if (idcAuditRiskDictvalue.getExistOrder() == 0) {
                        idcAuditRiskDictvalue.setExistOrderDesc("无订单");
                    } else if (idcAuditRiskDictvalue.getExistOrder() == 1) {
                        idcAuditRiskDictvalue.setExistOrderDesc("有订单");
                    }
                }

                if (idcAuditRiskDictvalue.getRoleId() != null) {
                    if ("10100000611".equals(idcAuditRiskDictvalue.getRoleId().toString())) {
                        idcAuditRiskDictvalue.setRoleDesc("A");
                    } else if ("10100000612".equals(idcAuditRiskDictvalue.getRoleId().toString())) {
                        idcAuditRiskDictvalue.setRoleDesc("Z");
                    }
                }
            }
        }

        return idcAuditRiskDictvalues;
    }

    public List<IdcAuditRiskRule> queryRiskRuleById(BigDecimal riskId) throws Exception {
        List<IdcAuditRiskRule> result = new ArrayList<>();

        JSONObject param = new JSONObject();
        param.put("pageSize", 100);
        param.put("riskId", riskId);
        JSONObject res = otnDao.queryRiskRuleById(param, "ds_idc_js");
        JSONArray data = res.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(data)) {
            result = JSON.parseArray(data.toString(), IdcAuditRiskRule.class);
        }

        return result;
    }

    private JSONObject createStartEndDate() {
        LocalDate date = LocalDate.now();
        DayOfWeek day = date.getDayOfWeek();

        LocalDate start = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate end = date;

        JSONObject json = new JSONObject();
        json.put("start", start);
        json.put("end", end);
        return json;
    }

    public void sendAuditMailMessage() throws Exception {
        JSONObject dateJson = this.createStartEndDate();
        logger.info("sendAuditMailMessage：" + dateJson.toJSONString());

        //获取附件
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("开始时间", dateJson.getString("start") + " 00:00:00");
        jsonObject.put("截至时间", dateJson.getString("end") + " 23:59:59");
        Map<String, Object> mailMap = this.getAttachmentForAuditMail(jsonObject);
        List<File> files = (List<File>) mailMap.get("files");
        String html = mailMap.get("html") == null ? null : String.valueOf(mailMap.get("html"));

        //发送邮件
        JSONObject param = new JSONObject();
        JSONArray recipients = new JSONArray(Arrays.asList(new String[]{"<EMAIL>", "<EMAIL>"}));
        JSONArray ccList = new JSONArray(Arrays.asList(new String[]{"<EMAIL>","<EMAIL>"}));
        param.put("host", "smtp.chinatelecom.cn");
        param.put("port", 465);
        param.put("username", "<EMAIL>");
        param.put("password", "9nW@vb4@hN5b!f)$");
        param.put("recipients", recipients);
        param.put("ccList", ccList);
        param.put("subject", "IDC智审试运行");
        param.put("text", "IDC智审试运行");
        param.put("html", html);
        String result = this.sendMailMessage(param, files);

        //写入 idc_audit_email_report
        IdcAuditEmailReport addEmail = new IdcAuditEmailReport();
        addEmail.setTitle("IDC设备操作稽核报告");
        if (!"success".equals(result)) {
            addEmail.setState(-1);
        } else {
            addEmail.setState(1);
        }
        otnDao.insertAuditEmailReport(addEmail, "ds_idc_js");

        if (!"success".equals(result)) {
            throw new RuntimeException(result);
        }
    }

    private Map<String, Object> getAttachmentForAuditMail(JSONObject jsonObject) throws Exception {
        List<File> files = new ArrayList<>();
        File file = null;

        JSONObject statisticCount = this.queryOperationAuditCount(jsonObject);
        List<JSONObject> auditDetails = this.queryOperationAuditDetail(jsonObject, false);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmsssss");

        //附件2
        String excelFileName = "IDC智审稽核详单" + sdf.format(new Date()) + ".xlsx";
        file = new File(excelFileName);
        //周报中的风险详情取所有的
        JSONObject param = new JSONObject();
        List<JSONObject> allAuditDetails = this.queryOperationAuditDetail(param, false);
        idcAuditService.weekExportDetailExcel(allAuditDetails, file);
        files.add(file);

        //附件1
        String fileName = "IDC设备操作稽核报告" + sdf.format(new Date()) + ".pdf";
        file = new File(fileName);
        String html = idcAuditService.weekExport(jsonObject, statisticCount, auditDetails, file, excelFileName);
        files.add(file);

        Map<String, Object> map = new HashMap<>();
        map.put("files", files);
        map.put("html", html);
        return map;
    }

    private String sendMailMessage(JSONObject param, List<File> files) {
        // 邮件配置
        String host = param.getString("host"); // SMTP服务器地址
        int port = param.getInteger("port"); // TLS端口
        String username = param.getString("username"); // 发件人邮箱
        String password = param.getString("password"); // 邮箱密码/应用专用密码
        JSONArray recipients = param.getJSONArray("recipients"); // 收件人邮箱
        JSONArray ccList = param.getJSONArray("ccList"); // 抄送人邮箱

        // 设置属性
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.put("mail.smtp.socketFactory.fallback", "false");
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        props.put("mail.smtp.ssl.trust", "*");
        props.put("mail.smtp.socketFactory.port", port);
        props.put("mail.smtp.host", host);
        System.setProperty("mail.mime.splitlongparameters", "false");

        // 创建Session对象
        Session session = Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });

        try {
            // 创建邮件对象
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username));
            message.setSubject(param.getString("subject"));

            //收件人
            InternetAddress[] recipientAddresses = new InternetAddress[recipients.size()];
            for (int i = 0; i < recipients.size(); i++) {
                String recipient = recipients.getString(i);
                recipientAddresses[i] = new InternetAddress(recipient);

            }
            message.setRecipients(Message.RecipientType.TO, recipientAddresses);

            //抄送人
            if (CollectionUtils.isNotEmpty(ccList)) {
                InternetAddress[] ccAddresses = new InternetAddress[ccList.size()];
                for (int i = 0; i < ccList.size(); i++) {
                    String cc = ccList.getString(i);
                    ccAddresses[i] = new InternetAddress(cc);

                }
                message.setRecipients(Message.RecipientType.CC, ccAddresses);
            }

            // 创建多部分内容（HTML + 纯文本备用）
            Multipart multipart = new MimeMultipart("alternative");

            //正文
            if (StringUtils.isNotEmpty(param.getString("text"))) {
                MimeBodyPart textPart = new MimeBodyPart();
                textPart.setText(param.getString("text"));
                multipart.addBodyPart(textPart);
            }

            if (StringUtils.isNotEmpty(param.getString("html"))) {
                MimeBodyPart htmlPart = new MimeBodyPart();
                htmlPart.setContent(param.getString("html"), "text/html; charset=utf-8");
                multipart.addBodyPart(htmlPart);
            }

            //附件
            if (CollectionUtils.isNotEmpty(files)) {
                for (File file : files) {
                    MimeBodyPart attachmentPart = new MimeBodyPart();
                    DataSource source = new FileDataSource(file);
                    attachmentPart.setDataHandler(new DataHandler(source));
                    attachmentPart.setFileName(MimeUtility.encodeText(source.getName()));
                    multipart.addBodyPart(attachmentPart);
                }
            }

            message.setContent(multipart);

            // 发送邮件
            Transport.send(message);
            return "success";
        } catch (Exception e) {
            logger.error("邮件发送异常", e);
            return ExceptionUtil.getExceptionMsg(e, 2000);
        } finally {
            if (CollectionUtils.isNotEmpty(files)) {
                for (File file : files) {
                    if (file != null && file.exists()) {
                        file.delete();
                    }
                }
            }
        }
    }
}
