package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.telecom.nrm.dao.IdcAuditDao;
import com.telecom.nrm.dao.NetConfigDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.AnalysisPortService;
import com.telecom.nrm.service.IdcAuditService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;

@Service
@Log4j2
public class AnalysisPortServiceImpl implements AnalysisPortService {

    @Autowired
    private IdcAuditDao idcAuditDao;

    @Autowired
    private NetConfigDao configDao;

    private static final ObjectMapper mapper = new ObjectMapper();


    @Async("graphRestPool")
    @Override
    public void groupAnalysisPortInfo(List<JSONObject> idcMachineList, String num) {
        //如果没有设备配置，或者没有idc的设备集合，则直接返回
        if (CollectionUtils.isEmpty(idcMachineList)) {
            return;
        }
        int i = 1;
        Map<String, String> successMap = new HashMap<>();
        Map<String, String> failMap = new HashMap<>();
        for (JSONObject needAnalysisJson : idcMachineList) {
            log.info("线程{}开始解析第{}个文件，共{}个文件", num, i, idcMachineList.size());
            i++;
            if (needAnalysisJson == null) {
                continue;
            }
            String path = needAnalysisJson.getString("filePath");
            String id = needAnalysisJson.getString("id");
            String deviceIp = needAnalysisJson.getString("deviceIp");
            try {
                //根据ID读取文件
                String contentStr = read(path);
                if (StringUtils.isEmpty(contentStr)) {
                    failMap.put(id, "文件为空");
                    continue;
                }
                //解析文件
                JSONObject content =JSONObject.parseObject(contentStr);
                String key = null;
                for (String contenKey : content.keySet()) {
                    if (contenKey.startsWith("IDC_Cai")) {
                        key = contenKey;
                    }
                }
                if (StringUtils.isEmpty(key)) {
                    failMap.put(id, "文件没有IDC_Cai开头的节点");
                    continue;
                }
                String result = content.getString(key);
                if (StringUtils.isEmpty(result)) {
                    failMap.put(id, "文件"+key+"节点中没有内容");
                    continue;
                }
                //解析整机配置中每个端口的配置速率
                List<Map<String, Object>> portInfos = analysisPortInfo(result, deviceIp);
                if (CollectionUtils.isNotEmpty(portInfos)) {
                    successMap.put(id, "解析成功");
                    //根据deviceip先把所有端口信息删除
                    JSONObject param = new JSONObject();
                    param.put("deviceIp", deviceIp);
                    param.put("list", portInfos);
                    idcAuditDao.deletePmGatherPortByDeviceIp(param, NRMConstants.IDC_DATA);
                    idcAuditDao.insertPmGatherPort(param, NRMConstants.IDC_DATA);
                } else {
                    successMap.put(id, "解析成功,但没有解析到端口");
                }
            } catch (Exception e) {
                failMap.put(id, "解析端口失败：" + e.getMessage());
            } finally {
                //删除文件
                delete(path);
            }
        }
        //解析完成后更新pm_init_gather_port表中的asalysisStatus字段
        List<JSONObject> updateList = new ArrayList<>();
        successMap.entrySet().stream().forEach(entry -> {
            JSONObject param = new JSONObject();
            param.put("id", entry.getKey());
            param.put("asalysisStatus", 1);
            param.put("asalysisResult", entry.getValue());
            updateList.add(param);
        });
        failMap.entrySet().stream().forEach(entry -> {
            JSONObject param = new JSONObject();
            param.put("id", entry.getKey());
            param.put("asalysisStatus", 2);
            param.put("asalysisResult", entry.getValue());
            updateList.add(param);
        });
        JSONObject param = new JSONObject();
        param.put("list", updateList);
        idcAuditDao.updateInitGatherPort(param, NRMConstants.SHARDING_CODE);
    }

    @Async("graphRestPool")
    @Override
    public void groupRateTemplate(List<JSONObject> idcMachineList, String num) {
        //如果没有设备配置，或者没有idc的设备集合，则直接返回
        if (CollectionUtils.isEmpty(idcMachineList)) {
            return;
        }
        int i = 1;
        Map<String, String> successMap = new HashMap<>();
        Map<String, String> failMap = new HashMap<>();
        for (JSONObject needAnalysisJson : idcMachineList) {
            log.info("线程{}开始解析第{}个文件，共{}个文件", num, i, idcMachineList.size());
            i++;
            if (needAnalysisJson == null) {
                continue;
            }
            String path = needAnalysisJson.getString("filePath");
            String id = needAnalysisJson.getString("id");
            String deviceIp = needAnalysisJson.getString("deviceIp");
            try {
                //根据ID读取文件
                String contentStr = read(path);
                if (StringUtils.isEmpty(contentStr)) {
                    failMap.put(id, "文件为空");
                    continue;
                }
                //解析文件
                JSONObject content =JSONObject.parseObject(contentStr);
                String key = null;
                for (String contenKey : content.keySet()) {
                    if (contenKey.startsWith("IDC_Cai")) {
                        key = contenKey;
                    }
                }
                if (StringUtils.isEmpty(key)) {
                    failMap.put(id, "文件没有IDC_Cai开头的节点");
                    continue;
                }
                String result = content.getString(key);
                if (StringUtils.isEmpty(result)) {
                    failMap.put(id, "文件"+key+"节点中没有内容");
                    continue;
                }
                //取出设备的厂商和型号
                JSONObject unitInfo = content.getJSONObject("UnitInfo");
                String vendor = unitInfo.getString("Vendor");
                String devModel = unitInfo.getString("DevModel");
                //是否思科设备，思科模板的速率单位是bit，和其他型号的设备不一致
                boolean isCisco = false;
                if ("CS".equals(vendor) && StringUtils.isNotEmpty(devModel) &&
                        (devModel.contains("2950") || devModel.contains("2960") || devModel.contains("3750") ||
                                devModel.contains("4506") || devModel.contains("7609") || devModel.contains("C3550") || devModel.contains("IDC_OUT_CRS"))) {
                    isCisco = true;
                }
                //解析整机配置中每个端口的配置速率
                List<Map<String, Object>> rateTemplates = analysisConfigRate(result, deviceIp, isCisco);
                if (CollectionUtils.isNotEmpty(rateTemplates)) {
                    successMap.put(id, "解析成功");
                    //根据oltip先把所有模板删除
                    JSONObject param = new JSONObject();
                    param.put("deviceIp", deviceIp);
                    param.put("list", rateTemplates);
                    idcAuditDao.deletePmDeviceRateTemplateByDeviceIp(param, NRMConstants.IDC_DATA);
                    idcAuditDao.insertPmDeviceRateTemplate(param, NRMConstants.IDC_DATA);
                } else {
                    successMap.put(id, "解析成功,但没有解析到速率模板");
                }
            } catch (Exception e) {
                failMap.put(id, "解析端口失败：" + e.getMessage());
            } finally {
                //删除文件
                delete(path);
            }
        }
        //解析完成后更新pm_init_gather_port表中的asalysisStatus字段
        List<JSONObject> updateList = new ArrayList<>();
        successMap.entrySet().stream().forEach(entry -> {
            JSONObject param = new JSONObject();
            param.put("id", entry.getKey());
            param.put("asalysisStatus", 1);
            param.put("asalysisResult", entry.getValue());
            updateList.add(param);
        });
        failMap.entrySet().stream().forEach(entry -> {
            JSONObject param = new JSONObject();
            param.put("id", entry.getKey());
            param.put("asalysisStatus", 2);
            param.put("asalysisResult", entry.getValue());
            updateList.add(param);
        });
        JSONObject param = new JSONObject();
        param.put("list", updateList);
        idcAuditDao.updateInitGatherPort(param, NRMConstants.SHARDING_CODE);
    }

    private String read(String path) {
        try {
            StringBuilder contentent = new StringBuilder();
            BufferedReader reader = new BufferedReader(new FileReader(path));
            String line;
            while ((line = reader.readLine()) != null) {
                contentent.append(line);
            }
            reader.close();
            return contentent.toString();
        } catch (IOException e) {
            log.error("读取文件失败：{}", e.getMessage());
        }
        try {
            String fileName = path.split("/")[1];
            String id = fileName.replace(".txt", "");
            if (fileName.contains("rate")) {
                id = fileName.replace("rate", "");
            }
            JSONObject getResult = configDao.getNetConfigFile(new BigDecimal(id), NRMConstants.SHARDING_CODE);
            return getResult.getString("content");
        } catch (Exception e) {
            log.error("读取ceph文件失败：{}", e.getMessage());
        }
        return null;
    }

    private void delete(String path) {
        try {
            File file = new File(path);
            file.delete();
        } catch (Exception e) {
            log.error("删除文件失败：{}", e.getMessage());
        }
    }

    public List<Map<String, Object>> analysisPortInfo(String data, String deviceIp) {
        String sysContent = "你是一名网络设备配置审核专家，能够从网络设备命令中分析出每个端口限制的速率和ip配置：\n" +
                "在网络设备的命令中，一般是通过interface xxx 进入端口，xxx为端口名称，在interface命令后操作的限速命令才对该端口生效，在interface命令前的限速命令请忽略。\n" +
                "进入端口后，限速一般会有如下命令：\n" +
                "1.命令样例：speed xxx 参数说明：xxx参数为限速的速率值；\n" +
                "2.命令样例：bandwidth xxx 参数说明：xxx参数为限速的速率值；\n" +
                "3.命令样例：qos apply policy xxx inbound 参数说明：xxx是入向的限速模板；\n" +
                "4.命令样例：qos apply policy xxx outbound 参数说明：xxx是出向的限速模板；\n" +
                "5.命令样例：qos lr outbound cir xxxx cbs xxxx 参数说明：cir后的xxxx是出向限速的速率值；\n" +
                "6.命令样例：qos lr inbound cir xxxx cbs xxxx 参数说明：cir后的xxxx是入向限速的速率值；\n" +
                "7.命令样例：qos car inbound xxx 参数说明：xxx为入向限速模板名称；\n" +
                "8.命令样例：qos car outbound xxx 参数说明：xxx为出向限速模板名称；\n" +
                "9.命令样例：qos lr cir xxxx cbs xxxx outbound 参数说明：cir后的xxxx是出向限速的速率值；\n" +
                "10.命令样例：qos lr cir xxxx cbs xxxx inbound 参数说明：cir后的xxxx是入向限速的速率值；\n" +
                "11.命令样例：traffic-policy  xxx inbound 参数说明：xxx为入向限速模板名称；\n" +
                "12.命令样例：traffic-policy  xxx outbound 参数说明：xxx为出向限速模板名称；\n" +
                "13.命令样例：qos-profile xxx outbound 参数说明：xxx为出向限速模板名称；\n" +
                "14.命令样例：qos-profile xxx inbound 参数说明：xxx为入向限速模板名称；\n" +
                "15.命令样例：line-rate outbound xxx 参数说明：xxx为出向限速的速率值；\n" +
                "16.命令样例：line-rate inbound xxx 参数说明：xxx为入向限速的速率值；\n" +
                "17.命令样例：service-policy input xxx 参数说明：xxx为入向限速模板名称；\n" +
                "18.命令样例：service-policy output xxx 参数说明：xxx为出向限速模板名称；\n" +
                "19.命令样例：traffic-limit inbound acl xx rule x cir xx pir xxx cbs xxxx pbs xxx 参数说明：cir后的xx为入向限速的速率值；\n" +
                "20.命令样例：traffic-limit outbound acl xx rule x cir xx pir xxx cbs xxxx pbs xxx 参数说明：cir后的xx为出向限速的速率值；\n" +
                "21.命令样例：traffic-limit rate-limit xxx 参数说明：xxx为限速的速率值；\n" +
                "22.命令样例：trate-limit input xxx 参数说明：xxx为入向限速的速率值；\n" +
                "23.命令样例：trate-limit output xxx 参数说明：xxx为出向限速的速率值；\n" +
                "24.命令样例：config service ies 序号 interface 序号 sap 端口名称:vlan ingress qos xxx 参数说明：xxx为限速的模板名称；\n" +
                "25.命令样例：traffic-limit 端口名称 rate-limit xxx 参数说明：xxx为限速的速率值。\n" +
                "请按照上述的限速命令样例，分析每个端口是否限速，取出每个端口限速的速率并按照规范返回。\n" +
                "端口ip配置样例：\n" +
                "1.命令样例：ip address xxx xxxx 参数说明：xxx为ip地址；xxxx为ip掩码\n" +
                "2.命令样例：ipv6 address xxx 参数说明：xxx为ipv6地址\n" +
                "返回格式：必须严格遵守。请用纯文本格式回答，仅是json数组格式，输出结果不要出现```json```，不要使用任何Markdown符号或代码块，每个对象包括:pn(端口名称),is(入向限速速率值，不是模板名称),os(出向限速速率值，不是模板名称),ist(入向限速速率模板)，ost(出向限速速率模板)，ip(ip地址),ipMask(ip地址掩码),ipv6(ipv6地址)。  \n" +
                "返回内容：不要出现```json!不要出现```json!不要出现```json!\n" +
                "端口名称：一般通过interface xxx(xxx为端口名称)命令进入端口视图,有这个命令的端口名称都要返回，不要返回日志中不存在的端口名称，输出的端口遵循以下规则：如果端口名称有多个空格，去除第一个数字前的空格，后续所有空格替换为'/'; \n" +
                "入向限速速率值：提取出端口的入向限速的速率值，如果命令没有指定方向，则也认为是入向限速速率值，请输出原值，不要输出单位，不要做任何单位换算; \n" +
                "出向限速速率值：提取出端口的出向限速的速率值，如果命令没有指定方向，则也认为是入向限速速率值，请输出原值，不要输出单位，不要做任何单位换算; \n" +
                "入向限速模板：提取出端口的入向限速模板名称，如果命令没有指定方向，则也认为是入向限速模板名称; \n" +
                "出向限速模板：提取出端口的出向限速模板名称，如果命令没有指定方向，则也认为是出向限速模板名称; ";
        data.replace("\\r\\n", "\r\n");
        String[] split = data.split("\r\n");
        List<Map<String, Object>> portInfoList = new ArrayList<>();
        for (int i = 0; i < split.length; i+=50){
            Map<String, Map<String, Object>> portMap = new HashMap<>();
            //循环提取1000条数据，往回追溯100条
            int startIndex = i==0?0:i-20;
            int endIndex = Math.min(i + 50, split.length) -1;
            // 提取子数组（包含 start 到 end 的位置）
            String[] subArray = Arrays.copyOfRange(split, startIndex, endIndex + 1);
            // 拼接为字符串
            String join = String.join("\n", subArray);
            //log.info("入参：{}", join);
            String result = askParam(join, sysContent);
            //log.info("ai解析结果：{}", result);
            try {
                if (result.contains("```json")) {
                    result = result.replace("```json", "");
                }
                if (result.contains("```")) {
                    result = result.replace("```", "");
                }
                JSONArray jsonArray = JSONObject.parseArray(result);
                jsonArray.forEach(o -> {
                    JSONObject jsonObject = (JSONObject) o;
                    String portName = jsonObject.getString("pn");
                    String inSpeed = jsonObject.getString("is");
                    try {
                        if (StringUtils.isNotEmpty(inSpeed)) {
                            inSpeed = Double.parseDouble(inSpeed) / 1000 + "";
                        }
                    } catch (Exception e) {
                        inSpeed = "";
                    }
                    String outSpeed = jsonObject.getString("os");
                    try {
                        if (StringUtils.isNotEmpty(outSpeed)) {
                            outSpeed = Double.parseDouble(outSpeed) / 1000 + "";
                        }
                    } catch (Exception e) {
                        outSpeed = "";
                    }
                    String inSpeedTemplate = jsonObject.getString("ist");
                    String outSpeedTemplate = jsonObject.getString("ost");
                    String ip = jsonObject.getString("ip");
                    String ipMask = jsonObject.getString("ipMask");
                    String ipv6 = jsonObject.getString("ipv6");
                    Map<String, Object> rateTemplate = new HashMap<>();
                    rateTemplate.put("deviceIp", deviceIp);
                    rateTemplate.put("portName", portName);
                    rateTemplate.put("inSpeed", inSpeed);
                    rateTemplate.put("outSpeed", outSpeed);
                    rateTemplate.put("inSpeedTemplate", inSpeedTemplate);
                    rateTemplate.put("outSpeedTemplate", outSpeedTemplate);
                    rateTemplate.put("ip", ip);
                    rateTemplate.put("ipMask", ipMask);
                    rateTemplate.put("ipv6", ipv6);
                    if (portMap.get(portName) == null) {
                        portMap.put(portName, rateTemplate);
                    } else {
                        int m = 0;
                        int n = 0;
                        Map<String, Object> oldRateTemplate = portMap.get(portName);
                        String oldInSpeed = MapUtils.getString(oldRateTemplate, "inSpeed");
                        String oldOutSpeed = MapUtils.getString(oldRateTemplate, "outSpeed");
                        String oldInSpeedTemplate = MapUtils.getString(oldRateTemplate, "inSpeedTemplate");
                        String oldOutSpeedTemplate = MapUtils.getString(oldRateTemplate, "outSpeedTemplate");
                        if (StringUtils.isNotEmpty(oldInSpeed)) {
                            m += 1;
                        }
                        if (StringUtils.isNotEmpty(oldOutSpeed)) {
                            m += 1;
                        }
                        if (StringUtils.isNotEmpty(oldInSpeedTemplate)) {
                            m += 1;
                        }
                        if (StringUtils.isNotEmpty(oldOutSpeedTemplate)) {
                            m += 1;
                        }
                        if (StringUtils.isNotEmpty(inSpeed)) {
                            n += 1;
                        }
                        if (StringUtils.isNotEmpty(outSpeed)) {
                            n += 1;
                        }
                        if (StringUtils.isNotEmpty(inSpeedTemplate)) {
                            n += 1;
                        }
                        if (StringUtils.isNotEmpty(outSpeedTemplate)) {
                            n += 1;
                        }
                        if (n > m) {
                            portMap.put(portName, rateTemplate);
                        }
                    }
                });
                portMap.forEach((k, v) -> portInfoList.add(v));
            } catch (Exception e) {
                log.error("解析端口限速模板失败：{}", result);
                log.error("解析端口限速模板失败：{}{}", deviceIp, e.getMessage());
            }
        }
        return portInfoList;
    }

    private String askParam( String content, String sysContent) {
        StringBuffer res = new StringBuffer();
        JSONObject param = new JSONObject();
        JSONArray messages = new JSONArray();
        JSONObject system = new JSONObject();
        system.put("content",sysContent);
        system.put("role","system");
        messages.add(system);
        JSONObject user = new JSONObject();
        user.put("content",  content);
        user.put("role","user");
        messages.add(user);

        param.put("messages",messages);
        param.put("model","DeepSeek-V3-w8a8");
        param.put("stream",true);


        try (CloseableHttpClient client = HttpClients.createDefault()) {

            HttpPost request = new HttpPost("http://openapi.telecomjs.com:80/eop/AIGLKFPT/DeepSeekV3_w8a8_AIPlatform/completions");
            request.addHeader("X-APP-ID", "2191d2c95aaa43d0991bce3d9131075f");
            request.addHeader("X-APP-KEY", "b2e19bea3b6a4da385ff3ffbe63a6a30");
            request.setEntity(new StringEntity(param.toJSONString(), "UTF-8"));

            // 执行请求
            HttpResponse response = client.execute(request);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                try (InputStream inputStream = entity.getContent();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                    String line;
                    // 逐行读取流式响应
                    while ((line = reader.readLine()) != null) {
                        if (line.startsWith("data:")) {
                            processDataChunk(line.substring(5).trim(),res);
                        }
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return res.toString();

    }

    private static void processDataChunk(String jsonData,StringBuffer sb) {
        try {
            JsonNode node = mapper.readTree(jsonData);
            JsonNode choices = node.get("choices");
            if (choices.isArray() && choices.size() > 0) {
                JsonNode delta = choices.get(0).get("delta");
                if (delta.has("content")) {
                    sb.append(delta.get("content").asText());

                }
            }
        } catch (Exception e) {

        }
    }

    private List<Map<String, Object>> analysisConfigRate(String data, String deviceIp, boolean isCisco) {
        String sysContent = "你是一名网络设备配置审核专家，能够从网络设备中分析出命令中有哪些限速模板：\n" +
                "限速模板命令样例：" +
                "1.traffic behavior xxxx \n car cir xxx cbs xxx ebs xx green pass red discard yellow。behavior后的xxxx为限速模板名称，cir后的xxx为限速速率值（单位为Kbps） " +
                "2.qos-profile xxxx \n user-queue cir xxx pir xxx。qos-profile后的xxxx为限速模板名称，cir后的xxx为限速速率值（单位为Kbps）" +
                "3.policy-map xxx \n" +
                "  class speedlimit\n" +
                "police xxx confirm-action transmit exceed-action drop \n" +
                "exit。 policy-map后的xxxx为限速模板名称，police后的xxx为限速速率值（单位为Kbps）" +
                "4.config qos\n" +
                "sap-ingress xxxx create\n" +
                "queue 1 create\n" +
                "rate xxx cir xxx。 sap-ingress后的xxxx为限速模板名称，cir后的xxx为限速速率值（单位为Kbps）。" +
                "5.traffic policy xxx\n" +
                "classifier xx behavior xxxxxx precedence x 。 traffic policy后的xxx为限速模板名称，behavior后的xxxxx为引用的限速模板名称，如果有多个classifier,请将引用的限速模板名称用,分割。\n" +
                "6.qos car xxx cir xxxx kbps cbs xxxxx bytes\n" +
                " qos car后的xxx为限速模板名称，cir后的xxxx为限速速率值（单位为Kbps）。\n" +
                "         请返回限速模板：\n" +
                "         返回格式：必须严格遵守。仅是json数组格式，每个对象包括:tempName(限速模板名称，不要去除空格),value(模板限速值(请输出原值，不要输出单位，不要做任何单位换算)),instinct(具体配置命令),relaTempName(引用的限速模板名称)。  \n" +
                "请用纯文本格式回答，不要使用任何Markdown符号或代码块,返回结果不要有```json和```。";
        String[] split = data.split("\\r\\n");
        List<Map<String, Object>> rateTemplateList = new ArrayList<>();
        List<String> tempNameList = new ArrayList<>();
        Map<String, String> tempValue = new HashMap<>();
        for (int i = 0; i < split.length; i+=100){
            //循环提取1000条数据，往回追溯100条
            int startIndex = i==0?0:i-50;
            int endIndex = Math.min(i + 100, split.length) -1;
            // 提取子数组（包含 start 到 end 的位置）
            String[] subArray = Arrays.copyOfRange(split, startIndex, endIndex + 1);
            // 拼接为字符串
            String join = String.join("\n", subArray);
            //log.info("入参：{}", join);
            String result = askParam(join, sysContent);
            //log.info("ai解析结果：{}", result);
            try {
                if (result.contains("```json")) {
                    result = result.replace("```json", "");
                }
                if (result.contains("```")) {
                    result = result.replace("```", "");
                }
                JSONArray jsonArray = JSONObject.parseArray(result);
                jsonArray.forEach(o -> {
                    JSONObject jsonObject = (JSONObject) o;
                    String tempName = jsonObject.getString("tempName");
                    if (StringUtils.isNotEmpty(tempName)) {
                        String value = jsonObject.getString("value");
                        String relaTempName = jsonObject.getString("relaTempName");
                        //如果限速值为0.则认为未限速
                        if (!"0".equals(value) && StringUtils.isNotEmpty(value)) {
                            if (isCisco) {
                                value = Double.parseDouble(value) / 1000000 + "";
                            } else {
                                value = Double.parseDouble(value) / 1000 + "";
                            }
                            tempValue.put(tempName, value);
                        }
                        String instinct = jsonObject.getString("instinct");
                        Map<String, Object> rateTemplate = new HashMap<>();
                        rateTemplate.put("deviceIp", deviceIp);
                        rateTemplate.put("templateName", tempName);
                        rateTemplate.put("rateValue", value);
                        rateTemplate.put("relaTempName", relaTempName);
                        rateTemplate.put("instinct", splitStr(instinct, 250));
                        if (!tempNameList.contains(tempName)) {
                            tempNameList.add(tempName);
                            rateTemplateList.add(rateTemplate);
                        }
                    }
                });
            } catch (Exception e) {
                log.error("解析速率模板失败：{}", result);
                log.error("解析速率模板失败：{}{}", deviceIp, e.getMessage());
            }
        }
        if (CollectionUtils.isNotEmpty(rateTemplateList)) {
            for (Map<String, Object> stringObjectMap : rateTemplateList) {
                String value = MapUtils.getString(stringObjectMap, "rateValue");
                if (("0".equals(value) || StringUtils.isEmpty(value)) && StringUtils.isNotEmpty(MapUtils.getString(stringObjectMap, "relaTempName"))) {
                    String relaTempName = MapUtils.getString(stringObjectMap, "relaTempName");
                    for (String tempName : tempValue.keySet()) {
                        if (tempName.contains(relaTempName)) {
                            value = tempValue.get(tempName);
                            break;
                        }
                    }
                }
                if ("0".equals(value)) {
                    stringObjectMap.put("rateValue", null);
                } else {
                    stringObjectMap.put("rateValue", value);
                }
            }
        }
        return rateTemplateList;
    }

    public String splitStr(String data, int splitStr) {
        if (org.apache.commons.lang.StringUtils.isEmpty(data)) {
            return data;
        }
        if (data.length() < splitStr) {
            return data;
        }
        return data.substring(0, splitStr) + "...";
    }
}
