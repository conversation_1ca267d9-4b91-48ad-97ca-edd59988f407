package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.nrm.config.DocumentSecurityProperties;
import com.telecom.nrm.dao.ExportLogDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.dto.ExportLogDTO;
import com.telecom.nrm.dto.ExportResponseDTO;
import com.telecom.nrm.dto.SheetData;
import com.telecom.nrm.service.DocumentExportService;
import com.telecom.nrm.service.SftpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 文档导出服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Service
@Slf4j
public class DocumentExportServiceImpl implements DocumentExportService {

    @Autowired
    private SftpService sftpService;

    @Autowired
    private DocumentSecurityProperties documentSecurityProperties;

    @Autowired
    private ExportLogDao exportLogDao;
    
    @Override
    public ExportResponseDTO exportToDocumentSecurity(List<Map<String, Object>> data,
                                                    List<String> columns,
                                                    String sheetName,
                                                    String fileNamePrefix,
                                                    String exportModule,
                                                    String exportInterface,
                                                    String exportType) {
        Date startTime = new Date();
        String operatorId = null;
        String operatorName = null;
        String areaCode = null;

        try {
            log.info("🚀 开始导出文档: fileNamePrefix={}, dataCount={}", fileNamePrefix, data.size());

            // 获取用户信息
            JwtUser jwtUser = SecurityContext.getJwtUser();
            if (jwtUser != null) {
                operatorId = jwtUser.getId();
                operatorName = jwtUser.getUsername();
                // 这里可以根据需要获取地市编码，暂时使用默认值
                areaCode = "wx"; // 可以从用户信息或其他地方获取
            }

            // 创建Excel工作簿
            XSSFWorkbook workbook = createWorkbook(data, columns, sheetName);

            // 生成文件名
            String fileName = generateFileName(fileNamePrefix);

            // 上传到文档安全平台
            ExportResponseDTO result = uploadToDocumentSecurity(workbook, fileName);

            Date endTime = new Date();

            // 记录日志
            if (result.isSuccess()) {
                // 记录成功日志
                recordSuccessLog(operatorId, operatorName, exportModule, exportInterface, exportType,
                               fileName, result.getFileSize(), data.size(), sheetName,
                               result.getRedirectUrl(), startTime, endTime, areaCode);
            } else {
                // 记录失败日志
                recordFailureLog(operatorId, operatorName, exportModule, exportInterface, exportType,
                               result.getMessage(), startTime, endTime, areaCode);
            }

            return result;

        } catch (Exception e) {
            log.error("❌ 文档导出失败: fileNamePrefix={}, error={}", fileNamePrefix, e.getMessage(), e);

            Date endTime = new Date();
            // 记录异常日志
            recordFailureLog(operatorId, operatorName, exportModule, exportInterface, exportType,
                           "导出异常: " + e.getMessage(), startTime, endTime, areaCode);

            return ExportResponseDTO.failure("导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建Excel工作簿
     */
    private XSSFWorkbook createWorkbook(List<Map<String, Object>> data,
                                      List<String> columns,
                                      String sheetName) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        createSheet(workbook, data, columns, sheetName);
        return workbook;
    }
    
    /**
     * 创建工作表
     */
    private void createSheet(XSSFWorkbook workbook,
                           List<Map<String, Object>> data,
                           List<String> columns,
                           String sheetName) {
        XSSFSheet sheet = workbook.createSheet(sheetName);

        // 创建表头
        XSSFRow header = sheet.createRow(0);
        for (int i = 0; i < columns.size(); i++) {
            String columnName = columns.get(i).split(":")[0];
            XSSFCell headerCell = header.createCell(i);
            headerCell.setCellType(CellType.STRING);
            headerCell.setCellValue(columnName);
        }

        // 填充数据
        int rowIndex = 1;
        for (Map<String, Object> rowData : data) {
            XSSFRow row = sheet.createRow(rowIndex++);
            for (int i = 0; i < columns.size(); i++) {
                String columnCode = columns.get(i).split(":")[1];
                Object value = rowData.get(columnCode);
                XSSFCell cell = row.createCell(i);
                // 强制设置为文本类型，避免数字前导0丢失
                cell.setCellType(CellType.STRING);
                cell.setCellValue(value != null ? value.toString() : "");
            }
        }

        log.info("📊 创建工作表: sheetName={}, dataRows={}", sheetName, data.size());
    }
    
    /**
     * 上传Excel到文档安全平台
     */
    private ExportResponseDTO uploadToDocumentSecurity(XSSFWorkbook workbook, String fileName) {
        try {
            // 将Excel写入字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            workbook.close();

            long fileSize = baos.size();

            // 获取用户工号
            String userWorkNumber = getUserWorkNumber();

            // 上传到SFTP服务器
            boolean uploadSuccess = sftpService.uploadFile(userWorkNumber, fileName,
                    new ByteArrayInputStream(baos.toByteArray()));

            if (uploadSuccess) {
                log.info("✅ 文档上传成功: fileName={}, fileSize={}KB", fileName, fileSize / 1024);
                return ExportResponseDTO.success(fileName, documentSecurityProperties.getRedirect_url(), fileSize, null);
            } else {
                return ExportResponseDTO.failure("文件上传到文档安全平台失败");
            }

        } catch (IOException e) {
            log.error("❌ 文档上传异常: fileName={}, error={}", fileName, e.getMessage(), e);
            return ExportResponseDTO.failure("文件处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String prefix) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        return prefix + "_" + timestamp + ".xlsx";
    }
    
    /**
     * 获取用户统一工号
     */
    private String getUserWorkNumber() {
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();
            if (jwtUser != null && jwtUser.getUsername() != null) {
                String fullWorkNumber = jwtUser.getUsername();
                log.info("🔍 SFTP文件目录记录 - 获取完整用户工号: {}", fullWorkNumber);
                System.out.println("🔍 SFTP文件目录记录 - 获取完整用户工号: " + fullWorkNumber);

                // 截取@@@前面的部分作为目录名
                String workNumber;
                if (fullWorkNumber.contains("@@@")) {
                    workNumber = fullWorkNumber.substring(0, fullWorkNumber.indexOf("@@@"));
                    log.info("🔍 SFTP文件目录记录 - 截取后的工号: {}", workNumber);
                    System.out.println("🔍 SFTP文件目录记录 - 截取后的工号: " + workNumber);
                } else {
                    workNumber = fullWorkNumber;
                    log.info("🔍 SFTP文件目录记录 - 工号中无@@@标识，直接使用: {}", workNumber);
                    System.out.println("🔍 SFTP文件目录记录 - 工号中无@@@标识，直接使用: " + workNumber);
                }

                return workNumber;
            } else {
                log.warn("⚠️ JWT用户信息为空，使用默认工号");
                System.out.println("⚠️ JWT用户信息为空，使用默认工号");
                return "UNKNOWN_USER";
            }
        } catch (Exception e) {
            log.warn("获取用户工号失败: {}", e.getMessage());
            System.out.println("获取用户工号失败: " + e.getMessage());
            return "UNKNOWN_USER";
        }
    }

    /**
     * 记录成功日志
     */
    private void recordSuccessLog(String operatorId, String operatorName, String exportModule,
                                String exportInterface, String exportType, String fileName,
                                Long fileSize, Integer dataCount, String sheetName,
                                String redirectUrl, Date startTime, Date endTime, String areaCode) {
        try {
            // 参数验证
            if (!StringUtils.hasText(operatorId) || !StringUtils.hasText(exportInterface) || !StringUtils.hasText(exportType)) {
                log.warn("⚠️ 导出日志记录参数不完整，跳过记录");
                return;
            }

            // 构建日志参数
            JSONObject logParam = new JSONObject();
            logParam.put("id", UUID.randomUUID().toString().replace("-", ""));
            logParam.put("operatorId", operatorId);
            logParam.put("operatorName", operatorName != null ? operatorName : "未知用户");
            logParam.put("exportModule", exportModule);
            logParam.put("exportInterface", exportInterface);
            logParam.put("exportType", exportType);
            logParam.put("fileName", fileName);
            logParam.put("fileSize", fileSize);
            logParam.put("dataCount", dataCount);
            logParam.put("sheetName", sheetName);
            logParam.put("exportStatus", "SUCCESS");
            logParam.put("sftpUploadStatus", "SUCCESS");
            logParam.put("redirectUrl", redirectUrl);
            logParam.put("startTime", startTime);
            logParam.put("endTime", endTime);
            logParam.put("durationMs", endTime.getTime() - startTime.getTime());
            logParam.put("areaCode", areaCode);

            // 调用DataQL保存日志
            Integer insertResult = exportLogDao.saveExportLog(logParam, NRMConstants.SHARDING_GRAPH_DB);

            if (insertResult != null && insertResult > 0) {
                log.info("✅ 导出日志记录成功: fileName={}", fileName);
            } else {
                log.warn("⚠️ 导出日志记录失败: insertResult={}", insertResult);
            }

        } catch (Exception e) {
            log.error("❌ 记录导出日志异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录失败日志
     */
    private void recordFailureLog(String operatorId, String operatorName, String exportModule,
                                String exportInterface, String exportType, String errorMessage,
                                Date startTime, Date endTime, String areaCode) {
        try {
            // 构建日志参数
            JSONObject logParam = new JSONObject();
            logParam.put("id", UUID.randomUUID().toString().replace("-", ""));
            logParam.put("operatorId", operatorId != null ? operatorId : "UNKNOWN_USER");
            logParam.put("operatorName", operatorName != null ? operatorName : "未知用户");
            logParam.put("exportModule", exportModule);
            logParam.put("exportInterface", exportInterface);
            logParam.put("exportType", exportType);
            logParam.put("exportStatus", "FAILED");
            logParam.put("errorMessage", errorMessage);
            logParam.put("startTime", startTime);
            logParam.put("endTime", endTime);
            if (startTime != null && endTime != null) {
                logParam.put("durationMs", endTime.getTime() - startTime.getTime());
            }
            logParam.put("areaCode", areaCode);

            // 调用DataQL保存日志
            Integer insertResult = exportLogDao.saveExportLog(logParam, NRMConstants.SHARDING_GRAPH_DB);

            if (insertResult != null && insertResult > 0) {
                log.info("✅ 导出失败日志记录成功: errorMessage={}", errorMessage);
            } else {
                log.warn("⚠️ 导出失败日志记录失败: insertResult={}", insertResult);
            }

        } catch (Exception e) {
            log.error("❌ 记录导出失败日志异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public ExportResponseDTO exportMultipleSheetToDocumentSecurity(List<SheetData> sheetDataList,
                                                                 String fileNamePrefix,
                                                                 String exportModule,
                                                                 String exportInterface,
                                                                 String exportType) {
        Date startTime = new Date();
        String operatorId = null;
        String operatorName = null;
        String areaCode = null;
        int totalDataCount = 0;

        try {
            log.info("🚀 开始多Sheet导出文档: fileNamePrefix={}, sheetCount={}", fileNamePrefix, sheetDataList.size());

            // 计算总数据量
            for (SheetData sheetData : sheetDataList) {
                totalDataCount += sheetData.getData().size();
            }

            // 获取用户信息
            JwtUser jwtUser = SecurityContext.getJwtUser();
            if (jwtUser != null) {
                operatorId = jwtUser.getId();
                operatorName = jwtUser.getUsername();
                areaCode = "wx"; // 可以从用户信息或其他地方获取
            }

            // 创建多Sheet Excel工作簿
            XSSFWorkbook workbook = createMultipleSheetWorkbook(sheetDataList);

            // 生成文件名
            String fileName = generateFileName(fileNamePrefix);

            // 上传到文档安全平台
            ExportResponseDTO result = uploadToDocumentSecurity(workbook, fileName);

            Date endTime = new Date();

            // 记录日志
            if (result.isSuccess()) {
                // 构建工作表名称字符串
                StringBuilder sheetNames = new StringBuilder();
                for (int i = 0; i < sheetDataList.size(); i++) {
                    if (i > 0) sheetNames.append(",");
                    sheetNames.append(sheetDataList.get(i).getSheetName());
                }

                // 记录成功日志
                recordSuccessLog(operatorId, operatorName, exportModule, exportInterface, exportType,
                               fileName, result.getFileSize(), totalDataCount, sheetNames.toString(),
                               result.getRedirectUrl(), startTime, endTime, areaCode);
            } else {
                // 记录失败日志
                recordFailureLog(operatorId, operatorName, exportModule, exportInterface, exportType,
                               result.getMessage(), startTime, endTime, areaCode);
            }

            return result;

        } catch (Exception e) {
            log.error("❌ 多Sheet导出异常: {}", e.getMessage(), e);
            Date endTime = new Date();
            recordFailureLog(operatorId, operatorName, exportModule, exportInterface, exportType,
                           e.getMessage(), startTime, endTime, areaCode);
            return ExportResponseDTO.failure("导出失败: " + e.getMessage());
        }
    }

    /**
     * 创建多Sheet Excel工作簿
     */
    private XSSFWorkbook createMultipleSheetWorkbook(List<SheetData> sheetDataList) {
        XSSFWorkbook workbook = new XSSFWorkbook();

        for (SheetData sheetData : sheetDataList) {
            log.info("📊 创建工作表: {}, 数据行数: {}", sheetData.getSheetName(), sheetData.getData().size());

            XSSFSheet sheet = workbook.createSheet(sheetData.getSheetName());

            // 创建表头
            XSSFRow headerRow = sheet.createRow(0);
            List<String> columns = sheetData.getColumns();
            for (int i = 0; i < columns.size(); i++) {
                String columnDef = columns.get(i);
                String displayName = columnDef.contains(":") ? columnDef.split(":")[0] : columnDef;
                XSSFCell headerCell = headerRow.createCell(i);
                headerCell.setCellType(CellType.STRING);
                headerCell.setCellValue(displayName);
            }

            // 填充数据
            List<Map<String, Object>> data = sheetData.getData();
            for (int rowIndex = 0; rowIndex < data.size(); rowIndex++) {
                XSSFRow dataRow = sheet.createRow(rowIndex + 1);
                Map<String, Object> rowData = data.get(rowIndex);

                for (int colIndex = 0; colIndex < columns.size(); colIndex++) {
                    String columnDef = columns.get(colIndex);
                    String fieldName = columnDef.contains(":") ? columnDef.split(":")[1] : columnDef;
                    Object cellValue = rowData.get(fieldName);
                    XSSFCell cell = dataRow.createCell(colIndex);
                    // 强制设置为文本类型，避免数字前导0丢失
                    cell.setCellType(CellType.STRING);
                    cell.setCellValue(cellValue != null ? cellValue.toString() : "");
                }
            }
        }

        return workbook;
    }

    @Override
    public ExportResponseDTO uploadRawFileToDocumentSecurity(byte[] fileBytes, String fileName,
                                                           String moduleName, String description,
                                                           String apiEndpoint, String exportType,
                                                           String fileExtension) {
        try {
            log.info("🎯 [原始文件上传] 开始上传原始文件到文档安全平台");
            log.info("📁 [原始文件上传] 文件名: {}", fileName);
            log.info("📊 [原始文件上传] 文件大小: {} bytes", fileBytes.length);
            log.info("📄 [原始文件上传] 文件扩展名: {}", fileExtension);

            // 1. 上传文件到SFTP
            String uploadedFileName = uploadFileToSFTP(fileBytes, fileName);
            if (uploadedFileName == null) {
                return ExportResponseDTO.failure("文件上传到SFTP失败");
            }

            // 2. 构建响应
            ExportResponseDTO response = ExportResponseDTO.success(
                uploadedFileName,
                documentSecurityProperties.getRedirect_url(),
                (long) fileBytes.length,
                null  // 原始文件上传没有数据条数概念
            );

            log.info("✅ [原始文件上传] 文件上传成功: {}", uploadedFileName);
            return response;

        } catch (Exception e) {
            log.error("💥 [原始文件上传] 上传失败: {}", e.getMessage(), e);
            return ExportResponseDTO.failure("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public void recordExportLog(JSONObject logData) {
        try {
            log.info("📝 [导出日志] 开始记录导出日志到数据库");
            log.info("📋 [导出日志] 日志数据: {}", logData);

            // 调用DataQL接口记录日志
            String shardingCode = getShardingCodeFromAreaName(logData.getString("operator_name"));
            Integer result = exportLogDao.saveExportLog(logData, shardingCode);

            if (result != null && result > 0) {
                log.info("✅ [导出日志] 日志记录成功，影响行数: {}", result);
            } else {
                log.error("❌ [导出日志] 日志记录失败，影响行数: {}", result);
            }

        } catch (Exception e) {
            log.error("💥 [导出日志] 记录日志异常: {}", e.getMessage(), e);
            throw new RuntimeException("记录导出日志失败", e);
        }
    }

    /**
     * 从操作员名称中提取地区名称并获取分库代码
     */
    private String getShardingCodeFromAreaName(String operatorName) {
        try {
            if (operatorName != null && operatorName.contains("@@@")) {
                String areaName = operatorName.split("@@@")[1];
                // 这里可以根据地区名称映射到分库代码
                // 暂时返回默认分库代码
                return "ds_bc_o3_js"; // 默认江苏省分库
            }
            return "ds_bc_o3_js"; // 默认分库代码
        } catch (Exception e) {
            log.warn("解析操作员地区信息失败: {}", e.getMessage());
            return "ds_bc_o3_js"; // 默认分库代码
        }
    }

    /**
     * 上传文件到SFTP服务器
     * @param fileBytes 文件字节数组
     * @param fileName 文件名
     * @return 上传成功的文件名，失败返回null
     */
    private String uploadFileToSFTP(byte[] fileBytes, String fileName) {
        try {
            log.info("🎯 [SFTP上传] 开始上传文件: {}", fileName);
            log.info("📊 [SFTP上传] 文件大小: {} bytes", fileBytes.length);

            // 获取用户工号
            String userWorkNumber = getUserWorkNumber();
            log.info("👤 [SFTP上传] 用户工号: {}", userWorkNumber);

            // 上传到SFTP服务器
            boolean uploadSuccess = sftpService.uploadFile(userWorkNumber, fileName,
                    new ByteArrayInputStream(fileBytes));

            if (uploadSuccess) {
                log.info("✅ [SFTP上传] 文件上传成功: {}", fileName);
                return fileName;
            } else {
                log.error("❌ [SFTP上传] 文件上传失败: {}", fileName);
                return null;
            }

        } catch (Exception e) {
            log.error("💥 [SFTP上传] 上传异常: fileName={}, error={}", fileName, e.getMessage(), e);
            return null;
        }
    }
}
