package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.PmIpranUser;
import com.telecom.nrm.service.ExternalInterfaceService;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class ExternalInterfaceServiceImpl implements ExternalInterfaceService {

    @Value("${external.point2Point.url}")
    String point2PointUrl;

    @Value("${external.queryRes.url}")
    String queryResUrl;

    @Override
    public String queryPoint2Point(String accessCode, String bussinessCode) {
        Map<String, String> params = new HashMap<>();
        params.put("access_code", accessCode);
        params.put("bussiness_code", bussinessCode);
        try {
            String str = HttpRequestsUtils.sendGet(point2PointUrl, null, params);
            if (StringUtils.isNotEmpty(str)) {
                JSONObject jsonObject = JSONObject.parseObject(str);
                if (jsonObject.containsKey("error")) {
                    log.error("调用ipran点对点接口结果出现error");

                } else {
                    return str;
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    @Override
    public PmIpranUser queryRes(String accessCode, String bussinessCode) {
        Map<String, String> params = new HashMap<>();
        params.put("access_code", accessCode);
        params.put("bussiness_code", bussinessCode);
        try {
            String str = HttpRequestsUtils.sendGet(queryResUrl, null, params);
            if (StringUtils.isNotEmpty(str)) {
                JSONObject jsonObject = JSONObject.parseObject(str);
                if (jsonObject.containsKey("error")) {
                    log.error("调用资源接口出现错误");
                } else {
                    return JSONObject.parseObject(str, PmIpranUser.class);
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }
}
