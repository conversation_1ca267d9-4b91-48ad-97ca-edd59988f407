package com.telecom.nrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.identitymanagement.model.User;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.FlowDao;
import com.telecom.nrm.dao.LifeCircuitDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import com.telecom.nrm.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
@Service
@Slf4j
public class FlowServiceImpl implements FlowService {

    @Autowired
    FlowDao flowDao;

    @Autowired
    UserService userService;

    public JSONObject pm_flow_instance_insert(JSONObject jsonObject){
        return flowDao.pm_flow_instance_insert(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public JSONObject pm_task_instance_insert(JSONObject jsonObject){
        return flowDao.pm_task_instance_insert(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public JSONObject pm_rela_flow_task_insert(JSONObject jsonObject){
        return flowDao.pm_rela_flow_task_insert(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public JSONObject pm_resource_process_insert(JSONObject jsonObject){
        return flowDao.pm_resource_process_insert(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public JSONObject pm_rela_flow_resource_insert(JSONObject jsonObject){
        return flowDao.pm_rela_flow_resource_insert(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public JSONObject pm_flow_instance_update(JSONObject jsonObject){
        return flowDao.pm_flow_instance_update(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public JSONObject pm_flow_instance_query(JSONObject jsonObject){
        return flowDao.pm_flow_instance_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public JSONObject pm_flow_resource_query(JSONObject jsonObject){
        return flowDao.pm_flow_resource_query(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    public Integer pm_resource_process_delete(JSONObject jsonObject){
        return flowDao.pm_resource_process_delete(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }


    public JSONObject doPost(String url_address,JSONObject jsonObject) throws IOException {
        URL url = new URL(url_address);
        String result= HttpClientUtil.doPost(url_address,  JSONObject.toJSONString(jsonObject));
        JSONObject result_json = JSONObject.parseObject(result);
        return result_json;
    }

    public JSONObject flow_myTodoService(JSONObject jsonObject)throws IOException {
        String flow_myTodo_url = "http://*************:39110/open/flow/myTodo";
        JSONObject result = doPost(flow_myTodo_url, jsonObject);
        List<JSONObject> all_flow_list = new ArrayList<>();
        if(ObjectUtil.isNotNull(pm_flow_instance_query(new JSONObject()).getJSONArray("result"))){
            all_flow_list = pm_flow_instance_query(new JSONObject()).getJSONArray("result");
        }
        List<JSONObject> todo_list = new ArrayList<>();
        if(ObjectUtil.isNotNull(result.getJSONArray("result"))){
            todo_list = result.getJSONArray("result");
        }
        for(JSONObject todo: todo_list){
            for(JSONObject flow : all_flow_list){
                if(todo.getString("instanceId").equals(flow.getString("instance_id"))){
                    todo.putAll(flow);
                    break;
                }
            }
        }
        return result;
    }

    public JSONObject flow_param(JSONObject jsonObject){
        JSONObject save_flow = new JSONObject();
        save_flow.put("instance_id",jsonObject.getString("flow_id"));
        save_flow.put("task_id", jsonObject.getString("id"));
        save_flow.put("task_name", jsonObject.getString("task_name"));
        save_flow.put("user_code", jsonObject.getString("userCode"));
        save_flow.put("next_handler", jsonObject.getString("nextHandler"));
        return save_flow;
    }

    public List<JSONObject> flow_task_query(JSONObject query_flow){
        JSONObject result_flow_task = flowDao.pm_flow_instance_task_query(query_flow,NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> result = new ArrayList<>();
        if(result_flow_task.getString("result").startsWith("[")){
            result = flowDao.pm_flow_instance_task_query(query_flow,NRMConstants.SHARDING_GRAPH_DB).getJSONArray("result");
        }else{
            JSONObject buf_result = flowDao.pm_flow_instance_task_query(query_flow,NRMConstants.SHARDING_GRAPH_DB).getJSONObject("result");
            if(ObjectUtil.isNotNull(buf_result) && ObjectUtil.isNotEmpty(buf_result)){
                result.add(flowDao.pm_flow_instance_task_query(query_flow,NRMConstants.SHARDING_GRAPH_DB).getJSONObject("result"));
            }
        }
        if(result.size()>0){
            for(JSONObject flow_task : result){
                String accept_user = "";
                if(ObjectUtil.isNotEmpty(flow_task.getString("task_name"))&& ObjectUtil.isNotNull(flow_task.getString("task_name")) && flow_task.getString("task_name").equals("生成工单")){
                    String regula = flow_task.getString("result");
                    if(regula.indexOf("[")>=0){
                        String[] accept_user_regula =regula.substring(regula.indexOf("[")+1, regula.indexOf("]")).split(",");
                        System.out.println(regula+",");
                        if(accept_user_regula.length>0){
                            for(String user : accept_user_regula){
                                String usernameinfo = getUserNameInfo(user.trim());
                                if(accept_user == ""){
                                    accept_user = usernameinfo;
                                }else accept_user = accept_user+","+ usernameinfo;

                            }
                        }
                    }
                }else{
                    String accept_user_handler = flow_task.getString("next_handler");
                    accept_user = getUserNameInfo(accept_user_handler);
                }
                flow_task.put("accept_user", accept_user);
                flow_task.put("user_code_new", getUserNameInfo(flow_task.getString("user_code")));
            }
        }
        return result;
    }

    public String getUserNameInfo(String name){
        JSONObject user_param = new JSONObject();
        user_param.put("usern", name);
        List<JSONObject> userinfo = userService.get_user_info(user_param);
        String accept_user = "";
        System.out.println("user"+name+userinfo);
        if(userinfo.size()>=1){
            accept_user = userinfo.get(0).getString("name")+"("+userinfo.get(0).getString("username")+")";
        }
        return accept_user;
    }

}
