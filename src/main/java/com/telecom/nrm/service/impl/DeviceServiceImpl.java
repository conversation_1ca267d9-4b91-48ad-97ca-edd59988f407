package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.DeviceDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.ResTreeParamDto;
import com.telecom.nrm.domain.WhereBody;
import com.telecom.nrm.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceServiceImpl implements DeviceService {


    @Autowired
    DeviceDao deviceDao;

    @Override
    public PageResponse<JSONObject> queryCircuitByDeviceInfo(JSONObject param, String shardingCode) {
        return deviceDao.queryCircuitByDeviceInfo(param, shardingCode);
    }

    @Override
    public PageResponse<JSONObject> deviceDetailQuery(JSONObject param, String shardingCode) {
        return deviceDao.deviceDetailQuery(param, shardingCode);
    }

    public JSONObject queryCircuitByDevice(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("deviceId".equals(body.getPropertyLabel())) {
                jsonObject.put("deviceId", body.getPropertyValue());
            } else if ("cardId".equals(body.getPropertyLabel())) {
                jsonObject.put("cardId", body.getPropertyValue());
            } else if ("portId".equals(body.getPropertyLabel())) {
                jsonObject.put("portId", body.getPropertyValue());
            } else if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  deviceDao.queryCircuitByDevice(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("circuitId", obj.getString("circuit_id"));
                node.put("circuitSpecId", obj.getString("circuit_spec_id"));
                node.put("circuitName", obj.getString("circuit_name"));
                node.put("circuitCode", obj.getString("circuit_code"));
                node.put("accessCode", obj.getString("access_code"));
                node.put("custName", obj.getString("cust_name"));
                node.put("rate", obj.getString("rate"));
                node.put("aPhysicDeviceId", obj.getString("a_device_id"));
                node.put("aPhysicDeviceSpec", obj.getString("a_device_spec_name"));
                node.put("aPhysicDeviceName", obj.getString("a_device_name"));
                node.put("aPhysicDeviceCode", obj.getString("a_device_code"));
                node.put("zPhysicDeviceId", obj.getString("z_device_id"));
                node.put("zPhysicDeviceSpec", obj.getString("z_device_spec_name"));
                node.put("zPhysicDeviceName", obj.getString("z_device_name"));
                node.put("zPhysicDeviceCode", obj.getString("z_device_code"));
                node.put("aPortId", obj.getString("a_port_id"));
                node.put("aPortSpec", obj.getString("a_port_spec_name"));
                node.put("aPortName", obj.getString("a_port_name"));
                node.put("aPortCode", obj.getString("a_port_code"));
                node.put("zPortId", obj.getString("z_port_id"));
                node.put("zPortSpec", obj.getString("z_port_spec_name"));
                node.put("zPortName", obj.getString("z_port_name"));
                node.put("zPortCode", obj.getString("z_port_code"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }

    public JSONObject queryResInfoByIds(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("entitys".equals(body.getPropertyLabel())) {
                JSONArray array = JSON.parseArray(JSON.toJSONString(body.getPropertyValue()));
                List<String> ids = new ArrayList<>();
                for (Object obj : array) {
                    JSONObject json = JSON.parseObject(String.valueOf(obj));
                    ids.add(json.getString("entityId"));
                }
                jsonObject.put("ids", ids);
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  deviceDao.queryResInfoByIds(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("entityId", obj.getString("entity_id"));
                node.put("entitySpecId", obj.getString("entity_spec_id"));
                node.put("longitude", obj.getString("longitude"));
                node.put("latitude", obj.getString("latitude"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }
}
