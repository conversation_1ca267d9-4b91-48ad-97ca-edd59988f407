package com.telecom.nrm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.IdcAuditDao;
import com.telecom.nrm.dao.NetConfigDao;
import com.telecom.nrm.dao.OTNDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.PortAbnormalRateDto;
import com.telecom.nrm.domain.PortAbnormalTrafficDto;
import com.telecom.nrm.domain.report.IReport;
import com.telecom.nrm.domain.report.PortImprovement;
import com.telecom.nrm.domain.report.RiskItem;
import com.telecom.nrm.entity.PortRateOverrunDto;
import com.telecom.nrm.service.AnalysisPortService;
import com.telecom.nrm.service.AuditService;
import com.telecom.nrm.entity.PmPortNetworkOperationAudit;
import com.telecom.nrm.service.IdcAuditService;
import com.telecom.nrm.service.IdcReportService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.*;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Log4j2
public class IdcAuditServiceImpl implements IdcAuditService {

    @Autowired
    private IdcAuditDao idcAuditDao;

    @Autowired
    private NetConfigDao configDao;

    @Autowired
    private IdcReportService idcReportService;

    @Autowired
    private OTNDao otnDao;

    @Autowired
    private AnalysisPortService analysisPortService;

    private static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static SimpleDateFormat formatday = new SimpleDateFormat("yyyy-MM-dd");
    private static final String API_URL = "http://openapi.telecomjs.com:80/eop/AIGLKFPT/DeepSeekV3_w8a8_AIPlatform/completions";
    private static final ObjectMapper mapper = new ObjectMapper();


    @Override
    public JSONObject qryIdcAuditStatistics(JSONObject params) {
        JSONObject result = new JSONObject();
        JSONObject jsonObject = idcAuditDao.qryIdcAuditStatistics(params, NRMConstants.IDC_DATA);
        JSONObject retObject = jsonObject.getJSONObject("ret");
        String totalCount = retObject.getString("totalcount");
        String closeCount = retObject.getString("closecount");
        String noRiskCount = retObject.getString("noriskcount");
        String openCount = retObject.getString("opencount");
        String riskCount = retObject.getString("riskcount");
        String startTime = params.getString("开始时间");
        String endTime = params.getString("截至时间");
        String userName = params.getString("用户名");
        String deviceIp = params.getString("设备IP");
        String portName = params.getString("端口名称");
        params.put("startDate", params.getString("开始时间"));
        params.put("regionNames", params.getJSONArray("地市"));
        params.put("portName", params.getString("端口名称"));
        params.put("deviceIp", params.getString("设备IP"));
        params.put("userName", params.getString("用户名"));
        params.put("auditStatus", 1);
        params.put("offset", 0);

        // 对提示地区进行重置
        JSONArray allCitys = idcAuditDao.queryOperationExistsCitys(params, "ds_idc_js").getJSONArray("citys");
        JSONArray existsCtitys = new JSONArray();
        for (int i = 0; i < allCitys.size(); i++) {
            existsCtitys.add(allCitys.getJSONObject(i).getString("regionName"));
        }
        params.put("regionNames", existsCtitys);
        params.put("地市", existsCtitys);
        String city = "全省";
        if (params.containsKey("地市") && !params.getJSONArray("地市").isEmpty()) {
            JSONArray citys = params.getJSONArray("地市");
            for (int i = 0; i < citys.size(); i++) {
                if (i == 0) {
                    city = citys.getString(i);
                }
                else {
                    city += "," + citys.getString(i);
                }
            }
        }
        String desc = startTime + "到" + endTime + " " + city  + " " + (StringUtils.isNotEmpty(userName)?"操作人:" + userName + " ":"")
                + (StringUtils.isNotEmpty(deviceIp)?"设备IP：" + deviceIp + " ":"") +
                (StringUtils.isNotEmpty(portName)? "端口名称：" + portName + " ":"") +
                "涉及网络设备端口的开关操作" + totalCount + "次,打开端口" + openCount +
                "次,关闭端口" + closeCount + "次,按单施工" + noRiskCount + "次,存在疑似风险" + riskCount + "次，详情请点击下方链接查看。";

        result.putAll(retObject);
        result.putAll(params);
        result.put("city", city);
        result.put("totalOperations", totalCount);
        result.put("openPorts", openCount);
        result.put("closePorts", closeCount);
        result.put("constructionCount", noRiskCount);
        result.put("unConstructionCount", Integer.valueOf(totalCount) - Integer.valueOf(noRiskCount));
        result.put("riskCount", riskCount);
        result.put("deviceIp", deviceIp);
        result.put("portName", portName);
        generateStatisticInfo(result);
        return result;
    }


    @SneakyThrows
    @Override
    public String dynamicAsk(String userContent, JSONObject paramResult, WebSocketSession session) {
        String regionNames = paramResult.getJSONArray("地市").toString();
        paramResult.put("startDate", paramResult.getString("开始时间"));
        paramResult.put("endDate", paramResult.getString("截至时间"));
        paramResult.put("regionNames", JSONArray.parseArray(regionNames));// 解决循环问题，导致转字符串异常
        paramResult.put("portName", paramResult.getString("端口名称"));
        paramResult.put("deviceIp", paramResult.getString("设备IP"));
        paramResult.put("userName", paramResult.getString("用户名"));
        JSONObject jsonObject = this.idcAuditDao.queryOperationAuditContent(paramResult, NRMConstants.IDC_DATA);
        JSONArray existsRegionsResults = this.idcAuditDao.queryExistsRegions(paramResult, NRMConstants.IDC_DATA).getJSONArray("data");
        JSONArray existsRegions = new JSONArray();
        StringBuffer regions = new StringBuffer();
        for (int i = 0; i < existsRegionsResults.size(); i++) {
            existsRegions.add(existsRegionsResults.getJSONObject(i).getString("regionName"));
            regions.append(existsRegionsResults.getJSONObject(i).getString("regionName"));
            if (i < existsRegionsResults.size() -1 ) {
                regions.append(",");
            }
        }
        JSONArray data = jsonObject.getJSONArray("data");

        StringBuffer destData = new StringBuffer();
        StringBuffer title = new StringBuffer("城市|操作人|操作类型|风险类型|操作时间|操作设备IP|操作端口名称");
        destData.append(title).append("\n");
        //AI分析有错误,人工帮忙做统计
        JSONArray autoStatictic = new JSONArray(); // 包含多类数据,{keys,data}
        JSONObject regionStatistic = new JSONObject();
        regionStatistic.put("conditions","城市"); //  存储维度条件,多个逗号给开
        regionStatistic.put("desc","根据城市统计"); //  描述信息
        regionStatistic.put("data",new JSONObject()); // 存储按照维度统计的数据 {南京；10}
        autoStatictic.add(regionStatistic);

        JSONObject operatorNameStatistic = new JSONObject();
        operatorNameStatistic.put("conditions","操作人");
        operatorNameStatistic.put("desc","根据操作人统计");
        operatorNameStatistic.put("data",new JSONObject());
        autoStatictic.add(operatorNameStatistic);

        JSONObject operationTypeStatistic = new JSONObject();
        operationTypeStatistic.put("conditions","操作类型");
        operationTypeStatistic.put("desc","根据操作类型统计");
        operationTypeStatistic.put("data",new JSONObject());
        autoStatictic.add(operationTypeStatistic);

        JSONObject riskAnalysisStatistic = new JSONObject();
        riskAnalysisStatistic.put("conditions","风险类型");
        riskAnalysisStatistic.put("desc","根据风险类型统计");
        riskAnalysisStatistic.put("data",new JSONObject());
        autoStatictic.add(riskAnalysisStatistic);

        JSONObject deviceIpStatistic = new JSONObject();
        deviceIpStatistic.put("conditions","操作设备IP");
        deviceIpStatistic.put("desc","根据设备IP统计");
        deviceIpStatistic.put("data",new JSONObject());
        autoStatictic.add(deviceIpStatistic);

        JSONObject operationDateStatistic = new JSONObject();
        operationDateStatistic.put("conditions","操作时间");
        operationDateStatistic.put("desc","根据操作日期统计");
        operationDateStatistic.put("data",new JSONObject());
        autoStatictic.add(operationDateStatistic);

        JSONObject regionRiskStatistic = new JSONObject();
        regionRiskStatistic.put("conditions","城市、风险类型");
        regionRiskStatistic.put("desc","根据城市、风险类型统计");
        regionRiskStatistic.put("data",new JSONObject());
        autoStatictic.add(regionRiskStatistic);

        for (int i =0;i<data.size();i++) {
            JSONObject d = data.getJSONObject(i);
            StringBuffer analysisData = new StringBuffer();
            analysisData.append(d.getString("regionName")).append("|");
            analysisData.append(d.getString("operatorName")).append("|");
            analysisData.append(d.getString("operationType")).append("|");
            analysisData.append(d.getString("riskAnalysis")).append("|");
            analysisData.append(d.getString("operationDate")).append("|");
            analysisData.append(d.getString("deviceIp")).append("|");
            analysisData.append(d.getString("portName"));
            destData.append(analysisData).append("\n");
//            if (!existsRegions.contains(d.getString("regionName"))) {
//                existsRegions.add(d.getString("regionName")); // 记录地市集合
//            }
            // 地区
            if (!regionStatistic.getJSONObject("data").containsKey(d.getString("regionName"))) {
                regionStatistic.getJSONObject("data").put(d.getString("regionName"), 0);
            }
            regionStatistic.getJSONObject("data").put(d.getString("regionName"),regionStatistic.getJSONObject("data").getIntValue(d.getString("regionName")) + 1);
            // 操作人
            if (!operatorNameStatistic.getJSONObject("data").containsKey(d.getString("operatorName"))) {
                operatorNameStatistic.getJSONObject("data").put(d.getString("operatorName"), 0);
            }
            operatorNameStatistic.getJSONObject("data").put(d.getString("operatorName"),operatorNameStatistic.getJSONObject("data").getIntValue(d.getString("operatorName")) + 1);
            // 风险操作类型
            if (!operationTypeStatistic.getJSONObject("data").containsKey(d.getString("operationType"))) {
                operationTypeStatistic.getJSONObject("data").put(d.getString("operationType"), 0);
            }
            operationTypeStatistic.getJSONObject("data").put(d.getString("operationType"),operationTypeStatistic.getJSONObject("data").getIntValue(d.getString("operationType")) + 1);
            // 风险类型
            if (!riskAnalysisStatistic.getJSONObject("data").containsKey(d.getString("riskAnalysis"))) {
                riskAnalysisStatistic.getJSONObject("data").put(d.getString("riskAnalysis"), 0);
            }
            riskAnalysisStatistic.getJSONObject("data").put(d.getString("riskAnalysis"),riskAnalysisStatistic.getJSONObject("data").getIntValue(d.getString("riskAnalysis")) + 1);

            // 操作IP
            if (!deviceIpStatistic.getJSONObject("data").containsKey(d.getString("deviceIp"))) {
                deviceIpStatistic.getJSONObject("data").put(d.getString("deviceIp"), 0);
            }
            deviceIpStatistic.getJSONObject("data").put(d.getString("deviceIp"),deviceIpStatistic.getJSONObject("data").getIntValue(d.getString("deviceIp")) + 1);

            // 操作日期
            String day = formatday.format(format.parse(d.getString("operationDate")));
            if (!operationDateStatistic.getJSONObject("data").containsKey(day)) {
                operationDateStatistic.getJSONObject("data").put(day, 0);
            }
            operationDateStatistic.getJSONObject("data").put(day,operationDateStatistic.getJSONObject("data").getIntValue(day) + 1);

            String regionRiskStatisticKey = d.getString("regionName") + "_" + d.getString("riskAnalysis");
            if (!regionRiskStatistic.getJSONObject("data").containsKey(regionRiskStatisticKey)) {
                regionRiskStatistic.getJSONObject("data").put(regionRiskStatisticKey, 0);
            }
            regionRiskStatistic.getJSONObject("data").put(regionRiskStatisticKey,regionRiskStatistic.getJSONObject("data").getIntValue(regionRiskStatisticKey) + 1);
        }

        paramResult.put("地市", existsRegions); // 给页面展示使用
        paramResult.put("regionNames", JSONArray.parseArray(existsRegions.toString())); //  {$ref: "$.regionNames"}
        JSONObject noRiskCountInfo = this.idcAuditDao.queryOperationAuditNoRiskCount(paramResult, NRMConstants.IDC_DATA);// 查询无风险情况，

        String param = paramResult.toJSONString();
        StringBuffer sb = new StringBuffer();
        sb.append("你是个idc设备操作日志分析专家,可以分析用户输入的意图并给予响应，要求如下:").append("\n");
//        sb.append("1、返回要求：以markdown的形式呈现。根据用户需求返回一份综合性的分析报告。内容可以以表格、文字、图表一种或者多种方式markdown呈现。充分识别用途的意图,根据用户意图返回不同内容。图表都是基于echarts实现的,使用echart语法,不允许其他的图表表达方式。" +
//                "必须使用表格,echart图表根据情况决策是否追加(即同种维度表格和图表都展示),不要引入有网络需要的外部资源,不要返回js代码,对于图表只返回echarts对应的markdown内容。图表返回的结构json配置数据里添加属性echartId，取值为uuid，多个图表之间不重复。" +
//                "不要过于深度思考,对于思考结果不需要反复验证。忽略用途意图中的城市和日期类的条件要求。").append("\n");
        sb.append("1、返回要求：以markdown的形式呈现。根据用户需求返回一份综合性的分析报告。内容只能使用表格、文字方式markdown呈现。充分识别用途的意图,根据用户意图返回不同内容。" +
                "不要引入有网络需要的外部资源,不要返回js代码。" +
                "不要过于深度思考,对于思考结果不需要反复验证。忽略用户意图中的城市和日期类的条件对数据的过滤,统计数据和操作清单都是已经使用用户日期和城市类条件过滤后的结果。返回内容必须首先严格返回概述情况,概述内容包括:总操作数、无风险次数、有风险次数、城市范围、风险发生时间范围,内容文字严格遵守这里的命名,并添加备注:未在范围内的城市表示在本次分析中不涉及或者在该地市未发生相关操作。分析完成后最后再添加分析总结和合理建议。" +
                "本次统计无风险次数为：").append(noRiskCountInfo.getJSONArray("data").getJSONObject(0).getString("total")).append(",有风险次数为本次操作记录的数量。" +
                "本次总操作数为有风险次数加上无风险次数。" +
                "本次城市范围:"  + regions.toString() +"。" ).append("。\n");
        sb.append("2、操作记录格式:这是一份所有的风险操作记录清单。该数据可以给用户对明细清单要求使用。对应第一行为字段说明标题行,其他行是操作记录。标题和操作记录字段通过|分割,分割后操作字段说明参考标题行说明" +
                "。每条数据之间无任何关系,即使完全一致也不是同一条操作,不存在重复情况。本次操作记录共计(不包含标题行)").append(data.size()).append("条。").append("\n");
        sb.append("3、思考过程严格遵守:分析用户意图,在操作记录中匹配到对应的一个或者多个字段,这些字段为约束条件,再根据这些条件去统计数据中的统计条件过滤出统计数据信息。").append("\n");
        sb.append("4、思考过程严格遵守:如果用户意图的约束条件可以在统计数据conditions里面匹配到(完全匹配),则使用匹配到的目标对象作为结果进行分析，如果用户有限制具体某个条件的值则再根据该值再匹配到对象的data中进行key匹配过滤,过滤出来的结果进行分析。 无需对操作端口分析,只在需要清单列表时体现。思考过程无需体现清单枚举过程。").append("\n");;
        sb.append("5、思考过程严格遵守:如果用户意图的约束条件可以在统计数据conditions里面无法匹配,则必须只能通过操作记录清单进行分析,不再关注统计数据的内容。逐行检查操作记录判断多个条件是否都满足,如果满足则计数加一,直到遍历完全部记录").append(data.size()).append("条。").append("\n");;
        sb.append("6、思考过程严格遵守:对于有多个约束条件的统计只能用表格呈现,不允许用图表,且表格按照多列呈现,包括:条件1、条件2、 ....、统计值。").append("\n");
        sb.append("7、思考过程严格遵守:返回结果的统计信息必须针对维度条件进行升序排序。").append("\n");
        sb.append("8、数据统计数据格式:是JSON数组,每一项表示的是每个维度的统计数据信息。其中desc表示该项统计信息的描述。conditions表示的是维度条件字段,多个用逗号隔开,是且的关系,内容和操作记录的字段名对应。data表示为该一个或者多个条件下的统计数据,是json对象。其中对应key为维度下某一项的名称（可以是多个条件值联合表达，通过_分割）,value是对应统计次数值。").append("\n");
        sb.append("9、思考过程严格遵守:在数据返回呈现时,对于符合条件的统计内容,需要全部呈现,不允许有遗漏、跳过等,不允许出现省略号、其他等情况，最后输出的结果数量银河符合内容的数量一致。").append("\n");
        sb.append("10、数据统计如下:").append("\n");
        sb.append(autoStatictic.toString()).append("\n");
//        sb.append("10、操作记录txt文件内容如下:").append("\n");
//        sb.append(destData.toString());
        String res = chat(userContent, sb,param,session);// 将解析的参数返回页面给显示详情 param
        return res;
    }

    @Override
    public void export(JSONObject jsonObject, JSONObject statisticCount, List<JSONObject> auditDetails, File retFile, String excelName) throws Exception {
        IReport report = getReport(jsonObject, statisticCount, auditDetails, excelName);
        report.setStartDate(report.getDataStartDate());
        report.setEndDate(report.getDataEndDate());
        idcReportService.generateWeekendReport(report, retFile, "report_template_page_html.xml");
    }


    private IReport getReport(JSONObject jsonObject, JSONObject statisticCount, List<JSONObject> auditDetails, String excelName) throws Exception{
        // 1. 准备数据模型
        Map<String, Object> data = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DecimalFormat df = new DecimalFormat("0.00");
        data.put("current",sdf.format(new Date()));
        //从结果你获取起始和终止时间，以及所有地市
        String startTime = null;
        String endTime = null;
        String areaName = null;
        //稽核数据开始时间
        String dataStartTime = null;
        //稽核数据开始时间
        String dataEndTime = null;
        if (CollectionUtils.isNotEmpty(auditDetails)) {
            List<Date> timeList = new ArrayList<>();
            List<String> areaNames = new ArrayList<>();
            for (JSONObject auditDetail : auditDetails) {
                JSONArray details = auditDetail.getJSONArray("detail");
                for (Object detail : details) {
                    JSONObject riskDetail = (JSONObject) detail;
                    if (StringUtils.isNotEmpty(riskDetail.getString("operationDate"))) {
                        timeList.add(format.parse(riskDetail.getString("operationDate")));
                    }
                    if (StringUtils.isNotEmpty(riskDetail.getString("operatorRegionName"))) {
                        areaNames.add(riskDetail.getString("operatorRegionName"));
                    }
                }
            }
            //升序排序
            Collections.sort(timeList);
            if (CollectionUtils.isNotEmpty(timeList)) {
                startTime = sdf.format(timeList.get(0));
                endTime = sdf.format(timeList.get(timeList.size() - 1));
                dataStartTime = sdf.format(timeList.get(0));
                dataEndTime = sdf.format(timeList.get(timeList.size() - 1));
            }
            //对地市进行排重
            List<String> distAreaName = areaNames.stream().distinct().collect(Collectors.toList());
            areaName = String.join(",", distAreaName);
        }
        //按周导出的时间以入参时间为准
        if(StringUtils.isNotEmpty(jsonObject.getString("开始时间"))) {
            startTime = sdf.format(format.parse(jsonObject.getString("开始时间")));
        }
        if(StringUtils.isNotEmpty(jsonObject.getString("截至时间"))) {
            endTime = sdf.format(format.parse(jsonObject.getString("截至时间")));
        }
        data.put("da",startTime + " 至 " + endTime);
        data.put("totalCount",statisticCount.getString("totalCount"));
        data.put("weekAreaName", areaName);
        data.put("openCount",statisticCount.getString("openPortCount"));
        data.put("closeCount",statisticCount.getString("closePortCount"));
        data.put("withOrderCount",statisticCount.getString("noRiskCount"));
        data.put("withOrderPercent", df.format(statisticCount.getDoubleValue("noRiskCount")/statisticCount.getDoubleValue("totalCount")*100) + "%");
        data.put("unOrderCount",statisticCount.getString("notBuildByOrderCount"));
        data.put("withoutOrderCount",statisticCount.getString("noOrderBuildCount"));
        data.put("riskCount",sumString(statisticCount.getString("notBuildByOrderCount"), statisticCount.getString("noOrderBuildCount")));
        List<Map<String, Object>> risks = new ArrayList<>();
        List<String> riskList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(auditDetails)) {
            int count = 1;
            for (JSONObject auditDetail : auditDetails) {
                Map<String, Object> risk = new HashMap<>();
                risk.put("graphCount", "2.1." + count);
                count++;
                risk.put("risk", auditDetail.getString("riskAnalysis"));
                riskList.add(auditDetail.getString("riskAnalysis"));
                risk.put("risk_level", auditDetail.getString("riskLevel"));
                risk.put("risk_percent", df.format(auditDetail.getDoubleValue("count")/MapUtils.getDoubleValue(data,"riskCount")*100) + "%");
                risk.put("risk_count", auditDetail.getString("count"));
                risk.put("risk_desc", auditDetail.getString("riskDesc"));
                risk.put("risk_result", auditDetail.getString("riskResult"));
                risk.put("suggest",auditDetail.getString("suggest"));
                JSONArray details = auditDetail.getJSONArray("detail");
                List<Map<String, Object>> tables = new ArrayList<>();
                for (Object detail : details) {
                    JSONObject riskDetail = (JSONObject) detail;
                    Map<String, Object> table = new HashMap<>();
                    table.put("areaName", StringUtils.isEmpty(riskDetail.getString("regionName")) ? "" : riskDetail.getString("regionName"));
                    table.put("operateDate", StringUtils.isEmpty(riskDetail.getString("operationDate")) ? "" : riskDetail.getString("operationDate"));
                    table.put("deviceName", StringUtils.isEmpty(riskDetail.getString("deviceIp")) ? "" : riskDetail.getString("deviceIp"));
                    table.put("portName", StringUtils.isEmpty(riskDetail.getString("portName")) ? "" : riskDetail.getString("portName"));
                    table.put("portStatus", StringUtils.isEmpty(riskDetail.getString("portStatus")) ? "" : riskDetail.getString("portStatus"));
                    table.put("custOrderCode", StringUtils.isEmpty(riskDetail.getString("crmCustOrderCode")) ? "" : riskDetail.getString("crmCustOrderCode"));
                    table.put("psoType", StringUtils.isEmpty(riskDetail.getString("psoType")) ? "" : riskDetail.getString("psoType"));
                    table.put("portAction", StringUtils.isEmpty(riskDetail.getString("operationType")) ? "" : riskDetail.getString("operationType"));
                    table.put("reviewStatus", StringUtils.isEmpty(riskDetail.getString("reviewStatusDesc")) ? "" : riskDetail.getString("reviewStatusDesc"));
                    table.put("reviewResult", StringUtils.isEmpty(riskDetail.getString("reviewRisk")) ? "" : riskDetail.getString("reviewRisk"));
                    table.put("operationName", StringUtils.isEmpty(riskDetail.getString("operationName")) ? "" : riskDetail.getString("operationName"));
                    table.put("operationType", StringUtils.isEmpty(riskDetail.getString("operationType")) ? "" : riskDetail.getString("operationType"));
                    table.put("dept", StringUtils.isEmpty(riskDetail.getString("deptName")) ? "" : riskDetail.getString("deptName"));
                    tables.add(table);
                }
                if (CollectionUtils.isNotEmpty(tables) && tables.get(0) != null) {
                    //示例
                    risk.put("risk_example",
                            MapUtils.getString(tables.get(0), "operateDate") + ",设备：" + MapUtils.getString(tables.get(0), "deviceName") + ",端口：" +
                                    MapUtils.getString(tables.get(0), "portName") + ",做了" + MapUtils.getString(tables.get(0), "operationType") +"的操作");
                    risk.put("risk_example_time", MapUtils.getString(tables.get(0), "operateDate"));
                    risk.put("risk_example_device", MapUtils.getString(tables.get(0), "deviceName"));
                    risk.put("risk_example_port", MapUtils.getString(tables.get(0), "portName"));
                    risk.put("risk_example_operation", MapUtils.getString(tables.get(0), "operationType"));
                    risk.put("risk_example_operation_name", MapUtils.getString(tables.get(0), "operationName"));
                    risk.put("risk_example_dept", MapUtils.getString(tables.get(0), "dept"));
                }
                risk.put("table", tables);
                risks.add(risk);
            }
        }
        //先默认给个值
        data.put("risks", risks);

        data.put("riskList", riskList);
        data.put("riskDesc", String.join(",", riskList));

        //查一次全量的清单
        JSONObject param = new JSONObject();
        JSONObject retJson = idcAuditDao.queryAllOperationAuditCount(param, "ds_idc_js");
        JSONArray ret = retJson.getJSONArray("ret");
        List<Map<String, Object>> allRisks = new ArrayList<>();
        JSONObject result = ret.getJSONObject(0);
        //全省操作次数
        data.put("allTotalCount",result.getString("totalcount"));
        data.put("allAreaName", result.getString("areaname"));
        data.put("allRiskCount", sumString(result.getString("unorder"), result.getString("noorder")));
        for (Object o : ret) {
            if (o == null) {
                continue;
            }
            JSONObject operationJson = (JSONObject) o;
            Map<String, Object> allRisk = new HashMap<>();
            allRisk.put("risk", operationJson.getString("analysis"));
            allRisk.put("risk_count", operationJson.getString("cou"));
            allRisk.put("risk_percent", df.format(operationJson.getDoubleValue("cou")/MapUtils.getDoubleValue(data,"allRiskCount")*100) + "%");
            allRisks.add(allRisk);
        }
        data.put("allRisks", allRisks);
        //未按单操作次数
        data.put("allUnOrder",result.getString("unorder"));
        data.put("allUnOrderPercent", df.format(result.getDoubleValue("unorder")/result.getDoubleValue("totalcount")*100) + "%");
        //按单操作次数
        data.put("allWithOrder",result.getString("withorder"));
        data.put("allWithOrderPercent", df.format(result.getDoubleValue("withorder")/result.getDoubleValue("totalcount")*100) + "%");
        //无工单操作次数
        data.put("allNoOrder",result.getString("noorder"));
        data.put("allNoOrderPercent", df.format(result.getDoubleValue("noorder")/result.getDoubleValue("totalcount")*100) + "%");
        //系统上线时间（处理最早时间）
        SimpleDateFormat formatPg = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        data.put("sysStartDate", sdf.format(formatPg.parse(result.getString("mincreatedate"))));

        //查询复核详情
        JSONObject reviewJson = idcAuditDao.queryIDCReviewOperationAuditCount(param, "ds_idc_js");
        JSONArray reviewResult = reviewJson.getJSONArray("result");
        List<Map<String, Object>> reviewList = new ArrayList<>();
        String allReviewTotalCount = "0";
        String allDispatchCount = "0";
        String allDispatchDealCount = "0";
        String reviewNoRiskCount = "0";
        for (Object o : reviewResult) {
            JSONObject reviewObj = (JSONObject) o;
            Map<String, Object> reviewMap = new HashMap<>();
            reviewMap.put("regionName", reviewObj.getString("regionname"));
            reviewMap.put("reviewTotalCount", reviewObj.getString("totalcount"));
            reviewMap.put("dispatchCount", reviewObj.getString("dispatchcount"));
            reviewMap.put("dispatchDealCount", reviewObj.getString("dealcount"));
            reviewMap.put("reviewCount", reviewObj.getString("reviewcount"));
            reviewMap.put("openReviewCount", reviewObj.getString("openreviewcount"));
            reviewMap.put("relayReviewCount", reviewObj.getString("relayreviewcount"));
            reviewMap.put("cutReviewCount", reviewObj.getString("cutreviewcount"));
            reviewMap.put("closePortReviewCount", reviewObj.getString("closeportreviewcount"));
            reviewMap.put("notNeedReviewCount", reviewObj.getString("notneedreviewcount"));
            allReviewTotalCount = reviewObj.getString("alltotalcount");
            allDispatchCount = reviewObj.getString("alldispatchcount");
            allDispatchDealCount = reviewObj.getString("alldealcount");
            reviewNoRiskCount = reviewObj.getString("reviewnoriskcount");
            reviewList.add(reviewMap);
        }
        data.put("allReviewTotalCount", allReviewTotalCount);
        data.put("allDispatchCount", allDispatchCount);
        data.put("allDispatchDealCount", allDispatchDealCount);
        data.put("reviewNoRiskCount", reviewNoRiskCount);
        data.put("reviewList", reviewList);
        data.put("excelName", excelName);



        // 获取当前日期 先解析出本周日期和上周的日期范围
        LocalDate now = LocalDate.now();
        // 本周范围
        LocalDate thisWeekStart = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate thisWeekEnd = now.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        // 上周范围
        LocalDate lastWeekStart = thisWeekStart.minusWeeks(1);
        LocalDate lastWeekEnd = thisWeekEnd.minusWeeks(1);
        // 格式化为字符串（可选）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //根据本周和上周的时间范围，获取每周异常流量信息
        JSONObject paramWeek = new JSONObject();
        paramWeek.put("lastWeekStartDay", lastWeekStart.format(formatter));
        paramWeek.put("lastWeekEndDay", lastWeekEnd.format(formatter));
        paramWeek.put("thisWeekStartDay", thisWeekStart.format(formatter));
        paramWeek.put("thisWeekEndDay", thisWeekEnd.format(formatter));
        System.out.println(paramWeek.toJSONString());
        JSONObject portAbnormJson = idcAuditDao.queryIDCPortAbnormalTrafficCount(paramWeek, "ds_idc_js");
        JSONObject portAbnormResult = portAbnormJson.getJSONObject("result");
        String thisWeekReview = portAbnormResult.getString("thisweekreview");
        String thisWeekAdd = portAbnormResult.getString("thisweekadd");
        String thisWeekTotal = portAbnormResult.getString("thisweektotal");
        String laskWeekTotal = portAbnormResult.getString("laskweektotal");
        String portAbnormAllCount = portAbnormResult.getString("allcount");
        String portAbnormCurrentCount = portAbnormResult.getString("currentcount");

        data.put("thisWeekReview", thisWeekReview);
        data.put("thisWeekAdd", thisWeekAdd);
        data.put("thisWeekTotal", thisWeekTotal);
        data.put("laskWeekTotal", laskWeekTotal);
        data.put("traffic_abnormality_all_count", portAbnormAllCount);
        data.put("traffic_abnormality_current_count", portAbnormCurrentCount);

        //根据本周和上周的时间范围，获取每周异常速率信息
        JSONObject portRateAbnormJson = idcAuditDao.queryIDCPortRateOverrunCount(paramWeek, "ds_idc_js");
        JSONObject portAbnormRateResult = portRateAbnormJson.getJSONObject("result");
        String thisWeekRateReview = portAbnormRateResult.getString("thisweekreview");
        String thisWeekRateAdd = portAbnormRateResult.getString("thisweekadd");
        String thisWeekRateTotal = portAbnormRateResult.getString("thisweektotal");
        String laskWeekRateTotal = portAbnormRateResult.getString("laskweektotal");
        String upCount = portAbnormRateResult.getString("upcount");
        String downcount = portAbnormRateResult.getString("lowcount");
        String portAbnormRateAllCount = portAbnormRateResult.getString("allcount");
        String portAbnormRateCurrentCount = portAbnormRateResult.getString("currentcount");

        data.put("thisWeekRateReview", thisWeekRateReview);
        data.put("thisWeekRateAdd", thisWeekRateAdd);
        data.put("rate_abnormality_count", thisWeekRateTotal);
        data.put("laskWeekRateTotal", laskWeekRateTotal);
        data.put("up_rate_abnormality_count", upCount);
        data.put("down_rate_abnormality_count", downcount);
        data.put("rate_abnormality_all_count", portAbnormRateAllCount);
        data.put("rate_abnormality_current_count", portAbnormRateCurrentCount);

        //派单超时信息
        Map<String, Integer> dispatchTimeOutMap = this.queryDispatchTimeOutMap();

        //复核风险信息
        Map<String, List<PmPortNetworkOperationAudit>> reviewRiskMap = this.queryReviewRiskMap();

        //report
        IReport report = new IReport();
        report.setDetailFileName(excelName); // 详单文件名
        report.setStartDate(startTime); // 报告开始时间
        report.setEndDate(endTime);// 报告截至时间
        report.setDataStartDate(dataStartTime);
        report.setDataEndDate(dataEndTime);
        report.setReportDate(sdf.format(new Date()));
        report.getCurrentCycle().getPortAudit().setTotal(MapUtils.getIntValue(data, "totalCount", 0)); // 本周期内端口操作总数
        report.getCurrentCycle().getPortAudit().setRisk(MapUtils.getIntValue(data, "riskCount", 0)); // 本周期内端口风险操作总数
        report.getCurrentCycle().getPortAudit().setCitys(MapUtils.getString(data, "weekAreaName", "")); // 涉及地市

        // 添加操作记录列表信息。 按单操作的无需添加建议样例等  wu说按单操作不算风险
        /*report.getCurrentCycle().getPortAudit().addRiskCatalog(
                RiskItem.builder().title("按单操作")
                        .count(MapUtils.getIntValue(data, "withOrderCount", 0))
                        .percent(MapUtils.getString(data, "withOrderPercent", ""))
                        .risk(false)
                        .build()
        );*/
        Set<String> citySet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(reviewRiskMap.entrySet())) {
            int totalCount = 0;
            for (Map.Entry<String, List<PmPortNetworkOperationAudit>> entry : reviewRiskMap.entrySet()) {
                String key = entry.getKey();
                List<PmPortNetworkOperationAudit> reviewRiskList = entry.getValue();

                String[] arr = key.split("-");
                String regionName = arr[0];
                String riskAnalysis = arr[1];
                int count = reviewRiskList.size();
                StringBuilder sb = new StringBuilder();
                sb.append(format.format(reviewRiskList.get(0).getOperationDate())).append(" ").
                        append(reviewRiskList.get(0).getDeviceIp()).append(" ").
                        append(reviewRiskList.get(0).getPortName()).append(" ").
                        append(reviewRiskList.get(0).getOperationType()).append(" ").
                        append(reviewRiskList.get(0).getOperatorName()).append(" ").
                        append(reviewRiskList.get(0).getOperatorFullDept()).append(" ");

                for (PmPortNetworkOperationAudit pmPortNetworkOperationAudit : reviewRiskList) {
                    totalCount++;
                    citySet.add(pmPortNetworkOperationAudit.getOperatorRegionName());
                }

                report.getTotalCycle().getPortReviewAudit().addRiskCatalog(
                        RiskItem.builder()
                                .city(regionName)
                                .riskAnalysis(riskAnalysis)
                                .count(count)
                                .description(sb.toString())
                                .risk(true)
                                .build()
                );
            }

            report.getTotalCycle().getPortReviewAudit().getRiskCatalogs().sort(Comparator.comparing(RiskItem :: getCity));

            report.getTotalCycle().getPortReviewAudit().addRiskCatalog(
                    RiskItem.builder()
                            .city("合计")
                            .riskAnalysis("")
                            .count(totalCount)
                            .description("")
                            .risk(true)
                            .build()
            );
        }

        if (CollectionUtils.isNotEmpty(risks)) {
            for (Map<String, Object> risk : risks) {
                report.getCurrentCycle().getPortAudit().addRiskCatalog(
                        RiskItem.builder()
                                .title(MapUtils.getString(risk, "risk", ""))
                                .count(MapUtils.getIntValue(risk, "risk_count", 0))
                                .percent(MapUtils.getString(risk, "risk_percent", ""))
                                .description(MapUtils.getString(risk, "risk_desc", ""))
                                .checkRule(MapUtils.getString(risk, "risk_result", ""))
                                .suggestion(MapUtils.getString(risk, "suggest", ""))
                                .exampleOperateTime(MapUtils.getString(risk, "risk_example_time", ""))
                                .exampleDeviceIp(MapUtils.getString(risk, "risk_example_device", ""))
                                .examplePortName(MapUtils.getString(risk, "risk_example_port", ""))
                                .exampleAction(MapUtils.getString(risk, "risk_example_operation", ""))
                                .exampleOperationName(MapUtils.getString(risk, "risk_example_operation_name", ""))
                                .exampleOperationDept(MapUtils.getString(risk, "risk_example_dept", ""))
                                .risk(true)
                                .build()
                );
            }
        }

        // 流量
        report.getCurrentCycle().getPortTraffic().setExceptFreeTotal(MapUtils.getIntValue(data, "thisWeekTotal", 0)); // 本周期流量空闲总数
        report.getCurrentCycle().getPortTraffic().setLaskweekFreeTotal(MapUtils.getIntValue(data, "laskWeekTotal", 0));// 上周量空闲总数
        report.getCurrentCycle().getPortTraffic().setCurrentAdd(MapUtils.getIntValue(data, "thisWeekAdd", 0)); // 本周新增
        report.getCurrentCycle().getPortTraffic().setCurrentNormal(MapUtils.getIntValue(data, "thisWeekReview", 0)); // 本周恢复

        // 速率
        report.getCurrentCycle().getPortRate().setExceptTotal(MapUtils.getIntValue(data, "rate_abnormality_count", 0)); // 异常总数
        report.getCurrentCycle().getPortRate().setLaskweekExceptTotal(MapUtils.getIntValue(data, "laskWeekRateTotal", 0)); // 上周异常总数
        report.getCurrentCycle().getPortRate().setExceptUpperTotal(MapUtils.getIntValue(data, "up_rate_abnormality_count", 0)); // 超过
        report.getCurrentCycle().getPortRate().setExceptLowerTotal(MapUtils.getIntValue(data, "down_rate_abnormality_count", 0)); // 不足
        report.getCurrentCycle().getPortRate().setCurrentAdd(MapUtils.getIntValue(data, "thisWeekRateAdd", 0));// 本周新增
        report.getCurrentCycle().getPortRate().setCurrentNormal(MapUtils.getIntValue(data, "thisWeekRateReview", 0));// 本周恢复

        /*************上线以来*************/
        report.getTotalCycle().setStartDate(MapUtils.getString(data, "sysStartDate", "")); // 开始时间
        report.getTotalCycle().setEndDate(endTime); // 截至时间
        report.getTotalCycle().getPortAudit().setTotal(MapUtils.getIntValue(data, "allTotalCount", 0)); // 端口操作总数
        report.getTotalCycle().getPortAudit().setRisk(MapUtils.getIntValue(data, "allRiskCount", 0)); // 端口操作风险速
        report.getTotalCycle().getPortAudit().setCitys(MapUtils.getString(data, "allAreaName", "")); // 涉及地市
        report.getTotalCycle().getPortAudit().setReviewRisk(MapUtils.getIntValue(data, "allReviewTotalCount", 0));//复核风险总数
        report.getTotalCycle().getPortReviewAudit().setCitys(String.join(",", citySet));
        // 添加风险分布情况    wu说按单操作不算风险
        /*report.getTotalCycle().getPortAudit().addRiskCatalog(
                RiskItem.builder().title("按单操作")
                        .count(MapUtils.getIntValue(data, "allWithOrder", 0))
                        .percent(MapUtils.getString(data, "allWithOrderPercent", ""))
                        .risk(false)
                        .build()
        );*/
        if (CollectionUtils.isNotEmpty(allRisks)) {
            for (Map<String, Object> risk : allRisks) {
                report.getTotalCycle().getPortAudit().addRiskCatalog(
                        RiskItem.builder()
                                .title(MapUtils.getString(risk, "risk", ""))
                                .count(MapUtils.getIntValue(risk, "risk_count", 0))
                                .percent(MapUtils.getString(risk, "risk_percent", ""))
                                .risk(true)
                                .build()
                );
            }
        }

        // 添加端口操作整治情况
        if (CollectionUtils.isNotEmpty(reviewList)) {
            for (Map<String, Object> review : reviewList) {
                String regionName = MapUtils.getString(review, "regionName", "");

                report.getTotalCycle().getPortAudit().addPortImprovement(
                        PortImprovement.builder().city(MapUtils.getString(review, "regionName", ""))
                                .riskCount(MapUtils.getIntValue(review, "reviewTotalCount", 0))
                                .dispatch(MapUtils.getIntValue(review, "dispatchCount", 0))
                                .complete(MapUtils.getIntValue(review, "dispatchDealCount", 0))
                                .completePercent(df.format(MapUtils.getDoubleValue(review, "dispatchDealCount")/MapUtils.getDoubleValue(review, "dispatchCount")*100) + "%")
                                .reviewCount(MapUtils.getIntValue(review, "reviewCount", 0))
                                .reviewPercent(df.format(MapUtils.getDoubleValue(review, "reviewCount")/MapUtils.getDoubleValue(review, "reviewTotalCount")*100) + "%")
                                .openReviewCount(MapUtils.getIntValue(review, "openReviewCount", 0))
                                .relayReviewCount(MapUtils.getIntValue(review, "relayReviewCount", 0))
                                .cutReviewCount(MapUtils.getIntValue(review, "cutReviewCount", 0))
                                .closePortReviewCount(MapUtils.getIntValue(review, "closePortReviewCount", 0))
                                .notNeedReviewCount(MapUtils.getIntValue(review, "notNeedReviewCount", 0))
                                .dispatchTimeOut(MapUtils.getIntValue(dispatchTimeOutMap, regionName, 0))
                                .build()
                );
            }
            report.getTotalCycle().getPortAudit().statisticImprovements(); // 最后添加合计
        }

        report.getTotalCycle().getPortRate().setExceptTotal(MapUtils.getIntValue(data, "rate_abnormality_all_count", 0)); // 速率总数
        report.getTotalCycle().getPortRate().setCurrentEexcept(MapUtils.getIntValue(data, "rate_abnormality_current_count", 0)); // 速率当前还异常

        report.getTotalCycle().getPortTraffic().setExceptFreeTotal(MapUtils.getIntValue(data, "traffic_abnormality_all_count", 0));// 流量总数 空闲
        report.getTotalCycle().getPortTraffic().setCurrentFreeEexcept(MapUtils.getIntValue(data, "traffic_abnormality_current_count", 0)); // 流量当前还异常 空闲
        return report;
    }

    private Map<String, List<PmPortNetworkOperationAudit>> queryReviewRiskMap() {
        Map<String, List<PmPortNetworkOperationAudit>> map = new HashMap<>();

        JSONObject reviewRiskParam = new JSONObject();
        PageResponse<PmPortNetworkOperationAudit> reviewRiskInfo = otnDao.queryReviewRiskInfo(reviewRiskParam, "ds_idc_js");
        List<PmPortNetworkOperationAudit> reviewRiskList = reviewRiskInfo.getData();
        if (CollectionUtils.isNotEmpty(reviewRiskList)) {
            for (PmPortNetworkOperationAudit pmPortNetworkOperationAudit : reviewRiskList) {
                String operatorRegionName = pmPortNetworkOperationAudit.getOperatorRegionName();
                String riskAnalysis = pmPortNetworkOperationAudit.getRiskAnalysis();
                pmPortNetworkOperationAudit.setKey(operatorRegionName + "-" + riskAnalysis);
            }
            map = reviewRiskList.stream().
                    collect(Collectors.groupingBy(audit -> audit.getKey()));
        }

        return map;
    }

    private Map<String, Integer> queryDispatchTimeOutMap() {
        Map<String, Integer> map = new HashMap<>();

        JSONObject dispatchTimeOutParam = new JSONObject();
        PageResponse<PmPortNetworkOperationAudit> dispatchTimeOutInfo = otnDao.queryDispatchTimeOutCount(dispatchTimeOutParam, "ds_idc_js");
        List<PmPortNetworkOperationAudit> dispatchTimeOutList = dispatchTimeOutInfo.getData();
        if (CollectionUtils.isNotEmpty(dispatchTimeOutList)) {
            for (PmPortNetworkOperationAudit pmPortNetworkOperationAudit : dispatchTimeOutList) {
                map.put(pmPortNetworkOperationAudit.getOperatorRegionName(), pmPortNetworkOperationAudit.getCount());
            }
        }

        return map;
    }

    private String sumString(String str1, String str2) {
        if (StringUtils.isEmpty(str2) && StringUtils.isEmpty(str1)) {
            return "0";
        }
        if (StringUtils.isEmpty(str1)) {
            return str2;
        }
        if (StringUtils.isEmpty(str2)) {
            return str1;
        }
        return String.valueOf(Integer.parseInt(str1) + Integer.parseInt(str2));
    }


    @Override
    public String weekExport(JSONObject jsonObject, JSONObject statisticCount, List<JSONObject> auditDetails, File retFile, String excelName) throws Exception {
        IReport report = getReport(jsonObject, statisticCount, auditDetails, excelName);
        return idcReportService.generateWeekendReport(report, retFile, "report_template_html.xml");
    }


    public void exportDetailExcel(List<JSONObject> auditDetails, File retFile) throws Exception {
        List<Map<String, Object>> risks = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(auditDetails)) {
            for (JSONObject auditDetail : auditDetails) {
                Map<String, Object> risk = new HashMap<>();
                risk.put("risk", auditDetail.getString("riskAnalysis"));
                risk.put("risk_level", auditDetail.getString("riskLevel"));
                risk.put("risk_count", auditDetail.getString("count"));
                risk.put("risk_desc", auditDetail.getString("riskDesc"));
                risk.put("risk_result", auditDetail.getString("riskResult"));
                risk.put("suggest",auditDetail.getString("suggest"));
                JSONArray details = auditDetail.getJSONArray("detail");
                List<Map<String, Object>> tables = new ArrayList<>();
                for (Object detail : details) {
                    JSONObject riskDetail = (JSONObject) detail;
                    Map<String, Object> table = new HashMap<>();
                    table.put("areaName", StringUtils.isEmpty(riskDetail.getString("regionName")) ? "" : riskDetail.getString("regionName"));
                    table.put("operateDate", StringUtils.isEmpty(riskDetail.getString("operationDate")) ? "" : riskDetail.getString("operationDate"));
                    table.put("deviceName", StringUtils.isEmpty(riskDetail.getString("deviceIp")) ? "" : riskDetail.getString("deviceIp"));
                    table.put("portName", StringUtils.isEmpty(riskDetail.getString("portName")) ? "" : riskDetail.getString("portName"));
                    table.put("portStatus", StringUtils.isEmpty(riskDetail.getString("portStatus")) ? "" : riskDetail.getString("portStatus"));
                    table.put("custOrderCode", StringUtils.isEmpty(riskDetail.getString("crmCustOrderCode")) ? "" : riskDetail.getString("crmCustOrderCode"));
                    table.put("psoType", StringUtils.isEmpty(riskDetail.getString("psoType")) ? "" : riskDetail.getString("psoType"));
                    table.put("portAction", StringUtils.isEmpty(riskDetail.getString("operationType")) ? "" : riskDetail.getString("operationType"));
                    table.put("reviewStatus", StringUtils.isEmpty(riskDetail.getString("reviewStatusDesc")) ? "" : riskDetail.getString("reviewStatusDesc"));
                    table.put("reviewResult", StringUtils.isEmpty(riskDetail.getString("reviewRisk")) ? "" : riskDetail.getString("reviewRisk"));
                    table.put("dispatchStatus", StringUtils.isEmpty(riskDetail.getString("dispatchStatus")) ? "" : riskDetail.getString("dispatchStatus"));
                    table.put("opAuditRemark", StringUtils.isEmpty(riskDetail.getString("opAuditRemark")) ? "" : riskDetail.getString("opAuditRemark"));
                    table.put("operationName", StringUtils.isEmpty(riskDetail.getString("operationName")) ? "" : riskDetail.getString("operationName"));
                    table.put("operationType", StringUtils.isEmpty(riskDetail.getString("operationType")) ? "" : riskDetail.getString("operationType"));
                    table.put("operatorRegionName", StringUtils.isEmpty(riskDetail.getString("operatorRegionName")) ? "" : riskDetail.getString("operatorRegionName"));
                    table.put("reviewDate", StringUtils.isEmpty(riskDetail.getString("reviewDate")) ? "" : riskDetail.getString("reviewDate"));
                    table.put("busiAnalysis", StringUtils.isEmpty(riskDetail.getString("busiAnalysis")) ? "" : riskDetail.getString("busiAnalysis"));
                    table.put("itAnalysis", StringUtils.isEmpty(riskDetail.getString("itAnalysis")) ? "" : riskDetail.getString("itAnalysis"));
                    table.put("dept", StringUtils.isEmpty(riskDetail.getString("deptName")) ? "" : riskDetail.getString("deptName"));
                    table.put("reviewCleanResult", StringUtils.isEmpty(riskDetail.getString("reviewCleanResult")) ? "" : riskDetail.getString("reviewCleanResult"));
                    table.put("reviewRiskAnalysis", StringUtils.isEmpty(riskDetail.getString("reviewRiskAnalysis")) ? "" : riskDetail.getString("reviewRiskAnalysis"));
                    tables.add(table);
                }
                if (CollectionUtils.isNotEmpty(tables) && tables.get(0) != null) {
                    //示例
                    risk.put("risk_example",
                            MapUtils.getString(tables.get(0), "operateDate") + ",设备：" + MapUtils.getString(tables.get(0), "deviceName") + ",端口：" +
                                    MapUtils.getString(tables.get(0), "portName") + ",做了" + MapUtils.getString(tables.get(0), "operationType") +"的操作");
                }
                risk.put("table", tables);
                risks.add(risk);
            }
        }
        generateExcelFile(retFile, risks);
    }


    public void weekExportDetailExcel(List<JSONObject> auditDetails, File retFile) throws Exception {
        //端口操作风险数据
        List<Map<String, Object>> risks = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(auditDetails)) {
            for (JSONObject auditDetail : auditDetails) {
                Map<String, Object> risk = new HashMap<>();
                risk.put("risk", auditDetail.getString("riskAnalysis"));
                risk.put("risk_level", auditDetail.getString("riskLevel"));
                risk.put("risk_count", auditDetail.getString("count"));
                risk.put("risk_desc", auditDetail.getString("riskDesc"));
                risk.put("risk_result", auditDetail.getString("riskResult"));
                risk.put("suggest",auditDetail.getString("suggest"));
                JSONArray details = auditDetail.getJSONArray("detail");
                List<Map<String, Object>> tables = new ArrayList<>();
                for (Object detail : details) {
                    JSONObject riskDetail = (JSONObject) detail;
                    Map<String, Object> table = new HashMap<>();
                    table.put("id", StringUtils.isEmpty(riskDetail.getString("id")) ? "" : riskDetail.getString("id"));
                    table.put("auditDate", StringUtils.isEmpty(riskDetail.getString("auditDate")) ? "" : riskDetail.getString("auditDate"));
                    table.put("areaName", StringUtils.isEmpty(riskDetail.getString("regionName")) ? "" : riskDetail.getString("regionName"));
                    table.put("operateDate", StringUtils.isEmpty(riskDetail.getString("operationDate")) ? "" : riskDetail.getString("operationDate"));
                    table.put("deviceName", StringUtils.isEmpty(riskDetail.getString("deviceIp")) ? "" : riskDetail.getString("deviceIp"));
                    table.put("portName", StringUtils.isEmpty(riskDetail.getString("portName")) ? "" : riskDetail.getString("portName"));
                    table.put("portStatus", StringUtils.isEmpty(riskDetail.getString("portStatus")) ? "" : riskDetail.getString("portStatus"));
                    table.put("custOrderCode", StringUtils.isEmpty(riskDetail.getString("crmCustOrderCode")) ? "" : riskDetail.getString("crmCustOrderCode"));
                    table.put("psoType", StringUtils.isEmpty(riskDetail.getString("psoType")) ? "" : riskDetail.getString("psoType"));
                    table.put("portAction", StringUtils.isEmpty(riskDetail.getString("operationType")) ? "" : riskDetail.getString("operationType"));
                    table.put("reviewStatus", StringUtils.isEmpty(riskDetail.getString("reviewStatusDesc")) ? "" : riskDetail.getString("reviewStatusDesc"));
                    table.put("dispatchStatus", StringUtils.isEmpty(riskDetail.getString("dispatchStatus")) ? "" : riskDetail.getString("dispatchStatus"));
                    table.put("opAuditRemark", StringUtils.isEmpty(riskDetail.getString("opAuditRemark")) ? "" : riskDetail.getString("opAuditRemark"));
                    table.put("reviewResult", StringUtils.isEmpty(riskDetail.getString("reviewRisk")) ? "" : riskDetail.getString("reviewRisk"));
                    table.put("operationName", StringUtils.isEmpty(riskDetail.getString("operationName")) ? "" : riskDetail.getString("operationName"));
                    table.put("operationType", StringUtils.isEmpty(riskDetail.getString("operationType")) ? "" : riskDetail.getString("operationType"));
                    table.put("operatorRegionName", StringUtils.isEmpty(riskDetail.getString("operatorRegionName")) ? "" : riskDetail.getString("operatorRegionName"));
                    table.put("reviewDate", StringUtils.isEmpty(riskDetail.getString("reviewDate")) ? "" : riskDetail.getString("reviewDate"));
                    table.put("busiAnalysis", StringUtils.isEmpty(riskDetail.getString("busiAnalysis")) ? "" : riskDetail.getString("busiAnalysis"));
                    table.put("itAnalysis", StringUtils.isEmpty(riskDetail.getString("itAnalysis")) ? "" : riskDetail.getString("itAnalysis"));
                    table.put("dept", StringUtils.isEmpty(riskDetail.getString("deptName")) ? "" : riskDetail.getString("deptName"));
                    table.put("reviewCleanResult", StringUtils.isEmpty(riskDetail.getString("reviewCleanResult")) ? "" : riskDetail.getString("reviewCleanResult"));
                    table.put("reviewRiskAnalysis", StringUtils.isEmpty(riskDetail.getString("reviewRiskAnalysis")) ? "" : riskDetail.getString("reviewRiskAnalysis"));
                    tables.add(table);
                }
                if (CollectionUtils.isNotEmpty(tables) && tables.get(0) != null) {
                    //示例
                    risk.put("risk_example",
                            MapUtils.getString(tables.get(0), "operateDate") + ",设备：" + MapUtils.getString(tables.get(0), "deviceName") + ",端口：" +
                                    MapUtils.getString(tables.get(0), "portName") + ",做了" + MapUtils.getString(tables.get(0), "operationType") +"的操作");
                }
                risk.put("table", tables);
                risks.add(risk);
            }
        }
        //异常流量数据
        JSONObject param = new JSONObject();
        // 获取当前日期 先解析出本周日期和上周的日期范围
        LocalDate now = LocalDate.now();
        // 本周范围
        LocalDate thisWeekStart = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate thisWeekEnd = now.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        // 格式化为字符串（可选）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //根据本周和上周的时间范围，获取每周异常流量信息
        param.put("thisWeekStartDay", thisWeekStart.format(formatter));
        param.put("thisWeekEndDay", thisWeekEnd.format(formatter));
        param.put("pageSize",100000);
        PageResponse<PortAbnormalTrafficDto> portAbnormalTrafficDtoPageResponse = idcAuditDao.getPortAbnormalTraffic(param, "ds_idc_js");
        //异常速率数据
        PageResponse<PortAbnormalRateDto> portAbnormalRateDtoPageResponse = idcAuditDao.getPortRateOverrunInfo(param, "ds_idc_js");
        generateWeekExcelFile(retFile, risks, portAbnormalTrafficDtoPageResponse.getData(), portAbnormalRateDtoPageResponse.getData());
    }


    /**
     * 生成excel
     * @param excelFile
     * @return
     * @throws IOException
     */
    private void generateWeekExcelFile(File excelFile, List<Map<String, Object>> risks, List<PortAbnormalTrafficDto> portAbnormalTrafficList, List<PortAbnormalRateDto> portRateAbnormalList) throws IOException {

        try (Workbook workbook = new XSSFWorkbook()) {
            //端口操作sheet
            Sheet sheet = workbook.createSheet("历史端口稽核信息清单");
            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("风险ID");
            headerRow.createCell(1).setCellValue("稽核时间");
            headerRow.createCell(2).setCellValue("端口所属地市");
            headerRow.createCell(3).setCellValue("风险类型");
            headerRow.createCell(4).setCellValue("操作时间");
            headerRow.createCell(5).setCellValue("设备");
            headerRow.createCell(6).setCellValue("端口");
            headerRow.createCell(7).setCellValue("端口状态");
            headerRow.createCell(8).setCellValue("工单编码");
            headerRow.createCell(9).setCellValue("业务类型");
            headerRow.createCell(10).setCellValue("动作");
            headerRow.createCell(11).setCellValue("派单状态");
            headerRow.createCell(12).setCellValue("派单结果");
            headerRow.createCell(13).setCellValue("操作人员");
            headerRow.createCell(14).setCellValue("操作人员所属地市");
            headerRow.createCell(15).setCellValue("复核状态");
            headerRow.createCell(16).setCellValue("复核结果");
            headerRow.createCell(17).setCellValue("复核时间");
            headerRow.createCell(18).setCellValue("复核风险类型");
            headerRow.createCell(19).setCellValue("稽核风险消除原因");
            headerRow.createCell(20).setCellValue("业务分析");
            headerRow.createCell(21).setCellValue("IT分析");
            headerRow.createCell(22).setCellValue("部门");
            int i = 0;
            if (CollectionUtils.isNotEmpty(risks)) {
                for (Map<String, Object> risk : risks) {
                    String riskAnalysis = MapUtils.getString(risk, "risk", "");
                    List<Map<String, Object>> tables = (List<Map<String, Object>>)risk.get("table");
                    for (Map<String, Object> table : tables) {
                        Row row = sheet.createRow(i + 1);
                        row.createCell(0).setCellValue(MapUtils.getString(table, "id", ""));
                        row.createCell(1).setCellValue(MapUtils.getString(table, "auditDate", ""));
                        row.createCell(2).setCellValue(MapUtils.getString(table, "areaName", ""));
                        row.createCell(3).setCellValue(riskAnalysis);
                        row.createCell(4).setCellValue(MapUtils.getString(table, "operateDate", ""));
                        row.createCell(5).setCellValue(MapUtils.getString(table, "deviceName", ""));
                        row.createCell(6).setCellValue(MapUtils.getString(table, "portName", ""));
                        row.createCell(7).setCellValue(MapUtils.getString(table, "portStatus", ""));
                        row.createCell(8).setCellValue(MapUtils.getString(table, "custOrderCode", ""));
                        row.createCell(9).setCellValue(MapUtils.getString(table, "psoType", ""));
                        row.createCell(10).setCellValue(MapUtils.getString(table, "portAction", ""));
                        row.createCell(11).setCellValue(MapUtils.getString(table, "dispatchStatus", ""));
                        row.createCell(12).setCellValue(MapUtils.getString(table, "opAuditRemark", ""));
                        row.createCell(13).setCellValue(MapUtils.getString(table, "operationName", ""));
                        row.createCell(14).setCellValue(MapUtils.getString(table, "operatorRegionName", ""));
                        row.createCell(15).setCellValue(MapUtils.getString(table, "reviewStatus", ""));
                        row.createCell(16).setCellValue(MapUtils.getString(table, "reviewResult", ""));
                        row.createCell(17).setCellValue(MapUtils.getString(table, "reviewDate", ""));
                        row.createCell(18).setCellValue(MapUtils.getString(table, "reviewRiskAnalysis", ""));
                        row.createCell(19).setCellValue(MapUtils.getString(table, "reviewCleanResult", ""));
                        row.createCell(20).setCellValue(MapUtils.getString(table, "busiAnalysis", ""));
                        row.createCell(21).setCellValue(MapUtils.getString(table, "itAnalysis", ""));
                        row.createCell(22).setCellValue(MapUtils.getString(table, "dept", ""));
                        i++;
                    }

                }
            }
            // 自动调整列宽
            for (int z = 0; z < 12; z++) {
                sheet.autoSizeColumn(z);
            }

            //异常流量sheet
            Sheet trafficSheet = workbook.createSheet("异常流量清单");
            // 创建表头
            Row trafficRow = trafficSheet.createRow(0);
            trafficRow.createCell(0).setCellValue("分公司");
            trafficRow.createCell(1).setCellValue("数据中心");
            trafficRow.createCell(2).setCellValue("机房");
            trafficRow.createCell(3).setCellValue("设备编码");
            trafficRow.createCell(4).setCellValue("设备名称");
            trafficRow.createCell(5).setCellValue("设备IP");
            trafficRow.createCell(6).setCellValue("端口名称");
            trafficRow.createCell(7).setCellValue("端口编码");
            trafficRow.createCell(8).setCellValue("状态");
            trafficRow.createCell(9).setCellValue("业务编码");
            trafficRow.createCell(10).setCellValue("客户名称");
            trafficRow.createCell(11).setCellValue("工单编码");
            trafficRow.createCell(12).setCellValue("业务类型");
            trafficRow.createCell(13).setCellValue("工单状态");
            trafficRow.createCell(14).setCellValue("采集设备IP");
            trafficRow.createCell(15).setCellValue("采集端口编码");
            trafficRow.createCell(16).setCellValue("流出流量");
            trafficRow.createCell(17).setCellValue("流入流量");
            trafficRow.createCell(18).setCellValue("异常类型");

            if(CollectionUtils.isNotEmpty(portAbnormalTrafficList)) {
                for (int j=0; j< portAbnormalTrafficList.size(); j++) {
                    Row row = trafficSheet.createRow(j + 1);
                    row.createCell(0).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getAreaName()));
                    row.createCell(1).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getStationName()));
                    row.createCell(2).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getRoomName()));
                    row.createCell(3).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getDevCode()));
                    row.createCell(4).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getDevName()));
                    row.createCell(5).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getDevAddr()));
                    row.createCell(6).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getPortName()));
                    row.createCell(7).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getPortCode()));
                    row.createCell(8).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getUsingstate()));
                    row.createCell(9).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getBusinessCode()));
                    row.createCell(10).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getCusName()));
                    row.createCell(11).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getCrmCustOrderCode()));
                    row.createCell(12).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getGomOrderState()));
                    row.createCell(13).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getIsArchived()));
                    row.createCell(14).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getIp()));
                    row.createCell(15).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getTagkey()));
                    row.createCell(16).setCellValue(String.valueOf(portAbnormalTrafficList.get(j).getOutval()));
                    row.createCell(17).setCellValue(String.valueOf(portAbnormalTrafficList.get(j).getInVal()));
                    row.createCell(18).setCellValue(judgeStr(portAbnormalTrafficList.get(j).getAbnormalType()));
                }
            }
            //异常流量sheet
            Sheet rateSheet = workbook.createSheet("端口速率异常清单");
            // 创建表头
            Row rateRow = rateSheet.createRow(0);
            rateRow.createCell(0).setCellValue("分公司");
            rateRow.createCell(1).setCellValue("数据中心");
            rateRow.createCell(2).setCellValue("机房");
            rateRow.createCell(3).setCellValue("设备编码");
            rateRow.createCell(4).setCellValue("设备名称");
            rateRow.createCell(5).setCellValue("设备IP");
            rateRow.createCell(6).setCellValue("端口名称");
            rateRow.createCell(7).setCellValue("端口编码");
            rateRow.createCell(8).setCellValue("状态");
            rateRow.createCell(9).setCellValue("业务编码");
            rateRow.createCell(10).setCellValue("客户名称");
            rateRow.createCell(11).setCellValue("工单速率");
            rateRow.createCell(12).setCellValue("入向限速");
            rateRow.createCell(13).setCellValue("出向限速");
            rateRow.createCell(14).setCellValue("对比结果（将采控的“实际配置速率”和工单的“工单速率（限速速率）”比较，超出或不足5%为正常，否则为异常）");

            if(CollectionUtils.isNotEmpty(portRateAbnormalList)) {
                for (int j=0; j< portRateAbnormalList.size(); j++) {
                    Row row = rateSheet.createRow(j + 1);
                    row.createCell(0).setCellValue(judgeStr(portRateAbnormalList.get(j).getAreaName()));
                    row.createCell(1).setCellValue(judgeStr(portRateAbnormalList.get(j).getStationName()));
                    row.createCell(2).setCellValue(judgeStr(portRateAbnormalList.get(j).getRoomName()));
                    row.createCell(3).setCellValue(judgeStr(portRateAbnormalList.get(j).getDevCode()));
                    row.createCell(4).setCellValue(judgeStr(portRateAbnormalList.get(j).getDevName()));
                    row.createCell(5).setCellValue(judgeStr(portRateAbnormalList.get(j).getDevAddr()));
                    row.createCell(6).setCellValue(judgeStr(portRateAbnormalList.get(j).getPortName()));
                    row.createCell(7).setCellValue(judgeStr(portRateAbnormalList.get(j).getPortCode()));
                    row.createCell(8).setCellValue(judgeStr(portRateAbnormalList.get(j).getUsingstateid()));
                    row.createCell(9).setCellValue(judgeStr(portRateAbnormalList.get(j).getBusinessCode()));
                    row.createCell(10).setCellValue(judgeStr(portRateAbnormalList.get(j).getCusName()));
                    row.createCell(11).setCellValue(judgeStr(portRateAbnormalList.get(j).getConfRate()));
                    row.createCell(12).setCellValue(judgeStr(portRateAbnormalList.get(j).getInSpeed()));
                    row.createCell(13).setCellValue(judgeStr(portRateAbnormalList.get(j).getOutSpeed()));
                    row.createCell(14).setCellValue(judgeStr(portRateAbnormalList.get(j).getNumericalComparison()));
                }
            }

            // 保存Excel文件
            try (FileOutputStream out = new FileOutputStream(excelFile)) {
                workbook.write(out);
            }
        }
    }

    private String judgeStr(String value) {
        if (StringUtils.isEmpty(value)) {
            return "";
        }
        return value;
    }

    /**
     * 生成excel
     * @param excelFile
     * @return
     * @throws IOException
     */
    private void generateExcelFile(File excelFile, List<Map<String, Object>> risks) throws IOException {

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("历史端口稽核信息清单");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("风险ID");
            headerRow.createCell(1).setCellValue("稽核时间");
            headerRow.createCell(2).setCellValue("端口所属地市");
            headerRow.createCell(3).setCellValue("风险类型");
            headerRow.createCell(4).setCellValue("操作时间");
            headerRow.createCell(5).setCellValue("设备");
            headerRow.createCell(6).setCellValue("端口");
            headerRow.createCell(7).setCellValue("端口状态");
            headerRow.createCell(8).setCellValue("工单编码");
            headerRow.createCell(9).setCellValue("业务类型");
            headerRow.createCell(10).setCellValue("动作");
            headerRow.createCell(11).setCellValue("派单状态");
            headerRow.createCell(12).setCellValue("派单结果");
            headerRow.createCell(13).setCellValue("操作人员");
            headerRow.createCell(14).setCellValue("操作人员所属地市");
            headerRow.createCell(15).setCellValue("复核状态");
            headerRow.createCell(16).setCellValue("复核结果");
            headerRow.createCell(17).setCellValue("复核时间");
            headerRow.createCell(18).setCellValue("复核风险类型");
            headerRow.createCell(19).setCellValue("稽核风险消除原因");
            headerRow.createCell(20).setCellValue("业务分析");
            headerRow.createCell(21).setCellValue("IT分析");
            headerRow.createCell(22).setCellValue("部门");
            int i = 0;
            if (CollectionUtils.isNotEmpty(risks)) {
                for (Map<String, Object> risk : risks) {
                    String riskAnalysis = MapUtils.getString(risk, "risk", "");
                    List<Map<String, Object>> tables = (List<Map<String, Object>>)risk.get("table");
                    for (Map<String, Object> table : tables) {
                        Row row = sheet.createRow(i + 1);
                        row.createCell(0).setCellValue(MapUtils.getString(table, "id", ""));
                        row.createCell(1).setCellValue(MapUtils.getString(table, "auditDate", ""));
                        row.createCell(2).setCellValue(MapUtils.getString(table, "areaName", ""));
                        row.createCell(3).setCellValue(riskAnalysis);
                        row.createCell(4).setCellValue(MapUtils.getString(table, "operateDate", ""));
                        row.createCell(5).setCellValue(MapUtils.getString(table, "deviceName", ""));
                        row.createCell(6).setCellValue(MapUtils.getString(table, "portName", ""));
                        row.createCell(7).setCellValue(MapUtils.getString(table, "portStatus", ""));
                        row.createCell(8).setCellValue(MapUtils.getString(table, "custOrderCode", ""));
                        row.createCell(9).setCellValue(MapUtils.getString(table, "psoType", ""));
                        row.createCell(10).setCellValue(MapUtils.getString(table, "portAction", ""));
                        row.createCell(11).setCellValue(MapUtils.getString(table, "dispatchStatus", ""));
                        row.createCell(12).setCellValue(MapUtils.getString(table, "opAuditRemark", ""));
                        row.createCell(13).setCellValue(MapUtils.getString(table, "operationName", ""));
                        row.createCell(14).setCellValue(MapUtils.getString(table, "operatorRegionName", ""));
                        row.createCell(15).setCellValue(MapUtils.getString(table, "reviewStatus", ""));
                        row.createCell(16).setCellValue(MapUtils.getString(table, "reviewResult", ""));
                        row.createCell(17).setCellValue(MapUtils.getString(table, "reviewDate", ""));
                        row.createCell(18).setCellValue(MapUtils.getString(table, "reviewRiskAnalysis", ""));
                        row.createCell(19).setCellValue(MapUtils.getString(table, "reviewCleanResult", ""));
                        row.createCell(20).setCellValue(MapUtils.getString(table, "busiAnalysis", ""));
                        row.createCell(21).setCellValue(MapUtils.getString(table, "itAnalysis", ""));
                        row.createCell(22).setCellValue(MapUtils.getString(table, "dept", ""));
                        i++;
                    }

                }
            }


            // 自动调整列宽
            for (int z = 0; z < 12; z++) {
                sheet.autoSizeColumn(z);
            }

            // 保存Excel文件
            try (FileOutputStream out = new FileOutputStream(excelFile)) {
                workbook.write(out);
            }
        }
    }

    private static void generateStatisticInfo(JSONObject params) {

        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("startTime", params.getString("开始时间"));
        dataModel.put("endTime", params.getString("截至时间"));
        dataModel.put("name", params.getString("用户名"));
        dataModel.put("regions", params.getString("city"));
        dataModel.put("totalOperations", Integer.valueOf(params.getString("totalOperations")));
        dataModel.put("openPorts", Integer.valueOf(params.getString("openPorts")));
        dataModel.put("closePorts", Integer.valueOf(params.getString("closePorts")));
        dataModel.put("constructionCount", Integer.valueOf(params.getString("constructionCount")));
        dataModel.put("unConstructionCount", Integer.valueOf(params.getString("unConstructionCount")));
        dataModel.put("riskCount", Integer.valueOf(params.getString("riskCount")));
        dataModel.put("deviceIp", params.getString("deviceIp"));
        dataModel.put("portName", params.getString("portName"));
        Configuration cfg = new Configuration();
        cfg.setClassForTemplateLoading(IdcAuditServiceImpl.class, "/templates");
        try {
            Template template = cfg.getTemplate("template.ftl");
            params.put("result",generateText(template, dataModel));
        } catch (Exception e) {
            e.printStackTrace();
            params.put("result","获取稽核反馈信息异常");
        }
    }

    public static String generateText(Template template, Map<String, Object> dataModel) throws IOException, TemplateException {
        Writer out = new StringWriter();
        template.process(dataModel, out);
        String result =  out.toString().replace("\r", "").replace("\n", "").replace(" ", "");
        // 调用大模型接口帮我格释放下
//        StringBuffer sb = new StringBuffer();
//        sb.append("你是个文本内容优化工程师,可以把用户输入文本按照目录章节层次进行格式化，要求如下:")
//                .append("返回格式：是markdown格式;")
//                .append("返回内容：对用户输入内容按照要求格式化。对于数据可以提供一些对比、占比分析。主题是一份IDC设备端口风险操作分析报告。");
//        String res = chat(result, sb);
//        return res;
        return result;
    }


    /**
     *
     * @param content 内容
     * @param rules 规则
     * @return
     */
    private static String chat( String content, StringBuffer rules, String askParam, WebSocketSession session) {

        StringBuffer res = new StringBuffer();
        JSONObject param = new JSONObject();
        JSONArray messages = new JSONArray();
        JSONObject system = new JSONObject();
        system.put("content",rules.toString().trim().replace("{currentTime}",format.format(new Date())));
        system.put("role","system");
        messages.add(system);
        JSONObject user = new JSONObject();
        user.put("content",  content);
        user.put("role","user");
        messages.add(user);

        param.put("messages",messages);
        param.put("model","DeepSeek-R1-Q-w8a8");
//        param.put("model","DeepSeek-V3-w8a8");
        /**
         * params = {
         *     "max_input_length": 12000,     # 根据JSON实际长度调整
         *     "truncation": False,
         *     "batch_size": 1,               # 确保完整处理数组
         *     "temperature": 0.3,            # 低随机性，提高计算准确性
         *     "do_sample": False,            # 关闭采样，使用确定性输出
         *     "num_return_sequences": 1
         * }
         */
        JSONObject parameters = new JSONObject();
        parameters.put("max_input_length", rules.toString().length()*2);
//        parameters.put("max_new_tokens", 2048);
//        parameters.put("temperature", 0.2);
        param.put("parameters", parameters);
        param.put("stream",true);
        try (CloseableHttpClient client = HttpClients.createDefault()) {

            HttpPost request = new HttpPost("http://openapi.telecomjs.com:80/eop/AIGLKFPT/DeepSeekR1_w8a8_AIPlatform/completions");
            request.addHeader("X-APP-ID", "a18f86ca710840688ff0fe023ea431f2");
            request.addHeader("X-APP-KEY", "a6c5eebd88fc4330a1ea18e5276faa8a");
            request.setEntity(new StringEntity(param.toJSONString(), "UTF-8"));

//            HttpPost request = new HttpPost("http://openapi.telecomjs.com:80/eop/AIGLKFPT/DeepSeekV3_w8a8_AIPlatform/completions");
//            request.addHeader("X-APP-ID", "2191d2c95aaa43d0991bce3d9131075f");
//            request.addHeader("X-APP-KEY", "b2e19bea3b6a4da385ff3ffbe63a6a30");
//            request.setEntity(new StringEntity(param.toJSONString(), "UTF-8"));

            // 执行请求
            HttpResponse response = client.execute(request);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                try (InputStream inputStream = entity.getContent();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                    String line;
                    // 逐行读取流式响应
                    while ((line = reader.readLine()) != null) {
                        if (line != null && line.startsWith("data:")) {
                            if (!processDataChunk(line.substring(5).trim(),res,askParam ,session)) {
                                break; // 可能内容终止或者是页面发起取消
                            }
                        }

                    }
                    try {
                        reader.close();
                        inputStream.close();
                    } catch (Exception e) {

                    } finally {
                        // 用于结束标记
                        JSONObject endContent = new JSONObject();
                        endContent.put("content","<end>");
                        endContent.put("askParam",askParam); // 将解析的参数返回页面给显示详情
                        session.sendMessage(new TextMessage(endContent.toJSONString()));
                        session.close();
                    }

                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return res.toString();

    }

    private static boolean processDataChunk(String jsonData,StringBuffer sb,String askParam,WebSocketSession session) {
        try {
            if("[DONE]".equalsIgnoreCase(jsonData)) {
                return false; //结束 V3接口会返回这个标识
            }
            JsonNode node = mapper.readTree(jsonData);
            JsonNode choices = node.get("choices");
            if (choices.isArray() && choices.size() > 0) {
                JsonNode delta = choices.get(0).get("delta");
                if (delta.has("content")) {
                    System.out.print(delta.get("content").asText());
                    sb.append(delta.get("content").asText());
                    if (session != null) {
                        JSONObject content = new JSONObject();
                        content.put("content",delta.get("content").asText());
                        content.put("askParam",askParam); // 将解析的参数返回页面给显示详情
                        session.sendMessage(new TextMessage(content.toJSONString()));
                    }

                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;

        }
    }

    @Async("graphRestPool")
    @Override
    public void idcAnalysisMachineConfig(JSONObject params) {
        //查询所有IDC的网络设备
        JSONObject deviceList = idcAuditDao.qryIDCNetworkDevice(params, NRMConstants.IDC_DATA);
        List<String> ipList = new ArrayList<>();
        if (deviceList != null && deviceList.getJSONArray("data") != null) {
            JSONArray deviceListArr = deviceList.getJSONArray("data");
            for (Object o : deviceListArr) {
                if (o == null) {
                    continue;
                }
                JSONObject device = (JSONObject) o;
                String ipAddr = device.getString("ipaddr");
                if (StringUtils.isEmpty(ipAddr)) {
                    continue;
                }
                ipList.add(ipAddr);
            }
        }
        if (CollectionUtils.isEmpty(ipList)) {
            return;
        }
        //先获取所有需要解析的IDC整机配置
        JSONObject idcMachine = idcAuditDao.qryIDCWholeConfig(params, NRMConstants.SHARDING_CODE);
        //将设备配置按照设备IP分组，然后每组在获取文件的时候取最大的文件，保存为文件，然后解析文件
        Map<String,String> initPortMap = new HashMap<>();
        Map<String,String> failPortMap = new HashMap<>();
        if (idcMachine != null && idcMachine.getJSONArray("data") != null) {
            JSONArray idcMachineList = idcMachine.getJSONArray("data");
            Map<String, List<JSONObject>> idcMachineMap = new HashMap<>();
            for (Object o : idcMachineList) {
                if (o == null) {
                    continue;
                }
                JSONObject pmNetConfigJson = (JSONObject) o;
                String manageIp = pmNetConfigJson.getString("manageIp");
                if (StringUtils.isEmpty(manageIp) || !ipList.contains(manageIp)) {
                    continue;
                }
                idcMachineMap.computeIfAbsent(manageIp, k -> new ArrayList<>());
                idcMachineMap.get(manageIp).add(pmNetConfigJson);
            }
            for (String manageIp : idcMachineMap.keySet()) {
                List<JSONObject> deviceInfos = idcMachineMap.get(manageIp);
                //获取最大的文件
                if (CollectionUtils.isEmpty(deviceInfos)) {
                    continue;
                }
                Map<String, Integer> sizeMap = new HashMap<>();
                Map<String, String> contentMap = new HashMap<>();
                for (JSONObject deviceInfo : deviceInfos) {
                    String id = deviceInfo.getString("id");
                    //根据id查询整机配置报文
                    try {
                        JSONObject getResult = configDao.getNetConfigFile(new BigDecimal(id), NRMConstants.SHARDING_CODE);
                        String content = getResult.getString("content");
                        if (StringUtils.isEmpty(content)) {
                            failPortMap.put(manageIp, "文件为空");
                            continue;
                        }
                        int length = content.length();
                        sizeMap.put(id, length);
                        contentMap.put(id, content);
                    } catch (Exception e) {
                        failPortMap.put(manageIp, "获取整机配置报文失败："+ e.getMessage());
                    }
                }
                //获取出最大的文件
                sizeMap.entrySet().stream().sorted((o1, o2) -> o2.getValue() - o1.getValue()).findFirst().ifPresent(entry -> {
                    String content = contentMap.get(entry.getKey());
                    //保存文件
                    String name = "gatherFile/rate" + entry.getKey() + ".txt";
                    write(content, name);
                    initPortMap.put(manageIp, name);
                });
            }
            List<JSONObject> deviceInfos = new ArrayList<>();
            //过滤失败的map在初始化的map中没有的，作为取数失败的记录到pm_init_gather_port表中
            failPortMap.entrySet().stream().filter(entry -> !initPortMap.containsKey(entry.getKey())).forEach(entry -> {
                JSONObject param = new JSONObject();
                param.put("deviceIp", entry.getKey());
                param.put("asalysisResult", entry.getValue());
                param.put("asalysisStatus", 2);
                deviceInfos.add(param);
            });
            //将成功的需要解析的记录也记录到pm_init_gather_port表中
            for (String manageIp : initPortMap.keySet()) {
                JSONObject param = new JSONObject();
                if (manageIp.contains("/32")) {
                    param.put("deviceIp", manageIp.split("/")[0]);
                } else {
                    param.put("deviceIp", manageIp);
                }
                param.put("filePath", initPortMap.get(manageIp));
                param.put("asalysisStatus", 0);
                param.put("method", "rateTemplate");
                deviceInfos.add(param);
            }
            JSONObject param = new JSONObject();
            param.put("list", deviceInfos);
            idcAuditDao.insertInitGatherPort(param, NRMConstants.SHARDING_CODE);
        }
        Map<String, List<JSONObject>> groupDeviceMap = qryIdcAnalysisMachinePortInfo("rateTemplate");
        if (MapUtils.isNotEmpty(groupDeviceMap)) {
            for (String num : groupDeviceMap.keySet()) {
                analysisPortService.groupRateTemplate(groupDeviceMap.get(num), num);
            }
        }
    }

    /**
     * 查询出需要解析的设备文件，比较文件后，记录到pm_init_gather_port表中
     * @param params
     * @return
     */
    @Async("graphRestPool")
    @Override
    public void idcAnalysisMachinePortInfo(JSONObject params) {
        //查询所有IDC的网络设备
        JSONObject deviceList = idcAuditDao.qryIDCNetworkDevice(params, NRMConstants.IDC_DATA);
        List<String> ipList = new ArrayList<>();
        if (deviceList != null && deviceList.getJSONArray("data") != null) {
            JSONArray deviceListArr = deviceList.getJSONArray("data");
            for (Object o : deviceListArr) {
                if (o == null) {
                    continue;
                }
                JSONObject device = (JSONObject) o;
                String ipAddr = device.getString("ipaddr");
                if (StringUtils.isEmpty(ipAddr)) {
                    continue;
                }
                ipList.add(ipAddr);
            }
        }
        if (CollectionUtils.isEmpty(ipList)) {
            return;
        }
        //先获取所有需要解析的IDC整机配置
        JSONObject idcMachine = idcAuditDao.qryIDCWholeConfig(params, NRMConstants.SHARDING_CODE);
        //将设备配置按照设备IP分组，然后每组在获取文件的时候取最大的文件，保存为文件，然后解析文件
        Map<String,String> initPortMap = new HashMap<>();
        Map<String,String> failPortMap = new HashMap<>();
        if (idcMachine != null && idcMachine.getJSONArray("data") != null) {
            JSONArray idcMachineList = idcMachine.getJSONArray("data");
            Map<String, List<JSONObject>> idcMachineMap = new HashMap<>();
            for (Object o : idcMachineList) {
                if (o == null) {
                    continue;
                }
                JSONObject pmNetConfigJson = (JSONObject) o;
                String manageIp = pmNetConfigJson.getString("manageIp");
                if (StringUtils.isEmpty(manageIp) || !ipList.contains(manageIp)) {
                    continue;
                }
                idcMachineMap.computeIfAbsent(manageIp, k -> new ArrayList<>());
                idcMachineMap.get(manageIp).add(pmNetConfigJson);
            }
            for (String manageIp : idcMachineMap.keySet()) {
                List<JSONObject> deviceInfos = idcMachineMap.get(manageIp);
                //获取最大的文件
                if (CollectionUtils.isEmpty(deviceInfos)) {
                    continue;
                }
                Map<String, Integer> sizeMap = new HashMap<>();
                Map<String, String> contentMap = new HashMap<>();
                for (JSONObject deviceInfo : deviceInfos) {
                    String id = deviceInfo.getString("id");
                    //根据id查询整机配置报文
                    try {
                        JSONObject getResult = configDao.getNetConfigFile(new BigDecimal(id), NRMConstants.SHARDING_CODE);
                        String content = getResult.getString("content");
                        if (StringUtils.isEmpty(content)) {
                            failPortMap.put(manageIp, "文件为空");
                            continue;
                        }
                        int length = content.length();
                        sizeMap.put(id, length);
                        contentMap.put(id, content);
                    } catch (Exception e) {
                        failPortMap.put(manageIp, "获取整机配置报文失败："+ e.getMessage());
                    }
                }
                //获取出最大的文件
                sizeMap.entrySet().stream().sorted((o1, o2) -> o2.getValue() - o1.getValue()).findFirst().ifPresent(entry -> {
                    String content = contentMap.get(entry.getKey());
                    //保存文件
                    String name = "gatherFile/" + entry.getKey() + ".txt";
                    write(content, name);
                    initPortMap.put(manageIp, name);
                });
            }
            List<JSONObject> deviceInfos = new ArrayList<>();
            //过滤失败的map在初始化的map中没有的，作为取数失败的记录到pm_init_gather_port表中
            failPortMap.entrySet().stream().filter(entry -> !initPortMap.containsKey(entry.getKey())).forEach(entry -> {
                JSONObject param = new JSONObject();
                param.put("deviceIp", entry.getKey());
                param.put("asalysisResult", entry.getValue());
                param.put("asalysisStatus", 2);
                deviceInfos.add(param);
            });
            //将成功的需要解析的记录也记录到pm_init_gather_port表中
            for (String manageIp : initPortMap.keySet()) {
                JSONObject param = new JSONObject();
                if (manageIp.contains("/32")) {
                    param.put("deviceIp", manageIp.split("/")[0]);
                } else {
                    param.put("deviceIp", manageIp);
                }
                param.put("filePath", initPortMap.get(manageIp));
                param.put("asalysisStatus", 0);
                param.put("method", "port");
                deviceInfos.add(param);
            }
            JSONObject param = new JSONObject();
            param.put("list", deviceInfos);
            idcAuditDao.insertInitGatherPort(param, NRMConstants.SHARDING_CODE);
        }
        Map<String, List<JSONObject>> groupDeviceMap = qryIdcAnalysisMachinePortInfo("port");
        if (MapUtils.isNotEmpty(groupDeviceMap)) {
            for (String num : groupDeviceMap.keySet()) {
                analysisPortService.groupAnalysisPortInfo(groupDeviceMap.get(num), num);
            }
        }

    }

    @Override
    public Map<String, List<JSONObject>> qryIdcAnalysisMachinePortInfo(String method){
        JSONObject params = new JSONObject();
        params.put("asalysisStatus", 0);
        params.put("method", method);
        JSONObject jsonObject = idcAuditDao.qryIdcNeedAnalysisDevice(params, NRMConstants.SHARDING_CODE);
        //先将所有的整机配置根据进行分组
        Map<String, List<JSONObject>> groupDeviceMap = new HashMap<>();
        if (jsonObject != null && jsonObject.getJSONArray("data") != null) {
            JSONArray idcMachineList = jsonObject.getJSONArray("data");
            for (Object o : idcMachineList) {
                if (o == null) {
                    continue;
                }
                JSONObject pmNetConfigJson = (JSONObject) o;
                String num = pmNetConfigJson.getString("num");
                if (StringUtils.isEmpty(num)) {
                    continue;
                }
                groupDeviceMap.computeIfAbsent(num, k -> new ArrayList<>());
                groupDeviceMap.get(num).add(pmNetConfigJson);
            }
        }
        return groupDeviceMap;
    }

    private void write(String content, String path) {
        File file = new File(path);
        try {
            String directoryPath = "gatherFile";
            File directory = new File(directoryPath);
            // 如果目录不存在，则创建目录
            if (!directory.exists()) {
                directory.mkdirs(); // mkdirs()会创建所有不存在的父目录
            }
            // 创建文件
            file.createNewFile();
        } catch (IOException e) {
            log.error("Error creating file: " + e.getMessage());
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            writer.write(content);
        } catch (IOException e) {
            log.error("生成文件失败：{}", e.getMessage());
        }
    }

    @Async("graphRestPool")
    @Override
    public void idcAnalysisMachineBookSize() {
        //查询所有IDC的网络设备
        JSONObject deviceList = idcAuditDao.qryIDCNetworkDevice(new JSONObject(), NRMConstants.IDC_DATA);
        List<String> ipList = new ArrayList<>();
        if (deviceList != null && deviceList.getJSONArray("data") != null) {
            JSONArray deviceListArr = deviceList.getJSONArray("data");
            for (Object o : deviceListArr) {
                if (o == null) {
                    continue;
                }
                JSONObject device = (JSONObject) o;
                String ipAddr = device.getString("ipAddr");
                if (StringUtils.isEmpty(ipAddr)) {
                    continue;
                }
                ipList.add(ipAddr);
            }
        }
        if (CollectionUtils.isEmpty(ipList)) {
            return;
        }
        //先获取所有需要解析的IDC整机配置
        JSONObject idcMachine = idcAuditDao.qryIDCWholeConfig(new JSONObject(), NRMConstants.SHARDING_CODE);
        if (idcMachine != null && idcMachine.getJSONArray("data") != null) {
            JSONArray idcMachineList = idcMachine.getJSONArray("data");
            List<JSONObject> bookSizeList = new ArrayList<>();
            for (Object o : idcMachineList) {
                JSONObject bookSize = new JSONObject();
                JSONObject pmNetConfigJson = (JSONObject) o;
                if (pmNetConfigJson == null) {
                    continue;
                }
                bookSize.put("mgmtIp", pmNetConfigJson.getString("manageIp"));
                String id = pmNetConfigJson.getString("id");
                bookSize.put("id", id);
                //根据id查询整机配置报文
                JSONObject getResult  =  configDao.getNetConfigFile(new BigDecimal(id), NRMConstants.SHARDING_CODE);
                JSONObject content = getResult.getJSONObject("content");
                String key = null;
                for (String contenKey : content.keySet()) {
                    if (contenKey.startsWith("IDC_Cai")) {
                        key = contenKey;
                    }
                }
                JSONObject unitInfo = content.getJSONObject("UnitInfo");
                if (unitInfo == null) {
                    bookSize.put("booksize", 0);
                    bookSizeList.add(bookSize);
                    continue;
                }
                String mgmtIp = unitInfo.getString("MgmtIp");
                if (StringUtils.isEmpty(mgmtIp) || !ipList.contains(mgmtIp)) {
                    bookSize.put("booksize", 0);
                    bookSizeList.add(bookSize);
                    continue;
                }
                if (StringUtils.isEmpty(key)) {
                    bookSize.put("booksize", 0);
                    bookSizeList.add(bookSize);
                    continue;
                }
                String result = content.getString(key);
                if (StringUtils.isEmpty(result)) {
                    bookSize.put("booksize", 0);
                    bookSizeList.add(bookSize);
                    continue;
                }
                int length = result.length();
                bookSize.put("booksize", length);
                bookSizeList.add(bookSize);
            }
            JSONObject param = new JSONObject();
            param.put("list", bookSizeList);
            idcAuditDao.insertPmBooksize(param, NRMConstants.SHARDING_CODE);
        }
    }

    @Override
    public void insertIdcPortGatherLock(String lockSign) {
        JSONObject param = new JSONObject();
        param.put("lockSign", lockSign);
        idcAuditDao.insertPmPortGatherLock(param, NRMConstants.SHARDING_CODE);
    }

    @Override
    public JSONObject qryIdcPortGatherLock(String lockSign) {
        JSONObject param = new JSONObject();
        param.put("lockSign", lockSign);
        return idcAuditDao.qryPmPortGatherLock(param, NRMConstants.SHARDING_CODE);
    }

    @Override
    public void auditPmPortRateOverrun() throws Exception {
        //查询出所有AP开头的实占用的端口，取出端口的配置速率，并关联采集到的端口实际配置的速率
        // 调用api平台
        PageResponse<PortRateOverrunDto> portRateOverrunDtoPageResponse = idcAuditDao.queryIDCPortRateOverrunAuditInfo(new JSONObject(), 10000, 1, NRMConstants.IDC_DATA);
        if (portRateOverrunDtoPageResponse == null || CollectionUtils.isEmpty(portRateOverrunDtoPageResponse.getData())) {
            throw new Exception("未查询到端口配置信息");
        }
        //先判断是否有重复的端口，如果有，则根据端口名称重新查询下实际配置速率
        List<PortRateOverrunDto> portRateOverrunDtos = new ArrayList<>();
        Map<BigDecimal, List<PortRateOverrunDto>> portRateOverrunMap = portRateOverrunDtoPageResponse.getData().stream().collect(Collectors.groupingBy(PortRateOverrunDto::getPortId));
        portRateOverrunMap.forEach((key, value) -> {
            if (value.size() > 1) {
                boolean hasPort = false;
                for (PortRateOverrunDto portRateOverrunDto : value) {
                    if (portRateOverrunDto.getIdcPortName().equals(portRateOverrunDto.getPortName())) {
                        portRateOverrunDtos.add(portRateOverrunDto);
                        hasPort = true;
                        break;
                    }
                }
                if (!hasPort) {
                    portRateOverrunDtos.add(value.get(0));
                }
            }
            if (value.size() == 1) {
                portRateOverrunDtos.add(value.get(0));
            }
        });
        //分析匹配到速率的端口，±5%的端口都为合规，其余端口不合规，将稽核结果记录到表中
        for (PortRateOverrunDto portRateOverrunDto : portRateOverrunDtos) {
            String cjSpeed = StringUtils.isEmpty(portRateOverrunDto.getInSpeed())?portRateOverrunDto.getOutSpeed():portRateOverrunDto.getInSpeed();
            if (StringUtils.isEmpty(cjSpeed)) {
                portRateOverrunDto.setAuditResult("未采集到数据");
                continue;
            }
            if (StringUtils.isEmpty(portRateOverrunDto.getConfRate())) {
                portRateOverrunDto.setAuditResult("配置速率为空");
                continue;
            }
            portRateOverrunDto.setAuditResult("正常");
            if (isWithinTolerance(Double.parseDouble(cjSpeed), Double.parseDouble(portRateOverrunDto.getConfRate())) < 0.95) {
                portRateOverrunDto.setAuditResult("超出");
            }
            if (isWithinTolerance(Double.parseDouble(cjSpeed), Double.parseDouble(portRateOverrunDto.getConfRate())) > 1.05) {
                portRateOverrunDto.setAuditResult("不足");
            }
        }

        JSONObject param = new JSONObject();
        param.put("list", portRateOverrunDtos);
        idcAuditDao.batchInsertPmPortRateOverrun(param, NRMConstants.IDC_DATA);
    }

    private double isWithinTolerance(double num1, double num2) {
        if (num1 == 0 && num2 == 0) return 1;
        if (num1 == 0 || num2 == 0) return 0;
        double ratio = num2 / num1;
        return ratio;  // ±5%范围
    }
}
