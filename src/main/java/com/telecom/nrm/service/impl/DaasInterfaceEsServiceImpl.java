package com.telecom.nrm.service.impl;

import com.telecom.nrm.dto.ApiCodeCountDTO;
import com.telecom.nrm.dto.ApiCodeCountResponse;
import com.telecom.nrm.dto.AppApiCountDTO;
import com.telecom.nrm.dto.AppApiCountResponse;
import com.telecom.nrm.service.DaasInterfaceEsService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregation;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;

@Service
@Slf4j
public class DaasInterfaceEsServiceImpl implements DaasInterfaceEsService {

    @Autowired
    private RestHighLevelClient esClient;


    @Override
    public ApiCodeCountResponse getApiCodeCallCounts(String startTime, String endTime, int page, int size) {

        List<ApiCodeCountDTO> result = new ArrayList<>();
        long totalCount = 0;

        try {
            SearchRequest searchRequest = new SearchRequest("daas-interface-rest-log-*");

            // 时间范围查询
            RangeQueryBuilder rangeQuery = new RangeQueryBuilder("@timestamp-service")
                    .gte(startTime)
                    .lte(endTime);

            // 构造 bool 查询：包含时间范围 + 排除 api-code.keyword 为空
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(rangeQuery)
                    .mustNot(QueryBuilders.termQuery("api-code.keyword", "")); // 排除空字段

            // Terms 聚合：按 api-code.keyword 分组 + 按 doc_count 降序
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQuery)
                    .aggregation(
                            AggregationBuilders.terms("api_code")
                                    .field("api-code.keyword") // 必须使用 keyword 字段
                                    .order(org.elasticsearch.search.aggregations.BucketOrder.count(false)) // 按 doc_count 降序
                                    .size(100000000) // 设置一个足够大的 size，防止截断
                    )
                    .size(0); // 不返回 hits，只看 aggregations

            searchRequest.source(sourceBuilder);

            SearchResponse response = esClient.search(searchRequest, RequestOptions.DEFAULT);

            Terms apiCodeTerms = response.getAggregations().get("api_code");
            List<? extends Terms.Bucket> buckets = apiCodeTerms.getBuckets();

            totalCount = buckets.size();



            // 伪分页逻辑：从 fullList 中按 page/size 切片
            int fromIndex = Math.max((page - 1) * size, 0);
            int toIndex = Math.min(fromIndex + size, buckets.size());

            for (int i = fromIndex; i < toIndex; i++) {
                Terms.Bucket bucket = buckets.get(i);
                String apiCode = bucket.getKeyAsString();
                long count = bucket.getDocCount();
                result.add(new ApiCodeCountDTO(apiCode, count));
            }

        } catch (IOException e) {
            log.error("Elasticsearch query failed", e);
        }

        return new ApiCodeCountResponse(totalCount, result);
    }

    @Override
    public AppApiCountResponse getAppApiCallCounts(String startTime, String endTime, int page, int size) {
        List<AppApiCountDTO> result = new ArrayList<>();
        try {
            SearchRequest searchRequest = new SearchRequest("daas-interface-rest-log-*");

            RangeQueryBuilder rangeQuery = new RangeQueryBuilder("@timestamp-service")
                    .gte(startTime)
                    .lte(endTime);

            // 构造 bool 查询：包含时间范围 + 排除 app-id 或 api-code 为空
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(rangeQuery)
                    .mustNot(QueryBuilders.termQuery("app-id.keyword", ""))
                    .mustNot(QueryBuilders.termQuery("api-code.keyword", ""));

            // terms 聚合 app-id.keyword，size控制返回多少不同appId
            TermsAggregationBuilder appIdAgg = AggregationBuilders.terms("app_ids")
                    .field("app-id.keyword")
                    .size(100000000); // 这个数量可根据预估调整

            // terms 聚合 api-code.keyword，size控制返回多少不同apiCode
            TermsAggregationBuilder apiCodeAgg = AggregationBuilders.terms("api_codes")
                    .field("api-code.keyword")
                    .size(100000000); // 这个数量可根据预估调整

            appIdAgg.subAggregation(apiCodeAgg);

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQuery)
                    .aggregation(appIdAgg)
                    .size(0);

            searchRequest.source(sourceBuilder);

            SearchResponse response = esClient.search(searchRequest, RequestOptions.DEFAULT);

            Terms appIds = response.getAggregations().get("app_ids");

            // 收集所有组合结果
            for (Terms.Bucket appIdBucket : appIds.getBuckets()) {
                String appId = appIdBucket.getKeyAsString();
                Terms apiCodes = appIdBucket.getAggregations().get("api_codes");

                for (Terms.Bucket apiCodeBucket : apiCodes.getBuckets()) {
                    String apiCode = apiCodeBucket.getKeyAsString();
                    long count = apiCodeBucket.getDocCount();

                    result.add(new AppApiCountDTO(appId, apiCode, count));
                }
            }

            // 按调用次数降序排序
            result.sort(Comparator.comparingLong(AppApiCountDTO::getCallCount).reversed());

            // 伪分页：subList 根据 page 和 size 计算开始和结束索引
            int fromIndex = (page - 1) * size;
            int toIndex = Math.min(fromIndex + size, result.size());

            if (fromIndex >= result.size()) {
                return new AppApiCountResponse(0,new ArrayList<>());
            }

            return new AppApiCountResponse(result.size(),result.subList(fromIndex, toIndex));

        } catch (IOException e) {
            log.error("Elasticsearch query failed", e);
        }
        return new AppApiCountResponse(result.size(),result);
    }


}
