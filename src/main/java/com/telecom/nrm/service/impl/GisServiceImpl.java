package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.GisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class GisServiceImpl implements GisService {

    // 地球半径，单位为米
    private static final double EARTH_RADIUS = 6371e3;

    /**
     * 用于计算2个点之间的距离
     * @param lat1
     * @param lon1
     * @param lat2
     * @param lon2
     * @return
     */
    @Override
    public double haversine(double lat1, double lon1, double lat2, double lon2) {
        // 将角度转换为弧度
        lat1 = Math.toRadians(lat1);
        lon1 = Math.toRadians(lon1);
        lat2 = Math.toRadians(lat2);
        lon2 = Math.toRadians(lon2);

        // 计算差值
        double dlat = lat2 - lat1;
        double dlon = lon2 - lon1;

        // Haversine公式
        double a = Math.sin(dlat / 2) * Math.sin(dlat / 2) +
                Math.cos(lat1) * Math.cos(lat2) *
                        Math.sin(dlon / 2) * Math.sin(dlon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        // 计算距离
        double distance = EARTH_RADIUS * c;

        return distance;
    }


}
