package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.etl.ETLTask;
import com.telecom.nrm.domain.etl.ETLTaskStatus;
import com.telecom.nrm.service.ETLTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class ETLTaskServiceImpl implements ETLTaskService {

    String convertAreaCode(String areaCode) {
        areaCode = areaCode.toUpperCase();
        String result = NRMConstants.AREA_CODE_MAP.get(areaCode);
        if (ObjectUtil.isNotEmpty(result)) {
            return result;
        }else{
            return areaCode;
        }
    }

    @Autowired
    RestTemplate restTemplate;
    @Value("${etl-rest.url}")
    String etlUrl;

    @Override
    public ETLTaskStatus submitTask(ETLTask task) {
        log.info("Submitting etl task {}", task);
        log.info("ETL url: {}", etlUrl);
        task.setAreaCode(convertAreaCode(task.getAreaCode()));
        restTemplate.postForObject(etlUrl+"/teleetl/runningTask/submit", JSONObject.toJSON(task).toString(), ETLTaskStatus.class);
        return status(task);
        // return etlTaskStatus;
    }

    @Override
    public ETLTaskStatus status(ETLTask task) {
        log.info("Submitting etl task {}", task);
        log.info("ETL url: {}", etlUrl);
        task.setAreaCode(convertAreaCode(task.getAreaCode()));
        ETLTaskStatus etlTaskStatus = restTemplate.postForObject(etlUrl+"/teleetl/runningTask/status", JSONObject.toJSON(task).toString(), ETLTaskStatus.class);
        return etlTaskStatus;
    }
}
