package com.telecom.nrm.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.HunderRiverService;
import com.telecom.nrm.utils.HttpRequests;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class HunderRiverServiceImpl implements HunderRiverService {
    @Value("${hundredRivers.pon.url}")
    String ponUrl;
    @Value("${hundredRivers.ipran.url}")
    String ipranUrl;
    @Value("${hundredRivers.newOldCity.url}")
    String newCityUrl;
    @Value("${hundredRivers.four.url}")
    String fourLogicUrl;
    @Value("${hundredRivers.deviceAlarm.url}")
    String deviceAlarmUrl;
    @Value("${hundredRivers.zd.url}")
    String zdUrl;
    @Value("${hundredRivers.ipm.url}")
    String ipmUrl;
    @Value("${hundredRivers.alarm.url}")
    String alarmUrl;
    @Value("${hundredRivers.pon.appKey}")
    String appKey;
    @Value("${hundredRivers.pon.appSecret}")
    String appSecret;

    @Value("${hundredRivers.zdNetFaultQuery.url}")
    String zdNetFaultQueryUrl;
    @Value("${hundredRivers.zdNetFaultQuery.appKey}")
    String zdNetFaultQueryAppKey;
    @Value("${hundredRivers.zdNetFaultQuery.appSecret}")
    String zdNetFaultQueryAppSecret;

    @Value("${hundredRivers.zdCustOrderQuery.url}")
    String zdCustOrderQueryUrl;
    @Value("${hundredRivers.zdCustOrderQuery.appKey}")
    String zdCustOrderQueryAppKey;
    @Value("${hundredRivers.zdCustOrderQuery.appSecret}")
    String zdCustOrderQueryAppSecret;

    @Override
    public JSONObject zdNetFaultQuery(JSONObject jsonObject) throws IOException {
        Map<String, String> header = new HashMap<>();
        header.put("appKey", zdNetFaultQueryAppKey);
        header.put("appSecret", zdNetFaultQueryAppSecret);
        String result = HttpRequestsUtils.sendPost(zdNetFaultQueryUrl, header, jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject zdCustOrderQuery(JSONObject jsonObject) throws IOException {
        Map<String, String> header = new HashMap<>();
        header.put("appKey", zdCustOrderQueryAppKey);
        header.put("appSecret", zdCustOrderQueryAppSecret);
        String result = HttpRequestsUtils.sendPost(zdCustOrderQueryUrl, header, jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject queryAlarmByAccessNumber(JSONObject jsonObject) throws IOException {
        String result = HttpRequestsUtils.sendPost(alarmUrl, getCommonHeader(), jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject queryPon(JSONObject jsonObject) {
        String result = HttpRequests.sendPost(ponUrl, getCommonHeader(), jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject queryIpran(JSONObject jsonObject) {
        String result = HttpRequests.sendPost(ipranUrl, getCommonHeader(), jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject queryNewCity(JSONObject jsonObject) {
        String result = HttpRequests.sendPost(newCityUrl, getCommonHeader(), jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject queryFourLogic(JSONObject jsonObject) {
        String result = HttpRequests.sendPost(fourLogicUrl, getCommonHeader(), jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject deviceAlarm(JSONObject jsonObject) {
        String result = HttpRequests.sendPost(deviceAlarmUrl, getCommonHeader(), jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject zd(JSONObject jsonObject) {
        String result = HttpRequests.sendPost(zdUrl, getCommonHeader(), jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject common(JSONObject jsonObject) {
        return null;
    }

    @Override
    public JSONObject ipm(JSONObject jsonObject) {
        String result = HttpRequests.sendPost(ipmUrl, getCommonHeader(), jsonObject);
        return JSONObject.parseObject(result);
    }

    @Override
    public JSONObject ipran(JSONObject jsonObject) {
        Map<String, String> header = new HashMap<>();
        header.put("appKey", "2cb7f8e46a9cc539");
        header.put("appSecret", "a57def9b2cb7f8e46a9cc5397bdb5541");
        String result = HttpRequests.sendPost(" http://sjgxpt.telecomjs.com:8090/gxpt/dataservice/api/queryData/ipran", header, jsonObject);
        return JSONObject.parseObject(result);
    }

    public Map<String, String> getCommonHeader() {
        Map<String, String> header = new HashMap<>();
        header.put("appKey", appKey);
        header.put("appSecret", appSecret);
        return header;
    }
}
