package com.telecom.nrm.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CircuitSingleServiceImpl implements  CircuitSingleService{

    @Autowired
    MstpService mstpService;

    @Autowired
    GraphService graphService;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    OptRoadPairService optRoadPairService;

    @Autowired
    FrontHoleService frontHoleService;

    void buildTrees(JSONObject data) {
        graphService.buildReverseTree(data,"政企业务", "管道段","mstp_circuits");
        graphService.mapProperty(data,"管道段", "mstp_circuits", mapFun);
        graphService.buildReverseTree(data,"政企业务", "光路","mstp_circuits");
        graphService.mapProperty(data,"光路", "mstp_circuits", mapFun);
        graphService.buildReverseTree(data,"政企业务", "光缆","mstp_circuits");
        graphService.mapProperty(data,"光缆", "mstp_circuits", mapFun);
        graphService.buildReverseTree(data,"光路", "管道段","opt_road_list");
        graphService.mapProperty(data, "管道段","opt_road_list", mapFun);
        graphService.buildReverseTree(data,"光路", "光缆","opt_road_list");
        graphService.mapProperty(data,"光缆", "opt_road_list", mapFun);

        graphService.buildTree(data,"局向光纤", "管道段","pipe_segment_list");
        graphService.mapProperty(data,"局向光纤", "pipe_segment_list", mapFun);

        graphService.buildReverseTree(data,"政企业务", "设备","mstp_circuits");
        graphService.mapProperty(data,"设备", "mstp_circuits", mapFun);

        graphService.buildTree(data,"政企业务","光路","opt_road_list");
        // graphService.mapProperty(data,"政企业务", "opt_road_list", mapFun);

        graphService.buildTree(data,"光路","管道段","pipe_segment_list");
        graphService.mapProperty(data,"光路", "pipe_segment_list", mapFun);

        graphService.buildTree(data,"光路","光缆","cable_list");
        graphService.mapProperty(data,"光路", "cable_list", mapFun);

        graphService.buildReverseTree(data,"端口", "板卡","mstp_circuits");
        graphService.mapProperty(data,"板卡", "mstp_circuits", mapFun);
    }

    Function<JSONObject,JSONObject> mapFun = (d)->{
        JSONObject result = new JSONObject();
        Set<String> keys = d.keySet();
        if (ObjectUtil.isNotEmpty(keys)) {
            // log.info("keys={}",keys.toString());
            for (String key: keys) {
                if (! (d.get(key) instanceof Collection)) {
                    result.put(key,d.get(key));
                };
            }
        }
        return result;
    };

    //过滤接入光路对
    public JSONObject filterAccessOptPair(List<JSONObject> optPairList, List<JSONObject> accessDevices){



        int count =0;
        List<JSONObject> accessOptPairList = new ArrayList<>();
        for(JSONObject accessDevice : accessDevices){
            // 光路对中的两条光路均为接入光路

            List<JSONObject> accessOptPairs = optPairList.stream().filter(optPair -> {
                JSONArray opt_road_list= optPair.getJSONObject("result").getJSONArray("opt_road_list");
                String opt_1_a_physic_device_id = opt_road_list.getJSONObject(0).getString("a_physic_device_id");
                String opt_2_a_physic_device_id = opt_road_list.getJSONObject(1).getString("a_physic_device_id");
                String opt_1_z_physic_device_id = opt_road_list.getJSONObject(0).getString("z_physic_device_id");
                String opt_2_z_physic_device_id = opt_road_list.getJSONObject(1).getString("z_physic_device_id");
                String accessDevice_device_id = accessDevice.getString("id");
                if(opt_1_a_physic_device_id!=null && accessDevice_device_id!=null && opt_1_z_physic_device_id!=null && opt_2_a_physic_device_id!=null &&
                        opt_2_z_physic_device_id!=null){
                    return (opt_1_a_physic_device_id.equals(accessDevice_device_id) || opt_1_z_physic_device_id.equals(accessDevice_device_id)) &&
                            (opt_2_a_physic_device_id.equals(accessDevice_device_id) || opt_2_z_physic_device_id.equals(accessDevice_device_id));
                }else{return false;}
            }).collect(Collectors.toList());
            int access_opt_count = accessOptPairs.size(); //计算同一个设备出局的光路数量
            if(count ==0){
                count = access_opt_count;
            }else if(count > access_opt_count && access_opt_count != 0){
                count = access_opt_count;
            }
            accessOptPairList.addAll(accessOptPairs);
        }
        JSONObject result = new JSONObject();
        result.put("count", count);
        result.put("accessOptPairList",accessOptPairList);
        return result;
    }

    //通过接入设备找上行接入光路
    public List<JSONObject> getAccessOptRoads(JSONObject result){
        List<JSONObject> accessOptGroup = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(result.getJSONArray("accessDevices")) && ObjectUtil.isNotEmpty(result.getJSONArray("optRoads"))){
            List<JSONObject> accessDevices = result.getJSONArray("accessDevices").toJavaList(JSONObject.class);
            List<JSONObject> optRoads = result.getJSONArray("optRoads").toJavaList(JSONObject.class);
            if(ObjectUtil.isNotEmpty(accessDevices) && accessDevices.size()!=0 &&ObjectUtil.isNotEmpty(optRoads) &&  optRoads.size()!=0) {
                for(JSONObject accessDevice : accessDevices){
                    List<JSONObject> accessOpt = optRoads.stream().filter(optRoad -> {
                        String accessDeviceId = accessDevice.getString("id");
                        String a_physic_device_id = optRoad.getString("a_physic_device_id");
                        String z_physic_device_id = optRoad.getString("z_physic_device_id");
                        log.info("这里---" +a_physic_device_id+","+ z_physic_device_id+ ","+ accessDeviceId);
                        if(accessDeviceId!=null && a_physic_device_id !=null && z_physic_device_id !=null){
                            return a_physic_device_id.equals(accessDeviceId)||z_physic_device_id.equals(accessDeviceId);
                        }
                        return false;
                    }).collect(Collectors.toList());
                    accessOptGroup.addAll(accessOpt);
                }
            }
            log.info("取到接入光路组："+accessOptGroup);
        }
        return accessOptGroup;
    }



    public JSONObject queryMSTPCircuitToPipeSegment(JSONObject request) {
        List<String> codes = JSONObjectUtil.jsonStringArrayToList(request.getJSONArray("codes"));
        String shardingCode=request.getString("shardingCode");



        GraphRequestBuilder builder = new GraphRequestBuilder();

        //shuangluyou
        GraphRequest graphRequest = builder.setApiId("query_mstp_shuangluyou").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("zq_service","codes",codes))
                .appendWhereBodyItem(new GraphWhereBodyItem("ether_link","is_up","1"))
                /*
                .appendFilter("ether_link",(dataList,g)->{
                    // 如果以太网链路对应的政企业务无MSTP电路,则添加该以太网链路
                    JSONObject graphData =   graphService.getData(g);
                    graphService.buildReverseTree(graphData, "政企业务", "以太网链路", "zq_services");
                    graphService.buildTree(graphData,"政企业务","mstp电路","mstp_circuits");
                    dataList = graphService.filterNodeByLabel(graphData,"以太网链路");
                    List<JSONObject> filteredList = new ArrayList<>();
                    for (JSONObject d: dataList) {
                        int mstpNum =0;
                        List<JSONObject> zqServiceList = d.getJSONArray("zq_services").toJavaList(JSONObject.class);
                        for (JSONObject zqService: zqServiceList) {
                            if(ObjectUtil.isNotEmpty(zqService.getJSONArray("mstp_circuits"))) {
                                mstpNum+=zqService.getJSONArray("mstp_circuits").size();
                            }

                        }
                        if (mstpNum==0) {
                            filteredList.add(d);
                        }
                    }
                    return filteredList;
                })
                
                 */
                .build();

        Graph graph = graphApiService.doApi(graphRequest);

        JSONObject data = graphService.getData(graph);

        List<JSONObject> pipeSegmentList = graphService.filterNodeByLabel(data,"管道段");
        List<JSONObject> roomList = graphService.filterNodeByLabel(data,"机房");
        List<JSONObject> deviceList = graphService.filterNodeByLabel(data,"设备");
        List<JSONObject> cableList = graphService.filterNodeByLabel(data,"光缆");
        List<JSONObject> localFiberOptList = graphService.filterNodeByLabel(data,"局向光纤");
        List<JSONObject> optRoadList = graphService.filterNodeByLabel(data,"光路");
        List<JSONObject> circuitList = graphService.filterNodeByLabel(data,"政企业务");
        List<JSONObject> accessDeviceList = graphService.filterNodeByLabel(data,"接入设备");
        List<JSONObject> cardBoardList = graphService.filterNodeByLabel(data,"板卡");
        frontHoleService.computeFrontHole(pipeSegmentList,deviceList);
        buildTrees(data);


        optRoadList.stream().forEach(optRoad -> {
            String optCode = optRoad.getString("code");
            List<JSONObject> pipe_segment_list = JSONObjectUtil.jsonObjectArrayToList(optRoad.getJSONArray("pipe_segment_list"));
            if(ObjectUtil.isNotEmpty(pipe_segment_list)) pipe_segment_list.stream().forEach(pipeSegment -> {if(ObjectUtil.isNotEmpty(pipeSegment)) pipeSegment.put("gis_id", optCode+ pipeSegment.getString("id"));});
        });

        JSONObject result = new JSONObject();
        result.put("ds", request.getString("ds"));
        result.put("optRoads",optRoadList);
        List<JSONObject> sameoptRoads = optRoadList.stream().filter(optRoad-> ObjectUtil.isNotEmpty(optRoad.getJSONArray("mstp_circuits"))&& optRoad.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        result.put("sameoptRoads", sameoptRoads);
        result.put("pipeSegments", pipeSegmentList);
        result.put("cables", cableList);
        result.put("devices", deviceList);
        result.put("rooms", roomList);
        result.put("cardBoards", cardBoardList);
        List<JSONObject> localFiberOpts = localFiberOptList.stream().filter(p-> ObjectUtil.isNotEmpty(p.getJSONArray("pipe_segment_list")) ).collect(Collectors.toList());
        result.put("localFiberOpts", localFiberOpts);
        result.put("accessDevices", accessDeviceList);
//---------------------------------------------

        List<JSONObject> samePipeSegmentList = pipeSegmentList.stream().filter(p-> ObjectUtil.isNotEmpty(p.getJSONArray("mstp_circuits"))&& p.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        List<JSONObject> sameCableList = cableList.stream().filter(p->ObjectUtil.isNotEmpty(p.getJSONArray("mstp_circuits"))&& p.getJSONArray("mstp_circuits").size()>1 ).collect(Collectors.toList());
        List<JSONObject> sameOptList = optRoadList.stream().filter(p-> ObjectUtil.isNotEmpty (p.getJSONArray("mstp_circuits"))&& p.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        List<JSONObject> samedeviceList = deviceList.stream().filter(p-> ObjectUtil.isNotEmpty (p.getJSONArray("mstp_circuits"))&& p.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());
        List<JSONObject> sameCardBoardList = cardBoardList.stream().filter(p-> ObjectUtil.isNotEmpty (p.getJSONArray("mstp_circuits"))&&p.getJSONArray("mstp_circuits").size()>1).collect(Collectors.toList());


        // sameCableList = sameCableList.stream().filter(p->p.getJSONArray("opt_road_list").size()>1 &&p.getJSONArray("opt_road_list").size()<3).collect(Collectors.toList());
        // samePipeSegmentList = samePipeSegmentList.stream().filter(p->p.getJSONArray("opt_road_list").size()>1 &&p.getJSONArray("opt_road_list").size()<3 ).collect(Collectors.toList());

        result.put("samedevices", samedeviceList); // 同设备
        result.put("samePipeSegments", samePipeSegmentList); //
        result.put("sameCables", sameCableList);
        result.put("sameOptList", sameOptList);
        result.put("sameCardBoard", sameCardBoardList);//同板卡
        result.put("devices_d_new", mstpService.getFirstLastDevice(graph,codes));


        // 计算光路对
        List<JSONObject> optRoadPairList = new ArrayList<>();

        JSONObject circuit_1 = circuitList.stream().filter(c -> c.getString("code").equals(codes.get(0))).findFirst().orElse(new JSONObject());
        // JSONObject circuit_2 = circuitList.stream().filter(c -> c.getString("code").equals(codes.get(1))).findFirst().orElse(new JSONObject());
        List<JSONObject> optRoadList1 = JSONObjectUtil.jsonObjectArrayToList(circuit_1.getJSONArray("opt_road_list"));
        List<JSONObject> optRoadList2 = JSONObjectUtil.jsonObjectArrayToList(circuit_1.getJSONArray("opt_road_list"));
        for (JSONObject optRoad1 : optRoadList1) {
            for (JSONObject optRoad2 : optRoadList2) {
                    String aOptCode = optRoad1.getString("code");
                    String zOptCode = optRoad2.getString("code");
                    if (aOptCode.equals(zOptCode)) continue;  // 如果两条电路同光路,则不生成光路对
                    JSONObject optRoadPair = optRoadPairService.buildOptRoadPair(optRoad1, optRoad2);
                    optRoadPair.put("a_code", codes.get(0));
                    optRoadPair.put("z_code", codes.get(0));
                    if (!isOptPairExists(optRoadPairList, optRoadPair)) {
                        optRoadPairList.add(optRoadPair);
                    }

            }
        }

        // 计算光路对重复管道段
        List<JSONObject> samePipeSegmentsOptPairList = optRoadPairService.getSamePipeSegments_d(optRoadPairList);
        result.put("samePipeSegments_d", optRoadPairService.getSamePipeSegments_d(optRoadPairList));

        // 计算光路对重复光路
        List<JSONObject> sameCableOptPairList = optRoadPairService.getSameCables_d(optRoadPairList);
        result.put("sameCables_d", optRoadPairService.getSameCables_d(optRoadPairList));




        // List<JSONObject> sameCableOptPairList = result.getJSONArray("sameCables_d").toJavaList(JSONObject.class);


        List<JSONObject> accessOptRoads = getAccessOptRoads(result);
        List<JSONObject> accessDevices = JSONObjectUtil.jsonObjectArrayToList(result.getJSONArray("accessDevices"));
        List<JSONObject> filteredSamePipeSegmentOptPair = new ArrayList<>(); // 同管道的光路对



        JSONObject pipeSegmentOptPair = filterAccessOptPair(samePipeSegmentsOptPairList, accessDevices);
        filteredSamePipeSegmentOptPair.addAll(JSONObjectUtil.jsonObjectArrayToList(pipeSegmentOptPair.getJSONArray("accessOptPairList")));
        int access_opt_count = pipeSegmentOptPair.getInteger("count");
        //filteredSamePipeSegmentOptPair.addAll(filterAZSameOptPair(samePipeSegmentsOptPairList));
        List<JSONObject> filteredSameCableOptPair = new ArrayList<>();
        JSONObject cableSegmentOptPair = filterAccessOptPair(sameCableOptPairList, accessDevices);
        filteredSameCableOptPair.addAll(JSONObjectUtil.jsonObjectArrayToList(cableSegmentOptPair.getJSONArray("accessOptPairList")));
        //filteredSameCableOptPair.addAll(filterAZSameOptPair(sameCableOptPairList));

        result.put("samePipeSegments_d", filteredSamePipeSegmentOptPair);
        result.put("sameCables_d", filteredSameCableOptPair);

        optRoadPairService.Result_dealt(result);


        //新加的，光路清单
        List<String> opt_code_list = optRoadList.stream().map(p->p.getString("code")).collect(Collectors.toList());
        result.put("opt_code_list",opt_code_list);
        return result;
    }

    boolean isOptPairExists(List<JSONObject> pairList ,JSONObject optRoadPair) {

        for (JSONObject pair : pairList) {
            if (pair.getString("a_opt_code").equals(optRoadPair.getString("a_opt_code")) && pair.getString("z_opt_code").equals(optRoadPair.getString("z_opt_code"))) {
                return true;
            }
            if (pair.getString("a_opt_code").equals(optRoadPair.getString("z_opt_code")) && pair.getString("a_opt_code").equals(optRoadPair.getString("z_opt_code"))) {
                return true;
            }
        }
        return false;

    }

}
