package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;

import java.io.IOException;
import java.util.List;

public interface FlowService {

    public JSONObject pm_flow_instance_insert(JSONObject jsonObject);
    public JSONObject pm_task_instance_insert(JSONObject jsonObject);
    public JSONObject pm_rela_flow_task_insert(JSONObject jsonObject);
    public JSONObject pm_resource_process_insert(JSONObject jsonObject);
    public JSONObject pm_rela_flow_resource_insert(JSONObject jsonObject);
    public JSONObject pm_flow_instance_update(JSONObject jsonObject);
    public JSONObject pm_flow_instance_query(JSONObject jsonObject);
    public JSONObject pm_flow_resource_query(JSONObject jsonObject);
    public JSONObject doPost(String url_address,JSONObject jsonObject) throws IOException;
    public JSONObject flow_myTodoService(JSONObject jsonObject)throws IOException;
    public JSONObject flow_param(JSONObject jsonObject);
    public Integer pm_resource_process_delete(JSONObject jsonObject);
    public List<JSONObject> flow_task_query(JSONObject query_flow);
}
