package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.topo.TopoMode;


import java.util.List;
import java.util.Map;

public interface CommonCircuitService {

    public JSONObject sortCircuitTopology(List<JSONObject> devices, List<JSONObject> links , TopoMode mode, String startId);
    public JSONObject sortCircuitTopology(List<JSONObject> devices, List<JSONObject> links , List<JSONObject> linkRelations, TopoMode mode, String startId);

    List<JSONObject> computeFirstLinks (List<JSONObject> links, List<String> currentIds, String endId, TopoMode mode, int layer);
}
