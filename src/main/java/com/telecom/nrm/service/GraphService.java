package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;

import java.util.List;
import java.util.Set;
import java.util.function.Function;

public interface GraphService {
    public boolean checkNoCircle (Graph graph);

    public boolean checkStartNode (Graph graph, String startNode);


    public List<Node> walkNodes(Graph graph);


    public Graph reload(Graph graph);

    public Graph parsePaths(String[] paths);


    public JSONObject getData(Graph graph);


    public JSONObject getPaths(JSONObject data, List<String> labels);

    public List<JSONObject> filterNodeByLabel(JSONObject data,String labelName);

    public void buildTree (JSONObject data, String parentLabel,String childLabel, String propertyName);


    public void buildReverseTree (JSONObject data, String parentLabel,String childLabel, String propertyName);


    public void mapProperty (JSONObject data, String label, String property, Function<JSONObject,JSONObject> mapFun);



    public String findStart(Set<Edge> edges);


    /*
    public List<JSONObject> buildCompleteTree(Graph graph);

    public List<JSONObject> buildCblTree(Graph graph);
    public List<JSONObject> buildPipeTree(Graph graph);


    public  JSONObject convertToFrontendFormat(List<JSONObject> inputList) ;

     */


}
