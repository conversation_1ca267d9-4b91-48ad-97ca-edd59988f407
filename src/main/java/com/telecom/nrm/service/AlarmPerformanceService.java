package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;
import java.util.Map;

public interface AlarmPerformanceService {

    /**
     * 查询告警信息
     * @param param
     * @param shardingCode
     * @return
     */
    PageResponse<JSONObject> alarmInfo(JSONObject param, String shardingCode);

    List<JSONObject> queryAlarmInfo(Map<String,String> param);

    PageResponse<JSONObject> performance(JSONObject param, String shardingCode);
}
