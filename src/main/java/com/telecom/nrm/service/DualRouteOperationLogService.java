package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 双路由管理操作日志服务接口 - 扩展版本
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface DualRouteOperationLogService {

    /**
     * 记录操作日志 - 核心方法
     *
     * @param operationType 操作类型
     * @param objectType 操作对象类型
     * @param objectId 操作对象ID
     * @param operationDescription 操作描述
     * @param areaCode 地市编码
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param request HTTP请求对象
     * @return 记录结果
     */
    boolean recordOperationLog(String operationType, String objectType, Long objectId,
                              String operationDescription, String areaCode,
                              String operatorId, String operatorName,
                              HttpServletRequest request);

    /**
     * 记录操作日志 - 完整方法
     *
     * @param operationType 操作类型
     * @param objectType 操作对象类型
     * @param objectId 操作对象ID
     * @param objectCode 操作对象编码
     * @param objectName 操作对象名称
     * @param operationDescription 操作描述
     * @param beforeData 修改前数据
     * @param afterData 修改后数据
     * @param areaCode 地市编码
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param request HTTP请求对象
     * @return 记录结果
     */
    boolean recordOperationLogFull(String operationType, String objectType, Long objectId,
                                  String objectCode, String objectName, String operationDescription,
                                  JSONObject beforeData, JSONObject afterData, String areaCode,
                                  String operatorId, String operatorName, HttpServletRequest request);

    /**
     * 分页查询操作日志
     *
     * @param queryParams 查询参数
     * @param pageSize 页大小
     * @param currentPage 当前页
     * @param shardingCode 分库编码
     * @return 分页查询结果
     */
    PageResponse<JSONObject> queryOperationLog(JSONObject queryParams, Integer pageSize,
                                             Integer currentPage, String shardingCode);

    /**
     * 查询操作日志详情
     *
     * @param logId 日志ID
     * @param shardingCode 分库编码
     * @return 日志详情
     */
    JSONObject queryOperationLogDetail(Long logId, String shardingCode);

    /**
     * 查询操作日志统计信息
     *
     * @param queryParams 查询参数
     * @param shardingCode 分库编码
     * @return 统计结果
     */
    List<JSONObject> queryOperationLogStatistics(JSONObject queryParams, String shardingCode);

    /**
     * 批量记录操作日志
     *
     * @param logList 日志列表
     * @param shardingCode 分库编码
     * @return 记录结果
     */
    Integer batchRecordOperationLog(List<JSONObject> logList, String shardingCode);

    /**
     * 清理过期日志
     *
     * @param retentionDays 保留天数
     * @param shardingCode 分库编码
     * @return 清理结果
     */
    Integer cleanupExpiredLogs(Integer retentionDays, String shardingCode);
}
