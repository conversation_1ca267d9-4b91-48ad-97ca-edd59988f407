package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ProtectCircuitCheckService {
    public String getAreaCode(String area_name);
    public JSONObject getrisk(JSONObject cur_result);
    public String getrisk(String risk_type);
    public String getrisktype(List<JSONObject> samePipeSegments_d, List<JSONObject> sameCables_d, List<JSONObject> samedevices);
    public String getriskdesc(List<JSONObject> samePipeSegments_d, List<JSONObject> sameCables_d, List<JSONObject> samedevices);
    public JSONObject getInterface(String code, String msg, String a_area_code, String z_area_code, String a_is_risk, String z_is_risk, String a_risk_type, String z_risk_type, String a_risk_desc, String z_risk_desc);
    public JSONObject getInterface(String code, String msg);
    public List<String> getAreaFromInput(String input);
    public Integer reportSave(String intf_code,String request_header, String request_body, String response_body, String response_header);
    public List<JSONObject> getPairCircuit(JSONObject param);
    public List<String>  getLeaveArea(List<JSONObject> circuits, List<String> areaList);
    public List<JSONObject> getCircuitFSC(JSONObject param);
    public List<JSONObject> getLeaveArea_circuit(List<JSONObject> circuits, List<JSONObject> circuitsFSC);
    public Integer savePairCircuits(String a_code, String z_code, String risk_type, String risk_desc,String ds);
    public JSONObject getSeq(String circuit_code, JSONObject result, String aProvince, String zProvince);
}
