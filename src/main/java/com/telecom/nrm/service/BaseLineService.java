package com.telecom.nrm.service;

import com.telecom.nrm.domain.ResTreeParamDto;
import com.telecom.nrm.domain.ResponseData;

public interface BaseLineService {

    ResponseData getBusinessCircuitByDevice(ResTreeParamDto resTreeParamDto) throws Exception ;

    ResponseData getServiceCircuitByCable(ResTreeParamDto resTreeParamDto) throws Exception ;

    ResponseData getServiceCircuitByCustID(ResTreeParamDto resTreeParamDto) throws Exception ;

    ResponseData getServiceCircuitByCircuitCode(ResTreeParamDto resTreeParamDto) throws Exception ;

    ResponseData getProductInfoByCustId(ResTreeParamDto resTreeParamDto) throws Exception ;

    ResponseData getDeviceResourceListByCondition(ResTreeParamDto resTreeParamDto) throws Exception ;

    ResponseData getCirCountByCustId(ResTreeParamDto resTreeParamDto) throws Exception ;
}
