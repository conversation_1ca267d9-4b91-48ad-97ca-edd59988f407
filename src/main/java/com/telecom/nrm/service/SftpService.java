package com.telecom.nrm.service;

import java.io.InputStream;

/**
 * SFTP服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface SftpService {
    
    /**
     * 上传文件到SFTP服务器
     * 
     * @param userWorkNumber 用户统一工号
     * @param fileName 文件名
     * @param inputStream 文件输入流
     * @return 上传成功返回true，失败返回false
     */
    boolean uploadFile(String userWorkNumber, String fileName, InputStream inputStream);
    
    /**
     * 测试SFTP连接
     * 
     * @return 连接成功返回true，失败返回false
     */
    boolean testConnection();
}
