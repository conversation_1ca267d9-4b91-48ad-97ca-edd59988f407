package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.security.SecureRandom;
import java.util.List;

public interface CircuitAnalysisService {

    JSONArray prePareOtnOduRoute( JSONArray links, boolean firstOdu);

    void createOtn(String code);

    void analysisCircuit(String circuitCode,String accessCode,String type);

    void saveException(String circutCode, String message, String type, String area, String result);

    void createRelaLinks(String code, long parentId, long parentSpecId, List<JSONObject> resLinkRelas, SecureRandom random, String routeCode, JSONArray routes);

    void syncResByIp(String ip, String areaCode);
}
