package com.telecom.nrm.service;

import com.telecom.nrm.dto.ExportResponseDTO;
import com.telecom.nrm.dto.SheetData;

import java.util.List;
import java.util.Map;

/**
 * 文档导出服务接口
 * 提供Excel导出和文档安全平台上传功能
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface DocumentExportService {

    /**
     * 导出Excel并上传到文档安全平台
     *
     * @param data 要导出的数据列表
     * @param columns 列定义，格式："显示名称:字段名"
     * @param sheetName 工作表名称
     * @param fileNamePrefix 文件名前缀
     * @param exportModule 导出模块名称
     * @param exportInterface 导出接口路径
     * @param exportType 导出类型
     * @return 导出结果
     */
    ExportResponseDTO exportToDocumentSecurity(List<Map<String, Object>> data,
                                             List<String> columns,
                                             String sheetName,
                                             String fileNamePrefix,
                                             String exportModule,
                                             String exportInterface,
                                             String exportType);

    /**
     * 导出多Sheet Excel并上传到文档安全平台
     *
     * @param sheetDataList 多个工作表数据列表
     * @param fileNamePrefix 文件名前缀
     * @param exportModule 导出模块名称
     * @param exportInterface 导出接口路径
     * @param exportType 导出类型
     * @return 导出结果
     */
    ExportResponseDTO exportMultipleSheetToDocumentSecurity(List<SheetData> sheetDataList,
                                                          String fileNamePrefix,
                                                          String exportModule,
                                                          String exportInterface,
                                                          String exportType);

    /**
     * 上传原始文件到文档安全平台
     * 支持前端生成的各种格式文件直接上传
     *
     * @param fileBytes 文件字节数组
     * @param fileName 文件名
     * @param moduleName 模块名称
     * @param description 描述
     * @param apiEndpoint API端点
     * @param exportType 导出类型
     * @param fileExtension 文件扩展名
     * @return 上传结果
     */
    ExportResponseDTO uploadRawFileToDocumentSecurity(byte[] fileBytes,
                                                    String fileName,
                                                    String moduleName,
                                                    String description,
                                                    String apiEndpoint,
                                                    String exportType,
                                                    String fileExtension);

    /**
     * 记录导出日志到数据库
     *
     * @param logData 日志数据
     */
    void recordExportLog(com.alibaba.fastjson.JSONObject logData);
}
