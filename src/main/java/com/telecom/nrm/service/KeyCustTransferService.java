package com.telecom.nrm.service;

import com.telecom.nrm.entity.*;

import java.util.List;
import java.util.Map;

public interface KeyCustTransferService {
    PageResult queryCmPortList(PortQueryDTO portQueryDTO);

    PageResult queryCmDeviceList(EntityQueryDTO entityQueryDTO);

    Result queryWavelengthChannel(WaveQueryDTO waveQueryDTO);

    Result queryOpticalRoute(Map<String, String> param);

    Result queryOptMultiSection(Map<String, String> param);

    Result queryOptRoadByMultiSegId(Map<String, String> param);

    Result queryRelayInfo(Map<String, String> param);

    Result queryRelayRoute(RelayRouteQueryDTO relayRouteQueryDTO);

    Result occupyResInfo(SyncOccupyResInfoDTO syncOccupyResInfoDTO);

    Result queryBcInfoList(List<BcInfoQueryDTO> queryBcInfo);
}
