package com.telecom.nrm.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.CustViewMemberDao;
import com.telecom.nrm.domain.*;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustLinkService {

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    CommonCircuitService commonCircuitService;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    MstpService mstpService;

    @Autowired
    ProtectCircuitCheckService protCircuitCheckService;

    @Autowired
    HunderRiverService hunderRiverService;

    public void setdownload(List<JSONObject> result, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("地市");
        header.createCell(1).setCellValue("客户名称");
        header.createCell(2).setCellValue("业务号码");
        header.createCell(3).setCellValue("电路编号");
        header.createCell(4).setCellValue("光路编码");
        header.createCell(5).setCellValue("光路名称");
        header.createCell(6).setCellValue("电路备注");
        header.createCell(7).setCellValue("成对业务号码");
        header.createCell(8).setCellValue("成对电路编号");
        header.createCell(9).setCellValue("成对光路编码");
        header.createCell(10).setCellValue("成对光路名称");
        header.createCell(11).setCellValue("成对电路备注");
        header.createCell(12).setCellValue("检测结果");

        // 填充数据
        int rowIndex = 1;
        for (int i =0; i< result.size();i++) {
            XSSFRow row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(result.get(i).getString("areacode"));
            row.createCell(1).setCellValue(result.get(i).getString("custName"));
            row.createCell(2).setCellValue(result.get(i).getString("accessCode"));
            row.createCell(3).setCellValue(result.get(i).getString("aCode"));
            row.createCell(4).setCellValue(result.get(i).getString("aOptCode"));
            row.createCell(5).setCellValue(result.get(i).getString("aOptRoad"));
            row.createCell(6).setCellValue(result.get(i).getString("aNotes"));
            row.createCell(7).setCellValue(result.get(i).getString("zAccessCode"));
            row.createCell(8).setCellValue(result.get(i).getString("zCode"));
            row.createCell(9).setCellValue(result.get(i).getString("zOptCode"));
            row.createCell(10).setCellValue(result.get(i).getString("zOptRoad"));
            row.createCell(11).setCellValue(result.get(i).getString("zNotes"));
            row.createCell(12).setCellValue(result.get(i).getString("result"));
        }
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=list.xlsx");

        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    public JSONObject queryCircuitByParam(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("accessCode".equals(body.getPropertyLabel())) {
                jsonObject.put("access_code", body.getPropertyValue());
            } else if ("circuitNo".equals(body.getPropertyLabel())) {
                jsonObject.put("circuit_code", body.getPropertyValue());
            } else if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuitsOrder(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("circuitId", obj.getString("link_id"));
                node.put("memo", obj.getString("notes"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }

    public JSONObject queryCircuitByCondition(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("accessCode".equals(body.getPropertyLabel())) {
                jsonObject.put("accessCode", body.getPropertyValue());
            } else if ("circuitNo".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitNo", body.getPropertyValue());
            } else if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("circuitName".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitName", body.getPropertyValue());
            } else if ("custName".equals(body.getPropertyLabel())) {
                jsonObject.put("custName", body.getPropertyValue());
            } else if ("custId".equals(body.getPropertyLabel())) {
                jsonObject.put("custId", body.getPropertyValue());
            } else if ("crmProductId".equals(body.getPropertyLabel())) {
                jsonObject.put("crmProductId", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.queryCircuitByCondition(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("circuitId", obj.getString("circuit_id"));
                node.put("circuitSpecId", obj.getString("circuit_spec_id"));
                node.put("circuitName", obj.getString("circuit_name"));
                node.put("circuitCode", obj.getString("circuit_code"));
                node.put("accessCode", obj.getString("access_code"));
                node.put("custName", obj.getString("a_customer_name"));
                node.put("custId", obj.getString("customer_id"));
                node.put("rate", obj.getString("rate"));
                node.put("aPhysicDeviceId", obj.getString("a_device_id"));
                node.put("aPhysicDeviceSpec", obj.getString("a_device_spec_name"));
                node.put("aPhysicDeviceName", obj.getString("a_device_name"));
                node.put("aPhysicDeviceCode", obj.getString("a_device_code"));
                node.put("zPhysicDeviceId", obj.getString("z_device_id"));
                node.put("zPhysicDeviceSpec", obj.getString("z_device_spec_name"));
                node.put("zPhysicDeviceName", obj.getString("z_device_name"));
                node.put("zPhysicDeviceCode", obj.getString("z_device_code"));
                node.put("aPortId", obj.getString("a_port_id"));
                node.put("aPortSpec", obj.getString("a_port_spec_name"));
                node.put("aPortName", obj.getString("a_port_name"));
                node.put("aPortCode", obj.getString("a_port_code"));
                node.put("zPortId", obj.getString("z_port_id"));
                node.put("zPortSpec", obj.getString("z_port_spec_name"));
                node.put("zPortName", obj.getString("z_port_name"));
                node.put("zPortCode", obj.getString("z_port_code"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }

    public JSONObject queryCircuitDetailByCondition(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("circuitId".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitId", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.queryCircuitByCondition(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("circuitId", obj.getString("circuit_id"));
                node.put("specId", obj.getString("circuit_spec_id"));
                node.put("specName", obj.getString("circuit_spec_name"));
                node.put("circuitName", obj.getString("circuit_name"));
                node.put("circuitCode", obj.getString("circuit_code"));
                node.put("aArea", obj.getString("a_area"));
                node.put("aFacility", obj.getString("a_facility_name"));
                node.put("aAddress", obj.getString("a_address_name"));
                node.put("aCust", obj.getString("a_customer_name"));
                node.put("aDeviceId", obj.getString("a_device_id"));
                node.put("aDeviceName", obj.getString("a_device_name"));
                node.put("aDeviceCode", obj.getString("a_device_code"));
                node.put("aPortCode", obj.getString("a_port_code"));
                node.put("zArea", obj.getString("z_area"));
                node.put("zFacility", obj.getString("z_facility_name"));
                node.put("zAdreess", obj.getString("z_address_name"));
                node.put("zCust", obj.getString("z_customer_name"));
                node.put("zDeviceId", obj.getString("z_device_id"));
                node.put("zDeviceName", obj.getString("z_device_name"));
                node.put("zDeviceCode", obj.getString("z_device_code"));
                node.put("zPortCode", obj.getString("z_port_code"));
                node.put("rate", obj.getString("rate"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }

    public JSONObject queryCircuitRouteByCondition(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("circuitId".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitId", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.queryCircuitByCondition(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("aDeviceId", obj.getString("a_device_id"));
                node.put("aPortId", obj.getString("a_port_id"));
                node.put("zDeviceId", obj.getString("z_device_id"));
                node.put("zPortId", obj.getString("z_port_id"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }

    public JSONObject queryCircuitRoutePlusByCondition(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("circuitId".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitId", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.queryCircuitByCondition(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> links = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject link = new JSONObject();
                link.put("linkId", obj.getString("circuit_id"));
                link.put("linkSpecId", obj.getString("circuit_spec_id"));
                link.put("linkSpecName", obj.getString("circuit_spec_name"));
                link.put("linkName", obj.getString("circuit_name"));
                link.put("linkCode", obj.getString("circuit_code"));
                link.put("aDeviceId", obj.getString("a_device_id"));
                link.put("aPortId", obj.getString("a_port_id"));
                link.put("zDeviceId", obj.getString("z_device_id"));
                link.put("zPortId", obj.getString("z_port_id"));
                links.add(link);
            }
        }

        data.put("nodes", new ArrayList<>());
        data.put("relatons", new ArrayList<>());
        data.put("links", links);
        return data;
    }

    public JSONObject queryFaultListByCondition(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("accessCode".equals(body.getPropertyLabel())) {
                jsonObject.put("accessCode", body.getPropertyValue());
            } else if ("circuitNo".equals(body.getPropertyLabel())) {
                jsonObject.put("circuitNo", body.getPropertyValue());
            } else if ("crmProductId".equals(body.getPropertyLabel())) {
                jsonObject.put("crmProductId", body.getPropertyValue());
            } else if ("workOrderType".equals(body.getPropertyLabel())) {
//                jsonObject.put("workOrderType", body.getPropertyValue());
            } else if ("workOrderStatus".equals(body.getPropertyLabel())) {
//                jsonObject.put("workOrderStatus", body.getPropertyValue());
            } else if ("workOrderCode".equals(body.getPropertyLabel())) {
//                jsonObject.put("workOrderCode", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.queryCircuitByCondition(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                //workOrderCode
                node.put("areaId", areaId);
                node.put("areaName", obj.getString("area_name"));
                node.put("productSpecId", obj.getString("product_spec_id"));
                node.put("productName", obj.getString("product_spec_name"));
                node.put("accessCode", obj.getString("access_code"));
                node.put("custName", obj.getString("a_customer_name"));
                node.put("crmProdcutId", obj.getString("crm_prodcut_id"));
                node.put("circuitCode", obj.getString("circuit_code"));
                //workOrderDes
                //workOrderState
                //createDate
                //archiveDate
                //processInfo
                //reportInfo
                //workOrderType
                //memo

                //调用百川系统
                JSONObject bcObject = new JSONObject();
                bcObject.put("accessCode", obj.getString("access_code"));
                JSONObject result = hunderRiverService.zd(bcObject);
                if (result.getBoolean("success")) {
                    JSONObject value = result.getJSONObject("value");
                    if (value != null) {
                        node.put("woId", value.getString("usertroublesn"));
                        node.put("errorMessage", value.getString("usertroubletitle"));
                    }
                }
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }

    public JSONObject queryCircuitCountByCusId(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("custId".equals(body.getPropertyLabel())) {
                jsonObject.put("custId", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.queryCircuitByCondition(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("productSpecName", obj.getString("product_spec_name"));
                node.put("productTypeID", obj.get("product_type_id") == null ? "-1" : obj.getString("product_type_id"));
                nodes.add(node);
            }

            Map<Object, List<JSONObject>> groupMap = nodes.stream().collect(Collectors.groupingBy(node -> node.get("productTypeID")));
            for (JSONObject node : nodes) {
                List<JSONObject> groupList = groupMap.get(node.getString("productTypeID"));
                node.put("sum", groupList.size());

                if ("-1".equals(node.getString("productTypeID"))) {
                    node.put("productTypeID", null);
                }
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        data.put("sumCircuitNum", nodes.size());
        return data;
    }

    public JSONObject queryCircuitListByCusId(ResTreeParamDto param) throws Exception {
        JSONObject data = new JSONObject();

        List<WhereBody> whereBody = param.getWhereBody();

        JSONObject jsonObject = new JSONObject();
        String areaId = null;
        for (WhereBody body : whereBody) {
            if ("areaId".equals(body.getPropertyLabel())) {
                areaId = String.valueOf(body.getPropertyValue());
            } else if ("custId".equals(body.getPropertyLabel())) {
                jsonObject.put("custId", body.getPropertyValue());
            }
        }

        if (StringUtils.isEmpty(areaId)) {
            throw new RuntimeException("区域ID不能为空");
        }

        //查询数据
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.queryCircuitByCondition(jsonObject, param.getPageSize(), param.getPageNum(),
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> list = pageResponse.getData();

        List<JSONObject> nodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject obj : list) {
                JSONObject node = new JSONObject();
                node.put("circuitId", obj.getString("circuit_id"));
                node.put("circuitNo", obj.getString("circuit_code"));
                node.put("circuitSpecId", obj.getString("circuit_spec_id"));
                node.put("accessCode", obj.getString("access_code"));
                node.put("circuitRate", obj.getString("rate"));
                nodes.add(node);
            }
        }

        data.put("nodes", nodes);
        data.put("relatons", new ArrayList<>());
        data.put("links", new ArrayList<>());
        return data;
    }

    /*
    public void smx_sly_xj(JSONObject param){
        JSONObject result =new JSONObject();
        JSONObject jsonObject = new JSONObject();
        List<JSONObject> pair_list_data =   new ArrayList<>();
        PageResponse<JSONObject> pageResponse_2 =  custViewMemberDao.queryList(param ,10000,1, NRMConstants.SHARDING_GRAPH_DB);
        JSONObjectUtil.convertBigNumberToString(pageResponse_2.getData());
        List<JSONObject> pair_list_data_2 = new ArrayList<>();
        pair_list_data_2.addAll(pageResponse_2.getData());
        //int count = 0;
        for(JSONObject pair_data: pair_list_data_2){
            //count ++;
            //System.out.println("当前正在执行第"+count);
            param.put("id",pair_data.getString("id"));
            if(pair_data.getString("circuit_code").equals("市检察院-溧阳检察院/ETN0001NP")) continue;
            if(pair_data.getString("circuit_code").equals("南京常州ETN0786NP")) continue;
            if(pair_data.getString("circuit_code").equals("HLWZX51920211229165")) continue;
            if(pair_data.getString("circuit_code").equals("江南银行-文化广场/ETN0001NP")) continue;
            circuit_analze(pair_data, param);
        }
        //custLinkInterfaceDao.tmp_result_shuangluyou_insert(jsonObject1, NRMConstants.BCO3DatabaseMap.get("无锡"));
    }
     */






    /*
    private List<Circuit2Fiber> getOneListCircuit2FiberByCode(String shardingCode, String circuitCode){
        List<Circuit2Fiber> circuit2FiberList = custLinkInterfaceDao.getFiberByCircuit(shardingCode,circuitCode);
        return circuit2FiberList;
    }
     */

    /**
     * 根据电路对成光路对入库
     * @param shardingCode
     * @param circuitCodeA
     * @param circuitCodeB
     */
    /*
    public void insertPairCircuit2FiberByCode(String shardingCode,String circuitCodeA,String circuitCodeB){

        List<Circuit2Fiber> circuit2FiberListA=getOneListCircuit2FiberByCode(shardingCode,circuitCodeA);
        List<Circuit2Fiber> circuit2FiberListB=getOneListCircuit2FiberByCode(shardingCode,circuitCodeB);

        for(Circuit2Fiber a : circuit2FiberListA){
            for(Circuit2Fiber b : circuit2FiberListB){
                CircuitPair2FiberPair circuitPair2FiberPair = new CircuitPair2FiberPair();
                circuitPair2FiberPair.setFiberAId(a.getFiberId());
                circuitPair2FiberPair.setFiberACode(a.getFiberCode());
                circuitPair2FiberPair.setCircuitAId(a.getCircuitId());
                circuitPair2FiberPair.setCircuitACode(a.getCircuitCode());

                circuitPair2FiberPair.setFiberBId(b.getFiberId());
                circuitPair2FiberPair.setFiberBCode(b.getFiberCode());
                circuitPair2FiberPair.setCircuitBId(b.getCircuitId());
                circuitPair2FiberPair.setCircuitBCode(b.getCircuitCode());

                circuitPair2FiberPair.setIsPairCircuit("T");

                custLinkInterfaceDao.insertCircuitPair2FiberPair(shardingCode,circuitPair2FiberPair);

            }
        }

    }

     */

    /*
    public void test(){
        String shardingCode="ds_odso_wx";
        String circuitCodeA="无锡CTVPN80019A";
        String circuitCodeB="无锡CTVPN80019B";
        insertSingleCircuit2FiberByCode(shardingCode,circuitCodeA);
        insertPairCircuit2FiberByCode(shardingCode,circuitCodeA,circuitCodeB);

    }

     */

    /**
     * 根据单电路成光路对入库
     * @param shardingCode
     * @param circuitCode
     */
    /*
    public void insertSingleCircuit2FiberByCode(String shardingCode,String circuitCode){

        List<Circuit2Fiber> circuit2FiberList=getOneListCircuit2FiberByCode(shardingCode,circuitCode);

        for(Circuit2Fiber a : circuit2FiberList){
            for(Circuit2Fiber b : circuit2FiberList){
                CircuitPair2FiberPair circuitPair2FiberPair = new CircuitPair2FiberPair();
                circuitPair2FiberPair.setFiberAId(a.getFiberId());
                circuitPair2FiberPair.setFiberACode(a.getFiberCode());
                circuitPair2FiberPair.setCircuitAId(a.getCircuitId());
                circuitPair2FiberPair.setCircuitACode(a.getCircuitCode());

                if(Objects.equals(a.getFiberId(), b.getFiberId()))
                    continue;
                circuitPair2FiberPair.setFiberBId(b.getFiberId());
                circuitPair2FiberPair.setFiberBCode(b.getFiberCode());
                circuitPair2FiberPair.setCircuitBId(b.getCircuitId());
                circuitPair2FiberPair.setCircuitBCode(b.getCircuitCode());

                circuitPair2FiberPair.setIsPairCircuit("F");

                custLinkInterfaceDao.insertCircuitPair2FiberPair(shardingCode,circuitPair2FiberPair);

            }
        }

    }
     */



}
