package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface OptViewService {
    /**
     * 计算两两光缆是否符合双路由规则，并进行入库
     */
    void computeDoubleRoute(JSONObject jsonParame);

    /**
     * 根据当前方案查看符合双路由的光缆
     */
    JSONObject getDoubleRoute(JSONObject jsonParame);

    /**
     * 按照az查询光缆方案
     */
    JSONObject getOptLinkScheme(JSONObject jsonParame);

    /**
     * 判断两条光缆集合是否符合双路由
     * @param plan1 光缆集合1
     * @param plan2 光缆集合2
     * @param ds
     * @return
     */
    public JSONObject checkDoubleRoute(List<String> plan1, List<String> plan2, String ds);


    /**
     * 判断两条光缆集合是否符合双路由(V2)
     * @param plan1 光缆集合1
     * @param plan2 光缆集合2
     * @param ds
     * @return
     */
    public JSONObject checkDoubleRouteV2(List<String> plan1, List<String> plan2, String ds);


    /**
     * 根据光缆集合查询信息
     */
    public JSONObject queryOptLinkInfo(List<String> plan, String ds);

}
