package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface PONService {

    public JSONObject getResourceTree(JSONObject request);

    public JSONObject getCableSegmentTree(JSONObject request);


    public JSONObject queryLOID2CFS(JSONObject request);

    public JSONObject buildTopo(JSONObject data);


    public JSONObject buildAlarmTopo(JSONObject data);

    public void sortcableSegmentList(List<JSONObject> cableSegmentList, List<JSONObject> deviceList);




}
