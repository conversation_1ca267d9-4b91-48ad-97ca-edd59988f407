package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.ResTreeParamDto;

public interface DeviceService {

    /**
     * 根据 设备信息 地市、局站、机房、 查询电路 支持分页
     * @param param
     * @param shardingCode
     * @return
     */
    PageResponse<JSONObject> queryCircuitByDeviceInfo(JSONObject param, String shardingCode);

    JSONObject queryCircuitByDevice(ResTreeParamDto param) throws Exception;

    JSONObject queryResInfoByIds(ResTreeParamDto param) throws Exception;

    /**
     * 根据设备ID查设备详情支持分页
     * @param param
     * @param shardingCode
     * @return
     */

    PageResponse<JSONObject> deviceDetailQuery(JSONObject param, String shardingCode);
}
