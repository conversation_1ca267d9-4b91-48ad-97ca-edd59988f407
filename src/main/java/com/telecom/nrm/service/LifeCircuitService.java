package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface LifeCircuitService {

    public List<JSONObject> getCircuitPairByCustView(List<JSONObject> custViewMemberList, String ds);
    public List<JSONObject> getCircuitPairByCustView(JSONObject custViewMember, String ds);
    public JSONObject lifeCircuit_statistic_dictionary(JSONObject jsonObject);
    public JSONObject lifeCircuit_statistic_his_dictionary(JSONObject jsonObject);
    public Integer white_insert(JSONObject jsonObject);
    public Integer white_delete(JSONObject jsonObject);
    public List<JSONObject> white_query(JSONObject jsonObject);
    public Integer insert_white(JSONObject jsonObject);
    public void setdownload(List<JSONObject> result, HttpServletResponse response) throws IOException;
    public JSONObject getLifeList(JSONObject jsonObject) throws IOException;
    public String lifeListDeal(JSONObject jsonObject);
    //public String lifeListDeal_1(JSONObject jsonObject, HttpServletResponse response);
}
