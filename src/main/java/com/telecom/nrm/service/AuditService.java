package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.entity.IdcAuditRiskDictvalue;
import com.telecom.nrm.entity.IdcAuditRiskRule;
import com.telecom.nrm.entity.PmPortNetworkOperationAudit;
import com.telecom.nrm.entity.PmProductLinkAnalysis;
import com.telecom.nrm.dto.ExportResponseDTO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

public interface AuditService {

    List<PmProductLinkAnalysis> selectAll();

    void autoAnalysis();

    void batchInsertPmProductLinkAnalysis(List<PmProductLinkAnalysis> pmProductLinkAnalyses);

    List<PmProductLinkAnalysis> selectByRegion(String bcCode);

    void exportCommon(String json,  HttpServletResponse response) throws IOException;

    ExportResponseDTO exportToDocumentSecurity(String json);

    JSONObject queryOperationAuditCount(JSONObject param) throws Exception;

    List<JSONObject> queryOperationAuditDetail(JSONObject param, boolean needPage) throws Exception;

    List<JSONObject> queryOperationAuditDetailPage(JSONObject param) throws Exception;

    List<JSONObject> queryIdcTaskMonitorInfo() throws Exception;

    List<JSONObject> queryIdcTaskForSaveDeviceLogs() throws Exception;

    List<JSONObject> queryIdcTaskForPortIdentification() throws Exception;

    List<JSONObject> queryIdcTaskForAuditDaily() throws Exception;

    List<IdcAuditRiskDictvalue> queryAuditRiskDictvaluePage() throws Exception;

    List<IdcAuditRiskRule> queryRiskRuleById(BigDecimal riskId) throws Exception;

    void sendAuditMailMessage() throws Exception;
}


