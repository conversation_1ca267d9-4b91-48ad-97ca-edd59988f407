package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.scheduling.annotation.Async;

import java.io.IOException;

public interface IpranService {

    @Async("graphRestPool")
    void saveCache(JSONObject result, String accessCode, String areaCode,String code);

    JSONObject queryCache(String accessCode, String areaCode);

    JSONObject queryCache(JSONObject query);

    JSONObject ipran(JSONObject request) throws IOException;

    void clearAccessCodeCache( String accessCode);


     JSONObject unionPointPoint(JSONObject aResult, JSONObject zResult);

}
