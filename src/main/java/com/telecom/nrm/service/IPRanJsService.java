package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;

public interface IPRanJsService {
    JSONObject queryiPRanJs(JSONObject param, int i, int i1);

    JSONObject test(int i, int i1, String cjType);

    JSONObject queryTopoRelay(JSONObject param, int i, int i1);

    JSONObject queryiPRanJsPre(JSONObject param, int i, int i1);


    JSONObject queryiPRanJsPrelocal(int i, int i1, String cjType);

    JSONObject queryiPRanJsPre2(int i, int i1, String cjType);

    JSONObject queryiPRanJsPre4(int i, int i1, String cjType,JSONObject circuit);

    JSONObject queryiPRanJsPre3(int i, int i1, String cjType);

    void saveIpranInfo(JSONObject param);
}
