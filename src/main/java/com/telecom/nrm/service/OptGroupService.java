package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface OptGroupService {

    public JSONObject risk_analyze(JSONObject request);
    public JSONObject risk_analyze_previous(JSONObject request);
    public JSONObject risk_analyze_his(JSONObject request);
    // public String result2format(JSONObject result);
    public JSONObject optPair_statistic_dictionary(JSONObject jsonObject);
    public List<JSONObject> pm_risk_check_his_time_list_query(JSONObject jsonObject);

    public JSONObject get_previous_risk_check(JSONObject param);

    public void opt_group_download_response(List<JSONObject> result, HttpServletResponse response) throws IOException;
    public Integer updateOptRiskDealResult (JSONObject jsonObject);
    public Integer get_previous_risk_check_and_his_save(JSONObject param);

    /**
     * 查询光路保护组详情
     * @param param 查询参数（包含id和ds）
     * @return 保护组详情
     */
    public JSONObject queryOptGroupDetail(JSONObject param);
}
