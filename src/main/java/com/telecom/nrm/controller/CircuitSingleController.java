package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustViewDao;
import com.telecom.nrm.dao.CustViewMemberDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.CustViewMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/circuit-single-api")
@Slf4j
public class CircuitSingleController {

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    CustViewMemberService custViewMemberService;

    @PostMapping("/circuit_single_check")
    public void circuit_pair_download(@RequestBody JSONObject jsonObject, HttpServletResponse response) throws IOException {
        JSONObject param= new JSONObject();
        param.put("area_name",jsonObject.getString("area_name"));
        param.put("is_life","0");
        param.put("scene_id",jsonObject.getString("scene_id"));
        log.info("jsonObject_member"+param);
        PageResponse<JSONObject> pageResponse =  custViewMemberDao.queryList(param ,1000000,1, NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> list = pageResponse.getData();
        log.info("pageResponse:"+list.size());
        int count =0;
        for(JSONObject custViewMember : list){
            count = count+1;
            log.info("当前count:"+count);
            custViewMember.put("ds", "常州");
            custViewMemberService.risk_analyze(custViewMember);
        }
    }
}
