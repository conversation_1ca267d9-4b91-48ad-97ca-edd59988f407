package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.IPRanJsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/js")
public class IPRanJsController {

    @Autowired
    IPRanJsService iPRanJsService;

    /**
     * 云专网连接图API（省）
     * @return
     */
    @PostMapping("queryTopoCustomer")
    public ResponseEntity<JSONObject> queryiPRanJs(@RequestBody JSONObject param) {
        //String cjType = param.getString("cjType");
        try {
            JSONObject response = iPRanJsService.queryiPRanJs(param, 100,1);

            ResponseEntity httpResponse =ResponseEntity.ok().body(response);
            return httpResponse;
        }catch (Exception ex) {
            JSONObject response = new JSONObject();
            response.put("data","");
            response.put("code",1);
            ResponseEntity httpResponse = ResponseEntity.ok().body(response);
            return httpResponse;
        }
    }

    @RequestMapping("test")
    public ResponseEntity<JSONObject> test(String cjType) {
        //String cjType = param.getString("cjType");
        try {
            JSONObject response = iPRanJsService.test(100,1 ,cjType);
            ResponseEntity httpResponse =ResponseEntity.ok().body(response);
            return httpResponse;
        }catch (Exception ex) {
            JSONObject response = new JSONObject();
            response.put("resultCode", "1");
            ResponseEntity httpResponse = ResponseEntity.ok().body(response);
            return httpResponse;
        }
    }

    /**
     * IP虚拟专网连接图API（省）
     * @return
     */
    @PostMapping("queryTopoRelay")
    public ResponseEntity<JSONObject> queryTopoRelay(@RequestBody JSONObject param) {
        //String cjType = param.getString("cjType");
        try {
            JSONObject response = iPRanJsService.queryTopoRelay(param, 100,1);

            ResponseEntity httpResponse =ResponseEntity.ok().body(response);
            return httpResponse;
        }catch (Exception ex) {
            JSONObject response = new JSONObject();
            response.put("data","");
            response.put("code",1);
            ResponseEntity httpResponse = ResponseEntity.ok().body(response);
            return httpResponse;
        }
    }

    @PostMapping("queryTopoCustomerPre")
    public ResponseEntity<JSONObject> queryiPRanJsPre(@RequestBody JSONObject param) {
        //String cjType = param.getString("cjType");
        try {
            JSONObject response = iPRanJsService.queryiPRanJsPre(param, 100,1);

            ResponseEntity httpResponse =ResponseEntity.ok().body(response);
            return httpResponse;
        }catch (Exception ex) {
            JSONObject response = new JSONObject();
            response.put("data","");
            response.put("code",1);
            ResponseEntity httpResponse = ResponseEntity.ok().body(response);
            return httpResponse;
        }
    }

    @RequestMapping("queryTopoCustomerPreLocal")
    public ResponseEntity<JSONObject> queryiPRanJsPreLocal(String cjType) {
        //String cjType = param.getString("cjType");
        try {
            JSONObject response = iPRanJsService.queryiPRanJsPrelocal(100,1 ,cjType);

            ResponseEntity httpResponse =ResponseEntity.ok().body(response);
            return httpResponse;
        }catch (Exception ex) {
            JSONObject response = new JSONObject();
            response.put("data","");
            response.put("code",1);
            ResponseEntity httpResponse = ResponseEntity.ok().body(response);
            return httpResponse;
        }
    }

    @RequestMapping("queryTopoCustomerPre2")
    public ResponseEntity<JSONObject> queryiPRanJsPre2(String cjType) {
        //String cjType = param.getString("cjType");
        try {
            JSONObject response = iPRanJsService.queryiPRanJsPre2(100,1 ,cjType);

            ResponseEntity httpResponse =ResponseEntity.ok().body(response);
            return httpResponse;
        }catch (Exception ex) {
            JSONObject response = new JSONObject();
            response.put("data","");
            response.put("code",1);
            ResponseEntity httpResponse = ResponseEntity.ok().body(response);
            return httpResponse;
        }
    }

    @RequestMapping("queryTopoCustomerPre3")
    public ResponseEntity<JSONObject> queryiPRanJsPre3(String cjType) {
        //String cjType = param.getString("cjType");
        try {
            JSONObject response = iPRanJsService.queryiPRanJsPre3(100,1 ,cjType);

            ResponseEntity httpResponse =ResponseEntity.ok().body(response);
            return httpResponse;
        }catch (Exception ex) {
            JSONObject response = new JSONObject();
            response.put("data","");
            response.put("code",1);
            ResponseEntity httpResponse = ResponseEntity.ok().body(response);
            return httpResponse;
        }
    }

    @PostMapping("saveIpranInfo")
    public ResponseEntity<JSONObject> saveIpranInfo(@RequestBody JSONObject param) {
        iPRanJsService.saveIpranInfo(param);
        return ResponseEntity.ok().body(null);
    }

}
