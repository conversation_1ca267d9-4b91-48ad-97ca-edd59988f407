package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.domain.ServiceType;
import com.telecom.nrm.service.DeviceService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/device")

@Slf4j
public class DeviceController {

    @Autowired
    RegionDao regionDao;
    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;


    @Autowired
    DeviceService deviceService;


    @GetMapping("")
    @LogAnnotation( interfaceName="根据光缆查询业务电路")
    public BiyiPageResult<JSONObject> getPage(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        jsonObject.put("pageSize", jsonObject.getString("size"));
        jsonObject.put("currentPage", jsonObject.getString("page"));
        List<JSONObject> data = new ArrayList<JSONObject>();
        long totalCount = 0L;
        PageResponse<JSONObject> pageResponse = deviceService.queryCircuitByDeviceInfo(jsonObject,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if (pageResponse != null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data, totalCount, totalCount);
    }

    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for (int i = 0; i < Region2List.size(); i++) {

            if (Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))) {
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<ServiceType> serviceTypeArray = custLinkInterfaceDao.getServiceTypeDict(NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        meta.put("serviceType", serviceTypeArray);
        return ResponseEntity.ok().body(meta);
    }

    @GetMapping("/detail")
    @LogAnnotation( interfaceName="资源详情")
    public BiyiPageResult<JSONObject> detail(@RequestParam(required = false) Map example) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        String deviceId = jsonObject.getString("device_id");
        String code = jsonObject.getString("code");
        if (StringUtils.isNotEmpty(code)){
            jsonObject.put("code", code);
        }else {
            Objects.requireNonNull(deviceId);
            jsonObject.put("deviceId", deviceId);
        }
        List<JSONObject> data = new ArrayList<JSONObject>();
        long totalCount = 0L;
        String areaName = ShardingEnum.getRegionNameByO3(jsonObject.getString("area_id"));
        Objects.requireNonNull(areaName);
        PageResponse<JSONObject> pageResponse = deviceService.deviceDetailQuery(jsonObject,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
        if (pageResponse != null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data, totalCount, totalCount);

    }
}
