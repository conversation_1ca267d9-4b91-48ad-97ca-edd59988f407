package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.da.client.domain.Page;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.OTNDao;
import com.telecom.nrm.domain.*;
import com.telecom.nrm.service.OTNService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@RestController
@RequestMapping("/api/otn")
@Slf4j
public class OTNController {


    @Autowired
    OTNDao otnDao;

    @Autowired
    OTNService otnService;

    public static final Integer STATE_SUCCESS = 1;
    public static final Integer STATE_FAIL = 2;
    public static final Map<String, String> areaMap = new HashMap<String, String>() {
        {
            put("南京市", "nj");
            put("苏州市", "sz");
            put("南通市", "nt");
            put("扬州市", "yz");
            put("连云港市", "lyg");
            put("常州市", "cz");
            put("无锡市", "wx");
            put("宿迁市", "sq");
            put("镇江市", "zj");
            put("盐城市", "yc");
            put("泰州市", "tz");
            put("淮安市", "ha");
            put("徐州市", "xz");
            put("10001604", "nj");
            put("10001602", "ha");
            put("10001611", "yc");
            put("10001607", "sq");
            put("10001609", "wx");
            put("10001603", "lyg");
            put("10001608", "tz");
            put("10001610", "xz");
            put("10001605", "nt");
            put("10001612", "yz");
            put("10001606", "sz");
            put("10001613", "zj");
            put("10001601", "cz");
        }
    };
    public static final List<String> cityList = Arrays.asList("nj","nt","sq","tz","cz","wx","zj","lyg","yc","yz","sz","ha","xz");

    @GetMapping("/query")
    public BiyiPageResult<JSONObject> getList(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        log.debug("REST request to get entity list");
        JSONObject param = (JSONObject) JSON.toJSON(example);
        // JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = otnDao.pageQuery(param ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    //每条电路的稽核结果
    @GetMapping("/query-circuit-check-res")
    public BiyiPageResult<JSONObject> getPerCircuitCheckRes(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        log.debug("REST request to get entity list");
        JSONObject param = (JSONObject) JSON.toJSON(example);
        // JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = otnDao.pageQueryPerCircuitCheckRes(param ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    //电路稽核结果详情
    @GetMapping("/query-circuit-check-res-info")
    public BiyiPageResult<JSONObject> getPerCircuitInfo(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        log.debug("REST request to get entity list");
        JSONObject param = (JSONObject) JSON.toJSON(example);
        // JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = otnDao.getPerCircuitCheckRes(param ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    //每个地市的稽核结果报告
    @GetMapping("/getCheckRes")
    public BiyiPageResult<JSONObject> getCheckRes(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        log.debug("REST request to get entity list");
        JSONObject param = (JSONObject) JSON.toJSON(example);
        // JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        if (ObjectUtil.isEmpty(param)) {
            return new BiyiPageResult<>();
        } else {
            PageResponse<JSONObject> pageResponse = new PageResponse<>();
            String line_no = param.getString("line_no");
            String city = param.getString("city");
            if (!ObjectUtil.isEmpty(line_no)) {
                line_no = line_no.trim();
                JSONObject circuitExist = otnDao.checkCircuitExist(line_no, NRMConstants.SHARDING_GRAPH_DB);
                if (ObjectUtil.isEmpty(circuitExist)) {
                    //返回电路未找到稽核结果
                    return new BiyiPageResult<>();
                }
            }
            if (!ObjectUtil.isEmpty(city)) {
                JSONObject cityExist = otnDao.checkCityExist(city, NRMConstants.SHARDING_GRAPH_DB);
                if (ObjectUtil.isEmpty(cityExist)){
                    return new BiyiPageResult<>();
                }
                pageResponse = otnDao.getCheckResult(param ,pageable.getSize(),1, NRMConstants.SHARDING_GRAPH_DB);
            } else {
                List<JSONObject> res = new ArrayList<>();
                Integer totalcount = 0;
                for (int i = 0; i < cityList.size(); i++) {
                    String curCity = cityList.get(i);
                    JSONObject cityExist = otnDao.checkCityExist(curCity, NRMConstants.SHARDING_GRAPH_DB);
                    if (ObjectUtil.isEmpty(cityExist)){
                        continue;
                    }
                    param.put("city", curCity);
                    pageResponse = otnDao.getCheckResult(param ,pageable.getSize(),1, NRMConstants.SHARDING_GRAPH_DB);
                    List<JSONObject> data = pageResponse.getData();
                    res.addAll(data);
                    totalcount = totalcount + pageResponse.getPageInfo().getTotalCount();
                }
                pageResponse.setData(res);
                pageResponse.getPageInfo().setTotalCount(totalcount);
            }
            return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
        }
    }

    @RequestMapping("/query-invalid")
    public ResponseEntity<List<JSONObject>> queryInValidKB(@RequestBody JSONObject request, BiyiPageRequest pageable) {
        PageResponse<JSONObject> response= otnDao.pageQueryInvalid(request, pageable.getSize(), pageable.getPage(), "ds_graph_js");
        List<JSONObject> data= response.getData();
        return ResponseEntity.ok(data);
    }

    //稽核电路网元端口及光路
    @RequestMapping("/refresh-res-info")
    public ResponseEntity<JSONObject> resInfo(@RequestBody JSONObject request) {
        String id= request.getString("id");
        JSONObject result = new JSONObject();
        if (ObjectUtil.isEmpty(id)) {
            return ResponseEntity.ok().body(request);
        }
        JSONObject req = new JSONObject();
        String type=request.getString("line_type");
        req.put("code", request.getString("line_no"));
        req.put("a_city", request.getString("a_city"));
        req.put("z_city", request.getString("z_city"));
        if (type.equals("OTN")) {
            otn(req);
        }
        return ResponseEntity.ok(result);
    }

    @PostMapping("/download")
    public void download(@RequestBody JSONObject param, HttpServletResponse resp) throws IOException {
        String city = param.getString("city");
        if (ObjectUtil.isEmpty(city)) {
            return;
        }
        int currentPage = 1;
        int pageSize = 1000;
        PageResponse<JSONObject> checkResultByCity = otnDao.getCheckResultByCity(param,pageSize,currentPage,NRMConstants.SHARDING_GRAPH_DB);
        int totalPage = checkResultByCity.getPageInfo().getTotalPage();
        List<JSONObject> result = new ArrayList<>();
        for (int i = 1; i <= totalPage; i++) {
            Page pageInfo = new Page();
            pageInfo.setCurrentPage(i);
            pageInfo.setPageSize(pageSize);
            PageResponse<JSONObject> response = otnDao.getCheckResultByCity(param, pageInfo.getPageSize(), pageInfo.getCurrentPage(), NRMConstants.SHARDING_GRAPH_DB);
            if (!ObjectUtil.isEmpty(response) && !ObjectUtil.isEmpty(response.getData())) {
                result.addAll(response.getData());
            }
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("OTN稽核结果");
        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("分公司");
        header.createCell(1).setCellValue("电路名称");
        header.createCell(2).setCellValue("OTMS A设备名称");
        header.createCell(3).setCellValue("OTMS Z设备名称");
        header.createCell(4).setCellValue("OTMS A端口编码");
        header.createCell(5).setCellValue("OTMS Z端口编码");
        header.createCell(6).setCellValue("OTMS 网管光路或工程跳接");
        header.createCell(7).setCellValue("稽核结果");
        int rowIndex = 1;
        for (JSONObject l: result) {
            XSSFRow row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(l.getString("city"));
            row.createCell(1).setCellValue(l.getString("line_no"));
            row.createCell(2).setCellValue(l.getString("a_nm_device_code"));
            row.createCell(3).setCellValue(l.getString("z_nm_device_code"));
            row.createCell(4).setCellValue(l.getString("a_nm_device_port"));
            row.createCell(5).setCellValue(l.getString("z_nm_device_port"));
            row.createCell(6).setCellValue(l.getString("jt_fiber_code"));
            row.createCell(7).setCellValue(l.getString("note"));
        }

        // 设置响应头信息
        resp.setContentType("application/vnd.ms-excel");
        resp.setHeader("Content-Disposition", "attachment; filename=list.xlsx");

        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = resp.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    //更新每条电路稽核完整度
    @RequestMapping("/refresh-per-circuit-percent")
    public ResponseEntity<JSONObject> gerPerCircuitCheckRes() {
        int currentPage = 1;
        int pageSize = 1000;
        JSONObject param = new JSONObject();
        param.put("status", 1);
        PageResponse<JSONObject> checkResultByCity = otnDao.pageQuery(param,pageSize,currentPage,NRMConstants.SHARDING_GRAPH_DB);
        int totalPage = checkResultByCity.getPageInfo().getTotalPage();
        List<JSONObject> circuits = new ArrayList<>();
        for (int i = 1; i <= totalPage; i++) {
            Page pageInfo = new Page();
            pageInfo.setCurrentPage(i);
            pageInfo.setPageSize(pageSize);
            PageResponse<JSONObject> response = otnDao.pageQuery(param, pageInfo.getPageSize(), pageInfo.getCurrentPage(), NRMConstants.SHARDING_GRAPH_DB);
            if (!ObjectUtil.isEmpty(response) && !ObjectUtil.isEmpty(response.getData())) {
                circuits.addAll(response.getData());
            }
        }
        for (int i = 0; i < circuits.size(); i++) {
            JSONObject curCircuit = new JSONObject();
            String line_no = circuits.get(i).getString("line_no");
            curCircuit.put("line_no", line_no);

            JSONObject exist = otnDao.checkCircuitExist(line_no, NRMConstants.SHARDING_GRAPH_DB);
            if (ObjectUtil.isEmpty(exist)) {
                continue;
            }
            PageResponse<JSONObject> curCircuitCheckResponse = otnDao.getCheckResult(curCircuit, 10, 1, NRMConstants.SHARDING_GRAPH_DB);
            List<JSONObject> data = curCircuitCheckResponse.getData();
            if (!ObjectUtil.isEmpty(data)) {
                JSONObject curCircuitCheckResult = data.get(0);
                curCircuit.putAll(curCircuitCheckResult);
                otnDao.savePerCircuitCheckRes(curCircuit, NRMConstants.SHARDING_GRAPH_DB);
            }
        }
        return ResponseEntity.ok().body(new JSONObject());
    }

    private void otn(JSONObject param) {
        JSONObject jsonObject = otnService.queryOTNRoute(param);
        if (ObjectUtil.isEmpty(jsonObject)) {
            return;
        }
        JSONArray links = jsonObject.getJSONArray("links");
        JSONArray nodes = jsonObject.getJSONArray("nodes");
        //设备id---设备信息
        HashMap<String, String> deviceMap = new HashMap();
        for (int i = 0; i < nodes.size(); i++) {
            deviceMap.put(nodes.getJSONObject(i).getString("id"), nodes.getJSONObject(i).getString("name"));
        }
        //最底层的linklist
        ArrayList<JSONObject> linkList = new ArrayList<>();
        ArrayList<JSONObject> changtuList = new ArrayList<>();
//        recur(links, linkList);
        recurOTNAndChangtu(links, linkList, changtuList, deviceMap);
        otnDao.delDevicePortINTF(param.getString("code"), NRMConstants.SHARDING_GRAPH_DB);
        boolean flag = true;
        for (int i = 0; i < linkList.size(); i++) {
            JSONObject intfRes = new JSONObject();
            intfRes.put("line_no", param.getString("code"));
            JSONObject curLink = linkList.get(i);
            String a_nm_device_id = curLink.getString("a_physic_device_id");
            String z_nm_device_id = curLink.getString("z_physic_device_id");
            String a_region_id = curLink.getString("a_region_id");
            String z_region_id = curLink.getString("z_region_id");
            String a_nm_device_code_full = deviceMap.get(a_nm_device_id);
            String z_nm_device_code_full = deviceMap.get(z_nm_device_id);
            intfRes.put("a_nm_device_code", a_nm_device_code_full);
            intfRes.put("z_nm_device_code", z_nm_device_code_full);
            String a_city = "";
            String z_city = "";
            if (ObjectUtil.isEmpty(a_region_id)) {
                a_city = judgeCity(deviceMap.get(a_nm_device_id));
                if (ObjectUtil.isEmpty(a_city)) {
                    if (a_nm_device_code_full.contains("省外")){
                        continue;
                    }
                    //写到地区映射表，到表里关联设备编码和地区
                    a_city = otnDao.getDeviceRegion(a_nm_device_code_full, NRMConstants.SHARDING_GRAPH_DB);
                    if (ObjectUtil.isEmpty(a_city)) {
                        if (!ObjectUtil.isEmpty(a_nm_device_code_full)) {
                            otnDao.saveDeviceWithoutRegion(a_nm_device_code_full, NRMConstants.SHARDING_GRAPH_DB);
                            flag = false;
                            continue;
                        }
                    }
                }
            } else {
                a_city = areaMap.get(a_region_id);
            }
            if (ObjectUtil.isEmpty(z_region_id)) {
                z_city = judgeCity(deviceMap.get(z_nm_device_id));
                if (ObjectUtil.isEmpty(z_city)) {
                    if (z_nm_device_code_full.contains("省外")){
                        continue;
                    }
                    //写到地区映射表，到表里关联设备编码和地区
                    z_city = otnDao.getDeviceRegion(z_nm_device_code_full, NRMConstants.SHARDING_GRAPH_DB);
                    if (ObjectUtil.isEmpty(z_city)) {
                        if (!ObjectUtil.isEmpty(z_nm_device_code_full)) {
                            otnDao.saveDeviceWithoutRegion(z_nm_device_code_full, NRMConstants.SHARDING_GRAPH_DB);
                            flag = false;
                            continue;
                        }
                    }
                }
            } else {
                z_city = areaMap.get(z_region_id);
            }
            if (ObjectUtil.isEmpty(a_city) || ObjectUtil.isEmpty(z_city) || !a_city.equals(z_city)) {
                continue;
            }
            String ashardingCode = "ds_bc_o3_" + a_city;
            String zshardingCode = "ds_bc_o3_" + z_city;
            String a_nm_device_port_full = curLink.getString("a_port_code");
            String z_nm_device_port_full = curLink.getString("z_port_code");
            String a_nm_device_port = splitHalfPort(a_nm_device_port_full);
            String z_nm_device_port = splitHalfPort(z_nm_device_port_full);
            intfRes.put("a_nm_device_port", a_nm_device_port);
            intfRes.put("z_nm_device_port", z_nm_device_port);
            String jt_fiber_code = curLink.getString("code");
            intfRes.put("jt_fiber_code", jt_fiber_code);
            Device a_device = otnDao.getDeviceByNMDeviceCode(a_nm_device_code_full, ashardingCode);
            Device z_device = otnDao.getDeviceByNMDeviceCode(z_nm_device_code_full, zshardingCode);
            String a_port_id = "";
            String z_port_id = "";
            String note = "";
            if (!ObjectUtil.isEmpty(a_device)) {
                intfRes.put("a_device_code", a_device.getName());
                intfRes.put("a_device_id", a_device.getId());
                JSONObject par = new JSONObject();
                par.put("device_id", a_device.getId());
                par.put("nm_port_code", a_nm_device_port);
                Port aport = otnDao.getPortByNMPort(par, ashardingCode);
                if (!ObjectUtil.isEmpty(aport)) {
                    intfRes.put("a_device_port", aport.getCode());
                    intfRes.put("a_device_port_standard", aport.getNmCode());
                    intfRes.put("a_port_id", aport.getId());
                    a_port_id = aport.getId();
                } else if (!ObjectUtil.isEmpty(jt_fiber_code) && !jt_fiber_code.contains("f")) {
                    note = note + "A端端口未找到。";
                }
            } else {
                note = note + "A端设备未找到，A端端口未找到。";
            }
            if (!ObjectUtil.isEmpty(z_device)) {
                intfRes.put("z_device_code", z_device.getName());
                intfRes.put("z_device_id", z_device.getId());
                JSONObject par = new JSONObject();
                par.put("device_id", z_device.getId());
                par.put("nm_port_code", z_nm_device_port);
                Port zport = otnDao.getPortByNMPort(par, zshardingCode);
                if (!ObjectUtil.isEmpty(zport)) {
                    intfRes.put("z_device_port", zport.getCode());
                    intfRes.put("z_device_port_standard", zport.getNmCode());
                    intfRes.put("z_port_id", zport.getId());
                    z_port_id = zport.getId();
                } else if (!ObjectUtil.isEmpty(jt_fiber_code) && !jt_fiber_code.contains("f")) {
                    note = note + "Z端端口未找到。";
                }
            } else {
                note = note + "Z端设备未找到，Z端端口未找到。";
            }
            if (!ObjectUtil.isEmpty(jt_fiber_code) && a_city.equals(z_city)) {
                if (jt_fiber_code.startsWith("F")) {
                    Fiber fiber = otnDao.getFiberByJTFiber(jt_fiber_code, ashardingCode);
                    if (ObjectUtil.isEmpty(fiber)) {
                        //光路稽核错误
                        intfRes.put("is_fiber_right", 0);
                        note = note + "光纤光路未找到。";
                    } else {
                        String aportIdFiber = fiber.getAportId();
                        String zportIdFiber = fiber.getZportId();
                        if (!ObjectUtil.isEmpty(a_port_id) && !ObjectUtil.isEmpty(z_port_id) && !ObjectUtil.isEmpty(aportIdFiber) && !ObjectUtil.isEmpty(zportIdFiber)
                               && ((aportIdFiber.equals(a_port_id) && zportIdFiber.equals(z_port_id)) || (aportIdFiber.equals(z_port_id) && zportIdFiber.equals(a_port_id)))) {
                            intfRes.put("is_fiber_right", 1);
                            intfRes.put("pro_fiber_code", fiber.getCode());
                            intfRes.put("pro_fiber_id", fiber.getId());
                        } else {
                            intfRes.put("is_fiber_right", 0);
                            note = note + "光纤光路AZ端口未找到。";
                        }
                    }
                } else {
                    if (ObjectUtil.isEmpty(a_device) || ObjectUtil.isEmpty(z_device) || ObjectUtil.isEmpty(a_device.getFacilityID()) || ObjectUtil.isEmpty(z_device.getFacilityID())){
                        intfRes.put("is_fiber_right", 0);
                    } else if (!a_device.getFacilityID().equals(z_device.getFacilityID())) {
                        if (!ObjectUtil.isEmpty(a_port_id) && !ObjectUtil.isEmpty(z_port_id)) {
                            JSONObject tem = new JSONObject();
                            tem.put("a_port_id", a_port_id);
                            tem.put("z_port_id", z_port_id);
                            Fiber fiberByPort = otnDao.getFiberByPort(tem, ashardingCode);
                            if (ObjectUtil.isEmpty(fiberByPort)) {
                                intfRes.put("is_fiber_right", 0);
                                note = note + "AZ端设备未同机房。";
                            } else {
                                intfRes.put("is_fiber_right", 0);
                                note = note + "AZ端设备未同机房。省资源对应光路：" + fiberByPort.getCode() + "。";
                                intfRes.put("pro_fiber_code", fiberByPort.getCode());
                            }
                        } else {
                            intfRes.put("is_fiber_right", 0);
                            note = note + "AZ端设备未同机房。";
                        }
//                        intfRes.put("is_fiber_right", 0);
//                        note = note + "AZ端设备未同机房。";
                    } else {
                        intfRes.put("is_fiber_right", 1);
                    }
                }
//                else {
//                    intfRes.put("is_fiber_right", 1);
//                }
            } else {
                intfRes.put("is_fiber_right", 1);
            }
            intfRes.put("note", note);
            if (!a_city.equals(z_city)) {
                if (note.contains("A") || ObjectUtil.isEmpty(note)) {
                    intfRes.put("city", a_city);
                    otnDao.saveDevicePortINTF(intfRes, NRMConstants.SHARDING_GRAPH_DB);
                }
                if (note.contains("Z") || ObjectUtil.isEmpty(note)) {
                    intfRes.put("city", z_city);
                    otnDao.saveDevicePortINTF(intfRes, NRMConstants.SHARDING_GRAPH_DB);
                }
            } else {
                intfRes.put("city", a_city);
                otnDao.saveDevicePortINTF(intfRes, NRMConstants.SHARDING_GRAPH_DB);
            }
        }
        if (flag) {
            otnDao.updateOTNStatus(param.getString("code"), NRMConstants.SHARDING_GRAPH_DB);
        } else {
            otnDao.delDevicePortINTF(param.getString("code"), NRMConstants.SHARDING_GRAPH_DB);
        }
    }

    public void recur(JSONArray links, ArrayList<JSONObject> linkList) {
        for (int i = 0; i < links.size(); i++) {
            JSONObject jsonObject = links.getJSONObject(i);
            if (ObjectUtil.isEmpty(jsonObject.getJSONArray("children"))) {
                String a_ne_id = jsonObject.getString("a_physic_device_id");
                String z_ne_id = jsonObject.getString("z_physic_device_id");
                String adeviceResDomain = otnDao.getDeviceResDomain(a_ne_id, NRMConstants.SHARDING_GRAPH_DB);
                String zdeviceResDomain = otnDao.getDeviceResDomain(z_ne_id, NRMConstants.SHARDING_GRAPH_DB);
                if (!ObjectUtil.isEmpty(adeviceResDomain) && !ObjectUtil.isEmpty(zdeviceResDomain) && "1000".equals(adeviceResDomain) && "1000".equals(zdeviceResDomain)){
                    continue;
                } else {
                    linkList.add(jsonObject);
                }
            } else {
                recur(jsonObject.getJSONArray("children"), linkList);
            }
        }
    }

    public void recurOTNAndChangtu(JSONArray links, ArrayList<JSONObject> linkList, ArrayList<JSONObject> changtuList, HashMap<String, String> deviceMap) {
        for (int i = 0; i < links.size(); i++) {
            JSONObject jsonObject = links.getJSONObject(i);
            if (1 == jsonObject.getInteger("layer")) {
                String z_region_id = jsonObject.getString("z_region_id");
                String z_nm_device_id = jsonObject.getString("z_physic_device_id");
                String z_nm_device_code_full = deviceMap.get(z_nm_device_id);
                String z_city = getCity(z_region_id, z_nm_device_code_full);
                String a_region_id = jsonObject.getString("a_region_id");
                String a_nm_device_id = jsonObject.getString("a_physic_device_id");
                String a_nm_device_code_full = deviceMap.get(a_nm_device_id);
                String a_city = getCity(a_region_id, a_nm_device_code_full);
                String a_ne_id = jsonObject.getString("a_physic_device_id");
                String z_ne_id = jsonObject.getString("z_physic_device_id");
                String adeviceResDomain = otnDao.getDeviceResDomain(a_ne_id, NRMConstants.SHARDING_GRAPH_DB);
                String zdeviceResDomain = otnDao.getDeviceResDomain(z_ne_id, NRMConstants.SHARDING_GRAPH_DB);
                if (!ObjectUtil.isEmpty(adeviceResDomain) && !ObjectUtil.isEmpty(zdeviceResDomain) && "1000".equals(adeviceResDomain) && "1000".equals(zdeviceResDomain)){
                    recurOTNAndChangtu(jsonObject.getJSONArray("children"), changtuList, linkList, deviceMap);
                    continue;
                }
                if (ObjectUtil.isEmpty(a_city) || ObjectUtil.isEmpty(z_city) || !a_city.equals(z_city)) {
                    recurOTNAndChangtu(jsonObject.getJSONArray("children"), changtuList, linkList, deviceMap);
                    continue;
                }
            }
            if (ObjectUtil.isEmpty(jsonObject.getJSONArray("children"))) {
                linkList.add(jsonObject);
            } else {
                recurOTNAndChangtu(jsonObject.getJSONArray("children"), linkList, changtuList, deviceMap);
            }
        }
    }

    public String getCity(String z_region_id, String z_nm_device_code_full){
        if (ObjectUtil.isEmpty(z_region_id)) {
            String z_city = judgeCity(z_nm_device_code_full);
            if (ObjectUtil.isEmpty(z_city)) {
                if (z_nm_device_code_full.contains("省外")){
                    return "";
                }
                //写到地区映射表，到表里关联设备编码和地区
                z_city = otnDao.getDeviceRegion(z_nm_device_code_full, NRMConstants.SHARDING_GRAPH_DB);
                if (ObjectUtil.isEmpty(z_city)) {
                    if (!ObjectUtil.isEmpty(z_nm_device_code_full)) {
                        otnDao.saveDeviceWithoutRegion(z_nm_device_code_full, NRMConstants.SHARDING_GRAPH_DB);
                    }
                    return "";
                }
            }
            return z_city;
        } else {
            String z_city = areaMap.get(z_region_id);
            return z_city;
        }
    }

    public String splitDevice(String device) {
        if (ObjectUtil.isEmpty(device)){
            return device;
        }
        String[] split = device.split("-");
        return split[0];
    }

    public String splitHalfPort(String port) {
        if (ObjectUtil.isEmpty(port)){
            return port;
        }
        String[] split = port.split("_");
        if (split.length > 1) {
            return split[0].charAt(split[0].length() - 1) + "_" + split[1];
        } else {
            return port;
        }
    }

    public String judgeCity(String code) {
        if (ObjectUtil.isEmpty(code)) {
            return null;
        }
        if (code.contains("南京")){
            return "nj";
        }else if (code.contains("苏州")){
            return "sz";
        }else if (code.contains("南通")){
            return "nt";
        }else if (code.contains("宿迁")){
            return "sq";
        }else if (code.contains("常州")){
            return "cz";
        }else if (code.contains("淮安")){
            return "ha";
        }else if (code.contains("连云港")){
            return "lyg";
        }else if (code.contains("扬州")){
            return "yz";
        }else if (code.contains("盐城")){
            return "yc";
        }else if (code.contains("泰州")){
            return "tz";
        }else if (code.contains("镇江")){
            return "zj";
        }else if (code.contains("无锡")){
            return "wx";
        }else if (code.contains("徐州")){
            return "xz";
        }
        return null;
    }

}
