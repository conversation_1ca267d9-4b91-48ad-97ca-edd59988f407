package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.AccessDeviceUplinkDao;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.AccessDeviceUplinkService;
import com.telecom.nrm.service.OptGroupService;
import com.telecom.nrm.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.IOException;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/AccessDeviceUplink-api")
@Slf4j
public class AccessDeviceUplinkController {

    @Autowired
    AccessDeviceUplinkService accessDeviceUplinkService;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    AccessDeviceUplinkDao accessDeviceUplinkDao;

    @Autowired
    RegionDao regionDao;

//    @GetMapping("")
//    public BiyiPageResult<JSONObject> getPage(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
//        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
//        return accessDeviceUplinkService.getInfo(jsonObject,pageable);
//    }
//
//    @GetMapping("/dictionary")
//    public ResponseEntity<JSONObject> dictionary(@RequestParam(required = false) Map example) {
//        log.info("REST request to get dictionary : {}", example);
//        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
//        JSONObject meta = new JSONObject();
//        // 获取1级地区清单
//        Region regionParam = new Region();
//
//
//        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
//        List<Region> result = new ArrayList<>();
//        for(int i =0;i<Region2List.size();i++){
//
//            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100699"))||Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
//                if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100699"))){
//                    Region2List.get(i).setName("江苏省");
//                }
//                result.add(Region2List.get(i));
//            }
//        }
//        meta.put("region2List", result);
//        return ResponseEntity.ok().body(meta);
//    }

    @PostMapping("/deleteOptGroupRecord")
    public Integer deleteOptGroupRecord(@RequestBody JSONObject jsonObject) throws IOException {
        return accessDeviceUplinkDao.pm_opt_road_group_delete(jsonObject,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/deleteOpt")
    public Integer deleteOptRecord(@RequestBody JSONObject jsonObject) throws IOException {
        return accessDeviceUplinkDao.pm_opt_road_group_element_delete(jsonObject,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/upload_opt_group")
    public void upload_opt_group(@RequestParam("file") MultipartFile file, @RequestParam("ds") String ds) throws IOException {
        // String ds = "无锡";
        List<Map<String, String>> list = null;
        Sheet sheet = null;
        Row row = null;
        String cellData = null;
        List<String> keys = null;
        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            keys = new ArrayList<>();
            log.info("wb:" + rownum + "," + column);

            String areaName = "";
            String name = "";
            String code = "";

            List<JSONObject> dataList = new ArrayList<>();
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data = new JSONObject();
                    for (int j = 0; j < column; j++) {
                        if(j==0 ) {
                            if (ObjectUtil.isNotEmpty(sheet.getRow(i).getCell(j).getStringCellValue())) {
                                areaName = sheet.getRow(i).getCell(j).getStringCellValue();
                                current_data.put("ds", areaName);
                            }else{
                                current_data.put("ds", areaName);
                            }
                        };

                        if (j==1) {
                            if (ObjectUtil.isNotEmpty(sheet.getRow(i).getCell(j).getStringCellValue())) {
                                name = sheet.getRow(i).getCell(j).getStringCellValue();
                                current_data.put("name", name);
                            }else{
                                current_data.put("name", name);
                            }
                        }

                        if (j==2) {
                            if (ObjectUtil.isNotEmpty(sheet.getRow(i).getCell(j).getStringCellValue())) {
                                code = sheet.getRow(i).getCell(j).getStringCellValue();
                                current_data.put("code", code);
                            }else{
                                current_data.put("code", code);
                            }
                        }


                        if(j==3 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("opt_code",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==4 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("opt_name",sheet.getRow(i).getCell(j).getStringCellValue());
                    }
                    // log.info("目前处理到：{} , {}", i, current_data);
                    dataList.add(current_data);
                    // optGroupDao.pm_opt_road_group_insert(current_data, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                    // optGroupDao.pm_opt_road_group_element_insert(current_data, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                }
                log.info("pm_opt_road_group_insert");
                PageUtils.doSomethingByPage(v->{accessDeviceUplinkDao.pm_opt_road_group_insert(v, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));}, dataList, 500);
                log.info("pm_opt_road_group_element_insert");
                PageUtils.doSomethingByPage(v->{accessDeviceUplinkDao.pm_opt_road_group_element_insert(v, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));}, dataList, 500);
            }
        }
    }

    @PostMapping("/save_opt_group")
    public Integer saveOptGroup(@RequestBody JSONObject jsonObject) {
        // jsonObject.put("area_name", jsonObject.getString("ds"));
        if (ObjectUtil.isEmpty(jsonObject.getString("id"))) {
            List<JSONObject> list = Arrays.asList(jsonObject);
            accessDeviceUplinkDao.pm_opt_road_group_insert(list, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        }else {
            accessDeviceUplinkDao.pm_opt_road_group_note_update_device(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        }

        return 1;
    }

    @PostMapping("/save_opt_group_element")
    public Integer saveOptGroupElement(@RequestBody JSONObject jsonObject) {
        // jsonObject.put("code", jsonObject.getString("opt_code"));
        // jsonObject.put("name", jsonObject.getString("opt_name"));
        if (ObjectUtil.isEmpty(jsonObject.getString("id"))) {
            List<JSONObject> list = Arrays.asList(jsonObject);
            accessDeviceUplinkDao.pm_opt_road_group_element_insert(list, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        }else{

            accessDeviceUplinkDao.pm_opt_road_group_element_update(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        }

        return 1;
    }

    @GetMapping("")
    public BiyiPageResult<JSONObject> pageQuery(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {


        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> optGroupList = new ArrayList<JSONObject>();
        jsonObject.put("object_tyoe","optGroup");

        PageResponse<JSONObject> optGroupPage = accessDeviceUplinkDao.pm_opt_road_group_result_query_device(jsonObject, pageable.getSize(), pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
//        if(optGroupPage!=null &&optGroupPage.getData()!=null && optGroupPage.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(optGroupPage.getData());
            optGroupList.addAll(optGroupPage.getData());
//            System.out.println("ok");
//        }else{
//            System.out.println("fail");
//        }

        System.out.println(optGroupPage.getData().toString());

        List<String> ids = optGroupList.stream().map(optGroup -> optGroup.getString("id")).collect(Collectors.toList());
        JSONObject param_opt_group_member = new JSONObject();
        param_opt_group_member.put("ids", ids);
        List<JSONObject> opt_group_members = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(ids))opt_group_members = accessDeviceUplinkDao.pm_opt_road_group_element_query_details(param_opt_group_member, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        for(JSONObject optGroup: optGroupList) {
            String deal_result = optGroup.getString("dealResult");
            String check_result = optGroup.getString("checkResult");
            optGroup.put("result", ObjectUtil.isNotEmpty(optGroup.getString("dealResult")) ? deal_result : check_result);
            if(ObjectUtil.isNotEmpty(opt_group_members)) optGroup.put("members", opt_group_members.stream().filter(item -> item.getString("group_id").equals(optGroup.getString("id"))).collect(Collectors.toList()));

        }
        BiyiPageResult<JSONObject> result =  new BiyiPageResult(optGroupList,optGroupPage.getPageInfo().getTotalCount() ,optGroupPage.getPageInfo().getPageSize());
        return result;
    }
//    @GetMapping("")
//    public BiyiPageResult<JSONObject> getInfo(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
//        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
//        List<JSONObject> AccessDeviceUplinkList = new ArrayList<JSONObject>();
//        jsonObject.put("object_tyoe","AccessDeviceUplink");
//        PageResponse<JSONObject> AccessDeviceUplinkPage = accessDeviceUplinkDao.pm_opt_road_group_result_query_device(jsonObject, pageable.getSize(), pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
//        if(AccessDeviceUplinkPage!=null &&AccessDeviceUplinkPage.getData()!=null && AccessDeviceUplinkPage.getData().size() !=0){
//            JSONObjectUtil.convertBigNumberToString(AccessDeviceUplinkPage.getData());
//            AccessDeviceUplinkList.addAll(AccessDeviceUplinkPage.getData());
//        }
//        List<String> ids = AccessDeviceUplinkList.stream().map(AccessDeviceUplink -> AccessDeviceUplink.getString("id")).collect(Collectors.toList());
//        JSONObject param_opt_group_member = new JSONObject();
//        param_opt_group_member.put("ids", ids);
//        List<JSONObject> opt_group_members = new ArrayList<>();
//        if(ObjectUtil.isNotEmpty(ids))opt_group_members = accessDeviceUplinkDao.pm_opt_road_group_element_query(param_opt_group_member, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
//        for(JSONObject AccessDeviceUplink: AccessDeviceUplinkList) {
//            String deal_result = AccessDeviceUplink.getString("dealResult");
//            String check_result = AccessDeviceUplink.getString("checkResult");
//            AccessDeviceUplink.put("result", ObjectUtil.isNotEmpty(AccessDeviceUplink.getString("dealResult")) ? deal_result : check_result);
//            if(ObjectUtil.isNotEmpty(opt_group_members)) AccessDeviceUplink.put("members", opt_group_members.stream().filter(item -> item.getString("group_id").equals(AccessDeviceUplink.getString("id"))).collect(Collectors.toList()));
//
//        }
//        return new BiyiPageResult(AccessDeviceUplinkList,AccessDeviceUplinkPage.getPageInfo().getTotalCount() ,AccessDeviceUplinkPage.getPageInfo().getPageSize());
//    }



    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> get_dictionary(@RequestParam(required = false) Map example) {

        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> regionList = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> cityList = new ArrayList<>();
        for(int i =0;i<regionList.size();i++){
            if(regionList.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                cityList.add(regionList.get(i));
            }
        }
        meta.put("cityList", cityList);

        String currentCityName = exampleJSON.getString("ds");
        if (ObjectUtil.isNotEmpty(currentCityName)) {
            Region currentCity = cityList.stream().filter(c->c.getName().equals(currentCityName)).findFirst().orElse(new Region());
            String currentCityId = currentCity.getId();
            List<Region> leafRegionList = regionList.stream().filter(r->ObjectUtil.isNotEmpty(r.getParentId()) && r.getParentId().equals(currentCityId)).collect(Collectors.toList());
            meta.put("leafRegionList", leafRegionList);
        }else {
            meta.put("leafRegionList", new ArrayList<Region>());
        }

        List<String> StateList = new ArrayList<>();
        StateList.add("正常");
        StateList.add("无光路");
        StateList.add("单光路");
        StateList.add("单路由");
        StateList.add("缺管道");
        StateList.add("同管道");
        StateList.add("同光缆");
        StateList.add("局内光路");
        StateList.add("同局前井");

        /*
        JSONObject lifeState = new JSONObject();
        lifeState.put("stateName", "同管道");
        StateList.add("lifeState");
        JSONObject lifeState1 = new JSONObject();
        lifeState1.put("stateName", "同光缆");
        StateList.add(lifeState1);
        JSONObject lifeState3 = new JSONObject();
        lifeState3.put("stateName", "正常");
        StateList.add(lifeState3);
        JSONObject lifeState4 = new JSONObject();
        lifeState4.put("stateName", "已忽略异常");
        StateList.add(lifeState4);

         */

        meta.put("StateList", StateList);
        meta.put("Statistic", accessDeviceUplinkService.optPair_statistic_dictionary(exampleJSON));
        return ResponseEntity.ok().body(meta);
    }


    @GetMapping("/opt_pair_check")
    public void check(@RequestParam(required = false) Map example , BiyiPageRequest pageable){
        BiyiPageResult<JSONObject> multi_query = pageQuery(example, pageable);
        List<JSONObject> multi_query_list = multi_query.getData();
        for(JSONObject multi_opt : multi_query_list){
            JSONObject param = new JSONObject();
            param.put("ds",example.get("ds"));
            param.put("id", multi_opt.getString("id"));
            accessDeviceUplinkService.risk_analyze(param);
        }
    }

    @PostMapping("/note_update")
    public Integer note_update(@RequestBody JSONObject jsonObject){
        return accessDeviceUplinkDao.pm_opt_road_group_note_update_device(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/confirm_result")
    public Integer confirm_result(@RequestBody JSONObject jsonObject){
        jsonObject.put("object_type", "optGroup");
        jsonObject.put("confirm_status", "已确认");
        jsonObject.put("confirm_op", jsonObject.getString("userName"));
        System.out.println("ok"+jsonObject.toString());
        return accessDeviceUplinkDao.pm_risk_check_confirm(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/cancel_confirm_result")
    public Integer cancel_confirm_result(@RequestBody JSONObject jsonObject){
        jsonObject.put("object_type", "optGroup");
        jsonObject.put("confirm_status", "未确认");
        jsonObject.put("confirm_op", null);
        return accessDeviceUplinkDao.pm_risk_check_confirm(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/risk-analyze")
    public JSONObject riskAnalyze(@RequestBody JSONObject request, HttpServletResponse response){
        JSONObject result =  accessDeviceUplinkService.risk_analyze(request);
        return result;

    }

    @PostMapping("/white-insert")
    public Integer pair_circuit_input_fiber_result_corr_insert(@RequestBody JSONObject jsonObject){
        String type = jsonObject.getString("type");
        jsonObject.put("pipleline_code", jsonObject.getJSONObject("pipeSegment").getString("code"));
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_insert(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/white-delete")
    public Integer pipleline_white_delete(@RequestBody JSONObject jsonObject){
        String code = jsonObject.getJSONObject("pipeSegment").getString("code");
        if(code.contains("----已忽略")){
            code = code.substring(0, code.indexOf("----已忽略"));
        }
        jsonObject.put("pipleline_code", code.trim());
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_delete(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

}
