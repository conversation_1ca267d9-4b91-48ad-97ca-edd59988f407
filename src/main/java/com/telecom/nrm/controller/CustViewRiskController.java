package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.CustViewMemberDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/cust-view-risk-api")
@Slf4j
public class CustViewRiskController {

    @Autowired
    RegionDao regionDao;

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    /**
     * 客户视图对应的风险分析
     * @param example
     * @param pageable
     * @return
     */
    @GetMapping("/circuit_analyze")
    public BiyiPageResult<JSONObject> circuit_pair_analyze_analyze(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        if(jsonObject.getString("circuit_code")!=null || jsonObject.getString("circuit_code")=="" || !Objects.isNull(jsonObject.getString("circuit_code"))) {
            jsonObject.put("name", jsonObject.getString("circuit_code"));
        }
        PageResponse<JSONObject> custViewMemberPage =  custViewMemberDao.queryList(jsonObject ,10000,1, NRMConstants.SHARDING_GRAPH_DB);

        // JSONObject result = new JSONObject();
        List<JSONObject> custViewMemberList = custViewMemberPage.getData();

        List<JSONObject> data = new ArrayList<JSONObject>();
        System.out.println("custViewMemberList"+";"+custViewMemberList);
        List<String> code_param = new ArrayList<>();
        String ds = "无锡";
        if(ObjectUtil.isNotEmpty(custViewMemberList)){
            ds = !Objects.isNull(custViewMemberList.get(0).getString("area_name"))? custViewMemberList.get(0).getString("area_name"): custViewMemberList.get(1).getString("area_name");
            for(JSONObject custViewMember: custViewMemberList) {
                code_param.add(custViewMember.getString("circuit_code"));
            }
            jsonObject.put("codes", code_param);
            log.info("jsonobject"+";"+ds+";"+ jsonObject);
            List<JSONObject> circuitPairData = new ArrayList<>();
            if(!ds.equals("常州")){
                PageResponse<JSONObject> circuitPairPage =  custLinkInterfaceDao.getCircuit_pair(jsonObject ,10000,1,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                log.info("pageResponse");
                circuitPairData = circuitPairPage.getData();
                JSONObjectUtil.convertBigNumberToString(circuitPairData);
                circuitPairData.removeIf(circuitPair -> Objects.isNull(circuitPair.getString("result")));
                data.addAll(circuitPairData);
            }
            System.out.println("data+"+data);
            for(JSONObject custViewMember: custViewMemberList){
                int count =0;
                boolean isExist = false;
                for(JSONObject pair: circuitPairData){
                    if(custViewMember.getString("circuit_code").equals(pair.getString("a_code") )|| custViewMember.getString("circuit_code").equals(pair.getString("z_code"))){
                        isExist = true;
                        break;
                    }
                }
                if(!isExist){
                    JSONObject buf = new JSONObject();
                    buf.put("id",custViewMember.getString("id"));
                    buf.put("areacode",custViewMember.getString("area_name"));
                    buf.put("custName",custViewMember.getString("cus_name"));
                    buf.put("accessCode",custViewMember.getString("access_code"));
                    buf.put("aCode",custViewMember.getString("circuit_code"));
                    buf.put("aNotes",custViewMember.getString("notes"));
                    buf.put("service_type_name",custViewMember.getString("service_type_name"));
                    buf.put("aOptCode",custViewMember.getString("opt_code"));
                    buf.put("aOptRoad",custViewMember.getString("opt_road"));
                    buf.put("a_notes",custViewMember.getString("notes"));
                    buf.put("result",custViewMember.getString("result"));
                    buf.put("maintain_level",custViewMember.getString("maintain_level"));
                    data.add(buf);
                }
                if(custViewMember.getString("circuit_code").equals("常州深圳ETN0023NP")){
                    System.out.println("当前custViewMember："+custViewMember);
                }
            }
        }else{
            data = new ArrayList<>();
        }
        Long totalCount = (long)data.size();
        System.out.println("data_cur："+data);

        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    @PostMapping("/member-ignore")
    public Integer busi_member_res_cor(@RequestBody JSONObject jsonObject){
        jsonObject.getJSONObject("record").put("member_id", jsonObject.getJSONObject("record").getString("id"));
        jsonObject.getJSONObject("record").put("a_code", jsonObject.getJSONObject("record").getString("aCode"));
        jsonObject.getJSONObject("record").put("result", "正常");
        return custLinkInterfaceDao.pm_busi_user_scene_member_result_corr_insert(jsonObject.getJSONObject("record"), NRMConstants.SHARDING_GRAPH_DB);
    }

    @PostMapping("/delete-member-ignore")
    public Integer busi_member_res_cor_delete(@RequestBody JSONObject jsonObject){
        jsonObject.getJSONObject("record").put("member_id", jsonObject.getJSONObject("record").getString("id"));
        jsonObject.getJSONObject("record").put("a_code", jsonObject.getJSONObject("record").getString("aCode"));
        return custLinkInterfaceDao.pm_busi_user_scene_member_result_corr_delete(jsonObject.getJSONObject("record"), NRMConstants.SHARDING_GRAPH_DB);
    }
}
