package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.GraphAIDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphResponse;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URISyntaxException;
import java.util.UUID;

@RestController
@RequestMapping("/api/graph-ai-api")
@Slf4j
public class GraphAIController {
    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    GraphAIDao graphAIDao;

    @PostMapping("")
    public ResponseEntity<JSONObject> doApi(@RequestBody GraphRequest request) throws URISyntaxException {
        log.debug("REST request to save entity : {}", request);
        JSONObject response = new JSONObject();
        try {
            Graph graph=graphApiService.doApi(request);
            String resultKey =  UUID.randomUUID().toString();
            JSONObject insertParam=new JSONObject();
            insertParam.put("result",JSONObject.toJSONString(graph));
            insertParam.put("result_key", resultKey);
            graphAIDao.insertAIResult(insertParam, NRMConstants.SHARDING_GRAPH_DB);
            response.put("code", "200");
            response.put("message", "success");
            response.put("resultKey", resultKey);
        }catch (Exception ex) {
            log.error(ex.getMessage(),ex);
            response.put("code", "500");
            response.put("message", ex.getMessage());
        }
        return ResponseEntity.ok(response);
    }


    @PostMapping("/data")
    public ResponseEntity<GraphResponse> doApiData(@RequestBody GraphRequest request) throws URISyntaxException {
        log.debug("REST request to save entity : {}", request);
        GraphResponse graphResponse = new GraphResponse();
        try {
            Graph graph=graphApiService.doApi(request);
            JSONObject resultData = graphService.getData(graph);
            graphResponse.setData(resultData);
            graphResponse.setCode("200");
            graphResponse.setMessage("ok");
        }catch (Exception ex) {
            log.error(ex.getMessage(),ex);
            graphResponse.setCode("500");
            graphResponse.setMessage(ex.getMessage());
        }
        return ResponseEntity.ok(graphResponse);
    }

    

}
