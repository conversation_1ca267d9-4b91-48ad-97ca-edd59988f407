package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 智配Controller - 处理智能路由推荐相关功能
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/api/zhipei")
@Slf4j
public class ZhipeiController {

    private final RestTemplate restTemplate = new RestTemplate();
    
    /**
     * 获取智能路由推荐的加密令牌
     * 
     * @return 加密令牌响应
     */
    @PostMapping("/get-cipher-text")
    public ResponseEntity<Map<String, Object>> getCipherText() {
        try {
            log.info("🔐 开始获取智能路由推荐加密令牌");
            
            // 获取当前时间并延后6小时
            LocalDateTime currentTime = LocalDateTime.now().plusHours(6);
            String invokeTime = currentTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            
            log.info("📅 生成调用时间: {}", invokeTime);
            
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("systemSource", "CRM");
            requestData.put("cipherIndex", "3");
            requestData.put("encryptData", "invokeTime=" + invokeTime);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestData, headers);
            
            // 调用外部加密接口
            String encryptUrl = "http://*************:39056/uim/encrypt";
            log.info("🌐 调用加密接口: {}", encryptUrl);
            log.info("📤 请求参数: {}", requestData);
            
            ResponseEntity<String> response = restTemplate.postForEntity(encryptUrl, requestEntity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String cipherText = response.getBody();
                log.info("✅ 成功获取加密令牌: {}", cipherText);
                
                // 获取用户信息用于日志记录
                String operatorId = "UNKNOWN";
                String operatorName = "UNKNOWN";
                try {
                    JwtUser jwtUser = SecurityContext.getJwtUser();
                    if (jwtUser != null) {
                        operatorId = jwtUser.getId();
                        operatorName = jwtUser.getUsername();
                    }
                } catch (Exception e) {
                    log.warn("⚠️ 获取用户信息失败: {}", e.getMessage());
                }
                
                log.info("👤 操作用户: ID={}, Name={}", operatorId, operatorName);
                
                // 构建返回结果
                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("cipherText", cipherText);
                result.put("invokeTime", invokeTime);
                result.put("message", "成功获取加密令牌");
                
                return ResponseEntity.ok(result);
            } else {
                log.error("❌ 加密接口调用失败，状态码: {}", response.getStatusCode());
                
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "加密接口调用失败");
                errorResult.put("statusCode", response.getStatusCode().value());
                
                return ResponseEntity.ok(errorResult);
            }
            
        } catch (Exception e) {
            log.error("💥 获取加密令牌异常: {}", e.getMessage(), e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取加密令牌失败: " + e.getMessage());
            
            return ResponseEntity.ok(errorResult);
        }
    }
}
