package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.domain.NRMConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.util.StrUtil.trim;

@RestController
@RequestMapping("/api/obd_query")
@Slf4j
public class ObdQueryController {
    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;
    @PostMapping("")
    public BiyiPageResult<JSONObject> getInfo(@RequestBody JSONObject jsonObject, HttpServletResponse response){
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        List<JSONObject> result = new ArrayList<>();
        String area_code = areacodeTrans(jsonObject.getString("areaCode"));
        jsonObject.put("area_code", area_code);
        PageResponse<JSONObject> pageResponse = custLinkInterfaceDao.obd_acc_num(jsonObject, 1000,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(area_code));
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    public String areacodeTrans(String areaCode){
        String area_code = "";
        switch (areaCode.substring(0, areaCode.indexOf(".js"))){
            case "wx": area_code = "无锡";break;
            case "nj": area_code = "南京";break;
            case "xz": area_code = "徐州";break;
            case "cz": area_code = "常州";break;
            case "sz": area_code = "苏州";break;
            case "nt": area_code = "南通";break;
            case "lyg": area_code="连云港";break;
            case "ha": area_code ="淮安";break;
            case "yc": area_code = "盐城";break;
            case "yz": area_code ="扬州";break;
            case "zj": area_code ="镇江";break;
            case "tz": area_code ="泰州";break;
            case "sq": area_code = "宿迁";break;
        }
        return area_code;

    }



}
