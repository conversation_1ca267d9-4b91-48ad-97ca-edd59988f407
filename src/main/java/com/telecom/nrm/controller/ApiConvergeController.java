package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.DaClient;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.entity.TypeReference;
import com.telecom.nrm.service.UnifiedApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/apiconverge")
@Slf4j
public class ApiConvergeController {


    @Autowired
    private UnifiedApiService apiService;

    @Autowired
    DaClient client;

    @PostMapping("query")
    public JSONObject queryController(@RequestBody JSONObject request) {


        JSONObject result = apiService.executeApi(request,JSONObject.class);

        return result ;

    }

    @PostMapping("pagequery")
    public PageResponse<JSONObject> PagequeryController(@RequestBody JSONObject request) {

        PageResponse<JSONObject> result =  apiService.executeApi(
                request,
                new TypeReference<PageResponse<JSONObject>>() {});

        return result;

    }


    @PostMapping("insert")
    public int InsertController(@RequestBody JSONObject request) {

        Integer result = apiService.executeApi(request,Integer.class);
        return result ;

    }



    @PostMapping("update")
    public int UpdateController(@RequestBody JSONObject request) {

        Integer result = apiService.executeApi(request,Integer.class);
        return result ;

    }


    @PostMapping("delete")
    public Integer DeleteController(@RequestBody JSONObject request) {
        Integer result =apiService.executeApi(request,Integer.class);
        return result ;
    }




}
