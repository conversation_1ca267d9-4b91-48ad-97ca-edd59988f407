package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.CustViewMemberDao;
import com.telecom.nrm.dao.LifeCircuitDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.domain.Response;
import com.telecom.nrm.service.CustViewMemberService;
//import com.telecom.nrm.service.impl.LifeCircuitServiceImpl;
import com.telecom.nrm.service.LifeCircuitService;
import com.telecom.nrm.service.DocumentExportService;
import com.telecom.nrm.dto.ExportResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/life_circuit")
@Slf4j
public class LifeCircuitController {

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    RegionDao regionDao;


    @Autowired
    LifeCircuitDao lifeCircuitDao;


    @Autowired
    CustViewMemberService custViewMemberService;

    @Autowired
    LifeCircuitService lifeCircuitService;

    @Autowired
    DocumentExportService documentExportService;

    @GetMapping("")
    @LogAnnotation(interfaceName = "生命线业务查询")
    public BiyiPageResult<JSONObject> circuit_pair_analyze_analyze(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> custViewMemberPage =  custViewMemberDao.queryList(jsonObject ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> custViewMemberList = custViewMemberPage.getData();
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<String> code_param = new ArrayList<>();
        String ds = "";
        if(ObjectUtil.isNotEmpty(custViewMemberList)){
            ds = !Objects.isNull(custViewMemberList.get(0).getString("area_name"))? custViewMemberList.get(0).getString("area_name"): custViewMemberList.get(1).getString("area_name");
            List<JSONObject> circuitPairData = new ArrayList<>();
            if(1==0) {
                circuitPairData = lifeCircuitService.getCircuitPairByCustView(custViewMemberList, ds);
            }
            data.addAll(circuitPairData);
            for(JSONObject custViewMember: custViewMemberList){
                boolean isExist = false;
                for(JSONObject pair: circuitPairData){
                    if(custViewMember.getString("circuit_code").equals(pair.getString("aCode") )|| custViewMember.getString("circuit_code").equals(pair.getString("zCode"))){
                        isExist = true;
                        break;
                    }
                }
                if(!isExist){
                    JSONObject buf = new JSONObject();
                    buf.put("id",custViewMember.getString("id"));
                    buf.put("areacode",custViewMember.getString("area_name"));
                    buf.put("custName",custViewMember.getString("cus_name"));
                    buf.put("accessCode",custViewMember.getString("access_code"));
                    buf.put("aCode",custViewMember.getString("circuit_code"));
                    buf.put("aNotes",custViewMember.getString("notes"));
                    buf.put("service_type_name",custViewMember.getString("service_type_name"));
                    buf.put("aOptCode",custViewMember.getString("opt_code"));
                    buf.put("aOptRoad",custViewMember.getString("opt_road"));
                    buf.put("a_notes",custViewMember.getString("notes"));
                    buf.put("result",custViewMember.getString("result"));
                    buf.put("note",custViewMember.getString("note"));
                    buf.put("maintain_level",custViewMember.getString("maintain_level"));
                    data.add(buf);
                }
            }
        }else{
            data = new ArrayList<>();
        }
        return new BiyiPageResult(data, custViewMemberPage.getPageInfo().getTotalCount() , custViewMemberPage.getPageInfo().getTotalCount());
    }

    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary_f(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);

        List<JSONObject> lifeCircuitServiceTypeList =lifeCircuitDao.pm_busi_user_scene_member_service_type_name_query(new JSONObject(), NRMConstants.SHARDING_GRAPH_DB);

        meta.put("serviceTypeList", lifeCircuitServiceTypeList);

        List<JSONObject> lifeStateList = new ArrayList<>();
        JSONObject lifeState = new JSONObject();
        lifeState.put("stateName", "同管道");
        lifeStateList.add(lifeState);
        JSONObject lifeState1 = new JSONObject();
        lifeState1.put("stateName", "同光缆");
        lifeStateList.add(lifeState1);
        JSONObject lifeState2 = new JSONObject();
        lifeState2.put("stateName", "无接入光路");
        lifeStateList.add(lifeState2);
        JSONObject lifeState3 = new JSONObject();
        lifeState3.put("stateName", "正常");
        lifeStateList.add(lifeState3);
        JSONObject lifeState4 = new JSONObject();
        lifeState4.put("stateName", "已忽略异常");
        lifeStateList.add(lifeState4);
        JSONObject lifeState5 = new JSONObject();
        lifeState5.put("stateName", "已拆机");
        lifeStateList.add(lifeState5);
        JSONObject lifeState6 = new JSONObject();
        lifeState6.put("stateName", "单接入光路");
        lifeStateList.add(lifeState6);
        meta.put("lifeStateList", lifeStateList);


        meta.put("lifeCircuitStatistic", lifeCircuitService.lifeCircuit_statistic_dictionary(exampleJSON));
        log.info(exampleJSON.toJSONString());
        meta.put("lifeCircuitStatisticHis", lifeCircuitService.lifeCircuit_statistic_his_dictionary(exampleJSON));

        return ResponseEntity.ok().body(meta);
    }


    @PostMapping("/update_circuit_by_acc_code")
    public Integer updateCircuitByAccessCode(@RequestBody JSONObject jsonObject){
        JSONObject circuit =new JSONObject();
        if(Objects.isNull(jsonObject.getString("area_name"))){
            return 0;
        }
        PageResponse<JSONObject> pageResponse = custLinkInterfaceDao.getCustLink(jsonObject, 10,1,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("area_name")));
        if(pageResponse!=null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
            circuit = pageResponse.getData().get(0);
            log.info("ciruc"+circuit);
        }
        if(ObjectUtil.isNotEmpty(circuit)){
            JSONObject param = new JSONObject();
            param.put("service_id", circuit.getString("service_id"));
            param.put("circuit_code", circuit.getString("circuit_code"));
            return lifeCircuitDao.pm_busi_user_scene_member_circuit_code_query(param, NRMConstants.SHARDING_GRAPH_DB);
        }else{
            return 0;
        }
    }

    @PostMapping("/insert_white")
    public Integer insert_white(@RequestBody JSONObject jsonObject){
        return lifeCircuitService.insert_white(jsonObject);
    }

    @PostMapping("/delete_white")
    public Integer delete_white(@RequestBody JSONObject jsonObject){
        jsonObject.put("deal_result",null);
        return lifeCircuitDao.pm_busi_user_scene_member_result_deal_update(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    @PostMapping("/note_update")
    public Integer note_update(@RequestBody JSONObject jsonObject){
        return lifeCircuitDao.pm_busi_user_scene_member_note_update(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }


    @PostMapping("/download")
    public ResponseEntity<ExportResponseDTO> download_cust_view(@RequestBody JSONObject jsonObject) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("🎯 [生命线导出] 接收到生命线电路下载请求");
            log.info("📋 [生命线导出] 请求参数: {}", jsonObject);

            // 参数验证
            if (jsonObject == null || jsonObject.isEmpty()) {
                log.error("❌ [生命线导出] 请求参数为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("请求参数不能为空"));
            }

            String areaName = jsonObject.getString("area_name");
            String sceneId = jsonObject.getString("scene_id");
            log.info("📍 [生命线导出] 查询地区: {}, 场景ID: {}", areaName, sceneId);

            // 1. 查询生命线电路数据
            log.info("🔍 [生命线导出] 步骤1: 开始查询生命线电路数据");
            long queryStartTime = System.currentTimeMillis();
            BiyiPageRequest pageable = new BiyiPageRequest();
            pageable.setSize(1000000);
            pageable.setPage(1);
            BiyiPageResult<JSONObject> result = circuit_pair_analyze_analyze(jsonObject, pageable);
            long queryEndTime = System.currentTimeMillis();
            log.info("⏱️ [生命线导出] 步骤1完成: 数据查询耗时 {} ms", (queryEndTime - queryStartTime));

            if (result == null || result.getData() == null || result.getData().size() == 0) {
                log.warn("⚠️ [生命线导出] 查询结果为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("没有查询到生命线电路数据"));
            }

            JSONObjectUtil.convertBigNumberToString(result.getData());
            List<JSONObject> resultData = result.getData();
            log.info("📊 [生命线导出] 查询到数据量: {} 条", resultData.size());

            // 2. 转换数据格式
            log.info("🔄 [生命线导出] 步骤2: 开始转换数据格式");
            long convertStartTime = System.currentTimeMillis();
            List<Map<String, Object>> exportData = convertLifeCircuitData(resultData);
            long convertEndTime = System.currentTimeMillis();
            log.info("⏱️ [生命线导出] 步骤2完成: 数据转换耗时 {} ms", (convertEndTime - convertStartTime));
            log.info("📊 [生命线导出] 转换后数据量: {} 条记录", exportData.size());

            // 3. 定义列
            log.info("📝 [生命线导出] 步骤3: 定义Excel列结构");
            List<String> columns = Arrays.asList(
                "地区:area_name",
                "客户名称:customer_name",
                "业务号码:access_code",
                "电路编号:code",
                "光路编码:opt_code",
                "光路名称:opt_road",
                "电路备注:notes",
                "业务类型:service_type_name",
                "是否生命线:is_life_display",
                "检测结果:result",
                "处理结果:deal_result",
                "A端地址:a_address_name",
                "Z端地址:z_address_name",
                "创建时间:create_time"
            );
            log.info("📋 [生命线导出] 步骤3完成: 定义了 {} 个列", columns.size());

            // 4. 使用通用导出服务
            log.info("📤 [生命线导出] 步骤4: 开始调用文档导出服务");
            long exportStartTime = System.currentTimeMillis();
            ExportResponseDTO exportResult = documentExportService.exportToDocumentSecurity(
                exportData, columns, "生命线电路数据", "生命线电路下载",
                "生命线管理", "/api/life-circuit/download", "生命线电路数据导出"
            );
            long exportEndTime = System.currentTimeMillis();
            log.info("⏱️ [生命线导出] 步骤4完成: 文档导出服务耗时 {} ms", (exportEndTime - exportStartTime));

            if (exportResult.isSuccess()) {
                exportResult.setDataCount(exportData.size());
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("✅ [生命线导出] 导出成功完成!");
                log.info("📁 [生命线导出] 文件名: {}", exportResult.getFileName());
                log.info("📊 [生命线导出] 数据量: {} 条", exportData.size());
                log.info("⏱️ [生命线导出] 总耗时: {} ms", totalTime);
                log.info("📈 [生命线导出] 性能统计 - 查询: {}ms, 转换: {}ms, 导出: {}ms",
                    (queryEndTime - queryStartTime), (convertEndTime - convertStartTime), (exportEndTime - exportStartTime));
            } else {
                log.error("❌ [生命线导出] 导出失败: {}", exportResult.getMessage());
            }

            return ResponseEntity.ok(exportResult);

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("💥 [生命线导出] 导出异常，总耗时: {} ms", totalTime);
            log.error("💥 [生命线导出] 异常详情: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    @PostMapping("/lifeList")
    public String getLifeList(@RequestBody JSONObject jsonObject, HttpServletResponse response) throws IOException {
        JSONObject lifeList = lifeCircuitService.getLifeList(jsonObject);
        lifeList.put("ds",jsonObject.getString("localNetwork"));
        lifeList.put("scene_id",jsonObject.getString("scene_id"));
        return null;
        //lifeCircuitService.lifeListDeal_1(lifeList,response);
    }

    @PostMapping("/life_risk_analyze")
    public void risk_analyze(@RequestBody JSONObject jsonObject, HttpServletResponse response){
        jsonObject.put("is_life","0");

        for(int i =1;i<100;i++){
            PageResponse<JSONObject> pageResponse =  custViewMemberDao.queryList(jsonObject ,5,i, NRMConstants.SHARDING_GRAPH_DB);
            List<JSONObject> list = pageResponse.getData();
            for(JSONObject currentLife : list){
                log.info("current_life"+ currentLife);
                if(!currentLife.getString("circuit_code").equals("NO") && (currentLife.getString("result")==null || Objects.isNull(currentLife.getString("result")))){
                    JSONObject param = new JSONObject();
                    param.put("id", currentLife.getString("id"));
                    param.put("ds", currentLife.getString("area_name"));
                    custViewMemberService.risk_analyze(param);
                }
            }
        }

    }

    /**
     * 转换生命线电路数据格式
     */
    private List<Map<String, Object>> convertLifeCircuitData(List<JSONObject> resultData) {
        log.info("🔄 [数据转换] 开始转换生命线电路数据格式，输入数据量: {}", resultData.size());
        List<Map<String, Object>> data = new ArrayList<>();

        try {
            int processedCount = 0;

            for (JSONObject item : resultData) {
                try {
                    Map<String, Object> row = new HashMap<>();
                    row.put("area_name", item.getString("area_name") != null ? item.getString("area_name") : "");
                    row.put("customer_name", item.getString("customer_name") != null ? item.getString("customer_name") : "");
                    row.put("access_code", item.getString("access_code") != null ? item.getString("access_code") : "");
                    row.put("code", item.getString("code") != null ? item.getString("code") : "");
                    row.put("opt_code", item.getString("opt_code") != null ? item.getString("opt_code") : "");
                    row.put("opt_road", item.getString("opt_road") != null ? item.getString("opt_road") : "");
                    row.put("notes", item.getString("notes") != null ? item.getString("notes") : "");
                    row.put("service_type_name", item.getString("service_type_name") != null ? item.getString("service_type_name") : "");

                    // 处理是否生命线字段
                    String isLife = item.getString("is_life");
                    String isLifeDisplay = "";
                    if ("1".equals(isLife)) {
                        isLifeDisplay = "是";
                    } else if ("0".equals(isLife)) {
                        isLifeDisplay = "否";
                    } else {
                        isLifeDisplay = isLife != null ? isLife : "";
                    }
                    row.put("is_life_display", isLifeDisplay);

                    row.put("result", item.getString("result") != null ? item.getString("result") : "");
                    row.put("deal_result", item.getString("deal_result") != null ? item.getString("deal_result") : "");
                    row.put("a_address_name", item.getString("a_address_name") != null ? item.getString("a_address_name") : "");
                    row.put("z_address_name", item.getString("z_address_name") != null ? item.getString("z_address_name") : "");
                    row.put("create_time", item.getString("create_time") != null ? item.getString("create_time") : "");

                    data.add(row);
                    processedCount++;

                    // 每处理1000条记录输出一次进度
                    if (processedCount % 1000 == 0) {
                        log.info("📊 [数据转换] 已处理 {} / {} 条记录", processedCount, resultData.size());
                    }

                } catch (Exception e) {
                    log.error("❌ [数据转换] 转换第 {} 条记录时出错: {}", processedCount + 1, e.getMessage());
                    log.error("❌ [数据转换] 出错的数据: {}", item);
                }
            }

            log.info("✅ [数据转换] 转换完成");
            log.info("📊 [数据转换] 成功转换: {} 条记录", processedCount);

        } catch (Exception e) {
            log.error("💥 [数据转换] 数据转换过程中发生异常: {}", e.getMessage(), e);
        }

        log.info("🎯 [数据转换] 最终输出数据量: {}", data.size());
        return data;
    }

}
