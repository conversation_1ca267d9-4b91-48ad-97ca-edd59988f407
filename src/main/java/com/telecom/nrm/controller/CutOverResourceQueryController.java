package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CutOverResourceQueryDao;
import com.telecom.nrm.domain.NRMConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/resource-query")
@Slf4j
public class CutOverResourceQueryController {

    public static String ENTITY_SPEC_CODE_CABLE_SEGMENT="cbl_sect";
    public static String ENTITY_SPEC_CODE_CABLE="net_code";
    public static String ENTITY_SPEC_CODE_PON="pon";
    public static String ENTITY_SPEC_CODE_OLT="olt";
    public static String ENTITY_SPEC_CODE_OBD="obd";

    @Autowired
    CutOverResourceQueryDao cutOverResourceDao;

    @PostMapping("")
    public BiyiPageResult<JSONObject> queryCutOverResource(@RequestBody Map example , BiyiPageRequest pageable) {

        JSONObject param = (JSONObject) JSON.toJSON(example);
        String entitySpecCode = param.getString("entity_spec_code");

        if (ObjectUtil.isNotEmpty(entitySpecCode) && entitySpecCode.equals(ENTITY_SPEC_CODE_CABLE_SEGMENT)) {
            return queryCableSegment(param, pageable);
        }else if (ObjectUtil.isNotEmpty(entitySpecCode) && entitySpecCode.equals(ENTITY_SPEC_CODE_CABLE)){
            return queryCable(param, pageable);
        }else if (ObjectUtil.isNotEmpty(entitySpecCode) && entitySpecCode.equals(ENTITY_SPEC_CODE_PON)){
            return queryPon(param, pageable);
        } else if (ObjectUtil.isNotEmpty(entitySpecCode) && entitySpecCode.equals(ENTITY_SPEC_CODE_OLT)){
            return queryOLT(param, pageable);
        }else if (ObjectUtil.isNotEmpty(entitySpecCode) && entitySpecCode.equals(ENTITY_SPEC_CODE_OBD)){
            return queryOBD(param, pageable);
        } else {
            return new BiyiPageResult(new ArrayList<JSONObject>(), 0 ,0);
        }
    }

    private BiyiPageResult<JSONObject> queryCable(JSONObject param, BiyiPageRequest pageable) {
        PageResponse<JSONObject> pageResponse = cutOverResourceDao.queryCable(param, pageable.getSize(), pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(param.getString("ds")));
        List<JSONObject> dataList = pageResponse.getData();
        for (JSONObject data : dataList) {
            data.put("entity_code", data.getString("code"));
            data.put("entity_name", data.getString("name"));
            data.put("entity_spec_id", data.getString("spec_id"));
            data.put("entity_spec_name", "光缆");
            data.put("entity_id", data.getString("id"));
            data.put("entity_spec_code", ENTITY_SPEC_CODE_CABLE);
            data.remove("id");
        }
        JSONObjectUtil.convertBigNumberToString(dataList);
        return new BiyiPageResult(dataList, pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    public BiyiPageResult<JSONObject>  queryCableSegment(JSONObject param,  BiyiPageRequest pageable) {
        PageResponse<JSONObject> pageResponse = cutOverResourceDao.queryCableSegment(param, pageable.getSize(), pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(param.getString("ds")));
        List<JSONObject> dataList = pageResponse.getData();
        for (JSONObject data : dataList) {
            data.put("entity_code", data.getString("code"));
            data.put("entity_name", data.getString("name"));
            data.put("entity_spec_id", data.getString("spec_id"));
            data.put("entity_spec_name", "光缆段");
            data.put("entity_id", data.getString("id"));
            data.put("entity_spec_code", ENTITY_SPEC_CODE_CABLE_SEGMENT);
            data.remove("id");
        }
        JSONObjectUtil.convertBigNumberToString(dataList);
        return new BiyiPageResult(dataList, pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    private BiyiPageResult<JSONObject> queryOLT(JSONObject param, BiyiPageRequest pageable) {
        String datasourece = "ds_odso_" + param.getString("ds");
        PageResponse<JSONObject> pageResponse = cutOverResourceDao.queryOLT(param, pageable.getSize(), pageable.getPage(), datasourece);
        List<JSONObject> dataList = pageResponse.getData();
        for (JSONObject data : dataList) {
            data.put("entity_code", data.getString("olt_code"));
            data.put("entity_name", data.getString("olt_name"));
            data.put("entity_spec_id", "1028200001");
            data.put("entity_spec_name", "olt");
            data.put("entity_id", data.getString("phy_eqp_id"));
            data.put("dev_ip", data.getString("olt_ip"));
            data.put("entity_spec_code", ENTITY_SPEC_CODE_OLT);
            data.remove("id");
        }
        JSONObjectUtil.convertBigNumberToString(dataList);
        return new BiyiPageResult(dataList, pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    private BiyiPageResult<JSONObject> queryPon(JSONObject param, BiyiPageRequest pageable) {
        String datasourece = "ds_odso_" + param.getString("ds");
        PageResponse<JSONObject> pageResponse = cutOverResourceDao.queryPon(param, pageable.getSize(), pageable.getPage(), datasourece);
        List<JSONObject> dataList = pageResponse.getData();
        for (JSONObject data : dataList) {
            data.put("entity_code", data.getString("port_code"));
            data.put("entity_name", data.getString("port_name"));
            data.put("entity_spec_id", data.getString("port_spec_id"));
            data.put("dev_ip", data.getString("olt_ip"));
            data.put("entity_spec_name", "pon");
            data.put("entity_id", data.getString("port_id"));
            data.put("entity_spec_code", ENTITY_SPEC_CODE_PON);
            data.remove("id");
        }
        JSONObjectUtil.convertBigNumberToString(dataList);
        return new BiyiPageResult(dataList, pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    private BiyiPageResult<JSONObject> queryOBD(JSONObject param, BiyiPageRequest pageable) {
        String datasourece = "ds_odso_" + param.getString("ds");
        PageResponse<JSONObject> pageResponse = cutOverResourceDao.queryOBD(param, pageable.getSize(), pageable.getPage(), datasourece);
        List<JSONObject> dataList = pageResponse.getData();
        for (JSONObject data : dataList) {
            data.put("entity_code", data.getString("code"));
            data.put("entity_name", data.getString("name"));
            data.put("entity_spec_id", data.getString("spec_id"));
            data.put("entity_spec_name", "obd");
            data.put("entity_id", data.getString("id"));
            data.put("entity_spec_code", ENTITY_SPEC_CODE_OBD);
            data.remove("id");
        }
        JSONObjectUtil.convertBigNumberToString(dataList);
        return new BiyiPageResult(dataList, pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    @PostMapping("/upload-pon-check/{ds}")
    public List<JSONObject> uploadPon(@PathVariable String ds, @RequestParam("file") MultipartFile file) throws IOException {
        List<JSONObject> result = new ArrayList<>();
        if (ObjectUtil.isEmpty(ds)){
            return result;
        }
        String datasource = "ds_odso_"+ ds;
        Sheet sheet = null;
        Row row = null;
        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            log.info("wb:" + rownum + "," + column);
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data = new JSONObject();
                    for (int j = 0; j < column; j++) {
                        if(j==0 && !Objects.isNull(sheet.getRow(i).getCell(j))) {
                            String dev_ip = sheet.getRow(i).getCell(j).getStringCellValue().trim();
                            current_data.put("dev_ip", dev_ip);
                            current_data.put("olt_ip", dev_ip);
                        }
                        if(j==1 && !Objects.isNull(sheet.getRow(i).getCell(j))) {
                            String entity_code = sheet.getRow(i).getCell(j).getStringCellValue().trim();
                            current_data.put("entity_code", entity_code);
                            current_data.put("entity_name", entity_code);
                            current_data.put("pattern", entity_code);
                        }
                        current_data.put("entity_spec_id", 1041100004);
                        current_data.put("entity_spec_name", "pon");
                        current_data.put("entity_spec_code", ENTITY_SPEC_CODE_PON);
                    }
                    PageResponse<JSONObject> jsonObjectPageResponse = cutOverResourceDao.queryPon(current_data, 10, 1, datasource);
                    if (ObjectUtil.isNotEmpty(jsonObjectPageResponse.getData())) {
                        current_data.put("entity_id", jsonObjectPageResponse.getData().get(0).getString("port_id"));
                        result.add(current_data);
                    }
                }
            }
        }
        return result;
    }

    @PostMapping("/upload-cable-segment/{ds}")
    public List<JSONObject> uploadCable(@PathVariable String ds, @RequestParam("file") MultipartFile file) throws IOException {
        List<JSONObject> result = new ArrayList<>();
        if (ObjectUtil.isEmpty(ds)){
            return result;
        }
        String datasource = "ds_bc_o3_"+ ds;
        Sheet sheet = null;
        Row row = null;
        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            log.info("wb:" + rownum + "," + column);
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data = new JSONObject();
                    for (int j = 0; j < column; j++) {
                        if(j==0 && !Objects.isNull(sheet.getRow(i).getCell(j))) {
                            String entity_code = sheet.getRow(i).getCell(j).getStringCellValue().trim();
                            current_data.put("entity_code", entity_code);
                            current_data.put("entity_name", entity_code);
                            current_data.put("pattern", entity_code);
                        }
                        current_data.put("entity_spec_id", 1121000002);
                        current_data.put("entity_spec_name", "光缆段");
                        current_data.put("entity_spec_code", ENTITY_SPEC_CODE_CABLE_SEGMENT);
                    }
                    PageResponse<JSONObject> jsonObjectPageResponse = cutOverResourceDao.queryCableSegment(current_data, 10, 1, datasource);
                    if (ObjectUtil.isNotEmpty(jsonObjectPageResponse.getData())) {
                        JSONObject d  = jsonObjectPageResponse.getData().get(0);
                        current_data.put("entity_id", d.getString("id"));
                        current_data.put("entity_name", d.getString("name"));
                        result.add(current_data);
                    }
                }
            }
        }
        return result;
    }

    @PostMapping("/upload-obd-segment/{ds}")
    public List<JSONObject> uploadOBD(@PathVariable String ds, @RequestParam("file") MultipartFile file) throws IOException {
        List<JSONObject> result = new ArrayList<>();
        if (ObjectUtil.isEmpty(ds)){
            return result;
        }
        String datasource = "ds_odso_"+ ds;
        Sheet sheet = null;
        Row row = null;
        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            log.info("wb:" + rownum + "," + column);
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data = new JSONObject();
                    for (int j = 0; j < column; j++) {
                        if(j==0 && !Objects.isNull(sheet.getRow(i).getCell(j))) {
                            String entity_code = sheet.getRow(i).getCell(j).getStringCellValue().trim();
                            current_data.put("entity_code", entity_code);
                            current_data.put("entity_name", entity_code);
                            current_data.put("pattern", entity_code);
                        }
                        current_data.put("entity_spec_id", 1020200006);
                        current_data.put("entity_spec_name", "obd");
                        current_data.put("entity_spec_code", ENTITY_SPEC_CODE_OBD);
                    }
                    PageResponse<JSONObject> jsonObjectPageResponse = cutOverResourceDao.queryOBD(current_data, 10, 1, datasource);
                    if (ObjectUtil.isNotEmpty(jsonObjectPageResponse.getData())) {
                        JSONObject d  = jsonObjectPageResponse.getData().get(0);
                        current_data.put("entity_id", d.getString("id"));
                        current_data.put("entity_name", d.getString("name"));
                        result.add(current_data);
                    }
                }
            }
        }
        return result;
    }

}
