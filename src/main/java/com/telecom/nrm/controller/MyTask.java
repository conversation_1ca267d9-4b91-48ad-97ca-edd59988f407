package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.nrm.domain.NRMConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class MyTask {

    // @Autowired
    OptGroupController optGroupController;

    // 使用Cron表达式（每天12点执行）
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task1() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","南京");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task2() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","苏州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task3() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","常州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task4() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","宿迁");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task5() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","南通");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task6() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","扬州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task7() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","连云港");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task8() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","淮安");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task9() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","无锡");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task10() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","镇江");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task11() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","泰州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task12() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","徐州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task13() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","盐城");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(1);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task14() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","南京");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task15() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","苏州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task16() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","常州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task17() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","宿迁");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task18() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","南通");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task19() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","扬州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task20() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","连云港");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task21() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","淮安");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task22() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","无锡");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task23() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","镇江");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task24() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","泰州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
    // @Scheduled(cron = "0 0 0 * * ?")
    public void task25() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","徐州");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void task26() throws IOException {
        JSONObject param = new JSONObject();
        param.put("check_time", "1");
        param.put("ds","盐城");
        BiyiPageRequest pageable = new BiyiPageRequest();
        pageable.setPage(2);
        pageable.setSize(5000);
        optGroupController.optCheck(param, pageable);
    }
}

