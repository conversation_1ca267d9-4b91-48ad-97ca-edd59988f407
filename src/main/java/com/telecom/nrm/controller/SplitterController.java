package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.SplitterDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.SplitterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/splitter")
@Slf4j
public class SplitterPortController {
    
    @Autowired
    SplitterDao splitterDao;  // 使用新的DAO接口

    @Autowired
    SplitterService splitterService;  // 新的Service

    @Autowired
    RegionDao regionDao;

    /**
     * 前端调用: GET /api/splitter/devices
     * 获取分光器设备列表（对应前端的deviceList）
     */
    @GetMapping("/devices")
    @LogAnnotation(interfaceName = "分光器端口预警-设备列表查询")
    public BiyiPageResult<JSONObject> getDeviceList(
            @RequestParam(required = false) String deviceCode,      // 设备编码
            @RequestParam(required = false) String region,          // 区域
            @RequestParam(required = false) String deviceStatus,    // 设备状态: normal/attention/alarm
            @RequestParam(required = false) String occupancyRate,   // 实占率范围: 0-50/50-80/80-95/95-100
            @RequestParam(required = false) String freePortCount,   // 空闲端口数: 0/1/2-10/10+
            @RequestParam(required = false) String installAfterTime, // 入网时间（年月）
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String ds  // 数据源标识，默认南京
    ) {
        log.info("getDeviceList - deviceCode:{}, region:{}, page:{}, size:{}", deviceCode, region, page, size);
        
        try {
            // 1. 构建查询参数（调用现有API）
            JSONObject queryParam = new JSONObject();
            // 注意：现有API可能不支持查询条件，所以先查询全部数据
            
            // 2. 调用现有的obd_port_alert API
            String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(
                ObjectUtil.isEmpty(ds) ? "nj" : ds  // 使用数据库映射的key
            );

            // 调用DAO获取全量数据（API返回数组格式）
            List<JSONObject> allData = splitterDao.querySplitterDevices(queryParam, shardingCode);

            if (allData == null) {
                allData = new ArrayList<>();
            } else {
                JSONObjectUtil.convertBigNumberToString(allData);
            }
            
            // 3. 数据转换和计算（API字段 -> 前端字段）
            List<JSONObject> deviceList = splitterService.convertToDeviceDataList(allData);
            
            // 4. 内存筛选（因为API不支持查询条件）
            List<JSONObject> filteredList = filterDeviceList(
                deviceList, deviceCode, region, deviceStatus, 
                occupancyRate, freePortCount, installAfterTime
            );
            
            // 5. 手动分页
            int total = filteredList.size();
            int start = (page - 1) * size;
            int end = Math.min(start + size, total);
            
            List<JSONObject> pagedList = start < total ? 
                filteredList.subList(start, end) : new ArrayList<>();
            
            return new BiyiPageResult<>(pagedList, total, size);
            
        } catch (Exception e) {
            log.error("getDeviceList error", e);
            return new BiyiPageResult<>(new ArrayList<>(), 0, size);
        }
    }

    /**
     * 前端调用: GET /api/splitter/device/{deviceCode}/splitters  
     * 获取设备下的分光器详情（暂时返回空，后续扩展）
     */
    @GetMapping("/device/{deviceCode}/splitters")
    public ResponseEntity<List<JSONObject>> getDeviceSplitters(
            @PathVariable String deviceCode,
            @RequestParam(required = false) String ds
    ) {
        log.info("getDeviceSplitters - deviceCode:{}", deviceCode);
        
        // 由于目前只有obd_port_alert API，无法获取单个设备的分光器详情
        // 后续扩展时，可以调用splitterService.getDeviceSplitters(deviceCode)
        List<JSONObject> splitters = new ArrayList<>();
        
        // TODO: 后续实现
        // splitters = splitterService.getDeviceSplitters(deviceCode, ds);
        
        return ResponseEntity.ok(splitters);
    }

    /**
     * 前端调用: GET /api/splitter/device/{deviceCode}/prediction-detail
     * 获取设备预测详情（暂时返回空数据结构）
     */
    @GetMapping("/device/{deviceCode}/prediction-detail")  
    public ResponseEntity<JSONObject> getDevicePredictionDetail(@PathVariable String deviceCode) {
        log.info("getDevicePredictionDetail - deviceCode:{}", deviceCode);
        
        // 返回空的预测数据结构
        JSONObject result = new JSONObject();
        result.put("hasData", false);
        result.put("设备编码", deviceCode);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 前端调用: POST /api/splitter/predict/batch
     * 批量预测（暂时返回空）
     */
    @PostMapping("/predict/batch")
    @LogAnnotation(interfaceName = "分光器端口预警-批量预测")
    public ResponseEntity<List<JSONObject>> batchPredict(@RequestBody JSONObject request) {
        log.info("batchPredict - request:{}", request);

        // 返回空的预测结果
        List<JSONObject> predictions = new ArrayList<>();

        return ResponseEntity.ok(predictions);
    }

    /**
     * 兼容前端路径: POST /api/splitter_predict/batch
     * 重定向到正确的批量预测接口
     */
    @PostMapping("_predict/batch")
    public ResponseEntity<List<JSONObject>> batchPredictCompat(@RequestBody JSONObject request) {
        return batchPredict(request);
    }

    /**
     * 前端调用: GET /api/splitter/trend
     * 获取趋势数据（暂时返回空）
     */
    @GetMapping("/trend")
    public ResponseEntity<List<JSONObject>> getTrend(
            @RequestParam String deviceCode,
            @RequestParam String timeRange
    ) {
        log.info("getTrend - deviceCode:{}, timeRange:{}", deviceCode, timeRange);
        
        // 返回空的趋势数据
        List<JSONObject> trendData = new ArrayList<>();
        
        return ResponseEntity.ok(trendData);
    }

    /**
     * 数据转换：API返回字段 -> 前端期望字段
     */
    private JSONObject convertToDeviceData(JSONObject apiData) {
        JSONObject deviceData = new JSONObject();
        
        // 直接映射字段
        deviceData.put("设备编码", apiData.getString("code"));
        deviceData.put("区域", apiData.getString("leaf_region"));  
        deviceData.put("小区入库时间", apiData.getString("create_date"));
        deviceData.put("覆盖的工程级的线路到达房间数", apiData.getInteger("rooms"));
        deviceData.put("分光器数", apiData.getInteger("obd"));
        deviceData.put("分光器容量", apiData.getInteger("all_capacity"));
        deviceData.put("分光器空闲数", apiData.getInteger("free_capacity"));
        deviceData.put("ftth终端数", apiData.getInteger("ftth"));
        
        // 处理实占率（去掉%号，转为数字）
        String utilizationRateStr = apiData.getString("utilization_rate");
        Integer occupancyRate = parseOccupancyRate(utilizationRateStr);
        deviceData.put("实占率", occupancyRate);
        
        // 计算健康状态（实占率>95%告警，70-95%注意，<70%正常）
        String healthStatus = calculateHealthStatus(occupancyRate);
        deviceData.put("健康状态", healthStatus);
        
        // 预测字段暂时为空
        deviceData.put("预测实占率", null);
        deviceData.put("预测空闲数", null); 
        deviceData.put("预测状态", null);
        
        return deviceData;
    }

    /**
     * 解析实占率字符串为数字
     */
    private Integer parseOccupancyRate(String rateStr) {
        if (ObjectUtil.isEmpty(rateStr)) return 0;
        
        try {
            // 去掉%号并转换为整数
            String numericStr = rateStr.replace("%", "").trim();
            return (int) Math.round(Double.parseDouble(numericStr));
        } catch (Exception e) {
            log.warn("解析实占率失败: {}", rateStr);
            return 0;
        }
    }

    /**
     * 计算健康状态
     */
    private String calculateHealthStatus(Integer occupancyRate) {
        if (occupancyRate == null) return "normal";
        
        if (occupancyRate > 95) {
            return "alarm";      // 告警
        } else if (occupancyRate >= 70) {
            return "attention";  // 注意  
        } else {
            return "normal";     // 正常
        }
    }

    /**
     * 内存筛选设备列表
     */
    private List<JSONObject> filterDeviceList(
            List<JSONObject> deviceList,
            String deviceCode, String region, String deviceStatus,
            String occupancyRate, String freePortCount, String installAfterTime
    ) {
        
        return deviceList.stream()
            .filter(device -> {
                // 1. 设备编码筛选
                if (ObjectUtil.isNotEmpty(deviceCode)) {
                    String code = device.getString("设备编码");
                    if (ObjectUtil.isEmpty(code) || !code.contains(deviceCode)) {
                        return false;
                    }
                }
                
                // 2. 区域筛选
                if (ObjectUtil.isNotEmpty(region)) {
                    String deviceRegion = device.getString("区域");
                    if (!region.equals(deviceRegion)) {
                        return false;
                    }
                }
                
                // 3. 设备状态筛选
                if (ObjectUtil.isNotEmpty(deviceStatus)) {
                    String status = device.getString("健康状态");
                    if (!deviceStatus.equals(status)) {
                        return false;
                    }
                }
                
                // 4. 实占率范围筛选
                if (ObjectUtil.isNotEmpty(occupancyRate)) {
                    Integer rate = device.getInteger("实占率");
                    if (rate == null || !isInOccupancyRange(rate, occupancyRate)) {
                        return false;
                    }
                }
                
                // 5. 空闲端口数筛选
                if (ObjectUtil.isNotEmpty(freePortCount)) {
                    Integer freeCount = device.getInteger("分光器空闲数");
                    if (freeCount == null || !isInFreePortRange(freeCount, freePortCount)) {
                        return false;
                    }
                }
                
                // 6. 入网时间筛选（这里简化处理）
                if (ObjectUtil.isNotEmpty(installAfterTime)) {
                    // TODO: 根据需要实现时间筛选逻辑
                }
                
                return true;
            })
            .collect(Collectors.toList());
    }

    /**
     * 判断实占率是否在指定范围内
     */
    private boolean isInOccupancyRange(Integer rate, String range) {
        switch (range) {
            case "0-50": return rate >= 0 && rate <= 50;
            case "50-80": return rate > 50 && rate <= 80;
            case "80-95": return rate > 80 && rate <= 95;
            case "95-100": return rate > 95 && rate <= 100;
            default: return true;
        }
    }

    /**
     * 判断空闲端口数是否在指定范围内  
     */
    private boolean isInFreePortRange(Integer count, String range) {
        switch (range) {
            case "0": return count == 0;
            case "1": return count == 1;
            case "2-10": return count >= 2 && count <= 10;
            case "10+": return count > 10;
            default: return true;
        }
    }
}