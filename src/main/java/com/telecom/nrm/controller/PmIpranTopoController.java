package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.DaClient;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.PmIpranTopoDao;
import com.telecom.nrm.dao.PmIpranUserDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.PmIpranTopo;
import com.telecom.nrm.domain.PmIpranUser;
import com.telecom.nrm.service.ExternalInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/pmIpranTopo")
public class PmIpranTopoController {

    @Autowired
    DaClient daClient;
    @Autowired
    PmIpranUserDao pmIpranUserDao;
    @Autowired
    ExternalInterfaceService externalInterfaceService;
    @Autowired
    private PmIpranTopoDao pmIpranTopoDao;


    /**
     * 查询IPRAN资料
     *
     * @param example
     * @param pageable
     * @return
     */
    @GetMapping("/getPmIpranTopoInfo")
    public BiyiPageResult<List<PmIpranTopo>> getPmIpranTopoInfo(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = pmIpranTopoDao.getPmIpranTopoInfo(jsonObject, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_CODE);
        List<JSONObject> jsonDataList = pageResponse.getData();
        JSONObjectUtil.convertBigNumberToString(jsonDataList);
        List<PmIpranTopo> result = new LinkedList<>();
        for (JSONObject data : jsonDataList) {
            PmIpranTopo pmIpranTopo = JSON.toJavaObject(data, PmIpranTopo.class);
            result.add(pmIpranTopo);
        }
        return new BiyiPageResult(result, pageResponse.getPageInfo().getTotalCount(), pageResponse.getPageInfo().getTotalCount());
    }

    @PostMapping("/createPmIpranTopo")
    public String createPmIpranTopo(@RequestBody PmIpranTopo entity) {
        try {
            pmIpranTopoDao.savePmIpranTopo(entity, NRMConstants.SHARDING_CODE);
            PmIpranUser pmIpranUser = externalInterfaceService.queryRes("", entity.getServcode());
            if (null != pmIpranUser) pmIpranUserDao.savePmIpranUser(pmIpranUser, NRMConstants.SHARDING_CODE);
            return "ok";
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "error";
        }
    }

}
