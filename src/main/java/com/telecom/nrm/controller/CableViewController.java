package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CableViewDao;
import com.telecom.nrm.dao.GroupFaultRapidPositioningDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/cableview")
@Slf4j
public class CableViewController {

    @Autowired
    CableViewDao cableViewDao;


    @PostMapping("roominfo")
    public ResponseEntity<JSONObject> pageQueryRoominfo(@RequestBody JSONObject request) {
        JSONObject param = new JSONObject();
        JSONObject result = new JSONObject();

        JSONObject input = request.getJSONObject("input");
        //获取字段信息，按顺序第一个取到以后就结束
        String[] strArray = { "station_no","station_name","room_no", "room_name" };
        for (String str : strArray) {
            if(ObjectUtil.isNotEmpty(input.getString(str)))
            {

                param.put(str, input.getString(str));
                break;
            }
        }

        int pageSize;
        int currentPage;
        //int pageSize = request.getInteger("pageSize");
        pageSize = 1000;
        //int currentPage = request.getInteger("currentPage");
        currentPage = 1;
        String sharding_code = "ds_odso_"+ request.getString("city");


        PageResponse<JSONObject> response = cableViewDao.cableViewGetRoom(param, pageSize,currentPage, sharding_code);
        List<JSONObject> data= response.getData();

        result.put("roominfo",data);


        return ResponseEntity.ok(result);

    }

    @PostMapping("stationinfo")
    public ResponseEntity<JSONObject> pageQueryStationinfo(@RequestBody JSONObject request) {
        JSONObject param = new JSONObject();
        JSONObject result = new JSONObject();

        JSONObject input = request.getJSONObject("input");
        //获取字段信息，按顺序第一个取到以后就结束
        String[] strArray = { "station_no","station_name"};
        for (String str : strArray) {
            if(ObjectUtil.isNotEmpty(input.getString(str)))
            {

                param.put(str, input.getString(str));
                break;
            }
        }

        int pageSize;
        int currentPage;
        //int pageSize = request.getInteger("pageSize");
        pageSize = 1000;
        //int currentPage = request.getInteger("currentPage");
        currentPage = 1;
        String sharding_code = "ds_odso_"+ request.getString("city");

        PageResponse<JSONObject> response = cableViewDao.cableViewGetStation(param, pageSize,currentPage, sharding_code);
        List<JSONObject> data= response.getData();

        result.put("stationinfo",data);


        return ResponseEntity.ok(result);

    }



    @PostMapping("stationcable")
    public ResponseEntity<JSONObject> pageQueryStationCableinfo(@RequestBody JSONObject request) {
        JSONObject param = new JSONObject();
        JSONObject result = new JSONObject();

        JSONObject input = request.getJSONObject("input");
        //获取字段信息，按顺序第一个取到以后就结束
        String[] strArray = { "station_id","station_no"};
        for (String str : strArray) {
            if(ObjectUtil.isNotEmpty(input.getString(str)))
            {

                param.put(str, input.getString(str));
                break;
            }
        }

        int pageSize;
        int currentPage;
        //int pageSize = request.getInteger("pageSize");
        pageSize = 1000;
        //int currentPage = request.getInteger("currentPage");
        currentPage = 1;
        String sharding_code = "ds_odso_"+ request.getString("city");


        PageResponse<JSONObject> response = cableViewDao.cableViewGetStationCables(param, pageSize,currentPage, sharding_code);
        List<JSONObject> data= response.getData();

        result.put("stationcableinfo",data);


        return ResponseEntity.ok(result);

    }





}
