package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.OptGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/white-list")
@Slf4j
public class WhiteListController {

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    RegionDao regionDao;


    @GetMapping("")
    public BiyiPageResult<JSONObject> pageQuery(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        PageResponse<JSONObject> pageResponse = custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_query(jsonObject, pageable.getSize(), pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if (pageResponse != null && pageResponse.getData() != null && pageResponse.getData().size() != 0) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data, pageResponse.getPageInfo().getPageSize(), totalCount);
    }

    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> get_dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> regionList = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> cityList = new ArrayList<>();
        for (int i = 0; i < regionList.size(); i++) {
            if (regionList.get(i).getAreaLevelId().equals(Long.parseLong("100700"))) {
                cityList.add(regionList.get(i));
            }
        }
        meta.put("cityList", cityList);

        String currentCityName = exampleJSON.getString("ds");
        if (ObjectUtil.isNotEmpty(currentCityName)) {
            Region currentCity = cityList.stream().filter(c -> c.getName().equals(currentCityName)).findFirst().orElse(new Region());
            String currentCityId = currentCity.getId();
            List<Region> leafRegionList = regionList.stream().filter(r -> ObjectUtil.isNotEmpty(r.getParentId()) && r.getParentId().equals(currentCityId)).collect(Collectors.toList());
            meta.put("leafRegionList", leafRegionList);
        } else {
            meta.put("leafRegionList", new ArrayList<Region>());
        }
        return ResponseEntity.ok().body(meta);
    }

    @PostMapping("/white-insert")
    public Integer pair_circuit_input_fiber_result_corr_insert(@RequestBody JSONObject jsonObject){
        String type = jsonObject.getString("type");
        jsonObject.put("pipleline_code", jsonObject.getJSONObject("pipeSegment").getString("code"));
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_insert(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/white-delete")
    public Integer pipleline_white_delete(@RequestBody JSONObject jsonObject){
        String code = jsonObject.getJSONObject("pipeSegment").getString("code");
        if(code.contains("----已忽略")){
            code = code.substring(0, code.indexOf("----已忽略"));
        }
        jsonObject.put("pipleline_code", code.trim());
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_delete(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }
}
