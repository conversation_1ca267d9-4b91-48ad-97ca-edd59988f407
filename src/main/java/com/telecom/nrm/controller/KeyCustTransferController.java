package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONArray;
import com.telecom.nrm.entity.*;
import com.telecom.nrm.service.KeyCustTransferService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 集团重要客户传输数据
 */
@RestController
@RequestMapping("/api/keyCustTransfer")
@Slf4j
public class KeyCustTransferController {
    @Autowired
    private KeyCustTransferService keyCustTransferService;

    /**
     * 查询端口
     * @param portQueryDTO
     * @return
     */
    @PostMapping("/LocalPortInterface")
    public PageResult<List<CmPort>> queryCmPortList(@RequestBody PortQueryDTO portQueryDTO){
        PageResult portPageResult = null;
        try {
            portPageResult = this.keyCustTransferService.queryCmPortList(portQueryDTO);
        }catch (Exception e) {
            log.error("queryCmPortList error:{}",e);
            portPageResult = PageResult.fail("查询端口失败！请联系管理员");
        }
        return portPageResult;
    }

    /**
     * 查询设备
     * @param entityQueryDTO
     * @return
     */
    @PostMapping("/LocalDeviceInterface")
    public PageResult<List<CmDevice>> queryCmDeviceList(@RequestBody EntityQueryDTO entityQueryDTO){
        PageResult devicePageResult = null;
        try {
            devicePageResult = this.keyCustTransferService.queryCmDeviceList(entityQueryDTO);
        }catch (Exception e) {
            log.error("queryCmDeviceList error:{}",e);
            devicePageResult = PageResult.fail("查询设备失败！请联系管理员");
        }
        return devicePageResult;
    }

    /**
     * 查询光复用段波道
     * @param waveQueryDTO
     * @return
     */
    @PostMapping("/MultiSegWaveInterface")
    public Result<List<CmDevice>> queryWavelengthChannel(@RequestBody @Validated WaveQueryDTO waveQueryDTO){
        Result wareResult = null;
        try {
            wareResult = this.keyCustTransferService.queryWavelengthChannel(waveQueryDTO);
        }catch (Exception e) {
            log.error("queryWavelengthChannel error:{}",e);
            wareResult = Result.fail("查询光复用段波道失败！请联系管理员");
        }
        return wareResult;
    }

    /**
     * 调用省内光路路由查询接口
     * @param param
     * @return
     */
    @PostMapping("/OptRoadInterface")
    public Result<List<OptRoadInfo>> queryOpticalRoute(@RequestBody Map<String,String> param){
        Result optRoadResult = null;
        try {
            optRoadResult = this.keyCustTransferService.queryOpticalRoute(param);
        }catch (Exception e) {
            log.error("queryOpticalRoute error:{}",e);
            optRoadResult = Result.fail("查询光路路由失败！请联系管理员");
        }
        return optRoadResult;
    }

    /**
     * 光复用段查询接口
     * @param param
     * @return
     */
    @PostMapping("/MultiSegInterface")
    public Result<List<OptMultiSegInfo>> queryOptMultiSection(@RequestBody Map<String,String> param){
        Result optMultiSectionResult = null;
        try {
            optMultiSectionResult = this.keyCustTransferService.queryOptMultiSection(param);
        }catch (Exception e) {
            log.error("queryOptMultiSection error:{}",e);
            optMultiSectionResult = Result.fail("查询光复用段失败！请联系管理员");
        }
        return optMultiSectionResult;
    }

    /**
     * 光复用段路由查询接口
     * @param param
     * @return
     */
    @PostMapping("/MultiSegRouteInterface")
    public Result<List<OptMultiSegRouteInfo>> queryOptRoadByMultiSegId(@RequestBody Map<String,String> param){
        Result optMultiSectionRouteResult = null;
        try {
            optMultiSectionRouteResult = this.keyCustTransferService.queryOptRoadByMultiSegId(param);
        }catch (Exception e) {
            log.error("queryOptRoadByMultiSegId error:{}",e);
            optMultiSectionRouteResult = Result.fail("查询光复用段路由失败！请联系管理员");
        }
        return optMultiSectionRouteResult;
    }

    /**
     * 中继查询接口
     * @param param
     * @return
     */
    @PostMapping("/RelayInfoInterface")
    public Result<List<RelayInfo>> queryRelayInfo(@RequestBody Map<String,String> param){
        Result relayResult = null;
        try {
            relayResult = this.keyCustTransferService.queryRelayInfo(param);
        }catch (Exception e) {
            log.error("queryRelayInfo error:{}",e);
            relayResult = Result.fail("查询中继失败！请联系管理员");
        }
        return relayResult;
    }

    /**
     * 中继路由查询接口
     * @param relayRouteQueryDTO
     * @return
     */
    @PostMapping("/RelayRouteInterface")
    public Result<List<RelayRouteInfo>> queryRelayRoute(@RequestBody RelayRouteQueryDTO relayRouteQueryDTO){
        Result relayRouteResult = null;
        try {
            relayRouteResult = this.keyCustTransferService.queryRelayRoute(relayRouteQueryDTO);
        }catch (Exception e) {
            log.error("queryOptRoadByMultiSegId error:{}",e);
            relayRouteResult = Result.fail("查询中继路由失败！请联系管理员");
        }
        return relayRouteResult;
    }

    /**
     * 省内同步资源占用信息接口
     * @param syncOccupyResInfoDTO
     * @return
     */
    @PostMapping("/SyncOccupyResInfoInterface")
    public Result<String> occupyResInfo(@RequestBody SyncOccupyResInfoDTO syncOccupyResInfoDTO){
        Result occupyResInfoResult = null;
        try {
            occupyResInfoResult = this.keyCustTransferService.occupyResInfo(syncOccupyResInfoDTO);
        }catch (Exception e) {
            log.error("occupyResInfo error:{}",e);
            occupyResInfoResult = Result.fail("同步资源占用信息失败！请联系管理员");
        }
        return occupyResInfoResult;
    }


    /**
     * 调用省内电路/通道/中继的本地段占用资源路由查询接口
     * @param param
     * @return
     */
    @PostMapping("/BcLocalSegOccupyInfoInterface")
    public Result<List<BcInfoDTO>> queryBcInfo(@RequestBody Map<String, List<BcInfoQueryDTO>> param){
        Result bcInfoResult = null;
        try {
            List<BcInfoQueryDTO> bcInfoQueryDTOs = (List<BcInfoQueryDTO>) MapUtils.getObject(param, "queryBcInfo");
            if (CollectionUtils.isEmpty(bcInfoQueryDTOs)) {
                return Result.fail("参数为空！");
            }
            bcInfoResult = this.keyCustTransferService.queryBcInfoList(bcInfoQueryDTOs);
        }catch (Exception e) {
            log.error("queryOptRoadByMultiSegId error:{}",e);
            bcInfoResult = Result.fail("查询省内电路/通道/中继的本地段占用资源路由失败！请联系管理员");
        }
        return bcInfoResult;
    }

}
