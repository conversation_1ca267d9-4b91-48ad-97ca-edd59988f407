package com.telecom.nrm.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustViewDao;
import com.telecom.nrm.dao.CustomerDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.CustomerHealthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/customer-health")
@Slf4j
public class CustomerHealthController {

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    RegionDao regionDao;

    @Autowired
    CustomerHealthService customerHealthService;

    /**
     * 客户健康档案 记录列表
     * @param example
     * @param pageable
     * @return
     */
    @GetMapping("")
    public BiyiPageResult<JSONObject> getCustViewList(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        PageResponse<JSONObject> pageResponse =  customerDao.queryCustomerHealthGroupRecord(jsonObject ,pageable.getSize(),pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null &&pageResponse.getData()!=null && !pageResponse.getData().isEmpty()){
            List<JSONObject> dlist = pageResponse.getData();
            JSONObjectUtil.convertBigNumberToString(dlist);
            data.addAll(dlist);
            //处理子数据
            for (JSONObject jsonObject1 : dlist) {
                JSONObject query = new JSONObject();
                query.put("name",jsonObject1.getString("p_name"));
                PageResponse<JSONObject> pageResponse2 = customerDao.queryCustomerHealthRecord(query ,200,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
                jsonObject1.put("list",pageResponse2.getData());
            }
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();


        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){

            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100699"))||Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100699"))){
                    Region2List.get(i).setName("江苏省");
                }
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);
        return ResponseEntity.ok().body(meta);
    }

    @RequestMapping("/save")
    public JSONObject save(@RequestBody JSONObject jsonObject) {
        if (ObjectUtil.isEmpty(jsonObject.getString("id"))) {
            jsonObject.put("create_time", DateTime.now());
            jsonObject.put("modify_time", DateTime.now());
        }else{
            jsonObject.put("modify_time",DateTime.now());
        }
        Integer result_1 =  customerDao.saveCustviewsave(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        JSONObject result = new JSONObject();
        if(result_1 ==1){
            result.put("result","sueccess");
        }else{
            result.put("result", "error");
        }
        return result;
    }

    @DeleteMapping("/{id}")
    public JSONObject deletebyid(@PathVariable String id) {
        Integer result_1 =  customerDao.deleteCustview(id ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(""));
        JSONObject result = new JSONObject();
        if(result_1 ==1){
            result.put("result","sueccess");
        }else{
            result.put("result", "error");
        }
        return result;
    }

    @RequestMapping("/detail")
    public ResponseEntity<JSONObject> get(@RequestBody JSONObject jsonObject) {
        log.debug("REST request to get entity : {}", jsonObject);
        JSONObject custView = customerDao.getById(jsonObject,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        jsonObject.put("name",custView.getString("p_name"));
        custView = customerDao.getById2(jsonObject,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        return ResponseEntity.ok().body(custView);
    }
    /**
     * 查看客户光缆列表
     */
    @RequestMapping("/cable")
    public List<JSONObject> queryCustomerCable(@RequestBody JSONObject jsonObject) {
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse =  customerDao.queryCustomerCable(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);

        }
        return data;
    }

    /**
     * 查看客户设备资源
     */
    @RequestMapping("/device")
    public List<JSONObject> queryCustomerDevice(@RequestBody JSONObject jsonObject) {
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse =  customerDao.queryCustomerDevice(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);
        }
        return data;
    }

    /**
     * 查看客户设备资源
     */
    @RequestMapping("/device2")
    public List<JSONObject> queryCustomerDevice2(@RequestBody JSONObject jsonObject) {
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse =  customerDao.queryCustomerDevice2(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);
        }
        return data;
    }

    /**
     * 查看客户安全信息
     */
    @RequestMapping("/security")
    public List<JSONObject> getCustomerSecurity(@RequestBody JSONObject jsonObject) {
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse =  customerDao.getCustomerSecurity(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);
        }
        return data;
    }

    /**
     * 查看资源光缆
     */
    @RequestMapping("/resource/cable")
    public List<JSONObject> getResourceCables(@RequestBody JSONObject jsonObject) {
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse =  customerDao.getResourceCables(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);
        }
        return data;
    }

    /**
     * 查看资源设备
     */
    @RequestMapping("/resource/device")
    public List<JSONObject> getResourceDevices(@RequestBody JSONObject jsonObject) {
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse =  customerDao.getResourceDevices(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);
        }
        return data;
    }

    /**
     * 查看安全设备总量
     */
    @RequestMapping("/security/device/count")
    public JSONObject getSecurityDeviceCount(@RequestBody JSONObject jsonObject) {
        JSONObject data = new JSONObject();
        Integer pageResponse =  customerDao.getSecurityDeviceCount(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        data.put("count",pageResponse);
        return data;
    }

    /**
     * 查看符合双路由的设备数量
     */
    @RequestMapping("/security/device/double_route_count")
    public JSONObject getSecurityDeviceDoubleRouteCount(@RequestBody JSONObject jsonObject) {
        JSONObject data = new JSONObject();
        Integer pageResponse =  customerDao.getSecurityDeviceDoubleRoute(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        data.put("count",pageResponse);
        return data;
    }

    /**
     * 计算光缆安全度数据
     */
    @RequestMapping("/cable/security")
    public JSONObject getCustomerCablesList(@RequestBody JSONObject jsonObject) {
        log.info("getCustomerCablesList : {}", jsonObject);
        return customerHealthService.computeFiberOptSecurity(jsonObject);
    }

    /**
     * 计算资源能力
     */
    @RequestMapping("/resource/capacity")
    public JSONObject getResourceCapacity(@RequestBody JSONObject jsonObject) {
        log.info("getResourceCapacity : {}", jsonObject);
        return customerHealthService.computeDeviceResourceAbility(jsonObject);
    }
    /**
     * 计算业务能力
     */
    @RequestMapping("/business/capacity")
    public JSONObject getBusinessCapacity(@RequestBody JSONObject jsonObject) {
        log.info("getBusinessCapacity : {}", jsonObject);
        return customerHealthService.computeBusinessResourceAbility(jsonObject);
    }

    /**
     * 客户列表搜索
     */
    @RequestMapping("/search")
    public List<JSONObject> searchCustomer(@RequestBody JSONObject jsonObject) {
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse =  customerDao.searchCustomer(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);
        }
        return data;
    }

    /**
     * 设备列表搜索
     */
    @RequestMapping("/device/search")
    public List<JSONObject> searchDevice(@RequestBody JSONObject jsonObject) {
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse =  customerDao.searchDevice(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);
        }
        return data;
    }

    /**
     * 业务安全拓扑图
     */
    @RequestMapping("/topology")
    public List<JSONObject> getTopology(@RequestBody JSONObject jsonObject) {
        log.info("getTopology : {}", jsonObject);
        List<JSONObject> data = new ArrayList<JSONObject>();
        List<JSONObject> pageResponse = customerDao.getBusinessSecurityTopology(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null && !pageResponse.isEmpty()){
            JSONObjectUtil.convertBigNumberToString(pageResponse);
            data.addAll(pageResponse);
        }
        return data;
    }

    /**
     * 根据设备id查询设备属性
     */
    @RequestMapping("/device/property")
    public List<JSONObject> getDevicePropertyById(@RequestBody JSONObject jsonObject) {
        log.info("getDevicePropertyById : {}", jsonObject);
        List<JSONObject> data = customerDao.getDevicePropertyById(jsonObject ,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        return data;
    }


}
