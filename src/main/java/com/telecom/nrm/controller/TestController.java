package com.telecom.nrm.controller;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.IpranService;
import com.telecom.nrm.thread.audit.Otn;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/api/resTree")
@Slf4j
public class TestController {

    @Autowired
    private Otn otn;
    @Autowired
    private IpranService ipranService;

    @Autowired
    private CircuitAnalysisDao circuitAnalysisDao;

    @GetMapping("/otnTest/{code}")
    public String otnTest(@PathVariable("code") String code) {
        otn.saveOtnInfo(code);
        return "success";
    }


    /**
     * 保存局向光纤
     * @param id
     * @return
     */
    @GetMapping("/routesave/{id}/{shardingCode}")
    public String routesave(@PathVariable("id") String id,@PathVariable("shardingCode") String shardingCode) {
        otn.routesave(id,shardingCode);
        return "success";
    }


    @GetMapping("/alarm")
    public String alarm() {
        List<String> accessCodes =  new ArrayList<>();


        accessCodes.add("141118472");
        accessCodes.add("M15831294");
        accessCodes.add("M48562707");
        accessCodes.add("M07272766");
        accessCodes.add("12450072");
        accessCodes.add("M22836380");
        accessCodes.add("M00177064");
        accessCodes.add("M40048790");
        accessCodes.add("MSTPJS1002901386");
        accessCodes.add("MSTPJS1003109993");
        accessCodes.add("M22236389");
        accessCodes.add("M08851174");
        accessCodes.add("M09416159");
        accessCodes.add("M40262685");
        accessCodes.add("M10131168");
        accessCodes.add("MSTPJS1002905323");
        accessCodes.add("M25996888");
        accessCodes.add("M40116432");
        accessCodes.add("M40063439");
        accessCodes.add("MSTPJS1001711010");
        accessCodes.add("M10281162");
        accessCodes.add("M40052219");
        accessCodes.add("M52947966");
        accessCodes.add("M00181710");
        accessCodes.add("M10131171");
        accessCodes.add("M59072964");
        accessCodes.add("M10176189");
        accessCodes.add("M08866201");
        accessCodes.add("M08866177");
        accessCodes.add("K01415");
        accessCodes.add("M40053073");
        accessCodes.add("M00286956");
        accessCodes.add("M00280879");
        accessCodes.add("MSTPJS1003313440");
        accessCodes.add("M61402965");
        accessCodes.add("M00222528");
        accessCodes.add("MSTPJS1002131402");
        accessCodes.add("M63103174");
        accessCodes.add("12265030");
        accessCodes.add("M00302244");
        accessCodes.add("F6211834b");
        accessCodes.add("M22361377");
        accessCodes.add("MSTPJT1001799064");
        accessCodes.add("M45142956");
        accessCodes.add("M40052865");
        accessCodes.add("M07000614");
        accessCodes.add("M24251387");
        accessCodes.add("M40050996");
        accessCodes.add("HLWZX510202302142304");
        accessCodes.add("M00113207");
        accessCodes.add("M46452956");
        accessCodes.add("M24451379");
        accessCodes.add("M09631159");
        accessCodes.add("MSTPJS1002648203");
        accessCodes.add("M00196378");
        accessCodes.add("M49287701");
        accessCodes.add("M22001376");
        accessCodes.add("M23516385");
        accessCodes.add("M40051722");
        accessCodes.add("MSTPJS1001854118");
        accessCodes.add("MSTPJS1002876517");
        accessCodes.add("M08866162");
        accessCodes.add("HLWZX52720220638384");
        accessCodes.add("M46252968");
        accessCodes.add("M28931887");
        accessCodes.add("M10176168");
        accessCodes.add("MSTPJS1002982617");
        accessCodes.add("M25646898");
        accessCodes.add("M00005646");
        accessCodes.add("M16856354");
        accessCodes.add("M49402959");
        accessCodes.add("M07261624");
        accessCodes.add("M40053225");
        accessCodes.add("M28351894");
        accessCodes.add("M40052550");
        accessCodes.add("M00113219");
        accessCodes.add("MSTPJS1003292090");
        accessCodes.add("M19486382");
        accessCodes.add("M24711376");
        accessCodes.add("M00222056");
        accessCodes.add("M43627691");
        accessCodes.add("M19386428");
        accessCodes.add("M00286952");
        accessCodes.add("140246444");
        accessCodes.add("M00308347");
        accessCodes.add("M24126615");
        accessCodes.add("MSTPJS1002716975");
        accessCodes.add("18STN010564");
        accessCodes.add("M00151363");
        accessCodes.add("M00216877");
        accessCodes.add("M00223481");
        accessCodes.add("M17626375");
        accessCodes.add("MSTPJS1003191945");
        accessCodes.add("M45772956");
        accessCodes.add("M08861171");
        accessCodes.add("M40053719");
        accessCodes.add("MSTPJS1002171387");
        accessCodes.add("M12886165");
        accessCodes.add("M16751378");
        accessCodes.add("M43607962");
        accessCodes.add("MSTPJS1003355774");
        accessCodes.add("M40062967");
        accessCodes.add("M23186386");
        accessCodes.add("M59222968");
        accessCodes.add("M40042439");
        accessCodes.add("M48477701");
        accessCodes.add("MSTPJS1003246543");
        accessCodes.add("M48587721");
        accessCodes.add("南京南通ETN0140NP");
        accessCodes.add("M40073937");
        accessCodes.add("M30101888");
        accessCodes.add("M48272965");
        accessCodes.add("MSTPJS1002164511");
        accessCodes.add("MSTPJS1002318030");
        accessCodes.add("M00280196");
        accessCodes.add("M17991378");
        accessCodes.add("M20286380");
        accessCodes.add("MSTPJS1002674310");
        accessCodes.add("MSTPJS1003278442");
        accessCodes.add("北京连云港ZQOTN00002");
        accessCodes.add("M00314978");
        accessCodes.add("M00279748");
        accessCodes.add("M00102487");
        accessCodes.add("M08851231");
        accessCodes.add("M40047816");
        accessCodes.add("M00289346");
        accessCodes.add("MSTPJS1003337911");
        accessCodes.add("M16581360");
        accessCodes.add("M45527956");
        accessCodes.add("M44222959");
        accessCodes.add("M22316439");
        accessCodes.add("M00115301");
        accessCodes.add("M16476357");
        accessCodes.add("M42367679");
        accessCodes.add("M16651369");
        accessCodes.add("MSTPBJ1003013423");
        accessCodes.add("M53777978");
        accessCodes.add("M00353101");
        accessCodes.add("12265342");
        accessCodes.add("M40054330");
        accessCodes.add("154317481");
        accessCodes.add("M00226716");
        accessCodes.add("145098793");
        accessCodes.add("M40052171");
        accessCodes.add("M00163301");
        accessCodes.add("HLWZX512202307205625");
        accessCodes.add("12456958");
        accessCodes.add("M46662956");
        accessCodes.add("M00115219");
        accessCodes.add("M10341201");
        accessCodes.add("M10286159");
        accessCodes.add("M40064193");
        accessCodes.add("M07030546");
        accessCodes.add("M48587715");
        accessCodes.add("M00296162");
        accessCodes.add("M00247949");
        accessCodes.add("M00113146");
        accessCodes.add("M16951375");
        accessCodes.add("HLWZX51820230674644");
        accessCodes.add("M40060356");
        accessCodes.add("M00276736");
        accessCodes.add("M40007626");
        accessCodes.add("M00276063");
        accessCodes.add("MSTPJS1002715685");
        accessCodes.add("MSTPJS1001861064");
        accessCodes.add("MSTPJS1001663595");
        accessCodes.add("MSTPBJ1001932031");
        accessCodes.add("M14621231");
        accessCodes.add("M46157710");
        accessCodes.add("MSTPJS1003048372");
        accessCodes.add("M50462963");
        accessCodes.add("HLWZX523202309103079");
        accessCodes.add("M00127799");
        accessCodes.add("M40072487");
        accessCodes.add("M00320141");
        accessCodes.add("M15066235");
        accessCodes.add("M00215123");
        accessCodes.add("M00183264");
        accessCodes.add("15959493");
        accessCodes.add("M41147680");
        accessCodes.add("23956229");
        accessCodes.add("M14606246");
        accessCodes.add("154317902");
        accessCodes.add("M21326377");
        accessCodes.add("MSTPJS1003049902");
        accessCodes.add("M10346184");
        accessCodes.add("19342020");
        accessCodes.add("M40053382");
        accessCodes.add("M54977967");
        accessCodes.add("M00115328");
        accessCodes.add("M00223098");
        accessCodes.add("M17086372");
        accessCodes.add("M40052522");
        accessCodes.add("M23516394");
        accessCodes.add("M40059865");
        accessCodes.add("M41622959");
        accessCodes.add("M13011183");
        accessCodes.add("M00247789");
        accessCodes.add("M19731412");
        accessCodes.add("M59327964");
        accessCodes.add("M40172998");
        accessCodes.add("M00280599");
        accessCodes.add("M28161890");
        accessCodes.add("MSTPJS1003024969");
        accessCodes.add("MSTPJT1001983753");
        accessCodes.add("M40052868");
        accessCodes.add("M54107974");
        accessCodes.add("M53282964");
        accessCodes.add("1126550265");
        accessCodes.add("M00145221");
        accessCodes.add("M40046310");
        accessCodes.add("HLWZX51320230394981");
        accessCodes.add("MSTPJS1002776419");
        accessCodes.add("M00107408");
        accessCodes.add("M46157698");
        accessCodes.add("MSTPJS1002682200");
        accessCodes.add("M00261002");
        accessCodes.add("MSTPJS1001810537");
        accessCodes.add("154317405");
        accessCodes.add("M08851207");
        accessCodes.add("15366030");
        accessCodes.add("M40053637");
        accessCodes.add("M22316398");
        accessCodes.add("M40009221");
        accessCodes.add("M28481888");
        accessCodes.add("M00141784");
        accessCodes.add("M40144139");
        accessCodes.add("M18661390");
        accessCodes.add("154318079");
        accessCodes.add("M40052334");
        accessCodes.add("M27981891");
        accessCodes.add("MSTPJS1003225905");
        accessCodes.add("M44463004");
        accessCodes.add("M49542967");
        accessCodes.add("M40053412");
        accessCodes.add("13874730");
        accessCodes.add("M40053900");
        accessCodes.add("MSTPJS1001565321");
        accessCodes.add("M40052356");
        accessCodes.add("154317917");
        accessCodes.add("M15876285");
        accessCodes.add("CZ190200_c");
        accessCodes.add("M43182995");
        accessCodes.add("M10701168");
        accessCodes.add("MSTPJS1002823182");
        accessCodes.add("M00244781");
        accessCodes.add("M40053064");
        accessCodes.add("MSTPJS1002726401");
        accessCodes.add("M08866186");
        accessCodes.add("M10301168");
        accessCodes.add("M00197956");
        accessCodes.add("MSTPJS1002797946");
        accessCodes.add("M40050990");
        accessCodes.add("M43627679");
        accessCodes.add("MSTPJS1002312845");
        accessCodes.add("M08846195");
        accessCodes.add("M46696929");
        accessCodes.add("M10346187");
        accessCodes.add("M08866168");
        accessCodes.add("MSTPJS1003254184");
        accessCodes.add("M00115334");
        accessCodes.add("M27311888");
        accessCodes.add("M32386897");
        accessCodes.add("M19521384");
        accessCodes.add("M43602959");
        accessCodes.add("M43592976");
        accessCodes.add("M00347981");
        accessCodes.add("M00002943");
        accessCodes.add("M57942965");
        accessCodes.add("12265476");
        accessCodes.add("M10171162");
        accessCodes.add("M15206231");
        accessCodes.add("M10171174");
        accessCodes.add("M30851888");
        accessCodes.add("14703070");
        accessCodes.add("M00314967");
        accessCodes.add("MSTPJS1002189114");
        accessCodes.add("M40048274");
        accessCodes.add("M00286854");
        accessCodes.add("M40052947");
        accessCodes.add("25738257");
        accessCodes.add("M00278243");
        accessCodes.add("MSTPJS1003059592");
        accessCodes.add("M10256170");
        accessCodes.add("M16571375");
        accessCodes.add("M00295322");
        accessCodes.add("M00009100");
        accessCodes.add("M40068362");
        accessCodes.add("Z12245");
        accessCodes.add("M47637701");
        accessCodes.add("M00253495");
        accessCodes.add("M00302104");
        accessCodes.add("M07008686");
        accessCodes.add("HLWZX51820220537437");
        accessCodes.add("M58972969");
        accessCodes.add("M17536381");
        accessCodes.add("M07027667");
        accessCodes.add("M00273974");
        accessCodes.add("M00280311");
        accessCodes.add("M20321378");
        accessCodes.add("M42977679");
        accessCodes.add("M00262706");
        accessCodes.add("M00319804");
        accessCodes.add("M00113302");
        accessCodes.add("M16846486");
        accessCodes.add("M24061378");
        accessCodes.add("M19816383");
        accessCodes.add("M21876376");
        accessCodes.add("M46107698");
        accessCodes.add("M22271382");
        accessCodes.add("M07024978");
        accessCodes.add("M54277968");
        accessCodes.add("M24861381");
        accessCodes.add("154317483");
        accessCodes.add("MSTPJT1002388617");
        accessCodes.add("M26441903");
        accessCodes.add("M51972963");
        accessCodes.add("115807488");
        accessCodes.add("M12826176");
        accessCodes.add("M00291798");
        accessCodes.add("MSTPJS1001857066");
        accessCodes.add("M18691403");
        accessCodes.add("M10176171");
        accessCodes.add("M40013373");
        accessCodes.add("M40062011");
        accessCodes.add("M52128496");
        accessCodes.add("M00281266");
        accessCodes.add("M00297108");
        accessCodes.add("M40058283");
        accessCodes.add("M40054867");
        accessCodes.add("MSTPJS1002986310");
        accessCodes.add("M16596364");
        accessCodes.add("M00183273");
        accessCodes.add("M00216108");
        accessCodes.add("M48507707");
        accessCodes.add("M00235237");
        accessCodes.add("M40050073");
        accessCodes.add("M00316125");
        accessCodes.add("M40048476");
        accessCodes.add("M07006405");
        accessCodes.add("M00319101");
        accessCodes.add("M43507971");
        accessCodes.add("M00260242");
        accessCodes.add("12265326");
        accessCodes.add("MSTPJS1003053114");
        accessCodes.add("M00299343");
        accessCodes.add("M00002909");
        accessCodes.add("M24806381");
        accessCodes.add("M47487959");
        accessCodes.add("M41537682");
        accessCodes.add("M16951372");
        accessCodes.add("M40052721");
        accessCodes.add("M57587964");
        accessCodes.add("109005404");
        accessCodes.add("M57007966");
        accessCodes.add("M19421385");
        accessCodes.add("M00264008");
        accessCodes.add("MSTPJS1002940972");
        accessCodes.add("MSTPJS1002292734");
        accessCodes.add("M18016382");
        accessCodes.add("X00012562NJ568");
        accessCodes.add("M48587708");
        accessCodes.add("M00264011");
        accessCodes.add("MSTPJS1001984101");
        accessCodes.add("M40053055");
        accessCodes.add("M00002939");
        accessCodes.add("MSTPJS1001526785");
        accessCodes.add("M40059874");
        accessCodes.add("MSTPJS1001955011");
        accessCodes.add("M40702971");
        accessCodes.add("M40053173");
        accessCodes.add("M00271713");
        accessCodes.add("12264604");
        accessCodes.add("M00290704");
        accessCodes.add("114898944");
        accessCodes.add("M48437700");
        accessCodes.add("M25201392");
        accessCodes.add("M23031403");
        accessCodes.add("M48557705");
        accessCodes.add("M47102958");
        accessCodes.add("12265358");
        accessCodes.add("南京南通ETN0661NP");
        accessCodes.add("M07240769");
        accessCodes.add("M40053616");
        accessCodes.add("M54307966");
        accessCodes.add("MSTPJS1002120595");
        accessCodes.add("M07240766");
        accessCodes.add("M21241382");
        accessCodes.add("154317409");
        accessCodes.add("M00262810");
        accessCodes.add("MSTPJS1002870477");
        accessCodes.add("M40692959");
        accessCodes.add("M00218960");
        accessCodes.add("M48117956");
        accessCodes.add("MSTPJS1002290061");
        accessCodes.add("M17386405");
        accessCodes.add("M52397969");
        accessCodes.add("M40047851");
        accessCodes.add("M20631378");
        accessCodes.add("M40067114");
        accessCodes.add("M00261588");
        accessCodes.add("M40053385");
        accessCodes.add("M41687981");
        accessCodes.add("M40047923");
        accessCodes.add("M00174349");
        accessCodes.add("M40058280");
        accessCodes.add("M30401887");
        accessCodes.add("M24756405");
        accessCodes.add("B00007627");
        accessCodes.add("M00157388");
        accessCodes.add("M61702967");
        accessCodes.add("M00005661");
        accessCodes.add("MSTPJS1002393763");
        accessCodes.add("M23701397");
        accessCodes.add("M22061378");
        accessCodes.add("M00112519");
        accessCodes.add("M00293622");
        accessCodes.add("M10336165");
        accessCodes.add("M53202979");
        accessCodes.add("154317469");
        accessCodes.add("M00231434");
        accessCodes.add("12346039");
        accessCodes.add("M56247977");
        accessCodes.add("MSTPJS1001812195");
        accessCodes.add("M08866195");
        accessCodes.add("M60777965");
        accessCodes.add("M40052525");
        accessCodes.add("MSTPJS1003028059");
        accessCodes.add("M07036204");
        accessCodes.add("北京南京ZQOTN00099");
        accessCodes.add("M42058482");
        accessCodes.add("M00280922");
        accessCodes.add("M18171381");
        accessCodes.add("HLWZX523202311105972");
        accessCodes.add("M00291588");
        accessCodes.add("MSTPJS1002701073");
        accessCodes.add("M40053391");
        accessCodes.add("M40052715");
        accessCodes.add("12265088");
        accessCodes.add("M40053259");
        accessCodes.add("MSTPJS1002652937");
        accessCodes.add("M40053376");
        accessCodes.add("MSTPJS1003186991");
        accessCodes.add("MSTPJS1002678110");
        accessCodes.add("MSTPJS1002943759");
        accessCodes.add("JS10cy001962");
        accessCodes.add("M00254645");
        accessCodes.add("M42262984");
        accessCodes.add("M00195409");
        accessCodes.add("MSTPJS1002783669");
        accessCodes.add("JS13cy000474");
        accessCodes.add("M00183332");
        accessCodes.add("MSTPJS1002377624");
        accessCodes.add("12265452");
        accessCodes.add("M00215093");
        accessCodes.add("MSTPJS1002963634");
        accessCodes.add("M00113227");
        accessCodes.add("M40013253");
        accessCodes.add("M48142962");
        accessCodes.add("120079477");
        accessCodes.add("M48468482");
        accessCodes.add("M40047813");
        accessCodes.add("MSTPJS1001811179");
        accessCodes.add("MSTPJS1003003782");
        accessCodes.add("HLWZX512202307205620");
        accessCodes.add("M56682967");
        accessCodes.add("M08851237");
        accessCodes.add("M00302041");
        accessCodes.add("HLWZX025202304152783");
        accessCodes.add("M40053342");
        accessCodes.add("M22376382");
        accessCodes.add("M00210359");
        accessCodes.add("12458050");
        accessCodes.add("MSTPJT1001800067");
        accessCodes.add("M45962956");
        accessCodes.add("M43592973");
        accessCodes.add("M40053170");
        accessCodes.add("M40367956");
        accessCodes.add("M40050467");
        accessCodes.add("M00145884");
        accessCodes.add("M64503164");
        accessCodes.add("M16681360");
        accessCodes.add("M00296167");
        accessCodes.add("M07036504");
        accessCodes.add("12265057");
        accessCodes.add("MSTPJS1003030513");
        accessCodes.add("MSTPJS1002865801");
        accessCodes.add("M40052725");
        accessCodes.add("M40054345");
        accessCodes.add("M50207965");
        accessCodes.add("M11021165");
        accessCodes.add("M48537962");
        accessCodes.add("M40055101");
        accessCodes.add("M00289732");
        accessCodes.add("M61822968");
        accessCodes.add("JS14cy000503");
        accessCodes.add("M50642963");
        accessCodes.add("M40051516");
        accessCodes.add("M56927964");
        accessCodes.add("M40053082");
        accessCodes.add("M54142964");
        accessCodes.add("M07245676");
        accessCodes.add("M40059593");
        accessCodes.add("115315619");
        accessCodes.add("M21166379");
        accessCodes.add("M40057806");
        accessCodes.add("M07004538");
        accessCodes.add("M54457967");
        accessCodes.add("M00142081");
        accessCodes.add("M40053040");
        accessCodes.add("M51002703");
        accessCodes.add("15202201");
        accessCodes.add("M00113194");
        accessCodes.add("MSTPJS1003078477");
        accessCodes.add("15958609");
        accessCodes.add("M00104645");
        accessCodes.add("M00303667");
        accessCodes.add("M41047967");
        accessCodes.add("M00183635");
        accessCodes.add("M00281493");
        accessCodes.add("M10131174");
        accessCodes.add("M56367964");
        accessCodes.add("M15811264");
        accessCodes.add("18STN010566");
        accessCodes.add("M00316126");
        accessCodes.add("M40052128");
        accessCodes.add("M54357966");
        accessCodes.add("M42722980");
        accessCodes.add("M00179482");
        accessCodes.add("M40352962");
        accessCodes.add("M44366919");
        accessCodes.add("M42327959");
        accessCodes.add("M40048766");
        accessCodes.add("MSTPJS1002228187");
        accessCodes.add("MSTPJS1003251210");
        accessCodes.add("M19206385");
        accessCodes.add("M00280051");
        accessCodes.add("MSTPJS1003046977");
        accessCodes.add("MSTPJS1002652972");
        accessCodes.add("M45057956");
        accessCodes.add("MSTPJS1001984730");
        accessCodes.add("MSTPJS1002441729");
        accessCodes.add("MSTPJS1002981410");
        accessCodes.add("M49832976");
        accessCodes.add("M40056253");
        accessCodes.add("M65278168");
        accessCodes.add("154317490");
        accessCodes.add("MSTPJS1002629222");
        accessCodes.add("MSTPJS1002285848");
        accessCodes.add("JS11cy000762");
        accessCodes.add("M40062145");
        accessCodes.add("154318081");
        accessCodes.add("M10281159");
        accessCodes.add("M00269603");
        accessCodes.add("115912537");
        accessCodes.add("M00308385");
        accessCodes.add("M53907976");
        accessCodes.add("MSTPJS1002322421");
        accessCodes.add("M48876922");
        accessCodes.add("MSTPJS1002529369");
        accessCodes.add("M43592982");
        accessCodes.add("M45557960");
        accessCodes.add("M25081405");
        accessCodes.add("M15301294");
        accessCodes.add("M40051570");
        accessCodes.add("MSTPJS1001991582");
        accessCodes.add("M40048636");
        accessCodes.add("M54462974");
        accessCodes.add("M00291111");
        accessCodes.add("MSTPJS1003080701");
        accessCodes.add("MSTPJS1002191246");
        accessCodes.add("M00314941");
        accessCodes.add("M46157726");
        accessCodes.add("M40032897");
        accessCodes.add("M40051487");
        accessCodes.add("18STN010568");
        accessCodes.add("M00313145");
        accessCodes.add("M40052331");
        accessCodes.add("M57377964");
        accessCodes.add("M00262452");
        accessCodes.add("M61702971");
        accessCodes.add("M40051513");
        accessCodes.add("M00291709");
        accessCodes.add("M00272180");
        accessCodes.add("MSTPJS1002000641");
        accessCodes.add("M16311381");
        accessCodes.add("M00244799");
        accessCodes.add("M22641379");
        accessCodes.add("M40052605");
        accessCodes.add("MSTPJS1002653586");
        accessCodes.add("HLWZX51020211267298");
        accessCodes.add("M40058097");
        accessCodes.add("M15891243");
        accessCodes.add("MSTPJS1003209342");
        accessCodes.add("M40055056");
        accessCodes.add("12264545");
        accessCodes.add("14768173");
        accessCodes.add("M56587979");
        accessCodes.add("MSTPJS1003113473");
        accessCodes.add("M15646231");
        accessCodes.add("M48892956");
        accessCodes.add("M00262251");
        accessCodes.add("M40051896");
        accessCodes.add("M00119202");
        accessCodes.add("M40053302");
        accessCodes.add("M24096393");
        accessCodes.add("154317915");
        accessCodes.add("M14826264");
        accessCodes.add("M00143164");
        accessCodes.add("MSTPJS1002726457");
        accessCodes.add("MSTPJS1002291570");
        accessCodes.add("M00256582");
        accessCodes.add("M10166165");
        accessCodes.add("M40052709");
        accessCodes.add("M17606381");
        accessCodes.add("MSTPJS1002322931");
        accessCodes.add("M00244806");
        accessCodes.add("M00333681");
        accessCodes.add("M16141354");
        accessCodes.add("M00183252");
        accessCodes.add("M00298118");
        accessCodes.add("M00215081");
        accessCodes.add("MSTPJS1003336526");
        accessCodes.add("M00264728");
        accessCodes.add("M17561378");
        accessCodes.add("M10176159");
        accessCodes.add("M40053262");
        accessCodes.add("M00294639");
        accessCodes.add("M22141397");
        accessCodes.add("M61997964");
        accessCodes.add("M00275974");
        accessCodes.add("M40058583");
        accessCodes.add("M00298383");
        accessCodes.add("M09246159");
        accessCodes.add("L1003466");
        accessCodes.add("M40062186");
        accessCodes.add("M29361888");
        accessCodes.add("M18096390");
        accessCodes.add("M40050981");
        accessCodes.add("M40044761");
        accessCodes.add("M55212964");
        accessCodes.add("M00106651");
        accessCodes.add("M00231391");
        accessCodes.add("M22316422");
        accessCodes.add("M16236378");
        accessCodes.add("HLWZX523202409117658");
        accessCodes.add("M48542712");
        accessCodes.add("154318083");
        accessCodes.add("MSTPJS1002335433");
        accessCodes.add("M40052985");
        accessCodes.add("M15316237");
        accessCodes.add("M00143084");
        accessCodes.add("M40052970");
        accessCodes.add("M43592991");
        accessCodes.add("M40050984");
        accessCodes.add("M00323088");
        accessCodes.add("M32056892");
        accessCodes.add("M51702969");
        accessCodes.add("M60057964");
        accessCodes.add("14798679");
        accessCodes.add("M55472972");
        accessCodes.add("M48266931");
        accessCodes.add("M00326257");
        accessCodes.add("M17561381");
        accessCodes.add("M00113201");
        accessCodes.add("M40053222");
        accessCodes.add("M10341192");
        accessCodes.add("M40053607");
        accessCodes.add("HLWZX02520210212351");
        accessCodes.add("M42762679");
        accessCodes.add("M14826360");
        accessCodes.add("M41032959");
        accessCodes.add("M00183270");
        accessCodes.add("15959728");
        accessCodes.add("M40070587");
        accessCodes.add("M40052547");
        accessCodes.add("M15266243");
        accessCodes.add("M40033045");
        accessCodes.add("M40065427");
        accessCodes.add("M00308841");
        accessCodes.add("MSTPJS1001769242");
        accessCodes.add("M21131377");
        accessCodes.add("MSTPJS1001860036");
        accessCodes.add("M40055086");
        accessCodes.add("M00113240");
        accessCodes.add("M08851234");
        accessCodes.add("MSTPJS1002590135");
        accessCodes.add("113492007");
        accessCodes.add("M40003347");
        accessCodes.add("M08851192");
        accessCodes.add("M40075205");
        accessCodes.add("M40052880");
        accessCodes.add("M24251385");
        accessCodes.add("M00231385");
        accessCodes.add("M48252960");
        accessCodes.add("M43627682");
        accessCodes.add("MSTPJS1002321048");
        accessCodes.add("M17061357");
        accessCodes.add("M00217180");
        accessCodes.add("M40050987");
        accessCodes.add("MSTPJS1002863060");
        accessCodes.add("M23966395");
        accessCodes.add("M48742959");
        accessCodes.add("M58897966");
        accessCodes.add("MSTPJS1002284142");
        accessCodes.add("22091593");
        accessCodes.add("MSTPJS1002709974");
        accessCodes.add("M18531378");
        accessCodes.add("M40052595");
        accessCodes.add("M40052583");
        accessCodes.add("M00183309");
        accessCodes.add("M40061999");
        accessCodes.add("M19271405");
        accessCodes.add("M40053667");
        accessCodes.add("M40052982");
        accessCodes.add("M48457701");
        accessCodes.add("M46157727");
        accessCodes.add("M61702966");
        accessCodes.add("M24056379");
        accessCodes.add("M00276572");
        accessCodes.add("MSTPJT1003263262");
        accessCodes.add("MSTPJS1002828549");
        accessCodes.add("M15106237");
        accessCodes.add("MSTPJT1001800066");
        accessCodes.add("MSTPJS1001892694");
        accessCodes.add("M59007968");
        accessCodes.add("M48002956");
        accessCodes.add("M40053052");
        accessCodes.add("M07259977");
        accessCodes.add("M24861378");
        accessCodes.add("MSTPJT1001893390");
        accessCodes.add("M00223816");
        accessCodes.add("M40064028");
        accessCodes.add("M51112966");
        accessCodes.add("M40052752");
        accessCodes.add("15958878");
        accessCodes.add("M00261649");
        accessCodes.add("M23886378");
        accessCodes.add("HLWZX51020220381975");
        accessCodes.add("MSTPJS1003147535");
        accessCodes.add("14580793");
        accessCodes.add("M47922963");
        accessCodes.add("M16671354");
        accessCodes.add("M07299136");
        accessCodes.add("M00154484");
        accessCodes.add("149510859");
        accessCodes.add("M40052973");
        accessCodes.add("M00254776");
        accessCodes.add("M18721381");
        accessCodes.add("M00221197");
        accessCodes.add("M07831179");
        accessCodes.add("M23546389");
        accessCodes.add("M50207968");
        accessCodes.add("MSTPJS1002881433");
        accessCodes.add("M21716382");
        accessCodes.add("MSTPJS1002379215");
        accessCodes.add("M18866396");
        accessCodes.add("M00104666");
        accessCodes.add("M00008555");
        accessCodes.add("M00302648");
        accessCodes.add("MSTPJS1002715291");
        accessCodes.add("M00323431");
        accessCodes.add("HLWZX51020211264241");
        accessCodes.add("M30346890");
        accessCodes.add("15958998");
        accessCodes.add("M00222543");
        accessCodes.add("M44462959");
        accessCodes.add("M08661175");
        accessCodes.add("M40053348");
        accessCodes.add("MSTPJS1001918029");
        accessCodes.add("HLWZX025202403196685");
        accessCodes.add("M17576372");
        accessCodes.add("M00209595");
        accessCodes.add("MSTPJS1001993621");
        accessCodes.add("M40057034");
        accessCodes.add("M08861165");
        accessCodes.add("M55657965");
        accessCodes.add("M40051349");
        accessCodes.add("MSTPJS1002655928");
        accessCodes.add("M08851201");
        accessCodes.add("MSTPJT1001799090");
        accessCodes.add("M40009286");
        accessCodes.add("M50872963");
        accessCodes.add("M00305101");
        accessCodes.add("MSTPJS1001995430");
        accessCodes.add("M00160641");
        accessCodes.add("M55467968");
        accessCodes.add("M00190595");
        accessCodes.add("M40053379");
        accessCodes.add("M48602707");
        accessCodes.add("M00206669");
        accessCodes.add("M40047810");
        accessCodes.add("M00280423");
        accessCodes.add("M48362962");
        accessCodes.add("M00104657");
        accessCodes.add("M00222558");
        accessCodes.add("MSTPJS1001950113");
        accessCodes.add("110405392");
        accessCodes.add("MSTPJS1001991396");
        accessCodes.add("M10176165");
        accessCodes.add("MSTPJS1001698318");
        accessCodes.add("140762231");
        accessCodes.add("M43607959");
        accessCodes.add("M00290842");
        accessCodes.add("MSTPJS1001992724");
        accessCodes.add("M18891375");
        accessCodes.add("MSTPJT1001796061");
        accessCodes.add("M14606243");
        accessCodes.add("116954881");
        accessCodes.add("M31821899");
        accessCodes.add("M48517703");
        accessCodes.add("M00115228");
        accessCodes.add("M19906386");
        accessCodes.add("MSTPJT1001798112");
        accessCodes.add("M16741375");
        accessCodes.add("M00231413");
        accessCodes.add("M40021987");
        accessCodes.add("M46157965");
        accessCodes.add("M56747964");
        accessCodes.add("M14976231");
        accessCodes.add("M00115216");
        accessCodes.add("M40242971");
        accessCodes.add("M48477708");
        accessCodes.add("M17061354");
        accessCodes.add("M55782964");
        accessCodes.add("M00145321");
        accessCodes.add("M00244793");
        accessCodes.add("M30236892");
        accessCodes.add("M00107141");
        accessCodes.add("118159788");
        accessCodes.add("M19941423");
        accessCodes.add("M56542968");
        accessCodes.add("M12691159");
        accessCodes.add("M44697960");
        accessCodes.add("M49257965");
        accessCodes.add("MSTPJS1002651036");
        accessCodes.add("154318073");
        accessCodes.add("1124477169");
        accessCodes.add("MSTPJS1002485977");
        accessCodes.add("M00123771");
        accessCodes.add("M48997959");
        accessCodes.add("M46137963");
        accessCodes.add("HLWZX512202403264206");
        accessCodes.add("M40052556");
        accessCodes.add("M41577971");
        accessCodes.add("M00005664");
        accessCodes.add("M58467964");
        accessCodes.add("M40053822");
        accessCodes.add("M40046785");
        accessCodes.add("15959400");
        accessCodes.add("M00334303");
        accessCodes.add("M00227214");
        accessCodes.add("M22121378");
        accessCodes.add("M40053037");
        accessCodes.add("M00314974");
        accessCodes.add("M00292730");
        accessCodes.add("M48513476");
        accessCodes.add("MSTPJS1002589255");
        accessCodes.add("M13436165");
        accessCodes.add("M07002152");
        accessCodes.add("M40052764");
        accessCodes.add("12264844");
        accessCodes.add("MSTPJS1001984934");
        accessCodes.add("MSTPJS1002394915");
        accessCodes.add("M43642679");
        accessCodes.add("15959768");
        accessCodes.add("M10166159");
        accessCodes.add("M64488166");
        accessCodes.add("15959695");
        accessCodes.add("MSTPJS1003008668");
        accessCodes.add("M52292969");
        accessCodes.add("MSTPJS1003260457");
        accessCodes.add("M40076007");
        accessCodes.add("M00326260");
        accessCodes.add("M40052110");
        accessCodes.add("M08861180");
        accessCodes.add("X00003154NJ023");
        accessCodes.add("M66093163");
        accessCodes.add("M48542711");
        accessCodes.add("MSTPJS1001980161");
        accessCodes.add("M40002937");
        accessCodes.add("M00002941");
        accessCodes.add("MSTPJT1001796088");
        accessCodes.add("M16936375");
        accessCodes.add("HLWZX512202306199024");
        accessCodes.add("MSTPJS1003197835");
        accessCodes.add("M41842956");
        accessCodes.add("M40045293");
        accessCodes.add("M40062180");
        accessCodes.add("M52217963");
        accessCodes.add("M00234268");
        accessCodes.add("M23516401");
        accessCodes.add("M40058610");
        accessCodes.add("M00231423");
        accessCodes.add("M00141255");
        accessCodes.add("M56757966");
        accessCodes.add("M56497968");
        accessCodes.add("M27961893");
        accessCodes.add("MSTPJS1002652936");
        accessCodes.add("M40060913");
        accessCodes.add("M00299164");
        accessCodes.add("M19826378");
        accessCodes.add("HLWZX51820210310109");
        accessCodes.add("M00154687");
        accessCodes.add("M40262697");
        accessCodes.add("M51462963");
        accessCodes.add("MSTPJS1002007528");
        accessCodes.add("M48557716");
        accessCodes.add("112011837");
        accessCodes.add("M00273977");
        accessCodes.add("MSTPJS1002769675");
        accessCodes.add("M00314966");
        accessCodes.add("M00280596");
        accessCodes.add("北京苏州ZQOTN00032");
        accessCodes.add("M52652969");
        accessCodes.add("MSTPJS1003000434");
        accessCodes.add("M00107370");
        accessCodes.add("M00231412");
        accessCodes.add("M08866180");
        accessCodes.add("M08851213");
        accessCodes.add("M14786234");
        accessCodes.add("9198781");
        accessCodes.add("MSTPJT1001984770");
        accessCodes.add("M10311168");
        accessCodes.add("M40047861");
        accessCodes.add("M43592988");
        accessCodes.add("M00296166");
        accessCodes.add("M00002837");
        accessCodes.add("M00183322");
        accessCodes.add("MSTPJS1003194402");
        accessCodes.add("M21651384");
        accessCodes.add("M28081887");
        accessCodes.add("M00231360");
        accessCodes.add("M13121183");
        accessCodes.add("M10346171");
        accessCodes.add("M00249083");
        accessCodes.add("M55617968");
        accessCodes.add("M50212976");
        accessCodes.add("M00247732");
        accessCodes.add("MSTPJS1002451660");
        accessCodes.add("M40059596");
        accessCodes.add("MSTPJS1002900051");
        accessCodes.add("M00113225");
        accessCodes.add("MSTPJS1002649477");
        accessCodes.add("154317412");
        accessCodes.add("118705171");
        accessCodes.add("M40055281");
        accessCodes.add("M58517966");
        accessCodes.add("MSTPJS1003100429");
        accessCodes.add("M48557720");
        accessCodes.add("M40053339");
        accessCodes.add("M00223556");
        accessCodes.add("M00115337");
        accessCodes.add("M40059507");
        accessCodes.add("151052210");
        accessCodes.add("149485991");
        accessCodes.add("M50222969");
        accessCodes.add("M00289098");
        accessCodes.add("M00345123");
        accessCodes.add("M00182322");
        accessCodes.add("上海南京ZQOTN00055");
        accessCodes.add("M00115962");
        accessCodes.add("MSTPJS1002316735");
        accessCodes.add("M13021165");
        accessCodes.add("M08846207");
        accessCodes.add("M40053243");
        accessCodes.add("M61237964");
        accessCodes.add("M40054539");
        accessCodes.add("M20636382");
        accessCodes.add("M40048479");
        accessCodes.add("M48468476");
        accessCodes.add("M00228896");
        accessCodes.add("MSTPJS1002174683");
        accessCodes.add("M30286888");
        accessCodes.add("MSTPJS1002589289");
        accessCodes.add("M00147161");
        accessCodes.add("M00147047");
        accessCodes.add("M48557719");
        accessCodes.add("M44407691");
        accessCodes.add("MSTPJS1001647166");
        accessCodes.add("M15831291");
        accessCodes.add("M40046304");
        accessCodes.add("HLWZX523202309102743");
        accessCodes.add("M40052690");
        accessCodes.add("M40052562");
        accessCodes.add("MSTPZJ1003197644");
        accessCodes.add("M40053067");
        accessCodes.add("M19681385");
        accessCodes.add("M47568485");
        accessCodes.add("154317485");
        accessCodes.add("MSTPJS1003290725");
        accessCodes.add("MSTPHB1001885688");
        accessCodes.add("HLWZX025202312190497");
        accessCodes.add("MSTPJS1002072033");
        accessCodes.add("MSTPJT1001798079");
        accessCodes.add("M07000611");
        accessCodes.add("M40052589");
        accessCodes.add("117914885");
        accessCodes.add("M44217963");
        accessCodes.add("M43592979");
        accessCodes.add("M40121458");
        accessCodes.add("154317398");
        accessCodes.add("M40052243");
        accessCodes.add("M00115213");
        accessCodes.add("M40002381");
        accessCodes.add("M00308283");
        accessCodes.add("MSTPJS1003164406");
        accessCodes.add("M15396234");
        accessCodes.add("140050471");
        accessCodes.add("M59957963");
        accessCodes.add("M40053234");
        accessCodes.add("M65198164");
        accessCodes.add("M43592985");
        accessCodes.add("M59222969");
        accessCodes.add("M08861174");
        accessCodes.add("M00101792");
        accessCodes.add("MSTPJS1003120477");
        accessCodes.add("M44297685");
        accessCodes.add("M57342965");
        accessCodes.add("MSTPJS1003334017");
        accessCodes.add("MSTPZJ1002791295");
        accessCodes.add("M00008294");
        accessCodes.add("M00290706");
        accessCodes.add("M40052994");
        accessCodes.add("M10276162");
        accessCodes.add("M00231359");
        accessCodes.add("M00228902");
        accessCodes.add("M08851189");
        accessCodes.add("M07466159");
        accessCodes.add("M40052712");
        accessCodes.add("M40056814");
        accessCodes.add("M40052288");
        accessCodes.add("M43612962");
        accessCodes.add("IP190JS1002125371");
        accessCodes.add("M20241387");
        accessCodes.add("M40052598");
        accessCodes.add("MSTPJS1003252517");
        accessCodes.add("M21151380");
        accessCodes.add("12264774");
        accessCodes.add("K00064");
        accessCodes.add("M40051346");
        accessCodes.add("M60662964");
        accessCodes.add("M00183329");
        accessCodes.add("M50202973");
        accessCodes.add("M48542705");
        accessCodes.add("M40040399");
        accessCodes.add("15958945");
        accessCodes.add("M00142186");
        accessCodes.add("M00113148");
        accessCodes.add("M26511888");
        accessCodes.add("M56545920");
        accessCodes.add("MSTPJT1001800065");
        accessCodes.add("M00170562");
        accessCodes.add("M00263831");
        accessCodes.add("L40401060004");
        accessCodes.add("M00279706");
        accessCodes.add("M00280232");
        accessCodes.add("M52047970");
        accessCodes.add("M60047966");
        accessCodes.add("MSTPJS1003029289");
        accessCodes.add("MSTPJS1002983342");
        accessCodes.add("M00002931");
        accessCodes.add("HLWZX025202409208481");
        accessCodes.add("M48652705");
        accessCodes.add("12264878");
        accessCodes.add("M23171378");
        accessCodes.add("M18291375");
        accessCodes.add("M19531386");
        accessCodes.add("MSTPJS1003219324");
        accessCodes.add("M24241412");
        accessCodes.add("M00280103");
        accessCodes.add("M51702972");
        accessCodes.add("M00215099");
        accessCodes.add("M51197963");
        accessCodes.add("MSTPJS1001658061");
        accessCodes.add("M00183232");
        accessCodes.add("M40262691");
        accessCodes.add("12264565");
        accessCodes.add("MSTPJS1001889545");
        accessCodes.add("M00270091");
        accessCodes.add("M40064267");
        accessCodes.add("M00350323");
        accessCodes.add("M40055250");
        accessCodes.add("M40053864");
        accessCodes.add("M40042244");
        accessCodes.add("M00003627");
        accessCodes.add("M00273560");
        accessCodes.add("M00279023");
        accessCodes.add("M00218552");
        accessCodes.add("15958984");
        accessCodes.add("M43612959");
        accessCodes.add("154318090");
        accessCodes.add("M00002919");
        accessCodes.add("MSTPJS1003044603");
        accessCodes.add("MSTPJS1003266534");
        accessCodes.add("M14246162");
        accessCodes.add("M40065916");
        accessCodes.add("MSTPJS1003025180");
        accessCodes.add("M40054173");
        accessCodes.add("M17386387");
        accessCodes.add("MSTPJS1002372968");
        accessCodes.add("M00222492");
        accessCodes.add("M40059750");
        accessCodes.add("M40054503");
        accessCodes.add("M59327966");
        accessCodes.add("M07010601");
        accessCodes.add("15958910");
        accessCodes.add("M00315026");
        accessCodes.add("M40051707");
        accessCodes.add("M00299230");
        accessCodes.add("M40077039");
        accessCodes.add("北京苏州ZQOTN00030");
        accessCodes.add("M40060003");
        accessCodes.add("M40002854");
        accessCodes.add("192286003");
        accessCodes.add("M22316436");
        accessCodes.add("M22316401");
        accessCodes.add("M00218796");
        accessCodes.add("M10126162");
        accessCodes.add("M00323871");
        accessCodes.add("M07036501");
        accessCodes.add("MSTPJS1002786590");
        accessCodes.add("M00106848");
        accessCodes.add("M00254408");
        accessCodes.add("M47332975");
        accessCodes.add("M00160547");
        accessCodes.add("M00310461");
        accessCodes.add("M08866204");
        accessCodes.add("MSTPJS1002893781");
        accessCodes.add("M56357966");
        accessCodes.add("M00295957");
        accessCodes.add("MSTPGZ1001936440");
        accessCodes.add("MSTPJS1003045709");
        accessCodes.add("12265334");
        accessCodes.add("M00183258");
        accessCodes.add("M47568528");
        accessCodes.add("M26861888");
        accessCodes.add("M22316438");
        accessCodes.add("JS25cy003182");
        accessCodes.add("M45527971");
        accessCodes.add("M10106184");
        accessCodes.add("M00251162");
        accessCodes.add("M40053028");
        accessCodes.add("M18171372");
        accessCodes.add("M16651372");
        accessCodes.add("M57350927");
        accessCodes.add("M48477703");
        accessCodes.add("MSTPJS1001605755");
        accessCodes.add("MSTPJS1002815060");
        accessCodes.add("12264816");
        accessCodes.add("M08851225");
        accessCodes.add("M40053034");
        accessCodes.add("M56567969");
        accessCodes.add("M40053415");
        accessCodes.add("M00282036");
        accessCodes.add("M40262679");
        accessCodes.add("MSTPJS1002864703");
        accessCodes.add("M00004215");
        accessCodes.add("M00199834");
        accessCodes.add("M52956925");
        accessCodes.add("MSTPJS1002431560");
        accessCodes.add("M25026391");
        accessCodes.add("M00143282");
        accessCodes.add("M40062029");
        accessCodes.add("M17351405");
        accessCodes.add("MSTPJT1003263260");
        accessCodes.add("M40057979");
        accessCodes.add("M00002822");
        accessCodes.add("M00104687");
        accessCodes.add("HLWZX025202405200108");
        accessCodes.add("HLWZX025202311189169");
        accessCodes.add("M40051301");
        accessCodes.add("M51887963");
        accessCodes.add("M22561402");
        accessCodes.add("15959606");
        accessCodes.add("15958859");
        accessCodes.add("M00301782");
        accessCodes.add("M22651376");
        accessCodes.add("M10571168");
        accessCodes.add("MSTPJS1001933053");
        accessCodes.add("M23516409");
        accessCodes.add("M00181549");
        accessCodes.add("M08851216");
        accessCodes.add("M00127005");
        accessCodes.add("M52817966");
        accessCodes.add("M00106377");
        accessCodes.add("MSTPJS1001853193");
        accessCodes.add("MSTPJS1003370224");
        accessCodes.add("M00261066");
        accessCodes.add("M28436892");
        accessCodes.add("M53988995");
        accessCodes.add("M17131474");
        accessCodes.add("M22271383");
        accessCodes.add("M00217881");
        accessCodes.add("M22806376");
        accessCodes.add("MSTPJS1003107897");
        accessCodes.add("M00181828");
        accessCodes.add("M00117082");
        accessCodes.add("M40062522");
        accessCodes.add("M40052862");
        accessCodes.add("M61662964");
        accessCodes.add("M40053849");
        accessCodes.add("M47117699");
        accessCodes.add("M00333711");
        accessCodes.add("M00115210");
        accessCodes.add("M24431375");
        accessCodes.add("M45402959");
        accessCodes.add("M52192966");
        accessCodes.add("M40052162");
        accessCodes.add("M00291222");
        accessCodes.add("M40053031");
        accessCodes.add("MSTPJT1001801090");
        accessCodes.add("M48507703");
        accessCodes.add("HLWZX515202401121920");
        accessCodes.add("1100410859");
        accessCodes.add("M55052964");
        accessCodes.add("M00183267");
        accessCodes.add("M65303166");
        accessCodes.add("M00176786");
        accessCodes.add("154317911");
        accessCodes.add("M30181892");
        accessCodes.add("MSTPJS1002791227");
        accessCodes.add("15958828");
        accessCodes.add("M00285397");
        accessCodes.add("M40053016");
        accessCodes.add("MSTPJS1002574719");
        accessCodes.add("M40077519");
        accessCodes.add("M07004529");
        accessCodes.add("M28051912");
        accessCodes.add("15169884");
        accessCodes.add("M43627694");
        accessCodes.add("MSTPJS1001750785");
        accessCodes.add("X00016393WX001");
        accessCodes.add("M00290946");
        accessCodes.add("M40052574");
        accessCodes.add("MSTPJS1002803934");
        accessCodes.add("M40075208");
        accessCodes.add("M00236495");
        accessCodes.add("M08866210");
        accessCodes.add("18STN010535");
        accessCodes.add("M48562701");
        accessCodes.add("M14191171");
        accessCodes.add("192245864");
        accessCodes.add("MSTPJS1003098034");
        accessCodes.add("14724572");
        accessCodes.add("M40053076");
        accessCodes.add("M00113326");
        accessCodes.add("MSTPJT1001797092");
        accessCodes.add("M00308921");
        accessCodes.add("M00280314");
        accessCodes.add("M22471379");
        accessCodes.add("M18016372");
        accessCodes.add("MSTPJS1003251127");
        accessCodes.add("K00952");
        accessCodes.add("M00294682");
        accessCodes.add("M00224438");
        accessCodes.add("M00278985");
        accessCodes.add("M57777965");
        accessCodes.add("MSTPJS1002940677");
        accessCodes.add("M43612995");
        accessCodes.add("M17166369");
        accessCodes.add("MSTPJS1001936022");
        accessCodes.add("M40063353");
        accessCodes.add("M14996231");
        accessCodes.add("M16596367");
        accessCodes.add("M00256062");
        accessCodes.add("KF00080");
        accessCodes.add("M16311357");
        accessCodes.add("MSTPJS1002714650");
        accessCodes.add("HLWZX51820201205631");
        accessCodes.add("M43507962");
        accessCodes.add("154318085");
        accessCodes.add("M23516431");
        accessCodes.add("M51492966");
        accessCodes.add("M00266651");
        accessCodes.add("M00151301");
        accessCodes.add("M53427982");
        accessCodes.add("M14826267");
        accessCodes.add("MSTPJS1002968644");
        accessCodes.add("M00216977");
        accessCodes.add("MSTPJS1001984115");
        accessCodes.add("M24761426");
        accessCodes.add("M00236464");
        accessCodes.add("M51697969");
        accessCodes.add("M40051719");
        accessCodes.add("MSTPJS1002263033");
        accessCodes.add("M00231403");
        accessCodes.add("MSTPJS1001808283");
        accessCodes.add("M40052859");
        accessCodes.add("M40512968");
        accessCodes.add("14470456");
        accessCodes.add("IPRANJS1002319247");
        accessCodes.add("M58112964");
        accessCodes.add("MSTPJS1002170322");
        accessCodes.add("M28346893");
        accessCodes.add("MSTPJS1002986020");
        accessCodes.add("M18146384");
        accessCodes.add("M40048147");
        accessCodes.add("M00341841");
        accessCodes.add("MSTPJS1001701105");
        accessCodes.add("M10126168");
        accessCodes.add("M00002865");
        accessCodes.add("M00308463");
        accessCodes.add("M00265114");
        accessCodes.add("M00270718");
        accessCodes.add("M54277970");
        accessCodes.add("M16006237");
        accessCodes.add("M14656240");
        accessCodes.add("M40052568");
        accessCodes.add("M00145781");
        accessCodes.add("MSTPJS1002606351");
        accessCodes.add("M40053858");
        accessCodes.add("MSTPJS1002677790");
        accessCodes.add("MSTPJS1001950103");
        accessCodes.add("M09131159");
        accessCodes.add("M10256162");
        accessCodes.add("M40054167");
        accessCodes.add("MSTPJT1001982781");
        accessCodes.add("M24596377");
        accessCodes.add("M00291723");
        accessCodes.add("M10276171");
        accessCodes.add("M00225022");
        accessCodes.add("MSTPJT1002778087");
        accessCodes.add("M00218904");
        accessCodes.add("M12726159");
        accessCodes.add("MSTPJS1002640056");
        accessCodes.add("M13161165");
        accessCodes.add("M00330113");
        accessCodes.add("M00287070");
        accessCodes.add("M16526360");
        accessCodes.add("M40058395");
        accessCodes.add("M40059110");
        accessCodes.add("M18016376");
        accessCodes.add("M53577963");
        accessCodes.add("M40037012");
        accessCodes.add("M12791165");
        accessCodes.add("M10106181");
        accessCodes.add("M16741357");
        accessCodes.add("M00296721");
        accessCodes.add("M00113288");
        accessCodes.add("14649940");
        accessCodes.add("M18211375");
        accessCodes.add("M52267969");
        accessCodes.add("Z12515");
        accessCodes.add("M40262682");
        accessCodes.add("M07261252");
        accessCodes.add("M00265111");
        accessCodes.add("M57157971");
        accessCodes.add("M00262453");
        accessCodes.add("M40577962");
        accessCodes.add("K02806");
        accessCodes.add("M40113530");
        accessCodes.add("M07257678");
        accessCodes.add("M43593003");
        accessCodes.add("M08866192");
        accessCodes.add("M56907969");
        accessCodes.add("MSTPZJ1002772287");
        accessCodes.add("M40047457");
        accessCodes.add("HLWZX52320230798783");
        accessCodes.add("154317475");
        accessCodes.add("MSTPJS1003348549");
        accessCodes.add("MSTPJS1002139700");
        accessCodes.add("M00301963");
        accessCodes.add("MSTPJS1002903588");
        accessCodes.add("M15986256");
        accessCodes.add("M61792964");
        accessCodes.add("MSTPJS1003219681");
        accessCodes.add("MSTPJS1002493077");
        accessCodes.add("MSTPJS1001700235");
        accessCodes.add("MSTPJS1002013974");
        accessCodes.add("M00269548");
        accessCodes.add("MSTPJS1002788148");
        accessCodes.add("141983640");
        accessCodes.add("M41632958");
        accessCodes.add("M07265530");
        accessCodes.add("M56627969");
        accessCodes.add("M51907972");
        accessCodes.add("MSTPJS1002502321");
        accessCodes.add("M52572963");
        accessCodes.add("M40052276");
        accessCodes.add("12264731");
        accessCodes.add("MSTPJS1001993609");
        accessCodes.add("HLWZX51820230262186");
        accessCodes.add("MSTPBJ1002382222");
        accessCodes.add("M00251407");
        accessCodes.add("M48562704");
        accessCodes.add("M48452706");
        accessCodes.add("M40065390");
        accessCodes.add("M07259442");
        accessCodes.add("MSTPJS1001421958");
        accessCodes.add("M22736391");
        accessCodes.add("M40052322");
        accessCodes.add("M55617966");
        accessCodes.add("M53577966");
        accessCodes.add("MSTPJS1003187202");
        accessCodes.add("M00338205");
        accessCodes.add("连云港嘉兴ZQOTN00001");
        accessCodes.add("MSTPJS1002591331");
        accessCodes.add("M00215102");
        accessCodes.add("MSTPJS1001993574");
        accessCodes.add("M11801180");
        accessCodes.add("M40062023");
        accessCodes.add("M27821888");
        accessCodes.add("HLWZX51620210829508");
        accessCodes.add("苏州CTVPN19429A");
        accessCodes.add("南京徐州ETN0413NP");
        accessCodes.add("M23516423");
        accessCodes.add("M54977966");
        accessCodes.add("M07028415");
        accessCodes.add("M40053722");
        accessCodes.add("M40053013");
        accessCodes.add("M40048754");
        accessCodes.add("M29006892");
        accessCodes.add("M07241131");
        accessCodes.add("IPRANJS1002440305");
        accessCodes.add("M51153494");
        accessCodes.add("M17541387");
        accessCodes.add("M07020962");
        accessCodes.add("M40053058");
        accessCodes.add("M48562706");
        accessCodes.add("M55827967");
        accessCodes.add("MSTPJS1002901299");
        accessCodes.add("M00293204");
        accessCodes.add("M00120723");
        accessCodes.add("M00231404");
        accessCodes.add("M40052601");
        accessCodes.add("MSTPJT1002944293");
        accessCodes.add("M13301168");
        accessCodes.add("13849548");
        accessCodes.add("M32411887");
        accessCodes.add("MSTPJT1001798080");
        accessCodes.add("M00333901");
        accessCodes.add("M00190635");
        accessCodes.add("M48747956");
        accessCodes.add("10570479");
        accessCodes.add("X00012562NJ571");
        accessCodes.add("M40061352");
        accessCodes.add("M00298124");
        accessCodes.add("M24251401");
        accessCodes.add("M43392682");
        accessCodes.add("M08866174");
        accessCodes.add("M40054542");
        accessCodes.add("M00222179");
        accessCodes.add("154317479");
        accessCodes.add("M00314962");
        accessCodes.add("M10346181");
        accessCodes.add("M40052119");
        accessCodes.add("M23276381");
        accessCodes.add("MSTPJS1002770538");
        accessCodes.add("M40059216");
        accessCodes.add("泰州CTVPN189B007");
        accessCodes.add("M40052196");
        accessCodes.add("M43607956");
        accessCodes.add("M00199837");
        accessCodes.add("MSTPJS1002007715");
        accessCodes.add("MSTPJS1003061828");
        accessCodes.add("M28051913");
        accessCodes.add("M54147973");
        accessCodes.add("M40052571");
        accessCodes.add("MSTPJS1002872263");
        accessCodes.add("M00311868");
        accessCodes.add("M40058178");
        accessCodes.add("M40053079");
        accessCodes.add("M00298180");
        accessCodes.add("MSTPJS1002887098");
        accessCodes.add("M00231378");
        accessCodes.add("M15206234");
        accessCodes.add("M08846222");
        accessCodes.add("M07248576");
        accessCodes.add("MSTPJS1002162468");
        accessCodes.add("MSTPJS1003059979");
        accessCodes.add("10869327");
        accessCodes.add("MSTPJS1003206446");
        accessCodes.add("M00207999");
        accessCodes.add("M00183284");
        accessCodes.add("MSTPJS1002287898");
        accessCodes.add("M48132701");
        accessCodes.add("HLWZX51820220742159");
        accessCodes.add("M42657962");
        accessCodes.add("M60467966");
        accessCodes.add("MSTPJS1003167446");
        accessCodes.add("M40052877");
        accessCodes.add("13643536");
        accessCodes.add("M40053394");
        accessCodes.add("MSTPJS1002713982");
        accessCodes.add("M23516410");
        accessCodes.add("Z11427");
        accessCodes.add("MSTPJS1002059107");
        accessCodes.add("M08851177");
        accessCodes.add("12264757");
        accessCodes.add("M00315024");
        accessCodes.add("M23451378");
        accessCodes.add("MSTPJS1003225613");
        accessCodes.add("M47712978");
        accessCodes.add("M00222555");
        accessCodes.add("M00274474");
        accessCodes.add("M10176204");
        accessCodes.add("M00338304");
        accessCodes.add("HLWZX51420210936481");
        accessCodes.add("M00267992");
        accessCodes.add("M40051337");
        accessCodes.add("M00306781");
        accessCodes.add("M00275731");
        accessCodes.add("MSTPJS1002870243");
        accessCodes.add("M00259731");
        accessCodes.add("M40051567");
        accessCodes.add("M40002038");
        accessCodes.add("154318075");
        accessCodes.add("MSTPJS1002072192");
        accessCodes.add("M00263760");
        accessCodes.add("154318308");
        accessCodes.add("MSTPJS1003083948");
        accessCodes.add("M25201389");
        accessCodes.add("M21646376");
        accessCodes.add("M40063854");
        accessCodes.add("M00247206");
        accessCodes.add("M00230053");
        accessCodes.add("MSTPJS1002942029");
        accessCodes.add("M21516376");
        accessCodes.add("M00113250");
        accessCodes.add("M53777966");
        accessCodes.add("M41687959");
        accessCodes.add("M14976234");
        accessCodes.add("M00231400");
        accessCodes.add("M26521890");
        accessCodes.add("M49602968");
        accessCodes.add("M00285612");
        accessCodes.add("160807262");
        accessCodes.add("12265436");
        accessCodes.add("M31606890");
        accessCodes.add("M40052871");
        accessCodes.add("M48468479");
        accessCodes.add("M00183261");
        accessCodes.add("M30231890");
        accessCodes.add("M53792969");
        accessCodes.add("M40052255");
        accessCodes.add("MSTPJS1001853390");
        accessCodes.add("M26901888");
        accessCodes.add("15958959");
        accessCodes.add("M47712706");
        accessCodes.add("MSTPJS1002083033");
        accessCodes.add("M40052577");
        accessCodes.add("MSTPJS1001991579");
        accessCodes.add("M50361932");
        accessCodes.add("MSTPJS1003205089");
        accessCodes.add("M00281490");
        accessCodes.add("M07268843");
        accessCodes.add("M00296365");
        accessCodes.add("M00215114");
        accessCodes.add("M00211730");
        accessCodes.add("MSTPBJ1003309058");
        accessCodes.add("MSTPJS1003191762");
        accessCodes.add("28034795");
        accessCodes.add("M58972970");
        accessCodes.add("M00215132");
        accessCodes.add("M20286379");
        accessCodes.add("MSTPJS1003219662");
        accessCodes.add("M00262813");
        accessCodes.add("MSTPJS1002662130");
        accessCodes.add("11643627");
        accessCodes.add("M00300474");
        accessCodes.add("M56077964");
        accessCodes.add("X00012562NJ567");
        accessCodes.add("MSTPJS1003301637");
        accessCodes.add("M24376376");
        accessCodes.add("MSTPJS1002935606");
        accessCodes.add("154317407");
        accessCodes.add("M18051375");
        accessCodes.add("M10171171");
        accessCodes.add("M00314863");
        accessCodes.add("M46177963");
        accessCodes.add("M40062430");
        accessCodes.add("154317909");
        accessCodes.add("M48372703");
        accessCodes.add("110825603");
        accessCodes.add("154318077");
        accessCodes.add("154317919");
        accessCodes.add("M00280143");
        accessCodes.add("M43602965");
        accessCodes.add("14800894");
        accessCodes.add("M25136388");
        accessCodes.add("M00005658");
        accessCodes.add("M40058455");
        accessCodes.add("11840022");
        accessCodes.add("M00259365");
        accessCodes.add("M40052991");
        accessCodes.add("M42437965");
        accessCodes.add("M00297024");
        accessCodes.add("M42917959");
        accessCodes.add("15958971");
        accessCodes.add("M41257679");
        accessCodes.add("M44462962");
        accessCodes.add("M46157708");
        accessCodes.add("M52697963");
        accessCodes.add("M08866165");
        accessCodes.add("M18661393");
        accessCodes.add("MSTPJS1002291104");
        accessCodes.add("HLWZX51220210326959");
        accessCodes.add("MSTPJS1003227291");
        accessCodes.add("M23516388");
        accessCodes.add("M00231394");
        accessCodes.add("M07034572");
        accessCodes.add("15958931");
        accessCodes.add("12265466");
        accessCodes.add("M40052749");
        accessCodes.add("MSTPJS1002378778");
        accessCodes.add("M00247053");
        accessCodes.add("M17061363");
        accessCodes.add("M52753994");
        accessCodes.add("M40487959");
        accessCodes.add("M10171165");
        accessCodes.add("M00115222");
        accessCodes.add("M28696933");
        accessCodes.add("MSTPJT1001796060");
        accessCodes.add("HLWZX51020211054807");
        accessCodes.add("M51333496");
        accessCodes.add("M43602968");
        accessCodes.add("M60232964");
        accessCodes.add("MSTPJS1003230540");
        accessCodes.add("M48478476");
        accessCodes.add("M41677965");
        accessCodes.add("MSTPJS1002058048");
        accessCodes.add("M08861177");
        accessCodes.add("M40052977");
        accessCodes.add("M20681375");
        accessCodes.add("MSTPJT1001800105");
        accessCodes.add("M07304326");
        accessCodes.add("M22036380");
        accessCodes.add("L1003971");
        accessCodes.add("M19206379");
        accessCodes.add("147785335");
        accessCodes.add("M00003455");
        accessCodes.add("M00247595");
        accessCodes.add("154318310");
        accessCodes.add("M40001997");
        accessCodes.add("M00004415");
        accessCodes.add("M00222476");
        accessCodes.add("MSTPJS1003262320");
        accessCodes.add("MSTPJS1003009102");
        accessCodes.add("M22421387");
        accessCodes.add("M00210301");
        accessCodes.add("18STN010324");
        accessCodes.add("HLWZX025202307169529");
        accessCodes.add("MSTPJS1003121486");
        accessCodes.add("M00290808");
        accessCodes.add("M46157717");
        accessCodes.add("M00281928");
        accessCodes.add("M07007469");
        accessCodes.add("M22316431");
        accessCodes.add("M61127964");
        accessCodes.add("M00113189");
        accessCodes.add("M00183249");
        accessCodes.add("M48542709");
        accessCodes.add("MSTPJS1001661183");
        accessCodes.add("MSTPJS1002296370");
        accessCodes.add("M00263339");
        accessCodes.add("M00226297");
        accessCodes.add("154318088");
        accessCodes.add("M31431888");
        accessCodes.add("M07264644");
        accessCodes.add("M62853166");
        accessCodes.add("15959654");
        accessCodes.add("M00309125");
        accessCodes.add("M48477702");
        accessCodes.add("M00158241");
        accessCodes.add("MSTPJS1002823101");
        accessCodes.add("M00292201");
        accessCodes.add("M08851183");
        accessCodes.add("M43082965");
        accessCodes.add("M40058350");
        accessCodes.add("MSTPJS1001995242");
        accessCodes.add("M17806372");
        accessCodes.add("M00104726");
        accessCodes.add("M40052874");
        accessCodes.add("M42407956");
        accessCodes.add("M31436888");
        accessCodes.add("M46157709");
        accessCodes.add("14448181");
        accessCodes.add("M00183318");
        accessCodes.add("MSTPJS1002526109");
        accessCodes.add("M00306948");
        accessCodes.add("M00297567");
        accessCodes.add("M00278546");
        accessCodes.add("HLWZX025202309176024");
        accessCodes.add("15959575");
        accessCodes.add("MSTPJS1002868623");
        accessCodes.add("M00265368");
        accessCodes.add("MSTPJS1001567243");
        accessCodes.add("DG07448");
        accessCodes.add("M00110881");
        accessCodes.add("M56872966");
        accessCodes.add("12264825");
        accessCodes.add("M48542707");
        accessCodes.add("M00262242");
        accessCodes.add("1010039");
        accessCodes.add("MSTPJS1002148499");
        accessCodes.add("M08846189");
        accessCodes.add("117212471");
        accessCodes.add("M57352964");
        accessCodes.add("M40047858");
        accessCodes.add("M07004777");
        accessCodes.add("M30311888");
        accessCodes.add("M24806382");
        accessCodes.add("M08851180");
        accessCodes.add("M00183379");
        accessCodes.add("M17696372");
        accessCodes.add("12265092");
        accessCodes.add("M48602706");
        accessCodes.add("M11836159");
        accessCodes.add("HLWZX523202311105513");
        accessCodes.add("M40054825");
        accessCodes.add("M00291741");
        accessCodes.add("MSTPJS1002444250");
        accessCodes.add("MSTPJS1002809404");
        accessCodes.add("M64223166");
        accessCodes.add("M40052883");
        accessCodes.add("MSTPJS1001342049");
        accessCodes.add("M00115331");
        accessCodes.add("M00249151");
        accessCodes.add("M00232858");
        accessCodes.add("MSTPJS1003205120");
        accessCodes.add("M40049767");
        accessCodes.add("M00204176");
        accessCodes.add("M40047825");
        accessCodes.add("154317905");
        accessCodes.add("M00298119");
        accessCodes.add("M00113318");
        accessCodes.add("MSTPJS1003173435");
        accessCodes.add("M00220083");
        accessCodes.add("M40052156");
        accessCodes.add("154317493");
        accessCodes.add("M48557714");
        accessCodes.add("M00290058");
        accessCodes.add("M00005652");
        accessCodes.add("M42957956");
        accessCodes.add("M40051316");
        accessCodes.add("M54107973");
        accessCodes.add("M00145301");
        accessCodes.add("HLWZX510202208108804");
        accessCodes.add("MSTPJS1002932882");
        accessCodes.add("MSTPJS1001601214");
        accessCodes.add("M00280598");
        accessCodes.add("MSTPJS1003103754");
        accessCodes.add("MSTPJS1002006978");
        accessCodes.add("M00285592");
        accessCodes.add("M00174812");
        accessCodes.add("M40142673");
        accessCodes.add("HLWZX025202308170632");
        accessCodes.add("M00113142");
        accessCodes.add("M50562970");
        accessCodes.add("M43442965");
        accessCodes.add("M40053070");
        accessCodes.add("M40051530");
        accessCodes.add("M61092964");
        accessCodes.add("M00217064");
        accessCodes.add("M43921919");
        accessCodes.add("M27336892");
        accessCodes.add("M52282963");
        accessCodes.add("M00306603");
        accessCodes.add("M12941163");
        accessCodes.add("MSTPJS1003119176");
        accessCodes.add("HLWZX510202303151001");
        accessCodes.add("M16566357");
        accessCodes.add("M52026920");
        accessCodes.add("M40057973");
        accessCodes.add("MSTPJS1003190816");
        accessCodes.add("M40008006");
        accessCodes.add("M00221948");
        accessCodes.add("M00283653");
        accessCodes.add("M45007956");
        accessCodes.add("M00275671");
        accessCodes.add("M00002239");
        accessCodes.add("M60012964");
        accessCodes.add("M40051451");
        accessCodes.add("M19816384");
        accessCodes.add("M42527958");
        accessCodes.add("南京连云港ETN0328NP");
        accessCodes.add("M40051307");
        accessCodes.add("M00280268");
        accessCodes.add("M43592994");
        accessCodes.add("M00350283");
        accessCodes.add("M18096384");
        accessCodes.add("M00273299");
        accessCodes.add("M00224018");
        accessCodes.add("M40058106");
        accessCodes.add("M00304884");
        accessCodes.add("M40052700");
        accessCodes.add("M00224281");
        accessCodes.add("18STN010530");
        accessCodes.add("154317900");
        accessCodes.add("MSTPZJ1002809526");
        accessCodes.add("M23596380");
        accessCodes.add("M21731382");
        accessCodes.add("M00113529");
        accessCodes.add("M20216387");
        accessCodes.add("M24126397");
        accessCodes.add("M00115225");
        accessCodes.add("JS19cy000942");
        accessCodes.add("M46157719");
        accessCodes.add("M40052706");
        accessCodes.add("19611809");
        accessCodes.add("M45802956");
        accessCodes.add("M16531360");
        accessCodes.add("M40047926");
        accessCodes.add("M46157718");
        accessCodes.add("M00111924");
        accessCodes.add("M22316432");
        accessCodes.add("M53252963");
        accessCodes.add("M47067959");
        accessCodes.add("MSTPJS1003123666");
        accessCodes.add("M17541393");
        accessCodes.add("M55392968");
        accessCodes.add("M00225025");
        accessCodes.add("M00181389");
        accessCodes.add("MSTPJS1002658806");
        accessCodes.add("M08851204");
        accessCodes.add("M00299844");
        accessCodes.add("MSTPJS1002415329");
        accessCodes.add("M00263696");
        accessCodes.add("MSTPJS1002297305");
        accessCodes.add("M40262694");
        accessCodes.add("12264577");
        accessCodes.add("MSTPJS1001983694");
        accessCodes.add("HLWZX51520221283120");
        accessCodes.add("MSTPJS1003022759");
        accessCodes.add("M45977701");
        accessCodes.add("M15681243");
        accessCodes.add("M44182679");
        accessCodes.add("M00344785");
        accessCodes.add("M40053022");
        accessCodes.add("M12071165");
        accessCodes.add("M40052125");
        accessCodes.add("M19576385");
        accessCodes.add("M40055247");
        accessCodes.add("M00231422");
        accessCodes.add("HLWZX515202404124975");
        accessCodes.add("MSTPJT1001797090");
        accessCodes.add("M10336174");
        accessCodes.add("M00231428");
        accessCodes.add("M40003255");
        accessCodes.add("M00002945");
        accessCodes.add("154317913");
        accessCodes.add("M64658164");
        accessCodes.add("M00314501");
        accessCodes.add("MSTPJS1001997037");
        accessCodes.add("M43592997");
        accessCodes.add("M00314964");
        accessCodes.add("MSTPJS1002256084");
        accessCodes.add("M40051304");
        accessCodes.add("M12736178");
        accessCodes.add("M00283189");
        accessCodes.add("M00303988");
        accessCodes.add("M43677956");
        accessCodes.add("MSTPJS1001540577");
        accessCodes.add("M23546391");
        accessCodes.add("K03164");
        accessCodes.add("M14981243");
        accessCodes.add("M53602963");
        accessCodes.add("MSTPJT1001982780");
        accessCodes.add("MSTPJS1001664268");
        accessCodes.add("MSTPJS1002162023");
        accessCodes.add("HLWZX02520210954325");
        accessCodes.add("HLWZX51420220778802");
        accessCodes.add("M65358166");
        accessCodes.add("M22316437");
        accessCodes.add("M00271711");
        accessCodes.add("M14956243");
        accessCodes.add("MSTPJS1003226580");
        accessCodes.add("M19556378");
        accessCodes.add("MSTPJS1003242810");
        accessCodes.add("M08851186");
        accessCodes.add("HLWZX51520221283122");
        accessCodes.add("M40051871");
        accessCodes.add("M00280308");
        accessCodes.add("M40053367");
        accessCodes.add("MSTPJT1001697406");
        accessCodes.add("M00233191");
        accessCodes.add("M07253567");
        accessCodes.add("M47712956");
        accessCodes.add("M15631246");
        accessCodes.add("M50076920");
        accessCodes.add("M49933003");
        accessCodes.add("M25066393");
        accessCodes.add("M15436252");
        accessCodes.add("X00012562NJ570");
        accessCodes.add("MSTPJS1002436055");
        accessCodes.add("HLWZX025202206104563");
        accessCodes.add("M40069893");
        accessCodes.add("M00145501");
        accessCodes.add("M00280066");
        accessCodes.add("M40047929");
        accessCodes.add("M18626372");
        accessCodes.add("北京苏州ZQOTN00029");
        accessCodes.add("M45822957");
        accessCodes.add("M08851222");
        accessCodes.add("M00272491");
        accessCodes.add("M00354243");
        accessCodes.add("13342599");
        accessCodes.add("10606343");
        accessCodes.add("M40060063");
        accessCodes.add("M27981893");
        accessCodes.add("MSTPJS1003037470");
        accessCodes.add("MSTPJS1003334070");
        accessCodes.add("M00183289");
        accessCodes.add("M00327809");
        accessCodes.add("HLWZX523202308101911");
        accessCodes.add("MSTPJS1003040849");
        accessCodes.add("M40056462");
        accessCodes.add("M46377957");
        accessCodes.add("M23186391");
        accessCodes.add("M22041385");
        accessCodes.add("M00111923");
        accessCodes.add("M47987708");
        accessCodes.add("M00111209");
        accessCodes.add("M16311393");
        accessCodes.add("1127079704");
        accessCodes.add("M19406441");
        accessCodes.add("Z12243");
        accessCodes.add("MSTPJT1001800068");
        accessCodes.add("M52002963");
        accessCodes.add("M00155127");
        accessCodes.add("M14411231");
        accessCodes.add("M43602974");
        accessCodes.add("M18806375");
        accessCodes.add("M08866213");
        accessCodes.add("M51127963");
        accessCodes.add("M40054176");
        accessCodes.add("M00314951");
        accessCodes.add("M22316435");
        accessCodes.add("M40049785");
        accessCodes.add("MSTPJS1002947837");
        accessCodes.add("M44463010");
        accessCodes.add("M00005643");
        accessCodes.add("M40052237");
        accessCodes.add("M00152536");
        accessCodes.add("MSTPBJ1001979007");
        accessCodes.add("M40054545");
        accessCodes.add("M40003302");
        accessCodes.add("M50901921");
        accessCodes.add("M00338212");
        accessCodes.add("M10686162");
        accessCodes.add("M40047796");
        accessCodes.add("M40052202");
        accessCodes.add("M00286132");
        accessCodes.add("M20106384");
        accessCodes.add("M55932974");
        accessCodes.add("MSTPJS1002900178");
        accessCodes.add("MSTPJS1002964550");
        accessCodes.add("M32076890");
        accessCodes.add("MSTPJS1002127171");
        accessCodes.add("M40065336");
        accessCodes.add("M00308661");
        accessCodes.add("23956234");
        accessCodes.add("M14786231");
        accessCodes.add("M52652966");
        accessCodes.add("M40047864");
        accessCodes.add("MSTPJS1001355210");
        accessCodes.add("MSTPJS1003198575");
        accessCodes.add("MSTPJS1002894110");
        accessCodes.add("MSTPJS1003019662");
        accessCodes.add("M00236091");
        accessCodes.add("MSTPJS1002641323");
        accessCodes.add("M21876377");
        accessCodes.add("M08851228");
        accessCodes.add("M55482968");
        accessCodes.add("M43602977");
        accessCodes.add("M29941890");
        accessCodes.add("M40917959");
        accessCodes.add("M24796384");
        accessCodes.add("M40052153");
        accessCodes.add("M41321919");
        accessCodes.add("140246443");
        accessCodes.add("M17776375");
        accessCodes.add("MSTPJS1002313118");
        accessCodes.add("M00113268");
        accessCodes.add("M17696375");
        accessCodes.add("X00003154NJ039");
        accessCodes.add("M13141159");
        accessCodes.add("MSTPJT1003263261");
        accessCodes.add("M64528165");
        accessCodes.add("MSTPJS1002331081");
        accessCodes.add("M40054887");
        accessCodes.add("M00113280");
        accessCodes.add("M08556162");
        accessCodes.add("M00222801");
        accessCodes.add("M55512964");
        accessCodes.add("M40052225");
        accessCodes.add("12379448");
        accessCodes.add("M24376379");
        accessCodes.add("MSTPHB1003191453");
        accessCodes.add("M24881376");
        accessCodes.add("M17456372");
        accessCodes.add("12742110");
        accessCodes.add("M00276959");
        accessCodes.add("M47147959");
        accessCodes.add("M50592963");
        accessCodes.add("M26646889");
        accessCodes.add("M52942963");
        accessCodes.add("M44462968");
        accessCodes.add("M10341207");
        accessCodes.add("M00216523");
        accessCodes.add("M51287974");
        accessCodes.add("M00296168");
        accessCodes.add("M61402966");
        accessCodes.add("13STN007971");
        accessCodes.add("115306111");
        accessCodes.add("M00247080");
        accessCodes.add("M10176201");
        accessCodes.add("M40055322");
        accessCodes.add("M40040402");
        accessCodes.add("M15436255");
        accessCodes.add("M40123157");
        accessCodes.add("M10611174");
        accessCodes.add("MSTPJS1001741358");
        accessCodes.add("M00218916");
        accessCodes.add("M00292731");
        accessCodes.add("M00120464");
        accessCodes.add("M40055107");
        accessCodes.add("Z1229");
        accessCodes.add("M14636231");
        accessCodes.add("M00268004");

        accessCodes.add("园区建行-昆山市新镇镇奣子路/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3226NP");
        accessCodes.add("姜堰姜堰STN000044");
        accessCodes.add("141118472");
        accessCodes.add("园区建行-建设工程交易中心(M45557960)/ETN0001NP");
        accessCodes.add("建行泰州总行-姜堰财政局/30N0001NP");
        accessCodes.add("省建行-建行梦都大街176号/ETN0001NP");
        accessCodes.add("省建行-建行泰山路支行/ETN0001NP");
        accessCodes.add("南京扬州ETN0402NP");
        accessCodes.add("中信银行-中信银行上海汽车商用车公司ATM机/ETN0001NP");
        accessCodes.add("M15831294");
        accessCodes.add("M07253567A-M07253567Z");
        accessCodes.add("M07272766");
        accessCodes.add("12450072");
        accessCodes.add("南京泰州ETN0587NP");
        accessCodes.add("M00231428A-M00231428Z");
        accessCodes.add("省建设银行-苏豪控股江东中路399号/ETN0001NP");
        accessCodes.add("园区建行-常熟智慧机房/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3272NP");
        accessCodes.add("省建行-建设银行中和路111号/ETN0001NP");
        accessCodes.add("中信银行-昆山不动产交易中心/ETN0001NP");
        accessCodes.add("南京楚州ETN0002NP");
        accessCodes.add("建行-建行金坛吾悦广场/ETN0001NP");
        accessCodes.add("中信银行-无锡南外国王国际学校/ETN0001NP");
        accessCodes.add("中信银行-太仓县府东街中信银行/ETN0001NP");
        accessCodes.add("中信银行-六合中信银行江北支行/30N0001NP");
        accessCodes.add("中信银行-苏州大道东中信银行/ETN0001NP");
        accessCodes.add("M00113142A-M00113142Z");
        accessCodes.add("南京镇江ETN6033NPH");
        accessCodes.add("市公积金管理中心-省建行/ETN0001NP");
        accessCodes.add("建设银行-建设银行神农路1号/ETN0001NP");
        accessCodes.add("SZETN3812NP(建行-港区港务分理处M40050990)10M");
        accessCodes.add("常州人民银行-中信银行同城交换/ETN0001NP");
        accessCodes.add("南京盐城ETN6115NPH");
        accessCodes.add("M07036504A-M07036504Z");
        accessCodes.add("省建设-江宁南京金斯瑞生物科技/ETN0001PG");
        accessCodes.add("M00003627A-M00003627Z");
        accessCodes.add("园区建行-吴江亨通集团有限公司新大楼(M48742959)/ETN0001NP");
        accessCodes.add("省建行-建行樱驼花园分理处/ETN0001NP");
        accessCodes.add("建设银行-建行常工院秋白楼/ETN0001NP");
        accessCodes.add("政企网关（LAN）物理设备-B0D9BC8D-PC-0002528335");
        accessCodes.add("省建行-省建行黄埔路分理处/ETN0001NP");
        accessCodes.add("园区建行-昆山住建局(M42722980)2M");
        accessCodes.add("苏州-张家港/ETN3830NP");
        accessCodes.add("省建设银行洪武路188号-水利科学研究院广州路225号/ETN0001NP");
        accessCodes.add("中信银行-新城总部大厦/ETN0001NP");
        accessCodes.add("M07008686A-M07008686Z");
        accessCodes.add("中信银行-昆山支行(M43612962)/ETN0001NP");
        accessCodes.add("K01415");
        accessCodes.add("南京连云港ETN0526NP");
        accessCodes.add("北京连云港ETN2003NP");
        accessCodes.add("建设银行-建设银行东山街道竹山路565号/ETN0001NP");
        accessCodes.add("中信银行泰州总行-中信银行高港支行/ETN0002NP");
        accessCodes.add("中国建设银行江都市支行-中国建设银行江都市支行(M40144139)/N001");
        accessCodes.add("省建行-建设银行迈尧路89号/ETN0001NP");
        accessCodes.add("省建行-建设银行江心洲街道贤坤路60号/ETN0001NP");
        accessCodes.add("12265030");
        accessCodes.add("F6211834b");
        accessCodes.add("M00231391A-M00231391Z");
        accessCodes.add("中信银行-张家港国泰大厦/ETN0001NP");
        accessCodes.add("中信银行-中信梅村支行/ETN0001NP");
        accessCodes.add("南京苏州ETN0193NP");
        accessCodes.add("HLWZX510202302142304");
        accessCodes.add("SCETN4758NP(建行-衡山路分理处M40051896)");
        accessCodes.add("市政府数据资源中心-建行高邮盂城支行/ETN0001NP");
        accessCodes.add("省建行-中国建设银行文澜路99号/ETN0001NP");
        accessCodes.add("园区建行-创元集团财务/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3782NP");
        accessCodes.add("省建行-中国建设银行下曹村100号/ETN0001NP");
        accessCodes.add("市公积金-涟水高沟行政大厅ETN0001NP");
        accessCodes.add("广元连云港ETN0002NP");
        accessCodes.add("园区建行-吴江盛泽西环路丝绸广场(M40065427)10M");
        accessCodes.add("省建设银行大楼-凤凰国际广场/ETN0001NP");
        accessCodes.add("仪征建行城北支行-仪征不动产中心/ETN0001NP");
        accessCodes.add("扬州扬州ETN6187NPH");
        accessCodes.add("园区建行-建行黄埭分理处/ETN0001NP");
        accessCodes.add("数字电路用户终端（以太网电口）-8DD6EF2C-诚信大道519号中科曙光南京研究院-OTS-E6080P-2");
        accessCodes.add("上海南京ETN0575NP");
        accessCodes.add("园区建行-昆山皇冠假日酒店/ETN0001NP");
        accessCodes.add("园区建行-昆山金陵大饭店/ETN0001NP");
        accessCodes.add("园区建行-交通局/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3216NP");
        accessCodes.add("南京泰州ETN0608NP");
        accessCodes.add("HLWZX52720220638384");
        accessCodes.add("南通中信银行-如东中信银行/30N0001NP");
        accessCodes.add("南京徐州ETN0710NP");
        accessCodes.add("省建行-市公积金中心/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3990NP");
        accessCodes.add("中信银行-中信银行金门支行(M43592979)/ETN0001NP");
        accessCodes.add("M07261624");
        accessCodes.add("建设银行-建设银行黄埔路3号/ETN0001NP");
        accessCodes.add("江苏省建设银行-建设银行丰富路支行七家湾/ETN0001NP");
        accessCodes.add("南京徐州ETN6027NPH");
        accessCodes.add("M00002822A-M00002822Z");
        accessCodes.add("省烟草联通新时空大厦-省建行/ETN0001NP");
        accessCodes.add("省建行-江苏银联商务/ETN0001NP");
        accessCodes.add("省建行-主机楼6楼/ETN0001NP");
        accessCodes.add("省建行-天元东路支行/ETN0001NP");
        accessCodes.add("M00222056");
        accessCodes.add("南通建行-南通建行南通大学/ETN0001NP");
        accessCodes.add("140246444");
        accessCodes.add("南京扬州ETN0408NP");
        accessCodes.add("南京徐州ETN6023NPH");
        accessCodes.add("附二院-中核医疗产业管理(M47332975)/ETN0001NP");
        accessCodes.add("中信银行-吴江华衍水务(M40702971)2M");
        accessCodes.add("MSTPJS1002716975");
        accessCodes.add("中信银行-中信银行国慧商务广场/ETN0001NP");
        accessCodes.add("省建行-建设银行科学园新亭西路79号/ETN0001NP");
        accessCodes.add("园区建行-常熟尚湖支行(M40060913)2M");
        accessCodes.add("南京宿迁ETN0437NP");
        accessCodes.add("省建行-长江油运/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN4013NP");
        accessCodes.add("苏州-昆山/ETN3981NP");
        accessCodes.add("省建行-建设银行中材国际自助区/ETN0001NP");
        accessCodes.add("园区建行-建行科技城医院ATM(M45802956)/ETN0001NP");
        accessCodes.add("省石油-省建行/ETN0001NP");
        accessCodes.add("南京泰州ETN0585NP");
        accessCodes.add("省建行-建行亚东城支行/ETN0001NP");
        accessCodes.add("南京宿迁ETN0426NP");
        accessCodes.add("中信银行-学士路自助/ETN0002NP");
        accessCodes.add("园区建行-望亭大街118号A1幢1楼建设银行/ETN0001NP");
        accessCodes.add("园区建行-相城区财政局(M52002963)2M/ETN0001NP");
        accessCodes.add("苏州-吴江/ETN0002NP");
        accessCodes.add("园区建行-张家港永馨园爱心超市/ETN0001NP");
        accessCodes.add("建行泰州总行-建行兴化不动产ATM/ETN0001NP");
        accessCodes.add("南京南通ETN0140NP");
        accessCodes.add("园区建行-建行何山分理处(M40052562)/ETN0001NP");
        accessCodes.add("M00225025A-CZ.JIANPU01/HAC211");
        accessCodes.add("苏州上饶ETN6001NPH");
        accessCodes.add("中信银行-昆山高新区支行(M43607962)/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3982NP");
        accessCodes.add("中信银行-中信银行西交利物浦ATM(M40042244)2M");
        accessCodes.add("建行总部-城乡建设信息中心/ETN0001NP");
        accessCodes.add("中信银行-行政服务中心/ETN0001NP");
        accessCodes.add("市纪委-中信银行/ETN0001NP");
        accessCodes.add("北京连云港ZQOTN00002");
        accessCodes.add("园区建行-常熟职教中心/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN4108NP");
        accessCodes.add("中信银行-贝壳新锡签约中心(逸仙教培产业园)/ETN0001NP");
        accessCodes.add("M00102487");
        accessCodes.add("中信银行-园区公积金管理中心汇金大厦/ETN0001NP");
        accessCodes.add("M00117082A-M00117082Z");
        accessCodes.add("中信银行-苏州燃气(M42527958)2M");
        accessCodes.add("M00231422A-M00231422Z");
        accessCodes.add("M00113280A-M00113280Z");
        accessCodes.add("南通自来水公司-南通中信银行/ETN0001NP");
        accessCodes.add("园区建行-建行木渎支行(M40053079)/ETN0001NP");
        accessCodes.add("12265342");
        accessCodes.add("常熟机要局-建设银行(M48747956)10M");
        accessCodes.add("154317481");
        accessCodes.add("园区建行-常熟建业路8号建设银行(M65708163)/ETN0001NP");
        accessCodes.add("145098793");
        accessCodes.add("园区建行-建行胥江支行(M40052595)/ETN0001NP");
        accessCodes.add("省建行-建行河滨分理处/ETN0001NP");
        accessCodes.add("中信银行泰州总行-中信银行青年路支行/ETN0001NP");
        accessCodes.add("HLWZX512202307205625");
        accessCodes.add("园区建行-吴中商务中心(M51197963)/ETN0001NP");
        accessCodes.add("12456958");
        accessCodes.add("园区建行-建行新市路支行(M40052690)/ETN0001NP");
        accessCodes.add("省建行-建行新门诊大楼5层核心交换机房(/ETN0001NP");
        accessCodes.add("省建行-建设银行湖滨路商业街/ETN0001NP");
        accessCodes.add("公积金-建行琼花支行/ETN0001NP");
        accessCodes.add("中信银行-威孚高科技集团公司(外联)/ETN0001NP");
        accessCodes.add("建行健康支行-会展中心公积金ETN0001NP");
        accessCodes.add("南京泰州ETN0392NP");
        accessCodes.add("中信银行-中信江都支行/ETN0001NP");
        accessCodes.add("省建行-省联合征信江东中路49号/ETN0002NP");
        accessCodes.add("中信银行徐州分行-中和大厦产权处/ETN0001NP");
        accessCodes.add("建行-邗江公积金自助/ETN0001NP");
        accessCodes.add("HLWZX51820230674644");
        accessCodes.add("中信银行-港华燃气/30N0001NP-M07028415Z");
        accessCodes.add("园区建行-昆山时代大厦建行/ETN0001NP");
        accessCodes.add("省建行-建行御道街分理处/ETN0001NP");
        accessCodes.add("公积金-中信银行/ETN0002NP");
        accessCodes.add("泰州泰州STN000310");
        accessCodes.add("南京盐城ETN6105NPH");
        accessCodes.add("省建行-建行江宁路储蓄所/ETN0001NP");
        accessCodes.add("建行-龙河电信大楼5层机房/ETN0001NP");
        accessCodes.add("M00115331A-M00115331Z");
        accessCodes.add("北京途牛国际旅行社-省建行/ETN0001NP");
        accessCodes.add("南京常州ETN6057NPH");
        accessCodes.add("市政府数据资源中心-建行江都支行/ETN0001NP");
        accessCodes.add("园区建行-园区公积金管理中心(M40013373)50M");
        accessCodes.add("HLWZX523202309103079");
        accessCodes.add("苏州-常熟/ETN4801NP");
        accessCodes.add("中信银行-昆山正新橡胶ATM(M40917959)2M");
        accessCodes.add("南京泰州ETN0583NP");
        accessCodes.add("南通中信银行-启东交投大数据/ETN0001NP");
        accessCodes.add("M00215081A-M00215081Z");
        accessCodes.add("园区建行-海棠街18号210IDC机房(M49542967)10M");
        accessCodes.add("园区建行-吴江盛泽舜湖西路(M40048476)10M");
        accessCodes.add("15959493");
        accessCodes.add("省建行-建行中山东路305号军区总院外科楼16层信息科/ETN0001NP");
        accessCodes.add("M00008555A-M00008555Z");
        accessCodes.add("23956229");
        accessCodes.add("省建行-建设银行溧水高级中学1层机房/ETN0001NP");
        accessCodes.add("园区建行-建行渭西村ATM(M40058280)2M");
        accessCodes.add("省建行-建设银行花港路6号/ETN0001NP");
        accessCodes.add("154317902");
        accessCodes.add("M00113288A-M00113288Z");
        accessCodes.add("镇江市建设银行-丹阳社保/ETN0001NP-");
        accessCodes.add("省建行-通贤街41号建设银行/ETN0001NP");
        accessCodes.add("19342020");
        accessCodes.add("南京泰州ETN0599NP");
        accessCodes.add("南京徐州ETN6051NPH");
        accessCodes.add("省建行-众彩物流美食广场12幢/ETN0001NP");
        accessCodes.add("中信银行-吴江大厦B幢7楼/ETN0001NP");
        accessCodes.add("省建行-建设银行北京东路73号/ETN0001NP");
        accessCodes.add("中信银行-天都大厦商业广场/ETN0001NP");
        accessCodes.add("南京泰州ETN0631NP");
        accessCodes.add("园区建行(CYQJH)-建行天都支行/ETN0001NP");
        accessCodes.add("银监局新址-中信银行/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN4010NP");
        accessCodes.add("宿迁市建设银行-电信长线分局IDC机房D1机柜托管服务器/ETN0001NP");
        accessCodes.add("M07000614A-M07000614Z");
        accessCodes.add("省建行-南京工业大学浦江学院办公楼三楼机房/30N0001NP");
        accessCodes.add("省建行-建设银行未来科技城经济发展有限公司/ETN0001NP");
        accessCodes.add("中信银行-中信银行信用卡中心/ETN0001NP");
        accessCodes.add("市建行-八二医院建行一楼ETN0001NP");
        accessCodes.add("中信银行-中信邗江支行/ETN0001NP");
        accessCodes.add("南京泰州ETN6055NPH");
        accessCodes.add("中信银行-新市政府国土局ETN0001NP");
        accessCodes.add("市政府数据资源中心-建行城东支行/ETN0001NP");
        accessCodes.add("1126550265");
        accessCodes.add("中信银行徐州分行-新城区徐矿集团/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3986NP");
        accessCodes.add("园区建行-建行港龙城市商业广场(M45402959)/ETN0001NP");
        accessCodes.add("南京淮安ETN0257NP");
        accessCodes.add("苏州-昆山/ETN4006NP");
        accessCodes.add("中信银行股份有限公司盐城分行-中信银行股份有限公司盐城分行(M57350927)/N001");
        accessCodes.add("HLWZX51320230394981");
        accessCodes.add("MSTPJS1002776419");
        accessCodes.add("园区建行-张家港常青藤中学(M49257965)/ETN0001NP");
        accessCodes.add("中信银行-吴江房地产管理处/ETN0001NP");
        accessCodes.add("中信银行徐州分行-香山物流银行/ETN0001NP");
        accessCodes.add("园区建行-希尔顿逸林酒店(M46137963)/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN4003NP");
        accessCodes.add("中信银行-联茂电子自助/ETN0001NP");
        accessCodes.add("154317405");
        accessCodes.add("建行高港支行-高港公积金管理中心/ETN0001NP");
        accessCodes.add("中信银行-好邻居签约中心/ETN0001NP");
        accessCodes.add("15366030");
        accessCodes.add("中信银行-中信六合大厦/30N0001NP");
        accessCodes.add("园区建行-吴江南麻太平路(M40048479)10M");
        accessCodes.add("省建行-建行建设大厦4层/ETN0001NP");
        accessCodes.add("中信银行-昆山陆丰东路ATM(M46177963)/ETN0001NP");
        accessCodes.add("南京扬州ETN0422NP");
        accessCodes.add("省建行-建设银行中山东路75号/ETN0001NP");
        accessCodes.add("154318079");
        accessCodes.add("苏州-吴江/ETN2997NP");
        accessCodes.add("中信银行徐州分行-市国土资源局/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3197NP");
        accessCodes.add("园区建行-昆山汇金财富广场/ETN0001NP");
        accessCodes.add("园区建行-昆山巴城支行(M41632958)2M");
        accessCodes.add("省建行-苏源大道长安马自达/ETN0001NP");
        accessCodes.add("13874730");
        accessCodes.add("南京扬州ETN0616NP");
        accessCodes.add("市政府数据资源中心-建行邗江支行/ETN0001NP");
        accessCodes.add("154317917");
        accessCodes.add("CZ190200_c");
        accessCodes.add("园区建行-张家港泗港镇武警中队/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3991NP");
        accessCodes.add("建行-建行武宜天豪大厦/ETN0001NP");
        accessCodes.add("连云港连云港STN440305");
        accessCodes.add("中信银行-昆山宏致电子(M40061352)2M");
        accessCodes.add("建设银行-深圳路建设银行ETN0001NP");
        accessCodes.add("M00244781");
        accessCodes.add("建设银行-建行工业学院/ETN0001NP");
        accessCodes.add("园区建行-东景工业坊苏州建行ATM机(M57377964)4M/ETN0001NP");
        accessCodes.add("M00106651A-M00106651Z");
        accessCodes.add("园区建行-建行城市生活广场(M52652969)/ETN0001NP");
        accessCodes.add("省建行-建行湖北路支行/ETN0001NP");
        accessCodes.add("园区建行-建行希尔顿逸林酒店大堂ATM(M45822957)/ETN0001NP");
        accessCodes.add("苏州-常熟/ETN4834NP");
        accessCodes.add("中信银行-新区财政局/ETN0001NP");
        accessCodes.add("省建行-凤凰国际广场B座7层/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3966NP");
        accessCodes.add("省建行-中国电子科技第14研究所国睿路8号/ETN0001NP");
        accessCodes.add("M00224438A-M00224438Z");
        accessCodes.add("中信银行-人设金保二期/ETN0001NP");
        accessCodes.add("园区建行-建行新浒支行/ETN0001NP");
        accessCodes.add("中信银行-吴江盛泽支行/ETN0001NP");
        accessCodes.add("南京扬州ETN6020NPH");
        accessCodes.add("南京扬州ETN0327NP");
        accessCodes.add("建行-建行花东支行/ETN0001NP");
        accessCodes.add("省建行-中国人民解放军95856部队管理处/ETN0001NP");
        accessCodes.add("园区建行-建行西山分理处/ETN0001NP");
        accessCodes.add("市财政局-中信银行/ETN0001NP");
        accessCodes.add("省建设银行-南京工业大学浦江学院/ETN0001NP");
        accessCodes.add("12265476");
        accessCodes.add("建行城西支行-邗江公积金/ETN0001NP");
        accessCodes.add("园区建行-建行玉盘路支行(M42262984)2M");
        accessCodes.add("龙江电信机房中信-上元大街280号中信银行/ETN0001NP");
        accessCodes.add("14703070");
        accessCodes.add("中信银行-通行宝智慧交通/ETN0001NP");
        accessCodes.add("南京徐州ETN0240NP");
        accessCodes.add("25738257");
        accessCodes.add("南京泰州ETN0580NP");
        accessCodes.add("省建行-人行南京营管部/ETN0001NP");
        accessCodes.add("兴化兴化STN000086");
        accessCodes.add("建设银行-维科路档案室ETN0001NP");
        accessCodes.add("中国建设银行股份有限公司海门支行-中国建设银行股份有限公司海门支行(M00143364)/N001");
        accessCodes.add("中信银行-中信银行中山路348号/30N0001NP");
        accessCodes.add("徐州市建设银行-建行沛县大屯煤电公司科瑞公司/ETN0001NP");
        accessCodes.add("南通建行-启东交投大数据/ETN0001NP");
        accessCodes.add("省建行-建行长江后街6号东南大学/ETN0001NP");
        accessCodes.add("M00231403A-M00231403Z");
        accessCodes.add("苏州-张家港/ETN3831NP");
        accessCodes.add("M00107370A-M00107370Z");
        accessCodes.add("省建行-省烟草公司二长IDC/ETN0001NP");
        accessCodes.add("园区建行-建行东沙湖邻里中心支行(M45527956)/ETN0001NP");
        accessCodes.add("中信银行-财政局(M40063353)2M");
        accessCodes.add("苏州-太仓/ETN3810NP");
        accessCodes.add("省建行-建行下关支行/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3215NP");
        accessCodes.add("园区建行-建行园区湖东新街口支行/ETN0002NP");
        accessCodes.add("园区建行-给排水公司新大楼(M40067114)2M");
        accessCodes.add("南京淮安ETN0578NP");
        accessCodes.add("HLWZX51820220537437");
        accessCodes.add("南通长桥南院-南通中信银行/ETN0001NP");
        accessCodes.add("中信银行-新中博职业培训学校/ETN0001NP");
        accessCodes.add("建设银行-江宁区汤山街道/ETN0001NP");
        accessCodes.add("M00005658A-M00005658Z");
        accessCodes.add("园区建行-吴江公积金中心大厅建设银行/ETN0001NP");
        accessCodes.add("M07004777Z-M07004777A");
        accessCodes.add("园区建行-建行城中支行(M40073937)2M");
        accessCodes.add("园区建行-国裕大厦公积金中心点(M40053052)/ETN0002NP");
        accessCodes.add("园区建行-高铁新城紫光大厦/ETN0001NP");
        accessCodes.add("苏州-常熟/ETN4755NP");
        accessCodes.add("省建行-省联合征信江东中路49号/ETN0001NP");
        accessCodes.add("省建设银行-游府西街8号电信IDC机房/ETN0001NP");
        accessCodes.add("建设银行-建行万豪国际POS联/ETN0001NP");
        accessCodes.add("南京镇江ETN0565NP");
        accessCodes.add("苏州-昆山/ETN4024NP");
        accessCodes.add("建设银行-中国建设银行双龙大道1341号锐恒君铂大厦/ETN0001NP");
        accessCodes.add("中信银行徐州分行-新城区市政府/ETN0001NP");
        accessCodes.add("建设银行-建设银行科技职业学院T2幢(MSTPJS1002863060)/ETN0001NP");
        accessCodes.add("省建设银行-南京农业大学/ETN0001NP");
        accessCodes.add("M00113326A-M00113326Z");
        accessCodes.add("南京扬州ETN0347NP");
        accessCodes.add("连云港嘉兴ETN6001NPH");
        accessCodes.add("154317483");
        accessCodes.add("中信银行-中信银行江阴支行/ETN0001NP");
        accessCodes.add("成都苏州ETN6002NPH");
        accessCodes.add("115807488");
        accessCodes.add("省建行-浦口区丽景路2号/ETN0001NP");
        accessCodes.add("省建行-省建行兴隆大街/ETN0001NP");
        accessCodes.add("园区建行-建行太平支行/ETN0002NP");
        accessCodes.add("中信银行-浦口区财政局/ETN0001NP");
        accessCodes.add("园区建行-房产管理局/ETN0001NP");
        accessCodes.add("园区建行-苏大附一院平江新院(M43442965)/ETN0001NP");
        accessCodes.add("新城大厦人社局-省建行/ETN0001NP");
        accessCodes.add("北京连云港ETN2010NP");
        accessCodes.add("中信银行镇江分行-丹阳财政局/ETN0001NP");
        accessCodes.add("中信银行-省国土资源厅/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3879NP");
        accessCodes.add("省建行-建行中央门晓庄支行/ETN0001NP");
        accessCodes.add("园区建行-吴江七都人民路(M40052162)10M");
        accessCodes.add("中信银行-昆山淳华科技(M40063854)2M");
        accessCodes.add("省建行-建行凤台南路146号天合广场/ETN0001NP");
        accessCodes.add("南京镇江ETN6037NPH");
        accessCodes.add("苏州-昆山/ETN3947NP");
        accessCodes.add("M00115210A-M00115210Z");
        accessCodes.add("建设银行-建设银行新湖大道9号/ETN0001NP");
        accessCodes.add("省建行-建筑业施工人员服务管理中心苜蓿园大街45号/ETN0001NP");
        accessCodes.add("省建行-鼓楼区财政局山西路124号/ETN0001NP");
        accessCodes.add("中信银行-黄埔科技大厦/ETN0001NP");
        accessCodes.add("省农信社-中信银行南京分公司/ETN0001NP");
        accessCodes.add("建设银行-六合建行大厂镇幸福路8号/ETN0001NP");
        accessCodes.add("M07006405");
        accessCodes.add("12265326");
        accessCodes.add("省建行-建行尧化门支行/ETN0001NP");
        accessCodes.add("中信银行-中信银行吉兴集团自助银行/ETN0001NP");
        accessCodes.add("建设银行-建行技师院分理处/ETN0001NP");
        accessCodes.add("M00003455A-M00003455Z");
        accessCodes.add("南京连云港ETN0537NP");
        accessCodes.add("中信银行巨凝-文化广场劳动和社会保障局/ETN0001NP");
        accessCodes.add("中信银行-中信银行昆山薛赵路(M59957963)2M/ETN0001NP");
        accessCodes.add("省建行-金鹰尚美酒店负3楼机房/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3949NP");
        accessCodes.add("建设银行南京分行-黄山岭路江苏健康职业院/ETN0001NP");
        accessCodes.add("宿迁市建设银行-宿迁市公安局/ETN0001NP");
        accessCodes.add("省建行-营苑小区储蓄所/ETN0001NP");
        accessCodes.add("中信银行-不锈钢交易中心自助/ETN0001NP");
        accessCodes.add("南京淮安ETN6018NPH");
        accessCodes.add("109005404");
        accessCodes.add("省建行-六合方州路699号/ETN0001NP");
        accessCodes.add("园区建行-常熟公积金管理中心ATM(M50592963)/ETN0001NP");
        accessCodes.add("建行-江都大桥开发大厦ETN0001NP");
        accessCodes.add("省建行-大桥北路9号弘阳大厦/ETN0001NP");
        accessCodes.add("M00115962A-M00115962Z");
        accessCodes.add("无锡建行-江阴滨江IDC机房/ETN0001NP");
        accessCodes.add("政府信息中心财政局-中信银行/ETN0001NP");
        accessCodes.add("SCETN4830NP(建行-沿江开发区支行M40054887)");
        accessCodes.add("园区建行-董浜星辰路/ETN0001NP");
        accessCodes.add("M00005646A-M00005646Z");
        accessCodes.add("园区建行-建行万宝广场(M50212976)/ETN0001NP");
        accessCodes.add("市政府数据资源中心-建行城西支行/ETN0001NP");
        accessCodes.add("中信银行-锦峰大厦A区1楼/ETN0001NP");
        accessCodes.add("中信银行-中信银行金鸡湖支行(M43592994)/ETN0001NP");
        accessCodes.add("南京扬州ETN0383NP");
        accessCodes.add("省建行-建设银行星火路19号/ETN0001NP");
        accessCodes.add("建设银行-浦口/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN4005NP");
        accessCodes.add("园区建行-建行临湖支行/ETN0002NP");
        accessCodes.add("南京盐城ETN6048NPH");
        accessCodes.add("数字电路用户终端（G.703-75）-CCD46AB6-M07036204Z");
        accessCodes.add("园区建行-张家港锦丰支行(M40077039)10M");
        accessCodes.add("南京宿迁ETN0411NP");
        accessCodes.add("中信银行-中信仪征支行/ETN0001NP");
        accessCodes.add("南通中信银行-海门中信银行/ETN0002NP");
        accessCodes.add("M00002239A-M00002239Z");
        accessCodes.add("12264604");
        accessCodes.add("苏州-太仓/ETN3227NP");
        accessCodes.add("南京泰州ETN6024NPH");
        accessCodes.add("114898944");
        accessCodes.add("中信银行-中信锡山支行/ETN0001NP");
        accessCodes.add("建设银行-市公安局长乐路9号/ETN0001NP");
        accessCodes.add("南通中信银行-海安中信银行中坝南路/ETN0001NP");
        accessCodes.add("镇江市建设银行-丹阳自来水公司/ETN0001NP-");
        accessCodes.add("省建行-东芭路13号建行档案室/ETN0001NP");
        accessCodes.add("南京扬州ETN0380NP");
        accessCodes.add("12265358");
        accessCodes.add("南京南通ETN0661NP");
        accessCodes.add("省建行-建行江东南路储蓄所/ETN0001NP");
        accessCodes.add("M00004215A-M00004215Z");
        accessCodes.add("154317409");
        accessCodes.add("省建行-省建行城南支行/ETN0001NP");
        accessCodes.add("M00218960");
        accessCodes.add("省建行-建行福园路支行/ETN0001NP");
        accessCodes.add("中信银行-湖南路支行/ETN0001NP");
        accessCodes.add("M00113201A-M00113201Z");
        accessCodes.add("中信银行-省福彩中心/ETN0001NP");
        accessCodes.add("中信银行-兴吴路支行/ETN0001");
        accessCodes.add("中信银行-南京医保局凤游寺52号/ETN0001NP");
        accessCodes.add("省建行-建行幕府西路支行/ETN0001NP");
        accessCodes.add("南京扬州ETN0418NP");
        accessCodes.add("园区建行-自来水公司(M53577963)/ETN0001NP");
        accessCodes.add("M07002152A-M07002152Z");
        accessCodes.add("省建设银行-解放军联勤保障新门口10号/ETN0001NP");
        accessCodes.add("中信银行-社保局/ETN0001NP");
        accessCodes.add("中信银行-园区公积金管理中心(M40050073)/ETN0001NP");
        accessCodes.add("中信银行-吉兴集团自助/ETN0002NP");
        accessCodes.add("M00215114A-M00215114Z");
        accessCodes.add("中信银行-中信银行集庆路78号/ETN0001NP");
        accessCodes.add("园区建行-现代大厦国土储备中心/ETN0001NP");
        accessCodes.add("南京扬州ETN6141NPH");
        accessCodes.add("省建设银行-电信IDC机房/ETN0001NP");
        accessCodes.add("政企网关（LAN）物理设备-E4492EFD-PC-17305083");
        accessCodes.add("徐州住房公积金-建设银行/ENT0001NP");
        accessCodes.add("B00007627");
        accessCodes.add("园区建行-建行斜塘支行/ETN0001NP");
        accessCodes.add("Z12245A-Z12245Z");
        accessCodes.add("园区建行-吴中住建局(M48997959)/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3846NP");
        accessCodes.add("建设银行-昆山市玉山镇博士路99号/ETN0001NP");
        accessCodes.add("省建行-舜辉商务大厦/ETN0001NP");
        accessCodes.add("南京盐城ETN6025NPH");
        accessCodes.add("苏州-昆山MSTPJS100322590/ETN0001NP");
        accessCodes.add("园区建行-园区管委会(M53907976)/ETN0001NP");
        accessCodes.add("中国建设银行股份有限公司海门支行-中国建设银行股份有限公司海门支行(M00115222)/N002");
        accessCodes.add("市建设银行-建行贾汪财政局/ETN0001NP");
        accessCodes.add("中信银行-中信银行集庆门大街269号/30N0001NP");
        accessCodes.add("苏州-太仓/ETN3219NP");
        accessCodes.add("中信银行-建筑设计研究院创意路86号/ETN0001NP");
        accessCodes.add("园区建行(CYQJH)-建行新庄分理处/ETN0001NP");
        accessCodes.add("园区建行-建行渡村新市街ATM(M47102958)/ETN0001NP");
        accessCodes.add("154317469");
        accessCodes.add("仪征仪化公积金-建行仪化支行/ETN0001NP");
        accessCodes.add("12346039");
        accessCodes.add("国土资源局-园区建行/ETN0001NP");
        accessCodes.add("省建行-电力公司/ETN0002NP");
        accessCodes.add("南京连云港ETN0536NP");
        accessCodes.add("建设银行建宁路16号综合楼-中国建设银行中山北路260号/ETN0001NP");
        accessCodes.add("南通中信银行-如东中信银行友谊东路/ETN0001NP");
        accessCodes.add("省建行-江宁东山外国语学校/ETN0001NP");
        accessCodes.add("中信银行-中信银行和燕路251号/ETN0001NP");
        accessCodes.add("北京南京ZQOTN00099");
        accessCodes.add("市政府数据资源中心-建行新区支行/ETN0001NP");
        accessCodes.add("HLWZX523202311105972");
        accessCodes.add("数字电路用户终端（以太网电口）-0719D941-数字电路用户终端（以太网电口）-4B502D4C");
        accessCodes.add("省建行-浦口象山路江浦高级中学/ETN0001NP");
        accessCodes.add("M00113529A-M00113529Z");
        accessCodes.add("建设银行-建设银行太平金融大厦(MSTPJS1003121486)/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3931NP");
        accessCodes.add("中信银行-中信通惠路支行/ETN0001NP");
        accessCodes.add("12265088");
        accessCodes.add("建设银行-吉山1号楼5层B区/ETN0001NP");
        accessCodes.add("中信银行-绿点科技新厂自助（C5厂房旁ATM）/ETN0001NP");
        accessCodes.add("M40009286A-M40009286Z");
        accessCodes.add("建设银行-房管中心西康路47号/ETN0001NP");
        accessCodes.add("建行-张家港永联小镇北街/ETN0001NP");
        accessCodes.add("徐州徐州ETN6022NPH");
        accessCodes.add("中信银行-张家港沙钢集团财务大楼/ETN0001NP");
        accessCodes.add("M40007626A-M40007626Z");
        accessCodes.add("中信银行--贝壳海岸城签约中心/ETN0001NP");
        accessCodes.add("12265452");
        accessCodes.add("省建行-省民政厅1层中山北路277号/ETN0001NP");
        accessCodes.add("中信银行-硕放通祥路自助/ETN0002NP");
        accessCodes.add("苏州-张家港/ETN3839NP");
        accessCodes.add("120079477");
        accessCodes.add("M00231360A-M00231360Z");
        accessCodes.add("市建设银行-香山物流银行/ETN0001NP");
        accessCodes.add("HLWZX512202307205620");
        accessCodes.add("南京盐城ETN0700NP");
        accessCodes.add("省建行-建行紫金江宁上秦淮酒店/ETN0001NP");
        accessCodes.add("中国建行镇江分行-农业农村局/ETN0001NP");
        accessCodes.add("园区建行-建行人民桥分理处/ETN0001NP");
        accessCodes.add("M07027667A-M07027667Z");
        accessCodes.add("园区建行-建行沧浪支行(M40052577)10M");
        accessCodes.add("中信银行-市民广场B26F/ETN0001NP");
        accessCodes.add("中信银行-滨湖区财政局/ETN0001NP");
        accessCodes.add("建设银行-溧水县财政局/ETN0001NP");
        accessCodes.add("建设银行-昆山市玉山镇景王路/ETN0001");
        accessCodes.add("省建行-中国建设银行龙眠大道101号/ETN0001NP");
        accessCodes.add("M00302041");
        accessCodes.add("HLWZX025202304152783");
        accessCodes.add("12458050");
        accessCodes.add("省建行-建设银行广志路22号/ETN0001NP");
        accessCodes.add("省建行-六合德纳(南京)化工长芦街道/ETN0001NP");
        accessCodes.add("园区建行-方洲路598号行政楼(M52572963)/ETN0001NP");
        accessCodes.add("建行-建行丁堰延陵东/ETN0001NP");
        accessCodes.add("常州CTVPN19429A");
        accessCodes.add("园区建行-常熟长江路266号7楼机房/ETN0001NP");
        accessCodes.add("无锡住房置业担保公司-中信银行/ETN0001NP");
        accessCodes.add("园区建行-建行大观路/ETN0001NP");
        accessCodes.add("成都苏州ETN6001NPH");
        accessCodes.add("中信银行-自来水公司/ETN0001NP");
        accessCodes.add("建行连云港分行-东海县第二中学食堂/ETN0001NP");
        accessCodes.add("建行-软件园易交易/ETN0001NP");
        accessCodes.add("南京泰州ETN0611NP");
        accessCodes.add("园区建行-建行浒关开发区支行(M40075208)2M");
        accessCodes.add("12265057");
        accessCodes.add("苏州-张家港/ETN3811NP");
        accessCodes.add("中信银行-中信银行天元西路38号/ETN0001NP");
        accessCodes.add("建设银行-淮安国联ETN0001NP");
        accessCodes.add("省建行-江苏银行中山南路111号/ETN0001NP");
        accessCodes.add("园区建行-建行干将房金部/ETN0001NP");
        accessCodes.add("南京无锡ETN0975NP");
        accessCodes.add("M00005652A-M00005652Z");
        accessCodes.add("SCETN4825NP(建行-福山支行M40054345)");
        accessCodes.add("M00115228A-M00115228Z");
        accessCodes.add("新区管委会-园区建行(M52697963)/ETN0001NP");
        accessCodes.add("连云港连云港STN001761");
        accessCodes.add("无锡CTVPN19429A");
        accessCodes.add("建行-建行欣晨苏果/ETN0001NP");
        accessCodes.add("园区建行-建行滨湖支行(M40242971)2M");
        accessCodes.add("M07245676");
        accessCodes.add("省建行-苜蓿园分理处/ETN0001NP");
        accessCodes.add("海安住建局-海安建行/ETN0001NP");
        accessCodes.add("115315619");
        accessCodes.add("园区建行-住建局(M52282963)/ETN0001NP");
        accessCodes.add("园区建行-张家港沙洲东路高级中学(M47487959)/ETN0001NP");
        accessCodes.add("园区建行-太仓信息港3楼(M65818164)/ETN0001NP");
        accessCodes.add("建设银行-万象天地/ETN0001NP");
        accessCodes.add("SCETN4764NP(建行-菱北分理处M40052128)");
        accessCodes.add("园区建行-建行唯亭分理处/ETN0002NP");
        accessCodes.add("15202201");
        accessCodes.add("国际数据中心净慧东道88号303B房-中信银行/ETN0002NP");
        accessCodes.add("中信银行-中信新栖支行/ETN0001NP");
        accessCodes.add("园区建行-幼儿师范学ATM(M45142956)/ETN0001NP");
        accessCodes.add("省建行-省建行矿业分理处/ETN0001NP");
        accessCodes.add("15958609");
        accessCodes.add("南通中信银行-南通房管局人民东路/ETN0001NP");
        accessCodes.add("M00215132A-M00215132Z");
        accessCodes.add("市中信银行-大丰支行ETN0001NP(M41321919)");
        accessCodes.add("南京扬州ETN0096NP");
        accessCodes.add("省建行-建行扬子支行新华路497号/ETN0001NP");
        accessCodes.add("M00115225A-M00115225Z");
        accessCodes.add("建设银行-建行横山五一自助银行/ETN0001NP");
        accessCodes.add("上海南京ETN0741NP");
        accessCodes.add("常州人民银行-中信银行代收费/ETN0001NP");
        accessCodes.add("广元连云港ETN0001NP");
        accessCodes.add("园区建行-元和燃气大楼(M48272965)/ETN0001NP");
        accessCodes.add("省建行-建行特殊教育职业技术学院外联/ETN0001NP");
        accessCodes.add("建设银行-江宁/ETN0001NP");
        accessCodes.add("M00008294A-M00008294Z");
        accessCodes.add("省建行-建行房地产交易登记中心/ETN0001NP");
        accessCodes.add("建行中山北路支行-北区鼓楼区财政局/30N0001NP");
        accessCodes.add("PC-AAA03DDD-4+1E8-C(天翼网关)设备-F09C047C");
        accessCodes.add("4+1E8-C(天翼网关)设备-E4898B11-PC-0A45D88E");
        accessCodes.add("中信银行-南京信息工程大学创业中心3号楼201室/30N0001NP");
        accessCodes.add("园区建行-常熟达涅利ATM(M41687959)2M");
        accessCodes.add("MSTPJS1002981410");
        accessCodes.add("中信银行-江阴人民中路自助/ETN0002NP");
        accessCodes.add("中信银行-宜兴官林医院自助/ETN0001NP");
        accessCodes.add("园区建行(CYQJH)-建行葑门分理处/ETN0001NP");
        accessCodes.add("南京扬州ETN0350NP");
        accessCodes.add("中信银行-电信龙江机房/ETN0001NP");
        accessCodes.add("154317490");
        accessCodes.add("省建行-高淳财政局/ETN0001NP");
        accessCodes.add("南京沭阳ETN0012NP");
        accessCodes.add("园区建行-建行安洁科技北大门ATM(M40059507)2M");
        accessCodes.add("建设银行-建行大学城纺院食堂/ETN0001NP");
        accessCodes.add("中信银行-住建局(M52217963)/ETN0001NP");
        accessCodes.add("154318081");
        accessCodes.add("南京泰州ETN0590NP");
        accessCodes.add("扬州扬州ETN6123NPH");
        accessCodes.add("盐城中信银行-盐城盐东车管所/ETN0001NP");
        accessCodes.add("115912537");
        accessCodes.add("园区建行-昆山周市支行(M40077519)/ETN0001NP");
        accessCodes.add("省建行-建行润花园分理处/ETN0002NP");
        accessCodes.add("PC-8D0F2B9F-4+16政企融合网关(PON)设备-8BE97706");
        accessCodes.add("中信银行-中信银行信用卡/ETN0001NP");
        accessCodes.add("徐州市建设银行-沛县财政局/ETN0001NP");
        accessCodes.add("中信银行-国电南瑞长江新村2号/ETN0001NP");
        accessCodes.add("建设银行-梅山医院/ETN0001NP");
        accessCodes.add("省财政-中信银行/ETN0001NP");
        accessCodes.add("省建行-街道文德路8号建设银行/ETN0001NP");
        accessCodes.add("中信银行镇江分行-中信银行扬中支行（金叶大酒店）/ETN0001NP");
        accessCodes.add("中信银行-昆山西部服务中心(M51887963)/ETN0001NP");
        accessCodes.add("中信银行徐州分行-铜山柳新普惠双语学校/ETN0001NP");
        accessCodes.add("南京泰州ETN0604NP");
        accessCodes.add("省建行-交通银行明故宫路6号/ETN0001NP");
        accessCodes.add("建设银行省分行-浦口财政局机房/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3847NP");
        accessCodes.add("苏州-常熟/ETN4827NP");
        accessCodes.add("南京徐州ETN6050NPH");
        accessCodes.add("M00113268A-M00113268Z");
        accessCodes.add("M07004538A-M07004538Z");
        accessCodes.add("中信银行-昆山电信IDC机房(M57352964)2M/ETN0001NP");
        accessCodes.add("建行-市区行政服务中心自助/ETN0001NP");
        accessCodes.add("中信银行-中级法院/ETN0001NP");
        accessCodes.add("徐州住房公积金-中信银行/ENT0001NP");
        accessCodes.add("连云港连云港STN000025");
        accessCodes.add("盐城建行建军路支行-盐城市公积金管理中心/ETN0001NP");
        accessCodes.add("建设银行-江宁/ETN0003NP");
        accessCodes.add("建设银行-清江浦支行ETN0001NP");
        accessCodes.add("M00244799");
        accessCodes.add("M40003347A-M40003347Z");
        accessCodes.add("南通CTVPN19429E");
        accessCodes.add("园区建行(CYQJH)-建行桐泾分理处/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3832NP");
        accessCodes.add("HLWZX51020211267298");
        accessCodes.add("中信银行");
        accessCodes.add("南京徐州ETN0720NP");
        accessCodes.add("南京淮安ETN6047NPH");
        accessCodes.add("南京淮安ETN0539NP");
        accessCodes.add("M00223816A-M00223816Z");
        accessCodes.add("园区建行-国土资源局(M45007956)/ETN0001NP");
        accessCodes.add("如皋建行人社-如皋建行长江/ETN0001NP");
        accessCodes.add("SKETN4008NP(园区建行-开发区分理处M40053342)");
        accessCodes.add("滨海医保中心-建行阜东分理处MSTP2M");
        accessCodes.add("12264545");
        accessCodes.add("省建行-建设银行下曹村100号/ETN0001NP");
        accessCodes.add("省建设银行大楼-黄山岭路江苏健康职业院/ETN0001NP");
        accessCodes.add("14768173");
        accessCodes.add("泰州泰州ETN6277NPH");
        accessCodes.add("市政府数据资源中心-建行凯旋支行/ETN0001NP");
        accessCodes.add("市政府数据资源中心-建行仪征城北支行/ETN0001NP");
        accessCodes.add("北京无锡ETN2130NP");
        accessCodes.add("154317915");
        accessCodes.add("省建行-建行应天路分理处/ETN0001NP");
        accessCodes.add("中信银行-相城区住建局(M41622959)2M");
        accessCodes.add("常州建行-建行茅山医院点/ETN0001NP");
        accessCodes.add("南京泰州ETN6097NPH");
        accessCodes.add("中国建设银行-新城区财政局/ETN0001NP");
        accessCodes.add("国土资源局-中信银行/ETN0001NP");
        accessCodes.add("M00244806");
        accessCodes.add("M40116432A-M40116432Z");
        accessCodes.add("钟楼政务云-建行/ETN0001NP");
        accessCodes.add("省建行-省建行孝陵卫分理处/ETN0001NP");
        accessCodes.add("SCETN4809NP(建行-海虞北路老街分理处M40053412)");
        accessCodes.add("南京常州ETN0792NP");
        accessCodes.add("省建行-建设银行文苑路118号/ETN0001NP");
        accessCodes.add("中信银行-中信文昌支行/ETN0001NP");
        accessCodes.add("南京泰州ETN0635NP");
        accessCodes.add("中信银行-恒田纺织自助/ETN0001NP");
        accessCodes.add("中信银行-中信银行中山南路101号城中支行/ETN0001NP");
        accessCodes.add("园区建行-公安局(M61792964)/ETN0001NP");
        accessCodes.add("园区建行-常熟理工学院务本楼(M53282964)/ETN0001NP");
        accessCodes.add("省建设银行-建行虎踞北路12号/ETN0001NP");
        accessCodes.add("园区建行-建行附二院门诊大楼一楼ATM(M40062023)2M");
        accessCodes.add("M07010601A-M07010601Z");
        accessCodes.add("南京宿迁ETN0415NP");
        accessCodes.add("南京泰州ETN0594NP");
        accessCodes.add("L1003466");
        accessCodes.add("建设银行-建行张家港政府综合楼(MSTPJS1002682200)/ETN0001NP");
        accessCodes.add("省建行-建行润发路9号南方时代购物广场/ETN0001NP");
        accessCodes.add("中信银行-南施街IDC机房/ETN0001NP");
        accessCodes.add("园区建行-建行沙湖创投中心3号楼ATM(M40062186)2M");
        accessCodes.add("扬州扬州ETN6234NPH");
        accessCodes.add("建设银行-溧水文昌路201号建设银行/ETN0001NP");
        accessCodes.add("HLWZX523202409117658");
        accessCodes.add("154318083");
        accessCodes.add("数字电路用户终端（以太网电口）-41641E7B-数字电路用户终端（以太网电口）-1C7487AF");
        accessCodes.add("南京泰州ETN0632NP");
        accessCodes.add("园区建行-建行相城支行陆慕分理处/ETN0001NP");
        accessCodes.add("省建行-国信财务公司/ETN0001NP");
        accessCodes.add("中信银行-兆润财富中心中信银行/ETN0001NP");
        accessCodes.add("市建设银行-矿山路港华燃气/ETN0001NP");
        accessCodes.add("建行-河海大学图书馆/ETN0001NP");
        accessCodes.add("南京宿迁ETN0422NP");
        accessCodes.add("省建行-建设银行紫东路1号/ETN0001NP");
        accessCodes.add("省建行-港华燃气/30N0001NP-M07030546A");
        accessCodes.add("中国建设银行股份有限公司昆山分行(M40058106)/N001");
        accessCodes.add("扬州CTVPN19429A");
        accessCodes.add("苏州-太仓/ETN3186NP");
        accessCodes.add("盐城盐城STN000445");
        accessCodes.add("省建行-建设银行汤泉东路11号汤山分理处/ETN0001NP");
        accessCodes.add("建设银行-灌南县集中办公区财政局/ETN0001NP");
        accessCodes.add("14798679");
        accessCodes.add("中信银行-昆山电信IDC机房社保局/ETN0001NP");
        accessCodes.add("南京泰州ETN0602NP");
        accessCodes.add("省建行-五星电器游府西街4楼IDC/ETN0001NP");
        accessCodes.add("建设银行-江北市民中心建行自助区/ETN0001NP");
        accessCodes.add("中信银行-云计算中心国土局ETN0001NP");
        accessCodes.add("省建行-建设银行杨新路229号/ETN0001NP");
        accessCodes.add("市建设银行-建行中和大厦产权处/ETN0001NP");
        accessCodes.add("HLWZX02520210212351");
        accessCodes.add("苏州-昆山/ETN3965NP");
        accessCodes.add("省建行-电力公司/ETN0001NP");
        accessCodes.add("公积金-中信银行/ETN0001NP");
        accessCodes.add("中信银行-市政府信息中心(M52942963)2M");
        accessCodes.add("建设银行-建西支行ETN0001NP");
        accessCodes.add("南京镇江ETN6036NPH");
        accessCodes.add("15959728");
        accessCodes.add("中信银行-昆山扬明光学厂区(M40060356)2M");
        accessCodes.add("苏州-张家港/ETN3809NP");
        accessCodes.add("中信银行-九龙医院(M50222969)/ETN0001NP");
        accessCodes.add("中信银行梦都大街156号-中信银行草场门大街89号/ETN0001NP");
        accessCodes.add("园区建行-吴中供水(M50872963)/ETN0001NP");
        accessCodes.add("中信银行-中信银行锦绣路办事区/ETN0001NP");
        accessCodes.add("省建行-建设银行溧水东城汇商业街营业部/ETN0001NP");
        accessCodes.add("园区建行-东渚科技城IDC机房(G11机柜)(M43507971)2M/ETN0001NP");
        accessCodes.add("M00104726A-M00104726Z");
        accessCodes.add("园区建行-吴江松陵江兴西路(M40052237)10M");
        accessCodes.add("省建行-建行江东路分理处/ETN0001NP");
        accessCodes.add("中信银行南京分公司-省财政厅梦都大街136号/ETN0001NP");
        accessCodes.add("南通建行-如皋财政局/ETN0001NP");
        accessCodes.add("省建行-顶山龙虎巷中车浦镇/ETN0001NP");
        accessCodes.add("省建行-建行炼油厂分理处/ETN0001NP");
        accessCodes.add("M00231400A-M00231400Z");
        accessCodes.add("113492007");
        accessCodes.add("园区建行-启迪大厦26楼建设银行/ETN0001NP");
        accessCodes.add("园区建行-建行经贸学院传达室ATM(M40062029)2M");
        accessCodes.add("园区建行-建行望亭分理处/ETN0001");
        accessCodes.add("园区建行-建行长春路支行(M40053076)/ETN0001NP");
        accessCodes.add("中信银行-中信银行江星广场支行(M42327959)/ETN0001NP");
        accessCodes.add("园区建行-交通局(M55932974)/ETN0001NP");
        accessCodes.add("中信银行-南京信息工程大学/30N0001NP");
        accessCodes.add("22091593");
        accessCodes.add("中国建设银行股份有限公司宿迁分行-中国建设银行股份有限公司宿迁分行(M00262452)/N002");
        accessCodes.add("中信银行-中信银行南京分公司/ETN0001NP");
        accessCodes.add("中信银行-中信银行天一中学食堂ATM机/ETN0001NP");
        accessCodes.add("省建行-省建工集团郑和中路118号/ETN0001NP");
        accessCodes.add("中信银行-苏桐路/ETN0001NP");
        accessCodes.add("徐州徐州ETN6023NPH");
        accessCodes.add("南通中信银行-南通中信银行开发区支行/ETN0001NP");
        accessCodes.add("省建行-溧水县通济街11号建行/ETN0001NP");
        accessCodes.add("SCETN4814NP(建行-万达广场M40053849)4M");
        accessCodes.add("南通中信银行-海门中信银行解放中路/ETN0001NP");
        accessCodes.add("南京南通ETN6035NPH");
        accessCodes.add("句容市华阳镇华阳北路中信银行");
        accessCodes.add("园区建行-景德路建行(M54277970)/ETN0001NP");
        accessCodes.add("中信银行-中信新区太科园支行/ETN0001NP");
        accessCodes.add("宿迁市建设银行-洋河酒厂/ETN0001NP");
        accessCodes.add("省建行-建设银行游府西街8号/ETN0001NP");
        accessCodes.add("建设银行-建设银行百水芊城春水坊/ETN0001NP");
        accessCodes.add("省建行-建设银行浦滨路150号/ETN0001NP");
        accessCodes.add("中信银行-钟山学院电信营业厅/ETN0001NP");
        accessCodes.add("南京东莞ETN0038NP");
        accessCodes.add("15958878");
        accessCodes.add("HLWZX51020220381975");
        accessCodes.add("14580793");
        accessCodes.add("省建行-茅山路53号新区医院/ETN0001NP");
        accessCodes.add("中信银行-省企业信息化部吉山4楼/ETN0001NP");
        accessCodes.add("中信银行-中信广瑞路支行/ETN0001NP");
        accessCodes.add("南京徐州ETN0704NP");
        accessCodes.add("中信银行-金安押运公司/ETN0002NP");
        accessCodes.add("149510859");
        accessCodes.add("武汉南京ETN0044NP");
        accessCodes.add("省建行-建设银行浦滨路扬子科创中心/ETN0002NP");
        accessCodes.add("M00104666A-M00104666Z");
        accessCodes.add("园区建行-建行胜浦分理处/ETN0001NP");
        accessCodes.add("凤台南路146号天合广场-南京银联/ETN0001NP");
        accessCodes.add("南京泰州ETN0591NP");
        accessCodes.add("园区建行-吴江平望通运路(M40052156)10M");
        accessCodes.add("中信银行-江都龙川投资/ETN0001NP");
        accessCodes.add("中信银行诚信大道19号-中山路348号中信大厦(2M)/30NP");
        accessCodes.add("建行-公积金/ETN0001NP");
        accessCodes.add("南京扬州ETN0367NP");
        accessCodes.add("园区建行-供电公司(M65723163)/ETN0001NP");
        accessCodes.add("省建行-省公安厅/ETN0001NP");
        accessCodes.add("苏州-吴江/ETN2958NP");
        accessCodes.add("南京徐州ETN0712NP");
        accessCodes.add("中信银行-昆山绿地大道支行(M43607959)/ETN0001NP");
        accessCodes.add("省建行-建设银行军区总院外科楼/ETN0001NP");
        accessCodes.add("HLWZX51020211264241");
        accessCodes.add("中信银行-中信银行城南支行/ETN0001NP");
        accessCodes.add("园区建行-笠泽路建委大楼(M51907972)/ETN0001NP");
        accessCodes.add("15958998");
        accessCodes.add("南京宿迁ETN0436NP");
        accessCodes.add("仪征公积金-建行城北支行/ETN0001NP");
        accessCodes.add("M00209595");
        accessCodes.add("省建行-建行沙洲街道服务中心/ETN0001NP");
        accessCodes.add("中信银行-上海汽车自助/ETN0002NP");
        accessCodes.add("省建行-建行龙江支行/ETN0001NP");
        accessCodes.add("数据中心二期（公积金）-中信银行/ETN0001NP");
        accessCodes.add("园区建行-东桥为民服务中心ATM/ETN0001NP");
        accessCodes.add("园区建行-建行苏大附一院北1层放射科ATM(M46662956)/ETN0001NP");
        accessCodes.add("园区建行-建行新沧分理处/ETN0001NP");
        accessCodes.add("STETN3217NP(建行-人民南路M40047810)4M");
        accessCodes.add("建设银行-建行铁道高等职业技术学校/ETN0001NP");
        accessCodes.add("园区建行-建行星都支行(M40059596)2M");
        accessCodes.add("中国建设银行股份有限公司宿迁分行-泗洪兴洪中学/ETN0001NP");
        accessCodes.add("建设银行-城北支行ETN0001NP");
        accessCodes.add("南京宿迁ETN0427NP");
        accessCodes.add("苏州海盐ETN0001NP");
        accessCodes.add("中信银行-常熟智慧机房/ETN0001NP");
        accessCodes.add("省建行-建行城南支行/ETN0001NP");
        accessCodes.add("建设银行-建行江苏工程学院网点/ETN0001NP");
        accessCodes.add("中信银行-吴江支行(M43602974)10M");
        accessCodes.add("连云港灌南STN000107");
        accessCodes.add("园区建行-张家港沙洲湖科创园新点软件/ETN0001NP");
        accessCodes.add("省建行-省建行浅草明苑/ETN0001NP");
        accessCodes.add("园区建行-太仓浏河行政审批中心(M53602963)/ETN0001NP");
        accessCodes.add("110405392");
        accessCodes.add("省建行-南京工业大学浦江学院办公楼三楼机房/ETN0001NP");
        accessCodes.add("中信银行梦都大街156号-中信大厦中山路348号/ETN0001NP");
        accessCodes.add("园区建行-新区自来水(M53577966)/ETN0001NP");
        accessCodes.add("园区建行-久阳商务楼6楼/ETN0001NP");
        accessCodes.add("140762231");
        accessCodes.add("园区建行(CYQJH)-建行苏锦分理处/ETN0001NP");
        accessCodes.add("南京淮安ETN0529NP");
        accessCodes.add("园区建行-吴江同里迎燕路(M44217963)10M");
        accessCodes.add("116954881");
        accessCodes.add("南京宿迁ETN0424NP");
        accessCodes.add("省建行-建设银行上海路80号/ETN0001NP");
        accessCodes.add("园区建行-昆山沪士电子新厂房(M40064267)2M");
        accessCodes.add("园区建行-常熟海虞支行(M40062967)4M");
        accessCodes.add("苏州-昆山/ETN4002NP");
        accessCodes.add("园区建行-建行吴中支行营业部/ETN0001NP");
        accessCodes.add("M00231413");
        accessCodes.add("M46157965");
        accessCodes.add("省建行-市民卡有限公司/ETN0001NP");
        accessCodes.add("中信银行-张家港国泰时代广场(DG07448)/ETN0001NP");
        accessCodes.add("中国建设银行通州市支行-中国建设银行通州市支行(M00155517)/N003");
        accessCodes.add("园区建行-苏州中心广场建设银行(M49933003)/ETN0001NP");
        accessCodes.add("省建行-江苏第二师范学院/ETN0001NP");
        accessCodes.add("园区建行-建行润元路支行(M40058178)/ETN0001NP");
        accessCodes.add("省建行-省民政厅1层中山北路277号/ETN0002NP");
        accessCodes.add("省建行-建设银行中山陵5号/ETN0001NP");
        accessCodes.add("省建行-省社保基金贸易中心/ETN0001NP");
        accessCodes.add("M00244793");
        accessCodes.add("镇江市建设银行-丹阳医保中心/ETN0001NP");
        accessCodes.add("园区建行-太仓太平南路信息港(M54107974)/ETN0001NP");
        accessCodes.add("园区建行-太仓新华西路建行/ETN0001NP");
        accessCodes.add("江苏省财政厅-省建行/ETN0001NP");
        accessCodes.add("建设银行-藏书镇藏中路2100号部队财务室/ETN0001NP");
        accessCodes.add("118159788");
        accessCodes.add("建设银行-张家港市府大院综合楼(M51112966)/ETN0001NP");
        accessCodes.add("南京杭州ETN0110NP");
        accessCodes.add("园区建行-财政局(M53792969)/ETN0001NP");
        accessCodes.add("园区建行-建行金阊支行/ETN0001NP");
        accessCodes.add("园区建行-建行吴中西路ATM(M40058395)2M");
        accessCodes.add("中国建设银行股份有限公司镇江分行-镇江市公共交易资源中心/ETN0001NP");
        accessCodes.add("M00002941A-M00002941Z");
        accessCodes.add("园区建行-常熟景程天然气公司门卫(M52192966)/ETN0001NP");
        accessCodes.add("154318073");
        accessCodes.add("1124477169");
        accessCodes.add("园区建行-建行吴江流虹路嘉鸿支行(M41677965)10M");
        accessCodes.add("中信银行-中信银行浦东路13号/ETN0001NP");
        accessCodes.add("中信银行-中信银行工业大学丁家桥校区/30N0001NP");
        accessCodes.add("省建行-建设银行胜太路39号开发区分理处/ETN0001NP");
        accessCodes.add("HLWZX512202403264206");
        accessCodes.add("M00231378A-M00231378Z");
        accessCodes.add("省建行-工商银行/ETN0001NP");
        accessCodes.add("园区建行(CYQJH)-建行平江支行/ETN0001NP");
        accessCodes.add("南京扬州ETN0412NP");
        accessCodes.add("南京徐州ETN6128NPH");
        accessCodes.add("南京徐州ETN0427NP");
        accessCodes.add("省建设银行-建设银行海福巷71号/ETN0001NP");
        accessCodes.add("M00113148A-M00113148Z");
        accessCodes.add("15959400");
        accessCodes.add("M40121458A-M40121458Z");
        accessCodes.add("如皋建行人社-如皋建行大楼/ETN0001NP");
        accessCodes.add("园区建行-建行越溪分理处/ETN0001NP");
        accessCodes.add("园区建行-吴江盛泽舜新中路广场(M40049767)10M");
        accessCodes.add("泰兴公积金-黄桥建行/ETN0001NP");
        accessCodes.add("扬州扬州ETN6169NPH");
        accessCodes.add("建设银行-常州财政局/ETN0001NP");
        accessCodes.add("省建行-高淳红宝丽/ETN0001NP");
        accessCodes.add("12264844");
        accessCodes.add("南京南通ETN0946NP");
        accessCodes.add("镇江市财政局-中信银行镇江分行/ETN0001NP");
        accessCodes.add("园区建行-平江区国库支付中心(M53777966)/ETN0001NP");
        accessCodes.add("园区建行-建行胥口支行(M40053058)/ETN0001NP");
        accessCodes.add("15959768");
        accessCodes.add("中信银行-中信银行/ETN0001NP");
        accessCodes.add("园区建行-房产交易中心(M54277968)/ETN0001NP");
        accessCodes.add("M40002854A-M40002854Z");
        accessCodes.add("南京扬州ETN0447NP");
        accessCodes.add("省建行-省建行梅山三所/ETN0001NP");
        accessCodes.add("中信银行镇江分行-中信银行句容支行/ETN0002NP");
        accessCodes.add("15959695");
        accessCodes.add("南京徐州ETN0730NP");
        accessCodes.add("中信银行-中信银行诚信大道19号国电南瑞/30N0001NP");
        accessCodes.add("中信银行-吴江大厦B幢7楼(M52817966)/ETN0001NP");
        accessCodes.add("园区建行-建行吴江横扇南举路(M40057973)10M");
        accessCodes.add("仪征不动产交易中心-仪征建行支行/ETN0001NP");
        accessCodes.add("南通建行-如东大众燃气/ETN0001NP");
        accessCodes.add("园区建行-建行星韵商业中心/ETN0001NP");
        accessCodes.add("建设银行-溧阳涵田建行POS/ETN0001NP");
        accessCodes.add("省建行-高淳县财政局机房/ETN0001NP");
        accessCodes.add("杭州昆山ETN0012NP");
        accessCodes.add("住房公积金管理中心-灌南县新安镇人民西路(FTTH)住房公积金/ETN0001NP");
        accessCodes.add("M00002939A-M00002939Z");
        accessCodes.add("省建行-浦口区浦东路6号-1号/ETN0001NP");
        accessCodes.add("南京泰州ETN0582NP");
        accessCodes.add("中信银行-贝壳新体签约中心(华东大厦21楼)/ETN0001NP");
        accessCodes.add("中信银行-中信银行新模范马路5号/30N0001NP");
        accessCodes.add("HLWZX512202306199024");
        accessCodes.add("M00009100A-M00009100Z");
        accessCodes.add("建设银行-张家港杨舍镇农商行/ETN0001");
        accessCodes.add("中信银行-理工大学紫金学院/ETN0001NP");
        accessCodes.add("中信银行-南京工业大学江浦校区信息中心/30N0001NP");
        accessCodes.add("园区建行-海棠街18号电信IDC机房(M40076007)2M");
        accessCodes.add("中信银行-中信银行华侨大厦/ETN0001NP");
        accessCodes.add("园区建行-张家港学院现教中心(M49832976)/ETN0001NP");
        accessCodes.add("中信银行-中信银行国电南瑞/30N0001NP-M07240766Z");
        accessCodes.add("宿迁市建设银行-沭阳县财政局测试/ETN0001NP");
        accessCodes.add("省建行-建行林业大学宿舍楼研究生楼二楼财务处/ETN0001NP");
        accessCodes.add("苏州海盐ETN0002NP");
        accessCodes.add("M00113227A-M00113227Z");
        accessCodes.add("中国建行镇江分行-丹阳水务集团/ETN0001NP");
        accessCodes.add("园区建行-康阳路390号公安局/ETN0001NP");
        accessCodes.add("武汉南京ETN0042NP");
        accessCodes.add("省建行-建设银行中山南路499号/ETN0001NP");
        accessCodes.add("宿迁市建设银行-宿迁市财政局/ETN0002NP");
        accessCodes.add("园区建行-建设张家港支行(M50462963)/ETN0001NP");
        accessCodes.add("园区建行-黄埭镇为民服务中心ATM/ETN0001NP");
        accessCodes.add("HLWZX51820210310109");
        accessCodes.add("省建设银行-建行经贸学院/ETN0001NP");
        accessCodes.add("112011837");
        accessCodes.add("北京苏州ZQOTN00032");
        accessCodes.add("园区建行-阳澄湖镇人民医院/ETN0001NP");
        accessCodes.add("市政府数据资源中心-建行营业部/ETN0001NP");
        accessCodes.add("中信银行-市财政局/ETN0001NP");
        accessCodes.add("省建行-省工商行政管理局/ETN0001NP");
        accessCodes.add("省建行-建设银行北京东路71号/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3808NP");
        accessCodes.add("中信银行-中信银行江东中路国睿大厦/ETN0001NP");
        accessCodes.add("9198781");
        accessCodes.add("省建行-建设银行大明路282号/ETN0001NP");
        accessCodes.add("南京泰州ETN0607NP");
        accessCodes.add("园区建行-建行吴中区政府食堂右侧ATM(M40069893)2M");
        accessCodes.add("苏州-昆山/ETN3529NP");
        accessCodes.add("园区建行-建行相城区财政局驻点(M40059216)2M");
        accessCodes.add("建行维扬支行-邗江公积金/ETN0001NP");
        accessCodes.add("中信银行-新立基大厦2层中信银行/ETN0001NP");
        accessCodes.add("省建行-省建行铁心桥分理处/ETN0001NP");
        accessCodes.add("南京盐城ETN0622NP");
        accessCodes.add("中信银行-自来水公司(M44462968)/ETN0001NP");
        accessCodes.add("住房公积金管理中心-赣榆县青口镇黄海路公积金管理中心/ETN0001NP");
        accessCodes.add("南京泰州ETN0601NP");
        accessCodes.add("中信实业银行-中信银行泰兴营业部/ETN0001NP");
        accessCodes.add("中信银行镇江分行-镇江市华润燃气有限公司/ETN0001NP");
        accessCodes.add("园区建行-常熟职教中心(M43677956)/ETN0001NP");
        accessCodes.add("南京镇江ETN6034NPH");
        accessCodes.add("南京淮安ETN0588NP");
        accessCodes.add("苏州-昆山/ETN3646NP");
        accessCodes.add("园区建行-南施街电信3楼IDC机房(M40064028)2M");
        accessCodes.add("154317412");
        accessCodes.add("118705171");
        accessCodes.add("南京泰州ETN6054NPH");
        accessCodes.add("建设银行洪武路188号-建设银行奥体大街177号/ETN0002NP");
        accessCodes.add("园区建行-吴江亨通集团食堂自助(M40062522)");
        accessCodes.add("江苏省财政厅-省建行/ETN0003NP");
        accessCodes.add("151052210");
        accessCodes.add("149485991");
        accessCodes.add("泰州泰州ETN6326NPH");
        accessCodes.add("龙江电信机房中信-六合雄州街道中信银行/ETN0001NP");
        accessCodes.add("上海南京ZQOTN00055");
        accessCodes.add("园区建行-建行车坊支行(M40064193)2M");
        accessCodes.add("园区建行-建行东山分理处(M40053067)/ETN0001NP");
        accessCodes.add("南京泰州ETN0603NP");
        accessCodes.add("省建设银行-领智路56号/ETN0001NP");
        accessCodes.add("M00228896");
        accessCodes.add("园区建行-相城公积金中心(M40059110)/ETN0002PG");
        accessCodes.add("中信银行-吴江汾湖支行(M43602977)/ETN0001NP");
        accessCodes.add("省建行-赛世香樟园/ETN0001NP");
        accessCodes.add("省建行洪武路188号-江宁东南国投智汇路300号/ETN0001NP");
        accessCodes.add("镇江市建设银行-扬中菲尔斯金陵酒店有限公司/ETN0001NP");
        accessCodes.add("园区建行-苏大文正学院创意中心ATM(M51492966)/ETN0001NP");
        accessCodes.add("中信银行-中信银行溧阳中关村苏控集团/ETN0001NP");
        accessCodes.add("中信银行-中信银行北京西路支行/ETN0001NP");
        accessCodes.add("省建行-游府西街工商银行/ETN0001NP");
        accessCodes.add("建设银行-淮阴区卫校财务部ETN0001NP");
        accessCodes.add("M15831291");
        accessCodes.add("HLWZX523202309102743");
        accessCodes.add("清河市民服务中心工商-建行中北支行ETN0001NP");
        accessCodes.add("省建行-浦口区财政局/ETN0001NP");
        accessCodes.add("南京海盐ETN0001NP");
        accessCodes.add("154317485");
        accessCodes.add("园区建行-东平街建设银行/ETN0001NP");
        accessCodes.add("泰兴中信银行-住房公积金管理中心/ENT0001NP");
        accessCodes.add("HLWZX025202312190497");
        accessCodes.add("园区建行-建行园区个贷中心(M40692959)2M");
        accessCodes.add("南京泰州ETN6029NPH");
        accessCodes.add("园区建行-园区公积金管理中心/ETN0001NP");
        accessCodes.add("园区建行-清源华衍水务(M40032897)2M");
        accessCodes.add("省建行-高淳县淳溪镇汶溪路287号中医院机房/ETN0001NP");
        accessCodes.add("中国建设银行徐州分行-丰县财政局/ETN0001NP");
        accessCodes.add("园区建行-枫桥人民医院4楼(M48252960)/ETN0001NP");
        accessCodes.add("市纪委-建设银行/ETN0001NP");
        accessCodes.add("M00113194A-M00113194Z");
        accessCodes.add("154317398");
        accessCodes.add("公积金-中信银行ETN0001NP");
        accessCodes.add("新沂建行公积金-国土局");
        accessCodes.add("南京盐城ETN6117NPH");
        accessCodes.add("省建行-建设银行水阁路33号空军装备部/ETN0001NP");
        accessCodes.add("M00115337A-M00115337Z");
        accessCodes.add("车管所-建行/ETN0001NP");
        accessCodes.add("园区建行-电信14楼通缴(M48892956)/ETN0001NP");
        accessCodes.add("中信银行-张家港沙洲湖科创园新点软件(M65968164)/ETN0001NP");
        accessCodes.add("园区建行-太仓大庆锦绣新城深柳苑网点(M42657962)2M");
        accessCodes.add("SWETN2924NP(建行-亨通集团M40051871)2M");
        accessCodes.add("园区建行-建行苏街商务中心B楼ATM(M40068362)2M");
        accessCodes.add("140050471");
        accessCodes.add("中信银行-智慧常熟机房(M55617966)2M/ETN0001NP");
        accessCodes.add("M07036501A-M07036501Z");
        accessCodes.add("南京扬州ETN0409NP");
        accessCodes.add("邳州建行电子政务(M00261002)/2M01IP");
        accessCodes.add("宿迁市建设银行-湖滨新区公安分局/ETN0001NP");
        accessCodes.add("靖江靖江STN000077");
        accessCodes.add("中信银行-电信龙江机房/ETN0002NP");
        accessCodes.add("园区建行-市民卡海棠街机房(M53252963)/ETN0001NP");
        accessCodes.add("M00115213A-M00115213Z");
        accessCodes.add("M00101792");
        accessCodes.add("园区建行-建行苏蠡支行(M40056814)/ETN0001NP");
        accessCodes.add("江苏省财政厅-省建行/ETN0002NP");
        accessCodes.add("中信银行-太仓支行(M43612995)/ETN0001NP");
        accessCodes.add("南京灌南ETN0004NP");
        accessCodes.add("省建行-建设银行嘉陵江东街8号/ETN0001NP");
        accessCodes.add("M07268843A-M07268843Z");
        accessCodes.add("银联-省肿瘤医院/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3268NP");
        accessCodes.add("盐城中信银行-盐城公积金管理中心/ETN0001NP");
        accessCodes.add("Z1229A-Z1229Z");
        accessCodes.add("南京徐州ETN0778NP");
        accessCodes.add("建行泰州总行-建行财富中心（双提升）");
        accessCodes.add("园区建行-建行独墅湖高教区支行(M40060003)2M");
        accessCodes.add("M00231359");
        accessCodes.add("M00228902");
        accessCodes.add("建设银行南京分行-财政局/ETN0001NP");
        accessCodes.add("市住房公积金管理中心-连云区住房公积金管理中心/ETN0001NP");
        accessCodes.add("省建行-人社厅/ETN0001NP");
        accessCodes.add("南京CTVPN19429A");
        accessCodes.add("中信银行-昆山公共资源交易中心/ETN0001NP");
        accessCodes.add("常州建行-常州市劳动社会保险中心/ETN0001NP");
        accessCodes.add("园区建行-建行跨塘支行/ETN0001NP");
        accessCodes.add("苏州-常熟/ETN4754NP");
        accessCodes.add("12264774");
        accessCodes.add("中信银行-海棠街18号(M55052964)/ETN0001NP");
        accessCodes.add("K00064");
        accessCodes.add("中信银行-太仓太平南路信息港(M54147973)/ETN0001NP");
        accessCodes.add("园区建行-园区管委会建行ATM/ETN0002NP");
        accessCodes.add("园区建行-建行滟河湾花园支行(M45527971)/ETN0001NP");
        accessCodes.add("省建行-建行西康路1号/ETN0001NP");
        accessCodes.add("园区建行-公积金服务大厅(M50642963)/ETN0001NP");
        accessCodes.add("园区建行-常熟风范电力新厂区/ETN0001NP");
        accessCodes.add("建行-昆山花桥公积金中心(MSTPJS1002174683)/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3804NP");
        accessCodes.add("中信银行-中信江都龙川支行/ETN0001NP");
        accessCodes.add("南京苏州ETN1884NP");
        accessCodes.add("省建行-国际青年会议酒店/ETN0001NP");
        accessCodes.add("M00005643A-M00005643Z");
        accessCodes.add("苏州建行-昆山青阳支行(M40058097)/N001");
        accessCodes.add("宿迁市建设银行-宿迁建行老大楼四楼专线/ETN0001NP");
        accessCodes.add("15958945");
        accessCodes.add("华润燃气有限公司-中信银行/ETN0002NP");
        accessCodes.add("省建行-建设银行理工大新财务大楼/ETN0001NP");
        accessCodes.add("省建行-建设银行电信苜蓿园局3层/ETN0001NP");
        accessCodes.add("园区建行-龙山路45号IDC/ETN0001NP");
        accessCodes.add("园区建行-常熟鸳鸯工业园/ETN0001NP");
        accessCodes.add("银联二长五楼-中信银行/ETN0001NP");
        accessCodes.add("建行-建行溧阳中关村苏控集团/ETN0001NP");
        accessCodes.add("公安局-园区建行/ETN0001NP");
        accessCodes.add("中信银行-宜兴国土局土地交易中心/ETN0001NP");
        accessCodes.add("连云港东海STN000136");
        accessCodes.add("省建设银行-六合区华东路2号西门/ETN0001NP");
        accessCodes.add("L40401060004");
        accessCodes.add("南京句容ETN0028NP");
        accessCodes.add("南京苏州ETN1761NP");
        accessCodes.add("省建设银行-江宁区财政局/ETN0001NP");
        accessCodes.add("建行-高邮公司/ETN0001NP");
        accessCodes.add("南京扬州ETN0415NP");
        accessCodes.add("盐城盐城ETN6184NPH");
        accessCodes.add("中信银行-中信银行雨花支行/ETN0001NP");
        accessCodes.add("园区建行-苏州社保局(M47922963)/ETN0001NP");
        accessCodes.add("省建行-农业银行江东中路49号/ETN0001NP");
        accessCodes.add("省烟草联通新时空大厦-中信银行/ETN0001NP");
        accessCodes.add("南京南通ETN0067NP（建行）");
        accessCodes.add("MSTPJS1002983342");
        accessCodes.add("HLWZX025202409208481");
        accessCodes.add("园区建行-昆山电信IDC机房/ETN0001NP");
        accessCodes.add("12264878");
        accessCodes.add("南京淮安ETN0577NP");
        accessCodes.add("中信银行-锦峰大厦A区1楼/ETN0002NP");
        accessCodes.add("SKETN3905NP(中信-新至升M40046304)2M");
        accessCodes.add("M00215099");
        accessCodes.add("M40001997A-M40001997Z");
        accessCodes.add("SWETN2933NP(建行-厍星路南路M40052219)4M");
        accessCodes.add("中信银行-城中支行/ETN0001NP");
        accessCodes.add("12264565");
        accessCodes.add("建行-港华燃气/ETN0001NP");
        accessCodes.add("园区建行-常熟永旺梦城/ETN0001NP");
        accessCodes.add("中国建设银行股份有限公司宿迁分行-中国建设银行股份有限公司宿迁分行(M00262453)/N001");
        accessCodes.add("园区建行-建行唯泰精品购物村/ETN0001NP");
        accessCodes.add("M00215123A-M00215123Z");
        accessCodes.add("苏州-常熟/ETN4838NP");
        accessCodes.add("M00218552");
        accessCodes.add("M40065336(建行太平南路-苏州)2M");
        accessCodes.add("南京徐州ETN0713NP");
        accessCodes.add("中信银行股份有限公司镇江分行-扬中市财政局/ETN0001NP");
        accessCodes.add("15958984");
        accessCodes.add("省建行-鹿鸣大道33号南京机电职业技术学院/ETN0001NP");
        accessCodes.add("市建设银行-建行新沂财政局/ETN0001NP");
        accessCodes.add("宿迁市建设银行-个贷电子政务网专线/ETN0001NP");
        accessCodes.add("M00115216A-M00115216Z");
        accessCodes.add("154318090");
        accessCodes.add("园区建行-冯埂上游客服务中心/ETN0001NP");
        accessCodes.add("M00113225A-M00113225Z");
        accessCodes.add("M00235237A-M00235237Z");
        accessCodes.add("建行城建集团-江苏省建行/ETN0001NP");
        accessCodes.add("园区建行-建行市政府食堂ATM(M40058610)2M");
        accessCodes.add("苏州-张家港/ETN3836NP");
        accessCodes.add("省建行-建设银行茅亭路63号禄口分理处/ETN0001NP");
        accessCodes.add("SCETN4828NP(建行-东南开发区支行M40054545)");
        accessCodes.add("南京徐州ETN0345NP");
        accessCodes.add("中信银行-工学院新校区财务ETN0001NP");
        accessCodes.add("省建行-江浦街道象山路5号/ETN0001NP");
        accessCodes.add("省建行-建设银行将军大道166号管委会/ETN0001NP");
        accessCodes.add("园区建行-建行用直支行(M40053064)/ETN0001NP");
        accessCodes.add("园区建行-建设投资发展/ETN0001NP");
        accessCodes.add("镇江市建设银行-丹阳医保中心/ETN001NP");
        accessCodes.add("M00231394A-M00231394Z");
        accessCodes.add("东台市建设建行-东台市国土局(不动产)(M52026920)");
        accessCodes.add("SZETN3813NP");
        accessCodes.add("中信银行-昆山花桥康桥学校ATM(M42917959)2M");
        accessCodes.add("M00113240A-M00113240Z");
        accessCodes.add("15958910");
        accessCodes.add("中信银行长江新村2号-中信银行中信大厦/ETN0001NP");
        accessCodes.add("南京银行邺城路3号-中信银行/ETN0001");
        accessCodes.add("省建行-六合大厦中国建设银行/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3810NP");
        accessCodes.add("中信银行-兆润财富中心中信银行/ETN0002NP");
        accessCodes.add("北京苏州ZQOTN00030");
        accessCodes.add("省建行-省第二中医院/ETN0001NP");
        accessCodes.add("192286003");
        accessCodes.add("中信银行-相城公积金(M52292969)/ETN0001NP");
        accessCodes.add("园区建行-科技城医院(M46452956)/ETN0001NP");
        accessCodes.add("M00106848");
        accessCodes.add("苏州-常熟/ETN4808NP");
        accessCodes.add("南京扬州ETN6217NPH");
        accessCodes.add("常州建行-金坛第二人民医院建行点/ETN0001NP");
        accessCodes.add("园区建行-太仓中医院新大楼/ETN0001NP");
        accessCodes.add("北京苏州ETN2168NP");
        accessCodes.add("省建行-建设银行延安北路3号/ETN0001NP");
        accessCodes.add("南通中信银行-海门中信银行/30N0001NP");
        accessCodes.add("园区建行-郭新东路建行(M54462974)/ETN0001NP");
        accessCodes.add("园区建行-吴江松陵城南支行(M40053822)10M");
        accessCodes.add("园区建行-昆山西部服务中心/ETN0001NP");
        accessCodes.add("中信银行-宜兴环科园自助/ETN0001NP");
        accessCodes.add("12265334");
        accessCodes.add("省建行-建行扬子十村支行/ETN0001NP");
        accessCodes.add("SCETN4816NP(建行-东南理工学院ATMM40053858)4M");
        accessCodes.add("园区建行-供电公司/ETN0002NP");
        accessCodes.add("中信银行-滨江学院第二自助/ETN0001NP");
        accessCodes.add("市民卡-中信银行/ETN0001NP");
        accessCodes.add("SCETN4822NP(建行-新莲路分理处M40054176)");
        accessCodes.add("连云港连云港STN319247");
        accessCodes.add("建设银行-吴江中达资讯机房建行(M52652966)/ETN0001NP");
        accessCodes.add("南京扬州ETN0352NP");
        accessCodes.add("12264816");
        accessCodes.add("建设银行-建行灵谷寺路8号/ETN0001NP");
        accessCodes.add("南通中信银行-海安财政局/ETN0001NP");
        accessCodes.add("省建行-溧水人民医院/ETN0001NP");
        accessCodes.add("M00231385A-M00231385Z");
        accessCodes.add("市住房公积金-东海县住房公积金ETN0001NP(M48513476）");
        accessCodes.add("M07000611A-M07000611Z");
        accessCodes.add("人社厅-建设银行直属支行云南路31号/ETN0001NP");
        accessCodes.add("江省国税局浦江路30号-中信银行/ETN0001NP");
        accessCodes.add("中信银行-相城水务(M44463010)/ETN0001NP");
        accessCodes.add("北京南京ETN2560NP");
        accessCodes.add("扬州扬州ETN6056NPH");
        accessCodes.add("SKETN3707NP(中信-宏致电子M40042439)2M");
        accessCodes.add("南京徐州ETN0771NP");
        accessCodes.add("中信银行-中信银行马群街169号/30N0001NP");
        accessCodes.add("园区建行-建行东渚支行(M40052550)/ETN0001NP");
        accessCodes.add("省建行-中国建设银行科巷10号/ETN0001NP");
        accessCodes.add("省建行-省建行五龙桥分理处/ETN0002NP");
        accessCodes.add("江苏省财政厅-中信银行南京分公司/ETN0001NP");
        accessCodes.add("大屯建行-大屯公积金/ETN0001NP");
        accessCodes.add("HLWZX025202405200108");
        accessCodes.add("HLWZX025202311189169");
        accessCodes.add("中信银行-吴中区房管局(M44463004)/ETN0001NP");
        accessCodes.add("中信实业银行-中信银行姜堰支行/ETN0001NP");
        accessCodes.add("南通中信银行-如皋财政局/ETN0001NP");
        accessCodes.add("15959606");
        accessCodes.add("15958859");
        accessCodes.add("省建行-建行建设银行锁金村/ETN0001NP");
        accessCodes.add("南通建行-如皋自来水厂龙泉检测大楼/ETN0001NP");
        accessCodes.add("园区建行-海棠街18号电信IDC机房/ETN0001NP");
        accessCodes.add("建行-国安局解放东路/ETN0001NP");
        accessCodes.add("SCETN4800NP(建行-城东支行M40053170)");
        accessCodes.add("南京徐州ETN0719NP");
        accessCodes.add("苏州-张家港/ETN3878NP");
        accessCodes.add("M00106377");
        accessCodes.add("省建行-新晨国际大厦/ETN0001NP");
        accessCodes.add("南京扬州ETN0348NP");
        accessCodes.add("省建行-中山北路260号/ETN0001NP");
        accessCodes.add("中国建设银行股份有限公司盐城分行-中国建设银行股份有限公司盐城分行(M52956925)/N001");
        accessCodes.add("南京宿迁ETN0431NP");
        accessCodes.add("园区建行-常熟长江路266号综合机房/ETN0001NP");
        accessCodes.add("南京无锡ETN6122NPH");
        accessCodes.add("M07259977A-M07259977Z");
        accessCodes.add("建行-文化广场/ETN0001NP");
        accessCodes.add("园区建行-建行固锝电子公司食堂ATM(M40062145)2M");
        accessCodes.add("省建设银行-双牌石大街自助区/ETN0001NP");
        accessCodes.add("北京苏州ETN2033NP");
        accessCodes.add("南京无锡ETN0974NP");
        accessCodes.add("南通建行-如东建行人民北路/ETN0001NP");
        accessCodes.add("中信银行-常熟中信银行(M57342965)10M/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3255NP");
        accessCodes.add("中信银行-张家港中医院(M45772956)/ETN0001NP");
        accessCodes.add("江苏石油大厦6楼-省建行/ETN0001NP");
        accessCodes.add("园区建行-苏州银行/ETN0001NP");
        accessCodes.add("HLWZX515202401121920");
        accessCodes.add("1100410859");
        accessCodes.add("南京泰州ETN0612NP");
        accessCodes.add("省建行-浦口南京工业大学/ETN0001NP");
        accessCodes.add("中信银行徐州分行-新城区财政局/ETN0001NP");
        accessCodes.add("苏州-常熟/ETN4837NP");
        accessCodes.add("M00176786");
        accessCodes.add("154317911");
        accessCodes.add("15958828");
        accessCodes.add("园区建行-吴江盛虹集团/ETN0001NP");
        accessCodes.add("建设银行-建行常高新大厦/ETN0002NP");
        accessCodes.add("建行-建行地铁大厦控制/ETN0001NP");
        accessCodes.add("M07241131A-M07241131Z");
        accessCodes.add("15169884");
        accessCodes.add("劳保局-中信银行/ETN0001NP");
        accessCodes.add("园区建行-海棠街18号IDC(M54107973)/ETN0001NP");
        accessCodes.add("建行-建行通用自来水/ETN0001NP");
        accessCodes.add("园区建行-行政服务中心(M55212964)/ETN0001NP");
        accessCodes.add("中信银行-天一中学自助/ETN0002NP");
        accessCodes.add("中信银行-张家港金港支行(M43607956)/ETN0001NP/");
        accessCodes.add("192245864");
        accessCodes.add("南京泰州ETN0581NP");
        accessCodes.add("园区建行-张家港沙钢财务公司/ETN0001NP");
        accessCodes.add("14724572");
        accessCodes.add("M40003302A-M40003302Z");
        accessCodes.add("M00224018A-M00224018Z");
        accessCodes.add("南京扬州ETN0379NP");
        accessCodes.add("园区建行-吴江云梨路管委会大楼/ETN0001NP");
        accessCodes.add("K00952");
        accessCodes.add("省建行-建设银行南大仙林校区24幢1层/ETN0001NP");
        accessCodes.add("园区建行-建行中华园大饭店ATM(M40058455)2M");
        accessCodes.add("泰州泰州STN000383");
        accessCodes.add("苏州-昆山/ETN0001NP");
        accessCodes.add("北京连云港ETN2022NP");
        accessCodes.add("园区建行-建行新康分理处/ETN0001NP");
        accessCodes.add("如东农商行燎原-如东中信银行/ETN0001NP");
        accessCodes.add("053传输终端-054传输终端");
        accessCodes.add("KF00080");
        accessCodes.add("省建设银行-公共资源交易中心/ETN0001NP");
        accessCodes.add("省建行-淳县淳溪镇宝塔路77号二楼建行/ETN0001NP");
        accessCodes.add("HLWZX51820201205631");
        accessCodes.add("154318085");
        accessCodes.add("省建行-建宁路分理处/ETN0001NP");
        accessCodes.add("建设银行-建行稻香路/ETN0001NP");
        accessCodes.add("M07034572A-M07034572Z");
        accessCodes.add("园区建行-建行相城支行营业部/ETN0001NP");
        accessCodes.add("省建行-建行中央路42号省卫生厅/ETN0001NP");
        accessCodes.add("中信银行-中信银行龙江电信机房/ETN0001NP");
        accessCodes.add("M00236464");
        accessCodes.add("省建行--城建集团中央路256号/ETN0001NP");
        accessCodes.add("中信银行镇江分行-中信银行句容支行/ETN0001NP");
        accessCodes.add("园区建行-平路公安局主楼A幢1楼(M53202979)/ETN0001NP");
        accessCodes.add("南京扬州ETN0424NP");
        accessCodes.add("建设银行-省通服/ETN0001NP");
        accessCodes.add("中信银行-中信银行吴中支行(M43592982)/ETN0001NP");
        accessCodes.add("M00218916A-M00218916Z");
        accessCodes.add("中核生态环境有限公司-中核二四公司/ETN0001NP");
        accessCodes.add("M00113146A-M00113146Z");
        accessCodes.add("M00002909A-M00002909Z");
        accessCodes.add("省建行-高淳区国土局/ETN0001NP");
        accessCodes.add("中信银行-中信银行江阴周庄支行/ETN0001NP");
        accessCodes.add("14470456");
        accessCodes.add("省国税局浦江路30号-电信二长机房/ETN0001NP");
        accessCodes.add("车管所-中信元丰天宁支车管/ETN0001NP");
        accessCodes.add("苏州-常熟/ETN4763NP");
        accessCodes.add("MSTPJS1002986020");
        accessCodes.add("中信银行-昆山电信IDC机房B02机柜/ETN0001NP");
        accessCodes.add("Z12243A-Z12243Z");
        accessCodes.add("园区建行-海棠街18号210IDC机房(M51462963)/ETN0001NP");
        accessCodes.add("园区建行-太仓苏创实业集团(M42957956)2M");
        accessCodes.add("中信银行-人行营管部716室/ETN0001NP");
        accessCodes.add("M07024978A-M07024978Z");
        accessCodes.add("园区建行-建行国开行苏州软件开发中心ATM(M40070587)2M");
        accessCodes.add("南京泰州ETN0634NP");
        accessCodes.add("苏州-张家港/ETN3840NP");
        accessCodes.add("建行-建行文澜路99号自助/ETN0001NP");
        accessCodes.add("中信银行南京分公司-江苏省人力资源和社会保障厅/ETN0001NP");
        accessCodes.add("建行-建行轨道维修/ETN0001NP");
        accessCodes.add("中信银行-常熟东南支行(M43602959)/ETN0001NP");
        accessCodes.add("园区建行-吴江恒力集团(M46377957)/ETN0001NP");
        accessCodes.add("南京扬州ETN413NP");
        accessCodes.add("市建设银行-中和大厦不动产/ETN0001NP");
        accessCodes.add("省建行-建行东大九龙湖校区/ETN0001NP");
        accessCodes.add("园区建行-建行三香路支行/ETN0001NP");
        accessCodes.add("MSTPJT1001982781");
        accessCodes.add("M00234268A-M00234268Z");
        accessCodes.add("省银保监局-中信银行/ETN0001NP");
        accessCodes.add("省建行-江北新区市民中心/ETN0001NP");
        accessCodes.add("建设银行-吴江松陵行政大楼(M40059874)2M");
        accessCodes.add("建设银行-新区锦峰大厦/ETN0001NP");
        accessCodes.add("M00225022");
        accessCodes.add("M00218904");
        accessCodes.add("园区建行-常熟白茆工业园波司登(M40352962)2M");
        accessCodes.add("武汉苏州ETN6001NPH");
        accessCodes.add("省建设银行大楼-杨梅山部队机房/ETN0001NP");
        accessCodes.add("苏州-吴江/ETN2983NP");
        accessCodes.add("中信银行-常熟海虞北路支行(M43602965)/ETN0001NP");
        accessCodes.add("园区建行-吴江震泽镇横街(M40059750)2M");
        accessCodes.add("盐城-建湖/ETN0054NP");
        accessCodes.add("园区建行-太仓东仓南路建行(M50202973)/ETN0001NP");
        accessCodes.add("SCETN4823NP(建行-城南支行M40054173)");
        accessCodes.add("省建行-建行东南大学九龙湖/ETN0001NP");
        accessCodes.add("省建行-建设银行上河街魔法新城项目/ETN0001NP");
        accessCodes.add("园区建行-吴江开发区云梨路运东支行(M49402959)10M");
        accessCodes.add("园区建行-相城自来水公司(M40367956)2M");
        accessCodes.add("中信银行-中信银行观山路支行/ETN0001NP");
        accessCodes.add("中信银行-中信惠山分理处/ETN0001NP");
        accessCodes.add("园区建行-昆山杜克大学1号楼ATM(M41842956)2M");
        accessCodes.add("苏州-吴江/ETN2970NP");
        accessCodes.add("省建行-建设银行高庙路56号/ETN0001NP");
        accessCodes.add("园区建行(CYQJH)-建行中山路储蓄所/ETN0001NP");
        accessCodes.add("江苏省建行-南京群耀智晟信息科技/ETN0001NP");
        accessCodes.add("中信银行镇江分行-丹阳房管局物业管理基金/ETN0001NP");
        accessCodes.add("园区建行-太仓太平南路社保局(M54357966/ETN0001NP");
        accessCodes.add("园区建行-建行理想城支行/ETN0001NP");
        accessCodes.add("省建行-镇兴路208号建设银行/ETN0001NP");
        accessCodes.add("省建行-建行直属支行/ETN0001NP");
        accessCodes.add("14649940");
        accessCodes.add("南通建行-如皋建行档案馆/ETN0001NP");
        accessCodes.add("省建行-通行宝智慧交通/ETN0001NP");
        accessCodes.add("南京扬州ETN0449NP");
        accessCodes.add("园区建行-越溪国土资源局(M54977966)/ETN0001NP");
        accessCodes.add("园区建行-昆山华润万象汇/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3990NP");
        accessCodes.add("省建设银行洪武路188号-水利科学研究院广州路225号/ETN0002NP");
        accessCodes.add("省建行-建行大厂支行/ETN0001NP");
        accessCodes.add("K02806");
        accessCodes.add("园区建行-建行通安支行(M40052547)/ETN0001NP");
        accessCodes.add("省建行-建设银行浦滨路扬子科创中心/ETN0001NP");
        accessCodes.add("建行春江花城分理处-二手房交易中心/ETN0001NP");
        accessCodes.add("南京镇江ETN0577NP");
        accessCodes.add("M07257678");
        accessCodes.add("南通建行-南通日报社财政局/ETN0001NP");
        accessCodes.add("园区建行-常熟自来水/ETN0001NP");
        accessCodes.add("HLWZX52320230798783");
        accessCodes.add("M07020962A-M07020962Z");
        accessCodes.add("园区建行(CYQJH)-建行南环分理处/ETN0001NP");
        accessCodes.add("中信银行-昆山珠江北路128号中信(M40577962)10M");
        accessCodes.add("省建行-建行江宁水务集团/ETN0001NP");
        accessCodes.add("中信银行-中信银行南京分行汉中路89号南京金鹰国际/30N0001NP");
        accessCodes.add("南京泰州ETN0592NP");
        accessCodes.add("南京无锡ETN0901NP");
        accessCodes.add("154317475");
        accessCodes.add("省建行-三金燕语庭门面房/ETN0001NP");
        accessCodes.add("中信银行-电信14楼通缴(M44462962)/ETN0001NP");
        accessCodes.add("MSTPJS1003348549");
        accessCodes.add("园区建行-建行金阊新城支行(M40072487)2M");
        accessCodes.add("园区建行-建行附一院问讯处ATM(M40062011)2M");
        accessCodes.add("园区建行-建行赣江支行储蓄专柜(M40052589)10M");
        accessCodes.add("省建行-南京群耀智晟信息科技/ETN0001NP");
        accessCodes.add("南京南通ETN6031NPH");
        accessCodes.add("省建设银行-中泰国际广场华东石油局/ETN0001NP");
        accessCodes.add("天津苏州ETN6001NPH");
        accessCodes.add("苏州-昆山/ETN3932NP");
        accessCodes.add("南通中信银行-南通中信银行信用卡部/ETN0001NP");
        accessCodes.add("141983640");
        accessCodes.add("中信银行-中信银行安德门大街56号/ETN0001NP");
        accessCodes.add("南京CTVPN189A028");
        accessCodes.add("省建行-金融城10号楼/ETN0001NP");
        accessCodes.add("南京CTVPN189A029");
        accessCodes.add("建设银行-玄武大厦/ETN0001NP");
        accessCodes.add("中信银行-市房管局产监处/ETN0001NP");
        accessCodes.add("中信银行镇江分行-檀山路中信银行/ETN0001NP");
        accessCodes.add("12264731");
        accessCodes.add("市公积金-民生大厦公积金ETN0001NP");
        accessCodes.add("园区建行-张家港海关路建行(M47712978)/ETN0001NP");
        accessCodes.add("省建行-自来水总公司/ETN0001NP");
        accessCodes.add("HLWZX51820230262186");
        accessCodes.add("省建行-江宁江苏经贸职业技术学院/ETN0001NP");
        accessCodes.add("省建行-建设银行南京同仁医院康复中心/ETN0001NP");
        accessCodes.add("园区建行-建行北桥分理处(M40052985)/ETN0001NP");
        accessCodes.add("园区建行-建行吴中人民医院门诊大厅ATM(M40060063)2M");
        accessCodes.add("苏州-昆山/ETN4001NP");
        accessCodes.add("省建行-中国建设银行秦淮区解放路53号/ETN0001NP");
        accessCodes.add("如皋建行-如皋市民中心档案馆/ETN0001NP");
        accessCodes.add("北京南京ETN2587NP");
        accessCodes.add("苏州-昆山/ETN4009NP");
        accessCodes.add("中信银行-浦口区江浦街道新浦路127号/ETN0001NP");
        accessCodes.add("南京苏州ETN0224NP");
        accessCodes.add("园区建行-供电公司/ETN0001NP");
        accessCodes.add("园区建行-吴中区财政局(M53427982)/ETN0001NP");
        accessCodes.add("园区建行-嘉业阳光假日新苑建行ATM/ETN0002NP");
        accessCodes.add("省建行-明基医院/ETN0001NP");
        accessCodes.add("南京南通ETN0478NP");
        accessCodes.add("中信银行-公积金管理中心/ETN0001NP");
        accessCodes.add("连云港嘉兴ZQOTN00001");
        accessCodes.add("南京扬州ETN0332NP");
        accessCodes.add("建设银行-建行武进医院自助银行/ETN0001NP");
        accessCodes.add("省建行-省委安全厅/ETN0001NP");
        accessCodes.add("中信银行-绿点科技老厂自助（1号门门卫旁ATM机）/ETN0001NP");
        accessCodes.add("省建行-南京市民卡/ETN0001NP");
        accessCodes.add("南京连云港ETN0534NP");
        accessCodes.add("M00215102");
        accessCodes.add("南京启东ETN0013NP");
        accessCodes.add("HLWZX51620210829508");
        accessCodes.add("省建设银行-建行73680部队/ETN0001NP");
        accessCodes.add("省建行-建行鼓楼支行中山北路分理处/ETN0001NP");
        accessCodes.add("苏州CTVPN19429A");
        accessCodes.add("园区建行-建行房产管理局(M48142962)/ETN0001NP");
        accessCodes.add("南京徐州ETN0413NP");
        accessCodes.add("连云港连云港STN001789");
        accessCodes.add("省建行-建行鼓楼支行/ETN0001NP");
        accessCodes.add("园区建行-建行娄葑支行/ETN0001NP");
        accessCodes.add("马鞍山-省建行/ETN0001NP");
        accessCodes.add("建行-金坛自来水公司建行点/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3220NP");
        accessCodes.add("中信银行-人社局/ETN0001NP");
        accessCodes.add("建设银行-金湖金鑫公司ETN0001NP");
        accessCodes.add("M00231404");
        accessCodes.add("省建设银行-六合财政局/ETN0001NP");
        accessCodes.add("建行-平望美山子ATM/ETN0001NP");
        accessCodes.add("13849548");
        accessCodes.add("扬州江都ETN0400NP");
        accessCodes.add("中信银行徐州分行-中和大厦不动产/ETN0001NP");
        accessCodes.add("南京常州ETN0793NP");
        accessCodes.add("省建行-六合区雄州街道/ETN0001NP");
        accessCodes.add("省建行-南京汉开书院/ETN0001NP");
        accessCodes.add("南京南通ETN0928NP");
        accessCodes.add("园区建行-建行旅游财经学校ATM(M44697960)/ETN0001NP");
        accessCodes.add("M07004529A-M07004529Z");
        accessCodes.add("中信银行南京分公司-中信银行科学园格致路309号/ETN0001NP");
        accessCodes.add("园区建行-建行横泾支行(M40065916)2M");
        accessCodes.add("建行-建行环宇大厦信息/ETN0001NP");
        accessCodes.add("10570479");
        accessCodes.add("建行-建行黑龙江路28号/ETN0001NP");
        accessCodes.add("省建行-秣陵街道秣周东路12号紫金江宁5号楼/ETN0001NP");
        accessCodes.add("法院-中信银行/ETN0001NP");
        accessCodes.add("园区建行-吴江大厦信息中心(M52397969)/ETN0001NP");
        accessCodes.add("M00002931A-M00002931Z");
        accessCodes.add("154317479");
        accessCodes.add("建行海德国际府前办事处-兴化公积金中心/ETN0001NP");
        accessCodes.add("园区建行-吴江盛泽东方花园(M40049785)10M");
        accessCodes.add("泰州CTVPN189B007");
        accessCodes.add("扬州扬州ETN6210NPH");
        accessCodes.add("建行中山北路-建行中泰国际6号楼21楼/ETN0001NP");
        accessCodes.add("园区建行-常熟财政局(M51702972)/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3936NP");
        accessCodes.add("省建行-建行白云亭支行/ETN0001NP");
        accessCodes.add("南京泰州ETN0586NP");
        accessCodes.add("中信银行-苏宁广场中信银行江东北路88号/ETN0001NP");
        accessCodes.add("南京无锡ETN0993NP");
        accessCodes.add("市政府数据资源中心-建行开发区支行/ETN0001NP");
        accessCodes.add("南通中信银行-启东中信银行和平中路/ETN0001NP");
        accessCodes.add("连云港市房产局-连云港市建设银行/ETN0001NP");
        accessCodes.add("园区建行-昆山杜克大学建行ATM(M51287974)/ETN0001NP");
        accessCodes.add("建设银行-张家港永联村村委/ETN0001NP");
        accessCodes.add("SCETN4826NP(建行-沿江开发区支行M40054539)4M");
        accessCodes.add("省建行-江苏省农村信用社联合社/ETN0001NP");
        accessCodes.add("M00002837A-M00002837Z");
        accessCodes.add("M00113189A-M00113189Z");
        accessCodes.add("洪泽行政审批中心-洪泽建行ETN0001NP");
        accessCodes.add("省建行-扬子巴斯夫公司一层大厅/ETN0001NP");
        accessCodes.add("10869327");
        accessCodes.add("MSTPJS1003206446");
        accessCodes.add("泰兴公积金-泰兴建设银行/ETN0001NP");
        accessCodes.add("园区建行-建行二中心一楼/ETN0001NP");
        accessCodes.add("中信银行-仪征鼓楼自助/ETN0001NP");
        accessCodes.add("园区建行-金辉浅湾商业中心14幢103室建设银行/ETN0001NP");
        accessCodes.add("HLWZX51820220742159");
        accessCodes.add("南京银联-江苏银联商务和燕路/ETN0001NP");
        accessCodes.add("M00223098Z-M00223098A");
        accessCodes.add("建行-建行吕南路/ETN0001NP");
        accessCodes.add("13643536");
        accessCodes.add("苏州-张家港/ETN3841NP");
        accessCodes.add("省建行-建行中央门支行/ETN0001NP");
        accessCodes.add("M00004415A-M00004415Z");
        accessCodes.add("Z11427");
        accessCodes.add("苏州-吴江/ETN3048NP");
        accessCodes.add("园区建行-吴江万象汇建行(M54457967)/ETN0001NP");
        accessCodes.add("省建行-江东中路391号/ETN0001NP");
        accessCodes.add("南京徐州ETN6116NPH");
        accessCodes.add("12264757");
        accessCodes.add("建设银行-江宁/ETN0002NP");
        accessCodes.add("省建行-省级机关住房管理中心/ETN0002NP");
        accessCodes.add("建设银行-昆山花园路支行(M40059865)2M");
        accessCodes.add("HLWZX51420210936481");
        accessCodes.add("南京镇江ETN0298NP");
        accessCodes.add("中信银行泰州总行-中信银行姜堰东方不夜城支行/ETN0001NP");
        accessCodes.add("园区建行-建行苏大附一院住院部ATM(M45962956)/ETN0001NP");
        accessCodes.add("M00002865A-M00002865Z");
        accessCodes.add("银联银商机房-建行新门诊大楼5层/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN4012NP");
        accessCodes.add("镇江市建设银行-丹阳财政局/ETN0001NP");
        accessCodes.add("省建行-石佛村三宫组浦口监狱东机房1楼/ETN0001NP");
        accessCodes.add("靖江公积金管理中心-建行靖江总行/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3224NP");
        accessCodes.add("154318075");
        accessCodes.add("中信银行-发改委信息中心/ETN0001NP");
        accessCodes.add("园区建行-太平街道卫生院/ETN0001NP");
        accessCodes.add("园区建行-张家港新电大创业园建设自助银行电话(M51972963)/ETN0002NP");
        accessCodes.add("154318308");
        accessCodes.add("园区建行-昆山汉浦路淳华科技建行/ETN0001NP");
        accessCodes.add("园区建行-吴江盛泽支行(M40045293)20M");
        accessCodes.add("中信银行-滨江学院自助/ETN0003NP");
        accessCodes.add("建行-房产信息中心/ETN0001NP");
        accessCodes.add("中信银行-中信银行海关大楼ATM(M41687981)2M");
        accessCodes.add("园区建行-建行吴中开发区支行(M40053028)/ETN0001NP");
        accessCodes.add("160807262");
        accessCodes.add("中信银行-金螳螂/ETN0001NP");
        accessCodes.add("锡北人民医院-东港医院(建行外联)/ETN0001NP");
        accessCodes.add("12265436");
        accessCodes.add("徐州徐州STN100149");
        accessCodes.add("园区建行-建行人民路桃花坞支行(M40053302)/ETN0002NP");
        accessCodes.add("STETN3800NP(建行-浮桥办事处对公业务M40051707)4M");
        accessCodes.add("公交集团指挥中心-中信银行巨凝/ETN0001NP");
        accessCodes.add("园区建行-昆山祖冲之路(M48002956)/ETN0001NP");
        accessCodes.add("15958959");
        accessCodes.add("灌南灌南STN000054");
        accessCodes.add("南通中信银行-如皋中信银行海洋南路/ETN0002NP");
        accessCodes.add("镇江市建设银行-建设银行（丹阳公共资源交易中心)/ETN0001NP");
        accessCodes.add("省建行-金盾中心/ETN0001NP");
        accessCodes.add("建设银行-黄埭监管支队(MSTPJS1003049902)/ETN0001NP");
        accessCodes.add("省建行-建行江东中路49号二长4层/ETN0001NP");
        accessCodes.add("STETN3231NP(建行-港口网点M40047929)4M");
        accessCodes.add("M40009221A-M40009221Z");
        accessCodes.add("建设银行-凯尔博精密机械/ETN0001NP");
        accessCodes.add("南通南通ETN6402NPH");
        accessCodes.add("省建行-建设银行将军大道529号泉峰新能源/ETN0001NP");
        accessCodes.add("M00211730");
        accessCodes.add("苏州-张家港/ETN3844NP");
        accessCodes.add("南京南通ETN0122NP");
        accessCodes.add("园区建行-建行园区档案大厦房产交易中心(M41577971)2M");
        accessCodes.add("28034795");
        accessCodes.add("11643627");
        accessCodes.add("M00002943A-M00002943Z");
        accessCodes.add("北京苏州ETN2178NP");
        accessCodes.add("M00005661A-M00005661Z");
        accessCodes.add("SKETN3987NP(园区建行-友谊园分理处M40053234)10M");
        accessCodes.add("园区建行-建行水坊路邻里中心/ETN0001NP");
        accessCodes.add("市中信银行-高铁未来城交通控股/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3837NP");
        accessCodes.add("园区建行-平泷路住房公积金中心(M51702969)/ETN0001NP");
        accessCodes.add("南京镇江ETN0568NP");
        accessCodes.add("中信银行镇江分行-镇江市公交总公司/ETN0001NP");
        accessCodes.add("中信银行镇江分行-丹阳水务集团/ETN0001NP-");
        accessCodes.add("中信银行泰州总行-中信银行靖江支行/ETN0001NP");
        accessCodes.add("154317407");
        accessCodes.add("M00115219A-M00115219Z");
        accessCodes.add("中信银行-昆山开发区支行(M43612959)/ETN0001NP");
        accessCodes.add("省建行-江宁区财政局/ETN0001NP");
        accessCodes.add("国土资源局-省建行/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3888NP");
        accessCodes.add("园区建行-苏州燃气(M42407956)2M");
        accessCodes.add("南京扬州ETN0116NP");
        accessCodes.add("省建行-省建行北京西路支行/ETN0001NP");
        accessCodes.add("154317909");
        accessCodes.add("110825603");
        accessCodes.add("154318077");
        accessCodes.add("154317919");
        accessCodes.add("园区建行-建行湘城分理处(M40052994)/ETN0001NP");
        accessCodes.add("省建行-江宁金建大厦1层建行/ETN0001NP");
        accessCodes.add("14800894");
        accessCodes.add("中信银行-中信五爱支行/ETN0001NP");
        accessCodes.add("省建行-国土资源局/ETN0001NP");
        accessCodes.add("中信银行-平泷路住房公积金中心(M51697969)/ETN0001NP");
        accessCodes.add("苏州-昆山/ETN3958NP");
        accessCodes.add("苏州-太仓/ETN3221NP");
        accessCodes.add("11840022");
        accessCodes.add("中国建设银行吴江市支行(M40057979)/N001");
        accessCodes.add("建设银行-建行新丰街自助银行/ETN0001NP");
        accessCodes.add("园区建行(CYQJH)-建行东苑分理处/ETN0001NP");
        accessCodes.add("省建设银行-建行省环境保护厅环保厅/ETN0001NP");
        accessCodes.add("南京徐州ETN0703NP");
        accessCodes.add("中信银行-草场门大街/ETN0001NP");
        accessCodes.add("市建行-楚州财政局ETN0001NP");
        accessCodes.add("园区建行-石湖金陵花园酒店(M48117956)/ETN0001NP");
        accessCodes.add("15958971");
        accessCodes.add("省建行-龙袍建设银行/ETN0002NP");
        accessCodes.add("苏州-昆山/ETN4007NP");
        accessCodes.add("中信银行南京分公司-中信银行中央路258号江南大厦/ETN0001NP");
        accessCodes.add("南京扬州ETN0610NP");
        accessCodes.add("园区建行-附二医院(M52947966)/ETN0001NP");
        accessCodes.add("HLWZX51220210326959");
        accessCodes.add("园区建行-建行郭新东路/ETN0001NP");
        accessCodes.add("省建行-省建行山西路分理处/ETN0001NP");
        accessCodes.add("园区建行-建行枫桥支行(M40051722)10M");
        accessCodes.add("15958931");
        accessCodes.add("12265466");
        accessCodes.add("南京盐城ETN6112NPH");
        accessCodes.add("建设银行华南分理处－镇江建行/ETN0001NP");
        accessCodes.add("南京淮安ETN0540NP");
        accessCodes.add("南通中信银行-通州中信银行/ETN0001NP");
        accessCodes.add("建设银行-丹阳新城河分理处/ETN0001NP-");
        accessCodes.add("泰州泰州ETN6280NPH");
        accessCodes.add("中国建设银行股份有限公司镇江分行-丹阳市行政审批中心/ETN0001NP");
        accessCodes.add("苏州-常熟/ETN4833NP");
        accessCodes.add("HLWZX51020211054807");
        accessCodes.add("镇江市公共资源交易中心-中信银行股份有限公司镇江分行/ETN0001NP");
        accessCodes.add("南通中信银行-如东财政局/ETN0001NP");
        accessCodes.add("中信银行-中信银行新区支行(M43592991)/ETN0001NP");
        accessCodes.add("泰兴公积金建行点-泰兴建设银行/ETN0001NP");
        accessCodes.add("南通中信银行-南通房管局人民东路/ETN0002NP");
        accessCodes.add("苏州-张家港/ETN3843NP");
        accessCodes.add("园区建行-建行儿童医院门诊部收费处ATM(M40061999)2M");
        accessCodes.add("龙江电信机房中信-天元西路38号中信银行/ETN0001NP");
        accessCodes.add("建设银行-市房管局ETN0001NP");
        accessCodes.add("省建行-省建行和燕路分理处/ETN0002NP");
        accessCodes.add("苏州-昆山/ETN3647NP");
        accessCodes.add("L1003971");
        accessCodes.add("市住建局-中信银行ETN0001NP");
        accessCodes.add("省建行-建设银行雨润大街19号/ETN0001NP");
        accessCodes.add("建设银行省分行-建设银行三牌楼大街218号/ETN0001NP");
        accessCodes.add("省建行-梅山钢铁七号路中区食堂/ETN0001NP");
        accessCodes.add("盐城盐城STN000435");
        accessCodes.add("147785335");
        accessCodes.add("M00247595");
        accessCodes.add("苏州-常熟/ETN4831NP");
        accessCodes.add("中信银行-吴江住建局(M51127963)/ETN0001NP");
        accessCodes.add("154318310");
        accessCodes.add("南京扬州ETN0095NP1");
        accessCodes.add("中信银行-水务集团城北水厂/ETN0001NP");
        accessCodes.add("南京徐州ETN0238NP");
        accessCodes.add("省建行-鼓楼区财政局/ETN0001NP");
        accessCodes.add("中信银行-无锡百凌文件管理服务有限公司/ETN0002NP");
        accessCodes.add("中信银行-中信宜兴丁蜀支行/ETN0001NP");
        accessCodes.add("中信银行-张家港市政府机要局(M52047970)/ETN0001NP");
        accessCodes.add("建行-城建学校新校区/ETN0001NP");
        accessCodes.add("建设银行-建行技术师范学院（名都明都师源宾馆）/ETN0001NP");
        accessCodes.add("省建行-建设银行吉印大道1888号南京埃斯顿自动化/ETN0001NP");
        accessCodes.add("中信银行-中信银行相城支行(M43592985)/ETN0001NP");
        accessCodes.add("南京镇江ETN6001NPH");
        accessCodes.add("M00115328A-M00115328Z");
        accessCodes.add("南京扬州ETN6019NPH");
        accessCodes.add("HLWZX025202307169529");
        accessCodes.add("省建行-岱山路附近岱山经济适用房/ETN0001NP");
        accessCodes.add("省烟草联通新时空大厦-省建行/ETN0002NP");
        accessCodes.add("园区建行-建行新区支行/ETN0001NP");
        accessCodes.add("园区建行-常熟长江路局六楼IDC(M52267969)2M");
        accessCodes.add("中信银行-住建局(M46252968)/ETN0001NP");
        accessCodes.add("园区建行-建行望亭中医院(M47067959)/ETN0001NP");
        accessCodes.add("省建设银行-建行时代广场/ETN0001NP");
        accessCodes.add("南京泰州ETN0609NP");
        accessCodes.add("公积金管理中心-中国建设银行太平北路51号三楼/ETN0001NP");
        accessCodes.add("M00104687A-M00104687Z");
        accessCodes.add("常州中信银行-金坛中信银行/ETN0001NP");
        accessCodes.add("M00113318A-M00113318Z");
        accessCodes.add("154318088");
        accessCodes.add("省建行-省建行珠江路分理处/ETN0001NP");
        accessCodes.add("盐城建设银行-盐城公安局/ETN0001NP");
        accessCodes.add("中信银行泰州总行-中信银行新区支行/ETN0001NP");
        accessCodes.add("中国建设银行通州市支行-中国建设银行通州市支行(M00143445)/N001");
        accessCodes.add("15959654");
        accessCodes.add("园区建行-漕湖人民医院/ETN0001NP");
        accessCodes.add("园区建行-港口发展大厦30楼/ETN0001NP");
        accessCodes.add("南京宿迁ETN0412NP");
        accessCodes.add("SWETN2940NP(建行-浦南路M40052171)4M");
        accessCodes.add("建行办公室-建行办公室(M40142673)/N001");
        accessCodes.add("园区建行-建行澄和路支行(M40053022)/ETN0001NP");
        accessCodes.add("园区建行-建行科技城支行(M44222959)/ETN0001NP");
        accessCodes.add("M00111209A-M00111209Z");
        accessCodes.add("数字电路用户终端（以太网电口）-8593C5E6-工业大学逸夫图书馆3层-MSAP-E6080P");
        accessCodes.add("省建行-人社厅信息中心/ETN0001NP");
        accessCodes.add("14448181");
        accessCodes.add("数字电路用户终端（以太网电口）-59FD9B03-数字电路用户终端（以太网电口）-2ABDC471");
        accessCodes.add("中信银行-房产管理局(M44462959)/ETN0001NP");
        accessCodes.add("15959575");
        accessCodes.add("省建行-海福巷71号十四干休所/ETN0001");
        accessCodes.add("建行泰州总行-建行兴化建北储蓄所/ETN0001NP");
        accessCodes.add("省建行-建行中山支行专柜/ETN0001NP");
        accessCodes.add("园区建行-建行园区支行博世ATM/ETN0002NP");
        accessCodes.add("M00113250A-M00113250Z");
        accessCodes.add("12264825");
        accessCodes.add("住房公积金管理中心(运河东路388号)-建设银行/ETN0001NP");
        accessCodes.add("中信银行-中信银行园区金螳螂总部取款机(M40172998)2M");
        accessCodes.add("1010039");
        accessCodes.add("117212471");
        accessCodes.add("M00231412A-M00231412Z");
        accessCodes.add("12265092");
        accessCodes.add("建设银行-公积金代办ETN0001NP");
        accessCodes.add("南京南通ETN6044NPH");
        accessCodes.add("省建行-建行仙林支行/ETN0001NP");
        accessCodes.add("南通大众燃气-南通中信银行/ETN0001NP");
        accessCodes.add("HLWZX523202311105513");
        accessCodes.add("省建行-南京公共自行车中心点/ETN0001NP");
        accessCodes.add("园区建行-太仓北京东路建行(M54307966)/ETN0001NP");
        accessCodes.add("省建行-建行江东中路150号/ETN0001NP");
        accessCodes.add("建设银行省分行-发改委/ETN0001NP");
        accessCodes.add("省建行-建行体彩代办点五环大厦1层/ETN0001NP");
        accessCodes.add("M00291741");
        accessCodes.add("北京吴江ETN2007NP");
        accessCodes.add("中信实业银行-中信银行兴化支行/ETN0001NP");
        accessCodes.add("中信银行-中信银行信用卡中心/ETN0002NP");
        accessCodes.add("省建行-中国建设银行北圩路258号/ETN0001NP");
        accessCodes.add("省建行-建行中山南路支行/ETN0001NP");
        accessCodes.add("南京泰州ETN0606NP");
        accessCodes.add("园区建行-太仓市政府财政局(M42437965)2M");
        accessCodes.add("南通建行唐闸支行库房-南通建行南市街72号/ETN0001NP");
        accessCodes.add("盐城中信银行-盐城悦达集团/ETN0001NP");
        accessCodes.add("省建设银行大楼-长白街370号2楼/ETN0001NP");
        accessCodes.add("WJ.JHFZYU01/MSAP01-M00225025Z");
        accessCodes.add("常熟机要局-中信银行(MSTPJS1002284142)10M");
        accessCodes.add("M40003255A-M40003255Z");
        accessCodes.add("M00236495A-M00236495Z");
        accessCodes.add("省建行-市土地管理局/ETN0001NP");
        accessCodes.add("南京常州ETN6058NPH");
        accessCodes.add("154317905");
        accessCodes.add("M00220083");
        accessCodes.add("M00216523A-数字电路用户终端（G.703-75）-4684738B");
        accessCodes.add("中信银行-金安押运/ETN0001NP");
        accessCodes.add("中信银行-中信滨湖支行/ETN0001NP");
        accessCodes.add("省建设银行-空军气象学院/ETN0001NP");
        accessCodes.add("154317493");
        accessCodes.add("南京常州ETN6080NPH");
        accessCodes.add("园区建行-龙西路160号建设银行/ETN0001NP");
        accessCodes.add("中信银行巨凝-医疗保障局/ETN0001NP");
        accessCodes.add("园区建行-建行黄桥分理处/ETN0001NP");
        accessCodes.add("南京连云港ETN6022NPH");
        accessCodes.add("园区建行-昆山市社保局(M54977967)/ETN0001NP");
        accessCodes.add("HLWZX510202208108804");
        accessCodes.add("建设银行-华润燃气/ETN0001NP");
        accessCodes.add("园区建行-建行凤凰城分理处(M40075205)2M");
        accessCodes.add("南京连云港ETN0532NP");
        accessCodes.add("建行-联创科技大厦A座/ETN0001NP");
        accessCodes.add("园区建行-太仓东亭大厦建行(M49602968)/ETN0001NP");
        accessCodes.add("中信银行徐州分行-贾汪中专ATM机/ETN0001NP");
        accessCodes.add("园区建行-张家港永钢集团/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3223NP");
        accessCodes.add("HLWZX025202308170632");
        accessCodes.add("中信银行-天一中学信息楼/ETN0002NP");
        accessCodes.add("南京CTVPN189C031");
        accessCodes.add("园区建行-太仓东仓路建行(M50207968)/ETN0001NP");
        accessCodes.add("建设银行-智慧产业园4号机房招投标ETN0001NP");
        accessCodes.add("M00217064");
        accessCodes.add("南通中信银行-南通国土资源局跃龙路/ETN0001NP");
        accessCodes.add("南通南通STN076449");
        accessCodes.add("南京泰州ETN6098NPH");
        accessCodes.add("无锡CTVPN189A011");
        accessCodes.add("北京苏州ETN2172NP");
        accessCodes.add("南京泰州ETN6025NPH");
        accessCodes.add("中信银行-南京银行江东中路49号/ETN0001NP");
        accessCodes.add("HLWZX510202303151001");
        accessCodes.add("中信银行-草场门大街89号/ETN0001NP");
        accessCodes.add("南京泰州ETN0597NP");
        accessCodes.add("南京连云港ETN0533NP");
        accessCodes.add("中信银行-中信银行中山路228号/ETN0001NP");
        accessCodes.add("M40008006");
        accessCodes.add("苏州-昆山/ETN3963NP");
        accessCodes.add("M00221948");
        accessCodes.add("建设银行-溧阳建行中医院银医通/ETN0001NP");
        accessCodes.add("中信银行-中信惠源分理处/ETN0001NP");
        accessCodes.add("M00005664A-M00005664Z");
        accessCodes.add("中信银行-中信银行江东北路88号/ETN0001NP");
        accessCodes.add("连云港灌云STN000123");
        accessCodes.add("中信银行-江苏华侨大厦1层中信银行/ETN0001NP");
        accessCodes.add("南京连云港ETN0328NP");
        accessCodes.add("南通建行-南通汽车东站交通局/ETN0001NP");
        accessCodes.add("省建行-建设银行中山南路499号/ETN0002NP");
        accessCodes.add("镇江市建设银行-镇江广电大楼/ETN0001NP");
        accessCodes.add("省建设银行大楼-省气象局业务楼/ETN0001NP");
        accessCodes.add("中国建设银行通州市支行-中国建设银行通州市支行(M00144200)/N002");
        accessCodes.add("154317900");
        accessCodes.add("M00120464A-M00120464Z");
        accessCodes.add("建行-市民广场/ETN0001NP");
        accessCodes.add("省建行-建行溧水支行/ETN0001NP");
        accessCodes.add("南京CTVPN189A045");
        accessCodes.add("园区建行-园区金鸡湖路IDC二楼机房/ETN0001NP");
        accessCodes.add("连云港赣榆STN000128");
        accessCodes.add("建设银行-建行雷利电机/ETN0001NP");
        accessCodes.add("南通中信银行-通州中信银行世纪大道/ETN0001NP");
        accessCodes.add("SKETN3955NP(建行-花桥分理处ATMM40052276)10M");
        accessCodes.add("中信银行-吴江财政局(M40044761)2M");
        accessCodes.add("中信银行-无锡科技职业学院/ETN0001NP");
        accessCodes.add("中信银行-中信新区支行/ETN0001NP");
        accessCodes.add("常州市财政局-建设银行/ETN0001NP");
        accessCodes.add("19611809");
        accessCodes.add("906-江苏省建行-ASON2-3500-数字电路用户终端（以太网电口）-FF6C7644");
        accessCodes.add("中信银行-安井食品自助/ETN0001NP");
        accessCodes.add("南京无锡ETN0352NP");
        accessCodes.add("中信银行-中信银行大厂镇新华西路360号/30N0001NP");
        accessCodes.add("园区建行-新区财政局(M53777978)/ETN0001NP");
        accessCodes.add("中国建设银行徐州分行-沛县中学/ETN0001NP");
        accessCodes.add("M00111924");
        accessCodes.add("中信银行-中信银行园区支行(M43592988)/ETN0001NP");
        accessCodes.add("建行-市公积金结算ETN0001NP");
        accessCodes.add("中信银行股份有限公司镇江分行-信用卡中心/ETN0001NP");
        accessCodes.add("中信银行-南京证券/30N0001NP");
        accessCodes.add("园区建行-吴江松陵社保大楼(M54142964)/ETN0001NP");
        accessCodes.add("盐城市建设银行-盐城市房管局/ETN0001NP");
        accessCodes.add("SCETN4817NP(建行-元和路分理处M40053864)");
        accessCodes.add("北京南京ETN2196NP");
        accessCodes.add("省建行-银城地产股份有限公司/ETN0001NP");
        accessCodes.add("中信银行-苏州大道东中信银行/ETN0002NP");
        accessCodes.add("12264577");
        accessCodes.add("HLWZX51520221283120");
        accessCodes.add("南京CTVPN189K082");
        accessCodes.add("SD75-12997524-SD75-12997527");
        accessCodes.add("市房管局-建设银行ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3271NP");
        accessCodes.add("HLWZX515202404124975");
        accessCodes.add("建行常州分行总部-建行龙锦路分校/ETN0001NP");
        accessCodes.add("中信银行镇江分行-中信银行扬中支行(金叶大酒店)/ETN0001NP");
        accessCodes.add("省建行-亚东建设发展公司应天大街388号/ETN0001NP");
        accessCodes.add("中信银行-中信胡埭支行/ETN0001NP");
        accessCodes.add("中信银行-商业大厦/ETN0001NP");
        accessCodes.add("中信银行-中信银行宜兴支行/ETN0003NP");
        accessCodes.add("市朝阳中路住房公积金-灌云县住房公积金ETN0001NP(M48468476)");
        accessCodes.add("建行-建行中天钢铁南厂区/ETN0001NP");
        accessCodes.add("常州建行-建行政务云/ETN0001NP");
        accessCodes.add("154317913");
        accessCodes.add("南京淮安ETN6046NPH");
        accessCodes.add("园区建行-建行城中支行/ETN0001NP");
        accessCodes.add("草场门大街89号4层龙江电信机房-双龙大道8555号华润燃气/ETN0001NP");
        accessCodes.add("北京苏州30N2149NP");
        accessCodes.add("南京徐州ETN6037NPH");
        accessCodes.add("省建设银行-江苏第二师范学院建行自助区/ETN0001NP");
        accessCodes.add("园区建行-昆山市千灯镇恒丰时代广场/ETN0001NP");
        accessCodes.add("省建行-中国建设银行汉府街18号/ETN0001NP");
        accessCodes.add("M00002945A-M00002945Z");
        accessCodes.add("南京CTVPN189D048");
        accessCodes.add("省建行-省建行凯悦天琴分理处/ETN0001NP");
        accessCodes.add("省建行-高新开发区建行泰山支行/ETN0001NP");
        accessCodes.add("省建行-扬子国资/ETN0001NP");
        accessCodes.add("泰州兴化STN000053");
        accessCodes.add("K03164");
        accessCodes.add("省建行-省建行卫岗分理处/ETN0001NP");
        accessCodes.add("建设银行-天都大厦商业广场/ETN0001");
        accessCodes.add("M00104657A-M00104657Z");
        accessCodes.add("省建行-航空港建设第三工程总队/ETN0001NP");
        accessCodes.add("HLWZX02520210954325");
        accessCodes.add("HLWZX51420220778802");
        accessCodes.add("南京徐州ETN6129NPH");
        accessCodes.add("园区建行-张家港市凤凰镇凤南路江苏富淼科技/ETN0001NP");
        accessCodes.add("南京海门ETN0011NP");
        accessCodes.add("省建行-省建行梅山分理处/ETN0001NP");
        accessCodes.add("省建行-爱涛商务中心天元西路199号/ETN0001NP");
        accessCodes.add("中信银行-中信银行上海路支行/ETN0001NP");
        accessCodes.add("南京徐州ETN0702NP");
        accessCodes.add("HLWZX51520221283122");
        accessCodes.add("中信银行-宜兴房管局/ETN0001NP");
        accessCodes.add("园区建行-建行建屋2.5产业园(M43507962)/ETN0001NP");
        accessCodes.add("园区建行-建行吴中开发区管委会ATM(M40058583)2M");
        accessCodes.add("中信银行-诺家签约中心/ETN0001NP");
        accessCodes.add("中信银行-中信银行浦东路11号/ETN0001NP");
        accessCodes.add("中信银行-建筑业施工人员服务管理中心/ETN0001NP");
        accessCodes.add("南京徐州ETN0237NP");
        accessCodes.add("省建行-建行白下支行/ETN0001NP");
        accessCodes.add("南通中信银行-南通国土资源局跃龙路/ETN0002NP");
        accessCodes.add("南通建行-如东财政局/ETN0001NP");
        accessCodes.add("园区建行-建行葑谊分理处/ETN0001NP");
        accessCodes.add("徐州中信银行-徐州福源广场贝壳信息科技/ETN0001NP");
        accessCodes.add("园区建行-市民卡海棠街机房/ETN0001NP");
        accessCodes.add("南京泰州ETN0593NP");
        accessCodes.add("中信银行-江都房管局/ETN0001NP");
        accessCodes.add("建设银行-秣陵路21号/ETN0001NP");
        accessCodes.add("M00002919A-M00002919Z");
        accessCodes.add("NJ/GE1717");
        accessCodes.add("园区建行-建行九龙医院住院部大厅ATM(M40062180)2M");
        accessCodes.add("HLWZX025202206104563");
        accessCodes.add("中信银行-中信新栖支行学津路8号高创大厦/ETN0001NP");
        accessCodes.add("南京扬州ETN0419NP");
        accessCodes.add("省建行-建行北京西路5号/ETN0001NP");
        accessCodes.add("南京泰州ETN0584NP");
        accessCodes.add("园区建行-永旺华东商业(M40512968)2M");
        accessCodes.add("北京苏州ZQOTN00029");
        accessCodes.add("M00120723A-M00120723Z");
        accessCodes.add("无锡无锡STN000581");
        accessCodes.add("园区建行-建行庄基新社区大楼ATM(M40058350)2M");
        accessCodes.add("中信银行-中信银行城中支行(M43592976)/ETN0001NP");
        accessCodes.add("镇江建设银行丹徒支行-公积金管理中心/ETN0001NP");
        accessCodes.add("M00354243");
        accessCodes.add("中信银行-江阴国土局/ETN0001NP");
        accessCodes.add("13342599");
        accessCodes.add("10606343");
        accessCodes.add("省建设银行-福建路52号/ETN0001NP");
        accessCodes.add("苏州-太仓/ETN3222NP");
        accessCodes.add("省建行-上秦淮假日酒店5楼/ETN0001NP");
        accessCodes.add("扬州扬州ETN6243NPH");
        accessCodes.add("园区建行(CYQJH)-建行世贸城储蓄所/ETN0001NP");
        accessCodes.add("南通建行-如皋建行观凤路/ETN0001NP");
        accessCodes.add("无锡无锡STN001010");
        accessCodes.add("HLWZX523202308101911");
        accessCodes.add("南京CTVPN189C010");
        accessCodes.add("建行-建行武进新局5楼IDC/ETN0001NP");
        accessCodes.add("连云新城云湖路一带一路大数据中心B1幢22层-连云新城鹰游山路中核生态环境机房/ETN0001NP");
        accessCodes.add("省建行-公积金管理中心/ETN0003NP");
        accessCodes.add("省建行-省农信社江东中路49号/ETN0001NP");
        accessCodes.add("园区建行-建行城北支行/ETN0002NP");
        accessCodes.add("中信银行-住建局/ETN0001NP");
        accessCodes.add("南京扬州ETN0404NP");
        accessCodes.add("财政局-中信银行/ETN0001NP");
        accessCodes.add("M00104645A-M00104645Z");
        accessCodes.add("省建行-省建行大行宫支行专柜/ETN0001NP");
        accessCodes.add("建设银行-市场监督局/ETN0001NP");
        accessCodes.add("1127079704");
        accessCodes.add("M00231423A-M00231423Z");
        accessCodes.add("省建行-江宁麒麟街道富力城孩子王/ETN0001NP");
        accessCodes.add("苏州-常熟/ETN4762NP");
        accessCodes.add("中信银行镇江分行-镇江市自来水公司/ETN0001NP");
        accessCodes.add("建设银行南京分行-幕府花园/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3842NP");
        accessCodes.add("中信银行-国土资源管理局(M43082965)2M");
        accessCodes.add("省建行-溧水县大东门街建行/ETN0001NP");
        accessCodes.add("省建行-浦口天润新城支行/ETN0001NP");
        accessCodes.add("建设银行-建设银行郭新东路(MSTPJS1002876517)/ETN0001NP");
        accessCodes.add("省建设银行-通行宝智慧交通/ETN0001NP");
        accessCodes.add("住房公积金管理中心-中国建设银行/ETN0001NP");
        accessCodes.add("镇江CTVPN19429A");
        accessCodes.add("南京扬州ETN0372NP");
        accessCodes.add("中信银行-现代大厦国土储备中心(M45057956)/ETN0001NP");
        accessCodes.add("南京扬州ETN0358NP");
        accessCodes.add("园区建行-太仓高级中学/ETN0001NP");
        accessCodes.add("省建设银行-江苏经贸职业技术学院/ETN0001NP");
        accessCodes.add("省建行-网络小镇9号楼大厅建行自助厅/ETN0001NP");
        accessCodes.add("中信银行-新区支行/ETN0001NP");
        accessCodes.add("泰州泰州ETN6243NPH");
        accessCodes.add("建设银行-无锡地铁控制中心(清扬路228号)/ETN0001NP");
        accessCodes.add("南通中信银行-如皋中信银行海洋南路/ETN0001NP");
        accessCodes.add("南京苏州ETN0221NP");
        accessCodes.add("中信实业银行-泰州公积金管理中心/ETN0001NP");
        accessCodes.add("省建行-翠屏国际分理处/ETN0001NP");
        accessCodes.add("姜堰公积金-姜堰建设银行/ETN0001NP");
        accessCodes.add("中信实业银行-药城IDC机房国土局/ETN0001NP");
        accessCodes.add("园区建行-相城区房管局/ETN0001NP");
        accessCodes.add("23956234");
        accessCodes.add("南京如皋ETN0020NP");
        accessCodes.add("镇江市建设银行-丹阳迎春路分理处/ETN001NP");
        accessCodes.add("省建行-通行宝智慧交通/ETN0002NP");
        accessCodes.add("中信银行-雅迪电动车自助/ETN0001NP");
        accessCodes.add("中信银行-房地产市场处/ETN0001NP");
        accessCodes.add("南京扬州ETN0406NP");
        accessCodes.add("省建行-省建行城中支行/ETN0001NP");
        accessCodes.add("江苏省建行-省国税局浦江路30号/ETN0001NP");
        accessCodes.add("建行-公积金/ETN0004NP");
        accessCodes.add("南京常州ETN6063NPH");
        accessCodes.add("北京连云港ETN2024NP");
        accessCodes.add("中信银行-中信银行木渎支行(M43592997)/ETN0001NP");
        accessCodes.add("园区建行-建行兴元分理处(M40052706)/ETN0001NP");
        accessCodes.add("园区建行-吴江汾湖城司路(M40052196)10M");
        accessCodes.add("镇江市建设银行-建设银行普善路支行/ETN0001NP");
        accessCodes.add("园区建行-吴江黎里人民西路(M40052202)10M");
        accessCodes.add("园区建行-嘉盛数字科技大楼/ETN0001NP");
        accessCodes.add("南京贵阳ETN0012NP");
        accessCodes.add("中国建设银行-浦口丽景路2号/ETN0001NP");
        accessCodes.add("省建行-中国建设银行徐庄软件园/ETN0001NP");
        accessCodes.add("苏州-张家港/ETN3845NP");
        accessCodes.add("140246443");
        accessCodes.add("市建行-金湖建西支行公积金ETN0001NP");
        accessCodes.add("省建行-溧水国土局/ETN0001NP");
        accessCodes.add("园区建行-建行玲珑湾支行/ETN0001NP");
        accessCodes.add("中信银行-昆山电信IDC(M56077964)2M/ETN0001NP");
        accessCodes.add("M00236091A-M00236091Z");
        accessCodes.add("南京无锡ETN0981NP");
        accessCodes.add("车管所-建行常武中路车管/ETN0001NP");
        accessCodes.add("中信银行镇江分行-中信银行新区支行/ETN0001NP");
        accessCodes.add("南京徐州ETN0709NP");
        accessCodes.add("园区建行-建行万隆汽车ATM机(M40058283)2M");
        accessCodes.add("省建行-建行中山东路305号军区总院外科楼16层信息科/ETN0002NP");
        accessCodes.add("M00231434A-M00231434Z");
        accessCodes.add("建设银行-建设银行杨新路229号/ETN0001NP");
        accessCodes.add("建行-金坛市直溪镇直里村委直里村健尔康/ETN0001NP");
        accessCodes.add("省建行-建行新城科技园支行/ETN0001NP");
        accessCodes.add("省建行-省建行五台山支行/ETN0001NP");
        accessCodes.add("省建设银行-栖霞服务中心/ETN0001NP");
        accessCodes.add("中信银行-中信银行南门支行(M43592973)/ETN0001NP");
        accessCodes.add("银联银商机房-儿童医院河西分院门诊楼1层大厅/ETN0001NP");
        accessCodes.add("中信银行-太仓财政局(M40021987)/ETN0001NP");
        accessCodes.add("12379448");
        accessCodes.add("南京宿迁ETN0433NP");
        accessCodes.add("园区建行-太仓南院路建行(M50207965)/ETN0001NP");
        accessCodes.add("建行诚信大道990号华庭广场2栋九龙湖分理处/ETN0001NP");
        accessCodes.add("12742110");
        accessCodes.add("海安中信银行-海安农商行/ETN0001NP");
        accessCodes.add("建行泰州总行-靖江人力资源大楼/ETN0001NP");
        accessCodes.add("中信银行-中信汶北支行/ETN0001NP");
        accessCodes.add("省建设银行-省工商行政管理局/ETN0001NP");
        accessCodes.add("中信银行-中信中桥支行/ETN0001NP");
        accessCodes.add("南通中信银行-南通中信银行开发区支行/ETN0003NP");
        accessCodes.add("省建行-建设银行北京西路30号/ETN0001NP");
        accessCodes.add("南京常州ETN0367NP");
        accessCodes.add("省建行-建设银行永宁街1号/ETN0001NP");
        accessCodes.add("南通中信银行-海门中信银行行政中心/ETN0001NP");
        accessCodes.add("南通中信银行-启东中信银行建新路/ETN0001NP");
        accessCodes.add("银联二长-建设银行江东中路269号/ETN0001NP");
        accessCodes.add("115306111");
        accessCodes.add("省建行-建行雨花分理处/ETN0001NP");
        accessCodes.add("M00215093A-M00215093Z");
        accessCodes.add("园区建行-建行长江路支行/ETN0001NP");
        accessCodes.add("建设银行-万得大厦/ETN0001NP");
        accessCodes.add("南京连云港ETN6086NPH");
        accessCodes.add("苏州-昆山/ETN4000NP1");
        accessCodes.add("Z12515A-Z12515Z");
        accessCodes.add("M00223481A-M00223481Z");
        accessCodes.add("省建行-江苏银行中山南路111号/ETN0002NP");
        accessCodes.add("园区建行-建行金龙客车ATM/ETN0001");
        accessCodes.add("SWETN2944NP(建行-湖滨分理处M40052225)10M");


        Map<String,String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization","Bearer 58939ef8-bbf3-4aa2-9b9c-1b5986dc8588");
        for (int i = 0; i < accessCodes.size(); i++) {
            String accessCode = accessCodes.get(i);

            try {
                String res = HttpRequestsUtils.sendGet("http://**************:23244/apiInterface/110230011?moduleId=4e21e80daf56487ba86906c8666367c6&moduleName=%E5%91%8A%E8%AD%A6%E6%9F%A5%E8%AF%A2&moduleCode=alarmInfoMgtNew&archFlag=1&planDispatchTime=&monitorGrade=&searchItem=alarm_id,dispatch_rule_id,tt_no,parent_alarm_id,ems_id,area_id,specialty_id,alarm_level,alarm_name,user_label,alarm_summary,site_id,house_id,ne_alarm_time,ne_close_time,ne_open_time,entity_ip,equipment_type,insert_time,arch_flag,arch_time,dispatch_flag,dispatch_tt_time&lastSearchPage=1&pageNum=1&pageSize=20&alarmSummary="+accessCode, headers,new HashMap<>());
                System.out.println(i+"================");
                JSONObject alarms = JSONObject.parseObject(res);
                JSONObject data = alarms.getJSONObject("data");
                if (data == null) continue;
                JSONArray lists = data.getJSONArray("list");
                if (CollectionUtils.isEmpty(lists)) {
                    continue;
                }
                for (int i1 = 0; i1 < lists.size(); i1++) {
                    JSONObject alarm = lists.getJSONObject(i1);
                    alarm.put("access_code", accessCode);
                    this.circuitAnalysisDao.saveSyncAlarm(alarm, NRMConstants.SHARDING_GRAPH_DB);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }



        return "123";
    }





    @GetMapping("/netFault")
    public String netFault() {

        List<String> accessCodes = new ArrayList<>();
//        accessCodes.add("MSTPJT1002944293###321122930000000000000006");
//        accessCodes.add("13STN007971###321122930000000000000024");
//        accessCodes.add("HLWZX51420210936481###321122930000000000000021");
//        accessCodes.add("M00106848###321122930000000000000069");
//        accessCodes.add("MSTPJS1002318030###321122930000000000000013");
//        accessCodes.add("M40053070###321122930000000000000008");
//        accessCodes.add("MSTPJS1002872263###321122930000000000000063");
//        accessCodes.add("M40052153###321122930000000000000008");
//        accessCodes.add("MSTPJS1002709974###321122930000000000000008");
//        accessCodes.add("M00289732###321122930000000000000056");
//        accessCodes.add("M23516410###321122930000000000000006");
//        accessCodes.add("MSTPJT1002778087###321122930000000000000006");
//        accessCodes.add("M00228896###321122930000000000000069");
//        accessCodes.add("M00280143###321122930000000000000079");
//
//        accessCodes.add("M00115334###321122930000000000000024");
//        accessCodes.add("M07036204###321122930000000000000006");
//        accessCodes.add("M56627969###321122930000000000000008");
//        accessCodes.add("M40052288###321122930000000000000008");
//        accessCodes.add("M40047796###321122930000000000000008");
//        accessCodes.add("18STN010564###321122930000000000000063");
//        accessCodes.add("M46452956###321122930000000000000008");
//        accessCodes.add("M00002945###321122930000000000000037");
//        accessCodes.add("M45057956###321122930000000000000008");
//        accessCodes.add("M40062186###321122930000000000000008");
//        accessCodes.add("MSTPJS1002131402###321122930000000000000006");
//        accessCodes.add("M00286854###321122930000000000000046");
//        accessCodes.add("M20636382###321122930000000000000006");
//        accessCodes.add("14768173###321122930000000000000013");
//        accessCodes.add("M00279706###321122930000000000000079");
//        accessCodes.add("M14981243###321122930000000000000006");
//        accessCodes.add("MSTPJS1003049902###321122930000000000000008");
//        accessCodes.add("MSTPJS1003251210###321122930000000000000006");
//        accessCodes.add("M00151363###321122930000000000000037");
//        accessCodes.add("M07259977###321122930000000000000006");
//        accessCodes.add("M00222543###321122930000000000000069");
//        accessCodes.add("M19486382###321122930000000000000006");
//        accessCodes.add("12265057###321122930000000000000013");
//        accessCodes.add("HLWZX515202401121920###321122930000000000000017");
//        accessCodes.add("M40052977###321122930000000000000008");
//        accessCodes.add("M00292201###321122930000000000000046");
//        accessCodes.add("M00223556###321122930000000000000069");
//        accessCodes.add("MSTPJS1003009102###321122930000000000000079");
//        accessCodes.add("M48542712###321122930000000000000021");
//        accessCodes.add("MSTPJS1003191945###321122930000000000000006");
//        accessCodes.add("M40052601###321122930000000000000008");
//        accessCodes.add("M00353101###321122930000000000000024");
//        accessCodes.add("M00231378###321122930000000000000024");
//        accessCodes.add("M00111924###321122930000000000000037");
//        accessCodes.add("MSTPJS1003191762###321122930000000000000037");
//        accessCodes.add("MSTPJS1002714650###321122930000000000000006");
//        accessCodes.add("M16531360###321122930000000000000006");
//        accessCodes.add("M52282963###321122930000000000000008");
//        accessCodes.add("M07036501###321122930000000000000006");
//        accessCodes.add("M44182679###321122930000000000000021");
//        accessCodes.add("HLWZX51820230262186###321122930000000000000063");
//        accessCodes.add("M54457967###321122930000000000000008");
//        accessCodes.add("M40002854###321122930000000000000021");
//        accessCodes.add("M51702972###321122930000000000000008");
//        accessCodes.add("M40058097###321122930000000000000008");
//        accessCodes.add("M40052706###321122930000000000000008");
//        accessCodes.add("M07010601###321122930000000000000006");
//        accessCodes.add("MSTPJS1003098034###321122930000000000000024");
//        accessCodes.add("M40048147###321122930000000000000008");
//        accessCodes.add("M00147161###321122930000000000000037");
//        accessCodes.add("MSTPJS1002809404###321122930000000000000063");
//        accessCodes.add("M09131159###321122930000000000000006");
//        accessCodes.add("MSTPJS1002828549###321122930000000000000024");
//        accessCodes.add("M22271383###321122930000000000000006");
//        accessCodes.add("M40047851###321122930000000000000008");
//        accessCodes.add("M56927964###321122930000000000000008");
//        accessCodes.add("MSTPJS1001933053###321122930000000000000006");
//        accessCodes.add("MSTPJS1001698318###321122930000000000000006");
//        accessCodes.add("M40065390###321122930000000000000008");
//        accessCodes.add("M00280423###321122930000000000000079");
//        accessCodes.add("M27311888###321122930000000000000006");
//        accessCodes.add("M18096390###321122930000000000000006");
//        accessCodes.add("M30851888###321122930000000000000006");
//        accessCodes.add("M40053822###321122930000000000000008");
//        accessCodes.add("M00111209###321122930000000000000037");
//        accessCodes.add("M00143282###321122930000000000000037");
//        accessCodes.add("MSTPJS1003008668###321122930000000000000013");
//        accessCodes.add("M10346184###321122930000000000000006");
//        accessCodes.add("M40009221###321122930000000000000017");
//        accessCodes.add("MSTPJS1002682200###321122930000000000000008");
//        accessCodes.add("M14826360###321122930000000000000006");
//        accessCodes.add("M00009100###321122930000000000000037");
//        accessCodes.add("M00282036###321122930000000000000069");
//        accessCodes.add("154317483###321122930000000000000017");
//        accessCodes.add("M48557720###321122930000000000000021");
//        accessCodes.add("M07030546###321122930000000000000006");
//        accessCodes.add("M00115337###321122930000000000000024");
//        accessCodes.add("15958910###321122930000000000000013");
//        accessCodes.add("MSTPJS1002901299###321122930000000000000056");
//        accessCodes.add("MSTPJS1002713982###321122930000000000000006");
//        accessCodes.add("M00005646###321122930000000000000037");
//        accessCodes.add("M40053064###321122930000000000000008");
//        accessCodes.add("M20106384###321122930000000000000006");
//        accessCodes.add("M40003255###321122930000000000000021");
//        accessCodes.add("154317493###321122930000000000000017");
//        accessCodes.add("M43602968###321122930000000000000008");
//        accessCodes.add("M10341192###321122930000000000000006");
//        accessCodes.add("M40052871###321122930000000000000008");
//        accessCodes.add("MSTPJS1003107897###321122930000000000000024");
//        accessCodes.add("M00296168###321122930000000000000079");
//        accessCodes.add("M24126615###321122930000000000000006");
//        accessCodes.add("MSTPJS1002653586###321122930000000000000046");
//        accessCodes.add("M43627682###321122930000000000000021");
//        accessCodes.add("12264825###321122930000000000000013");
//        accessCodes.add("M40053667###321122930000000000000008");
//        accessCodes.add("M15831294###321122930000000000000006");
//        accessCodes.add("M47568528###321122930000000000000063");
//        accessCodes.add("M07299136###321122930000000000000006");
//        accessCodes.add("MSTPJS1002007715###321122930000000000000006");
//        accessCodes.add("MSTPJS1002865801###321122930000000000000073");
//        accessCodes.add("M40059593###321122930000000000000008");
//        accessCodes.add("M48997959###321122930000000000000008");
//        accessCodes.add("M55212964###321122930000000000000008");
//        accessCodes.add("M00233191###321122930000000000000013");
//        accessCodes.add("M08851192###321122930000000000000006");
//        accessCodes.add("M40053073###321122930000000000000008");
//        accessCodes.add("M25066393###321122930000000000000006");
//        accessCodes.add("M55932974###321122930000000000000008");
//        accessCodes.add("M49402959###321122930000000000000008");
//        accessCodes.add("M24451379###321122930000000000000006");
//        accessCodes.add("MSTPJS1003227291###321122930000000000000006");
//        accessCodes.add("南京南通ETN0661NP###321122930000000000000024");
//        accessCodes.add("M00226297###321122930000000000000013");
//        accessCodes.add("M15891243###321122930000000000000006");
//        accessCodes.add("M26646889###321122930000000000000006");
//        accessCodes.add("M00008555###321122930000000000000037");
//        accessCodes.add("M18016382###321122930000000000000006");
//        accessCodes.add("M00115228###321122930000000000000024");
//        accessCodes.add("M40047825###321122930000000000000008");
//        accessCodes.add("M00224438###321122930000000000000079");
//        accessCodes.add("M00236495###321122930000000000000024");
//        accessCodes.add("M40052859###321122930000000000000008");
//        accessCodes.add("MSTPJS1001980161###321122930000000000000008");
//        accessCodes.add("M22316431###321122930000000000000006");
//        accessCodes.add("HLWZX02520210212351###321122930000000000000006");
//        accessCodes.add("MSTPJS1003109993###321122930000000000000021");
//        accessCodes.add("M52947966###321122930000000000000008");
//        accessCodes.add("南京南通ETN0140NP###321122930000000000000024");
//        accessCodes.add("M42262984###321122930000000000000008");
//        accessCodes.add("M40062180###321122930000000000000008");
//        accessCodes.add("M40047929###321122930000000000000008");
//        accessCodes.add("M07264644###321122930000000000000006");
//        accessCodes.add("M40053607###321122930000000000000008");
//        accessCodes.add("MSTPJT1001982780###321122930000000000000013");
//        accessCodes.add("M42957956###321122930000000000000008");
//        accessCodes.add("M24376379###321122930000000000000006");
//        accessCodes.add("M26861888###321122930000000000000006");
//        accessCodes.add("MSTPJS1002788148###321122930000000000000006");
//        accessCodes.add("MSTPJS1003059592###321122930000000000000017");
//        accessCodes.add("M00142081###321122930000000000000037");
//        accessCodes.add("M00304884###321122930000000000000079");
//        accessCodes.add("M00174812###321122930000000000000013");
//        accessCodes.add("北京连云港ZQOTN00002###321122930000000000000063");
//        accessCodes.add("140246444###321122930000000000000063");
//        accessCodes.add("M40059750###321122930000000000000008");
//        accessCodes.add("M43592973###321122930000000000000008");
//        accessCodes.add("MSTPJS1002072033###321122930000000000000008");
//        accessCodes.add("M10256162###321122930000000000000006");
//        accessCodes.add("M18691403###321122930000000000000006");
//        accessCodes.add("MSTPJS1002649477###321122930000000000000021");
//        accessCodes.add("M40047858###321122930000000000000008");
//        accessCodes.add("MSTPJS1002485977###321122930000000000000006");
//        accessCodes.add("M00231394###321122930000000000000024");
//        accessCodes.add("192245864###321122930000000000000063");
//        accessCodes.add("M00244806###321122930000000000000056");
//        accessCodes.add("M00113227###321122930000000000000024");
//        accessCodes.add("M43592985###321122930000000000000008");
//        accessCodes.add("MSTPJS1002868623###321122930000000000000079");
//        accessCodes.add("M40053031###321122930000000000000008");
//        accessCodes.add("M08866201###321122930000000000000006");
//        accessCodes.add("MSTPJS1003119176###321122930000000000000017");
//        accessCodes.add("M00280879###321122930000000000000069");
//        accessCodes.add("M40053412###321122930000000000000008");
//        accessCodes.add("M42722980###321122930000000000000008");
//        accessCodes.add("M44222959###321122930000000000000008");
//        accessCodes.add("M49832976###321122930000000000000008");
//        accessCodes.add("MSTPJS1002652937###321122930000000000000006");
//        accessCodes.add("M40242971###321122930000000000000008");
//        accessCodes.add("M00281266###321122930000000000000079");
//        accessCodes.add("MSTPJS1001647166###321122930000000000000046");
//        accessCodes.add("M40070587###321122930000000000000008");
//        accessCodes.add("M43627679###321122930000000000000021");
//        accessCodes.add("JS11cy000762###321122930000000000000037");
//        accessCodes.add("M45142956###321122930000000000000008");
//        accessCodes.add("M00104657###321122930000000000000056");
//        accessCodes.add("HLWZX523202309103079###321122930000000000000079");
//        accessCodes.add("M41632958###321122930000000000000008");
//        accessCodes.add("M32411887###321122930000000000000006");
//        accessCodes.add("M00319101###321122930000000000000046");
//        accessCodes.add("MSTPJS1002716975###321122930000000000000008");
//        accessCodes.add("CZ190200_c###321122930000000000000069");
//        accessCodes.add("M40047861###321122930000000000000008");
//        accessCodes.add("M13436165###321122930000000000000006");
//        accessCodes.add("M16741375###321122930000000000000006");
//        accessCodes.add("M18721381###321122930000000000000006");
//        accessCodes.add("MSTPJS1002653586###321122930000000000000006");
//        accessCodes.add("L40401060004###321122930000000000000024");
//        accessCodes.add("M54107973###321122930000000000000008");
//        accessCodes.add("M00234268###321122930000000000000024");
//        accessCodes.add("MSTPJS1001936022###321122930000000000000008");
//        accessCodes.add("M25136388###321122930000000000000006");
//        accessCodes.add("M56747964###321122930000000000000008");
//        accessCodes.add("M26511888###321122930000000000000006");
//        accessCodes.add("M21131377###321122930000000000000006");
//        accessCodes.add("MSTPJT1001799090###321122930000000000000013");
//        accessCodes.add("M51887963###321122930000000000000008");
//        accessCodes.add("M48562701###321122930000000000000021");
//        accessCodes.add("M40053864###321122930000000000000008");
//        accessCodes.add("M44463010###321122930000000000000008");
//        accessCodes.add("M30181892###321122930000000000000006");
//        accessCodes.add("M15206234###321122930000000000000006");
//        accessCodes.add("M40053719###321122930000000000000008");
//        accessCodes.add("M00261649###321122930000000000000073");
//        accessCodes.add("M08851228###321122930000000000000006");
//        accessCodes.add("MSTPJS1002171387###321122930000000000000073");
//        accessCodes.add("M00260242###321122930000000000000079");
//        accessCodes.add("M07000611###321122930000000000000006");
//        accessCodes.add("MSTPJS1003262320###321122930000000000000063");
//        accessCodes.add("M00177064###321122930000000000000037");
//        accessCodes.add("MSTPJS1003191945###321122930000000000000056");
//        accessCodes.add("M24251385###321122930000000000000006");
//        accessCodes.add("M16651369###321122930000000000000006");
//        accessCodes.add("M40053082###321122930000000000000008");
//        accessCodes.add("M08866204###321122930000000000000006");
//        accessCodes.add("M48892956###321122930000000000000008");
//        accessCodes.add("M18146384###321122930000000000000006");
//        accessCodes.add("M40065336###321122930000000000000008");
//        accessCodes.add("15959575###321122930000000000000013");
//        accessCodes.add("M43592976###321122930000000000000008");
//        accessCodes.add("MSTPJS1001950103###321122930000000000000006");
//        accessCodes.add("115912537###321122930000000000000006");
//        accessCodes.add("M00113288###321122930000000000000024");
//        accessCodes.add("MSTPJS1001658061###321122930000000000000006");
//        accessCodes.add("M50202973###321122930000000000000008");
//        accessCodes.add("M10311168###321122930000000000000006");
//        accessCodes.add("15959400###321122930000000000000013");
//        accessCodes.add("MSTPJS1003040849###321122930000000000000063");
//        accessCodes.add("M00199837###321122930000000000000013");
//        accessCodes.add("M00183322###321122930000000000000013");
//        accessCodes.add("MSTPJS1001991579###321122930000000000000006");
//        accessCodes.add("M28051912###321122930000000000000006");
//        accessCodes.add("M22471379###321122930000000000000006");
//        accessCodes.add("M55617968###321122930000000000000008");
//        accessCodes.add("MSTPJS1002297305###321122930000000000000006");
//        accessCodes.add("M15436255###321122930000000000000006");
//        accessCodes.add("M22316437###321122930000000000000006");
//        accessCodes.add("M46157718###321122930000000000000021");
//        accessCodes.add("M56357966###321122930000000000000008");
//        accessCodes.add("19342020###321122930000000000000008");
//        accessCodes.add("12265088###321122930000000000000013");
//        accessCodes.add("M40054825###321122930000000000000008");
//        accessCodes.add("M40075205###321122930000000000000008");
//        accessCodes.add("M00119202###321122930000000000000013");
//        accessCodes.add("M00301963###321122930000000000000069");
//        accessCodes.add("M18051375###321122930000000000000006");
//        accessCodes.add("M00291741###321122930000000000000046");
//        accessCodes.add("MSTPJS1003061828###321122930000000000000069");
//        accessCodes.add("M57587964###321122930000000000000008");
//        accessCodes.add("M48507707###321122930000000000000021");
//        accessCodes.add("MSTPJS1002715291###321122930000000000000063");
//        accessCodes.add("1100410859###321122930000000000000063");
//        accessCodes.add("M07240766###321122930000000000000006");
//        accessCodes.add("M10166165###321122930000000000000006");
//        accessCodes.add("M00199834###321122930000000000000013");
//        accessCodes.add("M10336174###321122930000000000000006");
//        accessCodes.add("M40051307###321122930000000000000008");
//        accessCodes.add("M40052712###321122930000000000000008");
//        accessCodes.add("MSTPJS1001955011###321122930000000000000006");
//        accessCodes.add("HLWZX51820220742159###321122930000000000000063");
//        accessCodes.add("12265436###321122930000000000000013");
//        accessCodes.add("MSTPJS1002529369###321122930000000000000021");
//        accessCodes.add("MSTPJS1002905323###321122930000000000000006");
//        accessCodes.add("HLWZX52320230798783###321122930000000000000079");
//        accessCodes.add("154317405###321122930000000000000017");
//        accessCodes.add("M00218960###321122930000000000000069");
//        accessCodes.add("M40055247###321122930000000000000008");
//        accessCodes.add("MSTPJS1001892694###321122930000000000000008");
//        accessCodes.add("M59072964###321122930000000000000008");
//        accessCodes.add("MSTPJS1002651036###321122930000000000000021");
//        accessCodes.add("M07034572###321122930000000000000006");
//        accessCodes.add("M40054503###321122930000000000000008");
//        accessCodes.add("M12826176###321122930000000000000006");
//        accessCodes.add("M14786231###321122930000000000000006");
//        accessCodes.add("MSTPJS1003252517###321122930000000000000079");
//        accessCodes.add("M40062029###321122930000000000000008");
//        accessCodes.add("M14826267###321122930000000000000006");
//        accessCodes.add("M40052880###321122930000000000000008");
//        accessCodes.add("M00280308###321122930000000000000024");
//        accessCodes.add("M19421385###321122930000000000000006");
//        accessCodes.add("MSTPJS1001540577###321122930000000000000008");
//        accessCodes.add("M40055322###321122930000000000000008");
//        accessCodes.add("MSTPJS1001711010###321122930000000000000006");
//        accessCodes.add("MSTPJS1002189114###321122930000000000000006");
//        accessCodes.add("M23966395###321122930000000000000006");
//        accessCodes.add("M19906386###321122930000000000000006");
//        accessCodes.add("M48557714###321122930000000000000021");
//        accessCodes.add("M20681375###321122930000000000000006");
//        accessCodes.add("M00315024###321122930000000000000046");
//        accessCodes.add("MSTPJS1002964550###321122930000000000000046");
//        accessCodes.add("MSTPJS1002715685###321122930000000000000006");
//        accessCodes.add("M10131171###321122930000000000000006");
//        accessCodes.add("M54307966###321122930000000000000008");
//        accessCodes.add("145098793###321122930000000000000017");
//        accessCodes.add("M00314962###321122930000000000000046");
//        accessCodes.add("M00247732###321122930000000000000079");
//        accessCodes.add("M40007626###321122930000000000000017");
//        accessCodes.add("MSTPJS1002823101###321122930000000000000008");
//        accessCodes.add("MSTPJS1002893781###321122930000000000000006");
//        accessCodes.add("M48542705###321122930000000000000021");
//        accessCodes.add("M16006237###321122930000000000000006");
//        accessCodes.add("MSTPJS1003225905###321122930000000000000008");
//        accessCodes.add("M40051567###321122930000000000000008");
//        accessCodes.add("M43607956###321122930000000000000008");
//        accessCodes.add("MSTPJS1002502321###321122930000000000000008");
//        accessCodes.add("X00012562NJ571###321122930000000000000006");
//        accessCodes.add("M00300474###321122930000000000000056");
//        accessCodes.add("140050471###321122930000000000000063");
//        accessCodes.add("MSTPJS1001992724###321122930000000000000006");
//        accessCodes.add("M46157698###321122930000000000000021");
//        accessCodes.add("B00007627###321122930000000000000006");
//        accessCodes.add("M00154484###321122930000000000000037");
//        accessCodes.add("23956234###321122930000000000000008");
//        accessCodes.add("M47487959###321122930000000000000008");
//        accessCodes.add("M40052764###321122930000000000000008");
//        accessCodes.add("MSTPJS1002864703###321122930000000000000006");
//        accessCodes.add("12264844###321122930000000000000013");
//        accessCodes.add("M00308347###321122930000000000000069");
//        accessCodes.add("M47117699###321122930000000000000021");
//        accessCodes.add("M44366919###321122930000000000000017");
//        accessCodes.add("MSTPJS1002935606###321122930000000000000021");
//        accessCodes.add("M24761426###321122930000000000000006");
//        accessCodes.add("M49257965###321122930000000000000008");
//        accessCodes.add("M00259731###321122930000000000000079");
//        accessCodes.add("MSTPJS1001889545###321122930000000000000006");
//        accessCodes.add("M00107408###321122930000000000000079");
//        accessCodes.add("154318077###321122930000000000000017");
//        accessCodes.add("M14411231###321122930000000000000006");
//        accessCodes.add("M07007469###321122930000000000000006");
//        accessCodes.add("M10341207###321122930000000000000006");
//        accessCodes.add("M18016376###321122930000000000000006");
//        accessCodes.add("M40040402###321122930000000000000008");
//        accessCodes.add("M40052119###321122930000000000000008");
//        accessCodes.add("15202201###321122930000000000000079");
//        accessCodes.add("M40052709###321122930000000000000008");
//        accessCodes.add("M40047864###321122930000000000000008");
//        accessCodes.add("M07241131###321122930000000000000006");
//        accessCodes.add("MSTPJS1003194402###321122930000000000000056");
//        accessCodes.add("MSTPJS1002313118###321122930000000000000046");
//        accessCodes.add("MSTPJT1003263261###321122930000000000000006");
//        accessCodes.add("M00323088###321122930000000000000024");
//        accessCodes.add("M22806376###321122930000000000000006");
//        accessCodes.add("M22316401###321122930000000000000006");
//        accessCodes.add("M00316126###321122930000000000000046");
//        accessCodes.add("M16951372###321122930000000000000006");
//        accessCodes.add("M00102487###321122930000000000000056");
//        accessCodes.add("M00247053###321122930000000000000079");
//        accessCodes.add("M00197956###321122930000000000000013");
//        accessCodes.add("MSTPJS1002881433###321122930000000000000006");
//        accessCodes.add("M00254776###321122930000000000000069");
//        accessCodes.add("M60662964###321122930000000000000008");
//        accessCodes.add("M56545920###321122930000000000000017");
//        accessCodes.add("M10281159###321122930000000000000006");
//        accessCodes.add("M41842956###321122930000000000000008");
//        accessCodes.add("M00002919###321122930000000000000037");
//        accessCodes.add("M00002931###321122930000000000000037");
//        accessCodes.add("M17541393###321122930000000000000006");
//        accessCodes.add("M10346171###321122930000000000000006");
//        accessCodes.add("MSTPJS1002162023###321122930000000000000006");
//        accessCodes.add("MSTPJS1003019662###321122930000000000000006");
//        accessCodes.add("154317469###321122930000000000000017");
//        accessCodes.add("HLWZX02520210954325###321122930000000000000006");
//        accessCodes.add("M49542967###321122930000000000000008");
//        accessCodes.add("MSTPJS1001601214###321122930000000000000079");
//        accessCodes.add("M44462959###321122930000000000000008");
//        accessCodes.add("M00115962###321122930000000000000037");
//        accessCodes.add("M14956243###321122930000000000000006");
//        accessCodes.add("160807262###321122930000000000000024");
//        accessCodes.add("MSTPJS1002131402###321122930000000000000069");
//        accessCodes.add("M40052874###321122930000000000000008");
//        accessCodes.add("M00310461###321122930000000000000069");
//        accessCodes.add("M40051896###321122930000000000000008");
//        accessCodes.add("M48602707###321122930000000000000021");
//        accessCodes.add("M17576372###321122930000000000000006");
//        accessCodes.add("MSTPJS1002900178###321122930000000000000006");
//        accessCodes.add("M14826264###321122930000000000000006");
//        accessCodes.add("M08846195###321122930000000000000006");
//        accessCodes.add("M00115301###321122930000000000000069");
//        accessCodes.add("MSTPJS1002006978###321122930000000000000006");
//        accessCodes.add("MSTPBJ1001932031###321122930000000000000063");
//        accessCodes.add("M58897966###321122930000000000000008");
//        accessCodes.add("M00181549###321122930000000000000013");
//        accessCodes.add("M19531386###321122930000000000000006");
//        accessCodes.add("M40047457###321122930000000000000008");
//        accessCodes.add("MSTPJS1002940972###321122930000000000000006");
//        accessCodes.add("M00308463###321122930000000000000069");
//        accessCodes.add("M43602965###321122930000000000000008");
//        accessCodes.add("M52026920###321122930000000000000017");
//        accessCodes.add("MSTPJS1001993621###321122930000000000000046");
//        accessCodes.add("M40069893###321122930000000000000008");
//        accessCodes.add("M53777966###321122930000000000000008");
//        accessCodes.add("M00290706###321122930000000000000079");
//        accessCodes.add("MSTPJS1001997037###321122930000000000000069");
//        accessCodes.add("M00004215###321122930000000000000006");
//        accessCodes.add("12264757###321122930000000000000013");
//        accessCodes.add("M00280598###321122930000000000000073");
//        accessCodes.add("M00279748###321122930000000000000024");
//        accessCodes.add("M00183273###321122930000000000000013");
//        accessCodes.add("M08556162###321122930000000000000006");
//        accessCodes.add("HLWZX51620210829508###321122930000000000000046");
//        accessCodes.add("M40053234###321122930000000000000008");
//        accessCodes.add("M45527956###321122930000000000000008");
//        accessCodes.add("M54977966###321122930000000000000008");
//        accessCodes.add("M00210301###321122930000000000000013");
//        accessCodes.add("M58972969###321122930000000000000008");
//        accessCodes.add("M00163301###321122930000000000000037");
//        accessCodes.add("M12941163###321122930000000000000006");
//        accessCodes.add("M40052991###321122930000000000000008");
//        accessCodes.add("M40060003###321122930000000000000008");
//        accessCodes.add("M00314863###321122930000000000000046");
//        accessCodes.add("M16311357###321122930000000000000006");
//        accessCodes.add("M13301168###321122930000000000000006");
//        accessCodes.add("M40053348###321122930000000000000008");
//        accessCodes.add("M00104645###321122930000000000000056");
//        accessCodes.add("M00183232###321122930000000000000013");
//        accessCodes.add("12264731###321122930000000000000013");
//        accessCodes.add("M08851234###321122930000000000000006");
//        accessCodes.add("M00280596###321122930000000000000073");
//        accessCodes.add("M00259365###321122930000000000000079");
//        accessCodes.add("M00226716###321122930000000000000069");
//        accessCodes.add("M40051719###321122930000000000000008");
//        accessCodes.add("MSTPJS1002335433###321122930000000000000056");
//        accessCodes.add("110825603###321122930000000000000073");
//        accessCodes.add("M40046304###321122930000000000000008");
//        accessCodes.add("141983640###321122930000000000000017");
//        accessCodes.add("15958945###321122930000000000000013");
//        accessCodes.add("M23701397###321122930000000000000006");
//        accessCodes.add("M40064193###321122930000000000000008");
//        accessCodes.add("MSTPJS1001601214###321122930000000000000006");
//        accessCodes.add("M51333496###321122930000000000000063");
//        accessCodes.add("M13121183###321122930000000000000006");
//        accessCodes.add("M22316422###321122930000000000000006");
//        accessCodes.add("M00115225###321122930000000000000024");
//        accessCodes.add("154318083###321122930000000000000017");
//        accessCodes.add("M00281493###321122930000000000000024");
//        accessCodes.add("MSTPJS1002285848###321122930000000000000006");
//        accessCodes.add("MSTPJS1001991396###321122930000000000000006");
//        accessCodes.add("M48652705###321122930000000000000021");
//        accessCodes.add("12379448###321122930000000000000037");
//        accessCodes.add("M00222056###321122930000000000000069");
//        accessCodes.add("MSTPJS1002641323###321122930000000000000037");
//        accessCodes.add("M00298124###321122930000000000000046");
//        accessCodes.add("M40053376###321122930000000000000008");
//        accessCodes.add("M28051913###321122930000000000000006");
//        accessCodes.add("HLWZX025202308170632###321122930000000000000006");
//        accessCodes.add("M00224281###321122930000000000000013");
//        accessCodes.add("M00183249###321122930000000000000013");
//        accessCodes.add("MSTPJS1002823182###321122930000000000000006");
//        accessCodes.add("MSTPJS1003164406###321122930000000000000073");
//        accessCodes.add("M00113207###321122930000000000000024");
//        accessCodes.add("M17536381###321122930000000000000006");
//        accessCodes.add("M00217064###321122930000000000000069");
//        accessCodes.add("M40002381###321122930000000000000021");
//        accessCodes.add("MSTPJS1002791227###321122930000000000000021");
//        accessCodes.add("MSTPJS1002982617###321122930000000000000006");
//        accessCodes.add("154317479###321122930000000000000017");
//        accessCodes.add("F6211834b###321122930000000000000079");
//        accessCodes.add("M07240769###321122930000000000000006");
//        accessCodes.add("154317915###321122930000000000000017");
//        accessCodes.add("M40702971###321122930000000000000008");
//        accessCodes.add("M00222555###321122930000000000000069");
//        accessCodes.add("M24861378###321122930000000000000006");
//        accessCodes.add("M08866168###321122930000000000000006");
//        accessCodes.add("M00341841###321122930000000000000024");
//        accessCodes.add("M23516431###321122930000000000000006");
//        accessCodes.add("MSTPJS1002788148###321122930000000000000056");
//        accessCodes.add("K03164###321122930000000000000008");
//        accessCodes.add("M14786234###321122930000000000000006");
//        accessCodes.add("18STN010568###321122930000000000000063");
//        accessCodes.add("MSTPJS1002393763###321122930000000000000046");
//        accessCodes.add("IP190JS1002125371###321122930000000000000006");
//        accessCodes.add("X00003154NJ023###321122930000000000000006");
//        accessCodes.add("15959606###321122930000000000000013");
//        accessCodes.add("M49287701###321122930000000000000021");
//        accessCodes.add("M51112966###321122930000000000000008");
//        accessCodes.add("M00276959###321122930000000000000056");
//        accessCodes.add("M51197963###321122930000000000000008");
//        accessCodes.add("M25646898###321122930000000000000006");
//        accessCodes.add("M43507962###321122930000000000000008");
//        accessCodes.add("MSTPJS1003337911###321122930000000000000006");
//        accessCodes.add("M40052562###321122930000000000000008");
//        accessCodes.add("M54357966###321122930000000000000008");
//        accessCodes.add("MSTPJS1003120477###321122930000000000000017");
//        accessCodes.add("M00215123###321122930000000000000056");
//        accessCodes.add("M00106651###321122930000000000000079");
//        accessCodes.add("M51972963###321122930000000000000008");
//        accessCodes.add("M16751378###321122930000000000000006");
//        accessCodes.add("M00160547###321122930000000000000037");
//        accessCodes.add("HLWZX512202307205625###321122930000000000000008");
//        accessCodes.add("M00005652###321122930000000000000037");
//        accessCodes.add("MSTPJS1002059107###321122930000000000000046");
//        accessCodes.add("M00272491###321122930000000000000024");
//        accessCodes.add("M15986256###321122930000000000000006");
//        accessCodes.add("MSTPJS1003254184###321122930000000000000006");
//        accessCodes.add("MSTPJS1002296370###321122930000000000000008");
//        accessCodes.add("M52652969###321122930000000000000008");
//        accessCodes.add("M54107974###321122930000000000000008");
//        accessCodes.add("M00347981###321122930000000000000024");
//        accessCodes.add("M00215102###321122930000000000000056");
//        accessCodes.add("MSTPJS1002662130###321122930000000000000008");
//        accessCodes.add("14649940###321122930000000000000056");
//        accessCodes.add("M00005664###321122930000000000000037");
//        accessCodes.add("MSTPJS1001918029###321122930000000000000006");
//        accessCodes.add("MSTPJS1002905323###321122930000000000000073");
//        accessCodes.add("MSTPJT1003263262###321122930000000000000006");
//        accessCodes.add("M00247206###321122930000000000000079");
//        accessCodes.add("M18171381###321122930000000000000006");
//        accessCodes.add("M40053055###321122930000000000000008");
//        accessCodes.add("M13011183###321122930000000000000006");
//        accessCodes.add("M00113146###321122930000000000000024");
//        accessCodes.add("M00231385###321122930000000000000024");
//        accessCodes.add("M00183635###321122930000000000000013");
//        accessCodes.add("MSTPJS1002316735###321122930000000000000024");
//        accessCodes.add("HLWZX512202306199024###321122930000000000000008");
//        accessCodes.add("M00008294###321122930000000000000037");
//        accessCodes.add("M43602974###321122930000000000000008");
//        accessCodes.add("M61092964###321122930000000000000008");
//        accessCodes.add("MSTPJS1002148499###321122930000000000000013");
//        accessCodes.add("M00269603###321122930000000000000079");
//        accessCodes.add("M00101792###321122930000000000000056");
//        accessCodes.add("M00280311###321122930000000000000024");
//        accessCodes.add("M30231890###321122930000000000000006");
//        accessCodes.add("M40053079###321122930000000000000008");
//        accessCodes.add("M40067114###321122930000000000000008");
//        accessCodes.add("MSTPJS1002803934###321122930000000000000006");
//        accessCodes.add("MSTPJS1003336526###321122930000000000000006");
//        accessCodes.add("M27981891###321122930000000000000006");
//        accessCodes.add("MSTPJS1001854118###321122930000000000000006");
//        accessCodes.add("M22641379###321122930000000000000006");
//        accessCodes.add("M40050990###321122930000000000000008");
//        accessCodes.add("MSTPJS1002658806###321122930000000000000046");
//        accessCodes.add("M46377957###321122930000000000000008");
//        accessCodes.add("MSTPJS1002291570###321122930000000000000006");
//        accessCodes.add("M64223166###321122930000000000000008");
//        accessCodes.add("M40058283###321122930000000000000008");
//        accessCodes.add("MSTPJS1001933053###321122930000000000000037");
//        accessCodes.add("M00231413###321122930000000000000024");
//        accessCodes.add("M08661175###321122930000000000000006");
//        accessCodes.add("M22316436###321122930000000000000006");
//        accessCodes.add("M44462962###321122930000000000000008");
//        accessCodes.add("MSTPJS1002291104###321122930000000000000008");
//        accessCodes.add("MSTPJS1001664268###321122930000000000000008");
//        accessCodes.add("M54462974###321122930000000000000008");
//        accessCodes.add("M00308921###321122930000000000000069");
//        accessCodes.add("M19731412###321122930000000000000006");
//        accessCodes.add("M50592963###321122930000000000000008");
//        accessCodes.add("M41622959###321122930000000000000008");
//        accessCodes.add("MSTPJS1002776419###321122930000000000000008");
//        accessCodes.add("M40052589###321122930000000000000008");
//        accessCodes.add("Z1229###321122930000000000000017");
//        accessCodes.add("M44217963###321122930000000000000008");
//        accessCodes.add("M40055101###321122930000000000000008");
//        accessCodes.add("M00262810###321122930000000000000069");
//        accessCodes.add("M46157965###321122930000000000000008");
//        accessCodes.add("14580793###321122930000000000000013");
//        accessCodes.add("M00207999###321122930000000000000013");
//        accessCodes.add("M07304326###321122930000000000000006");
//        accessCodes.add("M00302244###321122930000000000000079");
//        accessCodes.add("MSTPJS1002863060###321122930000000000000008");
//        accessCodes.add("M56872966###321122930000000000000008");
//        accessCodes.add("M40052276###321122930000000000000008");
//        accessCodes.add("M15316237###321122930000000000000006");
//        accessCodes.add("M00115331###321122930000000000000024");
//        accessCodes.add("M14606243###321122930000000000000006");
//        accessCodes.add("MSTPJS1001853193###321122930000000000000006");
//        accessCodes.add("MSTPJS1002726457###321122930000000000000013");
//        accessCodes.add("MSTPJS1001810537###321122930000000000000006");
//        accessCodes.add("M15436252###321122930000000000000006");
//        accessCodes.add("M00278243###321122930000000000000079");
//        accessCodes.add("M08866180###321122930000000000000006");
//        accessCodes.add("MSTPJS1001995430###321122930000000000000008");
//        accessCodes.add("M53202979###321122930000000000000008");
//        accessCodes.add("M40692959###321122930000000000000008");
//        accessCodes.add("14470456###321122930000000000000013");
//        accessCodes.add("M00303988###321122930000000000000046");
//        accessCodes.add("M40052550###321122930000000000000008");
//        accessCodes.add("M24711376###321122930000000000000006");
//        accessCodes.add("M40262697###321122930000000000000021");
//        accessCodes.add("M00275671###321122930000000000000024");
//        accessCodes.add("MSTPJS1001421958###321122930000000000000008");
//        accessCodes.add("M16596367###321122930000000000000006");
//        accessCodes.add("M23516423###321122930000000000000006");
//        accessCodes.add("M56587979###321122930000000000000008");
//        accessCodes.add("M40052690###321122930000000000000008");
//        accessCodes.add("M00294639###321122930000000000000069");
//        accessCodes.add("MSTPJT1001800065###321122930000000000000013");
//        accessCodes.add("M61662964###321122930000000000000008");
//        accessCodes.add("MSTPJS1002963634###321122930000000000000046");
//        accessCodes.add("M08851222###321122930000000000000006");
//        accessCodes.add("M00298118###321122930000000000000046");
//        accessCodes.add("M07004529###321122930000000000000006");
//        accessCodes.add("M00306603###321122930000000000000069");
//        accessCodes.add("M29941890###321122930000000000000006");
//        accessCodes.add("M00280599###321122930000000000000073");
//        accessCodes.add("M40052752###321122930000000000000008");
//        accessCodes.add("M08851231###321122930000000000000006");
//        accessCodes.add("M16526360###321122930000000000000006");
//        accessCodes.add("M56682967###321122930000000000000008");
//        accessCodes.add("154318079###321122930000000000000017");
//        accessCodes.add("M00222801###321122930000000000000013");
//        accessCodes.add("MSTPJS1002943759###321122930000000000000024");
//        accessCodes.add("M53792969###321122930000000000000008");
//        accessCodes.add("M00151301###321122930000000000000037");
//        accessCodes.add("11840022###321122930000000000000024");
//        accessCodes.add("M00265114###321122930000000000000079");
//        accessCodes.add("M40046310###321122930000000000000008");
//        accessCodes.add("M40052862###321122930000000000000008");
//        accessCodes.add("MSTPZJ1002809526###321122930000000000000008");
//        accessCodes.add("M00251407###321122930000000000000073");
//        accessCodes.add("12456958###321122930000000000000013");
//        accessCodes.add("MSTPJS1002590135###321122930000000000000006");
//        accessCodes.add("M00005643###321122930000000000000037");
//        accessCodes.add("M17696372###321122930000000000000006");
//        accessCodes.add("M07265530###321122930000000000000006");
//        accessCodes.add("M10176159###321122930000000000000006");
//        accessCodes.add("M00218552###321122930000000000000069");
//        accessCodes.add("M00222558###321122930000000000000069");
//        accessCodes.add("M00290842###321122930000000000000046");
//        accessCodes.add("MSTPJS1002297305###321122930000000000000046");
//        accessCodes.add("154317485###321122930000000000000017");
//        accessCodes.add("M48468479###321122930000000000000063");
//        accessCodes.add("M00183329###321122930000000000000013");
//        accessCodes.add("M48562704###321122930000000000000021");
//        accessCodes.add("MSTPJS1003209342###321122930000000000000008");
//        accessCodes.add("MSTPJT1001797090###321122930000000000000013");
//        accessCodes.add("M56247977###321122930000000000000008");
//        accessCodes.add("M00315026###321122930000000000000046");
//        accessCodes.add("MSTPJS1001984115###321122930000000000000006");
//        accessCodes.add("M00275974###321122930000000000000046");
//        accessCodes.add("MSTPJS1001812195###321122930000000000000037");
//        accessCodes.add("M00280196###321122930000000000000056");
//        accessCodes.add("M13161165###321122930000000000000006");
//        accessCodes.add("M24056379###321122930000000000000006");
//        accessCodes.add("MSTPJS1002170322###321122930000000000000037");
//        accessCodes.add("M00320141###321122930000000000000046");
//        accessCodes.add("M23516394###321122930000000000000006");
//        accessCodes.add("MSTPJS1003292090###321122930000000000000046");
//        accessCodes.add("M07004777###321122930000000000000006");
//        accessCodes.add("M55472972###321122930000000000000008");
//        accessCodes.add("MSTPJS1002441729###321122930000000000000006");
//        accessCodes.add("X00012562NJ567###321122930000000000000006");
//        accessCodes.add("M18806375###321122930000000000000006");
//        accessCodes.add("M26901888###321122930000000000000006");
//        accessCodes.add("18STN010324###321122930000000000000063");
//        accessCodes.add("M41687981###321122930000000000000008");
//        accessCodes.add("M07261624###321122930000000000000006");
//        accessCodes.add("M40052128###321122930000000000000008");
//        accessCodes.add("M55467968###321122930000000000000008");
//        accessCodes.add("M00005661###321122930000000000000037");
//        accessCodes.add("M59957963###321122930000000000000008");
//        accessCodes.add("M60232964###321122930000000000000008");
//        accessCodes.add("M28436892###321122930000000000000006");
//        accessCodes.add("M60047966###321122930000000000000008");
//        accessCodes.add("M40061999###321122930000000000000008");
//        accessCodes.add("M41577971###321122930000000000000008");
//        accessCodes.add("HLWZX025202409208481###321122930000000000000006");
//        accessCodes.add("MSTPJS1002058048###321122930000000000000046");
//        accessCodes.add("M12071165###321122930000000000000006");
//        accessCodes.add("MSTPJS1001342049###321122930000000000000008");
//        accessCodes.add("MSTPJS1002942029###321122930000000000000006");
//        accessCodes.add("M10166159###321122930000000000000006");
//        accessCodes.add("M00283653###321122930000000000000024");
//        accessCodes.add("MSTPJS1002932882###321122930000000000000008");
//        accessCodes.add("M30401887###321122930000000000000006");
//        accessCodes.add("M08851183###321122930000000000000006");
//        accessCodes.add("M00298383###321122930000000000000069");
//        accessCodes.add("MSTPJS1002162023###321122930000000000000013");
//        accessCodes.add("M00330113###321122930000000000000024");
//        accessCodes.add("M40053382###321122930000000000000008");
//        accessCodes.add("M45977701###321122930000000000000021");
//        accessCodes.add("MSTPJS1002674310###321122930000000000000024");
//        accessCodes.add("M00270091###321122930000000000000024");
//        accessCodes.add("M40144139###321122930000000000000021");
//        accessCodes.add("M00215081###321122930000000000000056");
//        accessCodes.add("M40058610###321122930000000000000008");
//        accessCodes.add("MSTPJS1003028059###321122930000000000000008");
//        accessCodes.add("MSTPJS1002894110###321122930000000000000006");
//        accessCodes.add("M00223481###321122930000000000000024");
//        accessCodes.add("M15266243###321122930000000000000006");
//        accessCodes.add("M40073937###321122930000000000000008");
//        accessCodes.add("M00263339###321122930000000000000069");
//        accessCodes.add("M00254408###321122930000000000000079");
//        accessCodes.add("M40051346###321122930000000000000008");
//        accessCodes.add("MSTPJS1002441729###321122930000000000000017");
//        accessCodes.add("MSTPJS1003003782###321122930000000000000063");
//        accessCodes.add("M00280922###321122930000000000000069");
//        accessCodes.add("M40054330###321122930000000000000008");
//        accessCodes.add("M00265111###321122930000000000000079");
//        accessCodes.add("泰州CTVPN189B007###321122930000000000000079");
//        accessCodes.add("MSTPJS1003225613###321122930000000000000063");
//        accessCodes.add("M00112519###321122930000000000000024");
//        accessCodes.add("M40058178###321122930000000000000008");
//        accessCodes.add("MSTPJS1003246543###321122930000000000000063");
//        accessCodes.add("117914885###321122930000000000000006");
//        accessCodes.add("M48252960###321122930000000000000008");
//        accessCodes.add("M23171378###321122930000000000000006");
//        accessCodes.add("M00222528###321122930000000000000069");
//        accessCodes.add("M48542711###321122930000000000000021");
//        accessCodes.add("M18171372###321122930000000000000006");
//        accessCodes.add("M00254645###321122930000000000000079");
//        accessCodes.add("M52817966###321122930000000000000008");
//        accessCodes.add("M00141784###321122930000000000000037");
//        accessCodes.add("MSTPJS1002649477###321122930000000000000006");
//        accessCodes.add("M00235237###321122930000000000000024");
//        accessCodes.add("M00338304###321122930000000000000024");
//        accessCodes.add("MSTPJS1003292090###321122930000000000000006");
//        accessCodes.add("M00190635###321122930000000000000013");
//        accessCodes.add("M53427982###321122930000000000000008");
//        accessCodes.add("M00285592###321122930000000000000056");
//        accessCodes.add("M46662956###321122930000000000000008");
//        accessCodes.add("M00326260###321122930000000000000024");
//        accessCodes.add("MSTPJT1001800105###321122930000000000000013");
//        accessCodes.add("M40055056###321122930000000000000008");
//        accessCodes.add("M23886378###321122930000000000000006");
//        accessCodes.add("M52002963###321122930000000000000008");
//        accessCodes.add("MSTPJS1001661183###321122930000000000000079");
//        accessCodes.add("M61127964###321122930000000000000008");
//        accessCodes.add("M47067959###321122930000000000000008");
//        accessCodes.add("M00276572###321122930000000000000056");
//        accessCodes.add("M10571168###321122930000000000000006");
//        accessCodes.add("MSTPJS1002726457###321122930000000000000006");
//        accessCodes.add("154318088###321122930000000000000017");
//        accessCodes.add("M08851204###321122930000000000000006");
//        accessCodes.add("151052210###321122930000000000000021");
//        accessCodes.add("HLWZX510202208108804###321122930000000000000013");
//        accessCodes.add("KF00080###321122930000000000000008");
//        accessCodes.add("JS13cy000474###321122930000000000000024");
//        accessCodes.add("M00262251###321122930000000000000069");
//        accessCodes.add("M48468476###321122930000000000000063");
//        accessCodes.add("147785335###321122930000000000000021");
//        accessCodes.add("MSTPJS1003044603###321122930000000000000006");
//        accessCodes.add("M50222969###321122930000000000000008");
//        accessCodes.add("15958959###321122930000000000000013");
//        accessCodes.add("15366030###321122930000000000000079");
//        accessCodes.add("15169884###321122930000000000000079");
//        accessCodes.add("M00113240###321122930000000000000024");
//        accessCodes.add("MSTPJS1002284142###321122930000000000000008");
//        accessCodes.add("M00247949###321122930000000000000079");
//        accessCodes.add("MSTPJS1002640056###321122930000000000000006");
//        accessCodes.add("M07000614###321122930000000000000006");
//        accessCodes.add("12264878###321122930000000000000013");
//        accessCodes.add("M46157726###321122930000000000000021");
//        accessCodes.add("18STN010566###321122930000000000000063");
//        accessCodes.add("M45802956###321122930000000000000008");
//        accessCodes.add("M41537682###321122930000000000000021");
//        accessCodes.add("M15811264###321122930000000000000006");
//        accessCodes.add("MSTPJS1002870477###321122930000000000000013");
//        accessCodes.add("IPRANJS1002440305###321122930000000000000063");
//        accessCodes.add("M10286159###321122930000000000000006");
//        accessCodes.add("MSTPJS1002574719###321122930000000000000024");
//        accessCodes.add("MSTPJS1002120595###321122930000000000000006");
//        accessCodes.add("M16476357###321122930000000000000006");
//        accessCodes.add("MSTPJS1002331081###321122930000000000000037");
//        accessCodes.add("M41032959###321122930000000000000008");
//        accessCodes.add("M53777978###321122930000000000000008");
//        accessCodes.add("MSTPJT1001800068###321122930000000000000013");
//        accessCodes.add("M00299230###321122930000000000000069");
//        accessCodes.add("M00289346###321122930000000000000079");
//        accessCodes.add("M40262682###321122930000000000000021");
//        accessCodes.add("M50207968###321122930000000000000008");
//        accessCodes.add("MSTPJS1001853390###321122930000000000000006");
//        accessCodes.add("141118472###321122930000000000000021");
//        accessCodes.add("M19556378###321122930000000000000006");
//        accessCodes.add("M00278546###321122930000000000000079");
//        accessCodes.add("M24431375###321122930000000000000006");
//        accessCodes.add("M40052547###321122930000000000000008");
//        accessCodes.add("M00002943###321122930000000000000037");
//        accessCodes.add("M48266931###321122930000000000000017");
//        accessCodes.add("M00262242###321122930000000000000069");
//        accessCodes.add("M00113268###321122930000000000000024");
//        accessCodes.add("154317412###321122930000000000000017");
//        accessCodes.add("M24251387###321122930000000000000006");
//        accessCodes.add("MSTPJS1002968644###321122930000000000000063");
//        accessCodes.add("M40053722###321122930000000000000008");
//        accessCodes.add("MSTPJS1001812195###321122930000000000000006");
//        accessCodes.add("M40052196###321122930000000000000008");
//        accessCodes.add("M00286952###321122930000000000000056");
//        accessCodes.add("M61402966###321122930000000000000008");
//        accessCodes.add("M21731382###321122930000000000000006");
//        accessCodes.add("M00117082###321122930000000000000037");
//        accessCodes.add("X00003154NJ039###321122930000000000000006");
//        accessCodes.add("M10171171###321122930000000000000006");
//        accessCodes.add("M24796384###321122930000000000000006");
//        accessCodes.add("MSTPHB1003191453###321122930000000000000006");
//        accessCodes.add("M00263831###321122930000000000000079");
//        accessCodes.add("M00319804###321122930000000000000046");
//        accessCodes.add("M45822957###321122930000000000000008");
//        accessCodes.add("M00316125###321122930000000000000046");
//        accessCodes.add("M40046785###321122930000000000000008");
//        accessCodes.add("M00264011###321122930000000000000024");
//        accessCodes.add("MSTPJS1002591331###321122930000000000000006");
//        accessCodes.add("M00183261###321122930000000000000013");
//        accessCodes.add("北京苏州ZQOTN00029###321122930000000000000008");
//        accessCodes.add("M24241412###321122930000000000000006");
//        accessCodes.add("M42407956###321122930000000000000008");
//        accessCodes.add("M43592997###321122930000000000000008");
//        accessCodes.add("M00174349###321122930000000000000013");
//        accessCodes.add("M50872963###321122930000000000000008");
//        accessCodes.add("M40052700###321122930000000000000008");
//        accessCodes.add("M40062011###321122930000000000000008");
//        accessCodes.add("M29006892###321122930000000000000006");
//        accessCodes.add("HLWZX51020211264241###321122930000000000000013");
//        accessCodes.add("MSTPJS1003040849###321122930000000000000006");
//        accessCodes.add("MSTPJS1002655928###321122930000000000000006");
//        accessCodes.add("MSTPJS1003197835###321122930000000000000079");
//        accessCodes.add("M54147973###321122930000000000000008");
//        accessCodes.add("M00141255###321122930000000000000037");
//        accessCodes.add("MSTPJS1002640056###321122930000000000000069");
//        accessCodes.add("120079477###321122930000000000000006");
//        accessCodes.add("南京南通ETN0140NP###321122930000000000000006");
//        accessCodes.add("M40052156###321122930000000000000008");
//        accessCodes.add("M00231422###321122930000000000000024");
//        accessCodes.add("M40051516###321122930000000000000008");
//        accessCodes.add("14703070###321122930000000000000013");
//        accessCodes.add("M00215093###321122930000000000000056");
//        accessCodes.add("HLWZX025202309176024###321122930000000000000006");
//        accessCodes.add("M17061357###321122930000000000000006");
//        accessCodes.add("M00302648###321122930000000000000079");
//        accessCodes.add("M00147047###321122930000000000000037");
//        accessCodes.add("M32076890###321122930000000000000006");
//        accessCodes.add("M00183264###321122930000000000000013");
//        accessCodes.add("15959768###321122930000000000000013");
//        accessCodes.add("MSTPJS1001984101###321122930000000000000069");
//        accessCodes.add("M00231428###321122930000000000000024");
//        accessCodes.add("M00115328###321122930000000000000024");
//        accessCodes.add("M64658164###321122930000000000000008");
//        accessCodes.add("M00143164###321122930000000000000037");
//        accessCodes.add("MSTPJS1001567243###321122930000000000000008");
//        accessCodes.add("M17561378###321122930000000000000006");
//        accessCodes.add("M00228902###321122930000000000000069");
//        accessCodes.add("M40013373###321122930000000000000008");
//        accessCodes.add("MSTPJS1003123666###321122930000000000000008");
//        accessCodes.add("M14636231###321122930000000000000006");
//        accessCodes.add("M40053616###321122930000000000000008");
//        accessCodes.add("1126550265###321122930000000000000063");
//        accessCodes.add("MSTPJS1001647166###321122930000000000000006");
//        accessCodes.add("MSTPJS1003198575###321122930000000000000006");
//        accessCodes.add("M00104687###321122930000000000000056");
//        accessCodes.add("M08861180###321122930000000000000006");
//        accessCodes.add("M00120464###321122930000000000000024");
//        accessCodes.add("MSTPJS1003037470###321122930000000000000069");
//        accessCodes.add("113492007###321122930000000000000006");
//        accessCodes.add("M00266651###321122930000000000000024");
//        accessCodes.add("M00333681###321122930000000000000024");
//        accessCodes.add("M48362962###321122930000000000000008");
//        accessCodes.add("M00218796###321122930000000000000069");
//        accessCodes.add("M22361377###321122930000000000000006");
//        accessCodes.add("M57777965###321122930000000000000008");
//        accessCodes.add("MSTPJS1002120595###321122930000000000000079");
//        accessCodes.add("M40052243###321122930000000000000008");
//        accessCodes.add("HLWZX51820210310109###321122930000000000000063");
//        accessCodes.add("MSTPJS1003197835###321122930000000000000006");
//        accessCodes.add("M40058455###321122930000000000000008");
//        accessCodes.add("M40052994###321122930000000000000008");
//        accessCodes.add("M40072487###321122930000000000000008");
//        accessCodes.add("M00154687###321122930000000000000037");
//        accessCodes.add("MSTPJS1002431560###321122930000000000000021");
//        accessCodes.add("M62853166###321122930000000000000008");
//        accessCodes.add("M00002837###321122930000000000000037");
//        accessCodes.add("M43592994###321122930000000000000008");
//        accessCodes.add("M52572963###321122930000000000000008");
//        accessCodes.add("M00115216###321122930000000000000024");
//        accessCodes.add("MSTPJS1003048372###321122930000000000000056");
//        accessCodes.add("M09631159###321122930000000000000006");
//        accessCodes.add("M40060063###321122930000000000000008");
//        accessCodes.add("HLWZX025202312190497###321122930000000000000006");
//        accessCodes.add("M42977679###321122930000000000000021");
//        accessCodes.add("M00261002###321122930000000000000046");
//        accessCodes.add("M44697960###321122930000000000000008");
//        accessCodes.add("10869327###321122930000000000000013");
//        accessCodes.add("M40054867###321122930000000000000008");
//        accessCodes.add("MSTPJS1002379215###321122930000000000000073");
//        accessCodes.add("M17626375###321122930000000000000006");
//        accessCodes.add("M00296162###321122930000000000000069");
//        accessCodes.add("M00333711###321122930000000000000024");
//        accessCodes.add("JS14cy000503###321122930000000000000021");
//        accessCodes.add("12265030###321122930000000000000013");
//        accessCodes.add("MSTPJS1002900178###321122930000000000000079");
//        accessCodes.add("M40077519###321122930000000000000008");
//        accessCodes.add("13342599###321122930000000000000008");
//        accessCodes.add("M00286132###321122930000000000000056");
//        accessCodes.add("M40053058###321122930000000000000008");
//        accessCodes.add("M40113530###321122930000000000000063");
//        accessCodes.add("MSTPJS1003078477###321122930000000000000017");
//        accessCodes.add("M12791165###321122930000000000000006");
//        accessCodes.add("M00295322###321122930000000000000069");
//        accessCodes.add("M08851216###321122930000000000000006");
//        accessCodes.add("M00113280###321122930000000000000024");
//        accessCodes.add("M00113189###321122930000000000000024");
//        accessCodes.add("MSTPJS1003147535###321122930000000000000008");
//        accessCodes.add("M07466159###321122930000000000000006");
//        accessCodes.add("M14246162###321122930000000000000006");
//        accessCodes.add("M07028415###321122930000000000000006");
//        accessCodes.add("M40032897###321122930000000000000008");
//        accessCodes.add("M50462963###321122930000000000000008");
//        accessCodes.add("13643536###321122930000000000000013");
//        accessCodes.add("M28696933###321122930000000000000006");
//        accessCodes.add("M00183252###321122930000000000000013");
//        accessCodes.add("M42657962###321122930000000000000008");
//        accessCodes.add("M40047926###321122930000000000000008");
//        accessCodes.add("MSTPJS1002947837###321122930000000000000006");
//        accessCodes.add("M10176204###321122930000000000000006");
//        accessCodes.add("M17456372###321122930000000000000006");
//        accessCodes.add("M08866192###321122930000000000000006");
//        accessCodes.add("M23596380###321122930000000000000006");
//        accessCodes.add("M00179482###321122930000000000000037");
//        accessCodes.add("M00278985###321122930000000000000069");
//        accessCodes.add("1127079704###321122930000000000000063");
//        accessCodes.add("M43082965###321122930000000000000008");
//        accessCodes.add("K00952###321122930000000000000008");
//        accessCodes.add("M40054173###321122930000000000000008");
//        accessCodes.add("MSTPJS1003242810###321122930000000000000008");
//        accessCodes.add("M17561381###321122930000000000000006");
//        accessCodes.add("M22561402###321122930000000000000006");
//        accessCodes.add("140762231###321122930000000000000063");
//        accessCodes.add("M00280066###321122930000000000000079");
//        accessCodes.add("MSTPJS1002451660###321122930000000000000024");
//        accessCodes.add("154318073###321122930000000000000017");
//        accessCodes.add("14724572###321122930000000000000079");
//        accessCodes.add("M60467966###321122930000000000000008");
//        accessCodes.add("M40060913###321122930000000000000008");
//        accessCodes.add("M46107698###321122930000000000000021");
//        accessCodes.add("M00344785###321122930000000000000024");
//        accessCodes.add("M40487959###321122930000000000000008");
//        accessCodes.add("MSTPJS1002312845###321122930000000000000006");
//        accessCodes.add("MSTPJS1002485977###321122930000000000000073");
//        accessCodes.add("M00305101###321122930000000000000069");
//        accessCodes.add("MSTPJS1002648203###321122930000000000000006");
//        accessCodes.add("M59327966###321122930000000000000008");
//        accessCodes.add("M11801180###321122930000000000000006");
//        accessCodes.add("M32386897###321122930000000000000006");
//        accessCodes.add("HLWZX025202403196685###321122930000000000000006");
//        accessCodes.add("M00244793###321122930000000000000056");
//        accessCodes.add("M22141397###321122930000000000000006");
//        accessCodes.add("M00003627###321122930000000000000037");
//        accessCodes.add("M25026391###321122930000000000000006");
//        accessCodes.add("M40052571###321122930000000000000008");
//        accessCodes.add("M56367964###321122930000000000000008");
//        accessCodes.add("M00002941###321122930000000000000037");
//        accessCodes.add("M00183309###321122930000000000000013");
//        accessCodes.add("M51462963###321122930000000000000008");
//        accessCodes.add("M00256582###321122930000000000000079");
//        accessCodes.add("M55827967###321122930000000000000008");
//        accessCodes.add("MSTPJS1002292734###321122930000000000000008");
//        accessCodes.add("MSTPJS1002715685###321122930000000000000046");
//        accessCodes.add("M40062967###321122930000000000000008");
//        accessCodes.add("M40052883###321122930000000000000008");
//        accessCodes.add("MSTPJT1001798079###321122930000000000000013");
//        accessCodes.add("M61237964###321122930000000000000008");
//        accessCodes.add("MSTPJS1002865801###321122930000000000000006");
//        accessCodes.add("M40062430###321122930000000000000008");
//        accessCodes.add("M07245676###321122930000000000000006");
//        accessCodes.add("M48587721###321122930000000000000021");
//        accessCodes.add("M00274474###321122930000000000000056");
//        accessCodes.add("M00314966###321122930000000000000046");
//        accessCodes.add("M08851213###321122930000000000000006");
//        accessCodes.add("M00002239###321122930000000000000037");
//        accessCodes.add("M10346181###321122930000000000000006");
//        accessCodes.add("MSTPJS1003120477###321122930000000000000006");
//        accessCodes.add("MSTPJS1003301637###321122930000000000000006");
//        accessCodes.add("M00183379###321122930000000000000013");
//        accessCodes.add("M00145781###321122930000000000000037");
//        accessCodes.add("M40059110###321122930000000000000008");
//        accessCodes.add("M00181828###321122930000000000000037");
//        accessCodes.add("M48587715###321122930000000000000021");
//        accessCodes.add("M22001376###321122930000000000000006");
//        accessCodes.add("MSTPJT1001798080###321122930000000000000013");
//        accessCodes.add("M47147959###321122930000000000000008");
//        accessCodes.add("M23276381###321122930000000000000006");
//        accessCodes.add("12265334###321122930000000000000013");
//        accessCodes.add("M52956925###321122930000000000000017");
//        accessCodes.add("MSTPJS1002313118###321122930000000000000006");
//        accessCodes.add("MSTPJS1002263033###321122930000000000000024");
//        accessCodes.add("MSTPJS1003290725###321122930000000000000006");
//        accessCodes.add("M00225022###321122930000000000000069");
//        accessCodes.add("HLWZX025202311189169###321122930000000000000006");
//        accessCodes.add("M00285612###321122930000000000000056");
//        accessCodes.add("M43592991###321122930000000000000008");
//        accessCodes.add("M10131174###321122930000000000000006");
//        accessCodes.add("M00152536###321122930000000000000013");
//        accessCodes.add("MSTPJS1003260457###321122930000000000000063");
//        accessCodes.add("MSTPJS1001769242###321122930000000000000006");
//        accessCodes.add("M00275731###321122930000000000000024");
//        accessCodes.add("M00268004###321122930000000000000024");
//        accessCodes.add("M00273977###321122930000000000000046");
//        accessCodes.add("M15106237###321122930000000000000006");
//        accessCodes.add("MSTPJS1003103754###321122930000000000000056");
//        accessCodes.add("M00211730###321122930000000000000056");
//        accessCodes.add("M00123771###321122930000000000000013");
//        accessCodes.add("18STN010535###321122930000000000000063");
//        accessCodes.add("M14656240###321122930000000000000006");
//        accessCodes.add("M00280051###321122930000000000000024");
//        accessCodes.add("M20216387###321122930000000000000006");
//        accessCodes.add("M48477702###321122930000000000000021");
//        accessCodes.add("M40052219###321122930000000000000008");
//        accessCodes.add("M40076007###321122930000000000000008");
//        accessCodes.add("M00298180###321122930000000000000046");
//        accessCodes.add("MSTPJS1001983694###321122930000000000000008");
//        accessCodes.add("M22651376###321122930000000000000006");
//        accessCodes.add("M46157710###321122930000000000000021");
//        accessCodes.add("MSTPJS1002893781###321122930000000000000056");
//        accessCodes.add("M56077964###321122930000000000000008");
//        accessCodes.add("MSTPJS1001889545###321122930000000000000037");
//        accessCodes.add("M00276736###321122930000000000000069");
//        accessCodes.add("MSTPJS1001750785###321122930000000000000006");
//        accessCodes.add("M00231434###321122930000000000000024");
//        accessCodes.add("MSTPJS1002127171###321122930000000000000024");
//        accessCodes.add("M17696375###321122930000000000000006");
//        accessCodes.add("MSTPJS1003147535###321122930000000000000006");
//        accessCodes.add("M53252963###321122930000000000000008");
//        accessCodes.add("M40052525###321122930000000000000008");
//        accessCodes.add("M00104666###321122930000000000000056");
//        accessCodes.add("M40052331###321122930000000000000008");
//        accessCodes.add("JS25cy003182###321122930000000000000006");
//        accessCodes.add("M16741357###321122930000000000000006");
//        accessCodes.add("MSTPJS1002797946###321122930000000000000006");
//        accessCodes.add("M00227214###321122930000000000000069");
//        accessCodes.add("MSTPJS1003219662###321122930000000000000006");
//        accessCodes.add("MSTPJS1002809404###321122930000000000000006");
//        accessCodes.add("M40053052###321122930000000000000008");
//        accessCodes.add("HLWZX025202307169529###321122930000000000000006");
//        accessCodes.add("M46177963###321122930000000000000008");
//        accessCodes.add("M55617966###321122930000000000000008");
//        accessCodes.add("MSTPJS1003278442###321122930000000000000006");
//        accessCodes.add("M07024978###321122930000000000000006");
//        accessCodes.add("M00209595###321122930000000000000046");
//        accessCodes.add("M47568485###321122930000000000000063");
//        accessCodes.add("M00280314###321122930000000000000024");
//        accessCodes.add("M47712706###321122930000000000000021");
//        accessCodes.add("M00113318###321122930000000000000024");
//        accessCodes.add("M40061352###321122930000000000000008");
//        accessCodes.add("MSTPJS1002678110###321122930000000000000006");
//        accessCodes.add("MSTPJS1003059979###321122930000000000000008");
//        accessCodes.add("M08846189###321122930000000000000006");
//        accessCodes.add("M28931887###321122930000000000000006");
//        accessCodes.add("MSTPJS1003313440###321122930000000000000006");
//        accessCodes.add("M00216523###321122930000000000000046");
//        accessCodes.add("M40058350###321122930000000000000008");
//        accessCodes.add("M00183289###321122930000000000000013");
//        accessCodes.add("M20286379###321122930000000000000006");
//        accessCodes.add("149485991###321122930000000000000021");
//        accessCodes.add("MSTPJS1003187202###321122930000000000000006");
//        accessCodes.add("M00327809###321122930000000000000024");
//        accessCodes.add("M00314974###321122930000000000000046");
//        accessCodes.add("MSTPJS1002174683###321122930000000000000008");
//        accessCodes.add("MSTPJS1002651036###321122930000000000000006");
//        accessCodes.add("M08866213###321122930000000000000006");
//        accessCodes.add("M00160641###321122930000000000000037");
//        accessCodes.add("15958828###321122930000000000000013");
//        accessCodes.add("M00204176###321122930000000000000013");
//        accessCodes.add("M52217963###321122930000000000000008");
//        accessCodes.add("HLWZX51520221283120###321122930000000000000017");
//        accessCodes.add("M24806382###321122930000000000000006");
//        accessCodes.add("M16311393###321122930000000000000006");
//        accessCodes.add("M40063439###321122930000000000000008");
//        accessCodes.add("M50207965###321122930000000000000008");
//        accessCodes.add("M45557960###321122930000000000000008");
//        accessCodes.add("M10301168###321122930000000000000006");
//        accessCodes.add("M00115213###321122930000000000000024");
//        accessCodes.add("HLWZX51020220381975###321122930000000000000013");
//        accessCodes.add("HLWZX523202311105513###321122930000000000000079");
//        accessCodes.add("M20631378###321122930000000000000006");
//        accessCodes.add("M40062522###321122930000000000000008");
//        accessCodes.add("M43592979###321122930000000000000008");
//        accessCodes.add("10570479###321122930000000000000079");
//        accessCodes.add("M00183270###321122930000000000000013");
//        accessCodes.add("M51287974###321122930000000000000008");
//        accessCodes.add("M51702969###321122930000000000000008");
//        accessCodes.add("MSTPJS1003246543###321122930000000000000006");
//        accessCodes.add("M40042439###321122930000000000000008");
//        accessCodes.add("M00115210###321122930000000000000024");
//        accessCodes.add("MSTPJS1001993609###321122930000000000000006");
//        accessCodes.add("MSTPJS1002947837###321122930000000000000056");
//        accessCodes.add("M40053385###321122930000000000000008");
//        accessCodes.add("M40054542###321122930000000000000008");
//        accessCodes.add("M00230053###321122930000000000000069");
//        accessCodes.add("M40055086###321122930000000000000008");
//        accessCodes.add("M56497968###321122930000000000000008");
//        accessCodes.add("MSTPJS1002982617###321122930000000000000073");
//        accessCodes.add("M18626372###321122930000000000000006");
//        accessCodes.add("M15206231###321122930000000000000006");
//        accessCodes.add("M19206385###321122930000000000000006");
//        accessCodes.add("154317407###321122930000000000000017");
//        accessCodes.add("HLWZX52720220638384###321122930000000000000073");
//        accessCodes.add("M55657965###321122930000000000000008");
//        accessCodes.add("MSTPJS1002981410###321122930000000000000006");
//        accessCodes.add("M46157717###321122930000000000000021");
//        accessCodes.add("MSTPJS1002968644###321122930000000000000006");
//        accessCodes.add("M00262813###321122930000000000000069");
//        accessCodes.add("M40053262###321122930000000000000008");
//        accessCodes.add("M40050073###321122930000000000000008");
//        accessCodes.add("MSTPJS1001698318###321122930000000000000037");
//        accessCodes.add("M40052595###321122930000000000000008");
//        accessCodes.add("MSTPJS1002887098###321122930000000000000079");
//        accessCodes.add("M24061378###321122930000000000000006");
//        accessCodes.add("M57007966###321122930000000000000008");
//        accessCodes.add("MSTPJS1002013974###321122930000000000000006");
//        accessCodes.add("154317481###321122930000000000000017");
//        accessCodes.add("12458050###321122930000000000000013");
//        accessCodes.add("M00215114###321122930000000000000056");
//        accessCodes.add("M08851186###321122930000000000000006");
//        accessCodes.add("M00113142###321122930000000000000024");
//        accessCodes.add("M16581360###321122930000000000000006");
//        accessCodes.add("MSTPJS1003226580###321122930000000000000006");
//        accessCodes.add("MSTPJS1002652972###321122930000000000000006");
//        accessCodes.add("M19941423###321122930000000000000006");
//        accessCodes.add("M49933003###321122930000000000000008");
//        accessCodes.add("M51907972###321122930000000000000008");
//        accessCodes.add("M40063854###321122930000000000000008");
//        accessCodes.add("M61702971###321122930000000000000008");
//        accessCodes.add("M40065916###321122930000000000000008");
//        accessCodes.add("MSTPJS1002901386###321122930000000000000006");
//        accessCodes.add("M00127799###321122930000000000000013");
//        accessCodes.add("M55782964###321122930000000000000008");
//        accessCodes.add("M40052868###321122930000000000000008");
//        accessCodes.add("M10106184###321122930000000000000006");
//        accessCodes.add("M07027667###321122930000000000000006");
//        accessCodes.add("12264604###321122930000000000000013");
//        accessCodes.add("MSTPBJ1001979007###321122930000000000000008");
//        accessCodes.add("MSTPJS1002943759###321122930000000000000006");
//        accessCodes.add("MSTPJS1002191246###321122930000000000000006");
//        accessCodes.add("M47102958###321122930000000000000008");
//        accessCodes.add("M50212976###321122930000000000000008");
//        accessCodes.add("M00104726###321122930000000000000056");
//        accessCodes.add("12450072###321122930000000000000024");
//        accessCodes.add("M00314501###321122930000000000000046");
//        accessCodes.add("M00244781###321122930000000000000056");
//        accessCodes.add("M10176171###321122930000000000000006");
//        accessCodes.add("Z12245###321122930000000000000017");
//        accessCodes.add("MSTPJT1001697406###321122930000000000000006");
//        accessCodes.add("M40052982###321122930000000000000008");
//        accessCodes.add("154317900###321122930000000000000017");
//        accessCodes.add("M23186391###321122930000000000000006");
//        accessCodes.add("M40053037###321122930000000000000008");
//        accessCodes.add("MSTPJS1002228187###321122930000000000000006");
//        accessCodes.add("M07004538###321122930000000000000006");
//        accessCodes.add("MSTPJT1001983753###321122930000000000000013");
//        accessCodes.add("M23516385###321122930000000000000006");
//        accessCodes.add("MSTPJS1003313440###321122930000000000000037");
//        accessCodes.add("MSTPJS1001355210###321122930000000000000008");
//        accessCodes.add("M24861381###321122930000000000000006");
//        accessCodes.add("M40051451###321122930000000000000008");
//        accessCodes.add("M21876376###321122930000000000000006");
//        accessCodes.add("M55482968###321122930000000000000008");
//        accessCodes.add("M27961893###321122930000000000000006");
//        accessCodes.add("M40047810###321122930000000000000008");
//        accessCodes.add("MSTPJS1003251127###321122930000000000000079");
//        accessCodes.add("M00286956###321122930000000000000069");
//        accessCodes.add("M19271405###321122930000000000000006");
//        accessCodes.add("M00292731###321122930000000000000069");
//        accessCodes.add("M48477708###321122930000000000000021");
//        accessCodes.add("M43677956###321122930000000000000008");
//        accessCodes.add("IPRANJS1002319247###321122930000000000000063");
//        accessCodes.add("上海南京ZQOTN00055###321122930000000000000006");
//        accessCodes.add("M00206669###321122930000000000000013");
//        accessCodes.add("M12726159###321122930000000000000006");
//        accessCodes.add("HLWZX51020211267298###321122930000000000000013");
//        accessCodes.add("M00113201###321122930000000000000024");
//        accessCodes.add("M22036380###321122930000000000000006");
//        accessCodes.add("M40052877###321122930000000000000008");
//        accessCodes.add("154318081###321122930000000000000017");
//        accessCodes.add("MSTPJS1002713982###321122930000000000000037");
//        accessCodes.add("MSTPBJ1002382222###321122930000000000000008");
//        accessCodes.add("M17991378###321122930000000000000006");
//        accessCodes.add("M40054887###321122930000000000000008");
//        accessCodes.add("M40033045###321122930000000000000008");
//        accessCodes.add("M40262691###321122930000000000000021");
//        accessCodes.add("1010039###321122930000000000000046");
//        accessCodes.add("M16311381###321122930000000000000006");
//        accessCodes.add("MSTPJS1001741358###321122930000000000000013");
//        accessCodes.add("M40052947###321122930000000000000008");
//        accessCodes.add("MSTPJS1002783669###321122930000000000000006");
//        accessCodes.add("M00120723###321122930000000000000024");
//        accessCodes.add("M40051487###321122930000000000000008");
//        accessCodes.add("M51492966###321122930000000000000008");
//        accessCodes.add("15958931###321122930000000000000013");
//        accessCodes.add("13849548###321122930000000000000013");
//        accessCodes.add("M00333901###321122930000000000000024");
//        accessCodes.add("M24881376###321122930000000000000006");
//        accessCodes.add("M10176201###321122930000000000000006");
//        accessCodes.add("M41047967###321122930000000000000008");
//        accessCodes.add("M28346893###321122930000000000000006");
//        accessCodes.add("M00247789###321122930000000000000079");
//        accessCodes.add("M53577963###321122930000000000000008");
//        accessCodes.add("M48117956###321122930000000000000008");
//        accessCodes.add("M53907976###321122930000000000000008");
//        accessCodes.add("15958878###321122930000000000000013");
//        accessCodes.add("M40062145###321122930000000000000008");
//        accessCodes.add("M07036504###321122930000000000000006");
//        accessCodes.add("15959695###321122930000000000000013");
//        accessCodes.add("12265476###321122930000000000000013");
//        accessCodes.add("M00106377###321122930000000000000069");
//        accessCodes.add("M48452706###321122930000000000000021");
//        accessCodes.add("MSTPJS1002189114###321122930000000000000063");
//        accessCodes.add("MSTPJS1001995242###321122930000000000000063");
//        accessCodes.add("MSTPJS1001984115###321122930000000000000069");
//        accessCodes.add("MSTPJS1002170322###321122930000000000000006");
//        accessCodes.add("M52292969###321122930000000000000008");
//        accessCodes.add("MSTPJS1003083948###321122930000000000000006");
//        accessCodes.add("M23516409###321122930000000000000006");
//        accessCodes.add("M64488166###321122930000000000000008");
//        accessCodes.add("M45772956###321122930000000000000008");
//        accessCodes.add("M40068362###321122930000000000000008");
//        accessCodes.add("M00261066###321122930000000000000056");
//        accessCodes.add("M00263696###321122930000000000000069");
//        accessCodes.add("MSTPJS1001811179###321122930000000000000006");
//        accessCodes.add("M55392968###321122930000000000000008");
//        accessCodes.add("MSTPJS1002526109###321122930000000000000006");
//        accessCodes.add("M60777965###321122930000000000000008");
//        accessCodes.add("15958998###321122930000000000000013");
//        accessCodes.add("HLWZX523202309102743###321122930000000000000079");
//        accessCodes.add("M40052356###321122930000000000000008");
//        accessCodes.add("M40075208###321122930000000000000008");
//        accessCodes.add("M00279023###321122930000000000000079");
//        accessCodes.add("M16571375###321122930000000000000006");
//        accessCodes.add("M18016372###321122930000000000000006");
//        accessCodes.add("M58517966###321122930000000000000008");
//        accessCodes.add("M00210359###321122930000000000000013");
//        accessCodes.add("MSTPJS1003000434###321122930000000000000008");
//        accessCodes.add("MSTPJS1001700235###321122930000000000000056");
//        accessCodes.add("M09416159###321122930000000000000006");
//        accessCodes.add("MSTPJS1002493077###321122930000000000000073");
//        accessCodes.add("M48602706###321122930000000000000021");
//        accessCodes.add("M00314978###321122930000000000000046");
//        accessCodes.add("140246443###321122930000000000000063");
//        accessCodes.add("MSTPJS1002162468###321122930000000000000013");
//        accessCodes.add("M00002909###321122930000000000000037");
//        accessCodes.add("M40052973###321122930000000000000008");
//        accessCodes.add("M41257679###321122930000000000000021");
//        accessCodes.add("M54142964###321122930000000000000008");
//        accessCodes.add("M30311888###321122930000000000000006");
//        accessCodes.add("MSTPJS1003046977###321122930000000000000021");
//        accessCodes.add("M58972970###321122930000000000000008");
//        accessCodes.add("MSTPJS1003290725###321122930000000000000046");
//        accessCodes.add("M00218916###321122930000000000000046");
//        accessCodes.add("M42327959###321122930000000000000008");
//        accessCodes.add("M00145321###321122930000000000000037");
//        accessCodes.add("MSTPJS1003355774###321122930000000000000006");
//        accessCodes.add("K00064###321122930000000000000008");
//        accessCodes.add("MSTPJS1002986310###321122930000000000000006");
//        accessCodes.add("M00113148###321122930000000000000024");
//        accessCodes.add("南京南通ETN0661NP###321122930000000000000006");
//        accessCodes.add("MSTPJS1001663595###321122930000000000000056");
//        accessCodes.add("M40077039###321122930000000000000008");
//        accessCodes.add("MSTPJT1001797092###321122930000000000000013");
//        accessCodes.add("MSTPJS1002287898###321122930000000000000046");
//        accessCodes.add("MSTPJS1003024969###321122930000000000000008");
//        accessCodes.add("M40053016###321122930000000000000008");
//        accessCodes.add("M43921919###321122930000000000000017");
//        accessCodes.add("MSTPJS1003205120###321122930000000000000008");
//        accessCodes.add("M07020962###321122930000000000000006");
//        accessCodes.add("MSTPJS1001950113###321122930000000000000006");
//        accessCodes.add("M61702966###321122930000000000000008");
//        accessCodes.add("154318090###321122930000000000000017");
//        accessCodes.add("M08866162###321122930000000000000006");
//        accessCodes.add("M08846222###321122930000000000000006");
//        accessCodes.add("M40052715###321122930000000000000008");
//        accessCodes.add("M51153494###321122930000000000000063");
//        accessCodes.add("M40052110###321122930000000000000008");
//        accessCodes.add("MSTPJS1002823182###321122930000000000000079");
//        accessCodes.add("M00253495###321122930000000000000056");
//        accessCodes.add("M10701168###321122930000000000000006");
//        accessCodes.add("M40056462###321122930000000000000008");
//        accessCodes.add("M51697969###321122930000000000000008");
//        accessCodes.add("MSTPJS1003059979###321122930000000000000006");
//        accessCodes.add("M07259442###321122930000000000000006");
//        accessCodes.add("M40053028###321122930000000000000008");
//        accessCodes.add("北京苏州ZQOTN00030###321122930000000000000008");
//        accessCodes.add("M43612962###321122930000000000000008");
//        accessCodes.add("MSTPJS1003164406###321122930000000000000006");
//        accessCodes.add("MSTPZJ1002772287###321122930000000000000008");
//        accessCodes.add("154317913###321122930000000000000017");
//        accessCodes.add("M46696929###321122930000000000000017");
//        accessCodes.add("南京徐州ETN0413NP###321122930000000000000046");
//        accessCodes.add("M47712956###321122930000000000000008");
//        accessCodes.add("M43602959###321122930000000000000008");
//        accessCodes.add("HLWZX523202308101911###321122930000000000000079");
//        accessCodes.add("M29361888###321122930000000000000006");
//        accessCodes.add("L1003466###321122930000000000000069");
//        accessCodes.add("13874730###321122930000000000000008");
//        accessCodes.add("154318075###321122930000000000000017");
//        accessCodes.add("M61822968###321122930000000000000008");
//        accessCodes.add("M63103174###321122930000000000000008");
//        accessCodes.add("M42367679###321122930000000000000021");
//        accessCodes.add("M28351894###321122930000000000000006");
//        accessCodes.add("M55512964###321122930000000000000008");
//        accessCodes.add("MSTPJS1002322421###321122930000000000000006");
//        accessCodes.add("M48876922###321122930000000000000017");
//        accessCodes.add("M19816384###321122930000000000000006");
//        accessCodes.add("M00295957###321122930000000000000024");
//        accessCodes.add("M57942965###321122930000000000000008");
//        accessCodes.add("M54977967###321122930000000000000008");
//        accessCodes.add("MSTPJS1003251127###321122930000000000000006");
//        accessCodes.add("MSTPJS1003266534###321122930000000000000006");
//        accessCodes.add("M40047816###321122930000000000000008");
//        accessCodes.add("M00291222###321122930000000000000046");
//        accessCodes.add("M00281490###321122930000000000000024");
//        accessCodes.add("M40051570###321122930000000000000008");
//        accessCodes.add("M21516376###321122930000000000000006");
//        accessCodes.add("MSTPJS1003230540###321122930000000000000006");
//        accessCodes.add("110405392###321122930000000000000006");
//        accessCodes.add("18STN010530###321122930000000000000063");
//        accessCodes.add("M08861165###321122930000000000000006");
//        accessCodes.add("M40056814###321122930000000000000008");
//        accessCodes.add("MSTPJS1003219681###321122930000000000000013");
//        accessCodes.add("M46157709###321122930000000000000021");
//        accessCodes.add("M65358166###321122930000000000000008");
//        accessCodes.add("M10126162###321122930000000000000006");
//        accessCodes.add("M00271711###321122930000000000000073");
//        accessCodes.add("M40057806###321122930000000000000008");
//        accessCodes.add("JS10cy001962###321122930000000000000013");
//        accessCodes.add("M00231359###321122930000000000000024");
//        accessCodes.add("M10176165###321122930000000000000006");
//        accessCodes.add("M22376382###321122930000000000000006");
//        accessCodes.add("M00113529###321122930000000000000037");
//        accessCodes.add("M00262453###321122930000000000000073");
//        accessCodes.add("MSTPJS1002287898###321122930000000000000006");
//        accessCodes.add("M40002937###321122930000000000000021");
//        accessCodes.add("M10341201###321122930000000000000006");
//        accessCodes.add("M17806372###321122930000000000000006");
//        accessCodes.add("M00273560###321122930000000000000079");
//        accessCodes.add("M00291588###321122930000000000000024");
//        accessCodes.add("M43627691###321122930000000000000021");
//        accessCodes.add("M11836159###321122930000000000000006");
//        accessCodes.add("M00299844###321122930000000000000079");
//        accessCodes.add("154317475###321122930000000000000017");
//        accessCodes.add("M40123157###321122930000000000000017");
//        accessCodes.add("M40003302###321122930000000000000021");
//        accessCodes.add("M10171162###321122930000000000000006");
//        accessCodes.add("M40053900###321122930000000000000008");
//        accessCodes.add("M16951375###321122930000000000000006");
//        accessCodes.add("K02806###321122930000000000000008");
//        accessCodes.add("MSTPJS1003167446###321122930000000000000021");
//        accessCodes.add("M23516388###321122930000000000000006");
//        accessCodes.add("MSTPJS1002887098###321122930000000000000006");
//        accessCodes.add("M17541387###321122930000000000000006");
//        accessCodes.add("154318085###321122930000000000000017");
//        accessCodes.add("M48587708###321122930000000000000021");
//        accessCodes.add("北京南京ZQOTN00099###321122930000000000000006");
//        accessCodes.add("MSTPJS1001701105###321122930000000000000006");
//        accessCodes.add("M00005658###321122930000000000000037");
//        accessCodes.add("M20286380###321122930000000000000006");
//        accessCodes.add("M00297024###321122930000000000000069");
//        accessCodes.add("15958971###321122930000000000000013");
//        accessCodes.add("MSTPJS1003113473###321122930000000000000006");
//        accessCodes.add("15958609###321122930000000000000013");
//        accessCodes.add("M57352964###321122930000000000000008");
//        accessCodes.add("K01415###321122930000000000000008");
//        accessCodes.add("M40003347###321122930000000000000021");
//        accessCodes.add("M40352962###321122930000000000000008");
//        accessCodes.add("MSTPJS1003301637###321122930000000000000013");
//        accessCodes.add("12265342###321122930000000000000013");
//        accessCodes.add("M43627694###321122930000000000000021");
//        accessCodes.add("M57350927###321122930000000000000017");
//        accessCodes.add("M55052964###321122930000000000000008");
//        accessCodes.add("M00309125###321122930000000000000069");
//        accessCodes.add("M00217881###321122930000000000000013");
//        accessCodes.add("M48562706###321122930000000000000021");
//        accessCodes.add("M41147680###321122930000000000000021");
//        accessCodes.add("M21151380###321122930000000000000006");
//        accessCodes.add("MSTPJS1001860036###321122930000000000000006");
//        accessCodes.add("M08851207###321122930000000000000006");
//        accessCodes.add("M40064028###321122930000000000000008");
//        accessCodes.add("M40054539###321122930000000000000008");
//        accessCodes.add("M47712978###321122930000000000000008");
//        accessCodes.add("M40064267###321122930000000000000008");
//        accessCodes.add("X00012562NJ570###321122930000000000000006");
//        accessCodes.add("M48513476###321122930000000000000063");
//        accessCodes.add("M40056253###321122930000000000000008");
//        accessCodes.add("M28161890###321122930000000000000006");
//        accessCodes.add("M00261588###321122930000000000000079");
//        accessCodes.add("M47987708###321122930000000000000021");
//        accessCodes.add("12265326###321122930000000000000013");
//        accessCodes.add("M48537962###321122930000000000000008");
//        accessCodes.add("M40052970###321122930000000000000008");
//        accessCodes.add("M00196378###321122930000000000000013");
//        accessCodes.add("M00323871###321122930000000000000024");
//        accessCodes.add("M40044761###321122930000000000000008");
//        accessCodes.add("M40047813###321122930000000000000008");
//        accessCodes.add("M61997964###321122930000000000000008");
//        accessCodes.add("MSTPJS1002256084###321122930000000000000046");
//        accessCodes.add("M48372703###321122930000000000000021");
//        accessCodes.add("M00313145###321122930000000000000046");
//        accessCodes.add("MSTPJS1002903588###321122930000000000000008");
//        accessCodes.add("116954881###321122930000000000000006");
//        accessCodes.add("154317902###321122930000000000000017");
//        accessCodes.add("154317919###321122930000000000000017");
//        accessCodes.add("M27821888###321122930000000000000006");
//        accessCodes.add("M48747956###321122930000000000000008");
//        accessCodes.add("M40021987###321122930000000000000008");
//        accessCodes.add("M17061363###321122930000000000000006");
//        accessCodes.add("M00232858###321122930000000000000013");
//        accessCodes.add("12346039###321122930000000000000024");
//        accessCodes.add("M00290704###321122930000000000000079");
//        accessCodes.add("MSTPJS1003190816###321122930000000000000056");
//        accessCodes.add("M08866195###321122930000000000000006");
//        accessCodes.add("M08866186###321122930000000000000006");
//        accessCodes.add("MSTPJS1002322931###321122930000000000000008");
//        accessCodes.add("MSTPJS1002263033###321122930000000000000006");
//        accessCodes.add("M40052334###321122930000000000000008");
//        accessCodes.add("M19826378###321122930000000000000006");
//        accessCodes.add("M00297567###321122930000000000000046");
//        accessCodes.add("M40055107###321122930000000000000008");
//        accessCodes.add("MSTPJS1001955011###321122930000000000000073");
//        accessCodes.add("M08851174###321122930000000000000006");
//        accessCodes.add("M52697963###321122930000000000000008");
//        accessCodes.add("M40052577###321122930000000000000008");
//        accessCodes.add("HLWZX51820220537437###321122930000000000000063");
//        accessCodes.add("M41677965###321122930000000000000008");
//        accessCodes.add("M44463004###321122930000000000000008");
//        accessCodes.add("M26521890###321122930000000000000006");
//        accessCodes.add("M22061378###321122930000000000000006");
//        accessCodes.add("MSTPJS1003173435###321122930000000000000073");
//        accessCodes.add("MSTPJS1001984934###321122930000000000000008");
//        accessCodes.add("M00195409###321122930000000000000013");
//        accessCodes.add("HLWZX025202206104563###321122930000000000000006");
//        accessCodes.add("MSTPJS1003030513###321122930000000000000069");
//        accessCodes.add("MSTPJS1002964550###321122930000000000000006");
//        accessCodes.add("M00283189###321122930000000000000079");
//        accessCodes.add("MSTPJS1002321048###321122930000000000000056");
//        accessCodes.add("M41687959###321122930000000000000008");
//        accessCodes.add("MSTPJS1003025180###321122930000000000000056");
//        accessCodes.add("JS19cy000942###321122930000000000000069");
//        accessCodes.add("M07261252###321122930000000000000006");
//        accessCodes.add("M65303166###321122930000000000000008");
//        accessCodes.add("M31431888###321122930000000000000006");
//        accessCodes.add("15958984###321122930000000000000013");
//        accessCodes.add("M16566357###321122930000000000000006");
//        accessCodes.add("MSTPJS1002815060###321122930000000000000008");
//        accessCodes.add("M40053367###321122930000000000000008");
//        accessCodes.add("M40058583###321122930000000000000008");
//        accessCodes.add("M00183267###321122930000000000000013");
//        accessCodes.add("M00290058###321122930000000000000069");
//        accessCodes.add("M40052574###321122930000000000000008");
//        accessCodes.add("M30101888###321122930000000000000006");
//        accessCodes.add("M00113250###321122930000000000000024");
//        accessCodes.add("M40053342###321122930000000000000008");
//        accessCodes.add("M12736178###321122930000000000000006");
//        accessCodes.add("M17606381###321122930000000000000006");
//        accessCodes.add("M40051722###321122930000000000000008");
//        accessCodes.add("M07006405###321122930000000000000006");
//        accessCodes.add("HLWZX523202311105972###321122930000000000000079");
//        accessCodes.add("28034795###321122930000000000000037");
//        accessCodes.add("MSTPJT1001982781###321122930000000000000013");
//        accessCodes.add("MSTPJT1001798112###321122930000000000000013");
//        accessCodes.add("M00247080###321122930000000000000079");
//        accessCodes.add("M59327964###321122930000000000000008");
//        accessCodes.add("M00323431###321122930000000000000024");
//        accessCodes.add("M24251401###321122930000000000000006");
//        accessCodes.add("M40262694###321122930000000000000021");
//        accessCodes.add("MSTPJS1001984101###321122930000000000000006");
//        accessCodes.add("10606343###321122930000000000000024");
//        accessCodes.add("M00264008###321122930000000000000024");
//        accessCodes.add("M40049785###321122930000000000000008");
//        accessCodes.add("HLWZX51220210326959###321122930000000000000008");
//        accessCodes.add("154317398###321122930000000000000017");
//        accessCodes.add("HLWZX51420220778802###321122930000000000000021");
//        accessCodes.add("M00272180###321122930000000000000069");
//        accessCodes.add("M00221948###321122930000000000000069");
//        accessCodes.add("M00345123###321122930000000000000024");
//        accessCodes.add("M61792964###321122930000000000000008");
//        accessCodes.add("M40053849###321122930000000000000008");
//        accessCodes.add("M00265368###321122930000000000000073");
//        accessCodes.add("M08851189###321122930000000000000006");
//        accessCodes.add("15958859###321122930000000000000013");
//        accessCodes.add("M48557716###321122930000000000000021");
//        accessCodes.add("M50562970###321122930000000000000008");
//        accessCodes.add("154317917###321122930000000000000017");
//        accessCodes.add("M07268843###321122930000000000000006");
//        accessCodes.add("M00181710###321122930000000000000037");
//        accessCodes.add("15959493###321122930000000000000013");
//        accessCodes.add("M00002939###321122930000000000000037");
//        accessCodes.add("MSTPJT1001800067###321122930000000000000013");
//        accessCodes.add("MSTPJS1002444250###321122930000000000000006");
//        accessCodes.add("M14976231###321122930000000000000006");
//        accessCodes.add("154318310###321122930000000000000017");
//        accessCodes.add("19611809###321122930000000000000008");
//        accessCodes.add("M54277968###321122930000000000000008");
//        accessCodes.add("M40049767###321122930000000000000008");
//        accessCodes.add("M40053394###321122930000000000000008");
//        accessCodes.add("MSTPJS1001808283###321122930000000000000008");
//        accessCodes.add("HLWZX515202404124975###321122930000000000000017");
//        accessCodes.add("MSTPJS1002872263###321122930000000000000006");
//        accessCodes.add("MSTPJS1001661183###321122930000000000000006");
//        accessCodes.add("M40057034###321122930000000000000008");
//        accessCodes.add("M40040399###321122930000000000000008");
//        accessCodes.add("M08866165###321122930000000000000006");
//        accessCodes.add("M00290946###321122930000000000000079");
//        accessCodes.add("12264565###321122930000000000000013");
//        accessCodes.add("M46157727###321122930000000000000021");
//        accessCodes.add("M14996231###321122930000000000000006");
//        accessCodes.add("M40059216###321122930000000000000008");
//        accessCodes.add("M00290808###321122930000000000000079");
//        accessCodes.add("M58112964###321122930000000000000008");
//        accessCodes.add("M22316432###321122930000000000000006");
//        accessCodes.add("M00308385###321122930000000000000069");
//        accessCodes.add("MSTPJS1001991582###321122930000000000000006");
//        accessCodes.add("M16141354###321122930000000000000006");
//        accessCodes.add("M15831291###321122930000000000000006");
//        accessCodes.add("M00113194###321122930000000000000024");
//        accessCodes.add("M50361932###321122930000000000000017");
//        accessCodes.add("M48272965###321122930000000000000008");
//        accessCodes.add("M40052721###321122930000000000000008");
//        accessCodes.add("M17061354###321122930000000000000006");
//        accessCodes.add("M00222179###321122930000000000000013");
//        accessCodes.add("M00003455###321122930000000000000037");
//        accessCodes.add("M16671354###321122930000000000000006");
//        accessCodes.add("M40052225###321122930000000000000008");
//        accessCodes.add("MSTPJS1003078477###321122930000000000000006");
//        accessCodes.add("M00314964###321122930000000000000046");
//        accessCodes.add("M48742959###321122930000000000000008");
//        accessCodes.add("M00293622###321122930000000000000069");
//        accessCodes.add("M40512968###321122930000000000000008");
//        accessCodes.add("MSTPJS1003194402###321122930000000000000006");
//        accessCodes.add("M00220083###321122930000000000000056");
//        accessCodes.add("M49602968###321122930000000000000008");
//        accessCodes.add("M21876377###321122930000000000000006");
//        accessCodes.add("MSTPJS1003370224###321122930000000000000013");
//        accessCodes.add("MSTPJT1001796088###321122930000000000000013");
//        accessCodes.add("M00004415###321122930000000000000037");
//        accessCodes.add("M15631246###321122930000000000000006");
//        accessCodes.add("MSTPBJ1003013423###321122930000000000000063");
//        accessCodes.add("12265092###321122930000000000000013");
//        accessCodes.add("12265358###321122930000000000000013");
//        accessCodes.add("M00181389###321122930000000000000013");
//        accessCodes.add("M00262706###321122930000000000000056");
//        accessCodes.add("M66093163###321122930000000000000008");
//        accessCodes.add("L1003971###321122930000000000000069");
//        accessCodes.add("M27336892###321122930000000000000006");
//        accessCodes.add("M48517703###321122930000000000000021");
//        accessCodes.add("M40065427###321122930000000000000008");
//        accessCodes.add("154318308###321122930000000000000017");
//        accessCodes.add("MSTPJT1001796060###321122930000000000000013");
//        accessCodes.add("MSTPJS1002701073###321122930000000000000006");
//        accessCodes.add("M40054176###321122930000000000000008");
//        accessCodes.add("M00113326###321122930000000000000024");
//        accessCodes.add("M43507971###321122930000000000000008");
//        accessCodes.add("M24376376###321122930000000000000006");
//        accessCodes.add("M22041385###321122930000000000000006");
//        accessCodes.add("M42762679###321122930000000000000021");
//        accessCodes.add("M40048790###321122930000000000000008");
//        accessCodes.add("M08866174###321122930000000000000006");
//        accessCodes.add("M40053034###321122930000000000000008");
//        accessCodes.add("HLWZX51520221283122###321122930000000000000017");
//        accessCodes.add("M40053259###321122930000000000000008");
//        accessCodes.add("M00267992###321122930000000000000024");
//        accessCodes.add("M19386428###321122930000000000000006");
//        accessCodes.add("154317911###321122930000000000000017");
//        accessCodes.add("M40053013###321122930000000000000008");
//        accessCodes.add("M50642963###321122930000000000000008");
//        accessCodes.add("M54277970###321122930000000000000008");
//        accessCodes.add("HLWZX51320230394981###321122930000000000000024");
//        accessCodes.add("MSTPJT1001800066###321122930000000000000013");
//        accessCodes.add("M40052605###321122930000000000000008");
//        accessCodes.add("M00236091###321122930000000000000024");
//        accessCodes.add("112011837###321122930000000000000006");
//        accessCodes.add("M00142186###321122930000000000000037");
//        accessCodes.add("MSTPZJ1002791295###321122930000000000000006");
//        accessCodes.add("MSTPJS1003219324###321122930000000000000021");
//        accessCodes.add("M00231391###321122930000000000000024");
//        accessCodes.add("M16681360###321122930000000000000006");
//        accessCodes.add("M40054167###321122930000000000000008");
//        accessCodes.add("M00271713###321122930000000000000073");
//        accessCodes.add("M00231400###321122930000000000000024");
//        accessCodes.add("M00145884###321122930000000000000037");
//        accessCodes.add("115315619###321122930000000000000006");
//        accessCodes.add("MSTPJS1002726401###321122930000000000000046");
//        accessCodes.add("M40047923###321122930000000000000008");
//        accessCodes.add("MSTPJS1002415329###321122930000000000000063");
//        accessCodes.add("M17166369###321122930000000000000006");
//        accessCodes.add("M00338212###321122930000000000000024");
//        accessCodes.add("M48477703###321122930000000000000021");
//        accessCodes.add("HLWZX025202304152783###321122930000000000000006");
//        accessCodes.add("MSTPJS1002171387###321122930000000000000006");
//        accessCodes.add("25738257###321122930000000000000008");
//        accessCodes.add("M15681243###321122930000000000000006");
//        accessCodes.add("M00291709###321122930000000000000079");
//        accessCodes.add("M00215132###321122930000000000000056");
//        accessCodes.add("M00291723###321122930000000000000046");
//        accessCodes.add("M00298119###321122930000000000000046");
//        accessCodes.add("MSTPHB1001885688###321122930000000000000006");
//        accessCodes.add("MSTPJS1002770538###321122930000000000000006");
//        accessCodes.add("MSTPJS1001857066###321122930000000000000008");
//        accessCodes.add("M20321378###321122930000000000000006");
//        accessCodes.add("M40053225###321122930000000000000008");
//        accessCodes.add("M51127963###321122930000000000000008");
//        accessCodes.add("HLWZX025202405200108###321122930000000000000006");
//        accessCodes.add("M26441903###321122930000000000000006");
//        accessCodes.add("MSTPJS1002000641###321122930000000000000008");
//        accessCodes.add("MSTPJS1002083033###321122930000000000000056");
//        accessCodes.add("M00002822###321122930000000000000037");
//        accessCodes.add("M40053170###321122930000000000000008");
//        accessCodes.add("M43182995###321122930000000000000008");
//        accessCodes.add("M00231403###321122930000000000000024");
//        accessCodes.add("M40059596###321122930000000000000008");
//        accessCodes.add("M46137963###321122930000000000000008");
//        accessCodes.add("M00157388###321122930000000000000037");
//        accessCodes.add("M00306948###321122930000000000000024");
//        accessCodes.add("M40050987###321122930000000000000008");
//        accessCodes.add("M08851177###321122930000000000000006");
//        accessCodes.add("连云港嘉兴ZQOTN00001###321122930000000000000063");
//        accessCodes.add("M42437965###321122930000000000000008");
//        accessCodes.add("M57377964###321122930000000000000008");
//        accessCodes.add("149510859###321122930000000000000021");
//        accessCodes.add("M00127005###321122930000000000000013");
//        accessCodes.add("MSTPJS1003100429###321122930000000000000037");
//        accessCodes.add("M18291375###321122930000000000000006");
//        accessCodes.add("14798679###321122930000000000000008");
//        accessCodes.add("M23516401###321122930000000000000006");
//        accessCodes.add("23956229###321122930000000000000008");
//        accessCodes.add("M47637701###321122930000000000000021");
//        accessCodes.add("192286003###321122930000000000000063");
//        accessCodes.add("M18096384###321122930000000000000006");
//        accessCodes.add("M16651372###321122930000000000000006");
//        accessCodes.add("M00296721###321122930000000000000069");
//        accessCodes.add("M00113225###321122930000000000000024");
//        accessCodes.add("M07002152###321122930000000000000006");
//        accessCodes.add("M19576385###321122930000000000000006");
//        accessCodes.add("MSTPJS1003186991###321122930000000000000056");
//        accessCodes.add("M40037012###321122930000000000000008");
//        accessCodes.add("M00263760###321122930000000000000069");
//        accessCodes.add("M00216877###321122930000000000000013");
//        accessCodes.add("M42058482###321122930000000000000063");
//        accessCodes.add("M10276171###321122930000000000000006");
//        accessCodes.add("M10176168###321122930000000000000006");
//        accessCodes.add("M47922963###321122930000000000000008");
//        accessCodes.add("M10171174###321122930000000000000006");
//        accessCodes.add("M25996888###321122930000000000000006");
//        accessCodes.add("M00249151###321122930000000000000079");
//        accessCodes.add("M22316439###321122930000000000000006");
//        accessCodes.add("M17386405###321122930000000000000006");
//        accessCodes.add("MSTPJS1003225613###321122930000000000000006");
//        accessCodes.add("M00280268###321122930000000000000024");
//        accessCodes.add("M24596377###321122930000000000000006");
//        accessCodes.add("M24756405###321122930000000000000006");
//        accessCodes.add("MSTPJS1003080701###321122930000000000000006");
//        accessCodes.add("154317490###321122930000000000000017");
//        accessCodes.add("118159788###321122930000000000000046");
//        accessCodes.add("M40059507###321122930000000000000008");
//        accessCodes.add("M40053379###321122930000000000000008");
//        accessCodes.add("MSTPJS1002493077###321122930000000000000006");
//        accessCodes.add("MSTPJS1001750785###321122930000000000000021");
//        accessCodes.add("M40051349###321122930000000000000008");
//        accessCodes.add("M00280103###321122930000000000000046");
//        accessCodes.add("MSTPJS1003059592###321122930000000000000006");
//        accessCodes.add("M40048476###321122930000000000000008");
//        accessCodes.add("M00334303###321122930000000000000024");
//        accessCodes.add("M18211375###321122930000000000000006");
//        accessCodes.add("MSTPJS1001995242###321122930000000000000006");
//        accessCodes.add("M10611174###321122930000000000000006");
//        accessCodes.add("MSTPJS1003044603###321122930000000000000017");
//        accessCodes.add("MSTPJS1001853390###321122930000000000000024");
//        accessCodes.add("M40052125###321122930000000000000008");
//        accessCodes.add("M10281162###321122930000000000000006");
//        accessCodes.add("12265466###321122930000000000000013");
//        accessCodes.add("MSTPJS1002589289###321122930000000000000006");
//        accessCodes.add("M10686162###321122930000000000000006");
//        accessCodes.add("MSTPJS1002677790###321122930000000000000006");
//        accessCodes.add("MSTPJS1003348549###321122930000000000000006");
//        accessCodes.add("MSTPJS1002589255###321122930000000000000006");
//        accessCodes.add("M40116432###321122930000000000000021");
//        accessCodes.add("M31606890###321122930000000000000006");
//        accessCodes.add("M56907969###321122930000000000000008");
//        accessCodes.add("M18891375###321122930000000000000006");
//        accessCodes.add("MSTPJS1003045709###321122930000000000000046");
//        accessCodes.add("M40009286###321122930000000000000063");
//        accessCodes.add("M40052568###321122930000000000000008");
//        accessCodes.add("M00285397###321122930000000000000069");
//        accessCodes.add("MSTPJS1002377624###321122930000000000000056");
//        accessCodes.add("M40058395###321122930000000000000008");
//        accessCodes.add("M43392682###321122930000000000000021");
//        accessCodes.add("M40053022###321122930000000000000008");
//        accessCodes.add("M18661390###321122930000000000000006");
//        accessCodes.add("M30236892###321122930000000000000006");
//        accessCodes.add("M40053067###321122930000000000000008");
//        accessCodes.add("M24096393###321122930000000000000006");
//        accessCodes.add("M12886165###321122930000000000000006");
//        accessCodes.add("M40058280###321122930000000000000008");
//        accessCodes.add("M23546391###321122930000000000000006");
//        accessCodes.add("M00296365###321122930000000000000069");
//        accessCodes.add("M00170562###321122930000000000000037");
//        accessCodes.add("M00262452###321122930000000000000073");
//        accessCodes.add("M25201389###321122930000000000000006");
//        accessCodes.add("12264545###321122930000000000000013");
//        accessCodes.add("M44407691###321122930000000000000021");
//        accessCodes.add("M20241387###321122930000000000000006");
//        accessCodes.add("M00217180###321122930000000000000013");
//        accessCodes.add("115306111###321122930000000000000006");
//        accessCodes.add("M16596364###321122930000000000000006");
//        accessCodes.add("M00111923###321122930000000000000037");
//        accessCodes.add("M46252968###321122930000000000000008");
//        accessCodes.add("MSTPJS1002900051###321122930000000000000079");
//        accessCodes.add("M00308283###321122930000000000000069");
//        accessCodes.add("MSTPJS1001700235###321122930000000000000006");
//        accessCodes.add("M24126397###321122930000000000000006");
//        accessCodes.add("HLWZX51020211054807###321122930000000000000013");
//        accessCodes.add("M40050984###321122930000000000000008");
//        accessCodes.add("M19681385###321122930000000000000006");
//        accessCodes.add("MSTPJT1001801090###321122930000000000000013");
//        accessCodes.add("M40055250###321122930000000000000008");
//        accessCodes.add("M10336165###321122930000000000000006");
//        accessCodes.add("MSTPJS1003334017###321122930000000000000056");
//        accessCodes.add("M44297685###321122930000000000000021");
//        accessCodes.add("HLWZX51820201205631###321122930000000000000063");
//        accessCodes.add("M00297108###321122930000000000000024");
//        accessCodes.add("M15646231###321122930000000000000006");
//        accessCodes.add("M12691159###321122930000000000000006");
//        accessCodes.add("M27981893###321122930000000000000006");
//        accessCodes.add("MSTPJS1002983342###321122930000000000000006");
//        accessCodes.add("M40048754###321122930000000000000008");
//        accessCodes.add("M40002038###321122930000000000000021");
//        accessCodes.add("M10276162###321122930000000000000006");
//        accessCodes.add("M59007968###321122930000000000000008");
//        accessCodes.add("M40050996###321122930000000000000008");
//        accessCodes.add("M40051301###321122930000000000000008");
//        accessCodes.add("M25201392###321122930000000000000006");
//        accessCodes.add("M31436888###321122930000000000000006");
//        accessCodes.add("MSTPJS1002372968###321122930000000000000024");
//        accessCodes.add("M10346187###321122930000000000000006");
//        accessCodes.add("M65198164###321122930000000000000008");
//        accessCodes.add("M48562707###321122930000000000000021");
//        accessCodes.add("154317409###321122930000000000000017");
//        accessCodes.add("M40052237###321122930000000000000008");
//        accessCodes.add("M00113302###321122930000000000000024");
//        accessCodes.add("M07248576###321122930000000000000006");
//        accessCodes.add("M11021165###321122930000000000000006");
//        accessCodes.add("12742110###321122930000000000000013");
//        accessCodes.add("M17086372###321122930000000000000006");
//        accessCodes.add("M23031403###321122930000000000000006");
//        accessCodes.add("MSTPJT1001799064###321122930000000000000013");
//        accessCodes.add("M43442965###321122930000000000000008");
//        accessCodes.add("M18661393###321122930000000000000006");
//        accessCodes.add("M00273974###321122930000000000000046");
//        accessCodes.add("M15396234###321122930000000000000006");
//        accessCodes.add("12264774###321122930000000000000013");
//        accessCodes.add("MSTPJS1002652936###321122930000000000000006");
//        accessCodes.add("M00155127###321122930000000000000037");
//        accessCodes.add("M00115222###321122930000000000000024");
//        accessCodes.add("MSTPJS1001701105###321122930000000000000021");
//        accessCodes.add("M00244799###321122930000000000000056");
//        accessCodes.add("M52192966###321122930000000000000008");
//        accessCodes.add("M21716382###321122930000000000000006");
//        accessCodes.add("M00222476###321122930000000000000069");
//        accessCodes.add("M00143084###321122930000000000000037");
//        accessCodes.add("M00293204###321122930000000000000079");
//        accessCodes.add("M00338205###321122930000000000000024");
//        accessCodes.add("MSTPJT1003263260###321122930000000000000006");
//        accessCodes.add("M21326377###321122930000000000000006");
//        accessCodes.add("M32056892###321122930000000000000006");
//        accessCodes.add("M21646376###321122930000000000000006");
//        accessCodes.add("M48457701###321122930000000000000021");
//        accessCodes.add("M07253567###321122930000000000000006");
//        accessCodes.add("M25081405###321122930000000000000006");
//        accessCodes.add("22091593###321122930000000000000008");
//        accessCodes.add("M28081887###321122930000000000000006");
//        accessCodes.add("M40052171###321122930000000000000008");
//        accessCodes.add("MSTPJS1002164511###321122930000000000000013");
//        accessCodes.add("DG07448###321122930000000000000008");
//        accessCodes.add("M10126168###321122930000000000000006");
//        accessCodes.add("MSTPJS1001565321###321122930000000000000006");
//        accessCodes.add("M08851237###321122930000000000000006");
//        accessCodes.add("M00291111###321122930000000000000069");
//        accessCodes.add("M10106181###321122930000000000000006");
//        accessCodes.add("M40008006###321122930000000000000063");
//        accessCodes.add("9198781###321122930000000000000013");
//        accessCodes.add("M65278168###321122930000000000000008");
//        accessCodes.add("M40052583###321122930000000000000008");
//        accessCodes.add("M00110881###321122930000000000000079");
//        accessCodes.add("M22316435###321122930000000000000006");
//        accessCodes.add("M53577966###321122930000000000000008");
//        accessCodes.add("M00249083###321122930000000000000079");
//        accessCodes.add("M21166379###321122930000000000000006");
//        accessCodes.add("M53988995###321122930000000000000063");
//        accessCodes.add("MSTPJS1002870243###321122930000000000000079");
//        accessCodes.add("M40052725###321122930000000000000008");
//        accessCodes.add("M14976234###321122930000000000000006");
//        accessCodes.add("MSTPJS1002876517###321122930000000000000008");
//        accessCodes.add("15959654###321122930000000000000013");
//        accessCodes.add("M14621231###321122930000000000000006");
//        accessCodes.add("M40050467###321122930000000000000008");
//        accessCodes.add("M48557705###321122930000000000000021");
//        accessCodes.add("M48542707###321122930000000000000021");
//        accessCodes.add("MSTPJS1001565321###321122930000000000000017");
//        accessCodes.add("MSTPJS1001658061###321122930000000000000037");
//        accessCodes.add("M40053173###321122930000000000000008");
//        accessCodes.add("M40052598###321122930000000000000008");
//        accessCodes.add("109005404###321122930000000000000006");
//        accessCodes.add("M22121378###321122930000000000000006");
//        accessCodes.add("M42527958###321122930000000000000008");
//        accessCodes.add("M00294682###321122930000000000000046");
//        accessCodes.add("MSTPJS1002394915###321122930000000000000046");
//        accessCodes.add("M45527971###321122930000000000000008");
//        accessCodes.add("MSTPJS1003121486###321122930000000000000008");
//        accessCodes.add("M21241382###321122930000000000000006");
//        accessCodes.add("M00182322###321122930000000000000037");
//        accessCodes.add("M48132701###321122930000000000000021");
//        accessCodes.add("M48468482###321122930000000000000063");
//        accessCodes.add("M00350323###321122930000000000000024");
//        accessCodes.add("M00215099###321122930000000000000056");
//        accessCodes.add("M50901921###321122930000000000000017");
//        accessCodes.add("M17386387###321122930000000000000006");
//        accessCodes.add("117212471###321122930000000000000046");
//        accessCodes.add("M50076920###321122930000000000000017");
//        accessCodes.add("M22836380###321122930000000000000006");
//        accessCodes.add("M28481888###321122930000000000000006");
//        accessCodes.add("M19406441###321122930000000000000006");
//        accessCodes.add("M00256062###321122930000000000000079");
//        accessCodes.add("M09246159###321122930000000000000006");
//        accessCodes.add("M08851201###321122930000000000000006");
//        accessCodes.add("M43592982###321122930000000000000008");
//        accessCodes.add("M56567969###321122930000000000000008");
//        accessCodes.add("M00280232###321122930000000000000056");
//        accessCodes.add("M57342965###321122930000000000000008");
//        accessCodes.add("M40059874###321122930000000000000008");
//        accessCodes.add("M00236464###321122930000000000000024");
//        accessCodes.add("M40063353###321122930000000000000008");
//        accessCodes.add("M00176786###321122930000000000000037");
//        accessCodes.add("M40053302###321122930000000000000008");
//        accessCodes.add("M00292730###321122930000000000000069");
//        accessCodes.add("MSTPJS1002072192###321122930000000000000021");
//        accessCodes.add("M08866177###321122930000000000000006");
//        accessCodes.add("M00264728###321122930000000000000024");
//        accessCodes.add("MSTPJS1001526785###321122930000000000000008");
//        accessCodes.add("MSTPJS1003260457###321122930000000000000006");
//        accessCodes.add("M40053391###321122930000000000000008");
//        accessCodes.add("M48557719###321122930000000000000021");
//        accessCodes.add("M40051707###321122930000000000000008");
//        accessCodes.add("M48142962###321122930000000000000008");
//        accessCodes.add("M00247595###321122930000000000000056");
//        accessCodes.add("M52128496###321122930000000000000063");
//        accessCodes.add("苏州CTVPN19429A###321122930000000000000008");
//        accessCodes.add("M43607962###321122930000000000000008");
//        accessCodes.add("M16936375###321122930000000000000006");
//        accessCodes.add("M40052749###321122930000000000000008");
//        accessCodes.add("M00287070###321122930000000000000079");
//        accessCodes.add("M43612995###321122930000000000000008");
//        accessCodes.add("115807488###321122930000000000000006");
//        accessCodes.add("M30286888###321122930000000000000006");
//        accessCodes.add("MSTPJS1003252517###321122930000000000000006");
//        accessCodes.add("M00314951###321122930000000000000046");
//        accessCodes.add("M40051337###321122930000000000000008");
//        accessCodes.add("M00251162###321122930000000000000079");
//        accessCodes.add("M00314941###321122930000000000000046");
//        accessCodes.add("M40051530###321122930000000000000008");
//        accessCodes.add("M40054545###321122930000000000000008");
//        accessCodes.add("M52753994###321122930000000000000063");
//        accessCodes.add("M22421387###321122930000000000000006");
//        accessCodes.add("M40367956###321122930000000000000008");
//        accessCodes.add("M44462968###321122930000000000000008");
//        accessCodes.add("M15066235###321122930000000000000006");
//        accessCodes.add("HLWZX51820230674644###321122930000000000000063");
//        accessCodes.add("M16846486###321122930000000000000006");
//        accessCodes.add("M40262679###321122930000000000000021");
//        accessCodes.add("M40050981###321122930000000000000008");
//        accessCodes.add("MSTPZJ1003197644###321122930000000000000063");
//        accessCodes.add("M59222968###321122930000000000000008");
//        accessCodes.add("M40052522###321122930000000000000008");
//        accessCodes.add("M13141159###321122930000000000000006");
//        accessCodes.add("M40052865###321122930000000000000008");
//        accessCodes.add("M00224018###321122930000000000000024");
//        accessCodes.add("M19521384###321122930000000000000006");
//        accessCodes.add("154317909###321122930000000000000017");
//        accessCodes.add("MSTPJS1002986020###321122930000000000000006");
//        accessCodes.add("HLWZX512202307205620###321122930000000000000008");
//        accessCodes.add("M46157708###321122930000000000000021");
//        accessCodes.add("M22236389###321122930000000000000006");
//        accessCodes.add("M00326257###321122930000000000000024");
//        accessCodes.add("M40053415###321122930000000000000008");
//        accessCodes.add("M00115219###321122930000000000000024");
//        accessCodes.add("12264816###321122930000000000000013");
//        accessCodes.add("M52397969###321122930000000000000008");
//        accessCodes.add("M00107370###321122930000000000000079");
//        accessCodes.add("M00183284###321122930000000000000013");
//        accessCodes.add("MSTPJS1002139700###321122930000000000000008");
//        accessCodes.add("M40053243###321122930000000000000008");
//        accessCodes.add("M40013253###321122930000000000000008");
//        accessCodes.add("M17351405###321122930000000000000006");
//        accessCodes.add("M17776375###321122930000000000000006");
//        accessCodes.add("1124477169###321122930000000000000063");
//        accessCodes.add("M22271382###321122930000000000000006");
//        accessCodes.add("M40058106###321122930000000000000008");
//        accessCodes.add("Z11427###321122930000000000000017");
//        accessCodes.add("M40053040###321122930000000000000008");
//        accessCodes.add("M10171165###321122930000000000000006");
//        accessCodes.add("M00311868###321122930000000000000046");
//        accessCodes.add("M19816383###321122930000000000000006");
//        accessCodes.add("M00223098###321122930000000000000079");
//        accessCodes.add("MSTPJS1002769675###321122930000000000000079");
//        accessCodes.add("M40051304###321122930000000000000008");
//        accessCodes.add("MSTPJS1003186991###321122930000000000000006");
//        accessCodes.add("M00306781###321122930000000000000069");
//        accessCodes.add("M10131168###321122930000000000000006");
//        accessCodes.add("M08866210###321122930000000000000006");
//        accessCodes.add("M52267969###321122930000000000000008");
//        accessCodes.add("MSTPBJ1003309058###321122930000000000000006");
//        accessCodes.add("M40053222###321122930000000000000008");
//        accessCodes.add("M00269548###321122930000000000000073");
//        accessCodes.add("M56542968###321122930000000000000008");
//        accessCodes.add("M58467964###321122930000000000000008");
//        accessCodes.add("M40053076###321122930000000000000008");
//        accessCodes.add("MSTPJS1003100429###321122930000000000000006");
//        accessCodes.add("M43592988###321122930000000000000008");
//        accessCodes.add("M43602977###321122930000000000000008");
//        accessCodes.add("MSTPJS1001861064###321122930000000000000006");
//        accessCodes.add("MSTPJS1001993574###321122930000000000000006");
//        accessCodes.add("M43593003###321122930000000000000008");
//        accessCodes.add("M07008686###321122930000000000000006");
//        accessCodes.add("M00158241###321122930000000000000037");
//        accessCodes.add("M00223816###321122930000000000000079");
//        accessCodes.add("M40057973###321122930000000000000008");
//        accessCodes.add("M00190595###321122930000000000000013");
//        accessCodes.add("M40121458###321122930000000000000021");
//        accessCodes.add("MSTPJS1002900051###321122930000000000000006");
//        accessCodes.add("MSTPJS1001605755###321122930000000000000021");
//        accessCodes.add("M10176189###321122930000000000000006");
//        accessCodes.add("M00145301###321122930000000000000037");
//        accessCodes.add("M07257678###321122930000000000000006");
//        accessCodes.add("M00231360###321122930000000000000024");
//        accessCodes.add("MSTPJS1002436055###321122930000000000000017");
//        accessCodes.add("M40052202###321122930000000000000008");
//        accessCodes.add("M57157971###321122930000000000000008");
//        accessCodes.add("M00218904###321122930000000000000056");
//        accessCodes.add("M48477701###321122930000000000000021");
//        accessCodes.add("M40142673###321122930000000000000063");
//        accessCodes.add("M40051316###321122930000000000000008");
//        accessCodes.add("MSTPJS1002290061###321122930000000000000006");
//        accessCodes.add("M52047970###321122930000000000000008");
//        accessCodes.add("M00314967###321122930000000000000046");
//        accessCodes.add("MSTPJS1001984730###321122930000000000000073");
//        accessCodes.add("HLWZX523202409117658###321122930000000000000079");
//        accessCodes.add("12264577###321122930000000000000013");
//        accessCodes.add("14448181###321122930000000000000013");
//        accessCodes.add("M23546389###321122930000000000000006");
//        accessCodes.add("M00225025###321122930000000000000069");
//        accessCodes.add("M00302041###321122930000000000000046");
//        accessCodes.add("M40917959###321122930000000000000008");
//        accessCodes.add("MSTPJS1003053114###321122930000000000000056");
//        accessCodes.add("M22316438###321122930000000000000006");
//        accessCodes.add("M30346890###321122930000000000000006");
//        accessCodes.add("MSTPJS1002786590###321122930000000000000006");
//        accessCodes.add("M40051871###321122930000000000000008");
//        accessCodes.add("M00299164###321122930000000000000079");
//        accessCodes.add("M08851225###321122930000000000000006");
//        accessCodes.add("Z12243###321122930000000000000017");
//        accessCodes.add("M00216977###321122930000000000000069");
//        accessCodes.add("M40052985###321122930000000000000008");
//        accessCodes.add("M60057964###321122930000000000000008");
//        accessCodes.add("MSTPJS1002083033###321122930000000000000006");
//        accessCodes.add("Z12515###321122930000000000000017");
//        accessCodes.add("M08846207###321122930000000000000006");
//        accessCodes.add("M08851180###321122930000000000000006");
//        accessCodes.add("M00308661###321122930000000000000069");
//        accessCodes.add("M08861177###321122930000000000000006");
//        accessCodes.add("M00183332###321122930000000000000013");
//        accessCodes.add("HLWZX512202403264206###321122930000000000000008");
//        accessCodes.add("M00107141###321122930000000000000079");
//        accessCodes.add("M64503164###321122930000000000000008");
//        accessCodes.add("M40052322###321122930000000000000008");
//        accessCodes.add("M42917959###321122930000000000000008");
//        accessCodes.add("M40042244###321122930000000000000008");
//        accessCodes.add("M40048636###321122930000000000000008");
//        accessCodes.add("M40045293###321122930000000000000008");
//        accessCodes.add("MSTPJS1001810537###321122930000000000000024");
//        accessCodes.add("M53602963###321122930000000000000008");
//        accessCodes.add("M40051513###321122930000000000000008");
//        accessCodes.add("M40053858###321122930000000000000008");
//        accessCodes.add("M56757966###321122930000000000000008");
//        accessCodes.add("M14191171###321122930000000000000006");
//        accessCodes.add("M52942963###321122930000000000000008");
//        accessCodes.add("HLWZX510202302142304###321122930000000000000013");
//        accessCodes.add("MSTPJS1003113473###321122930000000000000079");
//        accessCodes.add("M48507703###321122930000000000000021");
//        accessCodes.add("M21651384###321122930000000000000006");
//        accessCodes.add("M14606246###321122930000000000000006");
//        accessCodes.add("M43607959###321122930000000000000008");
//        accessCodes.add("M23186386###321122930000000000000006");
//        accessCodes.add("M40048479###321122930000000000000008");
//        accessCodes.add("M00145221###321122930000000000000037");
//        accessCodes.add("M00113219###321122930000000000000024");
//        accessCodes.add("M08861171###321122930000000000000006");
//        accessCodes.add("M43612959###321122930000000000000008");
//        accessCodes.add("M40060356###321122930000000000000008");
//        accessCodes.add("M00216108###321122930000000000000013");
//        accessCodes.add("M64528165###321122930000000000000008");
//        accessCodes.add("M40053637###321122930000000000000008");
//        accessCodes.add("MSTPJT1002388617###321122930000000000000006");
//        accessCodes.add("M52652966###321122930000000000000008");
//        accessCodes.add("MSTPJS1003206446###321122930000000000000063");
//        accessCodes.add("M40052556###321122930000000000000008");
//        accessCodes.add("M61702967###321122930000000000000008");
//        accessCodes.add("M31821899###321122930000000000000006");
//        accessCodes.add("MSTPJS1002007528###321122930000000000000006");
//        accessCodes.add("M16856354###321122930000000000000006");
//        accessCodes.add("MSTPJS1002940677###321122930000000000000056");
//        accessCodes.add("M24806381###321122930000000000000006");
//        accessCodes.add("MSTPGZ1001936440###321122930000000000000006");
//        accessCodes.add("MSTPJS1002127171###321122930000000000000006");
//        accessCodes.add("M08861174###321122930000000000000006");
//        accessCodes.add("M00301782###321122930000000000000069");
//        accessCodes.add("M43642679###321122930000000000000021");
//        accessCodes.add("M00302104###321122930000000000000079");
//        accessCodes.add("M23451378###321122930000000000000006");
//        accessCodes.add("MSTPJS1002378778###321122930000000000000046");
//        accessCodes.add("M00281928###321122930000000000000073");
//        accessCodes.add("15959728###321122930000000000000013");
//        accessCodes.add("M00308841###321122930000000000000046");
//        accessCodes.add("M00145501###321122930000000000000037");
//        accessCodes.add("M40048274###321122930000000000000008");
//        accessCodes.add("M00273299###321122930000000000000069");
//        accessCodes.add("M00296167###321122930000000000000079");
//        accessCodes.add("MSTPJT1001893390###321122930000000000000006");
//        accessCodes.add("M61402965###321122930000000000000008");
//        accessCodes.add("MSTPJS1003334070###321122930000000000000056");
//        accessCodes.add("M53282964###321122930000000000000008");
//        accessCodes.add("M22736391###321122930000000000000006");
//        accessCodes.add("M18531378###321122930000000000000006");
//        accessCodes.add("M00221197###321122930000000000000079");
//        accessCodes.add("118705171###321122930000000000000006");
//        accessCodes.add("M16236378###321122930000000000000006");
//        accessCodes.add("M00231412###321122930000000000000024");
//        accessCodes.add("南京连云港ETN0328NP###321122930000000000000063");
//        accessCodes.add("M51002703###321122930000000000000021");
//        accessCodes.add("M40062023###321122930000000000000008");
//        accessCodes.add("M47332975###321122930000000000000008");
//        accessCodes.add("M00270718###321122930000000000000079");
//        accessCodes.add("M46157719###321122930000000000000021");
//        accessCodes.add("X00016393WX001###321122930000000000000013");
//        accessCodes.add("M15301294###321122930000000000000006");
//        accessCodes.add("M48002956###321122930000000000000008");
//        accessCodes.add("M40262685###321122930000000000000021");
//        accessCodes.add("M07272766###321122930000000000000006");
//        accessCodes.add("M40054345###321122930000000000000008");
//        accessCodes.add("M40057979###321122930000000000000008");
//        accessCodes.add("M40172998###321122930000000000000008");
//        accessCodes.add("北京苏州ZQOTN00032###321122930000000000000008");
//        accessCodes.add("M40059865###321122930000000000000008");
//        accessCodes.add("M40048766###321122930000000000000008");
//        accessCodes.add("M48478476###321122930000000000000063");
//        accessCodes.add("M19206379###321122930000000000000006");
//        accessCodes.add("154317905###321122930000000000000017");
//        accessCodes.add("M48437700###321122930000000000000021");
//        accessCodes.add("M00231404###321122930000000000000024");
//        accessCodes.add("MSTPJS1003254184###321122930000000000000021");
//        accessCodes.add("MSTPJS1002783669###321122930000000000000056");
//        accessCodes.add("11643627###321122930000000000000079");
//        accessCodes.add("MSTPJS1002629222###321122930000000000000006");
//        accessCodes.add("M00289098###321122930000000000000069");
//        accessCodes.add("M60012964###321122930000000000000008");
//        accessCodes.add("M00303667###321122930000000000000079");
//        accessCodes.add("MSTPJS1002589289###321122930000000000000069");
//        accessCodes.add("M00183258###321122930000000000000013");
//        accessCodes.add("HLWZX510202303151001###321122930000000000000013");
//        accessCodes.add("14800894###321122930000000000000008");
//        accessCodes.add("M00231423###321122930000000000000024");
//        accessCodes.add("MSTPJS1003022759###321122930000000000000069");
//        accessCodes.add("M00183318###321122930000000000000013");
//        accessCodes.add("M00276063###321122930000000000000079");
//        accessCodes.add("12265452###321122930000000000000013");
//        accessCodes.add("M40052255###321122930000000000000008");
//        accessCodes.add("M45402959###321122930000000000000008");
//        accessCodes.add("M17131474###321122930000000000000006");
//        accessCodes.add("MSTPJS1003205089###321122930000000000000006");
//        accessCodes.add("MSTPJS1002606351###321122930000000000000024");
//        accessCodes.add("MSTPJS1003173435###321122930000000000000006");
//        accessCodes.add("M00291798###321122930000000000000069");
//        accessCodes.add("M59222969###321122930000000000000008");
//        accessCodes.add("M07831179###321122930000000000000006");
//        accessCodes.add("M00002865###321122930000000000000037");
//        accessCodes.add("M40055281###321122930000000000000008");
//        accessCodes.add("M40001997###321122930000000000000021");
//        accessCodes.add("M00350283###321122930000000000000024");
//        accessCodes.add("M00296166###321122930000000000000079");
//        accessCodes.add("MSTPJS1003029289###321122930000000000000006");
//        accessCodes.add("M00299343###321122930000000000000069");
//        accessCodes.add("X00012562NJ568###321122930000000000000006");
//        accessCodes.add("M40052162###321122930000000000000008");
//        accessCodes.add("M48542709###321122930000000000000021");
//        accessCodes.add("MSTPJS1002648203###321122930000000000000069");
//        accessCodes.add("MSTPJS1002059107###321122930000000000000006");
//        accessCodes.add("M10256170###321122930000000000000006");
//        accessCodes.add("MSTPJT1001984770###321122930000000000000013");
//        accessCodes.add("MSTPJT1001796061###321122930000000000000013");
//        accessCodes.add("M45962956###321122930000000000000008");
//        accessCodes.add("M40577962###321122930000000000000008");
//        accessCodes.add("M15876285###321122930000000000000006");
//        accessCodes.add("M00354243###321122930000000000000024");
//        accessCodes.add("M13021165###321122930000000000000006");
//        accessCodes.add("M22316398###321122930000000000000006");
//        accessCodes.add("MSTPJS1002318030###321122930000000000000006");
//        accessCodes.add("MSTPJS1003187202###321122930000000000000017");
//        accessCodes.add("M41321919###321122930000000000000017");
//        accessCodes.add("MSTPJS1002940677###321122930000000000000006");
//        accessCodes.add("M45007956###321122930000000000000008");
//        accessCodes.add("114898944###321122930000000000000006");
//        accessCodes.add("M00222492###321122930000000000000079");
//        accessCodes.add("M18866396###321122930000000000000006");
//        accessCodes.add("MSTPJS1002797946###321122930000000000000056");
//        accessCodes.add("M40053339###321122930000000000000008");
//        accessCodes.add("M33686887###321122930000000000000006");
//        accessCodes.add("M00296547###321122930000000000000046");
        accessCodes.add("M00267008###321122930000000000000046");
        accessCodes.add("M23516435###321122930000000000000006");
        Map<String,String> headers = new HashMap<>();
        for (int i = 0; i < accessCodes.size(); i++) {
            String accessCode = accessCodes.get(i);
            JSONObject param = new JSONObject();
            param.put("access_code",accessCode.split("###")[0] );
            String areaId = accessCode.split("###")[1];
            String areaCode = ShardingEnum.getShardingEnumByO3(areaId).getBcShardingCode().replace("ds_bc_o3_","");
            param.put("areaCode",areaCode );

            try {
                String res = HttpRequestsUtils.sendPost("http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/ipranView", headers,param);
                System.out.println(i+"================");
                JSONObject alarms = JSONObject.parseObject(res);
//                JSONArray nodes = alarms.getJSONArray("nodes");
//                if (nodes == null) continue;
//                if (CollectionUtils.isEmpty(nodes)) {
//                    continue;
//                }
//                for (int i1 = 0; i1 < nodes.size(); i1++) {
//                    if ("1020200001".equalsIgnoreCase(nodes.getJSONObject(i1).getString("spec_id"))) {
//                        continue;
//                    }
//                    if ("1020200002".equalsIgnoreCase(nodes.getJSONObject(i1).getString("spec_id"))) {
//                        continue;
//                    }
//                    if ("1020200003".equalsIgnoreCase(nodes.getJSONObject(i1).getString("spec_id"))) {
//                        continue;
//                    }
//                    String name = nodes.getJSONObject(i1).getString("name");
//                    String code = nodes.getJSONObject(i1).getString("code");
//                    if ("省外设备".equalsIgnoreCase(name)) {
//                        continue;
//                    }
//                    if ("省外设备".equalsIgnoreCase(code)) {
//                        continue;
//                    }
//                    System.out.println(name);
//
//                    JSONObject param2 = new JSONObject();
//
//                    param2.put("name", name);
//                    param2.put("code", code);
//                    param2.put("accessCode", accessCode.split("###")[0]);
////                    this.circuitAnalysisDao.saveNetFault(param2, NRMConstants.SHARDING_GRAPH_DB);
//
//
//                }
                //  初始化缓存
                this.ipranService.clearAccessCodeCache(accessCode.split("###")[0]);
                this.ipranService.saveCache(alarms,accessCode.split("###")[0], areaCode,null );


            } catch (IOException e) {
                e.printStackTrace();
            }
        }



        return "123";
    }
}
