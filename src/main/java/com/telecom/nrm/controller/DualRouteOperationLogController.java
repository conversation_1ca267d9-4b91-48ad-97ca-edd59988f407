package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.service.DualRouteOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 双路由管理操作日志控制器
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@RestController
@RequestMapping("/api/dual-route-log")
public class DualRouteOperationLogController {

    private static final Logger log = LoggerFactory.getLogger(DualRouteOperationLogController.class);

    @Autowired
    private DualRouteOperationLogService dualRouteOperationLogService;

    /**
     * 记录操作日志
     */
    @PostMapping("/record")
    public ResponseEntity<Boolean> recordOperationLog(
            @RequestParam String operationType,
            @RequestParam String objectType,
            @RequestParam(required = false) Long objectId,
            @RequestParam(required = false) String objectCode,
            @RequestParam(required = false) String objectName,
            @RequestParam String operationDescription,
            @RequestParam String areaCode,
            @RequestParam String operatorId,
            @RequestParam String operatorName,
            HttpServletRequest request) {

        log.info("🎯 接收到业务操作日志记录请求:");
        log.info("   操作类型: {}", operationType);
        log.info("   对象类型: {}", objectType);
        log.info("   对象ID: {}", objectId);
        log.info("   对象编码: {}", objectCode);
        log.info("   对象名称: {}", objectName);
        log.info("   操作描述: {}", operationDescription);
        log.info("   地市编码: {}", areaCode);
        log.info("   操作人: {} ({})", operatorName, operatorId);

        try {
            log.info("🚀 开始调用日志记录服务...");

            // 使用完整版本的日志记录方法，包含 objectCode 和 objectName
            boolean result = dualRouteOperationLogService.recordOperationLogFull(
                operationType, objectType, objectId, objectCode, objectName,
                operationDescription, null, null, areaCode,
                operatorId, operatorName, request
            );

            if (result) {
                log.info("✅ 业务操作日志记录成功: operationType={}, objectId={}, objectCode={}, objectName={}",
                        operationType, objectId, objectCode, objectName);
            } else {
                log.warn("⚠️ 业务操作日志记录失败: operationType={}, objectId={}, objectCode={}, objectName={}",
                        operationType, objectId, objectCode, objectName);
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ 业务操作日志记录异常: operationType={}, objectId={}, objectCode={}, objectName={}",
                     operationType, objectId, objectCode, objectName, e);
            return ResponseEntity.ok(false);
        }
    }

    /**
     * 分页查询操作日志
     */
    @PostMapping("/query")
    public ResponseEntity<PageResponse<JSONObject>> queryOperationLog(
            @RequestBody JSONObject queryParams) {

        try {
            // 从请求体中获取分页参数
            Integer pageSize = queryParams.getInteger("pageSize");
            if (pageSize == null) pageSize = 10;

            Integer currentPage = queryParams.getInteger("currentPage");
            if (currentPage == null) currentPage = 1;

            String shardingCode = queryParams.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                log.error("❌ shardingCode参数不能为空");
                return ResponseEntity.badRequest().build();
            }

            log.info("🔍 [查询操作日志] 接收到查询请求:");
            log.info("   分页参数: pageSize={}, currentPage={}", pageSize, currentPage);
            log.info("   分库编码: {}", shardingCode);
            log.info("   查询条件: {}", queryParams.toJSONString());

            PageResponse<JSONObject> result = dualRouteOperationLogService.queryOperationLog(
                queryParams, pageSize, currentPage, shardingCode
            );

            log.info("✅ [查询操作日志] 查询完成，返回数据条数: {}",
                    result.getData() != null ? result.getData().size() : 0);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("❌ [查询操作日志] 查询失败", e);
            return ResponseEntity.ok(new PageResponse<>());
        }
    }

    /**
     * 查询操作日志详情
     */
    @PostMapping("/detail")
    public ResponseEntity<JSONObject> queryOperationLogDetail(
            @RequestBody JSONObject requestParams) {

        try {
            // 从请求体中获取参数
            Long logId = requestParams.getLong("logId");
            String shardingCode = requestParams.getString("shardingCode");

            log.info("🔍 [查询操作日志详情] 接收到详情查询请求:");
            log.info("   日志ID: {}", logId);
            log.info("   分库编码: {}", shardingCode);
            log.info("   请求参数: {}", requestParams.toJSONString());

            if (logId == null) {
                log.error("❌ logId参数不能为空");
                return ResponseEntity.badRequest().build();
            }

            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                log.error("❌ shardingCode参数不能为空");
                return ResponseEntity.badRequest().build();
            }

            JSONObject result = dualRouteOperationLogService.queryOperationLogDetail(logId, shardingCode);

            System.out.println("result   "+result);
            log.info("✅ [查询操作日志详情] 查询完成:");
            log.info("   返回结果: {}", result != null ? "有数据" : "无数据");
            log.info("   结果大小: {}", result != null ? result.size() : 0);

            if (result != null && !result.isEmpty()) {
                log.info("   记录ID: {}", result.get("id"));
                log.info("   操作类型: {}", result.get("operation_type"));
                log.info("   操作时间: {}", result.get("operation_time"));
                log.info("   操作人: {}", result.get("operator_name"));
                log.info("   操作描述: {}", result.get("operation_description"));

                // 返回标准的成功响应格式
                JSONObject response = new JSONObject();
                response.put("success", true);
                response.put("message", "查询日志详情成功");
                response.put("data", result);

                return ResponseEntity.ok(response);
            } else {
                log.warn("⚠️ [查询操作日志详情] 未找到对应的日志记录");

                // 返回标准的失败响应格式
                JSONObject response = new JSONObject();
                response.put("success", false);
                response.put("message", "未找到对应的日志记录");
                response.put("data", null);

                return ResponseEntity.ok(response);
            }
        } catch (Exception e) {
            log.error("❌ [查询操作日志详情] 查询失败", e);
            return ResponseEntity.ok(new JSONObject());
        }
    }

    /**
     * 查询操作日志统计信息
     */
    @PostMapping("/statistics")
    public ResponseEntity<List<JSONObject>> queryOperationLogStatistics(
            @RequestBody JSONObject queryParams) {

        try {
            String shardingCode = queryParams.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                log.error("❌ shardingCode参数不能为空");
                return ResponseEntity.badRequest().build();
            }

            log.info("📊 [查询操作日志统计] 接收到统计查询请求:");
            log.info("   分库编码: {}", shardingCode);
            log.info("   查询条件: {}", queryParams.toJSONString());

            List<JSONObject> result = dualRouteOperationLogService.queryOperationLogStatistics(queryParams, shardingCode);

            log.info("✅ [查询操作日志统计] 统计查询完成，返回数据条数: {}",
                    result != null ? result.size() : 0);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("❌ [查询操作日志统计] 统计查询失败", e);
            return ResponseEntity.ok(new ArrayList<>());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("OK");
    }

    /**
     * 测试业务日志记录接口
     */
    @PostMapping("/test-business-log")
    public ResponseEntity<String> testBusinessLog(HttpServletRequest request) {
        log.info("🧪 收到测试业务日志记录请求");
        
        try {
            log.info("🧪 开始测试业务日志记录...");
            boolean result = dualRouteOperationLogService.recordOperationLog(
                "test", "opt_group", 999L,
                "测试业务日志记录功能", "wx",
                "test_user", "测试用户", request
            );
            
            if (result) {
                log.info("🧪 ✅ 测试业务日志记录成功");
                return ResponseEntity.ok("测试业务日志记录成功，请查看控制台输出");
            } else {
                log.warn("🧪 ⚠️ 测试业务日志记录失败");
                return ResponseEntity.ok("测试业务日志记录失败，请查看控制台输出");
            }
            
        } catch (Exception e) {
            log.error("🧪 ❌ 测试业务日志记录异常", e);
            return ResponseEntity.ok("测试业务日志记录异常: " + e.getMessage());
        }
    }
}
