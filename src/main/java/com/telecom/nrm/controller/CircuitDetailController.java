package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.CircuitDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/circuitDetail")
@Slf4j

public class CircuitDetailController {

    @Autowired
    CircuitDetailService circuitDetailService;

    @GetMapping("")
    public BiyiPageResult<JSONObject> getPage(@RequestParam(required = false) Map example) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
/*        jsonObject.put("pageSize", jsonObject.getString("size"));
        jsonObject.put("currentPage", jsonObject.getString("page"));*/
        jsonObject.put("linkId", jsonObject.getString("link_id"));
        List<JSONObject> data = new ArrayList<JSONObject>();
        long totalCount = 0L;

        // int i=1/0;
        // int i=1/0;
        PageResponse<JSONObject> pageResponse = circuitDetailService.circuitDetailQuery(jsonObject,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if (pageResponse != null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data, totalCount, totalCount);
    }
}
