package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/customer-topology")
@Slf4j
public class CustomerTopologyController {

    @Autowired
    MstpService mstpService;

    @Autowired
    OTNService otnService;

    @Autowired
    ChangTuService changTuService;

    @Autowired
    OptRoadService optRoadService;

    @Autowired
    CircuitPairService circuitPairService;

    @Autowired
    IpranService ipranService;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;


    @PostMapping("/mstp")
    public ResponseEntity<JSONObject> mstp(@RequestBody JSONObject request) {
        JSONObject result = mstpService.mstp(request);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/ipranView")
    @LogAnnotation( interfaceName="业务电路拓扑")
    public ResponseEntity<JSONObject> ipran(@RequestBody JSONObject request) throws IOException {
//        JSONObject request = new JSONObject();
//        request.put("areaCode","yz");
//        request.put("all","Y");
//        request.put("access_code", "MSTPJS1002791227");
//        request.put("code","扬州扬州ETN6169NPH");
//        request.put("zAreaCode","wx");
//        request.put("code","南京无锡ETN0352NP");
        JSONObject result = ipranService.ipran(request);
        String zAreaCode = request.getString("zAreaCode");
        String areaCode = request.getString("areaCode");
//        if (zAreaCode != null && !zAreaCode.equals(areaCode)) {
//            // 双端展示
//            JSONObject param = new JSONObject();
//            param.put("circuit_code", request.getString("code"));
//            if (StringUtils.isEmpty(param.getString("circuit_code"))) {
//                return ResponseEntity.ok(result);
//            }
//            PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuitsOrder(param,10,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(zAreaCode));
//            if (pageResponse == null || pageResponse.getData() == null || pageResponse.getData().size() == 0) {
//                return ResponseEntity.ok(result);
//            }
//            String access_code = pageResponse.getData().get(0).getString("access_code");
//            request.put("areaCode", zAreaCode);
//            request.put("access_code", access_code);
//            JSONObject zResult = ipranService.ipran(request);
//            if (zResult != null ) {
//                JSONObject res = ipranService.unionPointPoint(result, zResult);
//                if (res != null ){
//                    return  ResponseEntity.ok(res); // 合并点到点的专线路由
//                }
//
//            }
//        }
        return ResponseEntity.ok(result);
    }

    @GetMapping("/ipranTest")
    public ResponseEntity<JSONObject> ipran() throws IOException {
        JSONObject request = new JSONObject();
//        request.put("areaCode","yz");
        request.put("all","Y");
//        request.put("access_code", "MSTPJS1002791227");
//        request.put("code","扬州扬州ETN6169NPH");
        request.put("areaCode","sz");
        request.put("access_code", "MSTPJS1001980161");
        request.put("code", "武汉苏州ETN6001NPH");
        JSONObject result = ipranService.ipran(request);
        return ResponseEntity.ok(result);
    }


    @PostMapping("/otn")
    public ResponseEntity<JSONObject> otn(@RequestBody JSONObject request) {
        JSONObject result = otnService.queryOTNRoute(request);
        // log.info(result.toJSONString());
        return ResponseEntity.ok(result);
    }

    @GetMapping("/test-otn")
    public ResponseEntity<JSONObject> testotn() {
        JSONObject request = new JSONObject();
//
        request.put("areaCode","sz");
//        request.put("access_code", "MSTPJS1001980161");
        request.put("code", "武汉苏州ETN6001NPH");
        JSONObject result = otnService.queryOTNRoute(request);
        // log.info(result.toJSONString());
        return ResponseEntity.ok(result);
    }

    @PostMapping("/changtu")
    public ResponseEntity<JSONObject> changtu(@RequestBody JSONObject request) {
        JSONObject result = changTuService.queryChangTuRoute(request);
        log.info(result.toString());
        return ResponseEntity.ok(result);
    }

    @PostMapping("/opt_road")
    public ResponseEntity<JSONObject> optRoad(@RequestBody JSONObject request) {
        JSONObject result = optRoadService.queryOPTRoute(request);
        log.info(result.toString());
        return ResponseEntity.ok(result);
    }

    @PostMapping("/opt_road_gis")
    public ResponseEntity<JSONObject> optRoadGis(@RequestBody JSONObject request) {
        JSONObject result = optRoadService.optRoadGis(request);
        log.info(result.toString());
        return ResponseEntity.ok(result);
    }

    @PostMapping("/otn-download")
    public void download(@RequestBody JSONObject jsonObject, HttpServletResponse response) throws IOException {
        JSONObject result = otnService.queryOTNRoute(jsonObject);
        List<JSONObject> leafLinks = new ArrayList<>();
        List<JSONObject> rootLinks = JSONObjectUtil.jsonObjectArrayToList(result.getJSONArray("links"));
        List<JSONObject> devices = JSONObjectUtil.jsonObjectArrayToList(result.getJSONArray("nodes"));

        computeLeafLinks(rootLinks,leafLinks);
        computeLinkAttributes(leafLinks,devices);

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("物理链路");
        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("id");

        header.createCell(1).setCellValue("A设备");
        header.createCell(2).setCellValue("Z设备");
        header.createCell(3).setCellValue("A端口");
        header.createCell(4).setCellValue("Z端口");

        header.createCell(5).setCellValue("名称");
        header.createCell(6).setCellValue("编码");
        header.createCell(7).setCellValue("通道类型");

        int rowIndex = 1;
        for (JSONObject l: leafLinks) {
            XSSFRow row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(l.getString("id"));

            row.createCell(1).setCellValue(l.getString("a_name"));
            row.createCell(2).setCellValue(l.getString("z_name"));
            row.createCell(3).setCellValue(l.getString("a_port_code"));
            row.createCell(4).setCellValue(l.getString("z_port_code"));

            row.createCell(5).setCellValue(l.getString("name"));
            row.createCell(6).setCellValue(l.getString("code"));
            row.createCell(7).setCellValue(l.getString("spec_name"));
        }

        // 设置响应头信息
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=list.xlsx");

        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();



    }

    void computeLeafLinks (List<JSONObject> links, List<JSONObject> leafLinks) {
        for (JSONObject l: links) {
            List<JSONObject> children = JSONObjectUtil.jsonObjectArrayToList(l.getJSONArray("children"));
            if (ObjectUtil.isEmpty(children)) {
                leafLinks.add(l);
            }else {
                computeLeafLinks(children, leafLinks);
            }
        }
    }

    void computeLinkAttributes (List<JSONObject> links,List<JSONObject> devices) {
        Map<String,JSONObject> idMapDevice = new HashMap();
        for (JSONObject d: devices) idMapDevice.put(d.getString("id"), d);
        for (JSONObject l: links) {

            String aId = l.getString("a_physic_device_id");
            String zId = l.getString("z_physic_device_id");

            JSONObject aDevice = idMapDevice.get(aId);
            JSONObject zDevice = idMapDevice.get(zId);
            l.put("a_name", aDevice.getString("name"));
            l.put("z_name", zDevice.getString("name"));
        }
    }

    @PostMapping("/mstp-to-pipe-segment")
    public ResponseEntity<JSONObject> queryMstp2PipeSegment(@RequestBody JSONObject request) {
        JSONObject result = mstpService.queryMSTPToPipeSegment(request);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/test-mstp-to-pipe-segment")
    public ResponseEntity<JSONObject> queryTestMstp2PipeSegment() {
        JSONObject request = new JSONObject();
        /**
         *  String areaCode=request.getString("areaCode");
         *         String code = request.getString("code");
         *         String all = request.getString("Y");
         *         String access_code = request.getString("access_code");
         */
//        request.put("access_code", "M23516416");
        request.put("areaCode", "yz");
        request.put("all", "Y");
        request.put("code", "南京扬州ETN0060NP");
        JSONObject result = mstpService.queryMSTPToPipeSegment(request);
        return ResponseEntity.ok(result);
    }

    /*
    @PostMapping("/mstp-circuits-to-pipe-segment")
    public ResponseEntity<JSONObject> queryMstpCircuits2PipeSegment(@RequestBody JSONObject request) {
        String areaCode = request.getString("areaCode");
        String shardingCode= "ds_bc_o3_"+areaCode;
        request.put("shardingCode", shardingCode);
        JSONObject result = mstpService.queryMSTPCircuitPairToPipeSegment(request);
        return ResponseEntity.ok(result);
    }

     */



}
