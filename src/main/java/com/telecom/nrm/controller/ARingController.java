package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.ARingDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/aring")
@Slf4j
public class ARingController {

    @Autowired
    ARingDao aRingDao;


    @Autowired
    RegionDao regionDao;

    @GetMapping("")
    public BiyiPageResult<JSONObject> getList(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        PageResponse<JSONObject> pageResponse =  aRingDao.NOC_gl_A_s_query(jsonObject ,pageable.getSize(),pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        List<JSONObject> getAllList = getAllList(jsonObject);
        log.info("pageResponse");
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        data.stream().map(item_b ->item_b.put("a_list", getAllList.stream().filter(item_a-> item_a.getString("id").equals(item_b.getString("id"))))).distinct().collect(Collectors.toList());
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    public  List<JSONObject>  getAllList(JSONObject jsonObject){
        List<JSONObject> data_all = new ArrayList<JSONObject>();
        PageResponse<JSONObject> pageResponse_all =  aRingDao.NOC_gl_A_s_all_query(jsonObject ,1000000,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse_all!=null &&pageResponse_all.getData()!=null && pageResponse_all.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse_all.getData());
            data_all.addAll(pageResponse_all.getData());
        }
        return data_all;
    }



    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary_1(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);


        return ResponseEntity.ok().body(meta);
    }
}
