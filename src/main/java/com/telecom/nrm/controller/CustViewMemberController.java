package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.*;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.domain.ServiceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/cust-view-member")
@Slf4j
public class CustViewMemberController {

    @Autowired
    RegionDao regionDao;

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    CustViewNodeDao custViewNodeDao;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;


    @Autowired
    CustViewDao custViewDao;

    void appendToNodes (List<JSONObject> nodes, String id,String nodeType) {
        for (JSONObject n: nodes) {
            if (n.getString("id").equals(id)) return;
        }
        JSONObject newNode = new JSONObject();
        newNode.put("id", id);
        newNode.put("node_type",nodeType);
        nodes.add(newNode);
    }

    JSONObject findExistRelation (List<JSONObject> relations, JSONObject r) {
        for (JSONObject r1: relations) {
            if (r1.getString("a_id").equals(r.getString("a_id")) && r1.getString("z_id").equals(r.getString("z_id")) || r1.getString("a_id").equals(r.getString("z_id")) && r1.getString("z_id").equals(r.getString("a_id"))) {
                return r1;
            }
        }
        return null;
    }


    @GetMapping("query-graph/{scene_id}")
    public JSONObject queryGraph(@PathVariable("scene_id") Long scene_id,
                                 @RequestParam(value = "service_type_id", required = false) String service_type_id,
                                 @RequestParam(value = "area_id", required = false) String area_id)

     {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("scene_id", scene_id);
        if (StringUtils.isNotEmpty(service_type_id) && StringUtils.isNotBlank(service_type_id)) {
            jsonObject.put("service_type_id", service_type_id);
        }
        if(StringUtils.isNotEmpty(area_id)){
            String area_name = ShardingEnum.getRegionNameByO3(area_id);
            jsonObject.put("area_name", area_name);
        }
        PageResponse<JSONObject> graphResponse =  custViewMemberDao.queryGraph(jsonObject ,100000,1, NRMConstants.SHARDING_GRAPH_DB);

        PageResponse<JSONObject> virtualNodeResponse =  custViewNodeDao.queryList(jsonObject, 100000, 1, NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> virtualNodes = virtualNodeResponse.getData();


        List<JSONObject> data = graphResponse.getData();
        List<JSONObject> relations = new ArrayList<>();
        List<JSONObject> nodes = new ArrayList<>();

        for (JSONObject vnode: virtualNodes) {
            appendToNodes(nodes,vnode.getString("node_name"),vnode.getString("node_type"));
        }

        for (JSONObject r: data) {

            String aId = r.getString("a_node_name");
            String zId = r.getString("z_node_name");
            r.put("a_id",aId);
            r.put("z_id",zId);
            r.put("circuit_count",r.getInteger("circuit_count"));
            JSONObject r1 = findExistRelation(relations,r);
            if (ObjectUtil.isNotEmpty(r1)) {
                r1.put("circuit_count",r1.getInteger("circuit_count")+r.getInteger("circuit_count"));
            }else{
                relations.add(r);
                appendToNodes(nodes,aId,"normal");
                appendToNodes(nodes,zId, "normal");
            }

        }
        JSONObject result = new JSONObject();
        result.put("nodes", nodes);
        result.put("relations", relations);
        return result;
    }

    @GetMapping("query-neighbor")
    public BiyiPageResult<JSONObject> queryNeighbors (@RequestParam(required = false) Map example) {
        JSONObject param = (JSONObject) JSON.toJSON(example);
        if (ObjectUtil.isNotEmpty(param.getString("node_name"))) {
            PageResponse<JSONObject> pageResponse = custViewMemberDao.queryNodeNeighbors(param, 10000000, 1, NRMConstants.SHARDING_GRAPH_DB);
            List<JSONObject> data = pageResponse.getData();

            return new BiyiPageResult(data, pageResponse.getPageInfo().getTotalCount(), param.size());
        }else{
            PageResponse<JSONObject> pageResponse = custViewMemberDao.queryLinkNeighbors(param, 10000000, 1, NRMConstants.SHARDING_GRAPH_DB);
            List<JSONObject> data = pageResponse.getData();

            return new BiyiPageResult(data, pageResponse.getPageInfo().getTotalCount(), param.size());
        }
    }


    @GetMapping("")
    public BiyiPageResult<JSONObject> pageQuery(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse =  custViewMemberDao.queryList(jsonObject ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);

        // JSONObject result = new JSONObject();
        List<JSONObject> list = pageResponse.getData();

        //result.put("list", list);

        return new BiyiPageResult(list,pageResponse.getPageInfo().getTotalCount() ,jsonObject.size());
    }





    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();


        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){

            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100699"))||Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100699"))){
                    Region2List.get(i).setName("江苏省");
                }
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<ServiceType> serviceTypeArray = custLinkInterfaceDao.getServiceTypeDict(NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        meta.put("serviceType", serviceTypeArray);
        return ResponseEntity.ok().body(meta);
    }

    @RequestMapping("/save-list")
    public void saveList(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        List<String> selectedRowKeys = JSONObjectUtil.jsonStringArrayToList( jsonObject.getJSONArray("selectedRowKeys"));
        List<JSONObject> dataSource =  JSONObjectUtil.jsonObjectArrayToList(jsonObject.getJSONArray("dataSource"));
        log.info("selectedRowKeys: {}", selectedRowKeys);
        log.info("dataSource: {}", dataSource);
        String sceneId = "";
        Set<String> keys =  selectedRowKeys.stream().collect(Collectors.toSet());
        if(ObjectUtil.isEmpty(jsonObject.getString("scene_id"))|| ObjectUtil.isNull(jsonObject.getString("scene_id"))){
            PageResponse<JSONObject> pageResponse =  custViewDao.queryCustview(jsonObject ,1,1, NRMConstants.SHARDING_GRAPH_DB);
            if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
                JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
                sceneId = pageResponse.getData().get(0).getString("id");
            }
        }else{
            sceneId = jsonObject.getString("scene_id");
        }



        List<JSONObject> data = new ArrayList<>();

        for (Map d: dataSource) {
            if (keys.contains(d.get("id").toString())) {
                data.add((JSONObject) JSONObject.toJSON(d));
            }
        }
        // List<JSONObject> data = dataSource.stream().filter(d->keys.contains(d.getString("id"))).collect(Collectors.toList());
        // List<JSONObject> data = dataSource.stream().filter((d)->keys.contains(d.getString("id"))).collect(Collectors.toList());


        for (Map m: data) {
            JSONObject d = (JSONObject) JSONObject.toJSON(m);
            d.put("service_id", d.getString("service_id"));
            d.put("id", "");
            d.put("scene_id", sceneId);
            d.put("a_node_name", d.getString("a_address_name"));
            d.put("z_node_name", d.getString("z_address_name"));
            if (ObjectUtil.isEmpty(d.get("service_type_id"))) {
                d.put("service_type_id", 2105L);
            }
            if (ObjectUtil.isEmpty(d.get("service_type_name"))) {
                d.put("service_type_name", "数字电路出租");
            }
            if (ObjectUtil.isEmpty(d.get("area_id"))) {
                d.put("area_id", new BigDecimal("321122930000000000000014"));
            }


            custViewMemberDao.save(d, NRMConstants.SHARDING_GRAPH_DB);
        }
        
    }

    @RequestMapping("/save")
    public void save(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        if (ObjectUtil.isEmpty(jsonObject.getString("id"))) {
            custViewMemberDao.save(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
        }else{
            custViewMemberDao.update(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
        }

    }

    @DeleteMapping("/{id}")
    public JSONObject deletebyid(@PathVariable String id) {
        Integer result_1 =  custViewMemberDao.delete(id ,NRMConstants.SHARDING_GRAPH_DB);
        JSONObject result = new JSONObject();
        if(result_1 ==1){
            result.put("result","sueccess");
        }else{
            result.put("result", "error");
        }
        return result;
    }

    @GetMapping("city-topo/{scene_id}")
    public JSONObject queryCityGraph(@PathVariable("scene_id") Long scene_id, HttpServletRequest request){

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("scene_id", scene_id);
        jsonObject.put("serviceTypeId", request.getParameter("serviceTypeId"));
        return custViewMemberDao.queryCityGraph(jsonObject,NRMConstants.SHARDING_GRAPH_DB);
    }

    @GetMapping("city-statistic/{scene_id}")
    public JSONObject queryCityStatistic(@PathVariable("scene_id") Long scene_id){
        return custViewMemberDao.queryCityStatistic(scene_id,NRMConstants.SHARDING_GRAPH_DB);
    }


}
