package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.nrm.service.AnalysisPortService;
import com.telecom.nrm.service.AuditService;
import com.telecom.nrm.service.IdcAuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.cxf.common.util.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@RestController
@RequestMapping("/api/idc")
@Slf4j
public class IdcAuditController {

    private static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static SimpleDateFormat formatday = new SimpleDateFormat("yyyy-MM-dd");
    private static final String API_URL = "http://openapi.telecomjs.com:80/eop/AIGLKFPT/DeepSeekV3_w8a8_AIPlatform/completions";
    private static final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private IdcAuditService idcAuditService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private AnalysisPortService analysisPortService;

    @PostMapping("/ask")
    public JSONObject ask(@RequestBody JSONObject param ) {
        String content = param.getString("content");
        JSONObject result = new JSONObject();
        String res = chat(content);
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(res) ) {
            result.put("code", "-1");
            result.put("msg", res);
            if (StringUtils.isEmpty(result.getString("msg"))) {
                result.put("msg", "服务器无响应,请稍后重试。");
            }
            return result;
        }
        res = res.replace("json","").replace("```","");
        try {
            result = JSONObject.parseObject(res);
            String tip = result.getString("结果");
            result.remove("结果");
            // 可能是闲聊
            if (!"识别完成".equalsIgnoreCase(tip)) {
                result.put("code", "-1");
                result.put("msg", tip);
                return result;
            }
            // 无法是被出
            if (isJsonEmpty(result)) {
                result.put("code", "-1");
                result.put("msg", "无法识别您的需求,请细化一些信息,信息内可包含:开始时间、截至时间、地市、设备IP、端口名称、用户名”,例如：张某在本月1号当天的稽核记录。");
                return result;
            }
            result.put("code", "0");
            setCity(result);
        } catch (Exception e) {
            result.put("code", "-1");
            result.put("msg", res);
        }
        return result;
    }

    private void setCity(JSONObject param) {
        // 补充地市,根据登录的统一认证地区确认，如果登录南京就是南京，如果是省公司则可以查询全省。实际权限由统一认证管理
        JwtUser jwtUser = SecurityContext.getJwtUser();
        String username = jwtUser.getUsername();
        if (StringUtils.isEmpty(username)) {
            return;
        }
        JSONArray city = new JSONArray();
        if (username.indexOf("南京") >= 0) {
            city.add("南京");
            param.put("地市", city);
        }
        if (username.indexOf("无锡") >= 0) {
            city.add("无锡");
            param.put("地市", city);
        }
        if (username.indexOf("常州") >= 0) {
            city.add("常州");
            param.put("地市", city);
        }
        if (username.indexOf("苏州") >= 0) {
            city.add("苏州");
            param.put("地市", city);
        }
        if (username.indexOf("镇江") >= 0) {
            city.add("镇江");
            param.put("地市", city);
        }

        if (username.indexOf("淮安") >= 0) {
            city.add("淮安");
            param.put("地市", city);
        }
        if (username.indexOf("盐城") >= 0) {
            city.add("盐城");
            param.put("地市", city);
        }

        if (username.indexOf("泰州") >= 0) {
            city.add("泰州");
            param.put("地市", city);
        }
        if (username.indexOf("宿迁") >= 0) {
            city.add("宿迁");
            param.put("地市", city);
        }

        if (username.indexOf("连云港") >= 0) {
            city.add("连云港");
            param.put("地市", city);
        }
        if (username.indexOf("徐州") >= 0) {
            city.add("徐州");
            param.put("地市", city);
        }
        if (username.indexOf("南通") >= 0) {
            city.add("南通");
            param.put("地市", city);
        }
        if (username.indexOf("扬州") >= 0) {
            city.add("扬州");
            param.put("地市", city);
        }

    }

    private String chat( String content) {

        StringBuffer res = new StringBuffer();
        JSONObject param = new JSONObject();
        JSONArray messages = new JSONArray();
        JSONObject system = new JSONObject();
        StringBuffer sb = new StringBuffer("你的角色是一名IDC设备日志稽核专家，可以为用户提供设备操作稽核信息的分析查询。\n" +
                "返回内容：返回json格式数据，key为中文,包括：开始时间、截至时间、地市、设备IP、端口名称、用户名、结果;\n" +
                "返回规则：\n" +
                "1、开始时间:根据用户输入文字解析出,日期格式为:yyyy-MM-dd HH:mm:ss。当前时间是{currentTime}。例如:3天内,开始时间是当前时间减去三天;本月1号是指本月的1号凌晨;本周是指当前时间所在周的周一凌晨;3月10日等是指3月10日的凌晨。如果无法识别给空即可。如果输入内容没有明显的时间特征,识别为空即可。\n" +
                "2、截至时间:根据用户输入文字解析出,日期格式为:yyyy-MM-dd HH:mm:ss。当前时间是{currentTime}。例如:本月1号是指本月的2号(次日)凌晨;3月10日等是指3月11（次日）日的凌晨。如果无法识别给空即可。如果输入内容没有明显的时间特征,识别为空即可。\n" +
                "3、设备IP：根据用户输入文字解析出,是设备上的管理IP,一般为ipv4格式,只返回单个IP文本内容。\n" +
                "4、端口名称：根据用户输入文字解析出,设备上的某个端口名称。格式多样。例如样例1:10GE3/0/25,样例2:10GE3-0-25,由字母、数字、分隔符组成,分析结果返回单个端口字符串内容。\n" +
                "5、用户名：根据用户输入文字解析出,是指人物名称,识别出一个人名即可。\n" +
                "6、城市：是江苏的城市，根据用户输入识别出一个或者多个。只需要城市名无需添加“市”,例如:返回南京而不是南京市。全省是指江苏省的13个城市。数据是数组格式，如果无法识别则给空数组;\n" +
                "7、结果:默认返回”识别完成“。如果分析用户是在闲聊或者问其他的技术、行业问题等，例如：你好、今天天气如何、帮我写个代码等，无需具体解决也不需要提供建议、帮助，只根据场景返回礼貌的内容和让用户确认其在日志稽核上的需求并可以给用户举个例子。");
        system.put("content",sb.toString().replace("{currentTime}",format.format(new Date())));


        system.put("role","system");
        messages.add(system);
        JSONObject user = new JSONObject();
        user.put("content",  content);
        user.put("role","user");
        messages.add(user);

        param.put("messages",messages);
        param.put("model","DeepSeek-V3-w8a8");
        param.put("stream",true);


        try (CloseableHttpClient client = HttpClients.createDefault()) {

            HttpPost request = new HttpPost(API_URL);
            request.addHeader("X-APP-ID", "2191d2c95aaa43d0991bce3d9131075f");
            request.addHeader("X-APP-KEY", "b2e19bea3b6a4da385ff3ffbe63a6a30");
            request.setEntity(new StringEntity(param.toJSONString(), "UTF-8"));

            // 执行请求
            HttpResponse response = client.execute(request);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                try (InputStream inputStream = entity.getContent();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                    String line;
                    // 逐行读取流式响应
                    while ((line = reader.readLine()) != null) {
                        if (line != null && line.startsWith("data:")) {
                            processDataChunk(line.substring(5).trim(),res);
                        }

                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return res.toString();

    }

    private static void processDataChunk(String jsonData,StringBuffer sb) {
        try {
            JsonNode node = mapper.readTree(jsonData);
            JsonNode choices = node.get("choices");
            if (choices.isArray() && choices.size() > 0) {
                JsonNode delta = choices.get(0).get("delta");
                if (delta.has("content")) {
                    System.out.print(delta.get("content").asText());
                    sb.append(delta.get("content").asText());

                }
            }
        } catch (Exception e) {

        }
    }


    @PostMapping("/idcAuditStatistics")
    public JSONObject idcAuditStatistics(@RequestBody JSONObject param) {
        JSONObject result = idcAuditService.qryIdcAuditStatistics(param);
        return result;
    }

    public static boolean isJsonEmpty(JSONObject jsonObject) {
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);

            if (value instanceof String) {
                if (!((String) value).isEmpty()) {
                    return false;
                }
            } else if (value instanceof JSONArray) {
                if (!isJsonArrayEmpty((JSONArray) value)) {
                    return false;
                }
            } else if (value instanceof JSONObject) {
                if (!isJsonEmpty((JSONObject) value)) {
                    return false;
                }
            } else if (value != null) {
                return false;
            }
        }
        return true;
    }

    public static boolean isJsonArrayEmpty(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.size(); i++) {
            Object value = jsonArray.get(i);

            if (value instanceof String) {
                if (!((String) value).isEmpty()) {
                    return false;
                }
            } else if (value instanceof JSONArray) {
                if (!isJsonArrayEmpty((JSONArray) value)) {
                    return false;
                }
            } else if (value instanceof JSONObject) {
                if (!isJsonEmpty((JSONObject) value)) {
                    return false;
                }
            } else if (value != null) {
                return false;
            }
        }
        return true;
    }

    @PostMapping("/export")
    public void exportCommon(@RequestBody @Valid String json, HttpServletResponse response) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(json);
        //查出统计的数量
        JSONObject statisticCount = auditService.queryOperationAuditCount(jsonObject);
        //查出详情信息
        List<JSONObject> auditDetails = auditService.queryOperationAuditDetail(jsonObject, false);
        File file = null;
        File excelFile = null;
        File zipfile = null;
        OutputStream outputStream = null;
        FileInputStream fis = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmsssss");
            String fileName = "IDC设备操作稽核报告" + sdf.format(new Date()) + ".pdf";
            String excelfileName = "IDC风险详单" + sdf.format(new Date()) + ".xlsx";
            file = new File(fileName);
            idcAuditService.export(jsonObject, statisticCount, auditDetails, file, excelfileName);
            excelFile = new File(excelfileName);
            idcAuditService.exportDetailExcel(auditDetails,excelFile);
            List<File>  zipFileList=new ArrayList<>();
            zipFileList.add(file);
            zipFileList.add(excelFile);
            if (CollectionUtils.isNotEmpty(zipFileList)){
                String outputMergedZip = "IDC设备操作稽核报告" + sdf.format(new Date())+".zip";
                zipfile= mergeZips(zipFileList,outputMergedZip);
            }
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(URLEncoder.encode(zipfile.getName()).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            outputStream = response.getOutputStream();
            fis = new FileInputStream(zipfile);
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = fis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
        }catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if(fis != null) {
                fis.close();
            }
            if (file != null) {
                file.delete();
            }
            if (excelFile != null) {
                excelFile.delete();
            }
            if (zipfile != null) {
                zipfile.delete();
            }
        }

    }

    public File mergeZips(List<File>  inputZips, String outputZip) {

        try {
            // 创建ZipOutputStream来写入ZIP文件
            try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(outputZip))) {
                for (File filePath : inputZips) {
                    // 创建FileInputStream来读取要压缩的文件
                    try (FileInputStream fis = new FileInputStream(filePath)) {
                        // 创建ZipEntry来代表ZIP文件中的每个文件
                        ZipEntry zipEntry = new ZipEntry(filePath.getPath());
                        zos.putNextEntry(zipEntry);

                        // 将文件内容写入到ZIP文件中
                        byte[] bytes = new byte[1024];
                        int length;
                        while ((length = fis.read(bytes)) >= 0) {
                            zos.write(bytes, 0, length);
                        }
                    }
                    // 关闭当前ZipEntry，为下一个文件做准备
                    zos.closeEntry();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new File(outputZip);
    }

    @PostMapping("/idcAnalysisMachineConfig")
    public String idcAnalysisMachineConfig(@RequestBody JSONObject param) throws Exception {
        JSONObject jsonObject = idcAuditService.qryIdcPortGatherLock("rateTemplate");
        if (jsonObject != null && jsonObject.getJSONArray("data") != null && jsonObject.getJSONArray("data").size() > 0) {
            return "doing";
        }
        idcAuditService.insertIdcPortGatherLock("rateTemplate");
        //异步解析IDC整机文件配置
        idcAuditService.idcAnalysisMachineConfig(param);
        //返回成功
        return "success";
    }

    @PostMapping("/idcAnalysisMachinePortInfo")
    public String idcAnalysisMachinePortInfo(@RequestBody JSONObject param) throws Exception {
        JSONObject jsonObject = idcAuditService.qryIdcPortGatherLock("port");
        if (jsonObject != null && jsonObject.getJSONArray("data") != null && jsonObject.getJSONArray("data").size() > 0) {
            return "doing";
        }
        idcAuditService.insertIdcPortGatherLock("port");
        //异步解析IDC整机文件中每个端口的限速速率和ip地址
        idcAuditService.idcAnalysisMachinePortInfo(param);
        //返回成功
        return "success";
    }

    @PostMapping("/reloadAnalysisPortInfo")
    public String reloadAnalysisPortInfo() throws Exception {
        JSONObject jsonObject = idcAuditService.qryIdcPortGatherLock("port");
        if (jsonObject != null && jsonObject.getJSONArray("data") != null && jsonObject.getJSONArray("data").size() > 0) {
            return "doing";
        }
        idcAuditService.insertIdcPortGatherLock("port");
        Map<String, List<JSONObject>> groupDeviceMap = idcAuditService.qryIdcAnalysisMachinePortInfo("port");
        if (MapUtils.isNotEmpty(groupDeviceMap)) {
            for (String num : groupDeviceMap.keySet()) {
                analysisPortService.groupAnalysisPortInfo(groupDeviceMap.get(num), num);
            }
        }
        //返回成功
        return "success";
    }

    @PostMapping("/idcAnalysisMachineBookSize")
    public String idcAnalysisMachineBookSize(HttpServletResponse response) throws Exception {
        //异步解析IDC整机文件中每个端口的限速速率和ip地址
        idcAuditService.idcAnalysisMachineBookSize();
        //返回成功
        return "success";
    }

    @PostMapping("/auditPmPortRateOverrun")
    public String auditPmPortRateOverrun() throws Exception {
        //稽核AP端口的限速速率与配置速率
        idcAuditService.auditPmPortRateOverrun();
        //返回成功
        return "success";
    }

}
