package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.nrm.dto.ExportResponseDTO;
import com.telecom.nrm.service.DocumentExportService;
import com.telecom.nrm.aop.LogAnnotation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 通用文件上传到文档安全平台Controller
 * 支持前端生成的各种格式文件（XLSX、Word等）直接上传到SFTP
 */
@RestController
@RequestMapping("/api/file-upload")
@CrossOrigin
public class FileUploadController {

    private static final Logger log = LoggerFactory.getLogger(FileUploadController.class);

    @Autowired
    private DocumentExportService documentExportService;

    /**
     * 上传前端生成的文件到文档安全平台
     * 支持各种格式：XLSX、DOCX、PDF等
     */
    @PostMapping("/upload-to-document-security")
    @LogAnnotation(interfaceName = "前端文件上传到文档安全平台")
    public ResponseEntity<ExportResponseDTO> uploadToDocumentSecurity(
            @RequestParam("file") MultipartFile file,
            @RequestParam("fileName") String fileName,
            @RequestParam("moduleName") String moduleName,
            @RequestParam("description") String description,
            @RequestParam(value = "apiEndpoint", required = false) String apiEndpoint,
            HttpServletRequest request) {
        
        long startTime = System.currentTimeMillis();
        try {
            log.info("🎯 [文件上传] 接收到前端生成文件上传请求");
            log.info("📁 [文件上传] 原始文件名: {}", file.getOriginalFilename());
            log.info("📁 [文件上传] 目标文件名: {}", fileName);
            log.info("📋 [文件上传] 模块名称: {}", moduleName);
            log.info("📝 [文件上传] 描述: {}", description);
            log.info("📊 [文件上传] 文件大小: {} bytes", file.getSize());
            log.info("📄 [文件上传] 文件类型: {}", file.getContentType());

            // 参数验证
            if (file.isEmpty()) {
                log.error("❌ [文件上传] 上传文件为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("上传文件不能为空"));
            }

            if (fileName == null || fileName.trim().isEmpty()) {
                log.error("❌ [文件上传] 文件名为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("文件名不能为空"));
            }

            if (moduleName == null || moduleName.trim().isEmpty()) {
                log.error("❌ [文件上传] 模块名称为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("模块名称不能为空"));
            }

            // 获取用户信息
            JwtUser jwtUser = SecurityContext.getJwtUser();
            log.info("👤 [文件上传] 操作用户: {}", jwtUser.getUsername());

            // 获取文件扩展名
            String fileExtension = getFileExtension(fileName);
            log.info("📄 [文件上传] 文件扩展名: {}", fileExtension);

            // 1. 直接上传文件到SFTP（不做任何处理）
            log.info("📤 [文件上传] 开始上传文件到SFTP文档安全平台");
            long uploadStartTime = System.currentTimeMillis();
            
            ExportResponseDTO result = documentExportService.uploadRawFileToDocumentSecurity(
                file.getBytes(), 
                fileName, 
                moduleName, 
                description,
                apiEndpoint != null ? apiEndpoint : "/api/file-upload/upload-to-document-security",
                "前端生成文件上传",
                fileExtension
            );
            
            long uploadEndTime = System.currentTimeMillis();
            log.info("⏱️ [文件上传] 文件上传耗时: {} ms", (uploadEndTime - uploadStartTime));

            // 2. 记录导出日志到数据库
            try {
                log.info("📝 [文件上传] 开始记录导出日志到数据库");
                recordExportLog(jwtUser, fileName, moduleName, description, file.getSize(), 
                    result.isSuccess(), result.getMessage(), apiEndpoint);
                log.info("✅ [文件上传] 导出日志记录成功");
            } catch (Exception e) {
                log.error("❌ [文件上传] 记录导出日志失败: {}", e.getMessage(), e);
                // 日志记录失败不影响文件上传结果
            }

            if (result.isSuccess()) {
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("✅ [文件上传] 文件上传成功!");
                log.info("📁 [文件上传] 上传文件名: {}", result.getFileName());
                log.info("📊 [文件上传] 文件大小: {} bytes", file.getSize());
                log.info("⏱️ [文件上传] 总耗时: {} ms", totalTime);
                
                // 设置文件大小信息
                result.setFileSize(file.getSize());
            } else {
                log.error("❌ [文件上传] 文件上传失败: {}", result.getMessage());
            }

            return ResponseEntity.ok(result);

        } catch (IOException e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("💥 [文件上传] 文件读取异常，总耗时: {} ms", totalTime);
            log.error("💥 [文件上传] 异常详情: {}", e.getMessage(), e);
            
            // 记录失败日志
            try {
                JwtUser jwtUser = SecurityContext.getJwtUser();
                recordExportLog(jwtUser, fileName, moduleName, description, file.getSize(), 
                    false, "文件读取失败: " + e.getMessage(), apiEndpoint);
            } catch (Exception logEx) {
                log.error("❌ [文件上传] 记录失败日志异常: {}", logEx.getMessage());
            }
            
            return ResponseEntity.ok(ExportResponseDTO.failure("文件读取失败: " + e.getMessage()));
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("💥 [文件上传] 文件上传异常，总耗时: {} ms", totalTime);
            log.error("💥 [文件上传] 异常详情: {}", e.getMessage(), e);
            
            // 记录失败日志
            try {
                JwtUser jwtUser = SecurityContext.getJwtUser();
                recordExportLog(jwtUser, fileName, moduleName, description, 
                    file != null ? file.getSize() : 0, false, "文件上传失败: " + e.getMessage(), apiEndpoint);
            } catch (Exception logEx) {
                log.error("❌ [文件上传] 记录失败日志异常: {}", logEx.getMessage());
            }
            
            return ResponseEntity.ok(ExportResponseDTO.failure("文件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 记录导出日志到数据库
     */
    private void recordExportLog(JwtUser jwtUser, String fileName, String moduleName, 
                                String description, long fileSize, boolean success, 
                                String message, String apiEndpoint) {
        try {
            // 构建日志记录
            JSONObject logData = new JSONObject();
            logData.put("operator_id", jwtUser.getId());
            logData.put("operator_name", jwtUser.getUsername() + "@@@" + getAreaNameFromUser(jwtUser));
            logData.put("file_name", fileName);
            logData.put("module_name", moduleName);
            logData.put("description", description);
            logData.put("file_size", fileSize);
            logData.put("success", success);
            logData.put("message", message);
            logData.put("api_endpoint", apiEndpoint);
            logData.put("export_time", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
            logData.put("export_type", "前端生成文件上传");
            
            // 调用DocumentExportService记录日志
            documentExportService.recordExportLog(logData);
            
        } catch (Exception e) {
            log.error("❌ [文件上传] 记录导出日志异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从用户信息中获取地区名称
     */
    private String getAreaNameFromUser(JwtUser jwtUser) {
        try {
            // 这里可以根据实际的用户信息结构来获取地区名称
            // 暂时返回默认值
            return "未知地区";
        } catch (Exception e) {
            log.warn("获取用户地区信息失败: {}", e.getMessage());
            return "未知地区";
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<JSONObject> health() {
        JSONObject result = new JSONObject();
        result.put("status", "ok");
        result.put("service", "FileUploadController");
        result.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(result);
    }
}
