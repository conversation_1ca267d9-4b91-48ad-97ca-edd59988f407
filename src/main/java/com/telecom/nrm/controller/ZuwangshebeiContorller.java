package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.ZuwangshebeiDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.dao.ZuwangshebeiDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.ZuwangshebeiService;
import com.telecom.nrm.service.ZuwangshebeiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/zuwangshebei-api")
@Slf4j
public class ZuwangshebeiContorller {
    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    ZuwangshebeiService zuwangshebeiService;

    @Autowired
    ZuwangshebeiDao zuwangshebeiDao;

    @Autowired
    RegionDao regionDao;

    @PostMapping("/save_opt_group")
    public Integer savezuwangshebei(@RequestBody JSONObject jsonObject) {
        jsonObject.put("area_name", jsonObject.getString("ds"));
        return zuwangshebeiDao.pm_opt_road_group_insert(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/save_opt_group_element")
    public Integer savezuwangshebeiElement(@RequestBody JSONObject jsonObject) {
        return zuwangshebeiDao.pm_opt_road_group_element_insert(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @GetMapping("")
    public BiyiPageResult<JSONObject> getInfo(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> zuwangshebeiList = new ArrayList<JSONObject>();
        jsonObject.put("object_tyoe","zuwangshebei");
        PageResponse<JSONObject> zuwangshebeiPage = zuwangshebeiDao.pm_opt_road_group_result_query_zuwang(jsonObject, pageable.getSize(), pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(zuwangshebeiPage!=null &&zuwangshebeiPage.getData()!=null && zuwangshebeiPage.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(zuwangshebeiPage.getData());
            zuwangshebeiList.addAll(zuwangshebeiPage.getData());
        }
        List<String> ids = zuwangshebeiList.stream().map(zuwangshebei -> zuwangshebei.getString("id")).collect(Collectors.toList());
        JSONObject param_opt_group_member = new JSONObject();
        param_opt_group_member.put("ids", ids);
        List<JSONObject> opt_group_members = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(ids))opt_group_members = zuwangshebeiDao.pm_opt_road_group_element_query(param_opt_group_member, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        for(JSONObject zuwangshebei: zuwangshebeiList) {
            String deal_result = zuwangshebei.getString("dealResult");
            String check_result = zuwangshebei.getString("checkResult");
            zuwangshebei.put("result", ObjectUtil.isNotEmpty(zuwangshebei.getString("dealResult")) ? deal_result : check_result);
            if(ObjectUtil.isNotEmpty(opt_group_members)) zuwangshebei.put("members", opt_group_members.stream().filter(item -> item.getString("group_id").equals(zuwangshebei.getString("id"))).collect(Collectors.toList()));

        }
        return new BiyiPageResult(zuwangshebeiList,zuwangshebeiPage.getPageInfo().getTotalCount() ,zuwangshebeiPage.getPageInfo().getPageSize());
    }

    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> get_dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);


        List<JSONObject> StateList = new ArrayList<>();
        JSONObject lifeState = new JSONObject();
        lifeState.put("stateName", "同管道");
        StateList.add(lifeState);
        JSONObject lifeState1 = new JSONObject();
        lifeState1.put("stateName", "同光缆");
        StateList.add(lifeState1);
        JSONObject lifeState3 = new JSONObject();
        lifeState3.put("stateName", "正常");
        StateList.add(lifeState3);
        JSONObject lifeState4 = new JSONObject();
        lifeState4.put("stateName", "已忽略异常");
        StateList.add(lifeState4);
        meta.put("StateList", StateList);
        meta.put("Statistic", zuwangshebeiService.optPair_statistic_dictionary(exampleJSON));
        return ResponseEntity.ok().body(meta);
    }

    @GetMapping("/opt_pair_check")
    public void check(@RequestParam(required = false) Map example , BiyiPageRequest pageable){
        BiyiPageResult<JSONObject> multi_query = getInfo(example, pageable);
        List<JSONObject> multi_query_list = multi_query.getData();
        for(JSONObject multi_opt : multi_query_list){
            JSONObject param = new JSONObject();
            param.put("ds",example.get("ds"));
            param.put("id", multi_opt.getString("id"));
            zuwangshebeiService.risk_analyze(param);
        }
    }

    @PostMapping("/note_update")
    public Integer note_update(@RequestBody JSONObject jsonObject){
        return zuwangshebeiDao.pm_opt_road_group_note_update_zuwang(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("area_name")));
    }

    @PostMapping("/confirm_result")
    public Integer confirm_result(@RequestBody JSONObject jsonObject){
        jsonObject.put("object_type", "optGroup");
        jsonObject.put("confirm_status", "已确认");
        jsonObject.put("confirm_op", jsonObject.getString("userName"));
        return zuwangshebeiDao.pm_risk_check_confirm(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/cancel_confirm_result")
    public Integer cancel_confirm_result(@RequestBody JSONObject jsonObject){
        jsonObject.put("object_type", "optGroup");
        jsonObject.put("confirm_status", "未确认");
        jsonObject.put("confirm_op", null);
        return zuwangshebeiDao.pm_risk_check_confirm(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/risk-analyze")
    public JSONObject riskAnalyze(@RequestBody JSONObject request, HttpServletResponse response){
        JSONObject result =  zuwangshebeiService.risk_analyze(request);
        return result;

    }

    /**
     * 通过F号查询数据
     * @param jsonObject f:[]
     * @return
     */
    @PostMapping("/query-by-f-number")
    public JSONObject queryByFNumber(@RequestBody JSONObject jsonObject){
        log.info("queryByFNumber:{}",jsonObject);
        return zuwangshebeiService.pm_opt_road_group_query_zuwang_by_f_number(jsonObject);
    }

    /**
     * 将两个光路纳为光路保护组
     * {
     *     fs:[{"code":"f1231231",name:""}]
     *     name:"",
     *     speciality:"",
     *     create_op:""
     * }
     * @param jsonObject
     * @return
     */
    @PostMapping("/pair-circuit")
    public Integer pair_circuit(@RequestBody JSONObject jsonObject){
        log.info("pair_circuit:{}",jsonObject);
        return zuwangshebeiService.pair_circuit(jsonObject);
    }

    /**
     * 通过F号查询双路由方案
     * {
     *     ds:"",
     *     code:"",
     *     opts:[""] 光缆集合
     *
     * }
     * @param jsonObject
     * @return
     */
    @PostMapping("/query-double-route-by-f-number")
    public JSONObject queryDoubleRouteByFNumber(@RequestBody JSONObject jsonObject){
        log.info("queryDoubleRouteByFNumber:{}",jsonObject);
        return zuwangshebeiService.queryDoubleRouteByFNumber(jsonObject);
    }

    /**
     * 通过两个设备id查询双路由方案
     * {
     *     ds:"",
     *     devices:[1,2]
     * }
     * @param jsonObject
     * @return
     */
    @PostMapping("/query-double-route-by-device-id")
    public JSONObject queryDoubleRouteByDeviceId(@RequestBody JSONObject jsonObject){
        log.info("queryDoubleRouteByDeviceId:{}",jsonObject);
        return zuwangshebeiService.queryDoubleRouteByDeviceId(jsonObject);
    }

    /**
     * 通过光缆编号查询双路由方案
     * {
     *     ds:"",
     *     cable_code:""
     * }
     * @param jsonObject
     * @return
     */
    @PostMapping("/query-double-route-by-cable-code")
    public JSONObject queryDoubleRouteByCableCode(@RequestBody JSONObject jsonObject){
        log.info("queryDoubleRouteByCableCode:{}",jsonObject);
        return zuwangshebeiService.queryDoubleRouteByCableCode(jsonObject);
    }

    @PostMapping("/white-insert")
    public Integer pair_circuit_input_fiber_result_corr_insert(@RequestBody JSONObject jsonObject){
        String type = jsonObject.getString("type");
        jsonObject.put("pipleline_code", jsonObject.getJSONObject("pipeSegment").getString("code"));
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_insert(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/white-delete")
    public Integer pipleline_white_delete(@RequestBody JSONObject jsonObject){
        String code = jsonObject.getJSONObject("pipeSegment").getString("code");
        if(code.contains("----已忽略")){
            code = code.substring(0, code.indexOf("----已忽略"));
        }
        jsonObject.put("pipleline_code", code.trim());
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_delete(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    /**
     * 逻辑图——中继数据
     */
    @GetMapping("/zj")
    public ResponseEntity<JSONObject> getOptZjData(@RequestParam(required = false) Map<String,Object> parame) {
        log.info("getOptLinkData:{}",parame);
        JSONObject jsonParame = (JSONObject) JSON.toJSON(parame);
        JSONObject meta = new JSONObject();
        //判断ds是否为空 为空默认南京
        if(ObjectUtil.isEmpty(jsonParame.getString("ds"))){
            jsonParame.put("ds","南京");
        }
        //处理codes数据
        if(ObjectUtil.isNotEmpty(jsonParame.getString("codes"))){
            String[] codes = jsonParame.getString("codes").split(",");
            jsonParame.put("codes",codes);
        }
        List<JSONObject> jsonObjectList = zuwangshebeiDao.pm_opt_road_group_query_zuwang_zj(jsonParame, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
        meta.put("zj",jsonObjectList==null?new ArrayList<>():jsonObjectList);
        return ResponseEntity.ok().body(meta);
    }

    /**
     * 重要设备ipran列表查询
     */
    @GetMapping("/important_device_ipran_list")
    public BiyiPageResult<JSONObject> queryImportantDeviceIpranList(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        log.info("queryImportantDeviceIpranList:{}",example);
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> zuwangshebeiList = new ArrayList<JSONObject>();
        PageResponse<JSONObject> zuwangshebeiPage = zuwangshebeiDao.queryImportantDeviceIpranList(jsonObject, pageable.getSize(), pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(zuwangshebeiPage!=null &&zuwangshebeiPage.getData()!=null && !zuwangshebeiPage.getData().isEmpty()){
            JSONObjectUtil.convertBigNumberToString(zuwangshebeiPage.getData());
            zuwangshebeiList.addAll(zuwangshebeiPage.getData());
        }
        List<String> ids = zuwangshebeiList.stream().map(zuwangshebei -> zuwangshebei.getString("id")).collect(Collectors.toList());
        JSONObject param_opt_group_member = new JSONObject();
        param_opt_group_member.put("ids", ids);
        List<JSONObject> opt_group_members = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(ids))opt_group_members = zuwangshebeiDao.queryImportantDeviceIpranCableDetail(param_opt_group_member, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        for(JSONObject zuwangshebei: zuwangshebeiList) {
            if(ObjectUtil.isNotEmpty(opt_group_members)) zuwangshebei.put("members", opt_group_members.stream().filter(item -> item.getString("id").equals(zuwangshebei.getString("id"))).collect(Collectors.toList()));
        }
        return new BiyiPageResult(zuwangshebeiList,zuwangshebeiPage.getPageInfo().getTotalCount() ,zuwangshebeiPage.getPageInfo().getPageSize());
    }

    @GetMapping("/important_device_ipran_list/dictionary")
    public ResponseEntity<JSONObject> get_dictionary2(@RequestParam(required = false) Map example) {
        return this.get_dictionary(example);
    }
    /**
     * 查询拓扑数据
     */
    @GetMapping("/important_device_ipran_list/topology")
    public JSONObject getTopology(@RequestParam(required = false) Map<String,Object> parame) {
        log.info("getTopology:{}",parame);
        JSONObject jsonParame = (JSONObject) JSON.toJSON(parame);
        JSONObject meta = new JSONObject();
        List<JSONObject> jsonObjectList = zuwangshebeiDao.queryImportantDeviceIpranTopo(jsonParame, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
        meta.put("topology",jsonObjectList==null?new ArrayList<>():jsonObjectList);
        return meta;
    }


}
