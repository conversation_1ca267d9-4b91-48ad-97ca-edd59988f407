package com.telecom.nrm.controller;


import com.telecom.nrm.domain.ResTreeParamDto;
import com.telecom.nrm.domain.ResponseData;
import com.telecom.nrm.service.BaseLineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



@RestController
@RequestMapping("/api/baseLine")
@Slf4j
public class BaseLineController {

    @Autowired
    BaseLineService baseLineService;

    /**
     * 数字孪生第一个场景
     * @param param
     * @return
     */
    @PostMapping("getServiceCircuitByDevice")
    public ResponseData getServiceCircuitByDevice(@RequestBody ResTreeParamDto param) {
        ResponseData responseData = new ResponseData();
        try {
            responseData = baseLineService.getBusinessCircuitByDevice(param);
        } catch (Exception e) {
            responseData.setCode(ResponseData.ERROR_999);
            responseData.setMessage(e.getMessage());
        }
        return responseData;
    }

    @PostMapping("getServiceCircuitByCable")
    public ResponseData getServiceCircuitByCable(@RequestBody ResTreeParamDto param) {
        ResponseData responseData = new ResponseData();
        try {
            responseData = baseLineService.getServiceCircuitByCable(param);
        } catch (Exception e) {
            responseData.setCode(ResponseData.ERROR_999);
            responseData.setMessage(e.getMessage());
        }
        return responseData;
    }

    @PostMapping("getServiceCircuitByCustID")
    public ResponseData getServiceCircuitByCustID(@RequestBody ResTreeParamDto param) {
        ResponseData responseData = new ResponseData();
        try {
            responseData = baseLineService.getServiceCircuitByCustID(param);
        } catch (Exception e) {
            responseData.setCode(ResponseData.ERROR_999);
            responseData.setMessage(e.getMessage());
        }
        return responseData;
    }

    @PostMapping("getServiceCircuitByCircuitCode")
    public ResponseData getServiceCircuitByCircuitCode(@RequestBody ResTreeParamDto param) {
        ResponseData responseData = new ResponseData();
        try {
            responseData = baseLineService.getServiceCircuitByCircuitCode(param);
        } catch (Exception e) {
            responseData.setCode(ResponseData.ERROR_999);
            responseData.setMessage(e.getMessage());
        }
        return responseData;
    }


    @PostMapping("getProductInfoByCustId")
    public ResponseData getProductInfoByCustId(@RequestBody ResTreeParamDto param) {
        ResponseData responseData = new ResponseData();
        try {
            responseData = baseLineService.getServiceCircuitByCustID(param);
        } catch (Exception e) {
            responseData.setCode(ResponseData.ERROR_999);
            responseData.setMessage(e.getMessage());
        }
        return responseData;
    }

    @PostMapping("getDeviceResourceListByCondition")
    public ResponseData getDeviceResourceListByCondition(@RequestBody ResTreeParamDto param) {
        ResponseData responseData = new ResponseData();
        try {
            responseData = baseLineService.getDeviceResourceListByCondition(param);
        } catch (Exception e) {
            responseData.setCode(ResponseData.ERROR_999);
            responseData.setMessage(e.getMessage());
        }
        return responseData;
    }

    @PostMapping("getCirCountByCustId")
    public ResponseData getCirCountByCustId(@RequestBody ResTreeParamDto param) {
        ResponseData responseData = new ResponseData();
        try {
            responseData = baseLineService.getCirCountByCustId(param);
        } catch (Exception e) {
            responseData.setCode(ResponseData.ERROR_999);
            responseData.setMessage(e.getMessage());
        }
        return responseData;
    }


}
