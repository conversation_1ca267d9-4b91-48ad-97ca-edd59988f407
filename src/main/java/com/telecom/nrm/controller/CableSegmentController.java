package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/cable-2-pipe-segment")
@Slf4j
public class CableSegmentController {
    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    /**
     * 双路由规则对比
     * @param jsonObject
     * @return
     */
    @PostMapping("/ruleCheck")
    public ResponseEntity<JSONObject> ruleCheck(@RequestBody JSONObject jsonObject) {
        String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"));
        GraphRequestBuilder builder = new GraphRequestBuilder();

        List<String> nodes = (List<String>) jsonObject.getJSONArray("nodes").stream().map(Object::toString).collect(Collectors.toList());
        //shuangluyou
        GraphRequest graphRequest = builder.setApiId("query_cable_pipe").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cable","codes",
                        nodes)).build();

        Graph graph = graphApiService.doApi(graphRequest);

        JSONObject data = graphService.getData(graph);

        graphService.buildTree(data,"光缆","管道段","pipe_segments");

        List<JSONObject> cableList = graphService.filterNodeByLabel(data,"光缆");

        JSONObject result = new JSONObject();
        result.put("data", cableList);

        return ResponseEntity.ok(result);
    }
}
