package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.CircuitAnalysisService;
import com.telecom.nrm.service.IPRanJsService;
import com.telecom.nrm.thread.audit.InternetSpecialtyLineService;
import com.telecom.nrm.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/api/circuitAnalysis")
@Slf4j
public class CircuitAnalysisController {

    @Autowired
    InternetSpecialtyLineService internetSpecialtyLine;
    @Autowired
    private CircuitAnalysisService circuitAnalysisService;

    @Autowired
    private IPRanJsService ipRanJsService;

    @Autowired
    CircuitAnalysisDao circuitAnalysisDao;

    @GetMapping("/create/otn/{code}")
    public String otnTest(@PathVariable("code") String code) {
        circuitAnalysisService.createOtn(code);
        return "success";
    }

    @GetMapping("/create/internet/{accessCode}/{regionName}")
    public String otnTest(@PathVariable("accessCode") String accessCode, @PathVariable("regionName") String regionName) {
        return internetSpecialtyLine.saveData(regionName, accessCode) ? "success" : "error";

    }

    @GetMapping("/test/internet")
    public String internet() {
        String  path ="src/main/resources/excel/光纤接入.xlsx";
        String sheetName ="互联网专线";
        int  startIndex =7, endIndex =9;
        Sheet sheet = ExcelUtil.getXSSFWorkbookSheet(path, sheetName);
        Set<String> errorList = new HashSet<>();
        for (int i = 1; i <=sheet.getLastRowNum() ; i++) {
            Row row = sheet.getRow(i);
            String region = row.getCell(endIndex).getStringCellValue();
            String accessCode = row.getCell(startIndex).getStringCellValue();
            try {
                internetSpecialtyLine.saveData(region,accessCode);
            }catch (Exception e){
                errorList.add( accessCode  + "  " +region);
            }
        }

        System.out.printf("总数量为%S,失败的数量为%s%n",(sheet.getLastRowNum()-1),errorList.size());
        for (String s : errorList) {
            System.out.println(s);
        }
        return "123";

    }


    /**
     * 重要：本地电路，需要把所有乱跳变硬跳， 不然会提示各种找不到节点！！
     * 重要：本地电路，地区需要变成江苏省
     * @param request
     * @return
     */
    @PostMapping("digitCircuit")
    public String digitCircuit(@RequestBody JSONObject request) {
        String circuitCode = request.getString("code");
        String areaCode = request.getString("areaCode");
        try {
            circuitAnalysisService.analysisCircuit(circuitCode, null, areaCode);
        }
        catch (Exception e) {
            return e.getMessage();
        }
        return "success";

    }


    /**
     * 根据IP 同步资源信息 ip\设备、ip和设备关系（管理IP和业务IP）
     * @param request
     * @return
     */
    @PostMapping("syncResByIp")
    public String syncResByIp(@RequestBody JSONObject request) {
        String ip = request.getString("ip");
        String areaCode = request.getString("areaCode");
        try {
            circuitAnalysisService.syncResByIp(ip,areaCode);
        }
        catch (Exception e) {
            return e.getMessage();
        }
        return "success";

    }


    /**
     * 整合路由
     * @param request
     * @return
     */
    @PostMapping("createRoute")
    public String createRoute(@RequestBody JSONObject request) {
        String cjtype = request.getString("cjtype");
        String code = request.getString("code");
        String areaCode = request.getString("areaCode");
        JSONObject circuit = new JSONObject();
        circuit.put("citySuburbId",areaCode);
        circuit.put("code", code);

        try {
            ipRanJsService.queryiPRanJsPre4(100,1,cjtype,circuit);
        }
        catch (Exception e) {
            return e.getMessage();
        }
        return "success";

    }

    @GetMapping("digTest")
    public String digTest() {
        List<String> info = new ArrayList<>();
        info.add("泰州CTVPN14190C##泰州CTVPN14190C##8321200");
//        info.add("中信银行-江阴兴澄集团ETN0002NP##MSTPJT1001982781##8320200");
//        info.add("中信银行-吉兴集团自助/ETN0002NP##MSTPJT1001799090##8320200");
//        info.add("中信银行-张家港国泰大厦(M66258164)20M##M66258164##8320500");
//        info.add("中信银行-中信银行江东中路359号/ETN0001NP##M23516404##8320100");
//        info.add("中信银行-中信文昌支行/ETN0002NP##M48257706##8321000");
//        info.add("中信银行-张家港紫金国际中信银行/ETN0001NP##M58677964##8320500");
//
//        info.add("中信银行-中信江都支行/ETN0001NP##M40262682##8321000");
//        info.add("苏州-太仓/ETN3220NP##M40047813##8320500");
//        info.add("中信银行-天都大厦商业广场/ETN0001NP##MSTPJS1002292734##8320500");
//        info.add("省建行-秣陵街道秣周东路12号紫金江宁5号楼/ETN0001NP##M10311168##8320100");
//        info.add("省建行-镇兴路208号建设银行/ETN0001NP##M10166159##8320100");
//        info.add("省建行-建行江东路分理处/ETN0001NP##M10176204##8320100");
//        info.add("EA09411001##M07024978##8320100");
//        info.add("JH-CZJ30N001NP##M00112519##8320600");
//        info.add("M23516401##M23516401##8320100");
//        info.add("江都公积金-中信龙川桥北分理处/30N0001NP##M40002854##8321000");
//        info.add("建行-建行轨道维修/ETN0001NP##M00308921##8320400");
//        info.add("省建行-高淳县淳溪镇汶溪路287号中医院机房/ETN0001NP##M15631246##8320100");
//        info.add("M23516423##M23516423##8320100");
//        info.add("EA121139001##M07259977##8320100");
//        info.add("园区建行-建行通安支行(M40052547)/ETN0001NP##M40052547##8320500");
//        info.add("苏州-太仓/ETN3227NP##M40047861##8320500");
//        info.add("园区建行-建行干将房金部(M40065390)10M##M40065390##8320500");
//        info.add("中信银行总部-龙江IDC(GE)-2 NJ/GE0778 F1611173170 NJCOSS10131328_1##M15831291##8320100");
//        info.add("南通中信银行-海安中信银行30N0001NP##M00223481##8320600");
//        info.add("徐州徐州STN100149##M00286854##8320300");
//        info.add("苏州-太仓/ETN3215NP##M40047851##8320500");
//        info.add("苏州-太仓/ETN3219NP##M40047796##8320500");
//        info.add("市政府数据资源中心-建行高邮盂城支行/ETN0001NP##M46157698##8321000");
//        info.add("省建行-南京红十字医院/ETN0001NP##M23596380##8320100");
//        info.add("中信银行镇江分行-丹阳财政局/ETN0001NP##M00142186##8321100");
//        info.add("苏州-昆山/ETN4013NP##M40053722##8320500");
//        info.add("镇江中信银行(镇江市润州区檀山路8号)-镇江新区中信银行（江苏镇江市新区大港金港花园17幢）/ETN0001NP##M00172532##8321100");
//        info.add("镇江中信银行(镇江市润州区檀山路8号)-镇江中信银行网点（镇江市长江路11号滨江银座大厦）/ETN0001NP##M00172530##8321100");
//        info.add("园区建行-建行何山分理处(M40052562)4M##M40052562##8320500");
//        info.add("镇江中信银行(镇江市润州区檀山路8号)-丹阳中信银行（丹阳市开发区东方路报业大厦1-2层）/ETN0001NP##M00172528##8321100");
//        info.add("南通大众燃气-南通中信银行/ETN0001NP##M00264011##8320600");
//        info.add("中信银行-金坛中信银行/ETN0001NP##M00299230##8320400");
//        info.add("中信银行镇江分行-镇江市自来水公司/ETN0001NP##M00151301##8321100");
//        info.add("省建行-建行大厂支行/ETN0001NP##M08851183##8320100");
//        info.add("EA121588001##M07259442##8320100");
//        info.add("EA121303001##M07265530##8320100");
//        info.add("苏州-太仓/ETN3224NP##M40047858##8320500");
//        info.add("南通中信银行-南通中信银行开发区支行/ETN0001NP##M00280308##8320600");
//        info.add("MSTPJS1002291570##MSTPJS1002291570##8320100");
//        info.add("MSTPJS1001711010##MSTPJS1001711010##8320100");
//        info.add("省建行-营苑小区储蓄所/ETN0001NP##M08851216##8320100");
//        info.add("MSTPJT1001893390##MSTPJT1001893390##8320100");
//        info.add("省建行-浦口区浦东路6号-1号/ETN0001NP##M10341192##8320100");
//        info.add("南通中信银行-海安中信银行中坝南路/ETN0001NP##M00280314##8320600");
//        info.add("省建行-建行沙洲街道服务中心/ETN0001NP##M08846189##8320100");
//        info.add("省建行-建设银行胜太路39号开发区分理处/ETN0001NP##M10176165##8320100");
//        info.add("常州中信银行-金坛中信银行/ETN0001NP##M00299230##8320400");
//        info.add("M00314962##M00314962##8320300");
//        info.add("M00315026##M00315026##8320300");
//        info.add("M34506887##M34506887##8320100");
//        info.add("建设银行-建行技术师范学院（名都明都师源宾馆）/ETN0001NP##M00262242##8320400");
//        info.add("南通中信银行-如东中信银行友谊东路/ETN0002NP##M00337048##8320600");
//        info.add("省建行-金鹰尚美酒店负3楼机房/ETN0001NP##M14606243##8320100");
//        info.add("中信银行总部-龙江IDC(GE)-1 NJ/GE0777 F1611173169 NJCOSS10131324_1[##M15831294##8320100");
//        info.add("MSTPJS1002148499##MSTPJS1002148499##8320200");
//        info.add("中信银行镇江分行-镇江市公交总公司/ETN0001NP##M00160547##8321100");
//        info.add("中信银行-太仓太平南路中信银行/ETN0001NP##M58677966##8320500");
//        info.add("苏州-常熟/ETN4755NP##M40051567##8320500");



        for (int i = 0; i < info.size(); i++) {
            String inf = info.get(i);
            try {
                System.out.println(i+"=================================================="+inf);
                circuitAnalysisService.analysisCircuit(inf.split("##")[0], inf.split("##")[1],inf.split("##")[2]);

            }
            catch (Exception e) {
                continue;
            }
        }

        return "success";

    }


    @GetMapping("correctCircuitAccessCode")
    public String correctCircuitAccessCode() {
        List<String> info = new ArrayList<>();
        info.add("M40033045##8320500");
        info.add("M00223816##8321200");
        info.add("M40013253##8320500");
        info.add("泰州兴化STN000053##8321200");
        info.add("建设银行-建行新丰街自助银行/ETN0001NP##8320400");
        info.add("园区建行-黄埭镇为民服务中心ATM/ETN0001NP##8320500");
        for (int i = 0; i < info.size(); i++) {
            String inf = info.get(i);
            try {
                System.out.println(i+"=================================================="+inf);
                circuitAnalysisService.analysisCircuit(inf.split("##")[0], null,inf.split("##")[1]);

            }
            catch (Exception e) {
                continue;
            }
        }

        return "success";

    }

    @GetMapping("/queryLog/{intfCode}/{pageSize}/{currentPage}")
    public ResponseEntity<JSONObject> digitCircuit(@PathVariable("intfCode") String intfCode, @PathVariable("pageSize") int pageSize, @PathVariable("currentPage") int currentPage) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("intfCode", intfCode);
        PageResponse<JSONObject> queryPmInterfaceLog = circuitAnalysisDao.queryPmInterfaceLog(jsonObject, pageSize, currentPage, NRMConstants.SHARDING_CODE);
        JSONObject result = new JSONObject();
        if (null != queryPmInterfaceLog && CollectionUtils.isNotEmpty(queryPmInterfaceLog.getData())) {
            result.put("data", queryPmInterfaceLog.getData());
        } else {
            result.put("data", null);
        }
        return ResponseEntity.ok(result);


    }

}
