package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.AlarmPerformanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("/api/performance")
@Slf4j

public class AlarmPerformanceController {


    @Autowired
    AlarmPerformanceService alarmPerformanceService;


    @GetMapping("")
    @LogAnnotation( interfaceName="性能详细信息")
    public BiyiPageResult<JSONObject> performance(@RequestParam(required = false) Map example) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        String deviceId = jsonObject.getString("device_id");
        String code = jsonObject.getString("code");
        if (StringUtils.isNotEmpty(code)){
            jsonObject.put("code", code);
        }else {
            Objects.requireNonNull(deviceId);
            jsonObject.put("deviceId", deviceId);
        }

        String string = jsonObject.getString("portCodeArray");
//        JSONArray jsonArray=new JSONArray(Arrays.asList(Arrays.stream(string.split(",")).toArray()));
//        jsonObject.put("portCodeArray",jsonArray);
//        if (jsonArray.isEmpty() || StringUtils.isEmpty(string)) {
//            throw new RuntimeException("入参非法");
//        }
        List<JSONObject> data = new ArrayList<JSONObject>();
        long totalCount = 0L;
        String areaName = ShardingEnum.getRegionNameByO3(jsonObject.getString("area_id"));
        Objects.requireNonNull(areaName);
        PageResponse<JSONObject> pageResponse = alarmPerformanceService.performance(jsonObject,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
        if (pageResponse != null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data, totalCount, totalCount);
    }


    @GetMapping("/alarmInfo")
    @LogAnnotation( interfaceName="告警详细信息")
    public BiyiPageResult<JSONObject> alarmInfo(@RequestParam(required = false) Map example) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        String deviceId = jsonObject.getString("device_id");
        String code = jsonObject.getString("code");
        if (StringUtils.isNotEmpty(code)){
            jsonObject.put("code", code);
        }else {
            Objects.requireNonNull(deviceId);
            jsonObject.put("deviceId", deviceId);
        }
        List<JSONObject> data = new ArrayList<JSONObject>();
        long totalCount = 0L;
        String areaName = ShardingEnum.getRegionNameByO3(jsonObject.getString("area_id"));
        Objects.requireNonNull(areaName);
        PageResponse<JSONObject> pageResponse = alarmPerformanceService.alarmInfo(jsonObject,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
        if (pageResponse != null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data, totalCount, totalCount);
    }

    @GetMapping("/qualityInfo")
    public BiyiPageResult<JSONObject> qualityInfo(@RequestParam(required = false) Map example) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        String deviceId = jsonObject.getString("device_id");
        Objects.requireNonNull(deviceId);
        jsonObject.put("deviceId", deviceId);
        List<JSONObject> data = new ArrayList<JSONObject>();
        long totalCount = 0L;
        String areaName = ShardingEnum.getRegionNameByO3(jsonObject.getString("area_id"));
        Objects.requireNonNull(areaName);
        PageResponse<JSONObject> pageResponse = alarmPerformanceService.alarmInfo(jsonObject,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaName));
        if (pageResponse != null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data, totalCount, totalCount);
    }


    @GetMapping("/queryAlarmInfo")
    @LogAnnotation(interfaceName = "告警详细信息")
    public BiyiPageResult<JSONObject> queryAlarmInfo(@RequestParam(required = false) Map example) {
        example.put("name", MapUtils.getString(example, "device_name"));
        example.put("circuit_code", MapUtils.getString(example, "access_code"));
        List<JSONObject> data = alarmPerformanceService.queryAlarmInfo(example);
        boolean isSuccess = CollectionUtils.isNotEmpty(data);
        if (CollectionUtils.isNotEmpty(data)) {
            JSONObjectUtil.convertBigNumberToString(data);
        }
        return new BiyiPageResult(data, isSuccess ? data.size() : 0, isSuccess ? data.size() : 0);
    }



}
