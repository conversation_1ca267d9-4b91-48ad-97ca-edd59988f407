package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustViewNodeDao;
import com.telecom.nrm.domain.NRMConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/cust-view-node")
@Slf4j
public class CustViewNodeController {

    @Autowired
    CustViewNodeDao custViewNodeDao;

    @RequestMapping("/save")
    public void save(@RequestBody JSONObject jsonObject, HttpServletResponse response) {

        custViewNodeDao.save(jsonObject, NRMConstants.SHARDING_GRAPH_DB);
    }

    @DeleteMapping("/{id}")
    public JSONObject deletebyid(@PathVariable String id) {
        Integer result_1 =  custViewNodeDao.delete(id ,NRMConstants.SHARDING_GRAPH_DB);
        JSONObject result = new JSONObject();
        if(result_1 ==1){
            result.put("result","sueccess");
        }else{
            result.put("result", "error");
        }
        return result;
    }

    @GetMapping("")
    public BiyiPageResult<JSONObject> pageQuery(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse =  custViewNodeDao.queryList(jsonObject ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);

        // JSONObject result = new JSONObject();
        List<JSONObject> list = pageResponse.getData();

        //result.put("list", list);

        return new BiyiPageResult(list,pageResponse.getPageInfo().getTotalCount() ,jsonObject.size());
    }

}
