package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.OTNDao;
import com.telecom.nrm.entity.IdcAuditRiskDictvalue;
import com.telecom.nrm.entity.IdcAuditRiskRule;
import com.telecom.nrm.entity.PmPortNetworkOperationAudit;
import com.telecom.nrm.entity.PmProductLinkAnalysis;
import com.telecom.nrm.service.AuditService;
import com.telecom.nrm.service.ProductLinkAnalysisCoreService;
import com.telecom.nrm.utils.ExceptionUtil;
import com.telecom.nrm.utils.Result;
import com.telecom.nrm.dto.ExportResponseDTO;
import org.springframework.http.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/audit")
@Slf4j
public class AuditController {

    @Autowired
    AuditService auditService;
    @Autowired
    OTNDao otnDao;

    @GetMapping("")
    public BiyiPageResult<JSONObject> getList(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<PmProductLinkAnalysis> data = new ArrayList<>();
        Long totalCount = 0L;
        PageResponse<PmProductLinkAnalysis> pageResponse = otnDao.select_pm_product_link_analysis_by_condition(jsonObject
                , pageable.getSize(), pageable.getPage(), "ds_graph_js");
        log.info("pageResponse");
        if (pageResponse != null && pageResponse.getData() != null && pageResponse.getData().size() != 0) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }

        return new BiyiPageResult(data, totalCount, totalCount);
    }

    @GetMapping("startCron")
    public String getList() {
        try {
            auditService.selectAll();
        } catch (Exception e) {
            return "fail";
        }
        return "success";
    }

    @GetMapping("/resetAuditState")
    public String queryOperationAuditCount() {
        try {
            productLinkAnalysisCoreService.resetAuditState();
            return "success";
        } catch (Exception e) {
            return "fail" + e.getMessage();
        }
    }

    @GetMapping("autoAnalysis")
    public String autoAnalysis() {
        try {
            auditService.autoAnalysis();
        } catch (Exception e) {
            return "fail";
        }
        return "success";
    }


    @Autowired
    ProductLinkAnalysisCoreService productLinkAnalysisCoreService;

    @PostMapping("/export")
    public ResponseEntity<ExportResponseDTO> exportCommon(@RequestBody @Valid String json) {
        long controllerStartTime = System.currentTimeMillis();
        try {
            log.info("🎯 [审计Controller] 接收到审计数据导出请求");
            log.info("📋 [审计Controller] 请求参数长度: {} 字符", json != null ? json.length() : 0);
            log.info("📋 [审计Controller] 请求参数内容: {}", json);

            log.info("🔄 [审计Controller] 开始调用审计服务");
            ExportResponseDTO result = auditService.exportToDocumentSecurity(json);
            log.info("🔄 [审计Controller] 审计服务调用完成");

            if (result.isSuccess()) {
                long controllerEndTime = System.currentTimeMillis();
                log.info("✅ [审计Controller] 审计数据导出成功");
                log.info("📁 [审计Controller] 返回文件名: {}", result.getFileName());
                log.info("📊 [审计Controller] 返回数据量: {}", result.getDataCount());
                log.info("⏱️ [审计Controller] Controller层总耗时: {} ms", (controllerEndTime - controllerStartTime));
            } else {
                log.error("❌ [审计Controller] 审计数据导出失败: {}", result.getMessage());
            }

            log.info("📤 [审计Controller] 准备返回响应给前端");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            long controllerEndTime = System.currentTimeMillis();
            log.error("💥 [审计Controller] 审计数据导出异常，Controller层耗时: {} ms", (controllerEndTime - controllerStartTime));
            log.error("💥 [审计Controller] 异常详情: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }


    @PostMapping("/analysis")
    public Result analysis(@RequestBody String str) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(str);
            PageResponse<PmProductLinkAnalysis> result = otnDao.select_pm_product_link_analysis_by_condition(jsonObject,
                    10, 1, "ds_graph_js");
            if (null != result && CollectionUtils.isNotEmpty(result.getData())) {
                productLinkAnalysisCoreService.analysis(result.getData().get(0));
            }

        } catch (Exception e) {
            e.printStackTrace();
            return new Result("500", "稽核失败" + e.getMessage());
        }
        return new Result("200", "稽核成功");
    }

    @PostMapping("/analysisTest")
    public Result analysisTest(@RequestBody String str) {
        List<String> ids = new ArrayList<>();
        ids.add("10057367");
        ids.add("10057390");
        ids.add("10057615");
        ids.add("10056776");
        ids.add("10056985");
        ids.add("10057787");
        ids.add("10058153");
        ids.add("10058154");
        ids.add("10058188");
        ids.add("10058173");
        ids.add("10058247");
        ids.add("10058349");
        ids.add("10058347");
        ids.add("10058402");
        ids.add("10058403");
        ids.add("10058636");
        ids.add("10058625");
        ids.add("10058626");
        ids.add("10058634");
        ids.add("10058635");
        ids.add("10058686");
        ids.add("10058310");
        ids.add("10057146");
        ids.add("10057816");
        ids.add("10056569");
        ids.add("10057016");
        ids.add("10056539");
        ids.add("10056504");
        ids.add("10057142");
        ids.add("10057393");
        ids.add("10057046");
        ids.add("10057232");
        ids.add("10057616");
        ids.add("10057737");
        ids.add("10057958");
        ids.add("10057035");
        ids.add("10057743");
        ids.add("10057412");
        ids.add("10056581");
        ids.add("10057233");
        ids.add("10056958");
        ids.add("10056983");
        ids.add("10057020");
        ids.add("10057569");
        ids.add("10057413");
        ids.add("10057928");
        ids.add("10057929");
        ids.add("10058149");
        ids.add("10058358");
        ids.add("10058398");
        ids.add("10058633");
        ids.add("10058631");
        ids.add("10058632");
        ids.add("10058533");
        ids.add("10058534");
        ids.add("10058618");
        ids.add("10058616");
        ids.add("10058705");
        ids.add("10058737");
        ids.add("10058773");
        ids.add("10058774");
        ids.add("10058775");
        ids.add("10057882");
        ids.add("10058655");
        ids.add("10058456");
        ids.add("10057877");
        ids.add("10058822");
        ids.add("10058823");
        ids.add("10058187");
        ids.add("10058820");
        ids.add("10058704");
        ids.add("10058703");
        ids.add("10057276");
        ids.add("10057324");
        ids.add("10057346");
        ids.add("10057565");
        ids.add("10057885");
        ids.add("10057879");
        ids.add("10057389");
        ids.add("10057145");
        ids.add("10057143");
        ids.add("10058132");
        ids.add("10058270");
        ids.add("10058619");
        ids.add("10058620");
        ids.add("10056977");
        ids.add("10056913");
        ids.add("10056968");
        ids.add("10058128");
        ids.add("10058283");
        ids.add("10056947");
        ids.add("10058336");
        ids.add("10057932");
        ids.add("10057931");
        ids.add("10058260");
        ids.add("10058282");
        ids.add("10057067");
        ids.add("10058752");
        ids.add("10056972");
        ids.add("10057800");
        ids.add("10057431");
        ids.add("10057277");
        ids.add("10058769");
        ids.add("10058768");
        ids.add("10057123");
        ids.add("10056748");
        ids.add("10057144");
        ids.add("10058127");
        ids.add("10058448");
        ids.add("10057903");
        ids.add("10058050");
        ids.add("10058835");
        ids.add("10058834");
        ids.add("10058251");
        ids.add("10056774");
        ids.add("10058490");
        ids.add("10058491");
        ids.add("10058193");
        ids.add("10057391");
        ids.add("10057021");
        ids.add("10057531");
        ids.add("10057706");
        ids.add("10058156");
        ids.add("10058159");
        ids.add("10058158");
        ids.add("10058346");
        ids.add("10058338");
        ids.add("10058744");
        ids.add("10058754");
        ids.add("10058750");
        ids.add("10058877");
        ids.add("10058890");
        ids.add("10058895");
        ids.add("10058885");
        ids.add("10058875");
        ids.add("10059018");
        ids.add("10058927");
        ids.add("10058951");
        ids.add("10058928");
        ids.add("10059019");
        ids.add("10058976");
        ids.add("10059017");
        ids.add("10058967");
        ids.add("10058968");
        ids.add("10058972");
        ids.add("10058977");
        ids.add("10058978");
        ids.add("10058979");
        ids.add("10058980");
        ids.add("10058986");
        ids.add("10058987");
        ids.add("10058999");
        ids.add("10059001");
        ids.add("10059002");
        ids.add("10059004");
        ids.add("10059005");
        ids.add("10059012");
        ids.add("10059013");
        ids.add("10059010");
        ids.add("10059011");
        ids.add("10059025");
        ids.add("10059026");
        ids.add("10059027");
        ids.add("10059037");
        ids.add("10059036");
        ids.add("10059046");
        ids.add("10059048");
        ids.add("10059049");
        ids.add("10059065");
        ids.add("10059066");
        ids.add("10059057");
        ids.add("10059064");
        ids.add("10059067");
        ids.add("10059058");
        ids.add("10059059");
        ids.add("10059060");
        ids.add("10059061");
        ids.add("10059072");
        ids.add("10059073");
        ids.add("10059075");
        ids.add("10059076");
        ids.add("10059083");
        ids.add("10059084");
        ids.add("10059085");
        ids.add("10059086");
        ids.add("10059096");
        ids.add("10059097");
        ids.add("10059094");
        ids.add("10058308");
        ids.add("10059189");
        ids.add("10059193");
        ids.add("10059200");
        ids.add("10059202");
        ids.add("10059204");
        ids.add("10059206");
        ids.add("10059207");
        ids.add("10059126");
        ids.add("10059140");
        ids.add("10059141");
        ids.add("10059147");
        ids.add("10059148");
        ids.add("10059187");
        ids.add("10059182");
        ids.add("10059183");
        ids.add("10059181");
        ids.add("10059186");
        ids.add("10059210");
        ids.add("10059185");
        ids.add("10059211");
        ids.add("10059251");
        ids.add("10059250");
        ids.add("10059252");
        ids.add("10059275");
        ids.add("10059247");
        ids.add("10059248");
        ids.add("10059249");
        ids.add("10059254");
        ids.add("10059279");
        ids.add("10059244");
        ids.add("10059245");
        ids.add("10059246");
        ids.add("10059259");
        ids.add("10059260");
        ids.add("10059228");
        ids.add("10059238");
        ids.add("10059239");
        ids.add("10059240");
        ids.add("10059289");
        ids.add("10059304");
        ids.add("10059355");
        ids.add("10059369");
        ids.add("10059370");
        ids.add("10059371");
        ids.add("10059384");
        ids.add("10059486");
        ids.add("10059487");
        ids.add("10059576");
        ids.add("10059497");
        ids.add("10059498");
        ids.add("10059506");
        ids.add("10059502");
        ids.add("10059527");
        ids.add("10059528");
        ids.add("10059466");
        ids.add("10059467");
        ids.add("10059468");
        ids.add("10059471");
        ids.add("10059197");
        ids.add("10059600");
        ids.add("10059520");
        ids.add("10057330");
        ids.add("10059523");
        ids.add("10056946");
        ids.add("10058950");
        ids.add("10059642");
        ids.add("10059643");
        ids.add("10059276");
        ids.add("10059654");
        ids.add("10059656");
        ids.add("10059671");
        ids.add("10059680");
        ids.add("10059677");
        ids.add("10059683");
        ids.add("10059682");
        ids.add("10059699");
        ids.add("10059700");
        ids.add("10059701");
        ids.add("10059778");
        ids.add("10059718");
        ids.add("10059779");
        ids.add("10059817");
        ids.add("10059833");
        ids.add("10059837");
        ids.add("10058627");
        ids.add("10058629");
        ids.add("10058857");
        ids.add("10058689");
        ids.add("10058677");
        ids.add("10058656");
        ids.add("10057478");
        ids.add("10056767");
        ids.add("10059043");
        ids.add("10057881");
        ids.add("10057467");
        ids.add("10057423");
        ids.add("10058621");
        ids.add("10057234");
        ids.add("10056982");
        ids.add("10057235");
        ids.add("10058460");
        ids.add("10058446");
        ids.add("10058678");
        ids.add("10058461");
        ids.add("10058622");
        ids.add("10058623");
        ids.add("10058757");
        ids.add("10057533");
        ids.add("10057236");
        ids.add("10058700");
        ids.add("10058701");
        ids.add("10058702");
        ids.add("10058766");
        ids.add("10058767");
        ids.add("10059016");
        ids.add("10059188");
        ids.add("10059792");
        ids.add("10058982");
        ids.add("10059050");
        ids.add("10059056");
        ids.add("10059062");
        ids.add("10059068");
        ids.add("10059080");
        ids.add("10059448");
        ids.add("10059081");
        ids.add("10059082");
        ids.add("10059087");
        ids.add("10059093");
        ids.add("10059191");
        ids.add("10059194");
        ids.add("10059261");
        ids.add("10059242");
        ids.add("10059243");
        ids.add("10058190");
        ids.add("10059350");
        ids.add("10059367");
        ids.add("10059368");
        ids.add("10059376");
        ids.add("10059372");
        ids.add("10059373");
        ids.add("10059704");
        ids.add("10059871");
        ids.add("10059447");
        ids.add("10059503");
        ids.add("10057546");
        ids.add("10059875");
        ids.add("10059876");
        ids.add("10059149");
        ids.add("10059241");
        ids.add("10059563");
        ids.add("10056986");
        ids.add("10057739");
        ids.add("10057532");
        ids.add("10058650");
        ids.add("10059044");
        ids.add("10059045");
        ids.add("10056984");
        ids.add("10058624");
        ids.add("10057884");
        ids.add("10058762");
        ids.add("10057878");
        ids.add("10057630");
        ids.add("10058874");
        ids.add("10058191");
        ids.add("10057529");
        ids.add("10058840");
        ids.add("10058129");
        ids.add("10058699");
        ids.add("10058630");
        ids.add("10057345");
        ids.add("10057237");
        ids.add("10058753");
        ids.add("10057819");
        ids.add("10058143");
        ids.add("10057880");
        ids.add("10059646");
        ids.add("10057883");
        ids.add("10057411");
        ids.add("10058756");
        ids.add("10058836");
        ids.add("10058765");
        ids.add("10058764");
        ids.add("10056981");
        ids.add("10058160");
        ids.add("10056775");
        ids.add("10056514");
        ids.add("10058755");
        ids.add("10057820");
        ids.add("10059039");
        ids.add("10059042");
        ids.add("10058983");
        ids.add("10059077");
        ids.add("10059092");
        ids.add("10059508");
        ids.add("10059844");
        ids.add("10059470");
        ids.add("10057337");
        ids.add("10057930");
        ids.add("10059038");
        ids.add("10058772");
        ids.add("10059088");
        ids.add("10059089");
        ids.add("10059090");
        ids.add("10059091");
        ids.add("10058189");
        ids.add("10059035");
        ids.add("10059040");
        ids.add("10059041");
        ids.add("10058770");
        ids.add("10058771");
        ids.add("10058759");
        ids.add("10058761");
        ids.add("10059071");
        ids.add("10059504");
        ids.add("10058760");
        ids.add("10058984");
        ids.add("10059006");
        ids.add("10059009");
        ids.add("10059069");
        ids.add("10059078");
        ids.add("10059702");
        ids.add("10059703");
        ids.add("10059253");
        ids.add("10059007");
        ids.add("10059008");
        ids.add("10059079");
        ids.add("10059507");
        ids.add("10058758");
        ids.add("10059255");
        ids.add("10059256");
        ids.add("10059257");
        ids.add("10059505");
        ids.add("10059845");
        ids.add("10058763");
        ids.add("10059567");
        ids.add("10059571");
        ids.add("10059472");
        ids.add("10058637");
        ids.add("10058638");
        ids.add("10059023");
        ids.add("10059031");
        ids.add("10058639");
        ids.add("10059029");
        ids.add("10059030");
        ids.add("10059033");
        ids.add("10058640");
        ids.add("10058993");
        ids.add("10058642");
        ids.add("10058991");
        ids.add("10059021");
        ids.add("10059028");
        ids.add("10058988");
        ids.add("10059020");
        ids.add("10059024");
        ids.add("10059032");
        ids.add("10058992");
        ids.add("10059014");
        ids.add("10059022");
        ids.add("10059034");
        ids.add("10059884");
        ids.add("10059886");
        ids.add("10059874");
        ids.add("10059848");
        ids.add("10059849");
        ids.add("10059852");
        ids.add("10059854");
        ids.add("10059857");
        ids.add("10059858");
        ids.add("10059859");
        ids.add("10059861");
        ids.add("10059863");
        ids.add("10059865");
        ids.add("10059864");
        ids.add("10059866");
        ids.add("10059903");
        ids.add("10058850");
        for (int i = 0; i < ids.size(); i++) {
            System.out.println(ids.get(i) +"开始......" + i);
            JSONObject p = new JSONObject();
            p.put("id", ids.get(i));
            this.analysis(p.toJSONString());
        }
        return new Result("200", "稽核成功");
    }





    @PostMapping("/queryOperationAuditCount")
    public Result queryOperationAuditCount(@RequestBody JSONObject param) {
        try {
            JSONObject result = auditService.queryOperationAuditCount(param);
            return new Result("200", "成功", result);
        } catch (Exception e) {
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @PostMapping("/queryOperationAuditDetail")
    public Result queryOperationAuditDetail(@RequestBody JSONObject param) {
        try {
            List<JSONObject> result = auditService.queryOperationAuditDetail(param, true);
            return new Result("200", "成功", result);
        } catch (Exception e) {
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @PostMapping("/queryOperationAuditDetailPage")
    public Result queryOperationAuditDetailPage(@RequestBody JSONObject param) {
        try {
            List<JSONObject> result = auditService.queryOperationAuditDetailPage(param);
            return new Result("200", "成功", result);
        } catch (Exception e) {
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @PostMapping("/queryIdcTaskMonitorInfo")
    public Result queryIdcTaskMonitorInfo() {
        try {
            List<JSONObject> result = auditService.queryIdcTaskMonitorInfo();
            return new Result("200", "成功", result);
        } catch (Exception e) {
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @PostMapping("/queryIdcTaskForSaveDeviceLogs")
    public Result queryIdcTaskForSaveDeviceLogs() {
        try {
            List<JSONObject> result = auditService.queryIdcTaskForSaveDeviceLogs();
            return new Result("200", "成功", result);
        } catch (Exception e) {
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @PostMapping("/queryIdcTaskForPortIdentification")
    public Result queryIdcTaskForPortIdentification() {
        try {
            List<JSONObject> result = auditService.queryIdcTaskForPortIdentification();
            return new Result("200", "成功", result);
        } catch (Exception e) {
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @PostMapping("/queryIdcTaskForAuditDaily")
    public Result queryIdcTaskForAuditDaily() {
        try {
            List<JSONObject> result = auditService.queryIdcTaskForAuditDaily();
            return new Result("200", "成功", result);
        } catch (Exception e) {
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @PostMapping("/queryAuditRiskDictvaluePage")
    public Result queryAuditRiskDictvaluePage() {
        try {
            List<IdcAuditRiskDictvalue> result = auditService.queryAuditRiskDictvaluePage();
            return new Result("200", "成功", result);
        } catch (Exception e) {
            log.error("queryAuditRiskDictvaluePage异常", e);
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @GetMapping("/queryRiskRuleById/{id}")
    public Result queryRiskRuleById(@PathVariable("id") BigDecimal id) {
        try {
            List<IdcAuditRiskRule> result = auditService.queryRiskRuleById(id);
            return new Result("200", "成功", result);
        } catch (Exception e) {
            log.error("queryRiskRuleById异常", e);
            return new Result("500", "失败" + e.getMessage());
        }
    }

    @PostMapping("/sendAuditMailMessage")
    public Result sendAuditMailMessage() {
        try {
            auditService.sendAuditMailMessage();
            return new Result("200", "成功");
        } catch (Exception e) {
            log.error("sendAuditMailMessage异常", e);
            return new Result("500", "失败" + ExceptionUtil.getExceptionMsg(e, 2000));
        }
    }
}
