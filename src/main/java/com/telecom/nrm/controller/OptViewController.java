package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.OptViewDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.domain.ServiceType;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.OptViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 光缆视图
 */
@RestController
@RequestMapping("/api/opt-view-api")
@Slf4j
public class OptViewController {

    @Autowired
    private OptViewDao optViewDao;

    @Autowired
    RegionDao regionDao;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    OptViewService optViewService;


    /**
     * 获取光缆数据
     */
    @GetMapping("")
    ResponseEntity<JSONObject> getOptViewData(@RequestParam(required = false) Map<String,Object> parame) {
        log.info("getOptViewData:{}",parame);
        JSONObject jsonParame = (JSONObject) JSON.toJSON(parame);
        JSONObject meta = new JSONObject();
        List<JSONObject> jsonObjectList = optViewDao.fiber_opt_net(new JSONObject(parame), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
        log.info("getOptViewData:{}",jsonObjectList);
        meta.put("opts",jsonObjectList);
        //连接数据
        List<JSONObject> jsonObjectLinkList = optViewDao.fiber_opt_link(new JSONObject(parame), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
        log.info("getOptViewData:{}",jsonObjectLinkList);
        meta.put("links",jsonObjectLinkList);
        return ResponseEntity.ok().body(meta);
    }

    /**
     * 获得光缆连接数据
     */
    @GetMapping("/link")
    ResponseEntity<JSONObject> getOptLinkData(@RequestParam(required = false) Map<String,Object> parame) {
        log.info("getOptLinkData:{}",parame);
        JSONObject jsonParame = (JSONObject) JSON.toJSON(parame);
        JSONObject meta = new JSONObject();
        List<JSONObject> jsonObjectList = optViewDao.fiber_opt_link(new JSONObject(parame), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonParame.getString("ds")));
        log.info("getOptLinkData:{}",jsonObjectList);
        meta.put("links",jsonObjectList);
        return ResponseEntity.ok().body(meta);
    }

    /**
     * 查询字典数据
     * @return
     */
    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary(@RequestParam(required = false) Map parame) {
        log.info("REST request to get dictionary : {}", parame);
        JSONObject parameJSON = (JSONObject) JSON.toJSON(parame);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for (int i = 0; i < Region2List.size(); i++) {
            if (Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))) {
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);
        return ResponseEntity.ok().body(meta);
    }

    /**
     * 根据az节点获得路径方案
     */
    @PostMapping("/path")
    public ResponseEntity<JSONObject> getPath(@RequestBody JSONObject jsonObject) {
        log.info("getPath:{}",jsonObject);
        JSONObject meta = optViewService.getOptLinkScheme(jsonObject);
        return ResponseEntity.ok().body(meta);
    }
    /**
     * 根据光缆编号获得管道信息
     */
    @PostMapping("/pipe")
    public ResponseEntity<JSONObject> getPipe(@RequestBody JSONObject jsonObject) {
        String shardingCode = NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"));
        GraphRequestBuilder builder = new GraphRequestBuilder();

        List<String> nodes = (List<String>) jsonObject.getJSONArray("codes").stream().map(Object::toString).collect(Collectors.toList());
        //shuangluyou
        GraphRequest graphRequest = builder.setApiId("query_cable_pipe").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cable","codes",
                        nodes)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);
        graphService.buildTree(data,"光缆","管道段","pipe_segments");
        List<JSONObject> cableList = graphService.filterNodeByLabel(data,"光缆");
        JSONObject result = new JSONObject();
        result.put("data", cableList);
        return ResponseEntity.ok(result);
    }

    //计算两两光缆是否符合双路由规则，并进行入库
    @PostMapping("/pair")
    public ResponseEntity<JSONObject> pair(@RequestBody JSONObject jsonObject) {
        log.info("pair:{}",jsonObject);
        JSONObject meta = new JSONObject();
        optViewService.computeDoubleRoute(jsonObject);
        return ResponseEntity.ok().body(meta);
    }

    /**
     * 根据当前光缆和起始终止节点查询符合双路由的方案数据
     * parame cableids光缆列表
     * parmae beginNode 起始节点
     * parame endNode 终止节点
     */
    @PostMapping("/compare")
    public ResponseEntity<JSONObject> compare(@RequestBody JSONObject jsonObject) {
        log.info("compare:{}",jsonObject);
        JSONObject meta = optViewService.getDoubleRoute(jsonObject);
        return ResponseEntity.ok().body(meta);
    }

    /**
     * 查询区县光缆数据
     */
    @PostMapping("/areaOptLink")
    ResponseEntity<JSONObject> getOptLinkAreaData(@RequestBody JSONObject parame) {
        log.info("getOptLinkAreaData:{}",parame);
        JSONObject meta = new JSONObject();
        List<JSONObject> jsonObjectList = optViewDao.fiber_opt_net_area(parame, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(parame.getString("ds")));
        log.info("getOptLinkAreaData:{}",jsonObjectList);
        meta.put("links",jsonObjectList);
        return ResponseEntity.ok().body(meta);
    }



}
