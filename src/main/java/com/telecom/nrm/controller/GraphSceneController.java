package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.GraphDao;
import com.telecom.nrm.dao.GraphSceneDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.GraphScene;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.service.GraphSceneService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.utils.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/api/scene")
public class GraphSceneController {

    private final Logger log = LoggerFactory.getLogger(GraphSceneController.class);
    @Autowired
    GraphSceneDao graphSceneDao;

    @Autowired
    GraphDao graphDao;

    @Autowired
    GraphService graphService;

    @Autowired
    GraphSceneService graphSceneService;

    @GetMapping("")
    public BiyiPageResult<GraphScene> getList(@RequestParam(required = false) Map exampleMap , BiyiPageRequest pageable) {
        log.debug("REST request to get entity list");
        // JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        JSONObject example = (JSONObject) JSON.toJSON(exampleMap);
        PageResponse<GraphScene> pageResponse =  graphSceneDao.pageQuery(example ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    @PostMapping("")
    public ResponseEntity<GraphScene> createNew(@RequestBody GraphScene entity) throws URISyntaxException {
        log.debug("REST request to save entity : {}", entity);

        entity.setCreateTime(new Date());

        GraphScene result =  graphSceneDao.save(entity, NRMConstants.SHARDING_GRAPH_DB,"");


        return ResponseEntity.created(new URI("/api/net-config-type" + "/" +result.getId() ))
                .body(result);
    }



    @PutMapping("")
    public ResponseEntity<Result> update(@RequestBody GraphScene entity)  {


        GraphScene result =  graphSceneDao.save(entity, NRMConstants.SHARDING_GRAPH_DB, "");

        return ResponseEntity.ok()
                .body(new Result("200", "成功"));
    }


    @PutMapping("/graph")
    public ResponseEntity<Result> updateGraph(@RequestBody GraphScene entity)  {

        log.debug("REST request to update entity : {}", entity);

        String startNode = entity.getStartNode();

        Graph g = entity.getGraph();

        if (!graphService.checkNoCircle(g)) {
            return ResponseEntity.ok()
                    .body(new Result("500", "不可成环"));
        }

        if (!graphService.checkStartNode(g,startNode)) {
            return ResponseEntity.ok()
                    .body(new Result("500", "起点只能有1个，必须是"+startNode));
        }

        entity.setGraphData(JSONObject.toJSONString(g));
        GraphScene result =  graphSceneDao.save(entity, NRMConstants.SHARDING_GRAPH_DB, "");

        return ResponseEntity.ok()
                .body(new Result("200", "成功"));
    }

    @GetMapping("/{id}")
    public ResponseEntity<GraphScene> get(@PathVariable Long id) {
        log.debug("REST request to get entity : {}", id);
        GraphScene scene = graphSceneService.getById(id);
        return ResponseEntity.ok()
                .body(scene);
    }



    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary () {
        log.debug("REST request to get dictionary ");
        JSONObject meta =new JSONObject();
        return ResponseEntity.ok().body(meta);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEntity(@PathVariable Long id) {

        log.debug("REST request to delete DaProject : {}", id);

        Integer update = 0;
        try{
            update = graphSceneDao.delete(id, NRMConstants.SHARDING_GRAPH_DB,"");
            // daClient.commitTransaction(xid);
        }catch (Exception ex) {
            // daClient.rollbackTransaction(xid);
            log.error(ex.getMessage(),ex);
        }

        Assert.isTrue(update.equals(1),"Update failed");
        return ResponseEntity.ok().build();
    }


}
