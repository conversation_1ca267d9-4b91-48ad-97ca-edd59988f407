package com.telecom.nrm.controller;

import ch.qos.logback.core.joran.spi.ElementSelector;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.da.client.domain.Page;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CutoverDao;
import com.telecom.nrm.dao.CzviewDao;
import com.telecom.nrm.dao.GroupFaultRapidPositioningDao;
import com.telecom.nrm.dao.UserDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.GraphScene;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import com.telecom.nrm.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.parameters.P;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/CzNetResource")
@Slf4j
public class CzviewController {

    @Autowired
    CzviewDao CzviewDao;


    /**
     * 保存割接项目
     *
     * @param params
     * @return
     */
    @PostMapping("/GetWirelessCount")
    public JSONObject GetWirelessCount(@RequestBody JSONObject params) {


        JSONObject info = CzviewDao.network_scale_wuxian_count(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/Get4grruCount")
    public JSONObject Get4grruCount(@RequestBody JSONObject params) {


        JSONObject info = CzviewDao.cz_rru4G_statistics(params, "ds_bc_o3_cz");
        return info;
    }

    @PostMapping("/Get5grruCount")
    public JSONObject Get5grruCount(@RequestBody JSONObject params) {


        JSONObject info = CzviewDao.cz_rru5G_statistics(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetCoverCount")
    public JSONObject GetCoverCount(@RequestBody JSONObject params) {


        JSONObject info = CzviewDao.mrcoveragecount(params, "ds_bc_o3_cz");
        return info;
    }

    @PostMapping("/GetCloudCPUCount")
    public JSONObject GetCLoudCPUCount(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.cloud_cpu_usage_count(params, "ds_bc_o3_cz");
        return info;
    }

    @PostMapping("/GetCloudMemeryCount")
    public JSONObject GetCloudMemeryCount(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.cloudmemorycount(params, "ds_bc_o3_cz");
        return info;
    }

    @PostMapping("/GetCloudStatsCount")
    public JSONObject GetCloudStatsCount(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.cloudstatscount(params, "ds_bc_o3_cz");
        return info;
    }

    @PostMapping("/GetCloudTopuser")
    public JSONObject GetCloudTopuser(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.cloudtopuserseach(params, "ds_bc_o3_cz");
        return info;
    }

    @PostMapping("/GetIDCdata")
    public JSONObject GetIDCdata(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.IDCdataseach(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetKdItvOnlinedata")
    public JSONObject GetKdItvOnlinedata(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czjieruywcount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetOtnPortdata")
    public JSONObject GetOtnPortdata(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czotncount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetMsapPortdata")
    public JSONObject GetMsapPortdata(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czmsapcount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetCN2Portdata")
    public JSONObject GetCN2Portdata(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.cywcn2dkcount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/Get163PortPortdata")
    public JSONObject Get163PortPortdata(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.cyw163dkcount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetChuanshuOtndata")
    public JSONObject GetChuanshuOtndata(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czjtotncount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetChuanshuBof10G")
    public JSONObject GetChuanshuBof10G(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czcsbf10gcount(params, "ds_bc_o3_cz");
        return info;
    }

    @PostMapping("/GetJiaoHuanPort")
    public JSONObject GetJiaoHuanPort(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czjhjdkcount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetChuanShu100G")
    public JSONObject GetChuanShu100G(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czcsbf100gcount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/GetPonRatio")
    public JSONObject GetPonRatio(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czoltponcount(params, "ds_bc_o3_cz");
        return info;
    }
    @PostMapping("/Getczcarrieraggregationdata")
    public JSONObject Getczcarrieraggregationdata(@RequestBody JSONObject params) {

        JSONObject info = CzviewDao.czcarrieraggregationdata(params, "ds_bc_o3_cz");
        return info;
    }
}
