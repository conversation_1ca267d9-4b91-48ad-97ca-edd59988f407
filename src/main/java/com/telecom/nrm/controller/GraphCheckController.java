package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.dao.IPRanJsDao;
import com.telecom.nrm.domain.NRMConstants;

import com.telecom.nrm.entity.PmProductLinkAnalysis;
import com.telecom.nrm.service.ProductLinkAnalysisCoreService;
import com.telecom.nrm.utils.HttpRequestsUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 调用集团校验接口
 */
@RestController
@RequestMapping("/api/check/graph")
public class GraphCheckController {

    @Autowired
    private CircuitAnalysisDao circuitAnalysisDao;

    @Autowired
    private IPRanJsDao iPRanJsDao;

    @Autowired
    private ProductLinkAnalysisCoreService productLinkAnalysisCoreService;




    //2.5数字电路拓扑图稽核工具API
    @GetMapping("/circuit/circuitValidate/transNet/{code}")
    public String transNet (@PathVariable("code") String code) {
        SimpleDateFormat s = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        JSONObject param = new JSONObject();
        JSONObject businessParam = new JSONObject();
        businessParam.put("apiId","780816022");
        businessParam.put("cjType","省业务连接图");
        businessParam.put("custmor","32413241");
        businessParam.put("requestId",new Date().getTime());
        businessParam.put("sendTime",s.format(new Date()));
        JSONArray whereBody = new JSONArray();
        JSONObject circuit = new JSONObject();
        circuit.put("nodeLabel","电路代号");
        circuit.put("propertyLabel","circuit");
        circuit.put("propertyValue",code);
        JSONObject cjTypeName = new JSONObject();
        cjTypeName.put("nodeLabel","组网方式名称");
        cjTypeName.put("propertyLabel","cjTypeName");
//        cjTypeName.put("propertyValue","MSTP接入");
        JSONObject productType = new JSONObject();
        productType.put("nodeLabel","产品类型");
        productType.put("propertyLabel","productType");
//        productType.put("propertyValue","114400");
        JSONObject provOutput = new JSONObject();
        provOutput.put("nodeLabel","省侧拓扑图");
        provOutput.put("propertyLabel","provOutput");
//        provOutput.put("propertyValue",null);
        whereBody.add(circuit);
        whereBody.add(cjTypeName);
        whereBody.add(productType);
        whereBody.add(provOutput);

        param.put("businessParam", businessParam);
        param.put("whereBody", whereBody);


        JSONObject cjTypeParam = new JSONObject();
        cjTypeParam.put("code", code); // 电路代号
        JSONObject cjTypeData = this.circuitAnalysisDao.queryCjtype(cjTypeParam, NRMConstants.SHARDING_GRAPH_DB);
        if (cjTypeData == null || CollectionUtils.isEmpty(cjTypeData.getJSONArray("data"))) {
            return "cjType无法确认";
        }
        JSONArray data = cjTypeData.getJSONArray("data");
        Map<String, String> headers = new HashMap<>();
        headers.put("X-APP-ID","712c84c87a5fe5a1fca04f2c9f81a991");
        headers.put("X-APP-KEY","9e09b9e30ba77508a7ad08f1c5db8550");
        for (int i = 0; i < data.size(); i++) {
            JSONObject cjType = data.getJSONObject(i);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("propertyValue", cjType.getString("code"));
            jsonObject.put("areaCode", cjType.getString("citycode"));
            PageResponse<JSONObject> codeResult = iPRanJsDao.getgraphresults(jsonObject, 10, 0, "ds_graph_js");
            if (codeResult == null || CollectionUtils.isEmpty(codeResult.getData())) {
                return "数据未整合,地区:"+cjType.getString("cityCode");
            }
            cjTypeName.put("propertyValue",cjType.getString("cjtype"));
            productType.put("propertyValue",cjType.getString("crm_product_spec_id"));
            provOutput.put("propertyValue",codeResult.getData().get(0).getString("results"));
            try {
                String res = HttpRequestsUtils.sendPost("http://************:20501/serviceAgent/rest/treegraphworker/circuit/circuitValidate/transNet",
                        headers,param);
                return res;
            } catch (IOException e) {
                return "调用异常:";
            }

        }

        return code;
    }


    /**
     * 添加局站
     * 对数字电路Z端终端添加所属局站 单端出省
     * @param code 电路编码
     * @param stationId 局站ID
     * @param region 地市
     * @return
     */
    @GetMapping("/circuit/circuitValidate/setTerminalStation/{region}/{code}/{stationId}")
    public String setTerminalStation(@PathVariable("region") String region,@PathVariable("code") String code,@PathVariable("stationId") String stationId) {
//        JSONObject cjTypeParam = new JSONObject();
//        cjTypeParam.put("code", code); // 电路代号
//        JSONObject cjTypeData = this.circuitAnalysisDao.queryCjtype(cjTypeParam, NRMConstants.SHARDING_GRAPH_DB);
//        if (cjTypeData == null || CollectionUtils.isEmpty(cjTypeData.getJSONArray("data"))) {
//            return "cjType无法确认";
//        }
//        JSONArray data = cjTypeData.getJSONArray("data");
////        if (data.size() > 1) {
////            return "非出省电路";
////        }
//        JSONObject cjType = data.getJSONObject(0);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("propertyValue", code);
        jsonObject.put("areaCode", region);
        PageResponse<JSONObject> codeResult = iPRanJsDao.getgraphresults(jsonObject, 10, 0, "ds_graph_js");
//        if (codeResult == null || CollectionUtils.isEmpty(codeResult.getData())) {
//            return "数据未整合,地区:"+cjType.getString("cityCode");
//        }
        JSONObject result = codeResult.getData().get(0).getJSONObject("results");
        JSONArray links = result.getJSONObject("data").getJSONArray("links");
        JSONArray nodes = result.getJSONObject("data").getJSONArray("nodes");
        JSONArray relations = result.getJSONObject("data").getJSONArray("relations");
        JSONObject station = null;
        for (int i = 0; i < nodes.size(); i++) {
            if (nodes.getJSONObject(i).getString("id").equals(stationId)) {
                station = nodes.getJSONObject(i);
                break;
            }
        }
        if (station == null) {
            station = this.getStationNode(stationId);
            nodes.add(station);
        }
        JSONObject transLink = null;
        for (int i = 0; i < links.size(); i++) {
            if (
                    ( links.getJSONObject(i).getString("linkType").equals("传输链路") ||
                            links.getJSONObject(i).getString("linkType").equals("以太网链路"))
                ) {
                transLink = links.getJSONObject(i);
                break;
            }
        }
        String zTerminalId = transLink.getString("z_device_id");
        String aTerminalId = transLink.getString("a_device_id");
        JSONObject zTerminal = null;
        JSONObject aTerminal = null;
        for (int i = 0; i < nodes.size(); i++) {
            if (nodes.getJSONObject(i).getString("id").equals(zTerminalId)) {
                zTerminal = nodes.getJSONObject(i);
            }
            if (nodes.getJSONObject(i).getString("id").equals(aTerminalId)) {
                aTerminal = nodes.getJSONObject(i);

            }
        }
        if ( aTerminal.getString("name").indexOf("长途")>=0) {
            zTerminal = aTerminal;
        } else if (!zTerminal.getString("spec_id").equals("1024192003")
                && !zTerminal.getString("spec_id").equals("1024192001")
                && !zTerminal.getString("spec_id").equals("1024192005")) {
//            return "terminal error";
        }

        JSONObject linkRela = new JSONObject();
        linkRela.put("relationtype","位于");
        linkRela.put("gid",zTerminal.getString("id"));
        linkRela.put("a_spec_id",zTerminal.getString("spec_id"));
        linkRela.put("a_entity_id",zTerminal.getString("id"));
        linkRela.put("a_nodeLabels",JSONArray.parseArray(zTerminal.getJSONArray("nodeLabels").toString()));
        linkRela.put("z_spec_id",station.getString("spec_id"));
        linkRela.put("z_entity_id",station.getString("id"));
        linkRela.put("z_nodeLabels",JSONArray.parseArray(station.getJSONArray("nodeLabels").toString()));
        linkRela.put("relationtype","位于");
        // 避免重复问题
//        for (int i = 0; i < relations.size(); i++) {
//            if (relations.getJSONObject(i).getString("a_entity_id").equals(zTerminal.getString("id")) &&
//                    relations.getJSONObject(i).getString("z_entity_id").equals(station.getString("id"))) {
//                return "has";
//            }
//        }
        // 移除其所在局站
        for (int i = relations.size() - 1; i >=0; i--) {
            if (relations.getJSONObject(i).getString("a_entity_id").equals(zTerminal.getString("id")) &&
                    "2210200001".equalsIgnoreCase(relations.getJSONObject(i).getString("z_spec_id"))
            ) {
                relations.remove(i);
            }
            else if (relations.getJSONObject(i).getString("a_entity_id").equals(aTerminal.getString("id")) &&
                    "2210200001".equalsIgnoreCase(relations.getJSONObject(i).getString("z_spec_id"))
            ) {
                relations.remove(i);
            }
        }
        relations.add(linkRela);

        JSONObject param = new JSONObject();
        param.put("code", code);
        param.put("region", region);
        param.put("results",result.toJSONString());
        this.circuitAnalysisDao.saveCodeResult(param, NRMConstants.SHARDING_GRAPH_DB);

        return "success";

    }

    /**
     * 调整
     * @param region
     * @param code
     * @return
     */
    @GetMapping("/circuit/circuitValidate/getRoutes/{region}/{code}")
    public JSONObject getRoutes(@PathVariable("region") String region,@PathVariable("code") String code) {
        //
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("propertyValue", code);
        jsonObject.put("areaCode", region);
        PageResponse<JSONObject> codeResult = iPRanJsDao.getgraphresults(jsonObject, 10, 0, "ds_graph_js");
        return codeResult.getData().get(0).getJSONObject("results");

    }

    /**
     * 长本边界：MSTP-CN2。校验规则：设备(Z端)。拼接结果：集团和省数据拼接失败;集团和省侧边界设备nm_ip不一致,集团nm_ip为:[***********]
     * ,省nm_ip为:[],若集团和省存在相同nm_ip，可考虑省数据下该nm_ip设备所在的link的az端设备是否符合规则中起始终止设备
     * @return
     */
    @GetMapping("/circuit/circuitValidate/setTerminalIp")
    public String setTerminalIp() {
        List<String>  infos = new ArrayList<>();

        infos.add("南京CTVPN1000A##***********");
        infos.add("南京CTVPN1000B##*********, **********");
        infos.add("南京CTVPN1000C##*********");
        infos.add("南京CTVPN1000D##**********");
        infos.add("南京CTVPN1000E##***********");
        infos.add("南京CTVPN1000F##***********");
        infos.add("南京CTVPN1000G##*********");
        infos.add("南京CTVPN1000H##***********");
        infos.add("南京CTVPN1000I##***********");
        infos.add("南京CTVPN1000J##*********");
        infos.add("南京CTVPN1000K##**********");
        infos.add("南京CTVPN1000L##***********");
        infos.add("南京CTVPN1000N##**********");
        infos.add("南京CTVPN1000O##**********");
        infos.add("南京CTVPN1000P##*********");
        infos.add("南京CTVPN1000Q##*********");
        infos.add("南京CTVPN1000R##*********");
        infos.add("南京CTVPN1000S##**********");
        infos.add("南京CTVPN1000T##*********");
        infos.add("南京CTVPN1001B##***********");
        infos.add("南京CTVPN1001C##**********");
        infos.add("南京CTVPN1001D##***********");
        infos.add("南京CTVPN1001E##***********");
        infos.add("南京CTVPN1001F##**********");
        infos.add("南京CTVPN1001O##***********");
        infos.add("南京CTVPN1001P##***********");
        infos.add("南京CTVPN1001Q##***********");
        infos.add("南京CTVPN1001R##**********");
        infos.add("南京CTVPN1001S##***********");
        infos.add("南京CTVPN1001T##*********");
        infos.add("南京CTVPN1001U##**********");
        infos.add("南京CTVPN1001V##*********");
        infos.add("南京CTVPN1001W##*********");
        infos.add("南京CTVPN1001Y##**********");
        infos.add("南京CTVPN1002AA##***********");
        infos.add("南京CTVPN1002AB##***********");
        infos.add("南京CTVPN1002AC##***********");
        infos.add("南京CTVPN1002AD##**********");
        infos.add("南京CTVPN1002AE##*********");
        infos.add("南京CTVPN1002AF##***********");
        infos.add("南京CTVPN1002AG##***********");
        infos.add("南京CTVPN1002AH##**********");
        infos.add("南京CTVPN1002AI##*********");
        infos.add("南京CTVPN1002AJ##**********");
        infos.add("南京CTVPN1002AK##*********");
        infos.add("南京CTVPN1002AL##**********");
        infos.add("南京CTVPN1002AM##*********");
        infos.add("南京CTVPN1002AN##**********");
        infos.add("南京CTVPN1002AO##*********");
        infos.add("南京CTVPN1002AP##**********");
        infos.add("南京CTVPN1002AQ##*********");
        infos.add("南京CTVPN1002S##***********");
        infos.add("南京CTVPN1002T##**********");
        infos.add("南京CTVPN1002U##***********");
        infos.add("南京CTVPN1002V##**********");
        infos.add("南京CTVPN1002W##*********, **********");
        infos.add("南京CTVPN1002Y##*********");
        infos.add("南京CTVPN1002Z##***********");
        infos.add("南京CTVPN1007A##*********");
        infos.add("南京CTVPN1100C##59.43.5.112");
        infos.add("南京CTVPN1100D##*********");
        infos.add("南京CTVPN1100E##59.43.5.112");
        infos.add("南京CTVPN1100F##*********");
        infos.add("南京CTVPN1100G##59.43.5.113");
        infos.add("南京CTVPN1100H##59.43.5.112");
        infos.add("南京CTVPN1107A##***********");
        infos.add("南京CTVPN1107B##***********");
        infos.add("南京CTVPN1107C##***********");
        infos.add("南京CTVPN1107D##***********");
        infos.add("南京CTVPN1107E##***********");
        infos.add("南京CTVPN1107F##***********");
        infos.add("南京CTVPN1107G##*********");
        infos.add("南京CTVPN1107H##**********");
        infos.add("南京CTVPN1107I##**********");
        infos.add("南京CTVPN1107J##*********");
        infos.add("南京CTVPN1107K##*********");
        infos.add("南京CTVPN1107L##**********");
        infos.add("南京CTVPN1107M##**********");
        infos.add("南京CTVPN1107N##*********");
        infos.add("南京CTVPN1124AA##59.43.5.112");
        infos.add("南京CTVPN1124AB##59.43.5.113");
        infos.add("南京CTVPN1124AC##***********");
        infos.add("南京CTVPN1124AE##***********");
        infos.add("南京CTVPN1124AF##***********");
        infos.add("南京CTVPN1124AG##**********");
        infos.add("南京CTVPN1124AH##***********");
        infos.add("南京CTVPN1124AI##**********");
        infos.add("南京CTVPN1124AJ##***********");
        infos.add("南京CTVPN1124AK##59.43.5.113");
        infos.add("南京CTVPN1124AL##59.43.5.112");
        infos.add("南京CTVPN1124AM##***********");
        infos.add("南京CTVPN1124AN##59.43.5.112");
        infos.add("南京CTVPN1124AO##*********");
        infos.add("南京CTVPN1124AP##59.43.5.113");
        infos.add("南京CTVPN1124AQ##**********");
        infos.add("南京CTVPN1124AR##*********");
        infos.add("南京CTVPN1124AS##**********");
        infos.add("南京CTVPN1124AT##***********");
        infos.add("南京CTVPN1124AU##*********");
        infos.add("南京CTVPN1124AV##***********");
        infos.add("南京CTVPN1124AW##***********");
        infos.add("南京CTVPN1124AY##***********");
        infos.add("南京CTVPN1124AZ##***********");
        infos.add("南京CTVPN1124BA##***********");
        infos.add("南京CTVPN1124BB##***********");
        infos.add("南京CTVPN1124BC##***********");
        infos.add("南京CTVPN1124BD##**********");
        infos.add("南京CTVPN1124BE##*********");
        infos.add("南京CTVPN1124BF##*********");
        infos.add("南京CTVPN1124BG##*********");
        infos.add("南京CTVPN1124BH##**********");
        infos.add("南京CTVPN1124BI##**********");
        infos.add("南京CTVPN1124BJ##**********");
        infos.add("南京CTVPN1124BK##*********");
        infos.add("南京CTVPN1124BL##*********");
        infos.add("南京CTVPN1124BM##**********");
        infos.add("南京CTVPN1124BN##**********");
        infos.add("南京CTVPN1124BO##*********");
        infos.add("南京CTVPN1124BP##**********");
        infos.add("南京CTVPN1124BQ##*********");
        infos.add("南京CTVPN1124BR##***********");
        infos.add("南京CTVPN1124BS##***********");
        infos.add("南京CTVPN1124BT##*********");
        infos.add("南京CTVPN1124BU##**********");
        infos.add("南京CTVPN1124BV##**********");
        infos.add("南京CTVPN1124BY##*********");
        infos.add("南京CTVPN1124BZ##***********");
        infos.add("南京CTVPN1124C##*********");
        infos.add("南京CTVPN1124CA##**********");
        infos.add("南京CTVPN1124CB##***********");
        infos.add("南京CTVPN1124E##***********");
        infos.add("南京CTVPN1124F##***********");
        infos.add("南京CTVPN1124G##***********");
        infos.add("南京CTVPN1124H##***********");
        infos.add("南京CTVPN1124I##*********");
        infos.add("南京CTVPN1124J##*********");
        infos.add("南京CTVPN1124K##59.43.5.112");
        infos.add("南京CTVPN1124L##59.43.5.113");
        infos.add("南京CTVPN1124O##*********");
        infos.add("南京CTVPN1124P##***********");
        infos.add("南京CTVPN1124Q##**********");
        infos.add("南京CTVPN1124R##**********");
        infos.add("南京CTVPN1124S##***********");
        infos.add("南京CTVPN1124T##***********");
        infos.add("南京CTVPN1124U##***********");
        infos.add("南京CTVPN1124V##*********");
        infos.add("南京CTVPN1126A##***********");
        infos.add("南京CTVPN1127A##***********");
        infos.add("南京CTVPN11304A##*********");
        infos.add("南京CTVPN1131A##59.43.5.112");
        infos.add("南京CTVPN1132C##*********");
        infos.add("南京CTVPN1132D##59.43.5.112");
        infos.add("南京CTVPN1132M##***********");
        infos.add("南京CTVPN1132N##**********");
        infos.add("南京CTVPN1133A##59.43.5.113");
        infos.add("南京CTVPN11350B##*********");
        infos.add("南京CTVPN11494C##***********");
        infos.add("南京CTVPN1152B##**********");
        infos.add("南京CTVPN1152C##*********");
        infos.add("南京CTVPN1152E##*********");
        infos.add("南京CTVPN1158A##***********");
        infos.add("南京CTVPN1158AA##***********");
        infos.add("南京CTVPN1158AB##***********");
        infos.add("南京CTVPN1158AC##***********");
        infos.add("南京CTVPN1158AD##***********");
        infos.add("南京CTVPN1158AE##***********");
        infos.add("南京CTVPN1158AF##***********");
        infos.add("南京CTVPN1158AG##***********");
        infos.add("南京CTVPN1158AH##***********");
        infos.add("南京CTVPN1158AI##***********");
        infos.add("南京CTVPN1158AJ##***********");
        infos.add("南京CTVPN1158AK##***********");
        infos.add("南京CTVPN1158AL##***********");
        infos.add("南京CTVPN1158AM##***********");
        infos.add("南京CTVPN1158AN##***********");
        infos.add("南京CTVPN1158B##***********");
        infos.add("南京CTVPN1158C##***********");
        infos.add("南京CTVPN1158D##***********");
        infos.add("南京CTVPN1158E##***********");
        infos.add("南京CTVPN1158F##***********");
        infos.add("南京CTVPN1158G##***********");
        infos.add("南京CTVPN1158H##***********");
        infos.add("南京CTVPN1158I##***********");
        infos.add("南京CTVPN1158J##***********");
        infos.add("南京CTVPN1158K##***********");
        infos.add("南京CTVPN1158L##***********");
        infos.add("南京CTVPN1158M##***********");
        infos.add("南京CTVPN1158N##***********");
        infos.add("南京CTVPN1158O##***********");
        infos.add("南京CTVPN1158P##***********");
        infos.add("南京CTVPN1158Q##***********");
        infos.add("南京CTVPN1158R##***********");
        infos.add("南京CTVPN1158S##***********");
        infos.add("南京CTVPN1158T##***********");
        infos.add("南京CTVPN1158U##***********");
        infos.add("南京CTVPN1158V##***********");
        infos.add("南京CTVPN1158W##***********");
        infos.add("南京CTVPN1158Y##***********");
        infos.add("南京CTVPN1158Z##***********");
        infos.add("南京CTVPN11731B##**********");
        infos.add("南京CTVPN13171A##*********");
        infos.add("南京CTVPN13171B##**********");
        infos.add("南京CTVPN18236A##**********");
        infos.add("南京CTVPN18236B##59.43.5.113");
        infos.add("南京CTVPN18236C##***********");
        infos.add("南京CTVPN18236F##*********");
        infos.add("南京CTVPN18921B##**********");
        infos.add("南京CTVPN18921C##***********");
        infos.add("南京CTVPN189A001##**********");
        infos.add("南京CTVPN189A003##**********");
        infos.add("南京CTVPN189A004##**********");
        infos.add("南京CTVPN189A006##**********");
        infos.add("南京CTVPN189A009##**********");
        infos.add("南京CTVPN189A010##**********");
        infos.add("南京CTVPN189A011##**********");
        infos.add("南京CTVPN189A016##**********");
        infos.add("南京CTVPN189A017##**********");
        infos.add("南京CTVPN189A018##**********");
        infos.add("南京CTVPN189A023##**********");
        infos.add("南京CTVPN189A028##**********");
        infos.add("南京CTVPN189A029##**********");
        infos.add("南京CTVPN189A032##**********");
        infos.add("南京CTVPN189A034##**********");
        infos.add("南京CTVPN189A036##**********");
        infos.add("南京CTVPN189A039##**********");
        infos.add("南京CTVPN189A042##**********");
        infos.add("南京CTVPN189A045##**********");
        infos.add("南京CTVPN189A046##**********");
        infos.add("南京CTVPN189B003##*********");
        infos.add("南京CTVPN189B004##*********");
        infos.add("南京CTVPN189B007##*********");
        infos.add("南京CTVPN189B008##*********");
        infos.add("南京CTVPN189B009##***********");
        infos.add("南京CTVPN189B015##*********");
        infos.add("南京CTVPN189B021##*********");
        infos.add("南京CTVPN189B022##*********");
        infos.add("南京CTVPN189B024##*********");
        infos.add("南京CTVPN189B025##*********");
        infos.add("南京CTVPN189B026##*********");
        infos.add("南京CTVPN189C002##**********");
        infos.add("南京CTVPN189C003##**********");
        infos.add("南京CTVPN189C004##**********");
        infos.add("南京CTVPN189C005##**********");
        infos.add("南京CTVPN189C010##**********");
        infos.add("南京CTVPN189C011##**********");
        infos.add("南京CTVPN189C012##**********");
        infos.add("南京CTVPN189C013##**********");
        infos.add("南京CTVPN189C014##**********");
        infos.add("南京CTVPN189C016##**********");
        infos.add("南京CTVPN189C017##**********");
        infos.add("南京CTVPN189C019##**********");
        infos.add("南京CTVPN189C021##**********");
        infos.add("南京CTVPN189C022##**********");
        infos.add("南京CTVPN189C025##**********");
        infos.add("南京CTVPN189C027##**********");
        infos.add("南京CTVPN189C028##**********");
        infos.add("南京CTVPN189C029##**********");
        infos.add("南京CTVPN189C030##**********");
        infos.add("南京CTVPN189C031##**********");
        infos.add("南京CTVPN189C035##**********");
        infos.add("南京CTVPN189C038##**********");
        infos.add("南京CTVPN189C042##**********");
        infos.add("南京CTVPN189C043##**********");
        infos.add("南京CTVPN189C044##**********");
        infos.add("南京CTVPN189D001##*********");
        infos.add("南京CTVPN189D002##*********");
        infos.add("南京CTVPN189D004##*********");
        infos.add("南京CTVPN189D005##*********");
        infos.add("南京CTVPN189D006##*********");
        infos.add("南京CTVPN189D008##*********");
        infos.add("南京CTVPN189D009##*********");
        infos.add("南京CTVPN189D010##*********");
        infos.add("南京CTVPN189D012##*********");
        infos.add("南京CTVPN189D015##*********");
        infos.add("南京CTVPN189D016##*********");
        infos.add("南京CTVPN189D017##*********");
        infos.add("南京CTVPN189D018##*********");
        infos.add("南京CTVPN189D019##*********");
        infos.add("南京CTVPN189D021##*********");
        infos.add("南京CTVPN189D022##*********");
        infos.add("南京CTVPN189D024##*********");
        infos.add("南京CTVPN189D026##*********");
        infos.add("南京CTVPN189D028##*********");
        infos.add("南京CTVPN189D033##*********");
        infos.add("南京CTVPN189D035##*********");
        infos.add("南京CTVPN189D036##*********");
        infos.add("南京CTVPN189D037##*********");
        infos.add("南京CTVPN189D038##*********");
        infos.add("南京CTVPN189D039##*********");
        infos.add("南京CTVPN189D041##*********");
        infos.add("南京CTVPN189D044##*********");
        infos.add("南京CTVPN189D046##*********");
        infos.add("南京CTVPN189D048##*********");
        infos.add("南京CTVPN189D050##*********");
        infos.add("南京CTVPN189D051##*********");
        infos.add("南京CTVPN189D052##*********");
        infos.add("南京CTVPN189D053##*********");
        infos.add("南京CTVPN189D054##*********");
        infos.add("南京CTVPN189D055##*********");
        infos.add("南京CTVPN189D056##*********");
        infos.add("南京CTVPN189D057##*********");
        infos.add("南京CTVPN189D060##*********");
        infos.add("南京CTVPN189G001##59.43.5.112");
        infos.add("南京CTVPN189G002##59.43.5.112");
        infos.add("南京CTVPN189G003##59.43.5.112");
        infos.add("南京CTVPN189G015##59.43.5.112");
        infos.add("南京CTVPN189G016##59.43.5.112");
        infos.add("南京CTVPN189G017##59.43.5.112");
        infos.add("南京CTVPN189H001##59.43.5.113");
        infos.add("南京CTVPN189H002##59.43.5.113");
        infos.add("南京CTVPN189H003##59.43.5.113");
        infos.add("南京CTVPN189H015##59.43.5.113");
        infos.add("南京CTVPN189H016##59.43.5.113");
        infos.add("南京CTVPN189H017##59.43.5.113");
        infos.add("南京CTVPN189H018##59.43.5.113");
        infos.add("南京CTVPN189H023##59.43.5.113");
        infos.add("南京CTVPN189I010##***********");
        infos.add("南京CTVPN189K061##*********");
        infos.add("南京CTVPN189K062##*********");
        infos.add("南京CTVPN189K063##*********");
        infos.add("南京CTVPN189K064##*********");
        infos.add("南京CTVPN189K065##*********");
        infos.add("南京CTVPN189K066##*********");
        infos.add("南京CTVPN189K068##*********");
        infos.add("南京CTVPN189K069##*********");
        infos.add("南京CTVPN189K070##*********");
        infos.add("南京CTVPN189K071##*********");
        infos.add("南京CTVPN189K072##*********");
        infos.add("南京CTVPN189K073##*********");
        infos.add("南京CTVPN189K074##*********");
        infos.add("南京CTVPN189K075##*********");
        infos.add("南京CTVPN189K076##*********");
        infos.add("南京CTVPN189K077##*********");
        infos.add("南京CTVPN189K078##*********");
        infos.add("南京CTVPN189K081##*********");
        infos.add("南京CTVPN189K082##*********");
        infos.add("南京CTVPN189K083##*********");
        infos.add("南京CTVPN189K084##*********");
        infos.add("南京CTVPN189K085##*********");
        infos.add("南京CTVPN189K087##*********");
        infos.add("南京CTVPN189K088##*********");
        infos.add("南京CTVPN189K089##*********");
        infos.add("南京CTVPN189K090##*********");
        infos.add("南京CTVPN189K091##**********");
        infos.add("南京CTVPN189K092##*********");
        infos.add("南京CTVPN189K093##*********");
        infos.add("南京CTVPN189K094##*********");
        infos.add("南京CTVPN189K095##*********");
        infos.add("南京CTVPN189K096##*********");
        infos.add("南京CTVPN189K097##*********");
        infos.add("南京CTVPN189K098##*********");
        infos.add("南京CTVPN189L048##**********");
        infos.add("南京CTVPN189L050##**********");
        infos.add("南京CTVPN189L051##**********");
        infos.add("南京CTVPN189L053##**********");
        infos.add("南京CTVPN189L054##**********");
        infos.add("南京CTVPN189L057##**********");
        infos.add("南京CTVPN189L058##**********");
        infos.add("南京CTVPN189L059##**********");
        infos.add("南京CTVPN189L061##**********");
        infos.add("南京CTVPN189L065##**********");
        infos.add("南京CTVPN189L067##**********");
        infos.add("南京CTVPN189L069##**********");
        infos.add("南京CTVPN189L070##**********");
        infos.add("南京CTVPN189L071##**********");
        infos.add("南京CTVPN189L072##**********");
        infos.add("南京CTVPN189L073##**********");
        infos.add("南京CTVPN189L074##**********");
        infos.add("南京CTVPN189L075##**********");
        infos.add("南京CTVPN189L076##**********");
        infos.add("南京CTVPN189L079##**********");
        infos.add("南京CTVPN189L080##**********");
        infos.add("南京CTVPN189L082##**********");
        infos.add("南京CTVPN189L083##**********");
        infos.add("南京CTVPN189L084##**********");
        infos.add("南京CTVPN19429A##**********");
        infos.add("南京CTVPN200A##59.43.5.113");
        infos.add("南京CTVPN200B##59.43.5.112");
        infos.add("南京CTVPN200C##*********");
        infos.add("南京CTVPN200D##*********");
        infos.add("南京CTVPN200E##59.43.5.112");
        infos.add("南京CTVPN200F##59.43.5.113");
        infos.add("南京CTVPN200G##***********");
        infos.add("南京CTVPN200H##*********");
        infos.add("南京CTVPN202A##59.43.5.113");
        infos.add("南京CTVPN202B##59.43.5.112");
        infos.add("南京CTVPN202C##*********");
        infos.add("南京CTVPN202D##*********");
        infos.add("南京CTVPN202E##59.43.5.112");
        infos.add("南京CTVPN202F##*********");
        infos.add("南京CTVPN202G##59.43.5.113");
        infos.add("南京CTVPN202H##***********");
        infos.add("南京CTVPN204A##*********");
        infos.add("南京CTVPN204B##*********");
        infos.add("南京CTVPN204C##59.43.5.113");
        infos.add("南京CTVPN204D##59.43.5.112");
        infos.add("南京CTVPN204E##***********");
        infos.add("南京CTVPN204F##*********");
        infos.add("南京CTVPN204G##59.43.5.113");
        infos.add("南京CTVPN204H##59.43.5.112");
        infos.add("南京CTVPN206A##***********");
        infos.add("南京CTVPN206AA##*********");
        infos.add("南京CTVPN206AC##*********");
        infos.add("南京CTVPN206AF##**********");
        infos.add("南京CTVPN206AG##***********");
        infos.add("南京CTVPN206AH##***********");
        infos.add("南京CTVPN206AI##*********");
        infos.add("南京CTVPN206B##***********");
        infos.add("南京CTVPN206C##*********");
        infos.add("南京CTVPN206D##*********");
        infos.add("南京CTVPN206E##59.43.5.112");
        infos.add("南京CTVPN206F##59.43.5.113");
        infos.add("南京CTVPN206G##***********");
        infos.add("南京CTVPN206H##59.43.5.113");
        infos.add("南京CTVPN206I##59.43.5.112");
        infos.add("南京CTVPN206J##*********");
        infos.add("南京CTVPN206K##**********");
        infos.add("南京CTVPN206L##*********");
        infos.add("南京CTVPN206M##*********");
        infos.add("南京CTVPN206N##***********");
        infos.add("南京CTVPN206Q##**********");
        infos.add("南京CTVPN206R##*********");
        infos.add("南京CTVPN206U##**********");
        infos.add("南京CTVPN206V##*********");
        infos.add("南京CTVPN206W##*********");
        infos.add("南京CTVPN206Y##**********");
        infos.add("南京CTVPN206Z##**********");
        infos.add("南京CTVPN2900008F##**********");
        infos.add("南京CTVPN29028D##*********");
        infos.add("南京CTVPN29029A##**********");
        infos.add("南京CTVPN29037A##*********");
        infos.add("南京CTVPN29037C##*********");
        infos.add("南京CTVPN29037D##**********");
        infos.add("南京CTVPN29037E##*********");
        infos.add("南京CTVPN29037F##59.43.5.112");
        infos.add("南京CTVPN29037G##**********");
        infos.add("南京CTVPN29037H##**********");
        infos.add("南京CTVPN29037I##***********");
        infos.add("南京CTVPN29037J##***********");
        infos.add("南京CTVPN29037M##59.43.5.112");
        infos.add("南京CTVPN29037N##59.43.5.113");
        infos.add("南京CTVPN2904C##*********");
        infos.add("南京CTVPN2904D##**********");
        infos.add("南京CTVPN2904G##59.43.5.113");
        infos.add("南京CTVPN2904H##59.43.5.112");
        infos.add("南京CTVPN2904I##***********");
        infos.add("南京CTVPN2904J##***********");
        infos.add("南京CTVPN2904K##59.43.5.112");
        infos.add("南京CTVPN2904L##***********");
        infos.add("南京CTVPN2904M##59.43.5.113");
        infos.add("南京CTVPN2904N##*********");
        infos.add("南京CTVPN2905E##**********");
        infos.add("南京CTVPN2905F##*********");
        infos.add("南京CTVPN2905G##59.43.5.112");
        infos.add("南京CTVPN2905H##*********");
        infos.add("南京CTVPN2905I##*********");
        infos.add("南京CTVPN2905J##**********");
        infos.add("南京CTVPN2905K##***********");
        infos.add("南京CTVPN2905L##***********");
        infos.add("南京CTVPN29107A##**********");
        infos.add("南京CTVPN29117A##***********");
        infos.add("南京CTVPN29117B##*********");
        infos.add("南京CTVPN29126A##*********");
        infos.add("南京CTVPN29126B##59.43.5.112");
        infos.add("南京CTVPN29127A##59.43.5.113");
        infos.add("南京CTVPN29127B##59.43.5.112");
        infos.add("南京CTVPN29127C##*********");
        infos.add("南京CTVPN29127D##**********");
        infos.add("南京CTVPN29127E##***********");
        infos.add("南京CTVPN29127F##***********");
        infos.add("南京CTVPN29131A##***********");
        infos.add("南京CTVPN29136C##59.43.5.112");
        infos.add("南京CTVPN29136D##**********");
        infos.add("南京CTVPN29140A##**********");
        infos.add("南京CTVPN29140B##*********");
        infos.add("南京CTVPN29146A##**********");
        infos.add("南京CTVPN29146B##*********");
        infos.add("南京CTVPN29146C##***********");
        infos.add("南京CTVPN29146D##*********");
        infos.add("南京CTVPN29186A##**********");
        infos.add("南京CTVPN29213A##*********");
        infos.add("南京CTVPN29215A##59.43.5.113");
        infos.add("南京CTVPN29215B##**********");
        infos.add("南京CTVPN29276A##**********");
        infos.add("南京CTVPN29276B##*********");
        infos.add("南京CTVPN35126A##**********");
        infos.add("南京CTVPN35126B##*********");
        infos.add("南京CTVPN38019A##***********");
        infos.add("南京CTVPN38037A##**********");
        infos.add("南京CTVPN3999A##***********");
        infos.add("南京CTVPN3999B##***********");
        infos.add("南京CTVPN3999C##***********");
        infos.add("南京CTVPN3999D##***********");
        infos.add("南京CTVPN46061AA##*********");
        infos.add("南京CTVPN46061AB##*********");
        infos.add("南京CTVPN46061AC##**********");
        infos.add("南京CTVPN46061AD##**********");
        infos.add("南京CTVPN46061AE##*********");
        infos.add("南京CTVPN46061AF##*********");
        infos.add("南京CTVPN46061AG##**********");
        infos.add("南京CTVPN46061AH##***********");
        infos.add("南京CTVPN46061AI##**********");
        infos.add("南京CTVPN46061AJ##*********");
        infos.add("南京CTVPN46061AK##*********");
        infos.add("南京CTVPN46061AL##***********");
        infos.add("南京CTVPN46061AM##**********");
        infos.add("南京CTVPN46061AN##***********");
        infos.add("南京CTVPN46061AO##***********");
        infos.add("南京CTVPN46061Z##**********");
        infos.add("南京CTVPN52031A##59.43.5.113");
        infos.add("南京CTVPN52072A##**********");
        infos.add("南京CTVPN52130D##*********");
        infos.add("南京CTVPN52260E##**********");
        infos.add("南京CTVPN52286A##*********");
        infos.add("南京CTVPN52301D##***********");
        infos.add("南京CTVPN52301E##***********");
        infos.add("南京CTVPN52312B##**********");
        infos.add("南京CTVPN52335B##59.43.5.112");
        infos.add("南京CTVPN52353C##***********");
        infos.add("南京CTVPN52492A##**********");
        infos.add("南京CTVPN52492B##**********");
        infos.add("南京CTVPN52492C##*********");
        infos.add("南京CTVPN52492D##**********");
        infos.add("南京CTVPN52492E##*********");
        infos.add("南京CTVPN52492F##*********");
        infos.add("南京CTVPN52542A##*********");
        infos.add("南京CTVPN53040I##59.43.5.112");
        infos.add("南京CTVPN53040J##59.43.5.113");
        infos.add("南京CTVPN53040R##**********");
        infos.add("南京CTVPN53040S##**********");
        infos.add("南京CTVPN53061H##59.43.5.113");
        infos.add("南京CTVPN53061I##***********");
        infos.add("南京CTVPN53061J##*********");
        infos.add("南京CTVPN53083A##***********, **********");
        infos.add("南京CTVPN53083B##***********");
        infos.add("南京CTVPN53094A##**********");
        infos.add("南京CTVPN53094B##*********");
        infos.add("南京CTVPN53432A##*********");
        infos.add("南京CTVPN53763B##*********");
        infos.add("南京CTVPN54028B##*********");
        infos.add("南京CTVPN54217G##59.43.5.113");
        infos.add("南京CTVPN54217H##*********");
        infos.add("南京CTVPN54217I##**********");
        infos.add("南京CTVPN54217L##**********");
        infos.add("南京CTVPN54295B##**********");
        infos.add("南京CTVPN54295D##59.43.5.112");
        infos.add("南京CTVPN54315A##**********");
        infos.add("南京CTVPN54315B##59.43.5.113");
        infos.add("南京CTVPN54360A##*********");
        infos.add("南京CTVPN54360B##***********");
        infos.add("南京CTVPN54390B##***********");
        infos.add("南京CTVPN54452A##*********");
        infos.add("南京CTVPN54483B##**********");
        infos.add("南京CTVPN54549A##59.43.5.113");
        infos.add("南京CTVPN54617B##59.43.5.113");
        infos.add("南京CTVPN54617C##59.43.5.113");
        infos.add("南京CTVPN54617D##*********");
        infos.add("南京CTVPN54617E##*********");
        infos.add("南京CTVPN54617F##*********");
        infos.add("南京CTVPN54617G##*********");
        infos.add("南京CTVPN54617H##59.43.5.112");
        infos.add("南京CTVPN54617I##**********");
        infos.add("南京CTVPN54617J##**********");
        infos.add("南京CTVPN54617K##***********");
        infos.add("南京CTVPN54617L##**********");
        infos.add("南京CTVPN54617M##*********");
        infos.add("南京CTVPN54617N##*********");
        infos.add("南京CTVPN54617O##59.43.5.113");
        infos.add("南京CTVPN54674A##59.43.5.113");
        infos.add("南京CTVPN54674B##**********");
        infos.add("南京CTVPN54674D##59.43.5.112");
        infos.add("南京CTVPN54674E##59.43.5.113");
        infos.add("南京CTVPN54674F##59.43.5.113");
        infos.add("南京CTVPN54674G##**********");
        infos.add("南京CTVPN54682A##**********");
        infos.add("南京CTVPN54682B##*********");
        infos.add("南京CTVPN54682C##*********");
        infos.add("南京CTVPN54682D##**********");
        infos.add("南京CTVPN54725A##**********");
        infos.add("南京CTVPN54808A##**********");
        infos.add("南京CTVPN54819A##*********");
        infos.add("南京CTVPN54819B##**********");
        infos.add("南京CTVPN54906A##**********");
        infos.add("南京CTVPN54935A##*********");
        infos.add("南京CTVPN54935B##**********");
        infos.add("南京CTVPN54935C##**********");
        infos.add("南京CTVPN54935D##*********");
        infos.add("南京CTVPN54935F##***********");
        infos.add("南京CTVPN54935H##***********");
        infos.add("南京CTVPN54935K##*********");
        infos.add("南京CTVPN54935L##**********");
        infos.add("南京CTVPN54987A##*********");
        infos.add("南京CTVPN55128A##***********");
        infos.add("南京CTVPN55140B##**********");
        infos.add("南京CTVPN55159A##***********");
        infos.add("南京CTVPN55159B##***********");
        infos.add("南京CTVPN55160A##**********");
        infos.add("南京CTVPN55160B##59.43.5.113");
        infos.add("南京CTVPN55160C##**********");
        infos.add("南京CTVPN55160D##59.43.5.113");
        infos.add("南京CTVPN55279A##**********");
        infos.add("南京CTVPN55327A##**********");
        infos.add("南京CTVPN55535A##***********");
        infos.add("南京CTVPN55535B##***********");
        infos.add("南京CTVPN55537A##59.43.5.112");
        infos.add("南京CTVPN55546A##*********");
        infos.add("南京CTVPN55551A##***********");
        infos.add("南京CTVPN55774A##59.43.5.113");
        infos.add("南京CTVPN55834A##**********");
        infos.add("南京CTVPN55834C##**********");
        infos.add("南京CTVPN55856A##*********");
        infos.add("南京CTVPN55884B##***********");
        infos.add("南京CTVPN55918B##**********");
        infos.add("南京CTVPN55918C##*********");
        infos.add("南京CTVPN55918F##*********");
        infos.add("南京CTVPN55918G##**********");
        infos.add("南京CTVPN55938B##*********");
        infos.add("南京CTVPN56012A##***********");
        infos.add("南京CTVPN56012B##***********");
        infos.add("南京CTVPN56014A##***********");
        infos.add("南京CTVPN56014B##***********");
        infos.add("南京CTVPN56015A##***********");
        infos.add("南京CTVPN56015B##***********");
        infos.add("南京CTVPN56016A##***********");
        infos.add("南京CTVPN56016B##***********");
        infos.add("南京CTVPN56024A##**********");
        infos.add("南京CTVPN56060B##***********");
        infos.add("南京CTVPN56060C##*********");
        infos.add("南京CTVPN56060D##***********");
        infos.add("南京CTVPN56060E##**********");
        infos.add("南京CTVPN56060F##*********");
        infos.add("南京CTVPN56060G##**********");
        infos.add("南京CTVPN56060H##***********");
        infos.add("南京CTVPN56060I##**********");
        infos.add("南京CTVPN56060J##***********");
        infos.add("南京CTVPN56060K##***********");
        infos.add("南京CTVPN56060L##**********");
        infos.add("南京CTVPN56060M##***********");
        infos.add("南京CTVPN56060N##*********");
        infos.add("南京CTVPN56060O##***********");
        infos.add("南京CTVPN56060P##***********");
        infos.add("南京CTVPN56099A##*********");
        infos.add("南京CTVPN56099B##**********");
        infos.add("南京CTVPN56099C##**********");
        infos.add("南京CTVPN56099D##*********");
        infos.add("南京CTVPN56105A##***********");
        infos.add("南京CTVPN56105B##**********");
        infos.add("南京CTVPN56148A##59.43.5.112");
        infos.add("南京CTVPN56148B##59.43.5.113");
        infos.add("南京CTVPN56148C##59.43.5.112");
        infos.add("南京CTVPN56148D##59.43.5.113");
        infos.add("南京CTVPN56149A##59.43.5.112");
        infos.add("南京CTVPN56149B##59.43.5.112");
        infos.add("南京CTVPN56149C##59.43.5.113");
        infos.add("南京CTVPN56149D##59.43.5.113");
        infos.add("南京CTVPN56150A##59.43.5.113");
        infos.add("南京CTVPN56150B##59.43.5.113");
        infos.add("南京CTVPN56150C##59.43.5.112");
        infos.add("南京CTVPN56150D##59.43.5.112");
        infos.add("南京CTVPN56151A##59.43.5.112");
        infos.add("南京CTVPN56151B##59.43.5.113");
        infos.add("南京CTVPN56151C##59.43.5.113");
        infos.add("南京CTVPN56151D##59.43.5.112");
        infos.add("南京CTVPN56157A##59.43.5.113");
        infos.add("南京CTVPN56157B##59.43.5.112");
        infos.add("南京CTVPN56157C##59.43.5.112");
        infos.add("南京CTVPN56157D##59.43.5.113");
        infos.add("南京CTVPN56160A##59.43.5.112");
        infos.add("南京CTVPN56160B##59.43.5.113");
        infos.add("南京CTVPN56160C##59.43.5.112");
        infos.add("南京CTVPN56160D##59.43.5.113");
        infos.add("南京CTVPN56160E##**********");
        infos.add("南京CTVPN56160F##*********");
        infos.add("南京CTVPN56160G##*********");
        infos.add("南京CTVPN56160H##**********");
        infos.add("南京CTVPN56160I##***********");
        infos.add("南京CTVPN56160J##***********");
        infos.add("南京CTVPN56284A##***********");
        infos.add("南京CTVPN56343A##**********");
        infos.add("南京CTVPN56398A##***********");
        infos.add("南京CTVPN56418A##***********");
        infos.add("南京CTVPN56905A##*********");
        infos.add("南京CTVPN56905B##**********");
        infos.add("南京CTVPN57141A##*********");
        infos.add("南京CTVPN57860A##***********");
        infos.add("南京CTVPN57901A##***********");
        infos.add("南京CTVPN57901B##*********");
        infos.add("南京CTVPN57901C##**********");
        infos.add("南京CTVPN57901D##**********");
        infos.add("南京CTVPN57901E##*********");
        infos.add("南京CTVPN57901K##**********");
        infos.add("南京CTVPN57901M##*********");
        infos.add("南京CTVPN57915A##***********");
        infos.add("南京CTVPN57915B##*********");
        infos.add("南京CTVPN57915C##*********");
        infos.add("南京CTVPN57915D##**********");
        infos.add("南京CTVPN57915E##**********");
        infos.add("南京CTVPN57915F##***********");
        infos.add("南京CTVPN57915G##***********");
        infos.add("南京CTVPN57915H##***********");
        infos.add("南京CTVPN57931A##*********");
        infos.add("南京CTVPN57933A##**********");
        infos.add("南京CTVPN57933B##*********");
        infos.add("南京CTVPN57933C##**********");
        infos.add("南京CTVPN57933D##*********");
        infos.add("南京CTVPN57966A##*********");
        infos.add("南京CTVPN57966B##**********");
        infos.add("南京CTVPN58015A##*********");
        infos.add("南京CTVPN58015B##**********");
        infos.add("南京CTVPN58023A##***********");
        infos.add("南京CTVPN58048A##*********");
        infos.add("南京CTVPN58093A##*********");
        infos.add("南京CTVPN58093E##*********");
        infos.add("南京CTVPN58099A##*********");
        infos.add("南京CTVPN58157A##*********");
        infos.add("南京CTVPN58174I##**********");
        infos.add("南京CTVPN58174J##*********");
        infos.add("南京CTVPN58174K##**********");
        infos.add("南京CTVPN58174L##*********");
        infos.add("南京CTVPN58174M##**********");
        infos.add("南京CTVPN58174N##*********");
        infos.add("南京CTVPN58174O##*********");
        infos.add("南京CTVPN58174P##**********");
        infos.add("南京CTVPN58193I##*********");
        infos.add("南京CTVPN58193J##**********");
        infos.add("南京CTVPN58193K##**********");
        infos.add("南京CTVPN58193L##*********");
        infos.add("南京CTVPN58193M##**********");
        infos.add("南京CTVPN58193N##**********");
        infos.add("南京CTVPN58193O##*********");
        infos.add("南京CTVPN58193P##*********");
        infos.add("南京CTVPN58256A##*********");
        infos.add("南京CTVPN58256B##**********");
        infos.add("南京CTVPN58256D##**********");
        infos.add("南京CTVPN58258A##**********");
        infos.add("南京CTVPN58272A##**********");
        infos.add("南京CTVPN58272B##**********");
        infos.add("南京CTVPN58320A##**********");
        infos.add("南京CTVPN58320B##**********");
        infos.add("南京CTVPN58320C##**********");
        infos.add("南京CTVPN58320D##**********");
        infos.add("南京CTVPN58341A##**********");
        infos.add("南京CTVPN58390A##*********");
        infos.add("南京CTVPN58393A##**********");
        infos.add("南京CTVPN58393B##*********");
        infos.add("南京CTVPN60021A##***********");
        infos.add("南京CTVPN60021B##***********");
        infos.add("南京CTVPN60021C##***********");
        infos.add("南京CTVPN60021D##***********");
        infos.add("南京CTVPN60022A##***********");
        infos.add("南京CTVPN60022B##***********");
        infos.add("南京CTVPN60022C##***********");
        infos.add("南京CTVPN60022D##***********");
        infos.add("南京CTVPN60023A##***********");
        infos.add("南京CTVPN60023B##***********");
        infos.add("南京CTVPN60023C##***********");
        infos.add("南京CTVPN60023D##***********");
        infos.add("南京CTVPN60024A##***********");
        infos.add("南京CTVPN60024B##***********");
        infos.add("南京CTVPN60024C##***********");
        infos.add("南京CTVPN60024D##***********");
        infos.add("南京CTVPN60025A##***********");
        infos.add("南京CTVPN60025B##***********");
        infos.add("南京CTVPN60025C##***********");
        infos.add("南京CTVPN60025D##***********");
        infos.add("南京CTVPN80000F##***********");
        infos.add("南京CTVPN80000G##***********");
        infos.add("南京CTVPN80001A##*********");
        infos.add("南京CTVPN80001B##**********");
        infos.add("南京CTVPN80001C##59.43.5.113");
        infos.add("南京CTVPN80001D##59.43.5.112");
        infos.add("南京CTVPN80002E##***********");
        infos.add("南京CTVPN80003D##***********");
        infos.add("南京CTVPN80011A##*********");
        infos.add("南京CTVPN80011AH##**********");
        infos.add("南京CTVPN80011AT##**********");
        infos.add("南京CTVPN80011AU##*********");
        infos.add("南京CTVPN80011AV##**********");
        infos.add("南京CTVPN80011AW##59.43.5.113");
        infos.add("南京CTVPN80011AY##59.43.5.112");
        infos.add("南京CTVPN80011B##**********");
        infos.add("南京CTVPN80011BA##59.43.5.113");
        infos.add("南京CTVPN80011BC##59.43.5.112");
        infos.add("南京CTVPN80011BE##59.43.5.113");
        infos.add("南京CTVPN80011BF##59.43.5.112");
        infos.add("南京CTVPN80011BH##59.43.5.112");
        infos.add("南京CTVPN80011BI##59.43.5.113");
        infos.add("南京CTVPN80011BK##*********");
        infos.add("南京CTVPN80011BL##**********");
        infos.add("南京CTVPN80011BN##*********");
        infos.add("南京CTVPN80011BP##**********");
        infos.add("南京CTVPN80011G##**********");
        infos.add("南京CTVPN80012AB##***********");
        infos.add("南京CTVPN80012AC##**********");
        infos.add("南京CTVPN80012AE##*********");
        infos.add("南京CTVPN80012AG##***********");
        infos.add("南京CTVPN80012AH##***********");
        infos.add("南京CTVPN80012AI##**********");
        infos.add("南京CTVPN80012AJ##*********");
        infos.add("南京CTVPN80012AK##**********");
        infos.add("南京CTVPN80012B##**********");
        infos.add("南京CTVPN80012C##**********");
        infos.add("南京CTVPN80012G##**********");
        infos.add("南京CTVPN80012L##**********");
        infos.add("南京CTVPN80012V##59.43.5.113");
        infos.add("南京CTVPN80019A##**********");
        infos.add("南京CTVPN80019B##59.43.5.113");
        infos.add("南京CTVPN80031A##***********");
        infos.add("南京CTVPN80032A##***********");
        infos.add("南京CTVPN80036A##*********");
        infos.add("南京CTVPN80037A##**********");
        infos.add("南京CTVPN80101A##**********");
        infos.add("南京CTVPN80101B##*********");
        infos.add("南京CTVPN80102C##*********");
        infos.add("南京CTVPN80102D##***********");
        infos.add("南京CTVPN80130A##***********");
        infos.add("南京CTVPN80130B##**********");
        infos.add("南京CTVPN80130D##*********");
        infos.add("南京CTVPN80130E##**********");
        infos.add("南京CTVPN80130F##*********");
        infos.add("南京CTVPN80130G##*********");
        infos.add("南京CTVPN80130H##**********");
        infos.add("南京CTVPN80130I##**********");
        infos.add("南京CTVPN80130J##***********");
        infos.add("南京CTVPN80131A##**********");
        infos.add("南京CTVPN80131B##***********");
        infos.add("南京CTVPN80131G##***********");
        infos.add("南京CTVPN80131H##*********");
        infos.add("南京CTVPN80131M##**********");
        infos.add("南京CTVPN80131O##***********");
        infos.add("南京CTVPN80134A##59.43.5.112");
        infos.add("南京CTVPN80137C##59.43.5.113");
        infos.add("南京CTVPN80137D##**********");
        infos.add("南京CTVPN80412A##**********");
        infos.add("南京CTVPN80412B##***********");
        infos.add("南京CTVPN80441B##*********");
        infos.add("南京CTVPN80441C##59.43.5.112");
        infos.add("南京CTVPN80441E##**********");
        infos.add("南京CTVPN80502A##59.43.5.112");
        infos.add("南京CTVPN80502B##59.43.5.113");
        infos.add("南京CTVPN81101A##*********");
        infos.add("南京CTVPN81101B##**********");
        infos.add("南京CTVPN998A##***********");
        infos.add("南京CTVPN998AA##**********");
        infos.add("南京CTVPN998AB##***********");
        infos.add("南京CTVPN998AC##**********");
        infos.add("南京CTVPN998AD##***********");
        infos.add("南京CTVPN998AE##***********");
        infos.add("南京CTVPN998AF##***********");
        infos.add("南京CTVPN998AG##*********");
        infos.add("南京CTVPN998AH##*********");
        infos.add("南京CTVPN998AI##*********, **********");
        infos.add("南京CTVPN998AJ##***********");
        infos.add("南京CTVPN998AK##*********");
        infos.add("南京CTVPN998AL##***********");
        infos.add("南京CTVPN998AM##***********");
        infos.add("南京CTVPN998AN##***********");
        infos.add("南京CTVPN998AO##***********");
        infos.add("南京CTVPN998AP##**********");
        infos.add("南京CTVPN998AQ##**********");
        infos.add("南京CTVPN998AR##*********");
        infos.add("南京CTVPN998AS##*********");
        infos.add("南京CTVPN998AT##**********");
        infos.add("南京CTVPN998AU##*********");
        infos.add("南京CTVPN998AV##**********");
        infos.add("南京CTVPN998AW##*********");
        infos.add("南京CTVPN998B##***********");
        infos.add("南京CTVPN998C##***********");
        infos.add("南京CTVPN998D##***********");
        infos.add("南京CTVPN998F##***********");
        infos.add("南京CTVPN998G##***********");
        infos.add("南京CTVPN998K##***********");
        infos.add("南京CTVPN998L##***********");
        infos.add("南京CTVPN998M##**********");
        infos.add("南京CTVPN998O##***********");
        infos.add("南京CTVPN998P##**********");
        infos.add("南京CTVPN998Q##*********");
        infos.add("南京CTVPN998R##***********");
        infos.add("南京CTVPN998S##***********");
        infos.add("南京CTVPN998T##**********");
        infos.add("南京CTVPN998U##***********");
        infos.add("南京CTVPN998V##*********");
        infos.add("南京CTVPN998W##**********");
        infos.add("南京CTVPN998Y##***********");
        infos.add("南京CTVPN998Z##***********");
        infos.add("南京CTVPN999A##***********");
        infos.add("南京CTVPN999AA##**********");
        infos.add("南京CTVPN999AB##***********");
        infos.add("南京CTVPN999AC##*********");
        infos.add("南京CTVPN999AD##*********");
        infos.add("南京CTVPN999AE##**********");
        infos.add("南京CTVPN999AF##*********");
        infos.add("南京CTVPN999AG##**********");
        infos.add("南京CTVPN999AI##***********");
        infos.add("南京CTVPN999AJ##***********");
        infos.add("南京CTVPN999AK##***********");
        infos.add("南京CTVPN999AL##*********");
        infos.add("南京CTVPN999AM##*********");
        infos.add("南京CTVPN999AN##***********");
        infos.add("南京CTVPN999AO##***********");
        infos.add("南京CTVPN999AP##**********");
        infos.add("南京CTVPN999AQ##***********");
        infos.add("南京CTVPN999AV##**********");
        infos.add("南京CTVPN999AW##***********");
        infos.add("南京CTVPN999AY##*********");
        infos.add("南京CTVPN999AZ##***********");
        infos.add("南京CTVPN999BA##***********");
        infos.add("南京CTVPN999BB##**********");
        infos.add("南京CTVPN999BC##*********");
        infos.add("南京CTVPN999BD##***********");
        infos.add("南京CTVPN999BE##***********");
        infos.add("南京CTVPN999BF##***********");
        infos.add("南京CTVPN999BG##***********");
        infos.add("南京CTVPN999BH##***********");
        infos.add("南京CTVPN999C##***********");
        infos.add("南京CTVPN999CP##**********");
        infos.add("南京CTVPN999CQ##**********");
        infos.add("南京CTVPN999CR##*********");
        infos.add("南京CTVPN999CS##*********");
        infos.add("南京CTVPN999CT##**********");
        infos.add("南京CTVPN999CU##*********");
        infos.add("南京CTVPN999CV##*********");
        infos.add("南京CTVPN999CW##**********");
        infos.add("南京CTVPN999D##***********");
        infos.add("南京CTVPN999E##***********");
        infos.add("南京CTVPN999F##***********");
        infos.add("南京CTVPN999G##***********");
        infos.add("南京CTVPN999H##***********");
        infos.add("南京CTVPN999I##***********");
        infos.add("南京CTVPN999J##***********");
        infos.add("南京CTVPN999K##***********");
        infos.add("南京CTVPN999L##***********");
        infos.add("南京CTVPN999M##***********");
        infos.add("南京CTVPN999N##***********");
        infos.add("南京CTVPN999O##***********");
        infos.add("南京CTVPN999P##*********, ***********");
        infos.add("南京CTVPN999Q##*********, **********");
        infos.add("南京CTVPN999R##*********");
        infos.add("南京CTVPN999S##***********");
        infos.add("南京CTVPN999T##**********");
        infos.add("南京CTVPN999W##***********");
        infos.add("南京CTVPN999Y##***********");
        infos.add("南京CTVPN999Z##***********");
        infos.add("南通CTVPN1107A##59.43.2.166");
        infos.add("南通CTVPN1107B##59.43.0.68");
        infos.add("南通CTVPN1107C##59.43.0.68");
        infos.add("南通CTVPN1107D##59.43.2.166");
        infos.add("南通CTVPN1152C##59.43.0.68");
        infos.add("南通CTVPN1152D##59.43.2.166");
        infos.add("南通CTVPN1158A##59.43.0.68");
        infos.add("南通CTVPN1158B##59.43.0.68");
        infos.add("南通CTVPN1158C##59.43.0.68");
        infos.add("南通CTVPN1158D##59.43.0.68");
        infos.add("南通CTVPN1158G##59.43.0.68");
        infos.add("南通CTVPN1158H##59.43.0.68");
        infos.add("南通CTVPN1158I##59.43.0.68");
        infos.add("南通CTVPN1158J##59.43.0.68");
        infos.add("南通CTVPN1158K##59.43.0.68");
        infos.add("南通CTVPN1158L##59.43.0.68");
        infos.add("南通CTVPN1158M##59.43.0.68");
        infos.add("南通CTVPN1158N##59.43.0.68");
        infos.add("南通CTVPN11731A##59.43.0.68");
        infos.add("南通CTVPN13134A##59.43.2.166");
        infos.add("南通CTVPN13134B##59.43.2.166");
        infos.add("南通CTVPN13171A##59.43.2.166");
        infos.add("南通CTVPN13171C##59.43.2.166");
        infos.add("南通CTVPN18236A##59.43.0.68");
        infos.add("南通CTVPN18921A##59.43.0.68");
        infos.add("南通CTVPN18921B##59.43.2.166");
        infos.add("南通CTVPN189A002##59.43.0.68");
        infos.add("南通CTVPN189A003##59.43.0.68");
        infos.add("南通CTVPN189A006##59.43.0.68");
        infos.add("南通CTVPN189A007##59.43.0.68");
        infos.add("南通CTVPN189A008##59.43.0.68");
        infos.add("南通CTVPN189A009##59.43.0.68");
        infos.add("南通CTVPN189A011##59.43.0.68");
        infos.add("南通CTVPN189A012##59.43.0.68");
        infos.add("南通CTVPN189A013##59.43.0.68");
        infos.add("南通CTVPN189A015##59.43.0.68");
        infos.add("南通CTVPN189A016##59.43.0.68");
        infos.add("南通CTVPN189A017##59.43.0.68");
        infos.add("南通CTVPN189A019##59.43.0.68");
        infos.add("南通CTVPN189A022##59.43.0.68");
        infos.add("南通CTVPN189A023##59.43.0.68");
        infos.add("南通CTVPN189A024##59.43.0.68");
        infos.add("南通CTVPN189A025##59.43.0.68");
        infos.add("南通CTVPN189A027##59.43.0.68");
        infos.add("南通CTVPN189A028##59.43.0.68");
        infos.add("南通CTVPN189A030##59.43.0.68");
        infos.add("南通CTVPN189A031##59.43.0.68");
        infos.add("南通CTVPN189A036##59.43.0.68");
        infos.add("南通CTVPN189A038##59.43.0.68");
        infos.add("南通CTVPN189A039##59.43.0.68");
        infos.add("南通CTVPN189A040##59.43.0.68");
        infos.add("南通CTVPN189A042##59.43.0.68");
        infos.add("南通CTVPN189A043##59.43.0.68");
        infos.add("南通CTVPN189B001##59.43.2.166");
        infos.add("南通CTVPN189B002##59.43.2.166");
        infos.add("南通CTVPN189B003##59.43.2.166");
        infos.add("南通CTVPN189B004##59.43.2.166");
        infos.add("南通CTVPN189B005##59.43.2.166");
        infos.add("南通CTVPN189B006##59.43.2.166");
        infos.add("南通CTVPN189B007##59.43.2.166");
        infos.add("南通CTVPN189B008##59.43.2.166");
        infos.add("南通CTVPN189B009##59.43.2.166");
        infos.add("南通CTVPN189B010##59.43.2.166");
        infos.add("南通CTVPN189B011##59.43.2.166");
        infos.add("南通CTVPN189B012##59.43.2.166");
        infos.add("南通CTVPN189B013##59.43.2.166");
        infos.add("南通CTVPN189B014##59.43.2.166");
        infos.add("南通CTVPN189B015##59.43.2.166");
        infos.add("南通CTVPN189B016##59.43.2.166");
        infos.add("南通CTVPN189B017##59.43.2.166");
        infos.add("南通CTVPN189B018##59.43.2.166");
        infos.add("南通CTVPN189B019##59.43.2.166");
        infos.add("南通CTVPN189B020##59.43.2.166");
        infos.add("南通CTVPN189B021##59.43.2.166");
        infos.add("南通CTVPN189B022##59.43.2.166");
        infos.add("南通CTVPN189B023##59.43.2.166");
        infos.add("南通CTVPN189B024##59.43.2.166");
        infos.add("南通CTVPN189B025##59.43.2.166");
        infos.add("南通CTVPN189B026##59.43.2.166");
        infos.add("南通CTVPN189B028##59.43.2.166");
        infos.add("南通CTVPN189B030##59.43.2.166");
        infos.add("南通CTVPN189B031##59.43.2.166");
        infos.add("南通CTVPN189B032##59.43.2.166");
        infos.add("南通CTVPN189B033##59.43.2.166");
        infos.add("南通CTVPN189B036##59.43.2.166");
        infos.add("南通CTVPN189B037##59.43.2.166");
        infos.add("南通CTVPN189B039##59.43.2.166");
        infos.add("南通CTVPN189B040##59.43.2.166");
        infos.add("南通CTVPN189B042##59.43.2.166");
        infos.add("南通CTVPN189B043##59.43.2.166");
        infos.add("南通CTVPN189B045##59.43.2.166");
        infos.add("南通CTVPN189B046##59.43.2.166");
        infos.add("南通CTVPN189B047##59.43.2.166");
        infos.add("南通CTVPN189B051##59.43.2.166");
        infos.add("南通CTVPN189B052##59.43.2.166");
        infos.add("南通CTVPN189B053##59.43.2.166");
        infos.add("南通CTVPN189B054##59.43.2.166");
        infos.add("南通CTVPN189B055##59.43.2.166");
        infos.add("南通CTVPN189B056##59.43.2.166");
        infos.add("南通CTVPN189B057##59.43.2.166");
        infos.add("南通CTVPN189B058##59.43.2.166");
        infos.add("南通CTVPN189B059##59.43.2.166");
        infos.add("南通CTVPN189B060##59.43.2.166");
        infos.add("南通CTVPN189B061##59.43.2.166");
        infos.add("南通CTVPN189B062##59.43.2.166");
        infos.add("南通CTVPN189B063##59.43.2.166");
        infos.add("南通CTVPN189B064##59.43.2.166");
        infos.add("南通CTVPN189B065##59.43.2.166");
        infos.add("南通CTVPN189B066##59.43.2.166");
        infos.add("南通CTVPN189B068##59.43.2.166");
        infos.add("南通CTVPN189B069##59.43.2.166");
        infos.add("南通CTVPN189B070##59.43.2.166");
        infos.add("南通CTVPN189B071##59.43.2.166");
        infos.add("南通CTVPN189B072##59.43.2.166");
        infos.add("南通CTVPN189B073##59.43.2.166");
        infos.add("南通CTVPN189B075##59.43.2.166");
        infos.add("南通CTVPN189B076##59.43.2.166");
        infos.add("南通CTVPN189B077##59.43.2.166");
        infos.add("南通CTVPN189B078##59.43.2.166");
        infos.add("南通CTVPN189B079##59.43.2.166");
        infos.add("南通CTVPN189B080##59.43.2.166");
        infos.add("南通CTVPN189B081##59.43.2.166");
        infos.add("南通CTVPN189B083##59.43.2.166");
        infos.add("南通CTVPN2904C##59.43.0.68");
        infos.add("南通CTVPN29117A##59.43.0.68");
        infos.add("南通CTVPN29123A##59.43.0.68");
        infos.add("南通CTVPN29123B##59.43.2.166");
        infos.add("南通CTVPN29123C##59.43.0.68");
        infos.add("南通CTVPN29123D##59.43.2.166");
        infos.add("南通CTVPN29123E##59.43.2.166");
        infos.add("南通CTVPN29123F##59.43.2.166");
        infos.add("南通CTVPN29123G##59.43.2.166");
        infos.add("南通CTVPN29123H##59.43.0.68");
        infos.add("南通CTVPN29123I##59.43.2.166");
        infos.add("南通CTVPN29127A##59.43.0.68");
        infos.add("南通CTVPN29127B##59.43.2.166");
        infos.add("南通CTVPN29129A##59.43.0.68");
        infos.add("南通CTVPN29186A##59.43.0.68");
        infos.add("南通CTVPN29276A##59.43.2.166");
        infos.add("南通CTVPN38003A##59.43.2.166");
        infos.add("南通CTVPN38019A##59.43.0.68");
        infos.add("南通CTVPN45159A##59.43.2.166");
        infos.add("南通CTVPN52130A##59.43.2.166");
        infos.add("南通CTVPN52492A##59.43.2.166");
        infos.add("南通CTVPN52492B##59.43.2.166");
        infos.add("南通CTVPN52492D##59.43.2.166");
        infos.add("南通CTVPN53040B##59.43.2.166");
        infos.add("南通CTVPN53040C##59.43.2.166");
        infos.add("南通CTVPN53040D##59.43.0.68");
        infos.add("南通CTVPN53040E##59.43.0.68");
        infos.add("南通CTVPN53040J##59.43.2.166");
        infos.add("南通CTVPN54183B##59.43.0.68");
        infos.add("南通CTVPN54217C##59.43.2.166");
        infos.add("南通CTVPN54295A##59.43.2.166");
        infos.add("南通CTVPN54360A##59.43.2.166");
        infos.add("南通CTVPN54360B##59.43.2.166");
        infos.add("南通CTVPN54617A##59.43.2.166");
        infos.add("南通CTVPN54617B##59.43.2.166");
        infos.add("南通CTVPN54617C##59.43.0.68");
        infos.add("南通CTVPN54617D##59.43.2.166");
        infos.add("南通CTVPN54617E##59.43.0.68");
        infos.add("南通CTVPN54617F##59.43.2.166");
        infos.add("南通CTVPN54617G##59.43.2.166");
        infos.add("南通CTVPN54617I##59.43.2.166");
        infos.add("南通CTVPN54617J##59.43.2.166");
        infos.add("南通CTVPN54819A##59.43.2.166");
        infos.add("南通CTVPN55128A##59.43.2.166");
        infos.add("南通CTVPN55160A##59.43.0.68");
        infos.add("南通CTVPN55160B##59.43.2.166");
        infos.add("南通CTVPN55490A##59.43.0.68");
        infos.add("南通CTVPN55537A##59.43.2.166");
        infos.add("南通CTVPN56012A##59.43.0.68");
        infos.add("南通CTVPN56012B##59.43.2.166");
        infos.add("南通CTVPN56105A##59.43.2.166");
        infos.add("南通CTVPN56148A##59.43.0.68");
        infos.add("南通CTVPN56148B##59.43.2.166");
        infos.add("南通CTVPN56149A##59.43.0.68");
        infos.add("南通CTVPN56149B##59.43.2.166");
        infos.add("南通CTVPN56150A##59.43.2.166");
        infos.add("南通CTVPN56150B##59.43.0.68");
        infos.add("南通CTVPN56151A##59.43.2.166");
        infos.add("南通CTVPN56151B##59.43.0.68");
        infos.add("南通CTVPN56905B##59.43.0.68");
        infos.add("南通CTVPN56905C##59.43.2.166");
        infos.add("南通CTVPN57843A##59.43.2.166");
        infos.add("南通CTVPN57933A##59.43.0.68");
        infos.add("南通CTVPN57933B##59.43.2.166");
        infos.add("南通CTVPN57966C##59.43.0.68");
        infos.add("南通CTVPN57966D##59.43.2.166");
        infos.add("南通CTVPN58074A##59.43.2.166");
        infos.add("南通CTVPN58239A##59.43.2.166");
        infos.add("南通CTVPN58320A##59.43.2.166");
        infos.add("南通CTVPN58320B##59.43.2.166");
        infos.add("南通CTVPN58320C##59.43.2.166");
        infos.add("南通CTVPN58320D##59.43.2.166");
        infos.add("南通CTVPN58346A##59.43.0.68");
        infos.add("南通CTVPN58390A##59.43.2.166");
        infos.add("南通CTVPN58393A##59.43.2.166");
        infos.add("南通CTVPN58520A##59.43.0.68");
        infos.add("南通CTVPN80011A##59.43.2.166");
        infos.add("南通CTVPN80011AG##59.43.0.68");
        infos.add("南通CTVPN80011AH##59.43.0.68");
        infos.add("南通CTVPN80011B##59.43.2.166");
        infos.add("南通CTVPN80011D##59.43.2.166");
        infos.add("南通CTVPN80011E##59.43.2.166");
        infos.add("南通CTVPN80011F##59.43.2.166");
        infos.add("南通CTVPN80011G##59.43.2.166");
        infos.add("南通CTVPN80011M##59.43.0.68");
        infos.add("南通CTVPN80011Z##59.43.0.68");
        infos.add("南通CTVPN80012A##59.43.2.166");
        infos.add("南通CTVPN80012B##59.43.2.166");
        infos.add("南通CTVPN80012C##59.43.0.68");
        infos.add("南通CTVPN80012G##59.43.2.166");
        infos.add("南通CTVPN80012H##59.43.2.166");
        infos.add("南通CTVPN80012I##59.43.2.166");
        infos.add("南通CTVPN80012J##59.43.2.166");
        infos.add("南通CTVPN80012K##59.43.2.166");
        infos.add("南通CTVPN80012L##59.43.2.166");
        infos.add("南通CTVPN80012N##59.43.0.68");
        infos.add("南通CTVPN80012P##59.43.2.166");
        infos.add("南通CTVPN80019A##59.43.2.166");
        infos.add("南通CTVPN80019B##59.43.0.68");
        infos.add("南通CTVPN80031A##59.43.0.68");
        infos.add("南通CTVPN80032A##59.43.0.68");
        infos.add("南通CTVPN80130A##59.43.0.68");
        infos.add("南通CTVPN80130B##59.43.2.166");
        infos.add("南通CTVPN80130D##59.43.0.68");
        infos.add("南通CTVPN80130E##59.43.0.68");
        infos.add("南通CTVPN80130F##59.43.2.166");
        infos.add("南通CTVPN80130G##59.43.2.166");
        infos.add("南通CTVPN80130H##59.43.2.166");
        infos.add("南通CTVPN80130I##59.43.2.166");
        infos.add("南通CTVPN80130J##59.43.0.68");
        infos.add("南通CTVPN80130K##59.43.0.68");
        infos.add("南通CTVPN80130M##59.43.0.68");
        infos.add("南通CTVPN80130O##59.43.2.166");
        infos.add("南通CTVPN80130P##59.43.0.68");
        infos.add("南通CTVPN80131A##59.43.2.166");
        infos.add("南通CTVPN80131B##59.43.0.68");
        infos.add("南通CTVPN80202E##59.43.2.166");
        infos.add("南通CTVPN80412A##59.43.2.166");
        infos.add("南通CTVPN80441A##59.43.0.68");
        infos.add("南通CTVPN80501A##59.43.2.166");
        infos.add("南通CTVPN80502A##59.43.0.68");
        infos.add("宿迁CTVPN1107E##59.43.0.74");
        infos.add("宿迁CTVPN1107F##59.43.2.168");
        infos.add("宿迁CTVPN1152C##59.43.2.168");
        infos.add("宿迁CTVPN1158A##59.43.2.168");
        infos.add("宿迁CTVPN1158B##59.43.2.168");
        infos.add("宿迁CTVPN1158C##59.43.2.168");
        infos.add("宿迁CTVPN1158D##59.43.2.168");
        infos.add("宿迁CTVPN1158E##59.43.2.168");
        infos.add("宿迁CTVPN1158F##59.43.2.168");
        infos.add("宿迁CTVPN1158G##59.43.2.168");
        infos.add("宿迁CTVPN1158H##59.43.2.168");
        infos.add("宿迁CTVPN1158I##59.43.2.168");
        infos.add("宿迁CTVPN1158J##59.43.2.168");
        infos.add("宿迁CTVPN1158K##59.43.2.168");
        infos.add("宿迁CTVPN1158L##59.43.2.168");
        infos.add("宿迁CTVPN1158M##59.43.2.168");
        infos.add("宿迁CTVPN1158N##59.43.2.168");
        infos.add("宿迁CTVPN189A001##59.43.0.74");
        infos.add("宿迁CTVPN189A002##59.43.0.74");
        infos.add("宿迁CTVPN189A003##59.43.0.74");
        infos.add("宿迁CTVPN189A004##59.43.0.74");
        infos.add("宿迁CTVPN189B001##59.43.2.168");
        infos.add("宿迁CTVPN29117A##59.43.0.74");
        infos.add("宿迁CTVPN29136A##59.43.0.74");
        infos.add("宿迁CTVPN29136B##59.43.2.168");
        infos.add("宿迁CTVPN29186A##59.43.0.74");
        infos.add("宿迁CTVPN29276A##59.43.0.74");
        infos.add("宿迁CTVPN45159A##59.43.0.74");
        infos.add("宿迁CTVPN45159B##59.43.0.74");
        infos.add("宿迁CTVPN52286A##59.43.0.74");
        infos.add("宿迁CTVPN53040A##59.43.2.168");
        infos.add("宿迁CTVPN53040B##59.43.0.74");
        infos.add("宿迁CTVPN54183A##59.43.0.74");
        infos.add("宿迁CTVPN54315A##59.43.0.74");
        infos.add("宿迁CTVPN54360A##59.43.0.74");
        infos.add("宿迁CTVPN54617B##59.43.0.74");
        infos.add("宿迁CTVPN54617C##59.43.0.74");
        infos.add("宿迁CTVPN54682A##59.43.0.74");
        infos.add("宿迁CTVPN55128A##59.43.0.74");
        infos.add("宿迁CTVPN56036A##59.43.0.74");
        infos.add("宿迁CTVPN56905A##59.43.0.74");
        infos.add("宿迁CTVPN56905B##59.43.2.168");
        infos.add("宿迁CTVPN57901A##59.43.0.74");
        infos.add("宿迁CTVPN58157A##59.43.0.74");
        infos.add("宿迁CTVPN58157B##59.43.0.74");
        infos.add("宿迁CTVPN80011D##59.43.0.74");
        infos.add("宿迁CTVPN80011E##59.43.2.168");
        infos.add("宿迁CTVPN80011F##59.43.0.74");
        infos.add("宿迁CTVPN80011L##59.43.0.74");
        infos.add("宿迁CTVPN80011M##59.43.2.168");
        infos.add("宿迁CTVPN80011N##59.43.0.74");
        infos.add("宿迁CTVPN80012B##59.43.0.74");
        infos.add("宿迁CTVPN80012C##59.43.2.168");
        infos.add("宿迁CTVPN80130A##59.43.0.74");
        infos.add("宿迁CTVPN80130B##59.43.0.74");
        infos.add("宿迁CTVPN80130E##59.43.0.74");
        infos.add("宿迁CTVPN80130F##59.43.0.74");
        infos.add("宿迁CTVPN80130G##59.43.0.74");
        infos.add("宿迁CTVPN80130H##59.43.0.74");
        infos.add("宿迁CTVPN80130I##59.43.0.74");
        infos.add("宿迁CTVPN80130J##59.43.2.168");
        infos.add("宿迁CTVPN80131D##59.43.0.74");
        infos.add("宿迁CTVPN80131E##59.43.2.168");
        infos.add("宿迁CTVPN80134A##59.43.0.74");
        infos.add("宿迁CTVPN80202B##59.43.2.168");
        infos.add("宿迁CTVPN80202D##59.43.0.74");
        infos.add("宿迁CTVPN80412A##59.43.0.74");
        infos.add("宿迁CTVPN80501A##59.43.0.74");
        infos.add("常州CTVPN1107A##*********2, 59.43.0.59");
        infos.add("常州CTVPN1107B##59.43.2.163, *********3");
        infos.add("常州CTVPN1107C##*********3");
        infos.add("常州CTVPN1107D##*********2");
        infos.add("常州CTVPN1107E##*********2");
        infos.add("常州CTVPN1107F##*********3");
        infos.add("常州CTVPN1152A##*********2, 59.43.0.59");
        infos.add("常州CTVPN1152B##59.43.2.163, *********3");
        infos.add("常州CTVPN1152D##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158A##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158B##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158C##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158D##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158E##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158F##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158G##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158H##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158J##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158K##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158L##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158M##*********2, 59.43.0.59");
        infos.add("常州CTVPN1158N##*********2, 59.43.0.59");
        infos.add("常州CTVPN11645B##59.43.2.163, *********3");
        infos.add("常州CTVPN11731A##*********2, 59.43.0.59");
        infos.add("常州CTVPN18236A##59.43.2.163");
        infos.add("常州CTVPN18236B##59.43.2.163, *********3");
        infos.add("常州CTVPN189A001##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A002##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A003##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A005##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A006##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A008##59.43.0.59");
        infos.add("常州CTVPN189A010##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A012##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A014##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A015##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A016##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A018##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A021##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A022##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A023##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A024##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A025##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A028##59.43.0.59");
        infos.add("常州CTVPN189A032##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A033##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A035##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A036##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A037##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A038##*********2, 59.43.0.59");
        infos.add("常州CTVPN189A039##59.43.2.163, *********3");
        infos.add("常州CTVPN189A043##*********2");
        infos.add("常州CTVPN189A044##*********2");
        infos.add("常州CTVPN189A045##*********2");
        infos.add("常州CTVPN189A047##*********2");
        infos.add("常州CTVPN189A049##*********2");
        infos.add("常州CTVPN189A050##*********2");
        infos.add("常州CTVPN189B001##59.43.2.163, *********3");
        infos.add("常州CTVPN189B002##59.43.2.163, *********3");
        infos.add("常州CTVPN189B003##59.43.2.163");
        infos.add("常州CTVPN189B004##59.43.2.163, *********3");
        infos.add("常州CTVPN189B006##59.43.2.163, *********3");
        infos.add("常州CTVPN189B009##59.43.2.163, *********3");
        infos.add("常州CTVPN189B016##59.43.2.163, *********3");
        infos.add("常州CTVPN189B020##59.43.2.163, *********3");
        infos.add("常州CTVPN189B040##59.43.2.163");
        infos.add("常州CTVPN189B041##59.43.2.163, *********3");
        infos.add("常州CTVPN189B042##*********2, 59.43.0.59");
        infos.add("常州CTVPN189B043##*********3");
        infos.add("常州CTVPN189B046##*********3");
        infos.add("常州CTVPN19429A##*********3");
        infos.add("常州CTVPN2904C##*********2, 59.43.0.59");
        infos.add("常州CTVPN2904D##59.43.2.163");
        infos.add("常州CTVPN2905C##59.43.2.163, *********3");
        infos.add("常州CTVPN2905D##*********2, 59.43.0.59");
        infos.add("常州CTVPN29117A##59.43.2.163, *********3");
        infos.add("常州CTVPN29127A##59.43.2.163, *********3");
        infos.add("常州CTVPN29127B##*********2, 59.43.0.59");
        infos.add("常州CTVPN29136A##*********2, 59.43.0.59");
        infos.add("常州CTVPN29136B##59.43.2.163, *********3");
        infos.add("常州CTVPN29186A##59.43.2.163");
        infos.add("常州CTVPN29194A##59.43.2.163, *********3");
        infos.add("常州CTVPN29203A##*********3");
        infos.add("常州CTVPN29203B##*********2");
        infos.add("常州CTVPN29203C##*********2");
        infos.add("常州CTVPN29276A##*********3");
        infos.add("常州CTVPN29290A##*********3");
        infos.add("常州CTVPN29290B##*********2");
        infos.add("常州CTVPN38019A##59.43.2.163, *********3");
        infos.add("常州CTVPN38037A##*********2, 59.43.0.59");
        infos.add("常州CTVPN38037B##59.43.2.163, *********3");
        infos.add("常州CTVPN52130A##59.43.2.163, *********3");
        infos.add("常州CTVPN52492K##*********2, 59.43.0.59");
        infos.add("常州CTVPN52648E##59.43.2.163, *********3");
        infos.add("常州CTVPN52648F##59.43.2.163, *********3");
        infos.add("常州CTVPN52701C##59.43.2.163, *********3");
        infos.add("常州CTVPN52945A##*********2, 59.43.0.59");
        infos.add("常州CTVPN52945B##59.43.2.163");
        infos.add("常州CTVPN53040C##59.43.2.163");
        infos.add("常州CTVPN54183A##*********2, 59.43.0.59");
        infos.add("常州CTVPN54183B##59.43.2.163, *********3");
        infos.add("常州CTVPN54217A##59.43.2.163, *********3");
        infos.add("常州CTVPN54295A##*********2, 59.43.0.59");
        infos.add("常州CTVPN54315A##*********2, 59.43.0.59");
        infos.add("常州CTVPN54360A##59.43.2.163, *********3");
        infos.add("常州CTVPN54487A##*********2, 59.43.0.59");
        infos.add("常州CTVPN54617A##*********2, 59.43.0.59");
        infos.add("常州CTVPN54617B##*********2, 59.43.0.59");
        infos.add("常州CTVPN54617C##*********2, 59.43.0.59");
        infos.add("常州CTVPN54617D##*********2, 59.43.0.59");
        infos.add("常州CTVPN54617E##*********2, 59.43.0.59");
        infos.add("常州CTVPN54617G##59.43.2.163, *********3");
        infos.add("常州CTVPN54819A##59.43.2.163, *********3");
        infos.add("常州CTVPN55128A##*********2, 59.43.0.59");
        infos.add("常州CTVPN55291A##59.43.2.163, *********3");
        infos.add("常州CTVPN55490A##59.43.2.163, *********3");
        infos.add("常州CTVPN55537A##59.43.2.163, *********3");
        infos.add("常州CTVPN55601A##*********2, 59.43.0.59");
        infos.add("常州CTVPN55601B##*********2, 59.43.0.59");
        infos.add("常州CTVPN56012A##*********2, 59.43.0.59");
        infos.add("常州CTVPN56012B##59.43.2.163, *********3");
        infos.add("常州CTVPN56014A##*********2, 59.43.0.59");
        infos.add("常州CTVPN56014B##59.43.2.163, *********3");
        infos.add("常州CTVPN56015A##*********2, 59.43.0.59");
        infos.add("常州CTVPN56015B##59.43.2.163, *********3");
        infos.add("常州CTVPN56016A##*********2, 59.43.0.59");
        infos.add("常州CTVPN56016B##59.43.2.163, *********3");
        infos.add("常州CTVPN56148A##59.43.2.163, *********3");
        infos.add("常州CTVPN56148B##*********2, 59.43.0.59");
        infos.add("常州CTVPN56148C##59.43.2.163, *********3");
        infos.add("常州CTVPN56148D##*********2, 59.43.0.59");
        infos.add("常州CTVPN56149A##59.43.2.163, *********3");
        infos.add("常州CTVPN56149B##*********2, 59.43.0.59");
        infos.add("常州CTVPN56149C##*********2, 59.43.0.59");
        infos.add("常州CTVPN56149D##59.43.2.163, *********3");
        infos.add("常州CTVPN56150A##59.43.2.163, *********3");
        infos.add("常州CTVPN56150B##59.43.2.163, *********3");
        infos.add("常州CTVPN56150C##*********2, 59.43.0.59");
        infos.add("常州CTVPN56150D##*********2, 59.43.0.59");
        infos.add("常州CTVPN56151A##59.43.2.163, *********3");
        infos.add("常州CTVPN56151B##59.43.2.163, *********3");
        infos.add("常州CTVPN56151C##*********2, 59.43.0.59");
        infos.add("常州CTVPN56151D##*********2, 59.43.0.59");
        infos.add("常州CTVPN56905A##*********2, 59.43.0.59");
        infos.add("常州CTVPN56905B##*********2, 59.43.0.59");
        infos.add("常州CTVPN57901A##*********2, 59.43.0.59");
        infos.add("常州CTVPN57966A##*********2, 59.43.0.59");
        infos.add("常州CTVPN57966B##59.43.2.163, *********3");
        infos.add("常州CTVPN58048A##59.43.2.163");
        infos.add("常州CTVPN58256A##59.43.2.163");
        infos.add("常州CTVPN80011A##*********2, 59.43.0.59");
        infos.add("常州CTVPN80011AA##*********2, 59.43.0.59");
        infos.add("常州CTVPN80011AE##*********2");
        infos.add("常州CTVPN80011B##*********2, 59.43.0.59");
        infos.add("常州CTVPN80011C##59.43.2.163, *********3");
        infos.add("常州CTVPN80011D##*********2, 59.43.0.59");
        infos.add("常州CTVPN80011L##*********2, 59.43.0.59");
        infos.add("常州CTVPN80011S##*********2, 59.43.0.59");
        infos.add("常州CTVPN80011U##*********2, 59.43.0.59");
        infos.add("常州CTVPN80012B##*********2, 59.43.0.59");
        infos.add("常州CTVPN80012D##*********2, 59.43.0.59");
        infos.add("常州CTVPN80012H##*********2, 59.43.0.59");
        infos.add("常州CTVPN80019A##*********2, 59.43.0.59");
        infos.add("常州CTVPN80019B##59.43.2.163, *********3");
        infos.add("常州CTVPN80031B##*********2, 59.43.0.59");
        infos.add("常州CTVPN80032B##59.43.2.163, *********3");
        infos.add("常州CTVPN80130A##*********2, 59.43.0.59");
        infos.add("常州CTVPN80130B##59.43.2.163, *********3");
        infos.add("常州CTVPN80130D##*********2, 59.43.0.59");
        infos.add("常州CTVPN80130E##59.43.2.163, *********3");
        infos.add("常州CTVPN80130G##59.43.2.163, *********3");
        infos.add("常州CTVPN80130H##*********2, 59.43.0.59");
        infos.add("常州CTVPN80130I##59.43.2.163");
        infos.add("常州CTVPN80131C##59.43.2.163, *********3");
        infos.add("常州CTVPN80131D##*********2, 59.43.0.59");
        infos.add("常州CTVPN80131E##*********3, 59.43.0.59");
        infos.add("常州CTVPN80441A##*********2, 59.43.0.59");
        infos.add("常州CTVPN80501A##59.43.2.163");
        infos.add("常州CTVPN80502A##*********2, 59.43.0.59");
        infos.add("徐州CTVPN1107A##59.43.0.83");
        infos.add("徐州CTVPN1107B##59.43.2.171");
        infos.add("徐州CTVPN11391B##59.43.2.171");
        infos.add("徐州CTVPN1158A##59.43.0.83");
        infos.add("徐州CTVPN1158Y##59.43.0.83");
        infos.add("徐州CTVPN1158Z##59.43.0.83");
        infos.add("徐州CTVPN189A001##59.43.0.83");
        infos.add("徐州CTVPN189A003##59.43.0.83");
        infos.add("徐州CTVPN189A004##59.43.0.83");
        infos.add("徐州CTVPN189A005##59.43.0.83");
        infos.add("徐州CTVPN189A006##59.43.0.83");
        infos.add("徐州CTVPN189A007##59.43.0.83");
        infos.add("徐州CTVPN189A008##59.43.0.83");
        infos.add("徐州CTVPN189A009##59.43.0.83");
        infos.add("徐州CTVPN189A010##59.43.0.83");
        infos.add("徐州CTVPN189A013##59.43.0.83");
        infos.add("徐州CTVPN189A014##59.43.0.83");
        infos.add("徐州CTVPN189A015##59.43.0.83");
        infos.add("徐州CTVPN189A016##59.43.0.83");
        infos.add("徐州CTVPN189A017##59.43.0.83");
        infos.add("徐州CTVPN189A019##59.43.0.83");
        infos.add("徐州CTVPN189A022##59.43.0.83");
        infos.add("徐州CTVPN189A023##59.43.0.83");
        infos.add("徐州CTVPN189A024##59.43.0.83");
        infos.add("徐州CTVPN189A026##59.43.0.83");
        infos.add("徐州CTVPN189A027##59.43.0.83");
        infos.add("徐州CTVPN189A028##59.43.0.83");
        infos.add("徐州CTVPN189A029##59.43.0.83");
        infos.add("徐州CTVPN189A030##59.43.0.83");
        infos.add("徐州CTVPN189A031##59.43.0.83");
        infos.add("徐州CTVPN189A032##59.43.0.83");
        infos.add("徐州CTVPN189A034##59.43.0.83");
        infos.add("徐州CTVPN189A035##59.43.0.83");
        infos.add("徐州CTVPN189A036##59.43.0.83");
        infos.add("徐州CTVPN189A037##59.43.0.83");
        infos.add("徐州CTVPN189A038##59.43.0.83");
        infos.add("徐州CTVPN189A039##59.43.0.83");
        infos.add("徐州CTVPN189B001##59.43.2.171");
        infos.add("徐州CTVPN189B0010##59.43.2.171");
        infos.add("徐州CTVPN189B002##59.43.2.171");
        infos.add("徐州CTVPN189B003##59.43.2.171");
        infos.add("徐州CTVPN189B004##59.43.2.171");
        infos.add("徐州CTVPN189B006##59.43.2.171");
        infos.add("徐州CTVPN189B007##59.43.2.171");
        infos.add("徐州CTVPN189B009##59.43.2.171");
        infos.add("徐州CTVPN189B011##59.43.2.171");
        infos.add("徐州CTVPN189B012##59.43.2.171");
        infos.add("徐州CTVPN189B013##59.43.2.171");
        infos.add("徐州CTVPN189B014##59.43.2.171");
        infos.add("徐州CTVPN189B015##59.43.2.171");
        infos.add("徐州CTVPN196A##59.43.2.171");
        infos.add("徐州CTVPN196B##59.43.2.171");
        infos.add("徐州CTVPN29117A##59.43.0.83");
        infos.add("徐州CTVPN29215D##59.43.0.83");
        infos.add("徐州CTVPN29215E##59.43.2.171");
        infos.add("徐州CTVPN29215F##59.43.0.83");
        infos.add("徐州CTVPN29215G##59.43.0.83");
        infos.add("徐州CTVPN29215H##59.43.0.83");
        infos.add("徐州CTVPN29215I##59.43.0.83");
        infos.add("徐州CTVPN29215J##59.43.0.83");
        infos.add("徐州CTVPN29215K##59.43.2.171");
        infos.add("徐州CTVPN29215L##59.43.0.83");
        infos.add("徐州CTVPN29215M##59.43.0.83");
        infos.add("徐州CTVPN29215N##59.43.0.83");
        infos.add("徐州CTVPN29215O##59.43.2.171");
        infos.add("徐州CTVPN29215P##59.43.2.171");
        infos.add("徐州CTVPN29215Q##59.43.2.171");
        infos.add("徐州CTVPN29215R##59.43.2.171");
        infos.add("徐州CTVPN29215S##59.43.2.171");
        infos.add("徐州CTVPN29215T##59.43.2.171");
        infos.add("徐州CTVPN29276A##59.43.2.171");
        infos.add("徐州CTVPN35126B##59.43.0.83");
        infos.add("徐州CTVPN38037A##59.43.2.171");
        infos.add("徐州CTVPN52492E##59.43.0.83");
        infos.add("徐州CTVPN53040D##59.43.0.83");
        infos.add("徐州CTVPN53040E##59.43.0.83");
        infos.add("徐州CTVPN53094A##59.43.0.83");
        infos.add("徐州CTVPN53094B##59.43.0.83");
        infos.add("徐州CTVPN54183B##59.43.2.171");
        infos.add("徐州CTVPN54183C##59.43.2.171");
        infos.add("徐州CTVPN54315A##59.43.2.171");
        infos.add("徐州CTVPN54617A##59.43.0.83");
        infos.add("徐州CTVPN54617D##59.43.0.83");
        infos.add("徐州CTVPN54617E##59.43.0.83");
        infos.add("徐州CTVPN54617F##59.43.2.171");
        infos.add("徐州CTVPN54617G##59.43.2.171");
        infos.add("徐州CTVPN54819A##59.43.2.171");
        infos.add("徐州CTVPN55128A##59.43.0.83");
        infos.add("徐州CTVPN55159A##59.43.0.83");
        infos.add("徐州CTVPN55160B##59.43.2.171");
        infos.add("徐州CTVPN55537A##59.43.0.83");
        infos.add("徐州CTVPN56418A##59.43.0.83");
        infos.add("徐州CTVPN58256A##59.43.0.83");
        infos.add("徐州CTVPN58272A##59.43.0.83");
        infos.add("徐州CTVPN58320A##59.43.0.83");
        infos.add("徐州CTVPN58380B##59.43.0.83");
        infos.add("徐州CTVPN58491A##59.43.0.83");
        infos.add("徐州CTVPN70020A##59.43.0.83");
        infos.add("徐州CTVPN80011A##59.43.0.83");
        infos.add("徐州CTVPN80011B##59.43.2.171");
        infos.add("徐州CTVPN80011C##59.43.2.171");
        infos.add("徐州CTVPN80011G##59.43.2.171");
        infos.add("徐州CTVPN80011H##59.43.2.171");
        infos.add("徐州CTVPN80011I##59.43.2.171");
        infos.add("徐州CTVPN80011L##59.43.0.83");
        infos.add("徐州CTVPN80012A##59.43.0.83");
        infos.add("徐州CTVPN80012D##59.43.0.83");
        infos.add("徐州CTVPN80012F##59.43.2.171");
        infos.add("徐州CTVPN80012G##59.43.0.83");
        infos.add("徐州CTVPN80012H##59.43.0.83");
        infos.add("徐州CTVPN80012I##59.43.0.83");
        infos.add("徐州CTVPN80012L##59.43.0.83");
        infos.add("徐州CTVPN80012P##59.43.0.83");
        infos.add("徐州CTVPN80019A##59.43.2.171");
        infos.add("徐州CTVPN80019B##59.43.0.83");
        infos.add("徐州CTVPN80031B##59.43.0.83");
        infos.add("徐州CTVPN80032A##59.43.0.83");
        infos.add("徐州CTVPN80130A##59.43.2.171");
        infos.add("徐州CTVPN80130C##59.43.0.83");
        infos.add("徐州CTVPN80130D##59.43.2.171");
        infos.add("徐州CTVPN80130E##59.43.2.171");
        infos.add("徐州CTVPN80130F##59.43.2.171");
        infos.add("徐州CTVPN80130G##59.43.2.171");
        infos.add("徐州CTVPN80130H##59.43.2.171");
        infos.add("徐州CTVPN80130K##59.43.2.171");
        infos.add("徐州CTVPN80130M##59.43.0.83");
        infos.add("徐州CTVPN80130N##59.43.2.171");
        infos.add("徐州CTVPN80131A##59.43.2.171");
        infos.add("徐州CTVPN80131B##59.43.0.83");
        infos.add("徐州CTVPN80202G##59.43.2.171");
        infos.add("徐州CTVPN80412A##59.43.0.83");
        infos.add("徐州CTVPN80441A##59.43.0.83");
        infos.add("徐州CTVPN80501A##59.43.2.171");
        infos.add("徐州CTVPN80502A##59.43.0.83");
        infos.add("徐州CTVPN81101A##59.43.2.171");
        infos.add("徐州CTVPN81101B##59.43.0.83");
        infos.add("扬州CTVPN1100A##59.43.0.89");
        infos.add("扬州CTVPN1100B##59.43.2.173");
        infos.add("扬州CTVPN1100C##59.43.0.89");
        infos.add("扬州CTVPN1100D##59.43.2.173");
        infos.add("扬州CTVPN1107A##59.43.2.173");
        infos.add("扬州CTVPN1107B##59.43.0.89");
        infos.add("扬州CTVPN1107D##59.43.2.173");
        infos.add("扬州CTVPN1107F##59.43.0.89");
        infos.add("扬州CTVPN1124C##*********");
        infos.add("扬州CTVPN1124D##***********");
        infos.add("扬州CTVPN1124E##***********");
        infos.add("扬州CTVPN1124F##**********");
        infos.add("扬州CTVPN1124G##***********");
        infos.add("扬州CTVPN1124H##***********");
        infos.add("扬州CTVPN1152A##59.43.2.173");
        infos.add("扬州CTVPN1152B##59.43.0.89");
        infos.add("扬州CTVPN1158E##59.43.2.173");
        infos.add("扬州CTVPN1158F##59.43.2.173");
        infos.add("扬州CTVPN1158G##59.43.2.173");
        infos.add("扬州CTVPN1158N##59.43.2.173");
        infos.add("扬州CTVPN1158O##59.43.2.173");
        infos.add("扬州CTVPN1158P##59.43.2.173");
        infos.add("扬州CTVPN1158Q##59.43.2.173");
        infos.add("扬州CTVPN1158R##59.43.2.173");
        infos.add("扬州CTVPN11759A##59.43.2.173");
        infos.add("扬州CTVPN13134A##59.43.0.89");
        infos.add("扬州CTVPN18311A##59.43.2.173");
        infos.add("扬州CTVPN18311B##59.43.0.89");
        infos.add("扬州CTVPN189A001##59.43.0.89");
        infos.add("扬州CTVPN189A002##59.43.0.89");
        infos.add("扬州CTVPN189A003##59.43.0.89");
        infos.add("扬州CTVPN189A004##59.43.0.89");
        infos.add("扬州CTVPN189A005##59.43.0.89");
        infos.add("扬州CTVPN189A006##59.43.0.89");
        infos.add("扬州CTVPN189A007##59.43.0.89");
        infos.add("扬州CTVPN189A009##59.43.0.89");
        infos.add("扬州CTVPN189A011##59.43.0.89");
        infos.add("扬州CTVPN189B002##59.43.2.173");
        infos.add("扬州CTVPN189B003##59.43.2.173");
        infos.add("扬州CTVPN19429A##59.43.2.173");
        infos.add("扬州CTVPN206A##59.43.0.89");
        infos.add("扬州CTVPN206B##59.43.2.173");
        infos.add("扬州CTVPN2904E##59.43.0.89");
        infos.add("扬州CTVPN2904F##59.43.2.173");
        infos.add("扬州CTVPN2905E##***********");
        infos.add("扬州CTVPN2905F##**********");
        infos.add("扬州CTVPN2905G##*********");
        infos.add("扬州CTVPN2905H##***********");
        infos.add("扬州CTVPN29117A##59.43.0.89");
        infos.add("扬州CTVPN29127C##59.43.2.173");
        infos.add("扬州CTVPN29127D##59.43.0.89");
        infos.add("扬州CTVPN29127E##***********");
        infos.add("扬州CTVPN29127F##*********");
        infos.add("扬州CTVPN29127G##**********");
        infos.add("扬州CTVPN29127H##***********");
        infos.add("扬州CTVPN29130A##59.43.0.89");
        infos.add("扬州CTVPN29131B##59.43.2.173");
        infos.add("扬州CTVPN29136C##59.43.0.89");
        infos.add("扬州CTVPN29136D##59.43.2.173");
        infos.add("扬州CTVPN29146A##59.43.2.173");
        infos.add("扬州CTVPN29146B##59.43.0.89");
        infos.add("扬州CTVPN29178A##59.43.0.89");
        infos.add("扬州CTVPN29186A##59.43.0.89");
        infos.add("扬州CTVPN29276A##59.43.0.89");
        infos.add("扬州CTVPN38037A##59.43.2.173");
        infos.add("扬州CTVPN52492A##59.43.2.173");
        infos.add("扬州CTVPN52492B##59.43.0.89");
        infos.add("扬州CTVPN52492D##59.43.2.173");
        infos.add("扬州CTVPN52492E##59.43.2.173");
        infos.add("扬州CTVPN52648A##59.43.0.89");
        infos.add("扬州CTVPN53040H##59.43.2.173");
        infos.add("扬州CTVPN54043A##59.43.0.89");
        infos.add("扬州CTVPN54315A##59.43.0.89");
        infos.add("扬州CTVPN54617A##59.43.0.89");
        infos.add("扬州CTVPN54617B##59.43.2.173");
        infos.add("扬州CTVPN54617C##59.43.0.89");
        infos.add("扬州CTVPN54617D##59.43.0.89");
        infos.add("扬州CTVPN54819A##59.43.2.173");
        infos.add("扬州CTVPN54868A##59.43.0.89");
        infos.add("扬州CTVPN55128A##59.43.2.173");
        infos.add("扬州CTVPN55128B##59.43.2.173");
        infos.add("扬州CTVPN55160A##59.43.0.89");
        infos.add("扬州CTVPN56160A##59.43.2.173");
        infos.add("扬州CTVPN56160B##59.43.0.89");
        infos.add("扬州CTVPN56905A##59.43.0.89");
        infos.add("扬州CTVPN56905B##59.43.2.173");
        infos.add("扬州CTVPN57966A##59.43.2.173");
        infos.add("扬州CTVPN57966B##59.43.0.89");
        infos.add("扬州CTVPN58129A##59.43.0.89");
        infos.add("扬州CTVPN58256A##59.43.2.173");
        infos.add("扬州CTVPN58256B##59.43.2.173");
        infos.add("扬州CTVPN58320A##59.43.2.173");
        infos.add("扬州CTVPN58393A##59.43.0.89");
        infos.add("扬州CTVPN60037A##59.43.0.89");
        infos.add("扬州CTVPN60037B##59.43.0.89");
        infos.add("扬州CTVPN60037C##59.43.0.89");
        infos.add("扬州CTVPN60037D##59.43.0.89");
        infos.add("扬州CTVPN60037E##59.43.2.173");
        infos.add("扬州CTVPN60037F##59.43.2.173");
        infos.add("扬州CTVPN60037G##59.43.2.173");
        infos.add("扬州CTVPN60037H##59.43.2.173");
        infos.add("扬州CTVPN80011A##59.43.0.89");
        infos.add("扬州CTVPN80011B##59.43.0.89");
        infos.add("扬州CTVPN80011C##59.43.0.89");
        infos.add("扬州CTVPN80011D##59.43.0.89");
        infos.add("扬州CTVPN80011E##59.43.0.89");
        infos.add("扬州CTVPN80011N##59.43.2.173");
        infos.add("扬州CTVPN80012A##59.43.0.89");
        infos.add("扬州CTVPN80012B##59.43.2.173");
        infos.add("扬州CTVPN80012C##59.43.2.173");
        infos.add("扬州CTVPN80019A##59.43.0.89");
        infos.add("扬州CTVPN80019B##59.43.2.173");
        infos.add("扬州CTVPN80031A##59.43.0.89");
        infos.add("扬州CTVPN80032A##59.43.0.89");
        infos.add("扬州CTVPN80130A##59.43.0.89");
        infos.add("扬州CTVPN80130B##59.43.2.173");
        infos.add("扬州CTVPN80130C##59.43.2.173");
        infos.add("扬州CTVPN80130D##59.43.0.89");
        infos.add("扬州CTVPN80130E##59.43.2.173");
        infos.add("扬州CTVPN80130F##59.43.0.89");
        infos.add("扬州CTVPN80131A##59.43.2.173");
        infos.add("扬州CTVPN80131B##59.43.0.89");
        infos.add("扬州CTVPN80412A##59.43.0.89");
        infos.add("扬州CTVPN80441A##59.43.0.89");
        infos.add("扬州CTVPN80501A##59.43.0.89");
        infos.add("扬州CTVPN80502A##59.43.0.89");
        infos.add("无锡CTVPN1107A##59.43.5.160");
        infos.add("无锡CTVPN1107B##59.43.5.161");
        infos.add("无锡CTVPN1107E##59.43.5.114");
        infos.add("无锡CTVPN1107F##59.43.5.115");
        infos.add("无锡CTVPN1107G##59.43.5.114");
        infos.add("无锡CTVPN1107H##59.43.5.115");
        infos.add("无锡CTVPN1107I##59.43.5.114");
        infos.add("无锡CTVPN1107J##59.43.5.115");
        infos.add("无锡CTVPN11391A##59.43.5.161");
        infos.add("无锡CTVPN1158A##59.43.5.114");
        infos.add("无锡CTVPN1158AA##59.43.5.115");
        infos.add("无锡CTVPN1158AB##59.43.5.114");
        infos.add("无锡CTVPN1158B##59.43.5.114");
        infos.add("无锡CTVPN1158C##59.43.5.114");
        infos.add("无锡CTVPN1158D##59.43.5.114");
        infos.add("无锡CTVPN1158E##59.43.5.115");
        infos.add("无锡CTVPN1158F##59.43.5.115");
        infos.add("无锡CTVPN1158G##59.43.5.115");
        infos.add("无锡CTVPN1158H##59.43.5.115");
        infos.add("无锡CTVPN1158I##59.43.5.114");
        infos.add("无锡CTVPN1158J##59.43.5.114");
        infos.add("无锡CTVPN1158K##59.43.5.115");
        infos.add("无锡CTVPN1158L##59.43.5.115");
        infos.add("无锡CTVPN1158M##59.43.5.114");
        infos.add("无锡CTVPN1158N##59.43.5.114");
        infos.add("无锡CTVPN1158O##59.43.5.115");
        infos.add("无锡CTVPN1158P##59.43.5.114");
        infos.add("无锡CTVPN1158Q##59.43.5.115");
        infos.add("无锡CTVPN1158R##59.43.5.115");
        infos.add("无锡CTVPN1158S##59.43.5.115");
        infos.add("无锡CTVPN1158T##59.43.5.115");
        infos.add("无锡CTVPN1158U##59.43.5.114");
        infos.add("无锡CTVPN1158V##59.43.5.114");
        infos.add("无锡CTVPN1158W##59.43.5.114");
        infos.add("无锡CTVPN1158Y##59.43.5.115");
        infos.add("无锡CTVPN1158Z##59.43.5.114");
        infos.add("无锡CTVPN11731B##59.43.5.160, 59.43.5.161");
        infos.add("无锡CTVPN18236A##59.43.5.160");
        infos.add("无锡CTVPN18236C##59.43.5.115");
        infos.add("无锡CTVPN18921A##59.43.5.160");
        infos.add("无锡CTVPN18921B##59.43.5.115");
        infos.add("无锡CTVPN18921C##59.43.5.160");
        infos.add("无锡CTVPN189A004##59.43.5.160");
        infos.add("无锡CTVPN189A007##59.43.5.160");
        infos.add("无锡CTVPN189A009##59.43.5.160");
        infos.add("无锡CTVPN189A011##59.43.5.160");
        infos.add("无锡CTVPN189A013##59.43.5.160");
        infos.add("无锡CTVPN189A014##59.43.5.160");
        infos.add("无锡CTVPN189A015##59.43.5.160");
        infos.add("无锡CTVPN189A016##59.43.5.160");
        infos.add("无锡CTVPN189A020##59.43.5.160");
        infos.add("无锡CTVPN189A021##59.43.5.160");
        infos.add("无锡CTVPN189A022##59.43.5.160");
        infos.add("无锡CTVPN189A023##59.43.5.160");
        infos.add("无锡CTVPN189A025##59.43.5.160");
        infos.add("无锡CTVPN189A034##59.43.5.160");
        infos.add("无锡CTVPN189A035##59.43.5.160");
        infos.add("无锡CTVPN189A036##59.43.5.160");
        infos.add("无锡CTVPN189A039##59.43.5.160");
        infos.add("无锡CTVPN189A040##59.43.5.160");
        infos.add("无锡CTVPN189A041##59.43.5.160");
        infos.add("无锡CTVPN189A043##59.43.5.160");
        infos.add("无锡CTVPN189B001##59.43.5.161");
        infos.add("无锡CTVPN189B002##59.43.5.161");
        infos.add("无锡CTVPN189B003##59.43.5.161");
        infos.add("无锡CTVPN189B006##59.43.5.161");
        infos.add("无锡CTVPN189B007##59.43.5.161");
        infos.add("无锡CTVPN189B008##59.43.5.161");
        infos.add("无锡CTVPN189B009##59.43.5.161");
        infos.add("无锡CTVPN189B011##59.43.5.161");
        infos.add("无锡CTVPN189B012##59.43.5.161");
        infos.add("无锡CTVPN189B015##59.43.5.161");
        infos.add("无锡CTVPN189B017##59.43.5.161");
        infos.add("无锡CTVPN189B021##59.43.5.161");
        infos.add("无锡CTVPN189B022##59.43.5.161");
        infos.add("无锡CTVPN189B025##59.43.5.161");
        infos.add("无锡CTVPN189B026##59.43.5.161");
        infos.add("无锡CTVPN189B028##59.43.5.161");
        infos.add("无锡CTVPN189B029##59.43.5.161");
        infos.add("无锡CTVPN189B032##59.43.5.161");
        infos.add("无锡CTVPN189B033##59.43.5.161");
        infos.add("无锡CTVPN189B034##59.43.5.161");
        infos.add("无锡CTVPN189B037##59.43.5.161");
        infos.add("无锡CTVPN189B038##59.43.5.161");
        infos.add("无锡CTVPN189G045##59.43.5.160");
        infos.add("无锡CTVPN189G047##59.43.5.160");
        infos.add("无锡CTVPN189G049##59.43.5.160");
        infos.add("无锡CTVPN189G050##59.43.5.160");
        infos.add("无锡CTVPN189G051##59.43.5.160");
        infos.add("无锡CTVPN189G052##59.43.5.160");
        infos.add("无锡CTVPN189H039##59.43.5.161");
        infos.add("无锡CTVPN189H040##59.43.5.161");
        infos.add("无锡CTVPN189H045##59.43.5.161");
        infos.add("无锡CTVPN189H046##59.43.5.161");
        infos.add("无锡CTVPN189H047##59.43.5.161");
        infos.add("无锡CTVPN189H048##59.43.5.161");
        infos.add("无锡CTVPN189H050##59.43.5.161");
        infos.add("无锡CTVPN189H052##59.43.5.161");
        infos.add("无锡CTVPN189H053##59.43.5.161");
        infos.add("无锡CTVPN19429A##59.43.5.114");
        infos.add("无锡CTVPN2904C##59.43.5.161");
        infos.add("无锡CTVPN2904D##59.43.5.160");
        infos.add("无锡CTVPN2905C##59.43.5.161");
        infos.add("无锡CTVPN2905D##59.43.5.160");
        infos.add("无锡CTVPN29107A##59.43.5.160");
        infos.add("无锡CTVPN29117A##59.43.5.160");
        infos.add("无锡CTVPN29127A##59.43.5.161");
        infos.add("无锡CTVPN29127B##59.43.5.160");
        infos.add("无锡CTVPN29186A##59.43.5.114");
        infos.add("无锡CTVPN29202A##59.43.5.160");
        infos.add("无锡CTVPN29202B##59.43.5.161");
        infos.add("无锡CTVPN29215C##59.43.5.115");
        infos.add("无锡CTVPN29215D##59.43.5.114");
        infos.add("无锡CTVPN29215I##59.43.5.160");
        infos.add("无锡CTVPN29215J##59.43.5.160");
        infos.add("无锡CTVPN29215N##59.43.5.161");
        infos.add("无锡CTVPN29215O##59.43.5.161");
        infos.add("无锡CTVPN29215P##59.43.5.115");
        infos.add("无锡CTVPN29215Q##59.43.5.114");
        infos.add("无锡CTVPN29215R##59.43.5.114");
        infos.add("无锡CTVPN29215S##59.43.5.161");
        infos.add("无锡CTVPN29215T##59.43.5.161");
        infos.add("无锡CTVPN29215Y##59.43.5.115");
        infos.add("无锡CTVPN29221A##59.43.5.115");
        infos.add("无锡CTVPN29276A##59.43.5.114");
        infos.add("无锡CTVPN29290A##59.43.5.115");
        infos.add("无锡CTVPN29290B##59.43.5.114");
        infos.add("无锡CTVPN29290C##59.43.5.114");
        infos.add("无锡CTVPN29290D##59.43.5.115");
        infos.add("无锡CTVPN29290E##59.43.5.115");
        infos.add("无锡CTVPN35126A##59.43.5.114");
        infos.add("无锡CTVPN38019B##59.43.5.161");
        infos.add("无锡CTVPN38019C##59.43.5.160");
        infos.add("无锡CTVPN38029A##59.43.5.160");
        infos.add("无锡CTVPN46061A##59.43.5.115");
        infos.add("无锡CTVPN46061B##59.43.5.114");
        infos.add("无锡CTVPN52130G##59.43.5.160");
        infos.add("无锡CTVPN52492C##59.43.5.160");
        infos.add("无锡CTVPN52492D##59.43.5.160");
        infos.add("无锡CTVPN53040B##59.43.5.160, 59.43.5.161");
        infos.add("无锡CTVPN53040K##59.43.5.114");
        infos.add("无锡CTVPN53083B##59.43.5.114");
        infos.add("无锡CTVPN53220A##59.43.5.161");
        infos.add("无锡CTVPN53615A##59.43.5.161");
        infos.add("无锡CTVPN53615C##59.43.5.161");
        infos.add("无锡CTVPN54295A##59.43.5.160");
        infos.add("无锡CTVPN54315A##59.43.5.160");
        infos.add("无锡CTVPN54360A##59.43.5.160");
        infos.add("无锡CTVPN54617B##59.43.5.160");
        infos.add("无锡CTVPN54617C##59.43.5.160");
        infos.add("无锡CTVPN54617D##59.43.5.160");
        infos.add("无锡CTVPN54617E##59.43.5.161");
        infos.add("无锡CTVPN54617F##59.43.5.161");
        infos.add("无锡CTVPN54617H##59.43.5.161");
        infos.add("无锡CTVPN54617I##59.43.5.160");
        infos.add("无锡CTVPN54617J##59.43.5.160");
        infos.add("无锡CTVPN54617K##59.43.5.160");
        infos.add("无锡CTVPN54617M##59.43.5.114");
        infos.add("无锡CTVPN54669B##59.43.5.160");
        infos.add("无锡CTVPN54819A##59.43.5.161");
        infos.add("无锡CTVPN54819B##59.43.5.160");
        infos.add("无锡CTVPN54938B##59.43.5.115");
        infos.add("无锡CTVPN55128A##59.43.5.160");
        infos.add("无锡CTVPN55159B##59.43.5.161");
        infos.add("无锡CTVPN55159C##59.43.5.115");
        infos.add("无锡CTVPN55160A##59.43.5.160");
        infos.add("无锡CTVPN55160B##59.43.5.160");
        infos.add("无锡CTVPN55291A##59.43.5.160");
        infos.add("无锡CTVPN55388A##59.43.5.160");
        infos.add("无锡CTVPN55438A##59.43.5.160");
        infos.add("无锡CTVPN55438B##59.43.5.161");
        infos.add("无锡CTVPN55490A##59.43.5.160");
        infos.add("无锡CTVPN55697A##59.43.5.161");
        infos.add("无锡CTVPN55860A##59.43.5.160");
        infos.add("无锡CTVPN55884C##59.43.5.160");
        infos.add("无锡CTVPN56160C##59.43.5.115");
        infos.add("无锡CTVPN56160D##59.43.5.114");
        infos.add("无锡CTVPN56290B##59.43.5.161");
        infos.add("无锡CTVPN56418A##59.43.5.160");
        infos.add("无锡CTVPN56418B##59.43.5.160");
        infos.add("无锡CTVPN56905A##59.43.5.115");
        infos.add("无锡CTVPN56905B##59.43.5.114");
        infos.add("无锡CTVPN57321A##59.43.5.161");
        infos.add("无锡CTVPN57322A##59.43.5.160");
        infos.add("无锡CTVPN57842A##59.43.5.161");
        infos.add("无锡CTVPN57842B##59.43.5.160");
        infos.add("无锡CTVPN57842D##59.43.5.161");
        infos.add("无锡CTVPN57901A##59.43.5.114");
        infos.add("无锡CTVPN57901B##59.43.5.161");
        infos.add("无锡CTVPN57922C##59.43.5.161");
        infos.add("无锡CTVPN57964A##59.43.5.114");
        infos.add("无锡CTVPN58048A##59.43.5.115");
        infos.add("无锡CTVPN58157A##59.43.5.161");
        infos.add("无锡CTVPN58173A##59.43.5.114");
        infos.add("无锡CTVPN58173B##59.43.5.115");
        infos.add("无锡CTVPN58244A##59.43.5.161");
        infos.add("无锡CTVPN58256A##59.43.5.114");
        infos.add("无锡CTVPN58320A##59.43.5.114");
        infos.add("无锡CTVPN58320B##59.43.5.114");
        infos.add("无锡CTVPN58320D##59.43.5.114");
        infos.add("无锡CTVPN58320F##59.43.5.114");
        infos.add("无锡CTVPN58320G##59.43.5.114");
        infos.add("无锡CTVPN58341B##59.43.5.115");
        infos.add("无锡CTVPN58380B##59.43.5.114");
        infos.add("无锡CTVPN58426A##59.43.5.114");
        infos.add("无锡CTVPN58426B##59.43.5.115");
        infos.add("无锡CTVPN58426C##59.43.5.161");
        infos.add("无锡CTVPN58520A##59.43.5.114");
        infos.add("无锡CTVPN80000A##59.43.5.114");
        infos.add("无锡CTVPN80000B##59.43.5.115");
        infos.add("无锡CTVPN80001A##59.43.5.160");
        infos.add("无锡CTVPN80001B##59.43.5.161");
        infos.add("无锡CTVPN80002A##59.43.5.161");
        infos.add("无锡CTVPN80002B##59.43.5.160");
        infos.add("无锡CTVPN80003A##59.43.5.161");
        infos.add("无锡CTVPN80011AM##59.43.5.161");
        infos.add("无锡CTVPN80011B##59.43.5.161");
        infos.add("无锡CTVPN80011C##59.43.5.160");
        infos.add("无锡CTVPN80011D##59.43.5.160");
        infos.add("无锡CTVPN80012A##59.43.5.160");
        infos.add("无锡CTVPN80012C##59.43.5.161");
        infos.add("无锡CTVPN80012D##59.43.5.160");
        infos.add("无锡CTVPN80012G##59.43.5.160");
        infos.add("无锡CTVPN80012K##59.43.5.160");
        infos.add("无锡CTVPN80012P##59.43.5.160");
        infos.add("无锡CTVPN80012Q##59.43.5.161");
        infos.add("无锡CTVPN80012S##59.43.5.160");
        infos.add("无锡CTVPN80012T##59.43.5.114");
        infos.add("无锡CTVPN80019A##59.43.5.160");
        infos.add("无锡CTVPN80019B##59.43.5.161");
        infos.add("无锡CTVPN80031A##59.43.5.160");
        infos.add("无锡CTVPN80032A##59.43.5.161");
        infos.add("无锡CTVPN80037A##59.43.5.160");
        infos.add("无锡CTVPN80130A##59.43.5.160");
        infos.add("无锡CTVPN80130B##59.43.5.161");
        infos.add("无锡CTVPN80130C##59.43.5.114");
        infos.add("无锡CTVPN80130D##59.43.5.115");
        infos.add("无锡CTVPN80130E##59.43.5.114");
        infos.add("无锡CTVPN80130F##59.43.5.115");
        infos.add("无锡CTVPN80131A##59.43.5.160");
        infos.add("无锡CTVPN80131B##59.43.5.161");
        infos.add("无锡CTVPN80131C##59.43.5.161");
        infos.add("无锡CTVPN80131D##59.43.5.161");
        infos.add("无锡CTVPN80131E##59.43.5.160");
        infos.add("无锡CTVPN80202E##59.43.5.160");
        infos.add("无锡CTVPN80412A##59.43.5.161");
        infos.add("无锡CTVPN80441A##59.43.5.160");
        infos.add("无锡CTVPN80501A##59.43.5.160");
        infos.add("无锡CTVPN80502A##59.43.5.160");
        infos.add("泰州CTVPN1107A##59.43.0.77");
        infos.add("泰州CTVPN1107B##59.43.2.169");
        infos.add("泰州CTVPN1107C##59.43.2.169");
        infos.add("泰州CTVPN1107D##59.43.0.77");
        infos.add("泰州CTVPN1107G##59.43.0.77");
        infos.add("泰州CTVPN1107H##59.43.2.169");
        infos.add("泰州CTVPN1158A##59.43.2.169");
        infos.add("泰州CTVPN1158B##59.43.2.169");
        infos.add("泰州CTVPN1158C##59.43.2.169");
        infos.add("泰州CTVPN1158D##59.43.2.169");
        infos.add("泰州CTVPN1158H##59.43.2.169");
        infos.add("泰州CTVPN1158I##59.43.2.169");
        infos.add("泰州CTVPN1158J##59.43.2.169");
        infos.add("泰州CTVPN1158K##59.43.2.169");
        infos.add("泰州CTVPN1158L##59.43.2.169");
        infos.add("泰州CTVPN1158M##59.43.2.169");
        infos.add("泰州CTVPN1158N##59.43.2.169");
        infos.add("泰州CTVPN13134A##59.43.0.77");
        infos.add("泰州CTVPN189A001##59.43.0.77");
        infos.add("泰州CTVPN189A002##59.43.0.77");
        infos.add("泰州CTVPN189A003##59.43.0.77");
        infos.add("泰州CTVPN189A004##59.43.0.77");
        infos.add("泰州CTVPN189A005##59.43.0.77");
        infos.add("泰州CTVPN189A006##59.43.0.77");
        infos.add("泰州CTVPN189A007##59.43.0.77");
        infos.add("泰州CTVPN189A009##59.43.0.77");
        infos.add("泰州CTVPN189A010##59.43.0.77");
        infos.add("泰州CTVPN189A011##59.43.0.77");
        infos.add("泰州CTVPN189A012##59.43.0.77");
        infos.add("泰州CTVPN189A013##59.43.0.77");
        infos.add("泰州CTVPN189A014##59.43.0.77");
        infos.add("泰州CTVPN189A016##59.43.0.77");
        infos.add("泰州CTVPN189A017##59.43.0.77");
        infos.add("泰州CTVPN189A018##59.43.0.77");
        infos.add("泰州CTVPN189A019##59.43.0.77");
        infos.add("泰州CTVPN189A020##59.43.0.77");
        infos.add("泰州CTVPN189A021##59.43.0.77");
        infos.add("泰州CTVPN189A022##59.43.0.77");
        infos.add("泰州CTVPN189A023##59.43.0.77");
        infos.add("泰州CTVPN189A024##59.43.0.77");
        infos.add("泰州CTVPN189A025##59.43.0.77");
        infos.add("泰州CTVPN189A026##59.43.0.77");
        infos.add("泰州CTVPN189A027##59.43.0.77");
        infos.add("泰州CTVPN189A028##59.43.0.77");
        infos.add("泰州CTVPN189A029##59.43.0.77");
        infos.add("泰州CTVPN189A030##59.43.0.77");
        infos.add("泰州CTVPN189A032##59.43.0.77");
        infos.add("泰州CTVPN189B001##59.43.2.169");
        infos.add("泰州CTVPN189B002##59.43.2.169");
        infos.add("泰州CTVPN189B003##59.43.2.169");
        infos.add("泰州CTVPN189B004##59.43.2.169");
        infos.add("泰州CTVPN189B005##59.43.2.169");
        infos.add("泰州CTVPN189B007##59.43.2.169");
        infos.add("泰州CTVPN189B008##59.43.2.169");
        infos.add("泰州CTVPN189B009##59.43.2.169");
        infos.add("泰州CTVPN189B010##59.43.2.169");
        infos.add("泰州CTVPN189B011##59.43.2.169");
        infos.add("泰州CTVPN189B012##59.43.2.169");
        infos.add("泰州CTVPN189B014##59.43.2.169");
        infos.add("泰州CTVPN189B015##59.43.0.77");
        infos.add("泰州CTVPN189B016##59.43.2.169");
        infos.add("泰州CTVPN189B017##59.43.2.169");
        infos.add("泰州CTVPN189B018##59.43.2.169");
        infos.add("泰州CTVPN23085B##59.43.0.77");
        infos.add("泰州CTVPN2904C##59.43.0.77");
        infos.add("泰州CTVPN2904D##59.43.2.169");
        infos.add("泰州CTVPN2905C##59.43.0.77");
        infos.add("泰州CTVPN2905D##59.43.2.169");
        infos.add("泰州CTVPN29117A##59.43.0.77");
        infos.add("泰州CTVPN29127A##59.43.2.169");
        infos.add("泰州CTVPN29127B##59.43.0.77");
        infos.add("泰州CTVPN29186A##59.43.0.77");
        infos.add("泰州CTVPN29276A##59.43.0.77");
        infos.add("泰州CTVPN29290A##59.43.0.77");
        infos.add("泰州CTVPN29290C##59.43.0.77");
        infos.add("泰州CTVPN29290D##59.43.0.77");
        infos.add("泰州CTVPN29290E##59.43.0.77");
        infos.add("泰州CTVPN38019A##59.43.0.77");
        infos.add("泰州CTVPN38019B##59.43.0.77");
        infos.add("泰州CTVPN52492F##59.43.0.77");
        infos.add("泰州CTVPN52683B##59.43.0.77");
        infos.add("泰州CTVPN52683C##59.43.0.77");
        infos.add("泰州CTVPN53040B##59.43.0.77");
        infos.add("泰州CTVPN54183A##59.43.2.169");
        infos.add("泰州CTVPN54295A##59.43.0.77");
        infos.add("泰州CTVPN54315A##59.43.0.77");
        infos.add("泰州CTVPN54360B##59.43.2.169");
        infos.add("泰州CTVPN54617A##59.43.0.77");
        infos.add("泰州CTVPN54617B##59.43.2.169");
        infos.add("泰州CTVPN54617C##59.43.0.77");
        infos.add("泰州CTVPN54617D##59.43.0.77");
        infos.add("泰州CTVPN54819A##59.43.0.77");
        infos.add("泰州CTVPN55128A##59.43.0.77");
        infos.add("泰州CTVPN55291A##59.43.0.77");
        infos.add("泰州CTVPN55438A##59.43.2.169");
        infos.add("泰州CTVPN55438B##59.43.0.77");
        infos.add("泰州CTVPN56418A##59.43.2.169");
        infos.add("泰州CTVPN56418B##59.43.0.77");
        infos.add("泰州CTVPN56905A##59.43.2.169");
        infos.add("泰州CTVPN56905B##59.43.0.77");
        infos.add("泰州CTVPN57133A##59.43.0.77");
        infos.add("泰州CTVPN57901A##59.43.0.77");
        infos.add("泰州CTVPN57901B##59.43.0.77");
        infos.add("泰州CTVPN57901C##59.43.2.169");
        infos.add("泰州CTVPN57969A##59.43.2.169");
        infos.add("泰州CTVPN57969B##59.43.0.77");
        infos.add("泰州CTVPN58048A##59.43.0.77");
        infos.add("泰州CTVPN80011A##59.43.0.77");
        infos.add("泰州CTVPN80011B##59.43.0.77");
        infos.add("泰州CTVPN80011C##59.43.0.77");
        infos.add("泰州CTVPN80011D##59.43.0.77");
        infos.add("泰州CTVPN80011E##59.43.0.77");
        infos.add("泰州CTVPN80011L##59.43.0.77");
        infos.add("泰州CTVPN80011M##59.43.2.169");
        infos.add("泰州CTVPN80012A##59.43.0.77");
        infos.add("泰州CTVPN80012B##59.43.0.77");
        infos.add("泰州CTVPN80012C##59.43.0.77");
        infos.add("泰州CTVPN80012D##59.43.0.77");
        infos.add("泰州CTVPN80012E##59.43.0.77");
        infos.add("泰州CTVPN80012G##59.43.0.77");
        infos.add("泰州CTVPN80032A##59.43.0.77");
        infos.add("泰州CTVPN80130A##59.43.2.169");
        infos.add("泰州CTVPN80130B##59.43.0.77");
        infos.add("泰州CTVPN80130C##59.43.2.169");
        infos.add("泰州CTVPN80130D##59.43.2.169");
        infos.add("泰州CTVPN80130E##59.43.2.169");
        infos.add("泰州CTVPN80130F##59.43.2.169");
        infos.add("泰州CTVPN80130G##59.43.2.169");
        infos.add("泰州CTVPN80131A##59.43.2.169");
        infos.add("泰州CTVPN80131B##59.43.0.77");
        infos.add("泰州CTVPN80412A##59.43.0.77");
        infos.add("泰州CTVPN80441A##59.43.0.77");
        infos.add("泰州CTVPN80501A##59.43.0.77");
        infos.add("泰州CTVPN80502A##59.43.0.77");
        infos.add("淮安CTVPN1107A##59.43.0.62");
        infos.add("淮安CTVPN1107B##59.43.0.62");
        infos.add("淮安CTVPN1158A##59.43.0.62");
        infos.add("淮安CTVPN1158B##59.43.0.62");
        infos.add("淮安CTVPN1158C##59.43.0.62");
        infos.add("淮安CTVPN1158D##59.43.0.62");
        infos.add("淮安CTVPN1158E##59.43.0.62");
        infos.add("淮安CTVPN1158F##59.43.0.62");
        infos.add("淮安CTVPN1158G##59.43.0.62");
        infos.add("淮安CTVPN1158H##59.43.0.62");
        infos.add("淮安CTVPN1158I##59.43.0.62");
        infos.add("淮安CTVPN1158J##59.43.0.62");
        infos.add("淮安CTVPN1158K##59.43.0.62");
        infos.add("淮安CTVPN1158L##59.43.0.62");
        infos.add("淮安CTVPN1158M##59.43.0.62");
        infos.add("淮安CTVPN1158N##59.43.0.62");
        infos.add("淮安CTVPN13134A##59.43.2.164");
        infos.add("淮安CTVPN13134B##59.43.0.62");
        infos.add("淮安CTVPN189A001##59.43.0.62");
        infos.add("淮安CTVPN189A002##59.43.0.62");
        infos.add("淮安CTVPN189A003##59.43.0.62");
        infos.add("淮安CTVPN189A005##59.43.0.62");
        infos.add("淮安CTVPN189A006##59.43.0.62");
        infos.add("淮安CTVPN189A008##59.43.0.62");
        infos.add("淮安CTVPN189A009##59.43.0.62");
        infos.add("淮安CTVPN189A010##59.43.0.62");
        infos.add("淮安CTVPN189A011##59.43.0.62");
        infos.add("淮安CTVPN189A012##59.43.0.62");
        infos.add("淮安CTVPN189A013##59.43.0.62");
        infos.add("淮安CTVPN189A014##59.43.0.62");
        infos.add("淮安CTVPN189A016##59.43.0.62");
        infos.add("淮安CTVPN189A017##59.43.0.62");
        infos.add("淮安CTVPN189A018##59.43.0.62");
        infos.add("淮安CTVPN189A019##59.43.0.62");
        infos.add("淮安CTVPN189A020##59.43.0.62");
        infos.add("淮安CTVPN189A023##59.43.0.62");
        infos.add("淮安CTVPN189A024##59.43.0.62");
        infos.add("淮安CTVPN189A025##59.43.0.62");
        infos.add("淮安CTVPN189A026##59.43.0.62");
        infos.add("淮安CTVPN189A027##59.43.0.62");
        infos.add("淮安CTVPN189A029##59.43.0.62");
        infos.add("淮安CTVPN189A030##59.43.0.62");
        infos.add("淮安CTVPN189B002##59.43.0.62");
        infos.add("淮安CTVPN189B003##59.43.2.164");
        infos.add("淮安CTVPN189B004##59.43.2.164");
        infos.add("淮安CTVPN189B005##59.43.2.164");
        infos.add("淮安CTVPN189B006##59.43.2.164");
        infos.add("淮安CTVPN189B007##59.43.2.164");
        infos.add("淮安CTVPN189B008##59.43.2.164");
        infos.add("淮安CTVPN189B012##59.43.2.164");
        infos.add("淮安CTVPN189B013##59.43.2.164");
        infos.add("淮安CTVPN189B015##59.43.2.164");
        infos.add("淮安CTVPN189B018##59.43.2.164");
        infos.add("淮安CTVPN189B020##59.43.2.164");
        infos.add("淮安CTVPN189B022##59.43.2.164");
        infos.add("淮安CTVPN189B023##59.43.2.164");
        infos.add("淮安CTVPN189B025##59.43.2.164");
        infos.add("淮安CTVPN2904C##59.43.0.62");
        infos.add("淮安CTVPN29117A##59.43.2.164");
        infos.add("淮安CTVPN29130A##59.43.0.62");
        infos.add("淮安CTVPN29146C##59.43.0.62");
        infos.add("淮安CTVPN29146D##59.43.2.164");
        infos.add("淮安CTVPN29186A##59.43.2.164");
        infos.add("淮安CTVPN29215A##59.43.0.62");
        infos.add("淮安CTVPN29215B##59.43.2.164");
        infos.add("淮安CTVPN29215C##59.43.2.164");
        infos.add("淮安CTVPN29215D##59.43.2.164");
        infos.add("淮安CTVPN29215E##59.43.2.164");
        infos.add("淮安CTVPN29215F##59.43.2.164");
        infos.add("淮安CTVPN29215G##59.43.2.164");
        infos.add("淮安CTVPN29215H##59.43.2.164");
        infos.add("淮安CTVPN29215I##59.43.2.164");
        infos.add("淮安CTVPN29215J##59.43.2.164");
        infos.add("淮安CTVPN29215K##59.43.2.164");
        infos.add("淮安CTVPN29276A##59.43.2.164");
        infos.add("淮安CTVPN29290A##59.43.2.164");
        infos.add("淮安CTVPN29290B##59.43.2.164");
        infos.add("淮安CTVPN29290C##59.43.2.164");
        infos.add("淮安CTVPN29290D##59.43.2.164");
        infos.add("淮安CTVPN29290E##59.43.2.164");
        infos.add("淮安CTVPN29290F##59.43.2.164");
        infos.add("淮安CTVPN52492A##59.43.0.62");
        infos.add("淮安CTVPN54183A##59.43.0.62");
        infos.add("淮安CTVPN54183B##59.43.0.62");
        infos.add("淮安CTVPN54183D##59.43.2.164");
        infos.add("淮安CTVPN54617B##59.43.2.164");
        infos.add("淮安CTVPN54617D##59.43.0.62");
        infos.add("淮安CTVPN54819A##59.43.2.164");
        infos.add("淮安CTVPN55128A##59.43.2.164");
        infos.add("淮安CTVPN56905A##59.43.0.62");
        infos.add("淮安CTVPN56905B##59.43.2.164");
        infos.add("淮安CTVPN57933A##59.43.2.164, 59.43.0.62");
        infos.add("淮安CTVPN57933B##59.43.2.164");
        infos.add("淮安CTVPN57964B##59.43.2.164");
        infos.add("淮安CTVPN57964C##59.43.0.62");
        infos.add("淮安CTVPN58520A##59.43.2.164");
        infos.add("淮安CTVPN80011A##59.43.0.62");
        infos.add("淮安CTVPN80011B##59.43.0.62");
        infos.add("淮安CTVPN80011C##59.43.0.62");
        infos.add("淮安CTVPN80011D##59.43.0.62");
        infos.add("淮安CTVPN80011E##59.43.0.62");
        infos.add("淮安CTVPN80011F##59.43.0.62");
        infos.add("淮安CTVPN80011J##59.43.2.164");
        infos.add("淮安CTVPN80012D##59.43.2.164");
        infos.add("淮安CTVPN80012F##59.43.2.164");
        infos.add("淮安CTVPN80130A##59.43.0.62");
        infos.add("淮安CTVPN80130B##59.43.2.164");
        infos.add("淮安CTVPN80130C##59.43.2.164");
        infos.add("淮安CTVPN80130D##59.43.2.164");
        infos.add("淮安CTVPN80130E##59.43.2.164");
        infos.add("淮安CTVPN80130F##59.43.2.164");
        infos.add("淮安CTVPN80130G##59.43.2.164");
        infos.add("淮安CTVPN80130H##59.43.2.164");
        infos.add("淮安CTVPN80130I##59.43.2.164");
        infos.add("淮安CTVPN80130J##59.43.0.62");
        infos.add("淮安CTVPN80131A##59.43.2.164");
        infos.add("淮安CTVPN80131B##59.43.0.62");
        infos.add("淮安CTVPN80412A##59.43.0.62");
        infos.add("淮安CTVPN80441A##59.43.0.62");
        infos.add("淮安CTVPN80501A##59.43.0.62");
        infos.add("淮安CTVPN80502A##59.43.2.164");
        infos.add("盐城CTVPN1107A##59.43.0.86");
        infos.add("盐城CTVPN1107B##59.43.2.172");
        infos.add("盐城CTVPN1152A##59.43.0.86");
        infos.add("盐城CTVPN1152B##59.43.2.172");
        infos.add("盐城CTVPN1158A##59.43.0.86");
        infos.add("盐城CTVPN1158C##59.43.0.86");
        infos.add("盐城CTVPN1158D##59.43.0.86");
        infos.add("盐城CTVPN1158E##59.43.0.86");
        infos.add("盐城CTVPN1158F##59.43.0.86");
        infos.add("盐城CTVPN1158G##59.43.0.86");
        infos.add("盐城CTVPN1158H##59.43.0.86");
        infos.add("盐城CTVPN1158I##59.43.0.86");
        infos.add("盐城CTVPN1158J##59.43.0.86");
        infos.add("盐城CTVPN1158K##59.43.0.86");
        infos.add("盐城CTVPN1158L##59.43.0.86");
        infos.add("盐城CTVPN1158M##59.43.0.86");
        infos.add("盐城CTVPN1158N##59.43.0.86");
        infos.add("盐城CTVPN13134A##59.43.0.86");
        infos.add("盐城CTVPN189A002##59.43.0.86");
        infos.add("盐城CTVPN189A003##59.43.0.86");
        infos.add("盐城CTVPN189A004##59.43.0.86");
        infos.add("盐城CTVPN189A005##59.43.0.86");
        infos.add("盐城CTVPN189A006##59.43.0.86");
        infos.add("盐城CTVPN189A007##59.43.0.86");
        infos.add("盐城CTVPN189A010##59.43.0.86");
        infos.add("盐城CTVPN189A011##59.43.0.86");
        infos.add("盐城CTVPN189B003##59.43.2.172");
        infos.add("盐城CTVPN189B004##59.43.2.172");
        infos.add("盐城CTVPN189B006##59.43.2.172");
        infos.add("盐城CTVPN189B007##59.43.2.172");
        infos.add("盐城CTVPN189B008##59.43.2.172");
        infos.add("盐城CTVPN2904C##59.43.0.86");
        infos.add("盐城CTVPN2904D##59.43.2.172");
        infos.add("盐城CTVPN2905C##59.43.0.86");
        infos.add("盐城CTVPN2905D##59.43.2.172");
        infos.add("盐城CTVPN29117A##59.43.2.172");
        infos.add("盐城CTVPN29127A##59.43.2.172");
        infos.add("盐城CTVPN29127B##59.43.0.86");
        infos.add("盐城CTVPN29204A##59.43.0.86");
        infos.add("盐城CTVPN29276A##59.43.0.86");
        infos.add("盐城CTVPN29290A##59.43.0.86");
        infos.add("盐城CTVPN29290B##59.43.0.86");
        infos.add("盐城CTVPN52492A##59.43.0.86");
        infos.add("盐城CTVPN53040A##59.43.0.86");
        infos.add("盐城CTVPN53040B##59.43.2.172");
        infos.add("盐城CTVPN53040C##59.43.0.86");
        infos.add("盐城CTVPN53040D##59.43.0.86");
        infos.add("盐城CTVPN54183A##59.43.0.86");
        infos.add("盐城CTVPN54360B##59.43.0.86");
        infos.add("盐城CTVPN54617A##59.43.2.172");
        infos.add("盐城CTVPN54617B##59.43.2.172");
        infos.add("盐城CTVPN54819A##59.43.0.86");
        infos.add("盐城CTVPN55128A##59.43.0.86");
        infos.add("盐城CTVPN55159A##59.43.0.86");
        infos.add("盐城CTVPN55291A##59.43.0.86");
        infos.add("盐城CTVPN55490A##59.43.0.86");
        infos.add("盐城CTVPN56905A##59.43.2.172");
        infos.add("盐城CTVPN56905B##59.43.0.86");
        infos.add("盐城CTVPN57901A##59.43.0.86");
        infos.add("盐城CTVPN57901B##59.43.0.86");
        infos.add("盐城CTVPN57922A##59.43.0.86");
        infos.add("盐城CTVPN57922B##59.43.2.172");
        infos.add("盐城CTVPN57966A##59.43.0.86");
        infos.add("盐城CTVPN57966B##59.43.2.172");
        infos.add("盐城CTVPN58004A##59.43.0.86");
        infos.add("盐城CTVPN58048A##59.43.0.86");
        infos.add("盐城CTVPN58256B##59.43.2.172");
        infos.add("盐城CTVPN80011A##59.43.0.86");
        infos.add("盐城CTVPN80011B##59.43.0.86");
        infos.add("盐城CTVPN80011C##59.43.0.86");
        infos.add("盐城CTVPN80011F##59.43.0.86");
        infos.add("盐城CTVPN80011G##59.43.0.86");
        infos.add("盐城CTVPN80011I##59.43.0.86");
        infos.add("盐城CTVPN80011J##59.43.0.86");
        infos.add("盐城CTVPN80011N##59.43.0.86");
        infos.add("盐城CTVPN80012A##59.43.0.86");
        infos.add("盐城CTVPN80012B##59.43.0.86");
        infos.add("盐城CTVPN80012C##59.43.0.86");
        infos.add("盐城CTVPN80012D##59.43.0.86");
        infos.add("盐城CTVPN80012E##59.43.0.86");
        infos.add("盐城CTVPN80012F##59.43.0.86");
        infos.add("盐城CTVPN80012H##59.43.0.86");
        infos.add("盐城CTVPN80012I##59.43.0.86");
        infos.add("盐城CTVPN80012K##59.43.0.86");
        infos.add("盐城CTVPN80012M##59.43.0.86");
        infos.add("盐城CTVPN80012N##59.43.2.172");
        infos.add("盐城CTVPN80012O##59.43.0.86");
        infos.add("盐城CTVPN80012P##59.43.0.86");
        infos.add("盐城CTVPN80012R##59.43.0.86");
        infos.add("盐城CTVPN80031A##59.43.0.86");
        infos.add("盐城CTVPN80032A##59.43.0.86");
        infos.add("盐城CTVPN80130A##59.43.2.172");
        infos.add("盐城CTVPN80130B##59.43.0.86");
        infos.add("盐城CTVPN80130C##59.43.0.86");
        infos.add("盐城CTVPN80130D##59.43.0.86");
        infos.add("盐城CTVPN80130G##59.43.0.86");
        infos.add("盐城CTVPN80130H##59.43.0.86");
        infos.add("盐城CTVPN80131A##59.43.0.86");
        infos.add("盐城CTVPN80131B##59.43.2.172");
        infos.add("盐城CTVPN80412A##59.43.0.86");
        infos.add("盐城CTVPN80501A##59.43.0.86");
        infos.add("盐城CTVPN80502A##59.43.0.86");
        infos.add("苏州CTVPN1001A##59.43.5.138");
        infos.add("苏州CTVPN1001B##59.43.5.14");
        infos.add("苏州CTVPN1002AC##59.43.5.13");
        infos.add("苏州CTVPN1002AD##59.43.5.14");
        infos.add("苏州CTVPN10537B##59.43.5.13");
        infos.add("苏州CTVPN10670A##59.43.5.14");
        infos.add("苏州CTVPN10827A##59.43.5.137");
        infos.add("苏州CTVPN1107C##59.43.5.138");
        infos.add("苏州CTVPN1107D##59.43.5.137");
        infos.add("苏州CTVPN1107E##59.43.5.138");
        infos.add("苏州CTVPN1107F##59.43.5.14");
        infos.add("苏州CTVPN1107G##59.43.5.13");
        infos.add("苏州CTVPN1107H##59.43.5.14");
        infos.add("苏州CTVPN1107I##59.43.5.13");
        infos.add("苏州CTVPN1107J##59.43.5.14");
        infos.add("苏州CTVPN1107K##59.43.6.78");
        infos.add("苏州CTVPN1107L##59.43.6.78");
        infos.add("苏州CTVPN1107M##59.43.6.77");
        infos.add("苏州CTVPN1107N##59.43.6.77");
        infos.add("苏州CTVPN1107O##59.43.5.14");
        infos.add("苏州CTVPN1107P##59.43.5.13");
        infos.add("苏州CTVPN11145A##59.43.5.13");
        infos.add("苏州CTVPN1124A##59.43.5.138");
        infos.add("苏州CTVPN1124B##59.43.5.137");
        infos.add("苏州CTVPN1124C##59.43.5.14");
        infos.add("苏州CTVPN1124D##59.43.5.13");
        infos.add("苏州CTVPN1124E##59.43.5.13");
        infos.add("苏州CTVPN1124F##59.43.5.14");
        infos.add("苏州CTVPN1124G##59.43.5.14");
        infos.add("苏州CTVPN1124H##59.43.5.13");
        infos.add("苏州CTVPN1124I##59.43.5.13");
        infos.add("苏州CTVPN1124J##59.43.5.14");
        infos.add("苏州CTVPN1124K##59.43.5.137");
        infos.add("苏州CTVPN1124L##59.43.5.138");
        infos.add("苏州CTVPN11258D##59.43.5.138");
        infos.add("苏州CTVPN11304C##59.43.5.14");
        infos.add("苏州CTVPN11304D##59.43.5.13");
        infos.add("苏州CTVPN11304E##59.43.5.138");
        infos.add("苏州CTVPN11391B##59.43.5.137");
        infos.add("苏州CTVPN11494C##59.43.5.14");
        infos.add("苏州CTVPN1152D##59.43.6.78");
        infos.add("苏州CTVPN1157A##59.43.5.14");
        infos.add("苏州CTVPN1157B##59.43.5.13");
        infos.add("苏州CTVPN1158A##59.43.5.13");
        infos.add("苏州CTVPN1158AG##59.43.5.13");
        infos.add("苏州CTVPN1158AH##59.43.5.13");
        infos.add("苏州CTVPN1158AI##59.43.5.137");
        infos.add("苏州CTVPN1158AJ##59.43.5.13");
        infos.add("苏州CTVPN1158AK##59.43.5.13");
        infos.add("苏州CTVPN1158AL##59.43.5.137");
        infos.add("苏州CTVPN1158AM##59.43.5.13");
        infos.add("苏州CTVPN1158AN##59.43.5.137");
        infos.add("苏州CTVPN1158AO##59.43.5.137");
        infos.add("苏州CTVPN1158AP##59.43.5.137");
        infos.add("苏州CTVPN1158AQ##59.43.5.137");
        infos.add("苏州CTVPN1158AR##59.43.5.137");
        infos.add("苏州CTVPN1158AS##59.43.5.137");
        infos.add("苏州CTVPN1158B##59.43.5.13");
        infos.add("苏州CTVPN1158C##59.43.5.137");
        infos.add("苏州CTVPN1158D##59.43.5.137");
        infos.add("苏州CTVPN1158E##59.43.5.13");
        infos.add("苏州CTVPN1158F##59.43.5.13");
        infos.add("苏州CTVPN1158G##59.43.5.13");
        infos.add("苏州CTVPN1158H##59.43.5.137");
        infos.add("苏州CTVPN1158I##59.43.5.137");
        infos.add("苏州CTVPN1158J##59.43.5.13");
        infos.add("苏州CTVPN1158K##59.43.5.13");
        infos.add("苏州CTVPN1158L##59.43.5.137");
        infos.add("苏州CTVPN1158M##59.43.5.137");
        infos.add("苏州CTVPN1158N##59.43.5.137");
        infos.add("苏州CTVPN1158O##59.43.5.13");
        infos.add("苏州CTVPN1158P##59.43.5.137");
        infos.add("苏州CTVPN1158Q##59.43.5.13");
        infos.add("苏州CTVPN1158R##59.43.5.13");
        infos.add("苏州CTVPN1158S##59.43.5.13");
        infos.add("苏州CTVPN1158T##59.43.5.137");
        infos.add("苏州CTVPN1158U##59.43.5.137");
        infos.add("苏州CTVPN11597A##59.43.5.137");
        infos.add("苏州CTVPN11597C##59.43.5.14");
        infos.add("苏州CTVPN11597D##59.43.5.137");
        infos.add("苏州CTVPN11731A##59.43.5.14");
        infos.add("苏州CTVPN11759A##59.43.5.137");
        infos.add("苏州CTVPN11759C##59.43.5.137");
        infos.add("苏州CTVPN13134B##59.43.5.14");
        infos.add("苏州CTVPN13134C##59.43.5.13");
        infos.add("苏州CTVPN18236A##59.43.5.14");
        infos.add("苏州CTVPN18236B##59.43.5.13");
        infos.add("苏州CTVPN18236D##59.43.5.13");
        infos.add("苏州CTVPN18236F##59.43.5.138");
        infos.add("苏州CTVPN18921A##59.43.5.14");
        infos.add("苏州CTVPN18921B##59.43.5.13");
        infos.add("苏州CTVPN189A001##59.43.5.137");
        infos.add("苏州CTVPN189A002##59.43.5.137");
        infos.add("苏州CTVPN189A005##59.43.5.137");
        infos.add("苏州CTVPN189A007##59.43.5.137");
        infos.add("苏州CTVPN189A008##59.43.5.137");
        infos.add("苏州CTVPN189A010##59.43.5.137");
        infos.add("苏州CTVPN189A013##59.43.5.137");
        infos.add("苏州CTVPN189A014##59.43.5.137");
        infos.add("苏州CTVPN189A015##59.43.5.137");
        infos.add("苏州CTVPN189A017##59.43.5.137");
        infos.add("苏州CTVPN189A018##59.43.5.137");
        infos.add("苏州CTVPN189A019##59.43.5.137");
        infos.add("苏州CTVPN189A020##59.43.5.137");
        infos.add("苏州CTVPN189A021##59.43.5.137");
        infos.add("苏州CTVPN189A023##59.43.5.137");
        infos.add("苏州CTVPN189A024##59.43.5.137");
        infos.add("苏州CTVPN189A025##59.43.5.137");
        infos.add("苏州CTVPN189A027##59.43.5.137");
        infos.add("苏州CTVPN189A033##59.43.5.137");
        infos.add("苏州CTVPN189A034##59.43.5.137");
        infos.add("苏州CTVPN189A038##59.43.5.137");
        infos.add("苏州CTVPN189A039##59.43.5.137");
        infos.add("苏州CTVPN189A043##59.43.5.137");
        infos.add("苏州CTVPN189A045##59.43.5.137");
        infos.add("苏州CTVPN189A046##59.43.5.137");
        infos.add("苏州CTVPN189A048##59.43.5.137");
        infos.add("苏州CTVPN189A050##59.43.5.137");
        infos.add("苏州CTVPN189A051##59.43.5.137");
        infos.add("苏州CTVPN189A053##59.43.5.137");
        infos.add("苏州CTVPN189A058##59.43.5.137");
        infos.add("苏州CTVPN189A060##59.43.5.137");
        infos.add("苏州CTVPN189A061##59.43.5.137");
        infos.add("苏州CTVPN189B002##59.43.5.13");
        infos.add("苏州CTVPN189B003##59.43.5.13");
        infos.add("苏州CTVPN189B004##59.43.5.13");
        infos.add("苏州CTVPN189B005##59.43.5.13");
        infos.add("苏州CTVPN189B007##59.43.5.13");
        infos.add("苏州CTVPN189B008##59.43.5.13");
        infos.add("苏州CTVPN189B012##59.43.5.13");
        infos.add("苏州CTVPN189B014##59.43.5.13");
        infos.add("苏州CTVPN189B015##59.43.5.13");
        infos.add("苏州CTVPN189B016##59.43.5.14");
        infos.add("苏州CTVPN189B018##59.43.5.14");
        infos.add("苏州CTVPN189B020##59.43.5.14");
        infos.add("苏州CTVPN189B026##59.43.5.14");
        infos.add("苏州CTVPN189B027##59.43.5.14");
        infos.add("苏州CTVPN189B028##59.43.5.13");
        infos.add("苏州CTVPN189B031##59.43.5.14");
        infos.add("苏州CTVPN189B035##59.43.5.14");
        infos.add("苏州CTVPN189B037##59.43.5.14");
        infos.add("苏州CTVPN189B039##59.43.5.14");
        infos.add("苏州CTVPN189B040##59.43.5.14");
        infos.add("苏州CTVPN189B043##59.43.5.14");
        infos.add("苏州CTVPN189B047##59.43.5.13");
        infos.add("苏州CTVPN189B048##59.43.5.13");
        infos.add("苏州CTVPN189B049##59.43.5.13");
        infos.add("苏州CTVPN189B050##59.43.5.13");
        infos.add("苏州CTVPN189B051##59.43.5.13");
        infos.add("苏州CTVPN189B052##59.43.5.13");
        infos.add("苏州CTVPN189B053##59.43.5.14");
        infos.add("苏州CTVPN189C001##59.43.5.14");
        infos.add("苏州CTVPN189C054##59.43.5.14");
        infos.add("苏州CTVPN189C055##59.43.5.14");
        infos.add("苏州CTVPN189C056##59.43.5.14");
        infos.add("苏州CTVPN189C057##59.43.5.14");
        infos.add("苏州CTVPN189C058##59.43.5.14");
        infos.add("苏州CTVPN189C060##59.43.5.14");
        infos.add("苏州CTVPN189D001##59.43.5.13");
        infos.add("苏州CTVPN189D002##59.43.5.13");
        infos.add("苏州CTVPN189D003##59.43.5.13");
        infos.add("苏州CTVPN189D053##59.43.5.13");
        infos.add("苏州CTVPN189D054##59.43.5.13");
        infos.add("苏州CTVPN189D055##59.43.5.13");
        infos.add("苏州CTVPN189D056##59.43.5.13");
        infos.add("苏州CTVPN189D059##59.43.5.13");
        infos.add("苏州CTVPN189D064##59.43.5.13");
        infos.add("苏州CTVPN189D065##59.43.5.13");
        infos.add("苏州CTVPN189E001##59.43.5.137");
        infos.add("苏州CTVPN189E002##59.43.5.137");
        infos.add("苏州CTVPN189E004##59.43.5.137");
        infos.add("苏州CTVPN189E005##59.43.5.137");
        infos.add("苏州CTVPN189E006##59.43.5.137");
        infos.add("苏州CTVPN189E063##59.43.5.137");
        infos.add("苏州CTVPN189E064##59.43.5.137");
        infos.add("苏州CTVPN189E066##59.43.5.137");
        infos.add("苏州CTVPN189E067##59.43.5.137");
        infos.add("苏州CTVPN189E068##59.43.5.137");
        infos.add("苏州CTVPN189E069##59.43.5.137");
        infos.add("苏州CTVPN189F001##59.43.5.138");
        infos.add("苏州CTVPN189F003##59.43.5.138");
        infos.add("苏州CTVPN189F008##59.43.5.138");
        infos.add("苏州CTVPN189F010##59.43.5.138");
        infos.add("苏州CTVPN189F054##59.43.5.138");
        infos.add("苏州CTVPN189F056##59.43.5.138");
        infos.add("苏州CTVPN19429A##59.43.5.14");
        infos.add("苏州CTVPN196A##59.43.5.137");
        infos.add("苏州CTVPN196B##59.43.5.138");
        infos.add("苏州CTVPN196C##59.43.5.14");
        infos.add("苏州CTVPN200A##59.43.5.138");
        infos.add("苏州CTVPN200B##59.43.5.14");
        infos.add("苏州CTVPN206A##59.43.5.14");
        infos.add("苏州CTVPN29013A##59.43.5.138");
        infos.add("苏州CTVPN29037A##59.43.5.138");
        infos.add("苏州CTVPN29037B##59.43.5.137");
        infos.add("苏州CTVPN2904D##59.43.5.138");
        infos.add("苏州CTVPN2905C##59.43.5.137");
        infos.add("苏州CTVPN2905D##59.43.5.138");
        infos.add("苏州CTVPN29107A##59.43.5.137");
        infos.add("苏州CTVPN29109A##59.43.5.138");
        infos.add("苏州CTVPN29117A##59.43.5.138");
        infos.add("苏州CTVPN29127A##59.43.5.137");
        infos.add("苏州CTVPN29127B##59.43.5.138");
        infos.add("苏州CTVPN29129A##59.43.5.137");
        infos.add("苏州CTVPN29136A##59.43.5.13");
        infos.add("苏州CTVPN29136B##59.43.5.14");
        infos.add("苏州CTVPN29140A##59.43.5.138");
        infos.add("苏州CTVPN29140B##59.43.5.137");
        infos.add("苏州CTVPN29171D##59.43.5.138");
        infos.add("苏州CTVPN29171E##59.43.5.137");
        infos.add("苏州CTVPN29171F##59.43.5.137");
        infos.add("苏州CTVPN29171G##59.43.5.14");
        infos.add("苏州CTVPN29186B##59.43.5.14");
        infos.add("苏州CTVPN29186C##59.43.5.14");
        infos.add("苏州CTVPN29186D##59.43.5.13");
        infos.add("苏州CTVPN29186E##59.43.5.13");
        infos.add("苏州CTVPN29186F##59.43.5.14");
        infos.add("苏州CTVPN29186G##59.43.5.13");
        infos.add("苏州CTVPN29186H##59.43.5.14");
        infos.add("苏州CTVPN29186I##59.43.5.13");
        infos.add("苏州CTVPN29186J##59.43.5.14");
        infos.add("苏州CTVPN29186K##59.43.5.138");
        infos.add("苏州CTVPN29186L##59.43.5.13");
        infos.add("苏州CTVPN29186M##59.43.5.14");
        infos.add("苏州CTVPN29204A##59.43.5.14");
        infos.add("苏州CTVPN29204B##59.43.5.13");
        infos.add("苏州CTVPN29276A##59.43.6.78");
        infos.add("苏州CTVPN29276B##59.43.5.14");
        infos.add("苏州CTVPN29276C##59.43.5.13");
        infos.add("苏州CTVPN29290A##59.43.5.14");
        infos.add("苏州CTVPN29290B##59.43.5.14");
        infos.add("苏州CTVPN29290C##59.43.5.14");
        infos.add("苏州CTVPN29290D##59.43.5.14");
        infos.add("苏州CTVPN38003A##59.43.5.138");
        infos.add("苏州CTVPN38003C##59.43.5.13");
        infos.add("苏州CTVPN38003D##59.43.5.14");
        infos.add("苏州CTVPN38003H##59.43.5.13");
        infos.add("苏州CTVPN38003K##59.43.5.14");
        infos.add("苏州CTVPN38003L##59.43.5.14");
        infos.add("苏州CTVPN38003M##59.43.5.137");
        infos.add("苏州CTVPN38003O##59.43.5.13");
        infos.add("苏州CTVPN38003P##59.43.5.14");
        infos.add("苏州CTVPN38003Q##59.43.5.13");
        infos.add("苏州CTVPN38003R##59.43.5.13");
        infos.add("苏州CTVPN38019A##59.43.5.14");
        infos.add("苏州CTVPN38019B##59.43.5.137");
        infos.add("苏州CTVPN38029A##59.43.5.13");
        infos.add("苏州CTVPN38037A##59.43.5.137");
        infos.add("苏州CTVPN38037B##59.43.5.13");
        infos.add("苏州CTVPN38037C##59.43.5.138");
        infos.add("苏州CTVPN46062A##59.43.5.14");
        infos.add("苏州CTVPN46062B##59.43.5.13");
        infos.add("苏州CTVPN52013B##59.43.5.138");
        infos.add("苏州CTVPN52040C##59.43.5.137");
        infos.add("苏州CTVPN52130F##59.43.5.137");
        infos.add("苏州CTVPN52130I##59.43.5.137");
        infos.add("苏州CTVPN52335B##59.43.5.138");
        infos.add("苏州CTVPN52353N##59.43.6.77");
        infos.add("苏州CTVPN52353P##59.43.5.138");
        infos.add("苏州CTVPN52353S##59.43.5.138");
        infos.add("苏州CTVPN52353T##59.43.5.137");
        infos.add("苏州CTVPN52353V##59.43.5.138");
        infos.add("苏州CTVPN52492A##59.43.5.14");
        infos.add("苏州CTVPN52542A##59.43.5.138");
        infos.add("苏州CTVPN52599C##59.43.5.14");
        infos.add("苏州CTVPN52599D##59.43.5.137");
        infos.add("苏州CTVPN52599E##59.43.5.13");
        infos.add("苏州CTVPN52599G##59.43.5.138");
        infos.add("苏州CTVPN52648F##59.43.5.13");
        infos.add("苏州CTVPN52648G##59.43.5.13");
        infos.add("苏州CTVPN52683A##59.43.5.13");
        infos.add("苏州CTVPN52683F##59.43.5.137");
        infos.add("苏州CTVPN52962A##59.43.5.137");
        infos.add("苏州CTVPN53022A##59.43.5.13");
        infos.add("苏州CTVPN53040B##59.43.5.13");
        infos.add("苏州CTVPN53040C##59.43.5.14");
        infos.add("苏州CTVPN53040J##59.43.5.14");
        infos.add("苏州CTVPN53040K##59.43.5.14");
        infos.add("苏州CTVPN53040L##59.43.5.14");
        infos.add("苏州CTVPN53061A##59.43.5.138");
        infos.add("苏州CTVPN53061E##59.43.5.138");
        infos.add("苏州CTVPN53174A##59.43.5.137");
        infos.add("苏州CTVPN53220A##59.43.5.14");
        infos.add("苏州CTVPN53220B##59.43.5.13");
        infos.add("苏州CTVPN53282A##59.43.5.13");
        infos.add("苏州CTVPN53615A##59.43.5.14");
        infos.add("苏州CTVPN53806A##59.43.5.137");
        infos.add("苏州CTVPN53889A##59.43.5.137");
        infos.add("苏州CTVPN53931A##59.43.5.14");
        infos.add("苏州CTVPN54190B##59.43.5.138");
        infos.add("苏州CTVPN54190F##59.43.5.138");
        infos.add("苏州CTVPN54217C##59.43.5.137");
        infos.add("苏州CTVPN54217D##59.43.5.13");
        infos.add("苏州CTVPN54217E##59.43.5.14");
        infos.add("苏州CTVPN54295A##59.43.5.138");
        infos.add("苏州CTVPN54315A##59.43.5.138");
        infos.add("苏州CTVPN54350A##59.43.5.14");
        infos.add("苏州CTVPN54360B##59.43.5.138");
        infos.add("苏州CTVPN54360C##59.43.5.137");
        infos.add("苏州CTVPN54360D##59.43.5.13");
        infos.add("苏州CTVPN54360E##59.43.5.13");
        infos.add("苏州CTVPN54399A##59.43.5.14");
        infos.add("苏州CTVPN54452C##59.43.5.138");
        infos.add("苏州CTVPN54524A##59.43.5.138");
        infos.add("苏州CTVPN54524B##59.43.5.137");
        infos.add("苏州CTVPN54524C##59.43.5.138");
        infos.add("苏州CTVPN54617A##59.43.5.138");
        infos.add("苏州CTVPN54617B##59.43.5.138");
        infos.add("苏州CTVPN54617C##59.43.5.138");
        infos.add("苏州CTVPN54617D##59.43.5.138");
        infos.add("苏州CTVPN54617E##59.43.5.137");
        infos.add("苏州CTVPN54617F##59.43.5.138");
        infos.add("苏州CTVPN54617G##59.43.5.138");
        infos.add("苏州CTVPN54617H##59.43.5.13");
        infos.add("苏州CTVPN54617I##59.43.5.138");
        infos.add("苏州CTVPN54617J##59.43.5.137");
        infos.add("苏州CTVPN54617L##59.43.5.137");
        infos.add("苏州CTVPN54617M##59.43.5.138");
        infos.add("苏州CTVPN54617N##59.43.5.138");
        infos.add("苏州CTVPN54617O##59.43.5.137");
        infos.add("苏州CTVPN54617P##59.43.5.14");
        infos.add("苏州CTVPN54617Q##59.43.5.14");
        infos.add("苏州CTVPN54617R##59.43.5.13");
        infos.add("苏州CTVPN54617S##59.43.5.14");
        infos.add("苏州CTVPN54819A##59.43.5.137");
        infos.add("苏州CTVPN54819B##59.43.5.138");
        infos.add("苏州CTVPN54819C##59.43.5.137");
        infos.add("苏州CTVPN54819D##59.43.5.138");
        infos.add("苏州CTVPN54819F##59.43.5.138");
        infos.add("苏州CTVPN54819G##59.43.5.13");
        infos.add("苏州CTVPN54918A##59.43.5.14");
        infos.add("苏州CTVPN54938A##59.43.5.138");
        infos.add("苏州CTVPN54938B##59.43.5.138");
        infos.add("苏州CTVPN55065C##59.43.5.138");
        infos.add("苏州CTVPN55128A##59.43.5.138");
        infos.add("苏州CTVPN55159B##59.43.5.137");
        infos.add("苏州CTVPN55159C##59.43.5.138");
        infos.add("苏州CTVPN55160B##59.43.5.138");
        infos.add("苏州CTVPN55160C##59.43.5.138");
        infos.add("苏州CTVPN55160D##59.43.5.137");
        infos.add("苏州CTVPN55160E##59.43.5.13");
        infos.add("苏州CTVPN55160G##59.43.5.13");
        infos.add("苏州CTVPN55160H##59.43.5.137");
        infos.add("苏州CTVPN55160I##59.43.5.14");
        infos.add("苏州CTVPN55160J##59.43.5.13");
        infos.add("苏州CTVPN55217A##59.43.5.138");
        infos.add("苏州CTVPN55279A##59.43.5.138");
        infos.add("苏州CTVPN55291B##59.43.5.137");
        infos.add("苏州CTVPN55291D##59.43.5.138");
        infos.add("苏州CTVPN55438C##59.43.5.138");
        infos.add("苏州CTVPN55438D##59.43.5.137");
        infos.add("苏州CTVPN55438E##59.43.5.14");
        infos.add("苏州CTVPN55438F##59.43.5.13");
        infos.add("苏州CTVPN55438G##59.43.5.14");
        infos.add("苏州CTVPN55438H##59.43.5.13");
        infos.add("苏州CTVPN55439B##59.43.5.13");
        infos.add("苏州CTVPN55443A##59.43.5.138");
        infos.add("苏州CTVPN55471A##59.43.5.137");
        infos.add("苏州CTVPN55490A##59.43.5.138");
        infos.add("苏州CTVPN55537A##59.43.5.138");
        infos.add("苏州CTVPN55594A##59.43.5.13");
        infos.add("苏州CTVPN55690A##59.43.5.14");
        infos.add("苏州CTVPN55690D##59.43.5.13");
        infos.add("苏州CTVPN55690E##59.43.5.14");
        infos.add("苏州CTVPN55690F##59.43.5.14");
        infos.add("苏州CTVPN55824A##59.43.5.13");
        infos.add("苏州CTVPN55824C##59.43.5.13");
        infos.add("苏州CTVPN55884A##59.43.5.13");
        infos.add("苏州CTVPN55884L##59.43.5.13");
        infos.add("苏州CTVPN55884M##59.43.5.14");
        infos.add("苏州CTVPN55954A##59.43.5.13");
        infos.add("苏州CTVPN55956A##59.43.5.14");
        infos.add("苏州CTVPN55982A##59.43.5.14");
        infos.add("苏州CTVPN56012B##59.43.5.14");
        infos.add("苏州CTVPN56024A##59.43.5.138");
        infos.add("苏州CTVPN56060A##59.43.5.14");
        infos.add("苏州CTVPN56060B##59.43.5.13");
        infos.add("苏州CTVPN56074A##59.43.5.14");
        infos.add("苏州CTVPN56105A##59.43.5.13");
        infos.add("苏州CTVPN56105B##59.43.5.14");
        infos.add("苏州CTVPN56108A##59.43.5.14");
        infos.add("苏州CTVPN56109A##59.43.5.14");
        infos.add("苏州CTVPN56148E##59.43.5.13");
        infos.add("苏州CTVPN56148F##59.43.5.13");
        infos.add("苏州CTVPN56148G##59.43.5.14");
        infos.add("苏州CTVPN56148H##59.43.5.14");
        infos.add("苏州CTVPN56149E##59.43.5.13");
        infos.add("苏州CTVPN56149F##59.43.5.14");
        infos.add("苏州CTVPN56149G##59.43.5.13");
        infos.add("苏州CTVPN56149H##59.43.5.14");
        infos.add("苏州CTVPN56150E##59.43.5.13");
        infos.add("苏州CTVPN56150F##59.43.5.14");
        infos.add("苏州CTVPN56150G##59.43.5.14");
        infos.add("苏州CTVPN56150H##59.43.5.13");
        infos.add("苏州CTVPN56151E##59.43.5.13");
        infos.add("苏州CTVPN56151F##59.43.5.14");
        infos.add("苏州CTVPN56151G##59.43.5.13");
        infos.add("苏州CTVPN56151H##59.43.5.14");
        infos.add("苏州CTVPN56157A##59.43.5.138");
        infos.add("苏州CTVPN56157B##59.43.5.137");
        infos.add("苏州CTVPN56160A##59.43.5.138");
        infos.add("苏州CTVPN56160B##59.43.5.137");
        infos.add("苏州CTVPN56188A##59.43.5.13");
        infos.add("苏州CTVPN56233A##59.43.5.14");
        infos.add("苏州CTVPN56282A##59.43.5.14");
        infos.add("苏州CTVPN56297A##59.43.5.14");
        infos.add("苏州CTVPN56398A##59.43.5.13");
        infos.add("苏州CTVPN56398B##59.43.5.14");
        infos.add("苏州CTVPN56398C##59.43.5.137");
        infos.add("苏州CTVPN56412A##59.43.5.14");
        infos.add("苏州CTVPN56412B##59.43.5.13");
        infos.add("苏州CTVPN56418A##59.43.5.14");
        infos.add("苏州CTVPN56418D##59.43.5.137");
        infos.add("苏州CTVPN56418E##59.43.5.138");
        infos.add("苏州CTVPN56418F##59.43.5.14");
        infos.add("苏州CTVPN56419A##59.43.5.14");
        infos.add("苏州CTVPN56905A##59.43.5.13");
        infos.add("苏州CTVPN56905B##59.43.5.137");
        infos.add("苏州CTVPN57322A##59.43.5.137");
        infos.add("苏州CTVPN57322B##59.43.5.138");
        infos.add("苏州CTVPN57825A##59.43.5.14");
        infos.add("苏州CTVPN57843A##59.43.5.14");
        infos.add("苏州CTVPN57901A##59.43.5.14");
        infos.add("苏州CTVPN57901B##59.43.5.13");
        infos.add("苏州CTVPN57914A##59.43.5.138");
        infos.add("苏州CTVPN57931A##59.43.5.14");
        infos.add("苏州CTVPN57933C##59.43.5.14");
        infos.add("苏州CTVPN57933D##59.43.5.13");
        infos.add("苏州CTVPN57934B##59.43.5.138");
        infos.add("苏州CTVPN57942A##59.43.5.137");
        infos.add("苏州CTVPN57942B##59.43.5.138");
        infos.add("苏州CTVPN57964G##59.43.5.14");
        infos.add("苏州CTVPN57964H##59.43.5.14");
        infos.add("苏州CTVPN57964I##59.43.5.13");
        infos.add("苏州CTVPN57969A##59.43.5.138");
        infos.add("苏州CTVPN57969B##59.43.5.137");
        infos.add("苏州CTVPN58004A##59.43.5.14");
        infos.add("苏州CTVPN58093A##59.43.5.13");
        infos.add("苏州CTVPN58157A##59.43.5.13");
        infos.add("苏州CTVPN58173A##59.43.5.13");
        infos.add("苏州CTVPN58173B##59.43.5.14");
        infos.add("苏州CTVPN58174E##59.43.5.14");
        infos.add("苏州CTVPN58174F##59.43.5.13");
        infos.add("苏州CTVPN58174G##59.43.5.14");
        infos.add("苏州CTVPN58174H##59.43.5.13");
        infos.add("苏州CTVPN58239A##59.43.5.14");
        infos.add("苏州CTVPN58256A##59.43.5.14");
        infos.add("苏州CTVPN58256B##59.43.5.14");
        infos.add("苏州CTVPN58272A##59.43.5.14");
        infos.add("苏州CTVPN58320A##59.43.5.14");
        infos.add("苏州CTVPN58320B##59.43.5.14");
        infos.add("苏州CTVPN58320C##59.43.5.14");
        infos.add("苏州CTVPN58320D##59.43.5.14");
        infos.add("苏州CTVPN58320E##59.43.5.14");
        infos.add("苏州CTVPN58320F##59.43.5.14");
        infos.add("苏州CTVPN58320G##59.43.5.14");
        infos.add("苏州CTVPN58341A##59.43.5.14");
        infos.add("苏州CTVPN58346A##59.43.5.13");
        infos.add("苏州CTVPN58346E##59.43.5.14");
        infos.add("苏州CTVPN58346F##59.43.5.14");
        infos.add("苏州CTVPN58346G##59.43.5.14");
        infos.add("苏州CTVPN58346H##59.43.5.14");
        infos.add("苏州CTVPN58393A##59.43.5.13");
        infos.add("苏州CTVPN58426A##59.43.5.14");
        infos.add("苏州CTVPN58426B##59.43.5.13");
        infos.add("苏州CTVPN58527A##59.43.5.14");
        infos.add("苏州CTVPN60037E##59.43.6.77");
        infos.add("苏州CTVPN60037F##59.43.6.78");
        infos.add("苏州CTVPN60057A##59.43.5.14");
        infos.add("苏州CTVPN80001A##59.43.5.138");
        infos.add("苏州CTVPN80001B##59.43.5.14");
        infos.add("苏州CTVPN80001C##59.43.5.138");
        infos.add("苏州CTVPN80001D##59.43.5.137");
        infos.add("苏州CTVPN80004A##59.43.5.13");
        infos.add("苏州CTVPN80011AJ##59.43.5.138");
        infos.add("苏州CTVPN80011BO##59.43.5.13");
        infos.add("苏州CTVPN80011I##59.43.5.137");
        infos.add("苏州CTVPN80011Q##59.43.5.13");
        infos.add("苏州CTVPN80012A##59.43.5.14");
        infos.add("苏州CTVPN80012AE##59.43.5.13");
        infos.add("苏州CTVPN80012AH##59.43.5.13");
        infos.add("苏州CTVPN80012AI##59.43.5.14");
        infos.add("苏州CTVPN80012AN##59.43.5.138");
        infos.add("苏州CTVPN80012AO##59.43.5.137");
        infos.add("苏州CTVPN80012AQ##59.43.5.14");
        infos.add("苏州CTVPN80012AR##59.43.5.14");
        infos.add("苏州CTVPN80012AT##59.43.5.137");
        infos.add("苏州CTVPN80012AU##59.43.5.138");
        infos.add("苏州CTVPN80012AV##59.43.5.138");
        infos.add("苏州CTVPN80012AW##59.43.5.13");
        infos.add("苏州CTVPN80012AY##59.43.5.14");
        infos.add("苏州CTVPN80012B##59.43.5.13");
        infos.add("苏州CTVPN80012BA##59.43.5.13");
        infos.add("苏州CTVPN80012BB##59.43.5.14");
        infos.add("苏州CTVPN80012C##59.43.5.14");
        infos.add("苏州CTVPN80012E##59.43.5.14");
        infos.add("苏州CTVPN80012G##59.43.5.14");
        infos.add("苏州CTVPN80012H##59.43.5.13");
        infos.add("苏州CTVPN80012J##59.43.5.13");
        infos.add("苏州CTVPN80012K##59.43.5.13");
        infos.add("苏州CTVPN80012O##59.43.5.137");
        infos.add("苏州CTVPN80012P##59.43.5.138");
        infos.add("苏州CTVPN80012Q##59.43.5.138");
        infos.add("苏州CTVPN80012Y##59.43.5.137");
        infos.add("苏州CTVPN80019A##59.43.5.13");
        infos.add("苏州CTVPN80019B##59.43.5.14");
        infos.add("苏州CTVPN80027A##59.43.5.14");
        infos.add("苏州CTVPN80027B##59.43.5.13");
        infos.add("苏州CTVPN80031A##59.43.5.137");
        infos.add("苏州CTVPN80032A##59.43.5.138");
        infos.add("苏州CTVPN80036A##59.43.5.14");
        infos.add("苏州CTVPN80037A##59.43.5.13");
        infos.add("苏州CTVPN80202G##59.43.5.137");
        infos.add("苏州CTVPN80412A##59.43.5.13");
        infos.add("苏州CTVPN80441A##59.43.5.13");
        infos.add("苏州CTVPN80501A##59.43.5.14");
        infos.add("苏州CTVPN80502A##59.43.5.13");
        infos.add("苏州CTVPN999A##59.43.5.13");
        infos.add("苏州CTVPN999B##59.43.5.14");
        infos.add("连云港CTVPN1107A##59.43.2.165");
        infos.add("连云港CTVPN1107B##59.43.2.165");
        infos.add("连云港CTVPN1107E##59.43.2.165");
        infos.add("连云港CTVPN1107F##59.43.2.165");
        infos.add("连云港CTVPN11494B##59.43.0.65");
        infos.add("连云港CTVPN1152C##59.43.2.165");
        infos.add("连云港CTVPN1152D##59.43.0.65");
        infos.add("连云港CTVPN1158A##59.43.0.65");
        infos.add("连云港CTVPN1158B##59.43.0.65");
        infos.add("连云港CTVPN1158C##59.43.0.65");
        infos.add("连云港CTVPN1158D##59.43.0.65");
        infos.add("连云港CTVPN1158E##59.43.0.65");
        infos.add("连云港CTVPN1158F##59.43.0.65");
        infos.add("连云港CTVPN1158G##59.43.0.65");
        infos.add("连云港CTVPN1158H##59.43.0.65");
        infos.add("连云港CTVPN1158I##59.43.0.65");
        infos.add("连云港CTVPN1158J##59.43.0.65");
        infos.add("连云港CTVPN1158K##59.43.0.65");
        infos.add("连云港CTVPN1158L##59.43.0.65");
        infos.add("连云港CTVPN1158M##59.43.0.65");
        infos.add("连云港CTVPN1158N##59.43.0.65");
        infos.add("连云港CTVPN1158O##59.43.0.65");
        infos.add("连云港CTVPN1158P##59.43.0.65");
        infos.add("连云港CTVPN1158Q##59.43.0.65");
        infos.add("连云港CTVPN1158R##59.43.0.65");
        infos.add("连云港CTVPN189A003##59.43.0.65");
        infos.add("连云港CTVPN189A004##59.43.0.65");
        infos.add("连云港CTVPN189B001##59.43.2.165");
        infos.add("连云港CTVPN29004A##59.43.0.65");
        infos.add("连云港CTVPN2905C##59.43.2.165");
        infos.add("连云港CTVPN2905D##59.43.0.65");
        infos.add("连云港CTVPN29117A##59.43.0.65");
        infos.add("连云港CTVPN29127C##59.43.0.65");
        infos.add("连云港CTVPN29127D##59.43.2.165");
        infos.add("连云港CTVPN29186A##59.43.2.165");
        infos.add("连云港CTVPN29276A##59.43.2.165");
        infos.add("连云港CTVPN29290A##59.43.0.65");
        infos.add("连云港CTVPN29290B##59.43.2.165");
        infos.add("连云港CTVPN52492C##59.43.2.165");
        infos.add("连云港CTVPN54183A##59.43.0.65");
        infos.add("连云港CTVPN54315A##59.43.2.165");
        infos.add("连云港CTVPN54617B##59.43.0.65");
        infos.add("连云港CTVPN54819B##59.43.0.65");
        infos.add("连云港CTVPN55128A##59.43.2.165");
        infos.add("连云港CTVPN55160A##59.43.0.65");
        infos.add("连云港CTVPN56905A##59.43.2.165");
        infos.add("连云港CTVPN56905B##59.43.0.65");
        infos.add("连云港CTVPN57905A##59.43.0.65");
        infos.add("连云港CTVPN57933A##59.43.0.65");
        infos.add("连云港CTVPN57933B##59.43.2.165");
        infos.add("连云港CTVPN57966C##59.43.2.165");
        infos.add("连云港CTVPN57966D##59.43.0.65");
        infos.add("连云港CTVPN57985A##59.43.0.65");
        infos.add("连云港CTVPN58390A##59.43.0.65");
        infos.add("连云港CTVPN80011A##59.43.0.65");
        infos.add("连云港CTVPN80011B##59.43.2.165");
        infos.add("连云港CTVPN80011C##59.43.2.165");
        infos.add("连云港CTVPN80011D##59.43.2.165");
        infos.add("连云港CTVPN80011F##59.43.2.165");
        infos.add("连云港CTVPN80012B##59.43.0.65");
        infos.add("连云港CTVPN80012C##59.43.0.65");
        infos.add("连云港CTVPN80012D##59.43.2.165");
        infos.add("连云港CTVPN80012E##59.43.0.65");
        infos.add("连云港CTVPN80012H##59.43.2.165");
        infos.add("连云港CTVPN80130A##59.43.0.65");
        infos.add("连云港CTVPN80130B##59.43.2.165");
        infos.add("连云港CTVPN80130C##59.43.2.165");
        infos.add("连云港CTVPN80130D##59.43.0.65");
        infos.add("连云港CTVPN80130E##59.43.0.65");
        infos.add("连云港CTVPN80130F##59.43.0.65");
        infos.add("连云港CTVPN80131G##59.43.2.165");
        infos.add("连云港CTVPN80131H##59.43.0.65");
        infos.add("连云港CTVPN80202E##59.43.2.165");
        infos.add("连云港CTVPN80412A##59.43.2.165");
        infos.add("连云港CTVPN80441A##59.43.2.165");
        infos.add("连云港CTVPN80501A##59.43.2.165");
        infos.add("连云港CTVPN80502A##59.43.2.165");
        infos.add("镇江CTVPN1107A##**********");
        infos.add("镇江CTVPN1107B##***********");
        infos.add("镇江CTVPN1158A##**********");
        infos.add("镇江CTVPN1158B##**********");
        infos.add("镇江CTVPN1158C##**********");
        infos.add("镇江CTVPN1158D##**********");
        infos.add("镇江CTVPN1158E##**********");
        infos.add("镇江CTVPN1158F##**********");
        infos.add("镇江CTVPN1158G##**********");
        infos.add("镇江CTVPN1158H##**********");
        infos.add("镇江CTVPN1158I##**********");
        infos.add("镇江CTVPN1158J##**********");
        infos.add("镇江CTVPN1158K##**********");
        infos.add("镇江CTVPN1158L##**********");
        infos.add("镇江CTVPN1158M##**********");
        infos.add("镇江CTVPN1158N##**********");
        infos.add("镇江CTVPN1158O##**********");
        infos.add("镇江CTVPN1158P##**********");
        infos.add("镇江CTVPN11731A##**********");
        infos.add("镇江CTVPN13134A##***********");
        infos.add("镇江CTVPN189A002##**********");
        infos.add("镇江CTVPN189A003##**********");
        infos.add("镇江CTVPN189A007##**********");
        infos.add("镇江CTVPN189A008##**********");
        infos.add("镇江CTVPN189A009##**********");
        infos.add("镇江CTVPN189A010##**********");
        infos.add("镇江CTVPN189A011##**********");
        infos.add("镇江CTVPN189A013##**********");
        infos.add("镇江CTVPN189A014##**********");
        infos.add("镇江CTVPN189A015##**********");
        infos.add("镇江CTVPN189A016##**********");
        infos.add("镇江CTVPN189A017##**********");
        infos.add("镇江CTVPN189A019##**********");
        infos.add("镇江CTVPN189A020##**********");
        infos.add("镇江CTVPN189A021##**********");
        infos.add("镇江CTVPN189A022##**********");
        infos.add("镇江CTVPN189A023##**********");
        infos.add("镇江CTVPN189A024##**********");
        infos.add("镇江CTVPN189A025##**********");
        infos.add("镇江CTVPN189A026##**********");
        infos.add("镇江CTVPN189B002##***********");
        infos.add("镇江CTVPN189B004##***********");
        infos.add("镇江CTVPN189B005##***********");
        infos.add("镇江CTVPN189B006##***********");
        infos.add("镇江CTVPN189B008##***********");
        infos.add("镇江CTVPN189B010##***********");
        infos.add("镇江CTVPN189B011##***********");
        infos.add("镇江CTVPN189B012##***********");
        infos.add("镇江CTVPN189B013##***********");
        infos.add("镇江CTVPN189B014##***********");
        infos.add("镇江CTVPN189B015##***********");
        infos.add("镇江CTVPN189B016##***********");
        infos.add("镇江CTVPN189B017##***********");
        infos.add("镇江CTVPN189B018##***********");
        infos.add("镇江CTVPN189B019##***********");
        infos.add("镇江CTVPN19429A##***********");
        infos.add("镇江CTVPN29117B##**********");
        infos.add("镇江CTVPN29186A##**********");
        infos.add("镇江CTVPN29276A##***********");
        infos.add("镇江CTVPN29290A##**********");
        infos.add("镇江CTVPN29290B##***********");
        infos.add("镇江CTVPN29290C##**********");
        infos.add("镇江CTVPN52492C##**********");
        infos.add("镇江CTVPN52683B##**********");
        infos.add("镇江CTVPN52778A##**********");
        infos.add("镇江CTVPN53040A##***********");
        infos.add("镇江CTVPN54315A##**********");
        infos.add("镇江CTVPN54617A##**********");
        infos.add("镇江CTVPN54617B##***********");
        infos.add("镇江CTVPN54617C##**********");
        infos.add("镇江CTVPN54819A##***********");
        infos.add("镇江CTVPN55128A##**********");
        infos.add("镇江CTVPN55160A##**********");
        infos.add("镇江CTVPN56105A##**********");
        infos.add("镇江CTVPN56905A##**********");
        infos.add("镇江CTVPN56905B##***********");
        infos.add("镇江CTVPN58520A##***********");
        infos.add("镇江CTVPN80011A##**********");
        infos.add("镇江CTVPN80011B##**********");
        infos.add("镇江CTVPN80011C##**********");
        infos.add("镇江CTVPN80011D##**********");
        infos.add("镇江CTVPN80011I##**********");
        infos.add("镇江CTVPN80012A##**********");
        infos.add("镇江CTVPN80012B##**********");
        infos.add("镇江CTVPN80012D##**********");
        infos.add("镇江CTVPN80012E##**********");
        infos.add("镇江CTVPN80012F##***********");
        infos.add("镇江CTVPN80012G##**********");
        infos.add("镇江CTVPN80032A##**********");
        infos.add("镇江CTVPN80130A##***********");
        infos.add("镇江CTVPN80130B##**********");
        infos.add("镇江CTVPN80130C##**********");
        infos.add("镇江CTVPN80130D##***********");
        infos.add("镇江CTVPN80130E##**********");
        infos.add("镇江CTVPN80130F##**********");
        infos.add("镇江CTVPN80130G##***********");
        infos.add("镇江CTVPN80130H##***********");
        infos.add("镇江CTVPN80131A##**********");
        infos.add("镇江CTVPN80131B##***********");
        infos.add("镇江CTVPN80134A##**********");
        infos.add("镇江CTVPN80202G##**********");
        infos.add("镇江CTVPN80412A##**********");
        infos.add("镇江CTVPN80441A##***********");
        infos.add("镇江CTVPN80501B##**********");
        infos.add("镇江CTVPN80502A##**********");




        for (int i = 0; i < infos.size(); i++) {
            String code  = infos.get(i).split("##")[0];
            String ip  = infos.get(i).split("##")[1].trim().split(",")[0];
            String area  = code.split("CTVPN")[0];
            String areaCode = ShardingEnum.getShardingEnumByRegionName(area).getPpmCode();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("propertyValue", code);
            jsonObject.put("areaCode", areaCode);
            PageResponse<JSONObject> codeResult = iPRanJsDao.getgraphresults(jsonObject, 10, 0, "ds_graph_js");
            if (codeResult == null || CollectionUtils.isEmpty(codeResult.getData())) {
                System.out.println( "数据未整合, :"+code);
                continue;
            }
            JSONObject result = codeResult.getData().get(0).getJSONObject("results");
            JSONArray links = result.getJSONObject("data").getJSONArray("links");
            JSONArray nodes = result.getJSONObject("data").getJSONArray("nodes");
            // 对以太网链路的Z 终端， 添加nm_ip nodelabel  添加PE
            JSONObject topLink = null;
            for (int i2 = 0; i2 < links.size(); i2++) {
                if (links.getJSONObject(i2).getString("linkType").equals("以太网链路")) {
                    topLink = links.getJSONObject(i2);
                    break;
                }
            }
            if (topLink == null) {
                System.out.println("============null" +  infos.get(i));

                continue;
            }
            String zTerminalId = topLink.getString("z_device_id");
            JSONObject terminal = null;
            JSONObject pe = null;
            for (int i2 = 0; i2 < nodes.size(); i2++) {
                if (nodes.getJSONObject(i2).getString("id").equalsIgnoreCase(zTerminalId)) {
                    terminal = nodes.getJSONObject(i2);
                }
                if (nodes.getJSONObject(i2).getJSONArray("nodeLabels").contains("PE")) {
                    pe = nodes.getJSONObject(i2);
                }
            }
            if (pe != null) {
                pe.put("nm_ip",ip);
            } else {
                terminal.put("nm_ip",ip);
                if (!terminal.getJSONArray("nodeLabels").contains("PE")) {
                    terminal.getJSONArray("nodeLabels").add("PE");
                }

            }

            JSONObject param = new JSONObject();
            param.put("code", code);
            param.put("region", areaCode);
            param.put("results",result.toJSONString());
            this.circuitAnalysisDao.saveCodeResult(param, NRMConstants.SHARDING_GRAPH_DB);
            System.out.println( infos.get(i) + "完成" + i);


        }
        return "success";

    }

    @GetMapping("/circuit/circuitValidate/setStation")
    public String setStation() {
        List<String>  infos = new ArrayList<>();

        infos.add("苏州无锡ETN0061NP#321122760000000000024898");
        infos.add("南京南通ETN0106NP#321252760000000000007683");
        infos.add("南通嵊泗ETN0001NP#321132760000000000001463");
        infos.add("扬州中卫ETN0001NP#321142760000000000001692");
        infos.add("南京徐州ETN0157NP#321252760000000000007683");
        infos.add("南京常州ETN0082NP#321252760000000000007683");
        infos.add("苏州中卫ETN0001NP#321122760000000000024898");
        infos.add("南京合肥ETN0060NP#321252760000000000009926");
        infos.add("南京苏州ETN1630NP#321252760000000000007683");
        infos.add("南通嵊泗ETN0002NP#321132760000000000001463");
        infos.add("南京常州ETN0179NP#321252760000000000007683");
        infos.add("南京无锡ETN0091NP#321252760000000000007683");
        infos.add("南京扬州ETN0624NP#321252760000000000033563");
        infos.add("南京常州ETN0834NP#321252760000000000007683");
        infos.add("徐州苏州ETN0018NP#321162760000000000008790");

        for (int i = 0; i < infos.size(); i++) {
            String regionId = null;
            String code = infos.get(i).split("#")[0];
            String stationId = infos.get(i).split("#")[1];
            if (stationId.startsWith("32112")) {
                regionId = "8320500";
            }
            if (stationId.startsWith("32110")) {
                regionId = "8320200";
            }
            if (stationId.startsWith("32111")) {
                regionId = "8321100";
            }
            if (stationId.startsWith("32113")) {
                regionId = "8320600";
            }
            if (stationId.startsWith("32114")) {
                regionId = "8321000";
            }
            if (stationId.startsWith("32115")) {
                regionId = "8320900";
            }
            if (stationId.startsWith("32116")) {
                regionId = "8320300";
            }
            if (stationId.startsWith("32117")) {
                regionId = "8320800";
            }
            if (stationId.startsWith("32118")) {
                regionId = "8320700";
            }
            if (stationId.startsWith("32119")) {
                regionId = "8320400";
            }
            if (stationId.startsWith("32125")) {
                regionId = "8320100";
            }
            if (stationId.startsWith("32123")) {
                regionId = "8321200";
            }
            if (stationId.startsWith("32127")) {
                regionId = "8321300";
            }
            if (StringUtils.isEmpty(regionId)) {
                System.out.println("无法确认地区");
                continue;
            }


            try {
                String a = this.setTerminalStation(regionId,code,stationId);
                System.out.println(a+ "============"+infos.get(i));
            } catch (Exception e) {
//                System.out.println(  "==CUOWU=========="+infos.get(i));
//               e.printStackTrace();
            }
        }
        return "finish";

    }

    @GetMapping("/circuit/circuitValidate/setHardJump")
    public String setHardJump() {


        List<String> info = new ArrayList<>();
        info.add("南京苏州ETN0236NP##8320100");



        for (int i = 0; i < info.size(); i++) {
            String code = info.get(i);
            System.out.println( i +" code======"+code +"开始");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("propertyValue", code.split("##")[0]);
            jsonObject.put("areaCode", code.split("##")[1]);
            PageResponse<JSONObject> codeResult = iPRanJsDao.getgraphresults(jsonObject, 10, 0, "ds_graph_js");

            if (CollectionUtils.isEmpty(codeResult.getData())) {
                System.out.println( i +" code======"+code +"不存在");
                continue;
            }
            JSONObject result =  codeResult.getData().get(0).getJSONObject("results");
            String res = result.toJSONString();
            System.out.println("软跳"+ res.indexOf("软跳"));
            res = res.replace("软跳","硬跳");
            res = res.replace("1132100001","1131100001");
            JSONObject param = new JSONObject();
            param.put("code",  code.split("##")[0]);
            param.put("region",  code.split("##")[1]);
            param.put("results",res);
            this.circuitAnalysisDao.saveCodeResult(param, NRMConstants.SHARDING_GRAPH_DB);
            System.out.println("code======"+code +"完成");
        }

        return "finish";


    }


    /**
     * 稽核数字电路
     * 1、 终端数量
     * 2、终端直连（软跳、硬跳）
     * 3、有断点
     * 4、 两个终端连接到一个设备上
     * @return
     */
    @GetMapping("/circuit/circuitValidate/check")
    public String check() {

        List<String> info = new ArrayList<>();

        info.add("南门ATM--建行学府华庭分理处30N001NP##M00008555##8321100");
        info.add("园区建行-太仓南院路建行(M50207965)/ETN0001NP##M50207965##8320500");
        info.add("园区建行-太仓大庆锦绣新城深柳苑网点/ETN0001NP##M42657962##8320500");
        info.add("园区建行-建行干将房金部(M40065390)10M##M40065390##8320500");
        info.add("园区建行-建设投资发展/ETN0001NP##M57942965##8320500");
        info.add("宿迁市建设银行-沭阳建设银行支行营业部/ETN0001NP##MSTPJS1001425880##8321300");
        info.add("宿迁市建设银行-沭阳建设银行迎宾支行/ETN0001NP##MSTPJS1001425883##8321300");
        info.add("市公积金-民生大厦公积金ETN0001NP##M00285612##8320800");
        info.add("市建行（CJH）-大厦（LS318）/30N0001NP/PRI##M00104657##8320800");
        info.add("常州市财政局-建设银行/ETN0001NP##M00280879##8320400");
        info.add("常州常州ETN6008NPH##MSTPJS1001406841##8320400");
        info.add("常州建行-劳动局社会保险中心/ETN0001NP##M00254776##8320400");
        info.add("常州建行-建行嬉戏谷/ETN0001NP##M00218552##8320400");
        info.add("常州建行-建行政务云/ETN0001NP##M00298383##8320400");
        info.add("常州建行-建行茅山医院点/ETN0001NP##M00293622##8320400");
        info.add("常州建行-金坛第二人民医院建行点/ETN0001NP##M00276736##8320400");
        info.add("建信人寿保险-建园大厦建信人寿保险(M51307966)/ETN0001NP##M51307966##8320100");
        info.add("建行健康支行-房产大厦/30N0001NP##M00104687##8320800");
        info.add("建行-公积金/ETN0001NP##M41147680##8321000");
        info.add("建行-公积金/ETN0004NP##M47117699##8321000");
        info.add("建行-城建学校新校区/ETN0001NP##M00263760##8320400");
        info.add("建行姜堰总行-姜堰公积金管理中心/30N0001PG##M00107141##8321200");
        info.add("建行常州分行总部-建行龙锦路分校/ETN0001NP##M00291111##8320400");
        info.add("建行-建行丁堰延陵东/ETN0001NP##M00295322##8320400");
        info.add("建行-建行中天钢铁南厂区/ETN0001NP##M00301963##8320400");
        info.add("建行-建行吕南路/ETN0001NP##M00308283##8320400");
        info.add("建行-建行地铁大厦控制/ETN0001NP##M00310461##8320400");
        info.add("建行-建行欣晨苏果/ETN0001NP##M00278985##8320400");
        info.add("建行-建行武宜天豪大厦/ETN0001NP##M00290058##8320400");
        info.add("建行-建行环宇大厦信息/ETN0001NP##M00308661##8320400");
        info.add("建行-河海大学图书馆/ETN0001NP##M00262810##8320400");
        info.add("建行泰州总行-兴化建行中医院ATM/ETN0001NP##M00278546##8321200");
        info.add("建行泰州总行-姜堰天然气公司/ETN0001NP##M00254408##8321200");
        info.add("建行泰州总行-建行新牧院ATM/ETN0001NP##M00256582##8321200");
        info.add("建行泰州总行-建行牧院生活区ATM/ETN0001NP##M00254645##8321200");
        info.add("建行泰州总行-泰州四人医7楼/ETN0001NP##M00260242##8321200");
        info.add("建行泰州总行-铁塔路中国税务点/ETN0001NP##M00222492##8321200");
        info.add("建行泰州总行-靖江财政局/ETN0001NP##M00279706##8321200");
        info.add("建行-金坛市直溪镇直里村委直里村健尔康/ETN0001NP##M00309125##8320400");
        info.add("建行-金坛自来水公司建行点/ETN0001NP##M00282036##8320400");
        info.add("建设银行-丹阳新城河分理处/ETN0001NP-##M00157388##8321100");
        info.add("建设银行-市房管局ETN0001NP##M00262706##8320800");
        info.add("建设银行-建行万豪国际POS联/ETN0001NP##M00273299##8320400");
        info.add("建设银行-建行中医院/ETN0001NP##M00222056##8320400");
        info.add("建设银行-建行吾悦国际广场/ETN0001NP##M00228902##8320400");
        info.add("建设银行-建行大学城纺院/ETN0003NP##M00225025##8320400");
        info.add("建设银行-建行常工院辽河路/ETN0001NP##M00228896##8320400");
        info.add("建设银行-建行技师院分理处/ETN0001NP##M00285397##8320400");
        info.add("建设银行-建行红星美凯龙飞龙路/ETN0001NP##M00221948##8320400");
        info.add("建设银行-建行铁道高等职业技术学校/ETN0001NP##M00262813##8320400");
        info.add("建设银行淮安分行-建行大治路分理处/ETN0001N##M00244799##8320800");
        info.add("建设银行淮安分行-建行淮北分理处/ETN0001NP##M00244806##8320800");
        info.add("建设银行淮安分行-建行淮海支行/ETN0001NP##M00244793##8320800");
        info.add("建设银行淮安分行-建行淮海支行ETN0001NP##M00247595##8320800");
        info.add("建设银行淮安分行-建行直属分理处/ETN0001NP##M00104645##8320800");
        info.add("建设银行淮安分行-新奥燃气天门站/30N0001NP##M00104666##8320800");
        info.add("建设银行淮安分行-淮安公积金/30N0002NP##M00215099##8320800");
        info.add("建设银行淮安分行-淮安银监会/30N0002NP/FR##M00101792##8320800");
        info.add("建设银行淮安分行-清浦财政支付中心/30N0001NP##M00215123##8320800");
        info.add("建设银行-淮阴区卫校财务部ETN0001NP##M00253495##8320800");
        info.add("建设银行-溧阳建行中医院银医通/ETN0001NP##M00272180##8320400");
        info.add("建设银行-溧阳涵田建行POS/ETN0001NP##M00218796##8320400");
        info.add("建设银行-维科路档案室ETN0001NP##M00261066##8320800");
        info.add("建设银行镇江分行-镇江电信公司30N001NP##M00009100##8321100");
        info.add("彭城路公积金-建行永安支行/ETN0001NP##MSTPJS1001476134##8320300");
        info.add("徐州市建信人寿公司-沛县建信人寿ETN0001NP##M00244916##8320300");
        info.add("徐州市建设银行-建行丰县财政局/ETN0001NP##M00216523##8320300");
        info.add("房改办-建行靖江总行/30N0002NP##M00106651##8321200");
        info.add("承德路与淮海东路交叉口 市分行科技中心机房至承德南路60号军民小学南侧，烟草局办公楼##M00215132##8320800");
        info.add("承德路与淮海东路交叉路口建行科技部机房至承德北路闸北派出所门面##M00215081##8320800");
        info.add("文化宫-建行娑罗巷支行/30N0001NP/FR##CZ190200_c##8320400");
        info.add("新奥燃气-建设银行淮安分行/30N0001NP##M00215102##8320800");
        info.add("江都建行-建行新区分理处/30N0001NP/FR##M40002483##8321000");
        info.add("江都建行-建行邵伯分理处/30N0001NP/FR##M40002485##8321000");
        info.add("沭阳建行-沭阳农商行##MSTPJS1001410755##8321300");
        info.add("泰兴中信银行-住房公积金/ENT0001NP##M00269603##8321200");
        info.add("泰州兴化STN000049##M00280066##8321200");
        info.add("泰州兴化STN000129##M00302648##8321200");
        info.add("泰州兴化STN000144##M00302104##8321200");
        info.add("泰州兴化STN000148##M00303667##8321200");
        info.add("泰州姜堰STN000106##M00299844##8321200");
        info.add("泰州泰兴STN000057##M00281266##8321200");
        info.add("泰州泰兴STN000071##M00283189##8321200");
        info.add("泰州泰兴STN000150##M00296167##8321200");
        info.add("泰州泰州STN000064##M00280143##8321200");
        info.add("泰州泰州STN000305##M00290704##8321200");
        info.add("泰州泰州STN000333##M00291709##8321200");
        info.add("泰州泰州STN000604##M00304884##8321200");
        info.add("泰州靖江STN000085##M00293204##8321200");
        info.add("泰州靖江STN000087##M00296168##8321200");
        info.add("清河广场-建设银行淮安分行/30N0003NP##M00104726##8320800");
        info.add("清河广场-楚州镇淮楼/ETN0019NP##M00102487##8320800");
        info.add("清河广场-涟水军民/ETN0007NP##M00244781##8320800");
        info.add("清河广场-淮阴区小营/30N0501NP##M00215093##8320800");
        info.add("省建行-南京红十字医院/ETN0001NP##M23596380##8320100");
        info.add("省建行-南京金融票据中心/ETN0001NP##M07257678##8320100");
        info.add("省建行-金融支付平台/ETN0001NP##M07272766##8320100");
        info.add("苏州-太仓/ETN3215NP##M40047851##8320500");
        info.add("苏州-太仓/ETN3219NP##M40047796##8320500");
        info.add("苏州-太仓/ETN3220NP##M40047813##8320500");
        info.add("苏州-太仓/ETN3224NP##M40047858##8320500");
        info.add("苏州-太仓/ETN3227NP##M40047861##8320500");
        info.add("连云港市住房公积金中心-连云公积金管理部/30N0001NP##M40008006##8320700");
        info.add("金湖老局-金湖电信新大楼/30N0109NP##M00218904##8320800");
        info.add("镇江市建设银行-丹阳医保中心/ETN0001NP##M00145321##8321100");
        info.add("镇江市建设银行-丹阳市建设银行开发区支行/30N0001NP##M00113529##8321100");
        info.add("镇江市建设银行-丹阳社保/ETN0001NP-##M00155127##8321100");
        info.add("镇江市建设银行-建设银行普善路支行/ETN0001NP##M00143164##8321100");
        info.add("镇江市建设银行-镇江市人民银行30N001NP##M00111209##8321100");
        info.add("马鞍山-省建行/ETN0001NP##M14246162##8320100");
        info.add("镇江中信银行(镇江市润州区檀山路8号)-镇江中信银行网点（镇江市长江路11号滨江银座大厦）/ETN0001NP##M00172530##8321100");
        info.add("镇江中信银行(镇江市润州区檀山路8号)-镇江新区中信银行（江苏镇江市新区大港金港花园17幢）/ETN0001NP##M00172532##8321100");
        info.add("中信银行-中信清扬路支行/ETN0003NP##M00221092##8320200");
        info.add("中信银行-张家港国泰大厦(M66258164)20M##M66258164##8320500");
        info.add("中信银行-张家港紫金国际中信银行/ETN0001NP##M58677964##8320500");
        info.add("中信银行-金坛中信银行/ETN0001NP##M00299230##8320400");
        info.add("建设银行-建行技术师范学院（名都明都师源宾馆）/ETN0001NP##M00262242##8320400");
        info.add("镇江中信银行(镇江市润州区檀山路8号)-丹阳中信银行（丹阳市开发区东方路报业大厦1-2层）/ETN0001NP##M00172528##8321100");
        info.add("M40033045##M40033045##8320500");
        info.add("EA091971001##M07030546##8320100");
        info.add("M00223816##M00223816##8321200");
        info.add("南京南通ETN0535NP##南京南通ETN0535NP##8320100");
        info.add("EA121303001##M07265530##8320100");
        info.add("EA10653001##M07036504##8320100");
        info.add("M40013253##M40013253##8320500");
        info.add("泰州兴化STN000053##M00280423##8321200");
        info.add("MSTPJS1003337911##MSTPJS1003337911##8320100");
        info.add("**********##M07000611##8320100");
        info.add("**********##M07002152##8320100");
        info.add("ZJ_E6770##M00005661##8321100");
        info.add("**********##M07004538##8320100");
        info.add("ZJ_E6687##M00005646##8321100");
        info.add("建设银行华南分理处－镇江建行/ETN0001NP##M00143282##8321100");
        info.add("南通建行-如皋建行下原/30N0001NP##M00231423##8320600");
        info.add("徐州市建设银行-产权处信息中心/ETN0001NP##M00209595##8320300");
        info.add("建行-软件园易交易/ETN0001NP##M00308385##8320400");
        info.add("建设银行-邮政枢纽/ETN0001NP##M00218960##8320400");
        info.add("南通中信银行-海门中信银行/30N0001NP##M00266651##8320600");














        for (int i = 0; i < info.size(); i++) {
            StringBuffer sb = new StringBuffer();
            String code = info.get(i);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("propertyValue", code.split("##")[0]);
            jsonObject.put("areaCode", code.split("##")[2]);
            sb.append(" 地市:").append(ShardingEnum.getShardingEnumByPmCode(code.split("##")[2]).getRegionName()).append("电路:").append( code.split("##")[0]).append(" 接入号:").append( code.split("##")[1]).append("=>");
            PageResponse<JSONObject> codeResult = iPRanJsDao.getgraphresults(jsonObject, 10, 0, "ds_graph_js");
            if (CollectionUtils.isEmpty(codeResult.getData())) {
                sb.append("路由不存在;");
                System.out.println(sb.toString());
                continue;
            }

            JSONObject result =  codeResult.getData().get(0).getJSONObject("results");
            // 获取以太网链路
            JSONArray links = result.getJSONObject("data").getJSONArray("links");
            JSONArray nodes = result.getJSONObject("data").getJSONArray("nodes");
            JSONArray relations = result.getJSONObject("data").getJSONArray("relations");
            JSONObject topLink = null;
            JSONArray topLinks = new JSONArray();
            JSONArray jumpLinks = new JSONArray();
            JSONArray terminals = new JSONArray();
            JSONArray topRelations= new JSONArray(); // 以太网下面那层是和有断点
            List<String> lowerLinkIds= new ArrayList<>(); // 以太网下面那层是和有断点
            for (int i1 = 0; i1 < links.size(); i1++) {
                if (links.getJSONObject(i1).getString("linkType").equals("以太网链路")) {
                    topLink = links.getJSONObject(i1);
                    topLinks.add(topLink);
                }
                if (links.getJSONObject(i1).getString("linkType").equals("软跳")) {
                    jumpLinks.add(links.getJSONObject(i1));
                }
                if (links.getJSONObject(i1).getString("linkType").equals("硬跳")) {
                    jumpLinks.add(links.getJSONObject(i1));
                }
            }

            if (topLinks.size() > 1) {
                sb.append("以太网链路有多条;");
            }
            if (topLink == null) {
                sb.append("无以太网链路;");
                System.out.println(sb.toString());
                continue;
            }
            // 终端判断
            for (int i1 = 0; i1 < nodes.size(); i1++) {
                if (nodes.getJSONObject(i1).getString("id").equals(topLink.getString("a_device_id"))) {
                    if (nodes.getJSONObject(i1).toJSONString().indexOf("数字电路用户终端") <0) {
                        sb.append("以太网链路单端不是数字电路用户终端;");
                    }
                }
                if (nodes.getJSONObject(i1).getString("id").equals(topLink.getString("z_device_id"))) {
                    if (nodes.getJSONObject(i1).toJSONString().indexOf("数字电路用户终端") <0) {
                        sb.append("以太网链路单端不是数字电路用户终端;");
                    }
                }
                if (nodes.getJSONObject(i1).toJSONString().indexOf("数字电路用户终端") >=0 &&
                        nodes.getJSONObject(i1).toJSONString().indexOf("设备") >=0) {
                    terminals.add(nodes.getJSONObject(i1));
                }

            }
            if (terminals.size()!=2) {
                sb.append("终端数量不是2;");
                System.out.println(sb.toString());
                continue;
            }
            // 终端直连（软跳、硬跳）
            for (int i1 = 0; i1 < jumpLinks.size(); i1++) {
                if (jumpLinks.getJSONObject(i1).getString("a_device_id").equals(terminals.getJSONObject(0).getString("id")) &&
                        jumpLinks.getJSONObject(i1).getString("z_device_id").equals(terminals.getJSONObject(1).getString("id"))
                ) {
                    sb.append("终端直连（非顶层链路）;");
                }
                if (jumpLinks.getJSONObject(i1).getString("z_device_id").equals(terminals.getJSONObject(0).getString("id")) &&
                        jumpLinks.getJSONObject(i1).getString("a_device_id").equals(terminals.getJSONObject(1).getString("id"))
                ) {
                    sb.append("终端直连（非顶层链路）;");
                }
            }
            // 检查断点
            for (int i1 = 0; i1 < relations.size(); i1++) {
                if (relations.getJSONObject(i1).getString("z_entity_id").equals(topLink.getString("gid")) &&
                        relations.getJSONObject(i1).getString("a_nodeLabels").indexOf("链路") >=0 &&
                        relations.getJSONObject(i1).getString("relationtype").equals("承载")) {
                    lowerLinkIds.add(relations.getJSONObject(i1).getString("a_entity_id"));
                }
            }
            for (int i1 = 0; i1 < links.size(); i1++) {
                if (lowerLinkIds.contains(links.getJSONObject(i1).getString("gid"))) {
                    topRelations.add(links.getJSONObject(i1));
                }
            }
            // 确认是否存在端点,检查设备出现一次数量
            List<String>  oneceDevices = new ArrayList<>();
            for (int i1 = 0; i1 < topRelations.size(); i1++) {
                String aDeviceId = topRelations.getJSONObject(i1).getString("a_device_id");
                boolean flag = false;
                for (int i2 = 0; i2 < topRelations.size(); i2++) {
                    if (i2 == i1) {
                        continue;
                    }
                    if ( topRelations.getJSONObject(i2).getString("a_device_id").equals(aDeviceId)) {
                        flag = true;
                    }
                    if ( topRelations.getJSONObject(i2).getString("z_device_id").equals(aDeviceId)) {
                        flag = true;
                    }

                }
                if (!flag) {
                    oneceDevices.add(aDeviceId);
                }
            }

            for (int i1 = 0; i1 < topRelations.size(); i1++) {
                String aDeviceId = topRelations.getJSONObject(i1).getString("z_device_id");
                boolean flag = false;
                for (int i2 = 0; i2 < topRelations.size(); i2++) {
                    if (i2 == i1) {
                        continue;
                    }
                    if ( topRelations.getJSONObject(i2).getString("a_device_id").equals(aDeviceId)) {
                        flag = true;
                    }
                    if ( topRelations.getJSONObject(i2).getString("z_device_id").equals(aDeviceId)) {
                        flag = true;
                    }

                }
                if (!flag) {
                    oneceDevices.add(aDeviceId);
                }
            }
            if (oneceDevices.size() !=2) {
                sb.append("存在断点;");
            }
            if (sb.indexOf(";") < 0) {
                sb.append("完成");
            }
            System.out.println(sb.toString());


        }

        return "finish";
    }


    /**
     * 清除多余局站
     * @param region
     * @param code
     * @return
     */
    @GetMapping("/circuit/circuitValidate/clearMoreStation/{region}/{code}")
    public JSONObject clearMoreStation(@PathVariable("region") String region,@PathVariable("code") String code) {
        //
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("propertyValue", code);
        jsonObject.put("areaCode", region);
        PageResponse<JSONObject> codeResult = iPRanJsDao.getgraphresults(jsonObject, 10, 0, "ds_graph_js");
        JSONObject result =  codeResult.getData().get(0).getJSONObject("results");
        JSONArray relations = result.getJSONObject("data").getJSONArray("relations");
        for (int i = relations.size() - 1; i >=0; i--) {
            if ( "2210200001".equalsIgnoreCase(relations.getJSONObject(i).getString("z_spec_id")) &&
                    "2210200001".equalsIgnoreCase(relations.getJSONObject(i).getString("z_spec_id"))){
                // 如果是局站，需要判断局站是否本地的，并进行移除
                JSONObject stationNode = this.getStationNode(relations.getJSONObject(i).getString("z_entity_id"));
                if (stationNode != null && !stationNode.getString("region_id").equals(ShardingEnum.getShardingEnumByPmCode(region).getO3())) {
                    // 同时是终端的


                    System.out.println("remove--" + relations.getJSONObject(i).toJSONString());
                    relations.remove(i);
                }
            }

        }
        JSONObject param = new JSONObject();
        param.put("code", code);
        param.put("region", region);
        param.put("results",result.toJSONString());
//        this.circuitAnalysisDao.saveCodeResult(param, NRMConstants.SHARDING_GRAPH_DB);
        return result;


    }



    /**
     * {
     *                 "code": "513NT.CQN00",
     *                 "spec_id": 2210200001,
     *                 "latitude": "31.92039",
     *                 "name": "南通长桥南院",
     *                 "id": "321132760000000000001463",
     *                 "nodeLabels": [
     *                     "局站",
     *                     "局站",
     *                     "513NT.CQN00"
     *                 ],
     *                 "longitude": "120.9591"
     *             }
     * @param id
     * @return
     */
    private JSONObject getStationNode(String id) {
        JSONObject param = new JSONObject();
        param.put("id", new BigDecimal(id));
        JSONObject jsonObject = this.circuitAnalysisDao.queryStationNode(param, NRMConstants.SHARDING_GRAPH_DB);
        if (CollectionUtils.isEmpty(jsonObject.getJSONArray("data")))  {
            return null;
        }
        JSONObject station = jsonObject.getJSONArray("data").getJSONObject(0);
        JSONArray nodeLabels = new JSONArray();
        nodeLabels.add("局站");
        nodeLabels.add("局站");
        nodeLabels.add(station.getString("code"));
        station.put("nodeLabels",nodeLabels);
        return station;
    }





    @GetMapping("/circuit/circuitValidate/xingneng")
    public String xingneng() {

        List<String>  info = new ArrayList<>();
        info.add("南京宿迁ETN0415NP##8320100");
        info.add("苏州-昆山/ETN4013NP##8320000");
        info.add("无锡无锡STN000581##8320000");
        info.add("中信银行-电力公司/ETN0001NP##8320000");
        info.add("省建行-建行溧水支行/ETN0001NP##8320000");
        info.add("园区建行-供电公司/ETN0002NP##8320000");
        info.add("省建行-国土资源局/ETN0001NP##8320000");
        info.add("省建行-电力公司/ETN0001NP##8320000");
        info.add("建行-建行花东支行/ETN0001NP##8320000");
        info.add("苏州-吴江/ETN2958NP##8320000");
        info.add("盐城盐城STN000435##8320000");
        info.add("省建行-赛世香樟园/ETN0001NP##8320000");
        info.add("江苏省财政厅-省建行/ETN0003NP##8320000");
        info.add("国土资源局-园区建行/ETN0001NP##8320000");
        info.add("省建行-鼓楼区财政局/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3839NP##8320000");
        info.add("省建行-建宁路分理处/ETN0001NP##8320000");
        info.add("省建行-建行龙江支行/ETN0001NP##8320000");
        info.add("M00302041##8320000");
        info.add("苏州-张家港/ETN3810NP##8320000");
        info.add("苏州-昆山/ETN3647NP##8320000");
        info.add("中信银行-中级法院/ETN0001NP##8320000");
        info.add("苏州-常熟/ETN4838NP##8320000");
        info.add("苏州-张家港/ETN3842NP##8320000");
        info.add("银联-省肿瘤医院/ETN0001NP##8320000");
        info.add("中信银行-城中支行/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN3646NP##8320000");
        info.add("中信银行-人社局/ETN0001NP##8320000");
        info.add("省建行-南京市民卡/ETN0001NP##8320000");
        info.add("国土资源局-省建行/ETN0001NP##8320000");
        info.add("省建行-高淳区国土局/ETN0001NP##8320000");
        info.add("灌南灌南STN000054##8320000");
        info.add("苏州-常熟/ETN4763NP##8320000");
        info.add("苏州-昆山/ETN4001NP##8320000");
        info.add("省建行-溧水国土局/ETN0001NP##8320000");
        info.add("省建行-新晨国际大厦/ETN0001NP##8320000");
        info.add("园区建行-供电公司/ETN0001NP##8320000");
        info.add("建行泰州总行-建行财富中心（双提升）##8320000");
        info.add("泰州泰州ETN6280NPH##8320000");
        info.add("省建行-扬子国资/ETN0001NP##8320000");
        info.add("省建行-苜蓿园分理处/ETN0001NP##8320000");
        info.add("省石油-省建行/ETN0001NP##8320000");
        info.add("中信银行-金安押运/ETN0001NP##8320000");
        info.add("苏州-吴江/ETN3048NP##8320000");
        info.add("苏州-吴江/ETN0002NP##8320000");
        info.add("苏州-昆山/ETN3982NP##8320000");
        info.add("省建行-明基医院/ETN0001NP##8320000");
        info.add("省建行-长江油运/ETN0001NP##8320000");
        info.add("省建行-建行下关支行/ETN0001NP##8320000");
        info.add("市房管局-建设银行ETN0001NP##8320000");
        info.add("江苏省财政厅-省建行/ETN0002NP##8320000");
        info.add("中信银行-自来水公司/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN3990NP##8320000");
        info.add("苏州-常熟/ETN4827NP##8320000");
        info.add("苏州-昆山/ETN4108NP##8320000");
        info.add("省建行-建行直属支行/ETN0001NP##8320000");
        info.add("省建行-省委安全厅/ETN0001NP##8320000");
        info.add("省建行-金盾中心/ETN0001NP##8320000");
        info.add("泰兴公积金-黄桥建行/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3844NP##8320000");
        info.add("苏州-昆山/ETN3958NP##8320000");
        info.add("苏州-昆山/ETN3947NP##8320000");
        info.add("园区建行-建行望亭分理处/ETN0001##8320000");
        info.add("苏州-常熟/ETN4755NP##8320000");
        info.add("苏州-张家港/ETN3832NP##8320000");
        info.add("泰州泰州STN000310##8320000");
        info.add("省建行-建行白下支行/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN4007NP##8320000");
        info.add("公积金-中信银行/ETN0002NP##8320000");
        info.add("苏州-张家港/ETN3846NP##8320000");
        info.add("中信银行-湖南路支行/ETN0001NP##8320000");
        info.add("江苏省财政厅-省建行/ETN0001NP##8320000");
        info.add("苏州-太仓/ETN3272NP##8320000");
        info.add("苏州-昆山/ETN3963NP##8320000");
        info.add("苏州-昆山/ETN3965NP##8320000");
        info.add("省建行-省公安厅/ETN0001NP##8320000");
        info.add("省建行-自来水总公司/ETN0001NP##8320000");
        info.add("省建行-高淳红宝丽/ETN0001NP##8320000");
        info.add("苏州-太仓/ETN3255NP##8320000");
        info.add("苏州-常熟/ETN4762NP##8320000");
        info.add("苏州-吴江/ETN2970NP##8320000");
        info.add("苏州-太仓/ETN3271NP##8320000");
        info.add("靖江靖江STN000077##8320000");
        info.add("省建行-江宁区财政局/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN3931NP##8320000");
        info.add("苏州-常熟/ETN4833NP##8320000");
        info.add("建行-高邮公司/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN4010NP##8320000");
        info.add("中信银行-住建局/ETN0001NP##8320000");
        info.add("省建行-舜辉商务大厦/ETN0001NP##8320000");
        info.add("苏州-常熟/ETN4754NP##8320000");
        info.add("市民卡-中信银行/ETN0001NP##8320000");
        info.add("中信银行-省福彩中心/ETN0001NP##8320000");
        info.add("中信银行-江都房管局/ETN0001NP##8320000");
        info.add("省建行-营苑小区储蓄所/ETN0001NP##8320000");
        info.add("省建行-南京群耀智晟信息科技/ETN0001NP##8320000");
        info.add("省建行-金鹰尚美酒店负3楼机房/ETN0001NP##8320000");
        info.add("园区建行(CYQJH)-建行葑门分理处/ETN0001NP##8320000");
        info.add("仪征不动产交易中心-仪征建行支行/ETN0001NP##8320000");
        info.add("园区建行-常熟长江路266号7楼机房/ETN0001NP##8320000");
        info.add("南通建行-南通汽车东站交通局/ETN0001NP##8320000");
        info.add("中信银行号-中信银行苜蓿园大街242号/ETN0001NP##8320000");
        info.add("中信银行-中信银行山西路67号/ETN0001NP##8320000");
        info.add("中信银行泰州总行-中信银行泰兴营业部/ETN0003NP##8320000");
        info.add("南通中信银行-海门中信银行解放中路/ETN0002NP##8320000");
        info.add("中信银行-阊胥路575号中信银行/ETN0001NP##8320000");
        info.add("中信银行-昆山金泰大厦中信银行/ETN0001NP##8320000");
        info.add("中信银行-中信银行金鸡湖支行/ETN0001NP##8320000");
        info.add("宿迁市建设银行-洋河酒厂/ETN0001NP##8320000");
        info.add("省建行-建行润花园分理处/ETN0002NP##8320000");
        info.add("园区建行-建行附一院问讯处ATM/ETN0001NP##8320000");
        info.add("江苏石油大厦6楼-省建行/ETN0001NP##8320000");
        info.add("省建行-省建行珠江路分理处/ETN0001NP##8320000");
        info.add("中信银行-中信洛社支行/ETN0003NP##8320000");
        info.add("中信银行-中信宜兴丁蜀支行/ETN0003NP##8320000");
        info.add("中信银行泰州总行-中信银行高港支行/ETN0001NP##8320000");
        info.add("南通中信银行-如皋中信银行海洋南路/ETN0003NP##8320000");
        info.add("省建行-省建行卫岗分理处/ETN0001NP##8320000");
        info.add("省建设银行-省工商行政管理局/ETN0001NP##8320000");
        info.add("建设银行-建设银行东山街道竹山路565号/ETN0001NP##8320000");
        info.add("如皋建行-如皋市民中心档案馆/ETN0001NP##8320000");
        info.add("中信银行-中信银行七家湾100号/ETN0002NP##8320000");
        info.add("中信银行-中信文昌支行/ETN0002NP##8320000");
        info.add("中信银行泰州总行-中信银行兴化支行/ETN0002NP##8320000");
        info.add("中信银行-昆山虹桥路中信银行/ETN0001NP##8320000");
        info.add("中信银行-常熟海虞北路支行/ETN0001NP##8320000");
        info.add("中信银行-中信银行驻金安押运/ETN0001NP##8320000");
        info.add("中信银行-宜兴国土局土地交易中心/ETN0001NP##8320000");
        info.add("中信银行-中信银行和燕路251号/ETN0002NP##8320000");
        info.add("中信银行-中信邗江支行/ETN0002NP##8320000");
        info.add("中信银行-中信锡山支行/ETN0003NP##8320000");
        info.add("中信银行-中信银行高新技术开发区支行/ETN0001NP##8320000");
        info.add("中信银行-中信银行宜兴支行/ETN0003NP##8320000");
        info.add("中信银行-中信银行北京西路支行/ETN0001NP##8320000");
        info.add("中信银行-吴江住建局(M51127963)/ETN0001NP##8320000");
        info.add("中信银行-中信银行昆山薛赵路(M59957963)2M/ETN0001NP##8320000");
        info.add("中信银行-天一中学信息楼/ETN0002NP##8320000");
        info.add("中信银行-中信惠山分理处/ETN0001NP##8320000");
        info.add("无锡住房置业担保公司-中信银行/ETN0001NP##8320000");
        info.add("中信银行泰州总行-中信银行姜堰东方不夜城支行/ETN0001NP##8320000");
        info.add("中信银行泰州总行-中信银行新区支行/ETN00001NP##8320000");
        info.add("中信银行徐州分行-香山物流银行/ETN0001NP##8320000");
        info.add("中信银行徐州分行-新城区徐矿集团/ETN0001NP##8320000");
        info.add("泰州泰州ETN6277NPH##8320000");
        info.add("中信银行-中信银行宜兴支行/ETN0004NP##8320000");
        info.add("中信银行泰州总行-中信银行姜堰东方不夜城支行/ETN0002NP##8320000");
        info.add("南通中信银行-南通中信银行星湖大道/ETN0001NP##8320000");
        info.add("中信银行-张家港国泰时代广场中信银行/ETN0001NP##8320000");
        info.add("中信银行-中信银行吴江盛泽支行/ETN0001NP##8320000");
        info.add("园区建行-建行唯亭分理处/ETN0002NP##8320000");
        info.add("中信银行-江苏华侨大厦1层中信银行/ETN0001NP##8320000");
        info.add("省建行-淳县淳溪镇宝塔路77号二楼建行/ETN0001NP##8320000");
        info.add("省建行-省建行五台山支行/ETN0001NP##8320000");
        info.add("马甸污水处理厂-楚州区2100ETN0001NP##8320000");
        info.add("省建设银行-南京工业大学浦江学院/ETN0001NP##8320000");
        info.add("省建设银行-双牌石大街自助区/ETN0001NP##8320000");
        info.add("中信银行-中信银行园区金螳螂总部取款机(M40172998)2M##8320000");
        info.add("中信银行-中信宜兴丁蜀支行/ETN0001NP##8320000");
        info.add("政府信息中心财政局-中兴银行/ETN0001NP##8320000");
        info.add("中信银行-吴江大厦B幢7楼/ETN0001NP##8320000");
        info.add("中信银行-浦口区财政局/ETN0001NP##8320000");
        info.add("中信银行-中信新区太科园支行/ETN0001NP##8320000");
        info.add("中信银行-智慧常熟机房(M55617966)2M/ETN0001NP##8320000");
        info.add("中信银行-平泷路住房公积金中心(M51697969)/ETN0001NP##8320000");
        info.add("建淮污水处理厂-楚州区2100ETN0001NP##8320000");
        info.add("中信银行镇江分行-檀山路中信银行/ETN0001NP##8320000");
        info.add("中信银行-中信惠源分理处/ETN0001NP##8320000");
        info.add("附二院-中核医疗产业管理(M47332975)/ETN0001NP##8320000");
        info.add("中信银行-兆润财富中心中信银行/ETN0001NP##8320000");
        info.add("中信银行-中信中桥支行/ETN0001NP##8320000");
        info.add("中信实业银行-中信银行兴化支行/ETN0001NP##8320000");
        info.add("三堡污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("南通中信银行-南通国土资源局跃龙路/ETN0001NP##8320000");
        info.add("中信银行-现代大厦国土储备中心(M45057956)/ETN0001NP##8320000");
        info.add("中信银行-建筑业施工人员服务管理中心/ETN0001NP##8320000");
        info.add("苏嘴污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("中信银行-太仓支行(M43612995)/ETN0001NP##8320000");
        info.add("南通大众燃气-南通中信银行/ETN0001NP##8320000");
        info.add("中信银行-中信银行信用卡中心/ETN0001NP##8320000");
        info.add("中信银行泰州总行-中信银行靖江支行/ETN0001NP##8320000");
        info.add("中信银行-南京银行江东中路49号/ETN0001NP##8320000");
        info.add("中信银行股份有限公司盐城分行-中信银行股份有限公司盐城分行(M57350927)/N001##8320000");
        info.add("中信银行-相城公积金(M52292969)/ETN0001NP##8320000");
        info.add("中信银行-行政服务中心/ETN0001NP##8320000");
        info.add("南闸污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("中信银行-南京医保局凤游寺52号/ETN0001NP##8320000");
        info.add("中信银行-吴江财政局(M40044761)2M##8320000");
        info.add("中信银行徐州分行-市国土资源局/ETN0001NP##8320000");
        info.add("中信银行-新立基大厦2层中信银行/ETN0001NP##8320000");
        info.add("施河污水处理厂-楚州区2100ETN0001NP##8320000");
        info.add("中信银行镇江分行-中信银行新区支行/ETN0001NP##8320000");
        info.add("流均污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("南京银联-江苏银联商务和燕路/ETN0001NP##8320000");
        info.add("中信银行-自来水公司(M44462968)/ETN0001NP##8320000");
        info.add("中信银行-昆山电信IDC机房B02机柜/ETN0001NP##8320000");
        info.add("中信银行泰州总行-中信银行高港支行/ETN0002NP##8320000");
        info.add("宋集污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("复兴污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("泾口污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("仇桥污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("中信银行-中信银行龙江电信机房/ETN0001NP##8320000");
        info.add("中信银行南京分公司-省财政厅梦都大街136号/ETN0001NP##8320000");
        info.add("中信银行-昆山支行(M43612962)/ETN0001NP##8320000");
        info.add("SKETN3707NP(中信-宏致电子M40042439)2M##8320000");
        info.add("上河污水处理厂-镇淮楼8600ETN0001NP##8320000");
        info.add("顺河污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("中信银行-常熟智慧机房/ETN0001NP##8320000");
        info.add("南通中信银行-南通中信银行开发区支行/ETN0001NP##8320000");
        info.add("中信银行-中信银行相城支行(M43592985)/ETN0001NP##8320000");
        info.add("海安中信银行-海安农商行/ETN0001NP##8320000");
        info.add("中信银行南京分公司-江苏省人力资源和社会保障厅/ETN0001NP##8320000");
        info.add("华润燃气有限公司-中信银行/ETN0002NP##8320000");
        info.add("林集污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("中信银行-昆山西部服务中心(M51887963)/ETN0001NP##8320000");
        info.add("镇江市公共资源交易中心-中信银行股份有限公司镇江分行/ETN0001NP##8320000");
        info.add("中信实业银行-中信银行泰兴营业部/ETN0001NP##8320000");
        info.add("苏州-太仓/ETN3186NP##8320000");
        info.add("苏州-张家港/ETN3840NP##8320000");
        info.add("苏州-昆山/ETN4024NP##8320000");
        info.add("苏州-张家港/ETN3990NP##8320000");
        info.add("苏州-张家港/ETN3845NP##8320000");
        info.add("建设银行-市场监督局/ETN0001NP##8320000");
        info.add("省建行-主机楼6楼/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN3936NP##8320000");
        info.add("苏州-常熟/ETN4808NP##8320000");
        info.add("省建行-市土地管理局/ETN0001NP##8320000");
        info.add("省建行-工商银行/ETN0001NP##8320000");
        info.add("劳保局-中信银行/ETN0001NP##8320000");
        info.add("盐城-建湖/ETN0057NP##8320000");
        info.add("省建行-高淳财政局/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3888NP##8320000");
        info.add("苏州-昆山/ETN3991NP##8320000");
        info.add("无锡无锡STN001010##8320000");
        info.add("中信银行-中信银行浦东路11号/ETN0001NP##8320000");
        info.add("溪河污水处理厂-楚州区2100ETN0001NP##8320000");
        info.add("市公积金-涟水高沟行政大厅ETN0001NP##8320000");
        info.add("连云港市房产局-连云港市建设银行/ETN0001NP##8320000");
        info.add("中信银行-中信仪征支行/ETN0001NP##8320000");
        info.add("中信银行-房管局/ETN0001NP##8320000");
        info.add("SWETN2942NP(建行-人民西路M40052202)4M##8320000");
        info.add("园区建行(CYQJH)-建行天都支行/ETN0001NP##8320000");
        info.add("园区建行-国土资源局(M45007956)/ETN0001NP##8320000");
        info.add("省建行-建设银行将军大道529号泉峰新能源/ETN0001NP##8320000");
        info.add("中国建设银行股份有限公司宿迁分行-中国建设银行股份有限公司宿迁分行(M00262453)/N001##8320000");
        info.add("盐城-东台/ETN0086NP##8320000");
        info.add("苏州-太仓/ETN3268NP##8320000");
        info.add("泰州泰州ETN6243NPH##8320000");
        info.add("中信银行-昆山花桥支行(M58677965)20M/ETN0001NP##8320000");
        info.add("园区建行-吴中住建局(M48997959)/ETN0001NP##8320000");
        info.add("M34506887##8320000");
        info.add("兴化兴化STN000086##8320000");
        info.add("园区建行-市民卡海棠街机房/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3837NP##8320000");
        info.add("苏州-昆山/ETN3986NP##8320000");
        info.add("园区建行-住建局信息中心(M45557960)/ETN0001NP##8320000");
        info.add("园区建行-建行金阊支行/ETN0001NP##8320000");
        info.add("园区建行-建行园区支行博世ATM/ETN0002NP##8320000");
        info.add("南通中信银行-通州中信银行/ETN0001NP##8320000");
        info.add("中信银行-中信银行江星广场支行(M42327959)/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN4006NP##8320000");
        info.add("建行-房产信息中心/ETN0001NP##8320000");
        info.add("园区建行-张家港永馨园爱心超市/ETN0001NP##8320000");
        info.add("园区建行-建行吴中人民医院门诊大厅ATM(M40060063)2M##8320000");
        info.add("园区建行-常熟永旺梦城/ETN0001NP##8320000");
        info.add("建信人寿保险-昆山伟业路18号(M40077192)2M##8320500");
        info.add("MSTPJS1002148499##8320200");
        info.add("中信银行诚信大道19号-中山路348号中信大厦(2M)/30NP##8320100");
        info.add("EA091896001##8320100");
        info.add("EA10597001##8320100");
        info.add("JHTZCZJ30N002NP-B##8320600");
        info.add("苏州-太仓/ETN3226NP##8320000");
        info.add("EA11825001##8320100");
        info.add("中信银行-昆山正新橡胶ATM(M40917959)2M##8320000");
        info.add("中信银行-中信银行南门支行(M43592973)/ETN0001NP##8320000");
        info.add("中信银行-中信银行城南支行/ETN0001NP##8320000");
        info.add("中信银行-中信银行城中支行(M43592976)/ETN0001NP##8320000");
        info.add("中信银行-昆山不动产交易中心/ETN0001NP##8320000");
        info.add("中信银行-苏宁广场中信银行江东北路88号/ETN0001NP##8320000");
        info.add("中信银行-水务集团城北水厂/ETN0001NP##8320000");
        info.add("江省国税局浦江路30号-中信银行/ETN0001NP##8320000");
        info.add("M23516409##8320100");
        info.add("中信银行-仪征鼓楼自助/ETN0001NP##8321000");
        info.add("MSTPJS1002285848##8320100");
        info.add("MSTPJS1002436055##8320900");
        info.add("SCETN4822NP(建行-新莲路分理处M40054176)4M##8320500");
        info.add("MSTPJS1003334017##8320800");
        info.add("MSTPJS1001711010##8320100");
        info.add("园区建行-财政局(M53792969)/ETN0001NP##8320000");
        info.add("园区建行-吴江迎燕路(M44217963)/ETN0001NP##8320000");
        info.add("M24096393##8320100");
        info.add("中信银行-新区支行/ETN0001NP##8321000");
        info.add("园区建行-常熟公积金管理中心ATM(M50592963)/ETN0001NP##8320000");
        info.add("省建行-省建行山西路分理处/ETN0001NP##8320000");
        info.add("省建行-建设银行延安北路3号/ETN0001NP##8320000");
        info.add("中信银行-国土资源管理局(M43082965)/ETN0001NP##8320000");
        info.add("M00315024##8320000");
        info.add("南京宿迁ETN0411NP##8321300");
        info.add("南京南通ETN0122NP##8320100");
        info.add("南京无锡ETN0901NP##8320100");
        info.add("南京泰州ETN0392NP##8320100");
        info.add("南京盐城ETN0622NP##8320100");
        info.add("南京宿迁ETN0411NP##8320100");
        info.add("南京南通ETN0535NP##8320100");
        info.add("南京南通ETN0122NP##8320600");
        info.add("南京南通ETN0535NP##8320600");
        info.add("南京泰州ETN0392NP##8321200");
        info.add("南京盐城ETN0622NP##8320900");
        info.add("南京无锡ETN0901NP##8320200");
        info.add("南京宿迁ETN0415NP##8321300");
        info.add("南京宿迁ETN0412NP##8321300");
        info.add("南京南通ETN0067NP##8320100");
        info.add("南京宿迁ETN0412NP##8320100");
        info.add("南京南通ETN0067NP##8320600");
        info.add("园区建行-建行三香路支行/ETN0001NP##8320000");
        info.add("园区建行-建行西山分理处/ETN0001NP##8320000");
        info.add("南通中信银行-海安中信银行中坝南路/ETN0002NP##8320000");
        info.add("园区建行-建行中华园大饭店ATM(M40058455)/ETN0002NP##8320000");
        info.add("园区建行-建行相城区财政局驻点/ETN0001NP##8320000");
        info.add("中信银行-昆山陆丰东路ATM(M46177963)2M##8320000");
        info.add("中信银行-兆润财富中心中信银行/ETN0002NP##8320000");
        info.add("园区建行-建行园区湖东新街口支行/ETN0002NP##8320000");
        info.add("园区建行-吴江震泽镇横街(M40059750)4M##8320000");
        info.add("苏州-昆山/ETN4012NP##8320000");
        info.add("中信银行-九龙医院(M50222969)/ETN0001NP##8320000");
        info.add("中信银行-常熟中信银行(M57342965)10M/ETN0001NP##8320000");
        info.add("市政府数据资源中心-建行仪征城北支行/ETN0001NP##8320000");
        info.add("苏州-太仓/ETN3223NP##8320000");
        info.add("STETN3800NP(建行-浮桥办事处对公业务M40051707)4M##8320000");
        info.add("SCETN4816NP(建行-东南理工学院ATMM40053858)4M##8320000");
        info.add("国际数据中心净慧东道88号303B房-中信银行/ETN0002NP##8320000");
        info.add("园区建行-建行新浒支行/ETN0001NP##8320000");
        info.add("省建行-高新开发区建行泰山支行/ETN0001NP##8320000");
        info.add("园区建行-嘉业阳光假日新苑建行ATM/ETN0002NP##8320000");
        info.add("省建行-建行江宁路储蓄所/ETN0001NP##8320000");
        info.add("省建行-中国建设银行龙眠大道101号/ETN0001NP##8320000");
        info.add("省建设银行-建行时代广场/ETN0001NP##8320000");
        info.add("园区建行-建行湘城分理处(M40052994)/ETN0001NP##8320000");
        info.add("中信银行-吴江房地产管理处/ETN0001NP##8320000");
        info.add("江苏太平洋酒业有限公司-江苏太平洋酒业有限公司(M00278348)/N002##8320000");
        info.add("省建行-江浦街道象山路5号/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3841NP##8320000");
        info.add("省建行-建行扬子支行新华路497号/ETN0001NP##8320000");
        info.add("省建行-江苏第二师范学院/ETN0001NP##8320000");
        info.add("省建行-建设银行胜太路39号开发区分理处/ETN0001NP##8320000");
        info.add("省建行-鼓楼区财政局山西路124号/ETN0001NP##8320000");
        info.add("南通建行唐闸支行库房-南通建行南市街72号/ETN0001NP##8320000");
        info.add("建行-龙河电信大楼5层机房/ETN0001NP##8320000");
        info.add("市政府数据资源中心-建行城西支行/ETN0001NP##8320000");
        info.add("园区建行(CYQJH)-建行桐泾分理处/ETN0001NP##8320000");
        info.add("南通中信银行-海安财政局/ETN0001NP##8320000");
        info.add("泰州泰州ETN6326NPH##8320000");
        info.add("盐城盐城ETN6184NPH##8320000");
        info.add("中信银行-新市路112号中信银行/ETN0001NP##8320000");
        info.add("中信银行-中信银行木渎支行(M58677974)20M/ETN0001NP##8320000");
        info.add("园区建行-建行胜浦分理处/ETN0001NP##8320000");
        info.add("省建设银行-栖霞服务中心/ETN0001NP##8320000");
        info.add("省建行-上秦淮假日酒店5楼/ETN0001NP##8320000");
        info.add("中信银行-新中博职业培训学校/ETN0001NP##8320000");
        info.add("中信实业银行-中信银行姜堰支行/ETN0001NP##8320000");
        info.add("中信银行-中信银行南京分公司/ETN0001NP##8320000");
        info.add("省建设银行-建设银行海福巷71号/ETN0001NP##8320000");
        info.add("省建行-岱山路附近岱山经济适用房/ETN0001NP##8320000");
        info.add("省烟草联通新时空大厦-省建行/ETN0001NP##8320000");
        info.add("省建行-建行凤台南路146号天合广场/ETN0001NP##8320000");
        info.add("省建行-省工商行政管理局/ETN0001NP##8320000");
        info.add("苏州-吴江/ETN2997NP##8320000");
        info.add("园区建行-常熟长江路局六楼IDC(M52267969)2M##8320000");
        info.add("省建行-建设银行下曹村100号/ETN0001NP##8320000");
        info.add("常熟机要局-建设银行(M48747956)10M##8320000");
        info.add("中信银行-相城水务(M44463010)/ETN0001NP##8320000");
        info.add("建设银行洪武路188号-建设银行奥体大街177号/ETN0002NP##8320000");
        info.add("M00314967##8320000");
        info.add("M00314978##8320000");
        info.add("苏州-太仓/ETN3810NP##8320500");
        info.add("市朝阳中路住房公积金-灌云县住房公积金ETN0001NP(M48468476)##8320700");
        info.add("省建行-建设银行水阁路33号空军装备部/ETN0001NP##8320000");
        info.add("中信银行-中信银行江阴周庄支行/ETN0003NP##8320000");
        info.add("南通中信银行-通州中信银行世纪大道/ETN0002NP##8320000");
        info.add("盐城-大丰/ETN0083NP##8320000");
        info.add("中国建设银行股份有限公司昆山分行(M40058106)/N001##8320000");
        info.add("园区建行-建行何山分理处(M40052562)4M##8320000");
        info.add("南通建行-如皋自来水厂龙泉检测大楼/ETN0001NP##8320000");
        info.add("市纪委-中信银行/ETN0001NP##8320000");
        info.add("省建行-建设银行未来科技城经济发展有限公司/ETN0001NP##8320000");
        info.add("M00314974##8320000");
        info.add("省建行-交通银行明故宫路6号/ETN0001NP##8320000");
        info.add("M00315026##8320000");
        info.add("泰兴公积金建行点-泰兴建设银行/ETN0001NP##8320000");
        info.add("园区建行-建行玉盘路支行(M42262984)/ETN0001NP##8320000");
        info.add("市纪委-建设银行/ETN0001NP##8320000");
        info.add("省烟草联通新时空大厦-省建行/ETN0002NP##8320000");
        info.add("M00314966##8320000");
        info.add("中信银行-中信银行总参60所/ETN0001NP##8320000");
        info.add("中信银行-中信银行溧阳支行监控/ETN0001NP##8320000");
        info.add("中信银行-江都龙川桥北办事处/ETN0001NP##8320000");
        info.add("中信银行-中信胡埭支行/ETN0003NP##8320000");
        info.add("中信银行泰州总行-中信银行新区支行/ETN0002NP##8320000");
        info.add("省银监局-建信人寿/ETN0001NP##8320000");
        info.add("省建行-建行梦都大街176号/ETN0001NP##8320000");
        info.add("省建行-省建行城中支行/ETN0001NP##8320000");
        info.add("省建行-建行中山东路305号军区总院外科楼16层信息科/ETN0001NP##8320000");
        info.add("省建行-秣陵街道秣周东路12号紫金江宁5号楼/ETN0001NP##8320000");
        info.add("省建设银行-游府西街8号电信IDC机房/ETN0001NP##8320000");
        info.add("中信银行-中信银行新浦路127号/ETN0001NP##8320000");
        info.add("中信银行-中信惠山支行/ETN0003NP##8320000");
        info.add("中信银行-常熟琴川支行(M58677975)20M/ETN0001NP##8320000");
        info.add("省建行-浦口天润新城支行/ETN0001NP##8320000");
        info.add("中信银行-威孚高科技集团公司(外联)/ETN0001NP##8320000");
        info.add("园区建行-苏州燃气(M42407956)2M##8320000");
        info.add("龙江电信机房中信-天元西路38号中信银行/ETN0001NP##8320000");
        info.add("银联银商机房-建行新门诊大楼5层/ETN0001NP##8320000");
        info.add("市建行-金湖建西支行公积金ETN0001NP##8320000");
        info.add("省建行-省建工集团郑和中路118号/ETN0001NP##8320000");
        info.add("省建行-建行特殊教育职业技术学院外联/ETN0001NP##8320000");
        info.add("中信银行-中信通惠路支行/ETN0001NP##8320000");
        info.add("靖江建行中泰支行-靖江公积金管理中心/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3843NP##8320000");
        info.add("园区建行-常熟自来水/ETN0001NP##8320000");
        info.add("中信银行-中信广瑞路支行/ETN0003NP##8320000");
        info.add("中信银行-中信银行江阴支行/ETN0003NP##8320000");
        info.add("中信银行泰州总行-中信银行靖江支行/ETN0002NP##8320000");
        info.add("中信银行-中信银行吴中支行/ETN0001NP##8320000");
        info.add("中信银行-中信银行工业园区支行/ETN0001NP##8320000");
        info.add("省建行-建行幕府西路支行/ETN0001NP##8320000");
        info.add("银联银商机房-儿童医院河西分院门诊楼1层大厅/ETN0001NP##8320000");
        info.add("省建行-建行江东中路150号/ETN0001NP##8320000");
        info.add("中信银行南京分公司-中信银行中央路258号江南大厦/ETN0001NP##8320000");
        info.add("省建行-建行中山东路305号军区总院外科楼16层信息科/ETN0002NP##8320000");
        info.add("省建行-建行林业大学宿舍楼研究生楼二楼财务处/ETN0001NP##8320000");
        info.add("省国税局浦江路30号-电信二长机房/ETN0001NP##8320000");
        info.add("省建行-建行中央门支行/ETN0001NP##8320000");
        info.add("龙江电信机房中信-上元大街280号中信银行/ETN0001NP##8320000");
        info.add("中信银行-中信滨湖支行/ETN0001NP##8320000");
        info.add("建设银行-灌南县集中办公区财政局/ETN0001NP##8320000");
        info.add("中信银行-中信银行姑苏支行/ETN0001NP##8320000");
        info.add("洪泽黄集污水处理厂-洪泽污水泵站ETN0001NP##8320000");
        info.add("省建行-省建行孝陵卫分理处/ETN0001NP##8320000");
        info.add("无锡建行-江阴滨江IDC机房/ETN0001NP##8320000");
        info.add("徐州徐州STN100098##8320000");
        info.add("省建行-省建行矿业分理处/ETN0001NP##8320000");
        info.add("国土资源局-中信银行/ETN0001NP##8320000");
        info.add("省建行-建设银行溧水东城汇商业街营业部/ETN0001NP##8320000");
        info.add("江苏省建设银行-建设银行丰富路支行七家湾/ETN0001NP##8320000");
        info.add("省建行-市民卡有限公司/ETN0001NP##8320000");
        info.add("中信银行-中信五爱支行/ETN0001NP##8320000");
        info.add("中信银行-中信锡山支行/ETN0001NP##8320000");
        info.add("中信银行徐州分行-新城区市政府/ETN0001NP##8320000");
        info.add("园区建行-市政府11号楼1楼建设银行ATM (M40058610)/ETN0002NP##8320000");
        info.add("中信银行-中信银行吴江支行/ETN0001NP##8320000");
        info.add("园区建行-给排水公司新大楼(M40067114)2M##8320000");
        info.add("省建设银行-建行经贸学院/ETN0001NP##8320000");
        info.add("省建行-建设银行北京东路71号/ETN0001NP##8320000");
        info.add("省建行-三金燕语庭门面房/ETN0001NP##8320000");
        info.add("省建行-建行应天路分理处/ETN0001NP##8320000");
        info.add("省建行-建行建设银行锁金村/ETN0001NP##8320000");
        info.add("人社厅-建设银行直属支行云南路31号/ETN0001NP##8320000");
        info.add("省建行-翠屏国际分理处/ETN0001NP##8320000");
        info.add("省建行-高淳县财政局机房/ETN0001NP##8320000");
        info.add("中信银行-中信银行相城支行/ETN0001NP##8320000");
        info.add("中信银行-金融港商务中心西楼中信银行/ETN0001NP##8320000");
        info.add("省建行-石佛村三宫组浦口监狱东机房1楼/ETN0001NP##8320000");
        info.add("省建行-中国建设银行北圩路258号/ETN0001NP##8320000");
        info.add("中信银行-中信银行和燕路251号/ETN0001NP##8320000");
        info.add("省建行-省烟草公司二长IDC/ETN0001NP##8320000");
        info.add("省建行-省建行城南支行/ETN0001NP##8320000");
        info.add("省建行-省联合征信江东中路49号/ETN0001NP##8320000");
        info.add("省建行-中国建设银行文澜路99号/ETN0001NP##8320000");
        info.add("省建行-建设银行浦滨路扬子科创中心/ETN0002NP##8320000");
        info.add("省建行-省建行北京西路支行/ETN0001NP##8320000");
        info.add("省建设银行大楼-长白街370号2楼/ETN0001NP##8320000");
        info.add("省建行-建行江东路分理处/ETN0001NP##8320000");
        info.add("园区建行-南施街电信3楼IDC机房(M40064028)2M##8320000");
        info.add("园区建行-建行吴中西路ATM/ETN0001NP##8320000");
        info.add("省建行-建行福园路支行/ETN0001NP##8320000");
        info.add("省建行-建设银行军区总院外科楼/ETN0001NP##8320000");
        info.add("省建行-茅山路53号新区医院/ETN0001NP##8320000");
        info.add("省建设银行-江苏第二师范学院建行自助区/ETN0001NP##8320000");
        info.add("中信银行-黄埔科技大厦/ETN0001NP##8320000");
        info.add("省建行-建设银行科学园新亭西路79号/ETN0001NP##8320000");
        info.add("省建行-建设银行文苑路118号/ETN0001NP##8320000");
        info.add("市住房公积金-东海县住房公积金ETN0001NP(M48513476）##8320000");
        info.add("中信银行-市房管局产监处/ETN0001NP##8320000");
        info.add("园区建行-建行临湖支行/ETN0002NP##8320000");
        info.add("建设银行省分行-浦口财政局机房/ETN0001NP##8320000");
        info.add("省建行-中山北路260号/ETN0001NP##8320000");
        info.add("省建行-溧水县通济街11号建行/ETN0001NP##8320000");
        info.add("省建行-建设银行茅亭路63号禄口分理处/ETN0001NP##8320000");
        info.add("省建行-建行中央路42号省卫生厅/ETN0001NP##8320000");
        info.add("中信银行-无锡南外国王国际学校/ETN0001NP##8320000");
        info.add("南通长桥南院-南通中信银行/ETN0001NP##8320000");
        info.add("南通中信银行-启东交投大数据/ETN0001NP##8320000");
        info.add("中信银行-昆山电信IDC机房社保局/ETN0001NP##8320000");
        info.add("建行健康支行-会展中心公积金ETN0001NP##8320000");
        info.add("中信银行-中信银行江阴支行/ETN0001NP##8320000");
        info.add("中信银行-中信胡埭支行/ETN0001NP##8320000");
        info.add("中信银行-商业大厦/ETN0001NP##8320000");
        info.add("建行邗江支行-邗江公积金/ETN0001NP##8320000");
        info.add("中信银行徐州分行-中和大厦不动产/ETN0001NP##8320000");
        info.add("中信银行-江阴国土局/ETN0001NP##8320000");
        info.add("园区建行-阳澄湖镇人民医院/ETN0001NP##8320000");
        info.add("园区建行-建行赣江支行储蓄专柜(M40052589)10M##8320000");
        info.add("园区建行-海棠街18号电信IDC机房(M40076007)2M##8320000");
        info.add("园区建行-建行九龙医院住院部大厅ATM/ETN0001NP##8320000");
        info.add("园区建行-建行长江路支行/ETN0001NP##8320000");
        info.add("园区建行-太仓新华西路建行/ETN0001NP##8320000");
        info.add("建设银行-张家港市府大院综合楼(M51112966)/ETN0001NP##8320000");
        info.add("中信银行-中信银行浦东路13号/ETN0001NP##8320000");
        info.add("省建行-建设银行中山南路499号/ETN0002NP##8320000");
        info.add("建行城建集团-江苏省建行/ETN0001NP##8320000");
        info.add("省建设银行-建行虎踞北路12号/ETN0001NP##8320000");
        info.add("省建行-镇兴路208号建设银行/ETN0001NP##8320000");
        info.add("园区建行-电信14楼通缴(M48892956)/ETN0001NP##8320000");
        info.add("省建行-建设银行浦滨路150号/ETN0001NP##8320000");
        info.add("园区建行-常熟职教中心(M43677956)/ETN0001NP##8320000");
        info.add("住房公积金管理中心-赣榆县青口镇黄海路公积金管理中心/ETN0001NP##8320000");
        info.add("园区建行-建行浒关开发区支行(M40075208)/ETN0001NP##8320000");
        info.add("省建行-建设银行浦滨路扬子科创中心/ETN0001NP##8320000");
        info.add("省农信社-中信银行南京分公司/ETN0001NP##8320000");
        info.add("省建行-建设银行江心洲街道贤坤路60号/ETN0001NP##8320000");
        info.add("中信银行-中信银行湖南路支行/ETN0001NP##8320000");
        info.add("中信银行-中信五爱支行/ETN0003NP##8320000");
        info.add("中信银行-苏州汾湖支行(M58677969)20M/ETN0001NP##8320000");
        info.add("中信银行-昆山西街支行(M58677972)20M/ETN0001NP##8320000");
        info.add("中信银行-吴江大厦B幢7楼(M52817966)/ETN0001NP##8320000");
        info.add("中信银行号-中信银行上元大街280号/ETN0001NP##8320000");
        info.add("中信银行-中信银行凯旋城东支/ETN0001NP##8320000");
        info.add("中信银行-常熟银环路中信银行/ETN0001NP##8320000");
        info.add("省建行-国际青年会议酒店/ETN0001NP##8320000");
        info.add("省建行-建行中山南路支行/ETN0001NP##8320000");
        info.add("草场门大街89号4层龙江电信机房-双龙大道8555号华润燃气/ETN0001NP##8320000");
        info.add("中国建设银行股份有限公司镇江分行-镇江市公共交易资源中心/ETN0001NP##8320000");
        info.add("建设银行-溧水文昌路201号建设银行/ETN0001NP##8320000");
        info.add("市建设银行-建行新沂财政局/ETN0001NP##8320000");
        info.add("省建行-凤凰国际广场B座7层/ETN0001NP##8320000");
        info.add("省建行-建设银行迈尧路89号/ETN0001NP##8320000");
        info.add("省建行-建设银行上海路80号/ETN0001NP##8320000");
        info.add("中信银行-新城总部大厦/ETN0001NP##8320000");
        info.add("省建行-东芭路13号建行档案室/ETN0001NP##8320000");
        info.add("省建设银行-中泰国际广场华东石油局/ETN0001NP##8320000");
        info.add("中信银行-中信新区支行/ETN0001NP##8320000");
        info.add("园区建行-清源华衍水务(M40032897)2M##8320000");
        info.add("中信银行-住建局(M52217963)/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3809NP##8320000");
        info.add("中信银行-电信14楼通缴(M44462962)/ETN0001NP##8320000");
        info.add("中信银行-锦峰大厦A区1楼/ETN0002NP##8320000");
        info.add("园区建行-苏州社保局(M47922963)/ETN0001NP##8320000");
        info.add("南通中信银行-南通房管局人民东路/ETN0002NP##8320000");
        info.add("苏州-吴江/ETN2936NP##8320000");
        info.add("中信银行-张家港市政府机要局(M52047970)/ETN0001NP##8320000");
        info.add("中信银行-昆山扬明光学厂区(M40060356)2M##8320000");
        info.add("省建行-建筑业施工人员服务管理中心苜蓿园大街45号/ETN0001NP##8320000");
        info.add("中信银行-昆山宏致电子(M40061352)2M##8320000");
        info.add("省建设银行-通行宝智慧交通/ETN0001NP##8320000");
        info.add("建行泰州总行-靖江人力资源大楼/ETN0001NP##8320000");
        info.add("中信银行-中信银行雨花支行/ETN0001NP##8320000");
        info.add("中信银行-中信梅村支行/ETN0001NP##8320000");
        info.add("SWETN2933NP(建行-厍星路南路M40052219)4M##8320000");
        info.add("中信银行-太仓县府东街中信银行/ETN0001NP##8320000");
        info.add("中信银行-张家港中医院(M45772956)/ETN0001NP##8320000");
        info.add("省建行-建设银行溧水高级中学1层机房/ETN0001NP##8320000");
        info.add("省建行-建设银行北京西路30号/ETN0001NP##8320000");
        info.add("宿迁市建设银行-湖滨新区公安分局/ETN0001NP##8320000");
        info.add("园区建行-园区管委会建行ATM/ETN0002NP##8320000");
        info.add("省建行-建设银行嘉陵江东街8号/ETN0001NP##8320000");
        info.add("省建行-省建行黄埔路分理处/ETN0001NP##8320000");
        info.add("中信银行-中信广瑞路支行/ETN0001NP##8320000");
        info.add("省建行-建行中山支行专柜/ETN0001NP##8320000");
        info.add("市政府数据资源中心-建行江都支行/ETN0001NP##8320000");
        info.add("中信银行-社保局/ETN0001NP##8320000");
        info.add("盐城中信银行-盐城公积金管理中心/ETN0001NP##8320000");
        info.add("常州中信银行-金坛中信银行/ETN0001NP##8320000");
        info.add("园区建行-常熟景程天然气公司门卫(M52192966)/ETN0001NP##8320000");
        info.add("省建行-公积金管理中心/ETN0003NP##8320000");
        info.add("省建行-建设银行中和路111号/ETN0001NP##8320000");
        info.add("北京途牛国际旅行社-省建行/ETN0001NP##8320000");
        info.add("苏州-吴江/ETN2983NP##8320000");
        info.add("中信银行-昆山电信IDC机房(M57352964)2M/ETN0001NP##8320000");
        info.add("省建行-建设银行南大仙林校区24幢1层/ETN0001NP##8320000");
        info.add("园区建行-董浜星辰路/ETN0001NP##8320000");
        info.add("省建行-中国人民解放军95856部队管理处/ETN0001NP##8320000");
        info.add("中信银行-中信银行江阴周庄支行/ETN0001NP##8320000");
        info.add("园区建行-常熟财政局(M51702972)/ETN0001NP##8320000");
        info.add("中信银行-锦峰大厦A区1楼/ETN0001NP##8320000");
        info.add("园区建行-建行相城支行营业部/ETN0001NP##8320000");
        info.add("中信银行-吴江华衍水务(M40702971)2M##8320000");
        info.add("园区建行(CYQJH)-建行南环分理处/ETN0001NP##8320000");
        info.add("中信银行-新市政府国土局ETN0001NP##8320000");
        info.add("园区建行-建行玲珑湾支行/ETN0001NP##8320000");
        info.add("省建行-建行中央门晓庄支行/ETN0001NP##8320000");
        info.add("省建行-建行东大九龙湖校区/ETN0001NP##8320000");
        info.add("省建设银行-电信IDC机房/ETN0001NP##8320000");
        info.add("园区建行-建行横泾支行(M40065916)/ETN0001NP##8320000");
        info.add("如皋建行人社-如皋建行长江/ETN0001NP##8320000");
        info.add("省建行-建行泰山路支行/ETN0001NP##8320000");
        info.add("园区建行-吴江行政大楼辅楼建设银行(M49402959)/ETN0001NP##8320000");
        info.add("中信银行-中信银行金门支行(M43592979)/ETN0001NP##8320000");
        info.add("中信银行-昆山淳华科技(M40063854)2M##8320000");
        info.add("苏州-昆山/ETN3847NP##8320000");
        info.add("省建行-建行鼓楼支行中山北路分理处/ETN0001NP##8320000");
        info.add("省建行-六合大厦中国建设银行/ETN0001NP##8320000");
        info.add("省建行-五星电器游府西街4楼IDC/ETN0001NP##8320000");
        info.add("中信银行-相城区住建局(M41622959)/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3831NP##8320000");
        info.add("省建行-溧水县大东门街建行/ETN0001NP##8320000");
        info.add("中信银行镇江分行-镇江市公交总公司/ETN0001NP##8320000");
        info.add("省建行-建设银行理工大新财务大楼/ETN0001NP##8320000");
        info.add("中信银行-中信银行吴中支行(M43592982)/ETN0001NP##8320000");
        info.add("仪征仪化公积金-建行仪化支行/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3879NP##8320000");
        info.add("STETN3231NP(建行-港口网点M40047929)4M##8320000");
        info.add("省建行-建行扬子十村支行/ETN0001NP##8320000");
        info.add("车桥污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("如皋建行人社-如皋建行大楼/ETN0001NP##8320000");
        info.add("建行办公室-建行办公室(M40142673)/N001##8320000");
        info.add("建设银行-建设银行黄埔路3号/ETN0001NP##8320000");
        info.add("省财政-中信银行/ETN0001NP##8320000");
        info.add("中国建行镇江分行-农业农村局/ETN0001NP##8320000");
        info.add("M00314964##8320000");
        info.add("中信银行-中信银行通江新北支/ETN0001NP##8320000");
        info.add("中信银行-南施街IDC机房/ETN0001NP##8320000");
        info.add("SWETN2941NP(建行-浮楼镇M40052162)4M##8320000");
        info.add("中信银行-中信银行信用卡中心/ETN0002NP##8320000");
        info.add("园区建行-建行娄葑支行/ETN0001NP##8320000");
        info.add("镇江市建设银行-扬中菲尔斯金陵酒店有限公司/ETN0001NP##8320000");
        info.add("省建设银行-江宁区财政局/ETN0001NP##8320000");
        info.add("中信银行-中信银行金鸡湖支行(M43592994)/ETN0001NP##8320000");
        info.add("南通中信银行-如东财政局/ETN0001NP##8320000");
        info.add("省建行-建设银行汤泉东路11号汤山分理处/ETN0001NP##8320000");
        info.add("省建行-建行御道街分理处/ETN0001NP##8320000");
        info.add("园区建行-建行东沙湖股权投资中心ATM/ETN0001NP##8320000");
        info.add("园区建行(CYQJH)-建行世贸城储蓄所/ETN0001NP##8320000");
        info.add("省建行-建行江东南路储蓄所/ETN0001NP##8320000");
        info.add("市政府数据资源中心-建行高邮盂城支行/ETN0001NP##8320000");
        info.add("园区建行-常熟达涅利ATM(M41687959)4M##8320000");
        info.add("省建行-建设银行中材国际自助区/ETN0001NP##8320000");
        info.add("省建行-建行亚东城支行/ETN0001NP##8320000");
        info.add("市政府数据资源中心-建行邗江支行/ETN0001NP##8320000");
        info.add("南通中信银行-南通中信银行信用卡部/ETN0001NP##8320000");
        info.add("省建设银行-解放军联勤保障新门口10号/ETN0001NP##8320000");
        info.add("中信银行-通行宝智慧交通/ETN0001NP##8320000");
        info.add("江苏省建行-省国税局浦江路30号/ETN0001NP##8320000");
        info.add("省建行-中国建设银行科巷10号/ETN0001NP##8320000");
        info.add("洪泽行政审批中心-洪泽建行ETN0001NP##8320000");
        info.add("省建行-建设银行高庙路56号/ETN0001NP##8320000");
        info.add("新城大厦人社局-省建行/ETN0001NP##8320000");
        info.add("园区建行-建行东渚支行(M40052550)/ETN0001NP##8320000");
        info.add("省建行-建行雨花分理处/ETN0001NP##8320000");
        info.add("省建行-建设银行星火路19号/ETN0001NP##8320000");
        info.add("镇江建设银行丹徒支行-公积金管理中心/ETN0001NP##8320000");
        info.add("南通中信银行-南通房管局人民东路/ETN0001NP##8320000");
        info.add("园区建行-吴江松陵社保大楼(M54142964)/ETN0001NP##8320000");
        info.add("龙江电信机房中信-六合雄州街道中信银行/ETN0001NP##8320000");
        info.add("省建设银行-建行省环境保护厅环保厅/ETN0001NP##8320000");
        info.add("仪征建行城北支行-仪征不动产中心/ETN0001NP##8320000");
        info.add("省建行-省建行梅山分理处/ETN0001NP##8320000");
        info.add("公积金管理中心-中国建设银行太平北路51号三楼/ETN0001NP##8320000");
        info.add("园区建行-建行同里敏华实业/ETN0001NP##8320000");
        info.add("SCETN4826NP(建行-沿江开发区支行M40054539)4M##8320000");
        info.add("SKETN4008NP(园区建行-开发区分理处M40053342)4M##8320000");
        info.add("市住房公积金管理中心-连云区住房公积金管理中心/ETN0001NP##8320000");
        info.add("省建行-建行樱驼花园分理处/ETN0001NP##8320000");
        info.add("建行维扬支行-邗江公积金/ETN0001NP##8320000");
        info.add("省建行-众彩物流美食广场12幢/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN3966NP##8320000");
        info.add("大屯建行-大屯公积金/ETN0001NP##8320000");
        info.add("中信银行-中信银行城中支行/ETN0001NP##8320000");
        info.add("省建行-建行沙洲街道服务中心/ETN0001NP##8320000");
        info.add("省建行-江宁东山外国语学校/ETN0001NP##8320000");
        info.add("省建行-建设银行北京东路73号/ETN0001NP##8320000");
        info.add("中信银行南京分公司-中信银行科学园格致路309号/ETN0001NP##8320000");
        info.add("省建行-梅山钢铁七号路中区食堂/ETN0001NP##8320000");
        info.add("博里污水处理厂-淮安区ETN0001NP##8320000");
        info.add("中信银行-吴中住建局(M44463004)/ETN0001NP##8320000");
        info.add("园区建行-苏大附一院平江新院(M43442965)/ETN0001NP##8320000");
        info.add("园区建行-附二医院(M52947966)/ETN0001NP##8320000");
        info.add("园区建行-建行港龙城市商业广场(M45402959)/ETN0001NP##8320000");
        info.add("宿迁市建设银行-宿迁市公安局/ETN0001NP##8320000");
        info.add("南通中信银行-南通中信银行开发区支行/ETN0003NP##8320000");
        info.add("建行-市民广场/ETN0001NP##8320000");
        info.add("东台市建设建行-东台市国土局(不动产)(M52026920)##8320000");
        info.add("建行海德国际府前办事处-兴化公积金中心/ETN0001NP##8320000");
        info.add("南通中信银行-海安中信银行中坝南路/ETN0001NP##8320000");
        info.add("中信银行-中信银行中央路258/ETN0001NP##8320000");
        info.add("中信银行-中信银行六合雄州南路198号/ETN0001NP##8320000");
        info.add("省建设银行-南京农业大学/ETN0001NP##8320000");
        info.add("省建行-建设银行中山陵5号/ETN0001NP##8320000");
        info.add("苏州-太仓/ETN3216NP##8320000");
        info.add("省建行-建行西康路1号/ETN0001NP##8320000");
        info.add("市政府数据资源中心-建行营业部/ETN0001NP##8320000");
        info.add("苏州-常熟/ETN4801NP##8320000");
        info.add("省建行-人社厅/ETN0001NP##8320000");
        info.add("姜堰姜堰STN000044##8320000");
        info.add("徐州徐州STN100149##8320000");
        info.add("南通中信银行-如东中信银行友谊东路/ETN0002NP##8320000");
        info.add("中信银行-中信银行中山路228号/ETN0001NP##8320000");
        info.add("中信银行-电信龙江机房/ETN0001NP##8320000");
        info.add("南通自来水公司-南通中信银行/ETN0001NP##8320000");
        info.add("市中信银行-高铁未来城交通控股/ETN0001NP##8320000");
        info.add("南通中信银行-南通国土资源局跃龙路/ETN0002NP##8320000");
        info.add("园区建行-建行经贸学院传达室ATM(M40062029)/ETN0001NP##8320000");
        info.add("盐城建行建军路支行-盐城市公积金管理中心/ETN0001NP##8320000");
        info.add("建设银行-溧水县财政局/ETN0001NP##8320000");
        info.add("M00314941##8320000");
        info.add("市建设银行-香山物流银行/ETN0001NP##8320000");
        info.add("中国建设银行股份有限公司宿迁分行/ETN0002NP##8320000");
        info.add("省建行-省级机关住房管理中心/ETN0002NP##8320000");
        info.add("省建设银行-空军气象学院/ETN0001NP##8320000");
        info.add("园区建行-常熟理工学院务本楼(M53282964)/ETN0001NP##8320000");
        info.add("省建行-航空港建设第三工程总队/ETN0001NP##8320000");
        info.add("园区建行-建行跨塘支行/ETN0001NP##8320000");
        info.add("锡北人民医院-东港医院(建行外联)/ETN0001NP##8320000");
        info.add("园区建行-常熟长江路266号综合机房/ETN0001NP##8320000");
        info.add("中信银行-中信江都支行/ETN0001NP##8320000");
        info.add("建行-张家港永联小镇北街/ETN0001NP##8320000");
        info.add("园区建行-建行房产管理局(M48142962)/ETN0002NP##8320000");
        info.add("园区建行-建行沧浪支行(M40052577)10M##8320000");
        info.add("中信银行-住建局(M46252968)/ETN0001NP##8320000");
        info.add("中信银行-苏州燃气(M42527958)/ETN0001NP##8320000");
        info.add("中信银行-中信银行木渎支行(M43592997)/ETN0001NP##8320000");
        info.add("园区建行-建行新沧分理处/ETN0001NP##8320000");
        info.add("省建行-扬子巴斯夫公司一层大厅/ETN0001NP##8320000");
        info.add("省建行-建设银行永宁街1号/ETN0001NP##8320000");
        info.add("镇江市建设银行-丹阳自来水公司/ETN0001NP##8320000");
        info.add("省建行-建设银行紫东路1号/ETN0001NP##8320000");
        info.add("M00314951##8320000");
        info.add("省建行-建设银行吉印大道1888号南京埃斯顿自动化/ETN0001NP##8320000");
        info.add("省建行-建行长江后街6号东南大学/ETN0001NP##8320000");
        info.add("建设银行-中国建设银行双龙大道1341号锐恒君铂大厦/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3836NP##8320000");
        info.add("省建行-鹿鸣大道33号南京机电职业技术学院/ETN0001NP##8320000");
        info.add("园区建行-建行苏大附一院北1层放射科ATM(M46662956)/ETN0002NP##8320000");
        info.add("建行中山北路-建行中泰国际6号楼21楼/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN4003NP##8320000");
        info.add("省建行-国信财务公司/ETN0001NP##8320000");
        info.add("省建行--城建集团中央路256号/ETN0001NP##8320000");
        info.add("中国建设银行-新城区财政局/ETN0001NP##8320000");
        info.add("省建行-建设银行将军大道166号管委会/ETN0001NP##8320000");
        info.add("建设银行-建设银行百水芊城春水坊/ETN0001NP##8320000");
        info.add("建设银行-建设银行杨新路229号/ETN0001NP##8320000");
        info.add("清河市民服务中心工商-建行中北支行ETN0001NP##8320000");
        info.add("中信银行-中信仪征支行/ETN0002NP##8320000");
        info.add("省建行-南京公共自行车中心点/ETN0001NP##8320000");
        info.add("连云港连云港STN000025##8320000");
        info.add("省建行-建设银行湖滨路商业街/ETN0001NP##8320000");
        info.add("省建行-省建行铁心桥分理处/ETN0001NP##8320000");
        info.add("省建行-通行宝智慧交通/ETN0001NP##8320000");
        info.add("省建行-省建行凯悦天琴分理处/ETN0001NP##8320000");
        info.add("江苏省财政厅-中信银行南京分公司/ETN0001NP##8320000");
        info.add("省建设银行-建行73680部队/ETN0001NP##8320000");
        info.add("中信银行-昆山公共资源交易中心/ETN0001NP##8320000");
        info.add("建设银行南京分行-财政局/ETN0001NP##8320000");
        info.add("省建行-建设银行广志路22号/ETN0001NP##8320000");
        info.add("省建行-建行紫金江宁上秦淮酒店/ETN0001NP##8320000");
        info.add("省建设银行-江苏经贸职业技术学院/ETN0001NP##8320000");
        info.add("园区建行-建行吴中支行营业部/ETN0001NP##8320000");
        info.add("省建行-中国建设银行秦淮区解放路53号/ETN0001NP##8320000");
        info.add("中信银行-昆山花桥康桥学校ATM(M42917959)2M##8320000");
        info.add("省建行-建设银行杨新路229号/ETN0001NP##8320000");
        info.add("省建行-建行尧化门支行/ETN0001NP##8320000");
        info.add("园区建行-建行城北支行/ETN0002NP##8320000");
        info.add("中信银行-公积金管理中心/ETN0001NP##8320000");
        info.add("邳州建行电子政务(M00261002)/2M01IP##8320000");
        info.add("中信银行-中信银行江东中路国睿大厦/ETN0001NP##8320000");
        info.add("省建设银行-福建路52号/ETN0001NP##8320000");
        info.add("中信银行-市政府信息中心(M52942963)2M##8320000");
        info.add("园区建行-太仓苏创实业集团(M42957956)2M##8320000");
        info.add("建设银行省分行-建设银行三牌楼大街218号/ETN0001NP##8320000");
        info.add("建行-建行黑龙江路28号/ETN0001NP##8320000");
        info.add("园区建行-常熟智慧机房/ETN0001NP##8320000");
        info.add("省建行-苏源大道长安马自达/ETN0001NP##8320000");
        info.add("中信银行-房产管理局(M44462959)/ETN0001NP##8320000");
        info.add("市政府数据资源中心-建行开发区支行/ETN0001NP##8320000");
        info.add("中信银行-中信银行上海路支行/ETN0001NP##8320000");
        info.add("省建行-建设银行电信苜蓿园局3层/ETN0001NP##8320000");
        info.add("省建行-省建行和燕路分理处/ETN0002NP##8320000");
        info.add("省建行-爱涛商务中心天元西路199号/ETN0001NP##8320000");
        info.add("园区建行-建行用直支行(M40053064)/ETN0001NP##8320000");
        info.add("省建行-省建行浅草明苑/ETN0001NP##8320000");
        info.add("省建行-建设银行上河街魔法新城项目/ETN0001NP##8320000");
        info.add("省建行-高淳县淳溪镇汶溪路287号中医院机房/ETN0001NP##8320000");
        info.add("省建行-江北新区市民中心/ETN0001NP##8320000");
        info.add("省建行-建行东南大学九龙湖/ETN0001NP##8320000");
        info.add("宿迁市建设银行-宿迁市财政局/ETN0002NP##8320000");
        info.add("省建设银行大楼-省气象局业务楼/ETN0001NP##8320000");
        info.add("建信人寿保险-栖庭小区/ETN0001NP##8320000");
        info.add("园区建行(CYQJH)-建行东苑分理处/ETN0001NP##8320000");
        info.add("省建行-浦口区浦东路6号-1号/ETN0001NP##8320000");
        info.add("省建行-建行炼油厂分理处/ETN0001NP##8320000");
        info.add("建设银行-建设银行新湖大道9号/ETN0001NP##8320000");
        info.add("园区建行-昆山金陵大饭店/ETN0001NP##8320000");
        info.add("市建设银行-建行中和大厦产权处/ETN0001NP##8320000");
        info.add("建行泰州总行-建行建北储蓄所10M##8320000");
        info.add("园区建行-吴江亨通集团食堂自助/ETN0001NP##8320000");
        info.add("银联二长-建设银行江东中路269号/ETN0001NP##8320000");
        info.add("省建行-建行体彩代办点五环大厦1层/ETN0001NP##8320000");
        info.add("苏州-张家港/ETN3878NP##8320000");
        info.add("中信银行-昆山电信IDC(M56077964)2M##8320000");
        info.add("园区建行-建行希尔顿逸林酒店大堂ATM(M45822957)/ETN0002NP##8320000");
        info.add("建信人寿保险-张家港国泰东方广场(M46337980)/ETN0001NP##8320000");
        info.add("宿迁市建设银行-沭阳县财政局测试/ETN0001NP##8320000");
        info.add("省建行-省建行五龙桥分理处/ETN0002NP##8320000");
        info.add("M40065336(建行太平南路-苏州)2M##8320000");
        info.add("园区建行-建行相城支行陆慕分理处/ETN0001NP##8320000");
        info.add("园区建行-相城公积金中心(M40059110)/ETN0002PG##8320000");
        info.add("中信银行-江都城区办事处/ETN0001NP##8320000");
        info.add("中信银行-中信银行环府武进支/ETN0001NP##8320000");
        info.add("中信银行-中信梅村支行/ETN0003NP##8320000");
        info.add("中信银行-中信新区太科园支行/ETN0003NP##8320000");
        info.add("中信银行泰州总行-中信银行青年路支行/ETN0002NP##8320000");
        info.add("南通中信银行-启东中信银行和平中路/ETN0002NP##8320000");
        info.add("中信银行-昆山前进中路中信银行/ETN0001NP##8320000");
        info.add("中信银行-工学院新校区财务ETN0001NP##8320000");
        info.add("平桥污水处理厂-镇淮楼ETN0001NP##8320000");
        info.add("中国建设银行江都市支行-中国建设银行江都市支行(M40144139)/N001##8320000");
        info.add("新沂建行公积金-国土局/ETN0001NP##8320000");
        info.add("中信银行-张家港金港支行(M43607956)/ETN0001NP/##8320000");
        info.add("南通中信银行-启东中信银行和平中路/ETN0001NP##8320000");
        info.add("园区建行-太仓市政府财政局(M42437965)2M##8320000");
        info.add("中信银行-园区公积金管理中心(M40050073)/ETN0001NP##8320000");
        info.add("园区建行-太仓东亭大厦建行(M49602968)4M##8320000");
        info.add("建行中山北路支行-北区鼓楼区财政局/30N0001NP##8320000");
        info.add("建行总部-城乡建设信息中心/ETN0001NP##8320000");
        info.add("园区建行-建行滟河湾花园支行(M45527971)/ETN0001NP##8320000");
        info.add("园区建行-创元集团财务/ETN0001NP##8320000");
        info.add("园区建行-黄埭镇为民服务中心ATM/ETN0001NP##8320000");
        info.add("园区建行-张家港市凤凰镇凤南路江苏富淼科技/ETN0001NP##8320000");
        info.add("南通中信银行-如皋财政局/ETN0001NP##8320000");
        info.add("园区建行-昆山祖冲之路(M48002956)/ETN0001NP##8320000");
        info.add("园区建行-港口发展大厦30楼/ETN0001NP##8320000");
        info.add("泰州兴化STN000053##8320000");
        info.add("建信人寿保险-钻石广场建信人寿保险(M51372963)/ETN0001NP##8320000");
        info.add("建行-文化广场/ETN0001NP##8320000");
        info.add("建设银行-建行横山五一自助银行/ETN0001NP##8320000");
        info.add("M00354243##8320000");
        info.add("中信银行-中信通惠路支行/ETN0003NP##8320000");
        info.add("如东农商行燎原-如东中信银行/ETN0001NP##8320000");
        info.add("中信银行巨凝-文化广场劳动和社会保障局/ETN0001NP##8320000");
        info.add("中信银行镇江分行-镇江市自来水公司/ETN0001NP##8320000");
        info.add("中信银行徐州分行-中和大厦产权处/ETN0001NP##8320000");
        info.add("中信银行-中信银行元丰天宁支/ETN0001NP##8320000");
        info.add("常州人民银行-中信银行同城交换/ETN0001NP##8320000");
        info.add("建信人寿保险-吴江上领国际大厦(M45732962)/ETN0001NP##8320000");
        info.add("建设银行-建行常工院秋白楼/ETN0001NP##8320000");
        info.add("园区建行-东平街建设银行/ETN0001NP##8320000");
        info.add("园区建行-吴中供水(M50872963)/ETN0001NP##8320000");
        info.add("武进政务云-建行/ETN0002NP##8320000");
        info.add("园区建行-平路公安局主楼A幢1楼(M53202979)/ETN0001NP##8320000");
        info.add("园区建行-现代大厦国土储备中心/ETN0001NP##8320000");
        info.add("镇江市建设银行-建设银行（丹阳公共资源交易中心)/ETN0001NP##8320000");
        info.add("公安局-园区建行/ETN0001NP##8320000");
        info.add("建行-建行滆湖中路(扁平化改造待拆)/ETN0001NP##8320000");
        info.add("园区建行-建行科技城支行(M44222959)/ETN0001NP##8320000");
        info.add("中信实业银行-泰州公积金管理中心/ETN0001NP##8320000");
        info.add("宿迁市建设银行-个贷电子政务网专线/ETN0001NP##8320000");
        info.add("园区建行-希尔顿逸林酒店(M46137963)/ETN0001NP##8320000");
        info.add("园区建行-建行吴中开发区管委会ATM/ETN0001NP##8320000");
        info.add("建行-邗江公积金自助/ETN0001NP##8320000");
        info.add("建设银行-建行常高新大厦/ETN0002NP##8320000");
        info.add("建行泰州总行-建行兴化不动产ATM/ETN0001NP##8320000");
        info.add("园区建行-建行吴中开发区支行(M40053028)/ETN0001NP##8320000");
        info.add("苏州-常熟/ETN4834NP##8320000");
        info.add("新区管委会-园区建行(M52697963)/ETN0001NP##8320000");
        info.add("园区建行-张家港沙洲东路高级中学(M47487959)/ETN0001NP##8320000");
        info.add("园区建行-康阳路390号公安局/ETN0001NP##8320000");
        info.add("园区建行-建行独墅湖高教区支行(M40060003)10M##8320000");
        info.add("钟楼政务云-建行/ETN0001NP##8320000");
        info.add("苏州建行-昆山青阳支行(M40058097)/N001##8320000");
        info.add("园区建行-相城区房管局/ETN0001NP##8320000");
        info.add("建设银行-建行稻香路/ETN0001NP##8320000");
        info.add("园区建行-郭新东路建行(M54462974)/ETN0001NP##8320000");
        info.add("园区建行-昆山杜克大学建行(M51287974)/ETN0001NP##8320000");
        info.add("园区建行-张家港沙钢财务公司/ETN0001NP##8320000");
        info.add("园区建行-科技城医院(M46452956)/ETN0001NP##8320000");
        info.add("盐城市建设银行-盐城市房管局/ETN0001NP##8320000");
        info.add("园区建行-幼儿师范学ATM(M45142956)/ETN0002NP##8320000");
        info.add("园区建行-东桥为民服务中心ATM/ETN0001NP##8320000");
        info.add("园区建行-元和燃气大楼(M48272965)/ETN0001NP##8320000");
        info.add("建设银行-建行大学城纺院食堂/ETN0001NP##8320000");
        info.add("园区建行-相城自来水公司(M40367956)2M##8320000");
        info.add("园区建行-常熟风范电力新厂区/ETN0001NP##8320000");
        info.add("园区建行-海棠街18号210IDC机房(M51462963)/ETN0001NP##8320000");
        info.add("M07004529##8320000");
        info.add("园区建行-市民卡海棠街机房(M53252963)/ETN0001NP##8320000");
        info.add("园区建行-建行黄桥分理处/ETN0001NP##8320000");
        info.add("园区建行-久阳商务楼6楼/ETN0001NP##8320000");
        info.add("园区建行-建行旅游财经学校ATM(M44697960)/ETN0002NP##8320000");
        info.add("园区建行-建行科技城医院ATM(M45802956)/ETN0002NP##8320000");
        info.add("中信银行-中信邗江支行/ETN0001NP##8320000");
        info.add("园区建行-太仓北京东路建行(M54307966)/ETN0001NP##8320000");
        info.add("园区建行-建行东沙湖邻里中心支行(M45527956)/ETN0001NP##8320000");
        info.add("园区建行-建行太平支行/ETN0002NP##8320000");
        info.add("园区建行-苏州中心广场建设银行(M49933003)/ETN0001NP##8320000");
        info.add("园区建行-相城区财政局(M52002963)2M/ETN0001NP##8320000");
        info.add("园区建行-房产交易中心(M54277968)/ETN0001NP##8320000");
        info.add("园区建行-建行枫桥支行(M40051722)10M##8320000");
        info.add("建行-建行溧阳中关村苏控集团/ETN0001NP##8320000");
        info.add("园区建行-建行苏街商务中心B楼ATM/ETN0001NP##8320000");
        info.add("园区建行-漕湖人民医院/ETN0001NP##8320000");
        info.add("建行春江花城分理处-二手房交易中心/ETN0001NP##8320000");
        info.add("宿迁市建设银行-电信长线分局IDC机房D1机柜托管服务器/ETN0001NP##8320000");
        info.add("南通中信银行-如皋中信银行海洋南路/ETN0002NP##8320000");
        info.add("园区建行-太平街道卫生院/ETN0001NP##8320000");
        info.add("苏州-常熟/ETN4837NP##8320000");
        info.add("SWETN2924NP(建行-亨通集团M40051871)2M##8320000");
        info.add("园区建行-建行郭新东路/ETN0001NP##8320000");
        info.add("园区建行-东渚科技城IDC机房(G11机柜)(M43507971)2M/ETN0001NP##8320000");
        info.add("园区建行-海棠街18号IDC(M54107973)/ETN0001NP##8320000");
        info.add("园区建行-房产管理局/ETN0001NP##8320000");
        info.add("建设银行-建行武进医院自助银行/ETN0001NP##8320000");
        info.add("园区建行-建行国开行苏州软件开发中心ATM/ETN0001NP##8320000");
        info.add("园区建行-张家港常青藤中学(M49257965)/ETN0001NP##8320000");
        info.add("园区建行-建行望亭中医院(M47067959)/ETN0001NP##8320000");
        info.add("海安住建局-海安建行/ETN0001NP##8320000");
        info.add("盐城-建湖/ETN0054NP##8320000");
        info.add("M40013253##8320000");
        info.add("盐城中信银行-盐城悦达集团/ETN0001NP##8320000");
        info.add("园区建行-太仓高级中学/ETN0001NP##8320000");
        info.add("园区建行-建行葑谊分理处/ETN0001NP##8320000");
        info.add("盐城盐城STN000219##8320000");
        info.add("园区建行-建行滨湖支行(M40242971)/ETN0001NP##8320000");
        info.add("M40009221##8320900");
        info.add("MSTPJS1003003782##8320700");
        info.add("MSTPJS1003103754##8320800");
        info.add("中信银行-中信银行新区支行(M43592991)/ETN0001NP##8320000");
        info.add("园区建行-建行万隆汽车ATM机(M40058283)2M/ETN0001NP##8320000");
        info.add("镇江中信银行(镇江市润州区檀山路8号)-句容中信银行（江苏句容市华阳北路9号句容宾馆）/ETN0001NP##8320000");
        info.add("园区建行-建行胥口支行(M40053058)/ETN0001NP##8320000");
        info.add("园区建行-住建局(M52282963)/ETN0001NP##8320000");
        info.add("园区建行-昆山市千灯镇恒丰时代广场/ETN0001NP##8320000");
        info.add("园区建行-建行城市生活广场(M52652969)/ETN0001NP##8320000");
        info.add("中信银行镇江分行-中信银行句容支行/ETN0001NP##8320000");
        info.add("园区建行-龙西路160号建设银行/ETN0001NP##8320000");
        info.add("中信银行-市民广场B26F/ETN0001NP##8320000");
        info.add("园区建行-石湖金陵花园酒店(M48117956)/ETN0001NP##8320000");
        info.add("园区建行-建行固锝电子公司食堂ATM/ETN0001NP##8320000");
        info.add("M23516385##8320100");
        info.add("园区建行-常熟海虞支行(M40062967)4M##8320500");
        info.add("M23516388##8320100");
        info.add("泰州泰州STN000383##8320000");
        info.add("中信银行-中信滨湖支行/ETN0003NP##8320000");
        info.add("园区建行-建行庄基新社区大楼ATM/ETN0001NP##8320000");
        info.add("中信银行镇江分行-丹阳财政局/ETN0001NP##8320000");
        info.add("中信银行-中信银行晋陵城中支/ETN0001NP##8320000");
        info.add("M00223816##8320000");
        info.add("园区建行-昆山汇金财富广场/ETN0001NP##8320000");
        info.add("M00327809##8320000");
        info.add("中信银行-无锡百凌文件管理服务有限公司/ETN0001NP##8320200");
        info.add("M23516410##8320100");
        info.add("园区建行-方洲路598号行政楼(M52572963)/ETN0001NP##8320000");
        info.add("中信银行镇江分行-镇江市华润燃气有限公司/ETN0001NP##8320000");
        info.add("园区建行-苏大文正学院创意中心ATM(M51492966)/ETN0001NP##8320000");
        info.add("园区建行-交通局(M55932974)/ETN0001NP##8320000");
        info.add("南通中信银行-通州中信银行世纪大道/ETN0001NP##8320000");
        info.add("M00004215##8320000");
        info.add("中信银行镇江分行-中信银行扬中支行(金叶大酒店)/ETN0001NP##8320000");
        info.add("园区建行-建行渡村新市街ATM(M47102958)/ETN0002NP##8320000");
        info.add("园区建行-建行吴中区政府6号楼1楼ATM(M40069893)4M/ETN0001NP##8320000");
        info.add("园区建行-张家港新电大创业园建设自助银行电话(M51972963)/ETN0002NP##8320000");
        info.add("园区建行-昆山沪士电子新厂房(M40064267)2M##8320500");
        info.add("中信银行-中信文昌支行/ETN0001NP##8321000");
        info.add("MSTPJS1002291570##8320100");
        info.add("园区建行-常熟白茆工业园波司登(M40352962)2M##8320000");
        info.add("中信银行-中信新区长江路支行/ETN0003NP##8320000");
        info.add("园区建行-建行渭西村ATM/ETN0001NP##8320000");
        info.add("建设银行-建行江苏工程学院网点/ETN0001NP##8320000");
        info.add("建行-建行金坛吾悦广场/ETN0001NP##8320000");
        info.add("园区建行-建行唯泰精品购物村/ETN0001NP##8320000");
        info.add("南通中信银行-海门中信银行解放中路/ETN0001NP##8320000");
        info.add("园区建行-吴江盛虹集团/ETN0001NP##8320000");
        info.add("园区建行-建行理想城支行/ETN0001NP##8320500");
        info.add("中信银行-中信银行上海汽车商用车公司ATM机/ETN0001NP##8320200");
        info.add("MSTPJS1003334070##8320800");
        info.add("园区建行(CYQJH)-建行中山路储蓄所/ETN0001NP##8320000");
        info.add("盐城盐城STN000445##8320000");
        info.add("园区建行-景德路建行(M54277970)/ETN0001NP##8320000");
        info.add("园区建行-海棠街18号电信IDC机房/ETN0001NP##8320000");
        info.add("园区建行-昆山市社保局(M54977967)/ETN0001NP##8320000");
        info.add("中信银行-天一中学自助/ETN0002NP##8320200");
        info.add("园区建行-昆山巴城支行(M41632958)2M##8320500");
        info.add("园区建行-张家港学院现教中心(M49832976)/ETN0001NP##8320500");
        info.add("园区建行-笠泽路建委大楼(M51907972)/ETN0001NP##8320000");
        info.add("园区建行-建行金龙客车ATM/ETN0001##8320000");
        info.add("园区建行-张家港海关路建行(M47712978)/ETN0001NP##8320000");
        info.add("中信银行镇江分行-丹阳房管局物业管理基金/ETN0001NP##8320000");
        info.add("住房公积金管理中心-灌南县新安镇人民西路(FTTH)住房公积金/ETN0001NP##8320700");
        info.add("M23516394##8320100");
        info.add("M00297567##8320300");
        info.add("MSTPJS1003355774##8320100");
        info.add("园区建行-公积金服务大厅(M50642963)/ETN0002NP##8320000");
        info.add("泰兴公积金-泰兴建设银行/ETN0001NP##8320000");
        info.add("园区建行-吴中区财政局(M53427982)/ETN0001NP##8320000");
        info.add("园区建行-公安局(M61792964)/ETN0001NP##8320000");
        info.add("园区建行-越溪国土资源局(M54977966)/ETN0001NP##8320000");
        info.add("园区建行-建行苏大附一院住院部ATM(M45962956)/ETN0002NP##8320000");
        info.add("园区建行-永旺华东商业(M40512968)2M##8320000");
        info.add("STETN3217NP(建行-人民南路M40047810)4M##8320500");
        info.add("中国建设银行股份有限公司盐城分行-中国建设银行股份有限公司盐城分行(M52956925)/N001##8320900");
        info.add("建信人寿保险-常熟中凯国际大厦(M40077141)2M##8320500");
        info.add("MSTPJS1002011156##8320800");
        info.add("M23516431##8320100");
        info.add("园区建行-建行澄和路支行(M40053022)/ETN0001NP##8320000");
        info.add("M00223098##8320000");
        info.add("M00236531##8320000");
        info.add("建行-建行轨道维修/ETN0001NP##8320000");
        info.add("中信银行-中信中桥支行/ETN0003NP##8320000");
        info.add("中信银行股份有限公司镇江分行-扬中市财政局/ETN0001NP##8320000");
        info.add("中信银行-中信银行信用卡/ETN0001NP##8320000");
        info.add("园区建行-吴江大厦信息中心(M52397969)/ETN0001NP##8320000");
        info.add("建设银行-建行雷利电机/ETN0001NP##8320000");
        info.add("中信银行-昆山花桥支行(M43607959)/ETN0001NP##8320000");
        info.add("公积金-中信银行/ETN0001NP##8320000");
        info.add("南通中信银行-海门中信银行/30N0001NP##8320600");
        info.add("句容市华阳镇华阳北路中信银行##8321100");
        info.add("建行高港支行-高港公积金管理中心/ETN0001NP##8321200");
        info.add("市政府数据资源中心-建行新区支行/ETN0001NP##8321000");
        info.add("中信银行-中信汶北支行/ETN0001NP##8320000");
        info.add("园区建行-平泷路住房公积金中心(M51702969)/ETN0001NP##8320000");
        info.add("苏州-昆山/ETN3808NP##8320000");
        info.add("中信银行镇江分行-中信银行句容支行/ETN0002NP##8320000");
        info.add("M40033045##8320000");
        info.add("园区建行-昆山住建局(M42722980)2M##8320000");
        info.add("园区建行-建行园区档案大厦房产交易中心/ETN0001NP##8320000");
        info.add("M00115962##8320000");
        info.add("建设银行-昆山花园路支行(M40059865)2M##8320500");
        info.add("镇江中信银行(镇江市润州区檀山路8号)-扬中中信银行（扬中市扬子中路133号1层、6层）/ETN0001NP##8321100");
        info.add("中信银行-上海汽车自助/ETN0002NP##8320200");
        info.add("园区建行-平江区国库支付中心(M53777966)/ETN0001NP##8320000");
        info.add("园区建行-张家港沙洲湖科创园新点软件/ETN0001NP##8320000");
        info.add("宿迁市建设银行-宿迁建行老大楼四楼专线/ETN0001NP##8320000");
        info.add("银监局新址-中信银行/ETN0001NP##8320000");
        info.add("公积金-建行琼花支行/ETN0001NP##8320000");
        info.add("园区建行-建行安洁科技北大门ATM/ETN0001NP##8320000");
        info.add("园区建行-吴中商务中心(M51197963)/ETN0001NP##8320000");
        info.add("园区建行-园区公积金管理中心/ETN0001NP##8320000");
        info.add("M07000614##8320000");
        info.add("园区建行(CYQJH)-建行苏锦分理处/ETN0001NP##8320000");
        info.add("中信银行泰州总行-中信银行青年路支行/ETN0001NP##8321200");
        info.add("省建行-南京工业大学浦江学院办公楼三楼机房/ETN0001NP##8320100");
        info.add("M00314501##8320300");
        info.add("M00314863##8320300");
        info.add("中信银行-中信新区支行/ETN0002NP##8321000");
        info.add("中信银行-中信银行吉兴集团自助银行/ETN0001NP##8320200");
        info.add("中信银行-中信银行天一中学食堂ATM机/ETN0001NP##8320200");
        info.add("MSTPJS1003336526##8320100");
        info.add("扬州建行-仪化股份公司/30N0001NP##8321000");
        info.add("市区三元路LS8734-扬州建行/30N0002NP/PRI##8321000");
        info.add("省建行-省国土厅/ETN0001NP##8320100");
        info.add("省建行-南京工业大学浦江学院办公楼三楼机房/30N0001NP##8320100");
        info.add("建行-宝应支行/30N0001NP##8321000");
        info.add("建行泰州总行-建行泰兴支行/30N0018NP##8321200");
        info.add("连云港市住房公积金中心-赣榆县公积金管理中心/30N0001NP##8320700");
        info.add("扬州建行-仪征建行/30N0001NP##8321000");
        info.add("镇江市建设银行-镇江电信公司计费中心/30N001NP##8321100");
        info.add("泰兴财政局-建行泰兴支行/30N0001PG##8321200");
        info.add("中信银行-中信银行工业大学丁家桥校区/30N0001NP##8320100");
        info.add("中信银行-中信银行大厂镇新华西路360号/30N0001NP##8320100");
        info.add("中信银行-中信银行南京分行汉中路89号南京金鹰国际/30N0001NP##8320100");
        info.add("中信银行-南京工业大学江浦校区信息中心/30N0001NP##8320100");
        info.add("中信银行-南京证券/30N0001NP##8320100");
        info.add("中信银行-中信银行新模范马路5号/30N0001NP##8320100");
        info.add("中信银行-南京信息工程大学/30N0001NP##8320100");
        info.add("南通中信银行-南通财政局/ETN0001NP##8320600");
        info.add("中信银行-南京信息工程大学创业中心3号楼201室/30N0001NP##8320100");
        info.add("中信银行-六合中信银行江北支行/30N0001NP##8320100");
        info.add("中信银行-中信银行马群街169号/30N0001NP##8320100");
        info.add("中信银行-中信银行诚信大道19号国电南瑞/30N0001NP##8320100");
        info.add("中信银行-中信银行中山路348号/30N0001NP##8320100");
        info.add("南通中信银行-启东中信银行筹建处/30N0001NP##8320600");
        info.add("南通中信银行-南通中信银行开发区支行/30N0002NP##8320600");
        info.add("中信银行-中信银行集庆门大街269号/30N0001NP##8320100");
        info.add("建行泰州总行-建行公积金房贷窗口./ETN0001NP##8321200");
        info.add("中信银行-真州路/30N0001NP##8321000");
        info.add("ZJ_E6113##8321100");
        info.add("泰州泰兴STN000132##8321200");
        info.add("中信银行-昆山西街支行(M40577962)2M##8320500");
        info.add("江都公积金-中信龙川桥北分理处/30N0001NP##8321000");
        info.add("镇江市财政局-中信银行镇江分行30N001NP##8321100");
        info.add("EA061835001##8320100");
        info.add("EA082031001##8320100");
        info.add("EA101552001##8320100");
        info.add("EA102965001##8320100");
        info.add("EA10654001##8320100");
        info.add("SZ30N044NP(中信银行)##8320500");
        info.add("EA061345001##8320100");
        info.add("EA091024001##8320100");
        info.add("ZJ_E6116##8321100");
        info.add("中信银行-中信六合大厦/30N0001NP##8320100");
        info.add("中信银行-吴江支行(M43602974)/ETN0001NP##8320500");
        info.add("中信银行-吴江盛泽支行(M43602968)/ETN0001NP##8320500");
        info.add("EA061066001##8320100");
        info.add("EA121139001##8320100");
        info.add("SCETN4764NP(建行-菱北分理处*********)4M##8320500");
        info.add("SCETN4800NP(建行-城东支行*********)4M##8320500");
        info.add("SCETN4817NP(建行-元和路分理处*********)2M##8320500");
        info.add("**********##8320100");
        info.add("*********##8320500");
        info.add("MSTPJS1002526109##8320100");
        info.add("NTCQNY-NTJH30N0##8320600");
        info.add("**********##8320100");
        info.add("JR_E4123##8321100");
        info.add("KHMFDL-20##8321100");
        info.add("MSTPJS1003348549##8320100");
        info.add("SCETN4758NP(建行-衡山路分理处*********)4M##8320500");
        info.add("SCETN4809NP(建行-海虞北路老街分理处*********)4M##8320500");
        info.add("SCETN4823NP(建行-城南支行*********)4M##8320500");
        info.add("SCETN4825NP(建行-福山支行*********)4M##8320500");
        info.add("SCETN4828NP(建行-东南开发区支行*********)4M##8320500");
        info.add("SCETN4830NP(建行-沿江开发区支行*********)4M##8320500");
        info.add("SKETN3932NP(建行-建大轮胎自助*********)2M##8320500");
        info.add("SKETN3949NP(建行-房产交易中心*********)2M##8320500");
        info.add("SKETN3955NP(建行-花桥分理处ATMM40052276)4M##8320500");
        info.add("SKETN3987NP(园区建行-友谊园分理处*********)4M##8320500");
        info.add("SWETN2914NP(建行-盛泽广场分理处*********)4M##8320500");
        info.add("SWETN2943NP(建行-通运路M40052156)2M##8320500");
        info.add("SZETN3812NP(建行-港区港务分理处M40050990)4M##8320500");
        info.add("SZETN3813NP(建行-保税区营业部M40050981)10M##8320500");
        info.add("TZCZJ30N001NP-B##8320600");
        info.add("tzhxjhnt##8320600");
        info.add("TZJHJS30N001NP-B##8320600");
        info.add("建行泰州总行-建行新牧院ATM/ETN0001NP##8321200");
        info.add("tzjhth##8320600");
        info.add("TZJHZZS30N001NP-B##8320600");
        info.add("TZZLSC30N001NP-B##8320600");
        info.add("ZJ_E6114##8321100");
        info.add("ZJ_E6117##8321100");
        info.add("省建行-建设银行浦珠中路208号/ETN0001NP##8320100");
        info.add("省建行-龙袍建设银行/ETN0001NP##8320100");
        info.add("TZJHYH30N001NP-B##8320600");
        info.add("tzntjh##8320600");
        info.add("ZJ_E6112##8321100");
        info.add("ZJ_E6115##8321100");
        info.add("ZJ_E6312##8321100");
        info.add("省建设银行-六合区人民法院/ETN0001NP##8320100");
        info.add("苏州-昆山/ETN3934NP##8320500");
        info.add("ZJ_E6314##8321100");
        info.add("ZJ_E6316##8321100");
        info.add("ZJ_E6759##8321100");
        info.add("ZJ_E8018##8321100");
        info.add("ZJ_E8289##8321100");
        info.add("苏州-吴江/ETN2891NP##8320500");
        info.add("苏州-昆山/ETN4000NP##8320500");
        info.add("ZJ_E6315##8321100");
        info.add("ZJ_E6642##8321100");
        info.add("ZJ_E8017##8321100");
        info.add("ZJ_E8059##8321100");
        info.add("ZJ_G_DZZW063##8321100");
        info.add("中信银行-中信银行江阴人民中路自助点/ETN0002NP##8320200");
        info.add("中信银行巨凝-市民广场B26F/ETN0001NP##8320400");
        info.add("中信银行-张家港国泰大厦(MSTPJS1001567243)10M##8320500");
        info.add("苏州-吴江/ETN2931NP##8320500");
        info.add("中国建设银行泰州市分行-姜堰财政局/30N0001NP##8321200");
        info.add("中国建设银行股份有限公司宿迁分行##8321300");
        info.add("南通建行-如皋建行新皋桥/30N0001NP##8320600");
        info.add("南通建行-如皋建行白蒲/30N0001NP##8320600");
        info.add("南通建行-如皋建行通扬路/30N0001NP##8320600");
        info.add("南通建行-通州建行三余/30N0001NP##8320600");
        info.add("园区建行-建行兴元分理处(M40052706)4M##8320500");
        info.add("园区建行-建行星都支行(M40059596)/ETN0001NP##8320500");
        info.add("健康东路59号四楼机房至翔宇中道158号##8320800");
        info.add("区建行-园区金鸡湖路IDC二楼机房(M40052110)2M##8320500");
        info.add("南区泰山路公安局-市劳动和社会保障局/10M0001NP/MSTP##8320300");
        info.add("南通中信银行-通州中信银行/30N0001NP##8320600");
        info.add("南通中信银行-通州财政局30N001NP##8320600");
        info.add("南通建行-如皋建行丁堰/30N0001NP##8320600");
        info.add("园区建行-建行人民桥分理处(M40052752)4M##8320500");
        info.add("园区建行-建行新区支行(M40051719)10M##8320500");
        info.add("园区建行-建行胥江支行(M40052595)4M##8320500");
        info.add("南通建行-如皋建行东陈/30N0001NP##8320600");
        info.add("南通建行-如皋建行宁海路/30N0001NP##8320600");
        info.add("南通建行-如皋建行吴窑/30N0001NP##8320600");
        info.add("南通建行-如皋建行海阳路/30N0001NP##8320600");
        info.add("南通建行-如皋建行软件园自助/ETN0001NP##8320600");
        info.add("南通建行-如皋行政审批中心/ETN0001NP##8320600");
        info.add("南通建行-通州建行二甲/30N0001NP##8320600");
        info.add("园区建行(CYQJH)-建行新康分理处/ETN0001NP##8320500");
        info.add("园区建行-昆山周市支行(M40077519)2M##8320500");
        info.add("南通建行-通州建行兴东/30N0002NP##8320600");
        info.add("南通建行-通州建行刘桥/30N0001NP##8320600");
        info.add("南通建行-通州建行平潮/30N0001NP##8320600");
        info.add("南通建行-通州建行石港/30N0001NP##8320600");
        info.add("园区建行-建行人民路桃花坞支行(M40053302)/ETN0001NP##8320500");
        info.add("南通建行-通州建行兴仁/30N0001NP##8320600");
        info.add("南通建行-通州建行十总/30N0001NP##8320600");
        info.add("南通建行-通州建行开发区/30N0001NP-B##8320600");
        info.add("南门ATM--建行学府华庭分理处30N001NP##8321100");
        info.add("园区建行-交通运输局(M61702967)2M/ETN0001NP##8320500");
        info.add("园区建行-吴江流虹路嘉鸿支行(M41677965)4M##8320500");
        info.add("园区建行-吴江金色摩纺丝绸广场分理处(M40065427)/ETN0001NP##8320500");
        info.add("园区建行-太仓柳州路建行(M54357966)/ETN0001NP##8320500");
        info.add("园区建行-常熟尚湖支行(M40060913)4M##8320500");
        info.add("园区建行-建行凤凰城分理处(M40075205)4M##8320500");
        info.add("园区建行-建行城中支行(M40052571)/ETN0001NP##8320500");
        info.add("园区建行-建行斜塘支行(M40059593)4M##8320500");
        info.add("园区建行-建行新市路支行(M40052690)4M##8320500");
        info.add("园区建行-建行润元路支行(M40058178)4M##8320500");
        info.add("园区建行-建行湖畔花园分理处双光板/ETN0001NP##8320500");
        info.add("园区建行-建行越溪分理处(M40053034)4M##8320500");
        info.add("园区建行-建行车坊支行(M40064193)4M##8320500");
        info.add("园区建行-张家港锦丰支行(M40077039)4M##8320500");
        info.add("园区建行-昆山市新镇镇?子路/ETN0001NP##8320500");
        info.add("园区建行-昆山金浦路捷强超市ATM(M40487959)2M##8320500");
        info.add("园区建行-昆山金融街财政(M56367964)2M##8320500");
        info.add("建信人寿保险-天都大厦建信人寿保险/ETN0001NP##8320500");
        info.add("建信人寿保险有限公司江苏分公司(M00253299)/N001##8320400");
        info.add("宿迁市建设银行-沭阳建设银行迎宾支行/ETN0001NP##8321300");
        info.add("市国土资源局（市民中心）-建设银行/ETN0001NP##8320200");
        info.add("市建行（CJH）-大厦（LS318）/30N0001NP/PRI##8320800");
        info.add("常州常州ETN6008NPH##8320400");
        info.add("建行健康支行-房产大厦/30N0001NP##8320800");
        info.add("建行姜堰总行-姜堰公积金管理中心/30N0001PG##8321200");
        info.add("建行-建行吴江横扇支行(M40057973)4M##8320500");
        info.add("建行泰州总行-兴化建行中医院ATM/ETN0001NP##8321200");
        info.add("建行泰州总行-姜堰天然气公司/ETN0001NP##8321200");
        info.add("建行泰州总行-建行牧院生活区ATM/ETN0001NP##8321200");
        info.add("建行泰州总行-泰州四人医7楼/ETN0001NP##8321200");
        info.add("建行泰州总行-铁塔路中国税务点/ETN0001NP##8321200");
        info.add("建行泰州总行-靖江财政局/ETN0001NP##8321200");
        info.add("建行泰州总行-高港区财政局/ETN0001NP##8321200");
        info.add("建设银行-建行大学城纺院/ETN0003NP##8320400");
        info.add("建设银行-新区财政局（建行外联）/ETN0001NP##8320200");
        info.add("建设银行-江北新区市民中心建行自助区/ETN0001NP##8320100");
        info.add("建设银行淮安分行-建行直属分理处/ETN0001NP##8320800");
        info.add("建设银行淮安分行-清浦财政支付中心/30N0001NP##8320800");
        info.add("建设银行镇江分行-镇江电信公司30N001NP##8321100");
        info.add("徐州市建信人寿公司-沛县建信人寿ETN0001NP##8320300");
        info.add("泰兴中信银行-住房公积金/ENT0001NP##8321200");
        info.add("泰州兴化STN000144##8321200");
        info.add("泰州泰兴STN000071##8321200");
        info.add("泰州泰州STN000305##8321200");
        info.add("泰州靖江STN000087##8321200");
        info.add("省建行-建行江宁水务集团/ETN0001NP##8320100");
        info.add("建设银行淮安分行-新奥燃气天门站/30N0001NP##8320800");
        info.add("徐州市建设银行-建行丰县财政局/ETN0001NP##8320300");
        info.add("承德路与淮海东路交叉口 市分行科技中心机房至承德南路60号军民小学南侧，烟草局办公楼##8320800");
        info.add("泰州姜堰STN000106##8321200");
        info.add("房改办-建行靖江总行/30N0002NP##8321200");
        info.add("承德路与淮海东路交叉路口建行科技部机房至承德北路闸北派出所门面##8320800");
        info.add("泰州兴化STN000049##8321200");
        info.add("泰州兴化STN000129##8321200");
        info.add("泰州兴化STN000148##8321200");
        info.add("泰州泰兴STN000057##8321200");
        info.add("泰州泰兴STN000150##8321200");
        info.add("泰州泰州STN000064##8321200");
        info.add("泰州泰州STN000333##8321200");
        info.add("泰州泰州STN000604##8321200");
        info.add("泰州靖江STN000085##8321200");
        info.add("清河广场-建设银行淮安分行/30N0003NP##8320800");
        info.add("清河广场-淮阴区小营/30N0501NP##8320800");
        info.add("省建行-南京红十字医院/ETN0001NP##8320100");
        info.add("省建行-天元东路支行/ETN0001NP##8320100");
        info.add("省建设银行-弘业期货中华路50号/ETN0001NP##8320100");
        info.add("苏州-吴江/ETN2738NP##8320500");
        info.add("苏州-吴江/ETN2892NP##8320500");
        info.add("苏州-吴江/ETN2908NP##8320500");
        info.add("苏州-吴江/ETN2944NP##8320500");
        info.add("EA10653001##8320100");
        info.add("泰州泰兴STN000085##8321200");
        info.add("镇江市建设银行-丹阳市建设银行开发区支行/30N0001NP##8321100");
        info.add("镇江市建设银行-镇江市人民银行30N001NP##8321100");
        info.add("EA091971001##8320100");
        info.add("EA121303001##8320100");
        info.add("**********##8320100");
        info.add("ZJ_E6770##8321100");
        info.add("ZJ_E6687##8321100");
        info.add("南通建行-如皋建行下原/30N0001NP##8320600");
        info.add("市建设银行-市住房公积金/ETN0001NP##8320300");
        info.add("**********##8320100");
        info.add("**********##8320100");
        info.add("DHCDL-45##8321100");
        info.add("中信银行－仪征大众联合自助/ETN0001NP##8321000");
        info.add("中信银行-仪征财政局/30N0001NP##8321000");
        info.add("市公积金管理中心-省建行/ETN0001NP##8320100");

        for (int i = 0; i < info.size(); i++) {

            String inf = info.get(i);


            new Thread( () -> {
                try {
                    /**
                     * {
                     *     "businessParam": {
                     *         "apiId": 761849516174114,
                     *         "cjType": "MSTP接入",
                     *         "custmor": "32413241",
                     *         "requestId": "123124",
                     *         "sendTime": "2022/9/31 16:52:27",
                     *         "circuitCode": null,
                     *         "isLayered": null
                     *     },
                     *     "pageNum": 0,
                     *     "pageSize": 1000,
                     *     "whereBody": [
                     *         {
                     *             "nodeLabel": "传输电路服务",
                     *             "propertyLabel": "area_code",
                     *             "propertyValue": "8320100"
                     *         },
                     *         {
                     *             "nodeLabel": "传输电路服务",
                     *             "propertyLabel": "code",
                     *             "propertyValue": "省建行-建设银行溧水东城汇商业街营业部/ETN0001NP"
                     *         },{
                     *             "nodeLabel": "传输电路服务",
                     *             "propertyLabel": "access_code",
                     *             "propertyValue": "M08596162"
                     *         },
                     *         {
                     *             "nodeLabel": "产品",
                     *             "propertyLabel": "crm_product_id",
                     *             "propertyValue": "100005077841"
                     *         }
                     *     ]
                     * }
                     */
                    System.out.println("info==================" + inf);
                    JSONObject param = new JSONObject();
                    JSONObject businessParam = new JSONObject();
                    businessParam.put("apiId","780816022");
                    businessParam.put("cjType","MSTP接入");
                    businessParam.put("custmor","32413241");

                    businessParam.put("sendTime","2022/9/31 16:52:27");
                    businessParam.put("requestId",new Date().getTime());
                    JSONArray whereBody = new JSONArray();
                    JSONObject area_code = new JSONObject();
                    area_code.put("nodeLabel","传输电路服务");
                    area_code.put("propertyLabel","area_code");
                    area_code.put("propertyValue",inf.split("##")[1]);

                    JSONObject code = new JSONObject();
                    code.put("nodeLabel","传输电路服务");
                    code.put("propertyLabel","code");
                    code.put("propertyValue",inf.split("##")[0]);


                    JSONObject crm_product_id = new JSONObject();
                    crm_product_id.put("nodeLabel","传输电路服务");
                    crm_product_id.put("propertyLabel","crm_product_id");
                    crm_product_id.put("propertyValue","100005077841");

                    whereBody.add(code);
                    whereBody.add(area_code);
                    whereBody.add(crm_product_id);

                    param.put("businessParam", businessParam);
                    param.put("whereBody", whereBody);



                    String res = HttpRequestsUtils.sendPost("http://nrm.oss.telecomjs.com:39049/graph-rest-api/js/queryTopoCustomer",
                            new HashMap<>(),param);
                    System.out.println("线程====================>" + inf + "=============>" + res.substring(0,40));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }).start();




            String code = info.get(i);
            System.out.println( i +" code======"+code +"开始");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("propertyValue", code.split("##")[0]);
            jsonObject.put("areaCode", "8320200");
            PageResponse<JSONObject> codeResult = iPRanJsDao.getgraphresults(jsonObject, 10, 0, "ds_graph_js");

            if (CollectionUtils.isEmpty(codeResult.getData())) {
                System.out.println( i +" code======"+code +"不存在");
                continue;
            }
            JSONObject result =  codeResult.getData().get(0).getJSONObject("results");
            String res = result.toJSONString();
            res = res.replace("软跳","硬跳");
            res = res.replace("1132100001","1131100001");
            JSONObject param = new JSONObject();
            param.put("code",  code.split("##")[0]);
            param.put("region",  code.split("##")[1]);
            param.put("results",res);
            this.circuitAnalysisDao.saveCodeResult(param, NRMConstants.SHARDING_GRAPH_DB);
            System.out.println("code======"+code +"完成");
        }

        return "finish";


    }

    @GetMapping("/circuit/circuitValidate/testAnalysis")
    public String testAnalysis() {
        PmProductLinkAnalysis pmProductLinkAnalysis = new PmProductLinkAnalysis();
        pmProductLinkAnalysis.setServiceId(new BigDecimal("321122820000000115929204"));
        pmProductLinkAnalysis.setRegionId(new BigDecimal("321122930000000000000008"));
        pmProductLinkAnalysis.setProductType("数字电路");
        pmProductLinkAnalysis.setLinkId(new BigDecimal("321122060000000195556223")); //北京苏州ETN2150NP
        this.productLinkAnalysisCoreService.analysis(pmProductLinkAnalysis);
        return "success";
    }

}
