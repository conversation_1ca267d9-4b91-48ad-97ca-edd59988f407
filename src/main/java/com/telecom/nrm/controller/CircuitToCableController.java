package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.dao.CircuitsToCableDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api/circuits")
public class CircuitToCableController {


    @Autowired
    CircuitsToCableDao circuitsToCableDao;

    @PostMapping("circuitstocable")
    public ResponseEntity<List<JSONObject>> CircuitsToCable(@RequestBody JSONObject request) {

        JSONObject param = new JSONObject();

        if(ObjectUtil.isNotEmpty(request.getString("circuit_ids")))
        {
            param.put("circuit_ids", Arrays.asList(request.getString("circuit_ids").split(",")));
        }
        if(ObjectUtil.isNotEmpty(request.getString("circuit_codes")))
        {
            param.put("circuit_codes", Arrays.asList(request.getString("circuit_codes").split(",")));        }


        String sharding_code = "ds_odso_"+ request.getString("city");
        JSONObject response = circuitsToCableDao.CircuitsToCable(param,  sharding_code);
        List<JSONObject> data= JSONObjectUtil.jsonObjectArrayToList( response.getJSONArray("data"));
        return ResponseEntity.ok(data);


    }
    @PostMapping("circuitstofacility")
    public ResponseEntity<List<JSONObject>> CircuitsToFacility(@RequestBody JSONObject request) {

        JSONObject param = new JSONObject();
        List<JSONObject> jsonList = new ArrayList<>();

        String circuit_codes = request.getString("circuit_codes");
        String delimiter = ",";
        String[] jsonStrings = circuit_codes.split(delimiter);

        param.put("circuit_codes", jsonStrings);
        String sharding_code = "ds_odso_"+ request.getString("city");
        List<JSONObject> response = circuitsToCableDao.CircuitsToFacility(param, sharding_code);
        return ResponseEntity.ok(response);


    }




}

