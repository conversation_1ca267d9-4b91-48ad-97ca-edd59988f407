package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.aop.LogAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 分光器预测控制器
 * 处理前端的预测相关API调用
 */
@RestController
@RequestMapping("/api/splitter_predict")
@Slf4j
public class SplitterPredictController {

    @Autowired
    SplitterPortController splitterPortController;

    /**
     * 前端调用: POST /api/splitter_predict/batch
     * 批量预测（兼容前端路径）
     */
    @PostMapping("/batch")
    @LogAnnotation(interfaceName = "分光器端口预警-批量预测兼容接口")
    public ResponseEntity<List<JSONObject>> batchPredict(@RequestBody JSONObject request) {
        log.info("SplitterPredictController.batchPredict - request:{}", request);
        
        // 委托给主控制器处理
        return splitterPortController.batchPredict(request);
    }
}
