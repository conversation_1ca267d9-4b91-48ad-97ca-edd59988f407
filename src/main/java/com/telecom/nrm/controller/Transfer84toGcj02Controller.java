package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.security.NoSuchAlgorithmException;

@RestController
@RequestMapping("/api/transfer84toGcj02Controller")
@Slf4j
public class Transfer84toGcj02Controller {

    @Autowired
    GetAkController getAkController;

    private WebClient webClient = WebClient.builder()
            .baseUrl("http://10.143.25.248:21000/gisIntf/convertCoord84toGCJ02")
            .build();

    @GetMapping("")
    public JSONObject doAPI(@RequestParam String lngx,@RequestParam String lngy,@RequestParam(value = "Ak", required = false) String Ak) throws NoSuchAlgorithmException {
        JSONObject akInfo = getAkController.doAPI();
        if(ObjectUtil.isEmpty(Ak))
        {
             Ak  = akInfo.getString("ak");
        }
        String url=  String.format("?ak=%s&lngx=%s&lngy=%s", Ak, lngx,lngy);
        // 样例"ak=MTcwMzcMTAwMU1UY3dNemN5T1Rnek9EZ3dOeU0yT1RJMA__&lngx=118.790577&lngy=32.05738";
        Mono<JSONObject> result = webClient
                .get() // 指定为GET请求
                .uri(url) // 设置请求的URL
                .retrieve()
                .bodyToMono(JSONObject.class);
        JSONObject response= result.block();
        log.info("doAPI return "+ response);
        return response; // 返回Mono<ResponseEntity<String>>对象，表示响应体内容为String类型，状态码为200的响应体
    }

}






