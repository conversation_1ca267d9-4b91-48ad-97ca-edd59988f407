package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.DSJJDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.MstpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/dsjj")
@Slf4j
public class DSJJController {

    @Autowired
    MstpService mstpService;

    @Autowired
    DSJJDao dsjjDao;

    @Autowired
    GraphApiService graphApiService;
    public static final Integer STATE_WAIT = 0;
    public static final Integer STATE_SUCCESS = 1;
    public static final Integer STATE_FAIL = 2;
    public static final Map<String, String> areaMap = new HashMap<String, String>() {
        {
            put("南京市", "nj");
            put("苏州市", "sz");
            put("南通市", "nt");
            put("扬州市", "yz");
            put("连云港市", "lyg");
            put("常州市", "cz");
            put("无锡市", "wx");
            put("宿迁市", "sq");
            put("镇江市", "zj");
            put("盐城市", "yc");
            put("泰州市", "tz");
            put("淮安市", "ha");
            put("徐州市", "xz");
        }
    };

    private String translateCity (String city) {
        if (!ObjectUtil.isEmpty(city) && city != "") {
            return areaMap.get(city);
        }else{
            return "nj";
        }
    }


    @RequestMapping("/refresh-res-info")
    public ResponseEntity<JSONObject> resInfo(@RequestBody JSONObject request) {
        String id= request.getString("id");
        JSONObject result = new JSONObject();
        if (ObjectUtil.isEmpty(id)) {
            return ResponseEntity.ok().body(request);
        }
        String city=translateCity(request.getString("city"));
        String type=request.getString("line_type");
        String code = request.getString("line_no");
        if (ObjectUtil.isEmpty(city)) city="nj";
        if (ObjectUtil.isEmpty(code)) code="========";
        String shardingCode = "ds_bc_o3_"+ city.toLowerCase();
        if (!ObjectUtil.isEmpty(type)) {
            if (type.equals("数字电路") || type.equals("OTN")) {
                result = mstp(city, code, shardingCode);
            } else if (type.equals("VPN")) {
                result = vpn(city, code, shardingCode);
            }
        }
        JSONObject dateRate = dsjjDao.getDateRateByAccessCode(code, shardingCode);
        if (!ObjectUtil.isEmpty(dateRate)) {
            request.put("open_date", dateRate.getString("open_date"));
            request.put("rate", dateRate.getString("rate"));
        }
        result.put("id", request.getInteger("id"));
        request.put("res_info", result.toJSONString());
        request.put("status", result.getInteger("status"));
        dsjjDao.updateResult(request,"ds_graph_js","");

        return ResponseEntity.ok(result);
    }

    @RequestMapping("/query-kb")
    public BiyiPageResult<JSONObject> getList(@RequestBody JSONObject param , BiyiPageRequest pageable) {
        log.debug("REST request to get entity list");
        // JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse =  dsjjDao.pageQuery(param ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        pageResponse.getData().forEach(d->{
            String resInfo = d.getString("res_info");
            d.put("res_info", JSONObject.parseObject(resInfo));
        });
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    @RequestMapping("/query-invalid-kb")
    public ResponseEntity<List<JSONObject>> queryInValidKB(@RequestBody JSONObject request, BiyiPageRequest pageable) {
        PageResponse<JSONObject> response= dsjjDao.pageQueryInvalid(request, pageable.getSize(), pageable.getPage(), "ds_graph_js");
        List<JSONObject> data= response.getData();
        return ResponseEntity.ok(data);
    }

    @RequestMapping("/query-room")
    public BiyiPageResult<JSONObject> getRoomInfoByPort(@RequestBody JSONObject request, BiyiPageRequest pageable) {
        if (ObjectUtil.isEmpty(request) || request.isEmpty() || ObjectUtil.isEmpty(request.getJSONArray("portIds"))){
            return new BiyiPageResult(new ArrayList<JSONObject>(), pageable.getSize(), pageable.getPage());
        }
        String city=translateCity(request.getString("city"));
        String shardingCode = "ds_bc_o3_"+ city.toLowerCase();
        PageResponse<JSONObject> roomInfo = dsjjDao.getRoomInfoByPort(request, pageable.getSize(), pageable.getPage(), shardingCode);
        return new BiyiPageResult(roomInfo.getData(), roomInfo.getPageInfo().getTotalCount() , roomInfo.getPageInfo().getTotalCount());
    }

    @RequestMapping("/query-line")
    public JSONObject getLineInfoByAccessCode(@RequestBody JSONObject request, BiyiPageRequest pageable) {
        JSONObject result = new JSONObject();
        if (ObjectUtil.isEmpty(request) || request.isEmpty() || ObjectUtil.isEmpty(request.getString("access_code"))) {
            result.put("message","缺少入参");
            return result;
        }
        String city = translateCity(request.getString("city"));
        String shardingCode = "ds_bc_o3_" + city.toLowerCase();
        String access_code = request.getString("access_code");
        String linkID = dsjjDao.getCircuitCodeByAccessCode(access_code, shardingCode);
        if (ObjectUtil.isEmpty(linkID)){
            result.put("message","未找到该条专线");
            return result;
        }
        result.put("province", "江苏省");
        result.put("city", request.getString("city"));
        result.put("access_code", access_code);
        PageResponse<JSONObject> subLinks = dsjjDao.getSubLinksByLinkID(linkID, 15, 0, shardingCode);
        List<JSONObject> links = subLinks.getData();
        JSONObject qryRoom = new JSONObject();
        ArrayList<BigDecimal> portIDs = new ArrayList<>();
        qryRoom.put("portIds", portIDs);
        PageResponse<JSONObject> pageResponse = dsjjDao.queryCircuitByCircuitCode(linkID, 15, 0, shardingCode);
        List<JSONObject> circuitAllDevice = pageResponse.getData();
        //去掉重复设备
        Map<String, Integer> deviceMap = new HashMap<>();
        for (JSONObject cur: circuitAllDevice) {
            if (!deviceMap.containsKey(cur.getString("device_id"))) {
                deviceMap.put(cur.getString("device_id"), 1);
                portIDs.add(new BigDecimal(cur.getString("port_id")));
            }
        }
        JSONObject dateRate = dsjjDao.getDateRateByAccessCode(access_code, shardingCode);
        if (!ObjectUtil.isEmpty(dateRate)) {
            result.put("open_date", dateRate.getString("open_date"));
            result.put("rate", dateRate.getString("rate"));
        }
        JSONObject lineType = dsjjDao.getLineTypeByAccessCode(access_code, shardingCode);
        if (!ObjectUtil.isEmpty(lineType)) {
            result.put("access_type", lineType.getString("name"));
            result.put("access_type_code", lineType.getString("code"));
            if (lineType.getString("code").equals("14")) {
                //用专线以太网链路的A端设备往上查PON链路
                JSONObject ponlink = dsjjDao.getPonLinksByLinkID(linkID, shardingCode);
                if (!ObjectUtil.isEmpty(ponlink)){
                    links.add(ponlink);
                    portIDs.add(new BigDecimal(ponlink.getString("a_port_id")));
                }
            }
        }
        PageResponse<JSONObject> roomInfoByPort = dsjjDao.getRoomInfoByPort(qryRoom, portIDs.size(), 0, shardingCode);
        List<JSONObject> roomInfo = roomInfoByPort.getData();
        result.put("links", links);
        result.put("devices", roomInfo);
        return result;
    }

    public JSONObject mstp(String city,String code, String shardingCode) {
        JSONObject result =  new JSONObject();
//        if (ObjectUtil.isEmpty(city)) city="nj";
//        if (ObjectUtil.isEmpty(code)) code="========";
//        String shardingCode = "ds_bc_o3_"+ city.toLowerCase();
        String linkID = dsjjDao.getCircuitCodeByAccessCode(code, shardingCode);
        if (ObjectUtil.isEmpty(linkID) || linkID == ""){
            List<JSONObject> data = new ArrayList<>();
            result.put("data", data);
            result.put("status", STATE_FAIL);
            return result;
        }
        JSONObject resp = dsjjDao.getCircuitCodeByLinkId(linkID, shardingCode);
        if (!ObjectUtil.isEmpty(resp) && !resp.isEmpty()) {
            String circuitCode = resp.getString("code");
            result.put("circuit_code",circuitCode);
        } else {
            result.put("circuit_code",code);
        }
        PageResponse<JSONObject> pageResponse = dsjjDao.queryCircuitByCircuitCode(linkID, 10, 0, shardingCode);
        List<JSONObject> data = pageResponse.getData();
        result.put("data", data);
        if (ObjectUtil.isEmpty(data) || data.isEmpty()) {
            result.put("status", STATE_FAIL);
        } else {
            result.put("status", STATE_SUCCESS);
        }
        return result;
    }

    public JSONObject vpn(String city,String code, String shardingCode) {

//        if (ObjectUtil.isEmpty(city)) city="nj";
//        if (ObjectUtil.isEmpty(code)) code="========";
//        String shardingCode = "ds_bc_o3_"+ city.toLowerCase();
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_cfs_2_pon").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cfs","access_code",code)).build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject result = new JSONObject();
        result.put("status", STATE_SUCCESS);
        result.put("access_code",code);
        try {
            String address = graph.getNodes().stream().filter(n -> n.getId().equals("address")).findFirst().get().getData().get(0).getString("full_name");
            result.put("address", address);
        }catch (Exception ex){
            result.put("address", "");
            result.put("status", STATE_FAIL);
            log.error(ex.getMessage());
        }
        List<JSONObject> data = new ArrayList<>();
        JSONObject oltInfo = new JSONObject();
        try {
            JSONObject olt = graph.getNodes().stream().filter(n -> n.getId().equals("olt")).findFirst().get().getData().get(0);
            oltInfo.put("device_name", olt.get("name"));
            oltInfo.put("device_code", olt.get("code"));
            oltInfo.put("device_nm_code", olt.get("nm_code"));
            oltInfo.put("device_ip", olt.get("device_ip"));
            oltInfo.put("device_id", new BigDecimal(olt.get("id").toString()));
            oltInfo.put("device_spec_id", olt.get("spec_id"));
        }catch (Exception ex){
            log.error(ex.getMessage());
        }

        try {
            JSONObject olt = graph.getNodes().stream().filter(n -> n.getId().equals("pon_port")).findFirst().get().getData().get(0);
            oltInfo.put("port_code", olt.get("code"));
            oltInfo.put("port_nm_code", olt.get("nm_code"));
            oltInfo.put("port_name", olt.get("name"));
            oltInfo.put("port_id", new BigDecimal(olt.get("id").toString()));
        }catch (Exception ex){
            log.error(ex.getMessage());
        }
        if (!oltInfo.isEmpty()) {
            data.add(oltInfo);
        }
        String linkID = dsjjDao.getCircuitCodeByAccessCode(code, shardingCode);
        if (ObjectUtil.isEmpty(linkID) || linkID == ""){
            result.put("data", data);
            result.put("status", STATE_FAIL);
            return result;
        }
        PageResponse<JSONObject> pageResponse = dsjjDao.queryCircuitByCircuitCode(linkID, 10, 0, shardingCode);
        List<JSONObject> circuitAllDevice = pageResponse.getData();
        data.addAll(0, circuitAllDevice);
        result.put("data", data);
        if (ObjectUtil.isEmpty(data) || data.isEmpty()) {
            result.put("status", STATE_FAIL);
        } else {
            result.put("status", STATE_SUCCESS);
        }
        return result;
    }

}
