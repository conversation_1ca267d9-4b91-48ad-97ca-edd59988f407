package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.CryptAES;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.constant.Constant;
import com.telecom.nrm.dao.CutoverDao;
import com.telecom.nrm.dao.GroupFaultRapidPositioningDao;
import com.telecom.nrm.dao.InfluenceAnalysisDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.ProjectInfluenceService;
import com.telecom.nrm.service.QueryCfsEffectService;
import com.telecom.nrm.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import cn.hutool.core.util.StrUtil;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@RestController
@RequestMapping("/api/influenceanalysis")
@Slf4j
public class InfluenceAnalysisController {



    final String aseCryKey = "tfcust";
    @Autowired
    QueryCfsEffectService queryCfsEffectService;
    @Autowired
    InfluenceAnalysisDao influenceAnalysisDao;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    ProjectInfluenceService projectInfluenceService;

    @Autowired
    CutoverDao cutoverDao;

    @PostMapping("query85bastoolt")
    @LogAnnotation( interfaceName="影响分析 query85bastoolt 接口-影响分析查询85表从bas到olt信息")
    public JSONObject query85bastoolt(@RequestBody JSONObject request) {
        JSONObject param = new JSONObject();
        int pageSize;
        int currentPage;
        //int pageSize = request.getInteger("pageSize");
        pageSize = 10000;
        //int currentPage = request.getInteger("currentPage");
        currentPage = 1;

        param.put("main_bras_code", request.get("main_bras_code"));
        param.put("main_bras_code", request.get("main_bras_code"));

        PageResponse<JSONObject> response = influenceAnalysisDao.query85bastoolt(param, pageSize, currentPage, "ds_net_resource_js");

        List<JSONObject> data = response.getData();

        JSONObject result = new JSONObject();


        result.put("oltlist", data);


        return result;
    }


    @PostMapping("getresourceinfo")
    @PreAuthorize("hasAuthority('资源影响分析导出（菜单）')")
    @LogAnnotation( interfaceName="影响分析 getresourceinfo 接口-获取影响分析")
    public JSONObject getresourceinfo(@RequestBody JSONObject request) {


        //调用pon口检测
        String devCode = request.getString("devCode");

        String shardingCode = "ds_bc_o3_" + request.getString("city");

        GraphRequestBuilder builder = new GraphRequestBuilder();

        GraphRequest graphRequest = builder.setApiId("query_influence_cbl").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cable_segment", "code", devCode)).build();
        Graph graph = graphApiService.doApi(graphRequest);

        //如果不配置一树一图场景的导出情况，就用这个
        //   List<JSONObject> CompleteTree =  graphService.buildCompleteTree(graph);

        //常州需求，设置好一树一图场景以及buildtree的方式以后建的树
        List<JSONObject> CompleteTree = buildCblTree(graph);


        JSONObject frontendFormat = convertToFrontendFormat(CompleteTree);

        //得到ID到name的转换
        Set<Node> Nodes = graph.getNodes();
        Map<String, String> nodeNameMap = new HashMap<>();
        for (Node node : Nodes) {
            nodeNameMap.put(node.getId(), node.getName());
        }

        //排序边边
        List<Edge> sortdges = graph.sortEdgesBFS();


        //获取节点的ID放进去
        List<String> sortedNodeIds = new ArrayList<>();
        for (Edge edge : sortdges) {
            String source = edge.getSource();
            String target = edge.getTarget();
            if (!sortedNodeIds.contains(source)) {
                sortedNodeIds.add(source);
            }
            if (!sortedNodeIds.contains(target)) {
                sortedNodeIds.add(target);
            }
        }

        //转换为name
        List<String> nodeNameList = new ArrayList<>();
        for (String nodeId : sortedNodeIds) {
            nodeNameList.add(nodeNameMap.get(nodeId));
        }


        frontendFormat.put("tagsData", nodeNameList);

        return frontendFormat;


    }

    @PostMapping("getExternalForcePipeResourceInfo")
    @LogAnnotation( interfaceName="影响分析 getExternalForcePipeResourceInfo 接口-获取外力点影响的管线资源信息")
    public JSONObject getExternalForcePipeResourceInfo(@RequestBody JSONObject request) {


        String devCode = request.getString("pipecode");

        String shardingCode = "ds_bc_o3_" + request.getString("city");

        GraphRequestBuilder builder = new GraphRequestBuilder();

        GraphRequest graphRequest = builder.setApiId("external_force_influence").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("pipe_segment", "code", devCode)).build();
        Graph graph = graphApiService.doApi(graphRequest);

        //如果不配置一树一图场景的导出情况，就用这个
        //   List<JSONObject> CompleteTree =  graphService.buildCompleteTree(graph);

        //常州需求，设置好一树一图场景以及buildtree的方式以后建的树
        List<JSONObject> CompleteTree = buildPipeTree(graph);


        JSONObject frontendFormat = convertToFrontendFormat(CompleteTree);


        //得到ID到name的转换
        Set<Node> Nodes = graph.getNodes();
        Map<String, String> nodeNameMap = new HashMap<>();
        for (Node node : Nodes) {
            nodeNameMap.put(node.getId(), node.getName());
        }

        //排序边边
        List<Edge> sortdges = graph.sortEdgesBFS();


        //获取节点的ID放进去
        List<String> sortedNodeIds = new ArrayList<>();
        for (Edge edge : sortdges) {
            String source = edge.getSource();
            String target = edge.getTarget();
            if (!sortedNodeIds.contains(source)) {
                sortedNodeIds.add(source);
            }
            if (!sortedNodeIds.contains(target)) {
                sortedNodeIds.add(target);
            }
        }

        //转换为name
        List<String> nodeNameList = new ArrayList<>();
//        for(String nodeId : sortedNodeIds){
//            nodeNameList.add(nodeNameMap.get(nodeId));
//        }

        nodeNameList.add("管道段");
        nodeNameList.add("光缆段");
        nodeNameList.add("光路");
        nodeNameList.add("PON端口");
        nodeNameList.add("olt");


        frontendFormat.put("tagsData", nodeNameList);

        return frontendFormat;


    }

    public void setRouteInflucnceIntoPmTB(String entity_spec_id, String entity_code, String city) {

        /*
        尝试解决常州反馈多查出CFS的问题
        JSONObject routeRequest = new JSONObject();
        List<JSONObject> routelist = null;

        routeRequest.put("entity_spec_id", entity_spec_id);
        routeRequest.put("entity_code", entity_code);

        routelist = projectInfluenceService.resourceInfluenceRoute(routeRequest, city);

        System.out.println(routelist);
        //todo:转化为list<string>
        List<String> routeresult = routelist.stream()
                .map(json -> json.getString("route_code"))
                .collect(Collectors.toList());

        projectInfluenceService.influenceRouteCarryCFS(routeresult, city);

         */


    }


    public JSONObject getResourceinFluence(@RequestBody JSONObject request) {
        String city = request.getString("city");
        String devCode = request.getString("devCode");
        String devType = request.getString("devType");
        String ip_addr = request.getString("ip_addr");


        //todo:查出影响的光路信息，用这个resourceInfluenceRoute
        // 之后调用ProjectInfluenceService的influenceRouteCarryCFS  (List<String> routeCodes, String city)方法


        JSONObject response;

        JSONObject param = new JSONObject();

        String sharding_code = "ds_odso_" + city;


        //case里面的中文都是一树一图标签名称
        switch (devType) {
            case "cbl_sect":
            case "光缆段":
                param.put("cbl_sect", devCode);

                setRouteInflucnceIntoPmTB("1121000002", devCode, city);

                response = influenceAnalysisDao.query_cfs_influence_by_cbl_sect_code(param, sharding_code);

                break;

            case "cbl_sect_id":

                param.put("cbl_sect_id", devCode);


                response = influenceAnalysisDao.query_cfs_influence_by_cbl_sect_code(param, sharding_code);

                break;


            case "fiber_busi_node":


//                param.put("fiber_busi_node",devCode);
//                response = influenceAnalysisDao.query_cfs_influence_by_fiber_busi_node(param,sharding_code);
//

                //一树一图的逻辑
                response = getFiberRouteCfsList(request);


                break;


            case "光路":
                //todo 这里要修改为一树一图的接口，才能查的全：

//
//                param.put("fiber_busi_id",devCode);
//
//                //检查入参
//
//                response = influenceAnalysisDao.query_cfs_influence_by_fiber_busi_node(param,sharding_code);


                //一树一图的逻辑
                response = getFiberRouteCfsList(request);


                break;

            case "net_code":
            case "光缆":

                setRouteInflucnceIntoPmTB("1211200002", devCode, city);

                param.put("net_code", devCode);
                response = influenceAnalysisDao.query_cfs_influence_by_cm_net(param, sharding_code);

                JSONArray cfsInfluenceList = response.getJSONArray("cfs_influence");
                int size = cfsInfluenceList.size();
                for (int i = 0; i < size; i++) {
                    JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(i);
                    cfsInfluence.put("route_code", cfsInfluence.getString("opt_road_code"));
                    cfsInfluence.put("route_name", cfsInfluence.getString("opt_road_name"));
                    cfsInfluence.put("line_no", cfsInfluence.getString("jx_code"));
                    cfsInfluence.put("a_road_device_name", cfsInfluence.getString("a_road_device_name"));
                    cfsInfluence.put("z_road_device_name", cfsInfluence.getString("z_road_device_name"));
                    cfsInfluence.put("a_road_port_code", cfsInfluence.getString("a_road_port_code"));
                    cfsInfluence.put("z_road_port_name", cfsInfluence.getString("z_road_port_name"));

                    cfsInfluence.put("a_cable_device_name", cfsInfluence.getString("a_cable_device_name"));
                    cfsInfluence.put("z_cable_device_name", cfsInfluence.getString("z_cable_device_name"));
                    cfsInfluence.put("a_cable_port_code", cfsInfluence.getString("a_cable_port_code"));
                    cfsInfluence.put("z_cable_port_name", cfsInfluence.getString("z_cable_port_name"));
                }


                break;
            case "pipe_code":


                param.put("pipe_code", devCode);
                response = influenceAnalysisDao.query_cfs_influence_by_cbl_sect_code(param, sharding_code);


                break;

            case "管道段":
                //todo
                param.put("pipe_id", devCode);
                response = influenceAnalysisDao.query_cfs_influence_by_cbl_sect_code(param, sharding_code);


                break;

            case "olt":


                setRouteInflucnceIntoPmTB("1028200001", devCode, city);

                if (ObjectUtil.isNotEmpty(ip_addr) && !ObjectUtil.equal(ip_addr, "")) {
                    param.put("ip_addr", ip_addr);
                    response = influenceAnalysisDao.query_cfs_influence_by_device_code_v2(param, sharding_code);

                } else {

                    //todo
                    param.put("code", devCode);

                    response = influenceAnalysisDao.query_cfs_influence_by_device_code_v2(param, sharding_code);

                }

                break;
            case "card":


                param.put("ip_addr", ip_addr);
                param.put("card_no", devCode);

                response = influenceAnalysisDao.query_cfs_influence_by_device_code_v2(param, sharding_code);


                break;
            case "pon":

                //todo

                if (ObjectUtil.isNotEmpty(ip_addr)) {

                    param.put("ip_addr", ip_addr);
                    param.put("pon_code", devCode);

                }
                response = influenceAnalysisDao.query_cfs_influence_by_pon_code(param, sharding_code);
                break;

            case "pon端口":


                param.put("port_id", devCode);
                response = influenceAnalysisDao.query_cfs_influence_by_port_id(param, sharding_code);


                break;


            case "obd":
                setRouteInflucnceIntoPmTB("1020200006", devCode, city);


            default:


                param.put("code", devCode);

                response = influenceAnalysisDao.query_cfs_influence_by_device_code_v2(param, sharding_code);


                break;


        }

        getLifeLineFlag(response, city);   // 查询生命线业务标志

        getCustomerManager(response, city); // 查询客户经理

        get30DayTimes(response); // 查询30天内割接的次数

        getCircuitCode(response, city);


        return response;
    }


    public JSONObject getFiberRouteCfsList(JSONObject request) {
        JSONObject result = new JSONObject();

        String routeCode = request.getString("devCode");

        String city = request.getString("city");
        String shardingCode = "ds_bc_o3_" + city.toLowerCase();

//        List<JSONObject> carryList = new ArrayList<>();

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("opt_road_effect").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("opt_road", "code", routeCode)).build();

        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject graphData = graphService.getData(graph);
//        graphService.buildTree(graphData, "光路", "cfs", "cfs");
        List<JSONObject> cfsList = graphService.filterNodeByLabel(graphData, "cfs");

        List<JSONObject> newcfsList = new ArrayList<>();

        if (cfsList.size() > 0) {
            for (int i = 0; i < cfsList.size(); i++) {

                JSONObject cfs = cfsList.get(i);

                JSONObject newcfs = new JSONObject();


                newcfs.put("accs_nbr_no", cfs.getString("code"));
                newcfs.put("service_name", cfs.getString("name"));
                newcfs.put("crm_prod_id", cfs.getString("crm_product_id"));
                newcfs.put("service_spec_name", cfs.getInteger("spec_id").toString());
                newcfs.put("customer_level_name", cfs.getString("customer_level_name"));
                newcfs.put("cust_name", cfs.getString("cust_name"));


                newcfsList.add(newcfs);

            }

        }
        result.put("cfs_influence", newcfsList);

        return result;


    }


    public void getLifeLineFlag(JSONObject response, String city) {
        // 关联生命线业务
        String areaName = NRMConstants.AREA_NAME_MAP.get(city);
        JSONObject param = new JSONObject();
        param.put("area_name", areaName);
        PageResponse<JSONObject> lifePage = influenceAnalysisDao.query_life_service_access_code(param, 1000000, 1, NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> lifeList = lifePage.getData();
        Map<String, String> lifeMap = new HashMap<>();
        for (JSONObject life : lifeList) {
            lifeMap.put(life.getString("access_code"), life.getString("scene_name"));
        }

        JSONArray cfsInfluenceList = response.getJSONArray("cfs_influence");
        if (ObjectUtil.isNotNull(cfsInfluenceList)) {
            if (cfsInfluenceList.size() > 0) {

                for (int i = 0; i < cfsInfluenceList.size(); i++) {
                    JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(i);
                    String accessCode = cfsInfluence.getString("accs_nbr_no");
                    String sceneName = lifeMap.get(accessCode);
                    cfsInfluence.put("scene_name", sceneName);
                }

            }
        }


    }


    public void getCustomerManager(JSONObject response, String city)  {
        String sharding_code = "ds_odso_" + city;
        JSONArray cfsInfluenceList = response.getJSONArray("cfs_influence");

        if (ObjectUtil.isNotNull(cfsInfluenceList)) {


            if (cfsInfluenceList.size() > 0) {
                int size = cfsInfluenceList.size();
                List<String> crmProdIds = new ArrayList<>();
                for (int i = 0; i < size; i++) {
                    JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(i);
                    crmProdIds.add(cfsInfluence.getString("crm_prod_id"));
                }
                final List<JSONObject> managerList = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(crmProdIds)) {
                    PageUtils.doSomeStringByPage(
                            (ids) -> {
                                String idsString = StrUtil.join(",", ids);
                                // log.info(idsString);
                                List<JSONObject> list = influenceAnalysisDao.queryProductCustomerManager(ids, sharding_code);
                                if (ObjectUtil.isNotEmpty(list)) {
                                    managerList.addAll(list);
                                }
                            }, crmProdIds, 1000
                    );
                    // managerList = influenceAnalysisDao.queryProductCustomerManager(crmProdIds, sharding_code);
                }
                Map<String, JSONObject> customerManagerMap = new HashMap<>();
                if (ObjectUtil.isNotEmpty(managerList)) {
                    for (JSONObject manager : managerList) {
                        customerManagerMap.put(manager.getString("crm_prod_id"), manager);
                    }
                }
                for (int i = 0; i < size; i++) {
                    JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(i);
                    String crmProdId = cfsInfluence.getString("crm_prod_id");
                    JSONObject manager = customerManagerMap.get(crmProdId);
                    if (ObjectUtil.isNotEmpty(manager)) {
                        // 前端展示时不解密，保持加密状态
                        cfsInfluence.put("khjl_phone", manager.getString("khjl_phone"));
                        cfsInfluence.put("khjl_name", manager.getString("khjl_name"));
                        cfsInfluence.put("khjl_org_name", manager.getString("org_name"));
                        cfsInfluence.put("khjl_post_type", manager.getString("post_type_name"));
                    }
                }
            }

            
        }
    }

    public void getCircuitCode(JSONObject response, String city) {
        String sharding_code = "ds_odso_" + city;
        JSONArray cfsInfluenceList = response.getJSONArray("cfs_influence");

        if (ObjectUtil.isNotNull(cfsInfluenceList)) {


            if (cfsInfluenceList.size() > 0) {
                int size = cfsInfluenceList.size();
                List<String> accessCodeList = new ArrayList<>();
                for (int i = 0; i < size; i++) {
                    JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(i);
                    accessCodeList.add(cfsInfluence.getString("accs_nbr_no"));
                }
                final List<JSONObject> circuitList = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(accessCodeList)) {
                    PageUtils.doSomeStringByPage(
                            (ids) -> {
                                String idsString = StrUtil.join(",", ids);
                                // log.info(idsString);
                                List<JSONObject> list = influenceAnalysisDao.queryCircuitCodeByAccessCode(ids, sharding_code);
                                if (ObjectUtil.isNotEmpty(list)) {
                                    circuitList.addAll(list);
                                }
                            }, accessCodeList, 1000
                    );
                    // managerList = influenceAnalysisDao.queryProductCustomerManager(crmProdIds, sharding_code);
                }
                Map<String, JSONObject> circitCodeMap = new HashMap<>();
                if (ObjectUtil.isNotEmpty(circuitList)) {
                    for (JSONObject manager : circuitList) {
                        circitCodeMap.put(manager.getString("access_code"), manager);
                    }
                }
                for (int i = 0; i < size; i++) {
                    JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(i);
                    String accessCode = cfsInfluence.getString("accs_nbr_no");
                    JSONObject circuitCodeObj = circitCodeMap.get(accessCode);
                    if (ObjectUtil.isNotEmpty(circuitCodeObj)) {
                        cfsInfluence.put("circuit_code", circuitCodeObj.getString("circuit_code"));
                    }
                }
            }

        }
    }

    public void get30DayTimes(JSONObject response) {
        String sharding_code = NRMConstants.SHARDING_GRAPH_DB;
        JSONArray cfsInfluenceList = response.getJSONArray("cfs_influence");
        if (ObjectUtil.isNotNull(cfsInfluenceList)) {
            int size = cfsInfluenceList.size();
            List<String> crmProdIds = new ArrayList<>();
            for (int i = 0; i < size; i++) {
                JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(i);
                crmProdIds.add(cfsInfluence.getString("crm_prod_id"));
            }
            final List<JSONObject> resultList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(crmProdIds)) {
                PageUtils.doSomeStringByPage(
                        (ids) -> {

                            List<JSONObject> list = influenceAnalysisDao.query30DCutOverTimes(ids, sharding_code);

                            if (ObjectUtil.isNotEmpty(list)) {
                                resultList.addAll(list);
                            }


                        }, crmProdIds, 1000
                );
                // managerList = influenceAnalysisDao.queryProductCustomerManager(crmProdIds, sharding_code);
            }
            Map<String, JSONObject> resultMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(resultList)) {
                for (JSONObject result : resultList) {
                    resultMap.put(result.getString("crm_product_id"), result);
                }
            }

            for (int i = 0; i < size; i++) {
                JSONObject cfsInfluence = cfsInfluenceList.getJSONObject(i);
                String crmProdId = cfsInfluence.getString("crm_prod_id");
                JSONObject result = resultMap.get(crmProdId);
                if (ObjectUtil.isNotEmpty(result)) {
                    cfsInfluence.put("day30_times", result.getInteger("day30_times"));
                } else {
                    cfsInfluence.put("day30_times", 0);
                }

            }
        }
    }


    @PostMapping("getresourceinfluence")
    @PreAuthorize("hasAuthority('资源影响分析导出（菜单）')")
    @LogAnnotation( interfaceName="影响分析 getresourceinfluence 接口-获取影响分析清单")
    public ResponseEntity<JSONObject> getResourceinFluenceRest(@RequestBody JSONObject request) {


        JSONObject response = getResourceinFluence(request);


        log.info(request.toString());


        return ResponseEntity.ok(response);
    }

    @PostMapping("getinfluenceroute")
    @PreAuthorize("hasAuthority('资源影响分析导出（菜单）')")
    @LogAnnotation( interfaceName="影响分析 getinfluenceroute 接口-获取光缆段影响光路清单")
    public ResponseEntity<JSONObject> getInfluenceRoute(@RequestBody JSONObject request) {
        String city = request.getString("city");
        String devCode = request.getString("devCode");
        String devType = request.getString("devType");

        JSONObject response = new JSONObject();

        // 只处理光缆段类型的查询
        if ("cbl_sect".equals(devType) || "光缆段".equals(devType)) {
            // 使用正确的数据源，参考ProjectInfluenceServiceImpl#resourceInfluenceRoute
            String datasource = "ds_odso_" + city.toLowerCase();

            JSONObject param = new JSONObject();
            param.put("code", devCode);

            try {
                // 使用现有的getInfluenceRouteByCableCode方法，这个方法已经在ProjectInfluenceServiceImpl中使用
                PageResponse<JSONObject> pageResponse = cutoverDao.getInfluenceRouteByCableCode(param, 10000, 1, datasource);
                List<JSONObject> routeList = pageResponse.getData();

                response.put("route_influence", routeList);
                response.put("total_count", routeList.size());
                response.put("status", "success");
            } catch (Exception e) {
                log.error("查询影响光路失败", e);
                response.put("route_influence", new ArrayList<>());
                response.put("total_count", 0);
                response.put("status", "error");
                response.put("message", e.getMessage());
            }
        } else {
            response.put("route_influence", new ArrayList<>());
            response.put("total_count", 0);
            response.put("status", "unsupported");
            response.put("message", "仅支持光缆段类型的影响光路查询");
        }

        log.info("影响光路查询请求: {}", request.toString());
        log.info("影响光路查询响应: {}", response.toString());

        return ResponseEntity.ok(response);
    }


    @PostMapping("getstaffidbyname")
    @LogAnnotation( interfaceName="影响分析 getstaffidbyname 接口-根据名称查询staffid")

    public ResponseEntity<JSONObject> PageQueryPUB_STAFF_ORG_INFO(@RequestBody JSONObject request) {


        PageResponse<JSONObject> result = influenceAnalysisDao.PageQueryPUB_STAFF_ORG_INFO(request, 10, 1, "ds_graph_js");


        JSONObject resultJson = new JSONObject();
        resultJson.put("data", result.getData());
        return ResponseEntity.ok(resultJson);


    }


    @PostMapping("querystationwithincycle")
    @LogAnnotation( interfaceName="影响分析 querystationwithincycle 接口-查询圆形范围内的局站信息")
    public ResponseEntity<JSONObject> Query_dim_station_within_cicle(@RequestBody JSONObject request) {

        JSONObject newrequest = updateCityToLocalAreaId(request);

        PageResponse<JSONObject> result = influenceAnalysisDao.Query_dim_station_within_cicle(newrequest, 100, 1, "ds_gis");


        JSONObject resultJson = new JSONObject();
        resultJson.put("data", result.getData());
        return ResponseEntity.ok(resultJson);


    }


    @PostMapping("querystationwithinpolygon")
    @LogAnnotation( interfaceName="影响分析 querystationwithinpolygon 接口-查询多边形范围内的局站信息")
    public ResponseEntity<JSONObject> Query_dim_station_within_polygon(@RequestBody JSONObject request) {

        JSONObject newrequest = updateCityToLocalAreaId(request);
        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;


        PageResponse<JSONObject> result = influenceAnalysisDao.Query_dim_station_within_polygon(newrequest, 100, 1, ShardingCode);

        JSONObject resultJson = new JSONObject();
        resultJson.put("data", result.getData());
        return ResponseEntity.ok(resultJson);


    }


    @PostMapping("querybsesectwithincircle")
    @LogAnnotation( interfaceName="影响分析 querybsesectwithincircle 接口-查询圆形范围内的支撑段信息")

    public ResponseEntity<JSONObject> Query_bse_sect_within_circle(@RequestBody JSONObject request) {


        JSONObject newrequest = updateCityToLocalAreaId(request);

        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;

        PageResponse<JSONObject> result = influenceAnalysisDao.Query_bse_sect_within_circle(newrequest, 100, 1, ShardingCode);

        JSONObject resultJson = new JSONObject();
        resultJson.put("data", result.getData());
        return ResponseEntity.ok(resultJson);


    }


    @PostMapping("querybsesectwithinpolygon")
    @LogAnnotation( interfaceName="影响分析 querybsesectwithinpolygon 接口-查询多边形范围内的支撑段信息")
    public ResponseEntity<JSONObject> Query_bse_sect_within_polygon(@RequestBody JSONObject request) {

        String city = request.getString("city");
        JSONObject newrequest = updateCityToLocalAreaId(request);

        System.out.println("newrequest" + newrequest);

        String ShardingCode = "ds_odso_" + city;

        PageResponse<JSONObject> result = influenceAnalysisDao.Query_bse_sect_within_polygon(newrequest, 100, 1, ShardingCode);
        JSONObject resultJson = new JSONObject();
        resultJson.put("bse_sect_list", result.getData());
        List<JSONObject> bse_sect_array = result.getData();
        List<String> sectIds = new ArrayList<>();
        for (JSONObject jsonObject : bse_sect_array) {
            // 假设每个JSONObject都包含bse_sect_id字段
            String sectId = jsonObject.getString("bse_sect_id");
            sectIds.add(sectId);
        }

        JSONObject param = new JSONObject();
        param.put("bse_sect_ids", sectIds);
        System.out.println("param" + param);
        JSONObject cbl_sect_list = influenceAnalysisDao.query_cbl_sect_by_bse_sect_ids(param, ShardingCode);
        JSONObject cbl_list = influenceAnalysisDao.query_cbl_by_bse_sect_ids(param, ShardingCode);


        resultJson.put("cbl_sect_list", cbl_sect_list.getJSONArray("data"));

        resultJson.put("cbl_list", cbl_list.getJSONArray("data"));


        System.out.println("cbl_list.getJSONArray(\"data\")" + cbl_list.getJSONArray("data"));


        return ResponseEntity.ok(resultJson);
    }


    public static JSONObject updateCityToLocalAreaId(JSONObject jsonObject) {
        // 定义城市到local_area_id的映射关系
        Map<String, Integer> cityToLocalAreaIdMap = new HashMap<>();
        cityToLocalAreaIdMap.put("nj", 3);
        cityToLocalAreaIdMap.put("zj", 4);
        cityToLocalAreaIdMap.put("wx", 15);
        cityToLocalAreaIdMap.put("sz", 20);
        cityToLocalAreaIdMap.put("nt", 26);
        cityToLocalAreaIdMap.put("yz", 33);
        cityToLocalAreaIdMap.put("yc", 39);
        cityToLocalAreaIdMap.put("xz", 48);
        cityToLocalAreaIdMap.put("ha", 60);
        cityToLocalAreaIdMap.put("lyg", 63);
        cityToLocalAreaIdMap.put("cz", 69);
        cityToLocalAreaIdMap.put("tz", 79);
        cityToLocalAreaIdMap.put("sq", 84);

        // 解析JSON对象
        String city = jsonObject.getString("city");
        Integer localAreaId = cityToLocalAreaIdMap.get(city);

        if (localAreaId != null) {
            // 更新city为local_area_id
            jsonObject.put("local_area_id", localAreaId);
            jsonObject.remove("city");
        }

        return jsonObject;
    }


    @PostMapping("saveexternalforce")
    @LogAnnotation( interfaceName="影响分析 saveexternalforce 接口-保存外力点")
    public ResponseEntity<JSONObject> saveexternalforce(@RequestBody JSONObject request) {


        System.out.println("request" + request);
        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;
        int result = influenceAnalysisDao.tb_ExternalForcePointConstructionArea_insert(request, ShardingCode);

        JSONObject resultJson = new JSONObject();
        resultJson.put("result", result);
        return ResponseEntity.ok(resultJson);


    }

    @PostMapping("deleteexternalforce")
    @LogAnnotation( interfaceName="影响分析 deleteexternalforce 接口-删除外力点")
    public ResponseEntity<JSONObject> deleteexternalforce(@RequestBody JSONObject request) {


        System.out.println("request" + request);
        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;
        int result = influenceAnalysisDao.tb_ExternalForcePointConstructionArea_delete(request, ShardingCode);

        JSONObject resultJson = new JSONObject();
        resultJson.put("result", result);
        System.out.print(resultJson);
        return ResponseEntity.ok(resultJson);


    }

    @PostMapping("queryexternalforce")
    @LogAnnotation( interfaceName="影响分析 queryexternalforce 接口-查询外力点")
    public ResponseEntity<JSONObject> externalforce(@RequestBody JSONObject request) {

//        JSONObject newrequest = updateCityToLocalAreaId(request);
        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;
        System.out.println("ShardingCode" + ShardingCode);
        PageResponse<JSONObject> result = influenceAnalysisDao.tb_ExternalForcePointConstructionArea_query(request, 10000, 1, ShardingCode);

        JSONObject resultJson = new JSONObject();
        resultJson.put("data", result.getData());

        return ResponseEntity.ok(resultJson);

    }


    @PostMapping("query_external_force_daily_summary")
    @LogAnnotation( interfaceName="影响分析 query_external_force_daily_summary 接口-查询外力点每日统计数据汇总")
    public ResponseEntity<JSONObject> query_external_force_daily_summary(@RequestBody JSONObject request) {

        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;
        JSONObject result = influenceAnalysisDao.query_external_force_daily_summary(request, ShardingCode);


        return ResponseEntity.ok(result);

    }


    @PostMapping("updateexternalforce")
    @LogAnnotation( interfaceName="影响分析 updateexternalforce 接口-更新外力点")
    public ResponseEntity<JSONObject> updateexternalforce(@RequestBody JSONObject request) {

        System.out.println("request" + request);
        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;
        int result = influenceAnalysisDao.tb_ExternalForce_update(request, ShardingCode);

        JSONObject resultJson = new JSONObject();
        resultJson.put("result", result);
        return ResponseEntity.ok(resultJson);

    }

    //失效
    @PostMapping("invalideexternalforce")
    @LogAnnotation( interfaceName="影响分析 invalideexternalforce 接口-失效外力点")
    public ResponseEntity<JSONObject> invalideexternalforce(@RequestBody JSONObject request) {

        System.out.println("request" + request);
        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;
        int result = influenceAnalysisDao.tb_ExternalForce_invalide(request, ShardingCode);

        JSONObject resultJson = new JSONObject();
        resultJson.put("result", result);
        return ResponseEntity.ok(resultJson);

    }


    @PostMapping("insert_into_externalforce_pipes")
    @LogAnnotation( interfaceName="影响分析 insert_into_externalforce_pipes 接口-新增外力点")
    public ResponseEntity<JSONObject> insert_into_externalforce_pipes(@RequestBody JSONObject request) {

        System.out.println("request" + request);


//        //下面这句话把request里面的city转换为local_area_id
//        updateCityToLocalAreaId(request);
//        System.out.println("request"+request);

        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;
        Integer local_area_id = NRMConstants.LOCAL_AREA_ID.get(city);

        request.put("local_area_id", local_area_id);

        int result = influenceAnalysisDao.insert_into_externalforce_pipes(request, ShardingCode);

        JSONObject resultJson = new JSONObject();
        resultJson.put("result", result);
        return ResponseEntity.ok(resultJson);

    }


    //发短信接口
    @PostMapping("sendMessage")
    @LogAnnotation( interfaceName="影响分析 sendMessage 接口-发短信接口")
    public JSONObject sendMessage(@RequestBody JSONObject params) {


        //params：
        //phoneNumber=15279381234&message=您的验证码是123456

        System.out.println("params" + params);

        String url = "http://**************:7026/sendByNumAndMsg";


        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.clear();

        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 创建表单数据
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("phoneNumber", params.getString("phoneNumber"));
        map.add("message", params.getString("message"));

        // 封装请求头和请求体
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 检查响应状态码是否成功
        if (response.getStatusCode().is2xxSuccessful()) {
            // 获取响应体中的JSON字符串
            String jsonString = response.getBody();

            // 使用FastJSON将JSON字符串解析为JSONObject
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            return jsonObject;

        } else {
            // 处理不成功的响应
            System.out.println("Request failed. Status code: " + response.getStatusCode());

            JSONObject result = new JSONObject();
            result.put("result", "发送短信失败");
            return result;

        }


    }


    //现在需要根据  query_tb_externalforce_influence_pipes  把这个表搬运到ct_graph_sch下面
    @PostMapping("/auto_tb_externalforce_influence_pipes")
    @LogAnnotation( interfaceName="影响分析 auto_tb_externalforce_influence_pipes 接口-更新外力点影响分析的记录到省库接口")
    public JSONObject auto_tb_externalforce_influence_pipes(JSONObject request) {
        String city = request.getString("city");
        String ShardingCode = "ds_odso_" + city;
        JSONObject param = new JSONObject();

        //查记录
        JSONObject result = influenceAnalysisDao.query_tb_externalforce_influence_pipes(param, ShardingCode);
        //清空graph里面的表
        JSONObject result2 = influenceAnalysisDao.truncate_tb_externalforce_influence_pipes(param, "ds_graph_js");
        //更新graph里面的表
        JSONObject result3 = influenceAnalysisDao.insert_tb_externalforce_influence_pipes(result, "ds_graph_js");
        //计算影响的光缆段
        System.out.println("result" + result);


        return result3;

    }


    public List<JSONObject> buildCompleteTree(Graph graph) {

        JSONObject data = graphService.getData(graph);

        Set<Edge> edges = graph.getEdges();
        Set<Node> Nodes = graph.getNodes();

        Map<String, String> nodeNameMap = new HashMap<>();
        for (Node node : Nodes) {
            nodeNameMap.put(node.getId(), node.getName());
        }

        for (Edge edge : edges) {
            String sourceName = nodeNameMap.get(edge.getSource());
            String targetName = nodeNameMap.get(edge.getTarget());
            graphService.buildTree(data, sourceName, targetName, targetName);
        }

        String startSource = graphService.findStart(edges);
        List<JSONObject> CompleteTree = graphService.filterNodeByLabel(data, nodeNameMap.get(startSource));

        return CompleteTree;


    }


    public List<JSONObject> buildCblTree(Graph graph) {

        JSONObject data = graphService.getData(graph);

        Set<Edge> edges = graph.getEdges();
        Set<Node> Nodes = graph.getNodes();

        Map<String, String> nodeNameMap = new HashMap<>();
        for (Node node : Nodes) {
            nodeNameMap.put(node.getId(), node.getName());
        }

//        buildTree(data, "光缆段","光路", "光路");
//        buildTree(data, "光路","设备", "设备");
//        buildTree(data, "光路","PON端口", "PON端口");

        graphService.buildTree(data, "光缆段", "olt", "olt");
        graphService.buildReverseTree(data, "PON端口", "olt", "PON端口");
        graphService.buildReverseTree(data, "光路", "PON端口", "光路");


        String startSource = graphService.findStart(edges);
        List<JSONObject> CompleteTree = graphService.filterNodeByLabel(data, nodeNameMap.get(startSource));

        return CompleteTree;


    }

    public List<JSONObject> buildPipeTree(Graph graph) {

        JSONObject data = graphService.getData(graph);

        Set<Edge> edges = graph.getEdges();
        Set<Node> Nodes = graph.getNodes();

        Map<String, String> nodeNameMap = new HashMap<>();
        for (Node node : Nodes) {
            nodeNameMap.put(node.getId(), node.getName());
        }

//        buildTree(data, "光缆段","光路", "光路");
//        buildTree(data, "光路","设备", "设备");
//        buildTree(data, "光路","PON端口", "PON端口");

        graphService.buildTree(data, "管道段", "光缆段", "光缆段");
        graphService.buildTree(data, "光缆段", "光路", "光路");
        graphService.buildTree(data, "光路", "PON端口", "PON端口");
        graphService.buildTree(data, "PON端口", "olt", "olt");


        String startSource = graphService.findStart(edges);
        List<JSONObject> CompleteTree = graphService.filterNodeByLabel(data, nodeNameMap.get(startSource));

        return CompleteTree;


    }


    //转前端要的tree的格式
    public JSONObject convertToFrontendFormat(List<JSONObject> inputList) {


        JSONArray tree = buildFrontFormatTree(inputList);

        JSONObject result = new JSONObject();
        result.put("FrontendFormat", tree);
        return result;
    }

    public static JSONArray buildFrontFormatTree(List<JSONObject> jsonObjectList) {
        JSONArray treeArray = new JSONArray();
        for (JSONObject jsonObject : jsonObjectList) {
            JSONObject treeNode = buildTreeNode(jsonObject);
            treeArray.add(treeNode);
        }
        return treeArray;
    }

    //如果不配置数据流向，就用这个默认build完整的tree
    private static JSONObject buildTreeNode(JSONObject jsonObject) {
        JSONObject treeNode = new JSONObject();

        treeNode.put("title", jsonObject.getString("name"));

        treeNode.put("key", UUID.randomUUID() + jsonObject.getString("gid"));
        treeNode.put("label_name", jsonObject.getString("label_name"));

        boolean isLeaf = true;
        for (String key : jsonObject.keySet()) {

            Object value = jsonObject.get(key);
            if (value instanceof JSONArray) {
                @SuppressWarnings("unchecked")
                JSONArray list = (JSONArray) value; // 非受检转换
                JSONArray childrenArray = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject item = list.getJSONObject(i);
                    JSONObject childNode = buildTreeNode(item);
                    childrenArray.add(childNode);
                }
                treeNode.put("children", childrenArray);

                isLeaf = false;
            }


        }
        treeNode.put("isLeaf", isLeaf);


        return treeNode;
    }


}
