package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.ProtCircuitCheckDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.CustLinkService;
import com.telecom.nrm.service.MstpService;
import com.telecom.nrm.service.ProtectCircuitCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

@RestController
@RequestMapping("/api/protCircuitCheck")
@Slf4j
public class ProtectCircuitCheckController {

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    CustLinkService custLinkService;

    @Autowired
    ProtectCircuitCheckService protCircuitCheckService;

    @Autowired
    ProtCircuitCheckDao protCircuitCheckDao;

    @Autowired
    MstpService mstpService;

    /*@PostMapping("/realtime")
    public JSONObject getInfo_realtime(@RequestHeader Map<String, String> headers,@RequestBody JSONObject param, HttpServletResponse response){
        JSONObject message = new JSONObject();
        message.put("intf_code","集团");
        message.put("request_header", headers.toString());
        message.put("request_body", param.toString());
        protCircuitCheckDao.graph_pm_interface_log_insert(message,NRMConstants.SHARDING_GRAPH_DB);
        if(Objects.isNull(param.getString("circuit_no")) || param.getString("circuit_no") ==null){
            String code = "1";
            String msg="缺少关键字";
            JSONObject result =protCircuitCheckService.getInterface(code,  msg,  null,  null,  null, null,  null, null,  null,  null);
            return result;
        }
        JSONObject result = new JSONObject();
        List<JSONObject> circuits = new ArrayList<>();
        String code = "";
        String msg = "";
        String a_area_code = "";
        String z_area_code = "";
        String a_is_risk= "";
        String z_is_risk= "";
        String a_risk_type = "";
        String z_risk_type = "";
        String a_risk_desc = "";
        String z_risk_desc= "";
        for(String ds :  NRMConstants.BCO3DatabaseMap.keySet()){
            PageResponse<JSONObject> pageResponse = custLinkInterfaceDao.getCircuit_pair(param, 100, 1, NRMConstants.BCO3DatabaseMap.get(ds));
            if(pageResponse !=null || Objects.isNull(pageResponse)){
                JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
                circuits.addAll(pageResponse.getData());

            }
        }

        if(!Objects.isNull(circuits)&& circuits!=null && circuits.size()>0){
            code = "0";
            msg="成功";
            for(int i =0;i<circuits.size();i++){
                JSONObject param_ds = new JSONObject();
                String ds = circuits.get(i).getString("areacode");
                param_ds.put("ds", ds);
                JSONObject cur_result = custLinkService.circuits_pair_analze(circuits.get(i), param_ds);
                JSONObject current_risk = protCircuitCheckService.getrisk(cur_result);
                String area_code = protCircuitCheckService.getAreaCode(ds);
                String  is_risk = current_risk.getString("is_risk");
                String risk_type = current_risk.getString("risk_type");
                String risk_desc = current_risk.getString("risk_desc");
                if(i ==0){
                    a_area_code = area_code;
                    a_is_risk = is_risk;
                    a_risk_type = risk_type;
                    a_risk_desc = risk_desc;
                }else{
                    z_area_code = area_code;
                    z_is_risk = is_risk;
                    z_risk_type = risk_type;
                    z_risk_desc = risk_desc;
                }
            }
        }else{
            code = "1";
            msg="电路不存在";
        }
        result =protCircuitCheckService.getInterface(code,  msg,  a_area_code,  z_area_code,  a_is_risk, z_is_risk,  a_risk_type, z_risk_type,  a_risk_desc,  z_risk_desc);
        message.put("response_body", result);
        protCircuitCheckDao.graph_pm_interface_log_insert(message,NRMConstants.SHARDING_GRAPH_DB);
        return result;
    }*/

    @PostMapping("")
    public JSONObject getInfo(@RequestHeader Map<String, String> headers, @RequestBody JSONObject param, HttpServletResponse response){
        String aProvince = "";
        String zProvince = "";
        if(!Objects.isNull(param.getString("aProvince"))|| param.getString("aProvince")!=null) aProvince = param.getString("aProvince");
        if(!Objects.isNull(param.getString("zProvince"))|| param.getString("zProvince")!=null) zProvince = param.getString("zProvince");
        String circuit_code_current ="";
        if(Objects.isNull(param.getString("circuit_no")) || param.getString("circuit_no") ==null){
            String code = "1";
            String msg="缺少关键字";
            JSONObject result =protCircuitCheckService.getInterface(code,  msg);
            return result;
        }
        String[] circuit_nos = param.getString("circuit_no").split(",");
        if(circuit_nos.length>1){
            param.put("circuit_no", circuit_nos[0]);
            param.put("circuit_code", circuit_nos[0]);
        }
        JSONObject result = new JSONObject();
        String code = "";
        String msg = "";
        String a_area_code = "";
        String z_area_code = "";
        String a_is_risk= "";
        String z_is_risk= "";
        String a_risk_type = "";
        String z_risk_type = "";
        String a_risk_desc = "";
        String z_risk_desc= "";
        List<JSONObject> circuits = protCircuitCheckService.getPairCircuit(param);
        List<String> areaList = protCircuitCheckService.getAreaFromInput(param.getString("circuit_no"));
        //如果能查到电路，就赋值
        if(!Objects.isNull(circuits)&& circuits!=null && circuits.size()>0){
            circuit_code_current = !Objects.isNull(circuits.get(0).getString("aCode"))?circuits.get(0).getString("aCode"):circuits.get(0).getString("zCode");

            code = "0";
            msg="成功";
            for(int i =0;i<circuits.size();i++){
                String ds = circuits.get(i).getString("areacode");
                String area_code = protCircuitCheckService.getAreaCode(ds);
                String risk_type = circuits.get(i).getString("riskType");
                String risk_desc = circuits.get(i).getString("riskDesc");
                String is_risk = protCircuitCheckService.getrisk(risk_type);
                if(i == 0){
                    a_area_code = area_code;
                    a_is_risk = is_risk;
                    a_risk_type = risk_type;
                    a_risk_desc = risk_desc;
                }else{
                    z_area_code = area_code;
                    z_is_risk = is_risk;
                    z_risk_type = risk_type;
                    z_risk_desc = risk_desc;
                }
            }
            if(areaList.size()==1){
                z_area_code = "";
                z_is_risk = "";
                z_risk_type = "";
                z_risk_desc = "";
                result =protCircuitCheckService.getInterface(code,  msg,  a_area_code,  z_area_code,  a_is_risk, z_is_risk,  a_risk_type, z_risk_type,  a_risk_desc,  z_risk_desc);
                return protCircuitCheckService.getSeq(circuit_code_current, result, aProvince,zProvince);

            }
            if(a_area_code!=null && a_area_code !="" && z_area_code !=null && z_area_code!=""){
                result =protCircuitCheckService.getInterface(code,  msg,  a_area_code,  z_area_code,  a_is_risk, z_is_risk,  a_risk_type, z_risk_type,  a_risk_desc,  z_risk_desc);
                return protCircuitCheckService.getSeq(circuit_code_current, result, aProvince,zProvince);
                //return result;
            }
        }
        //没有全找到的情况下，电路地区都存在的前提下，找到剩余的地市
        List<String> leave_area =new ArrayList<>();
        List<JSONObject> leave_circuit = new ArrayList<>();
        if(areaList.size() !=0){
            leave_area  = protCircuitCheckService.getLeaveArea(circuits,areaList);
        }else{
            //没找到，要根据再去地市库查下
            List<JSONObject> circuitsFSC = protCircuitCheckService.getCircuitFSC(param);
            if(circuitsFSC.size() == 0){
                code = "1";
                msg="非江苏业务或省内电路资料缺失";
                result =protCircuitCheckService.getInterface(code,  msg);
                return result;
            }else{
                leave_circuit =  protCircuitCheckService.getLeaveArea_circuit(circuits, circuitsFSC);
                if(circuit_code_current.equals("")) circuit_code_current = circuitsFSC.get(0).getString("code");
                for(int i =0;i<leave_circuit.size();i++){
                    leave_area.add(leave_circuit.get(i).getString("areacode"));
                }
            }
        }
        if(leave_area.size()==0){
            result =protCircuitCheckService.getInterface(code,  msg,  a_area_code,  z_area_code,  a_is_risk, z_is_risk,  a_risk_type, z_risk_type,  a_risk_desc,  z_risk_desc);
            return protCircuitCheckService.getSeq(circuit_code_current, result, aProvince,zProvince);
        }else{
            //取对应地市并进资源系统查询电路并分析
            //如果存在
            List<String> codes = new ArrayList<>();
            codes.add(param.getString("circuit_no"));
            int count =0;
            for(int i = 0;i<leave_area.size();i++) {
                JSONObject cm_link_code_result = custLinkInterfaceDao.cm_Link_code_query(param, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(leave_area.get(i)));
                if(!Objects.isNull(cm_link_code_result) && cm_link_code_result!=null) {
                    if(leave_circuit.size()!=0){
                        codes = new ArrayList<>();
                        for(int j=0;j<leave_circuit.size();j++){
                            codes.add(leave_circuit.get(j).getString("code"));
                        }
                    }
                    param.put("codes",codes);
                    param.put("shardingCode", NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(leave_area.get(i)));
                    JSONObject jsonObject_result = mstpService.queryMSTPCircuit(param);
                    String area_code = protCircuitCheckService.getAreaCode(leave_area.get(i));
                    String risk_type = protCircuitCheckService.getrisktype(JSONObjectUtil.jsonObjectArrayToList(jsonObject_result.getJSONArray("samePipeSegments_d")), JSONObjectUtil.jsonObjectArrayToList(jsonObject_result.getJSONArray("sameCables_d")), null);
                    String risk_desc = protCircuitCheckService.getriskdesc(JSONObjectUtil.jsonObjectArrayToList(jsonObject_result.getJSONArray("samePipeSegments_d")), JSONObjectUtil.jsonObjectArrayToList(jsonObject_result.getJSONArray("sameCables_d")), null);
                    String is_risk = protCircuitCheckService.getrisk(risk_type);
                    if(leave_area.size()<areaList.size()){
                        z_area_code = area_code;
                        z_is_risk = is_risk;
                        z_risk_type = risk_type;
                        z_risk_desc = risk_desc;
                    }else{
                        if(i == 0){
                            a_area_code = area_code;
                            a_is_risk = is_risk;
                            a_risk_type = risk_type;
                            a_risk_desc = risk_desc;
                        }else{
                            z_area_code = area_code;
                            z_is_risk = is_risk;
                            z_risk_type = risk_type;
                            z_risk_desc = risk_desc;
                        }
                    }
                    protCircuitCheckService.savePairCircuits(codes.get(0), codes.size()>1?codes.get(1):null, risk_type, risk_desc, leave_area.get(i));
                }else{
                    count++;
                }
            }
            if(count==0){
                code = "0";
                msg="成功";
                result =protCircuitCheckService.getInterface(code,  msg,  a_area_code,  z_area_code,  a_is_risk, z_is_risk,  a_risk_type, z_risk_type,  a_risk_desc,  z_risk_desc);
            }else if(count> 0 && count < areaList.size()){
                code = "0";
                msg="一端电路资料缺失";
                result =protCircuitCheckService.getInterface(code,  msg,a_area_code,  z_area_code,  a_is_risk, z_is_risk,  a_risk_type, z_risk_type,  a_risk_desc,  z_risk_desc);
            }else{
                code = "1";
                msg="电路资料缺失";
                result =protCircuitCheckService.getInterface(code,  msg);
            }
        }
        protCircuitCheckService.reportSave("集团",headers.toString(),param.toString(), result.toString(),"");
        if(!result.getString("code").equals("1")){
            return protCircuitCheckService.getSeq(circuit_code_current, result, aProvince,zProvince);
        }
        return result;

        //return result;
    }

    @PostMapping("/ceshi")
    public void getInfo_1(@RequestHeader Map<String, String> headers, @RequestBody JSONObject param, HttpServletResponse response) throws IOException {
        List<String> list = new ArrayList<>();
        list.add("南京东莞ZQOTN00001");
        list.add("M00172169");
                list.add("南京盐城ETN6059NPH");
                        list.add("南京南通ETN0258NP(A)");
        list.add("徐州电视台-新沂电视台/ETN0001NP");
        list.add("南京南通ETN6072NPH");
        list.add("花果山大道电视台大厦3层机房-灌南县新安镇人民西路文广体局发射台/ETN0001NP");
        list.add("南京连云港ETN6033NPH");
        list.add("北京南京ETN2415NP");
        list.add("武警镇江支队-执勤一大队镇江中队/ETN0001NP");
        list.add("南京徐州ETN0288NP");
        list.add("南京盐城ETN0208NP(B)");
        list.add("南京盐城ETN0195NP(B)");
        list.add("南京泰州ETN0172NP(B)");
        list.add("上海仪征ETN0015NP(A)");
        list.add("南京常州ETN0218NP(A)");
        list.add("北京南京ETN2402NP(A)");
        list.add("南京淮安30N0012NP(B)");
        list.add("东吴电视台-昆山市广播电视台/ETN0002NP");
        list.add("苏州苏州ETN6220NPH");
        list.add("连云港市广电发射台-东海县广电发射台/ETN0002NP");
        list.add("南京常州ETN6078NPH");
        list.add("北京南京30N2596NP");
        list.add("150012739");
        list.add("南通银监局-通州银监局/ETN0001NP");
        list.add("南京宿迁ZQOTN00015");
        list.add("南京泰州ETN0150NP(A)");
        list.add("南京徐州ETN0077NP");
        list.add("南京常州ETN6086NPH");
        list.add("呼市苏州ZQOTN00005");
        list.add("盐城-大丰/ETN0065NP(M51066921)");
        list.add("M00297825");
        list.add("南京南通ETN0227NP(A)");
        list.add("M00268730");
        list.add("花果山大道电视台大厦3层机房-北固山路电视转播台/ETN0002NP");
        list.add("盐城盐城ETN6122NPH");
        list.add("南京南通ETN0216NP(B)");
        list.add("南京盐城ETN0196NP(B)");
        list.add("北京无锡30N2099NP");
        list.add("镇江扬中/ETN0001NP");
        list.add("南京淮安ETN0234NP(B)");
        list.add("东吴电视台-昆山市广播电视台/ETN0001NP");
        list.add("上海南京ZQOTN00041");
        list.add("南京镇江ETN0203NP(A)");
        list.add("市电视台发射台-盱眙数据ETN0001NP");
        list.add("徐州徐州ETN6037NPH");
        list.add("南京无锡ETN0267NP(B)");
        list.add("宿迁宿迁ETN6087NPH");
        list.add("南京常州ZQOTN00011");
        list.add("南京常州ETN0215NP(B)");
        list.add("天津苏州ZQOTN00004");
        list.add("南京深圳ZQOTN00008");
        list.add("南京南通ETN0253NP(A)");
        list.add("南京徐州ETN0226NP(B)");
        list.add("武警镇江支队-龙潭保障大队/ETN0001NP");
        list.add("成都淮安ZQOTN00002");
        list.add("南京扬州ZQOTN00006");
        list.add("南京盐城30N0005NP(A)");
        list.add("M00297827");
        list.add("M00297829");
        list.add("南京常州ETN0238NP(A)");
        list.add("北京南京S-1N2111NP");
        list.add("南京镇江ZQOTN00004");
        list.add("南京佛山ETN0004NP");
        list.add("M00289940/泰兴电视台-泰兴黄桥电视台/ETN0001NP");
        list.add("南京无锡ETN6047NPH");
        list.add("南京宁波ZQOTN00007");
        list.add("南京常州ETN0279NP");
        list.add("南京宿迁ETN0158NP(B)");
        list.add("南京无锡30N0011NP(B)");
        list.add("北京南京ZQOTN00094");
        list.add("常州深圳30N0028NP");
        list.add("上海南京ETN0369NP(A)");
        list.add("南京徐州ETN6057NPH");
        list.add("南京苏州ZQOTN00041");
        list.add("南京南通ETN0258NP(B)");
        list.add("市电视台发射台-盱眙码流ETN0001NP");
        list.add("南京苏州ZQOTN00009");
        list.add("南京淮安ETN6041NPH");
        list.add("南京淮安ETN0175NP(B)");
        list.add("南京无锡ETN0226NP(A)");
        list.add("南京盐城ETN0216NP(B)");
        list.add("南京镇江ETN0265NP");
        list.add("南通银监局-如东银监局/ETN0001NP");
        list.add("南京淮安ETN0189NP(B)");
        list.add("南京扬州ETN0201NP(B)");
        list.add("南京南通ETN6070NPH");
        list.add("南京天津ZQOTN00007");
        list.add("南京南通ETN0245NP(B)");
        list.add("M19161423");
        list.add("盐城盐城ETN6123NPH");
        list.add("武警镇江支队-教导队/ETN0001NP");
        list.add("北京无锡30N2073NP");
        list.add("盐城盐城ETN6127NPH");
        list.add("南京南京ZQOTN00020");
        list.add("南京徐州ETN0205NP(B)");
        list.add("M00268712");
        list.add("电视台-江都电视台/ETN0001NP");
        list.add("M00297826");
        list.add("盐城盐城ETN6128NPH");
        list.add("南京泰州ETN6041NPH");
        list.add("武警无锡支队-武警执勤一中队吴桥西路无锡监狱/ETN0001NPH");
        list.add("常州深圳ETN0023NP");
        list.add("南京无锡ETN0241NP(A)");
        list.add("东台市广播电视台-三仓广播电视台100M(M51061929)");
        list.add("南京苏州30N0026NP(B)");
        list.add("常州武警支队-东岗监狱/ETN0001NP");
        list.add("南京徐州ZQOTN00002");
        list.add("南京镇江ETN6069NPH");
        list.add("南京泰州ETN0150NP(B)");
        list.add("武警无锡支队-武警执勤五中队(张渚镇芙蓉村宜兴监狱）/ETN0001NPH");
        list.add("盐城盐城ETN6121NPH");
        list.add("成都镇江ZQOTN00002");
        list.add("徐州徐州ETN6034NPH");
        list.add("南京泰州ETN0181NP(B)");
        list.add("MSTPJS1003102916");
        list.add("徐州电视台-睢宁电视台/ETN0001NP");
        list.add("南京盐城ETN0195NP(A)");
        list.add("南京常州ETN6088NPH");
        list.add("南京无锡30N0664NP");
        list.add("南京盐城ETN0223NP(A)");
        list.add("南京常州ETN6089NPH");
        list.add("南京无锡ETN0256NP(B)");
        list.add("南京泰州ETN0068NP");
        list.add("常州无锡ETN0028NP");
        list.add("南京盐城ETN6052NPH");
        list.add("南京淮安ETN0176NP(A)");
        list.add("武警无锡支队-武警执勤二中队山水东路222号/ETN0001NPH");
        list.add("上海江阴30N0007NP");
        list.add("南通广播电视-海安广播电视/ETN0002NP");
        list.add("上海苏州ZQOTN00032");
        list.add("南京泰州ETN6042NPH");
        list.add("南京苏州ZQOTN00024");
        list.add("南京泰州ETN0162NP(B)");
        list.add("南京无锡ETN0266NP(B)");
        list.add("南京南通ETN0223NP(B)");
        list.add("南京常州ETN0217NP(B)");
        list.add("MSTPJS1002578231");
        list.add("南京深圳ZQOTN00009(A),南京深圳ZQOTN00009(B)");
        list.add("南京宿迁30N0001NP(A)");
        list.add("通州广电-平潮广电/ETN0001NP");
        list.add("南通广播电视-如皋广播电视/ETN0001NP");
        list.add("南京泰州ETN0183NP(B)");
        list.add("盐城-东台/ETN0056NP(M51046922)");
        list.add("MSTPJS1002573885");
        list.add("南京太仓ETN0025NP(B)");
        list.add("南京扬州ETN0216NP(A)");
        list.add("南京泰州ETN0227NP(A)");
        list.add("南京太仓ETN0025NP(A)");
        list.add("南京无锡ETN0262NP(B)");
        list.add("南京淮安ETN6040NPH");
        list.add("南京连云港ETN0192NP(A)");
        list.add("南京盐城ETN0222NP(B)");
        list.add("南京苏州ETN1750NP(A)");
        list.add("苏州苏州ETN6239NPH");
        list.add("北京南京30N2603NP");
        list.add("南京苏州ETN1779NP(A)");
        list.add("南京宿迁ETN0165NP(A)");
        list.add("南京苏州ETN1837NP");
        list.add("常州武警支队-武警武进区看守所/ETN0001NP");
        list.add("南京扬州ETN0215NP(B)");
        list.add("南京镇江ETN0206NP(A)");
        list.add("南京扬州ETN0613NP");
        list.add("盐城建湖MSTP100M(M51061928)");
        list.add("南京徐州ETN0201NP(A)");
        list.add("南京扬州ETN6138NPH");
        list.add("北京南京S-1N2048NP");
        list.add("南京连云港ZQOTN00003");
        list.add("南京泰州ZQOTN00005");
        list.add("南京连云港ETN0205NP(A)");
        list.add("南京盐城ETN6056NPH");
        list.add("M16011341");
        list.add("南京张家港ETN0030NP(B)");
        list.add("南京徐州ETN0222NP(B)");
        list.add("南京淮安ZQOTN00004");
        list.add("武警镇江支队-执勤二大队执勤三中队/ETN0001NP");
        list.add("北京常州30N2025NP");
        list.add("南通广播电视-如东广播电视/ETN0001NP");
        list.add("连云港市广电发射台-东海县广电发射台/ETN0001NP");
        list.add("M00289936/泰州广播发射塔-姜堰广播电视台/ETN0002NP");
        list.add("北京苏州ZQOTN00022");
        list.add("花果山大道电视台大厦3层机房-连岛广播电视站/ETN0002NP");
        list.add("南京无锡ETN6046NPH");
        list.add("南京宿迁ETN0203NP(B)");
        list.add("南京泰州ETN6049NPH");
        list.add("南通南通ETN6312NPH");
        list.add("南京连云港ETN0269NP(B)");
        list.add("南京连云港ETN0183NP(B)");
        list.add("南京常州ETN0229NP(A)");
        list.add("南京常州ETN0275NP(B)");
        list.add("南京徐州ETN6055NPH");
        list.add("南京南通ETN0319NP(A)");
        list.add("常州苏州ETN0024NP(B)");
        list.add("上海南京ZQOTN00030");
        list.add("南通广播电视-如东广播电视/ETN0002NP");
        list.add("扬州扬州ETN6143NPH");
        list.add("南京宿迁ETN0141NP(A)");
        list.add("南京徐州ETN0226NP(A)");
        list.add("市电视台发射台-洪泽数据ETN0001NP");
        list.add("M00172173");
        list.add("南京常州ETN6087NPH");
        list.add("阜宁电视台-益林电视台20M(M51066957)");
        list.add("上海南京ETN0369NP(B)");
        list.add("南京常熟ETN0028NP(A)");
        list.add("南京连云港ETN0199NP(A)");
        list.add("花果山大道电视台大厦3层机房-灌云县伊山镇振兴南路广电局/ETN0002NP");
        list.add("泰州泰州ETN6201NPH");
        list.add("盐城盐城ETN6119NPH");
        list.add("上海南京ZQOTN00029");
        list.add("武警无锡支队-武警宜兴执勤四中队丁山监狱/ETN0001NPH");
        list.add("盐城-大丰/ETN0063NP(M51056920)");
        list.add("南京东莞ZQOTN00003(A),南京东莞ZQOTN00003(B)");
        list.add("北京南京ZQOTN00119");
        list.add("盐城盐城ETN6120NPH");
        list.add("南京南通ETN0228NP(A)");
        list.add("常州武警支队-武警衡玉路623台/ETN0001NP");
        list.add("武警无锡支队-武警无锡中队钱胡路567号/ETN0001NPH");
        list.add("M00289928/泰州广播发射塔-姜堰广播电视台/ETN0001NP");
        list.add("市电视台发射台—涟水码流ETN0001NP");
        list.add("南京镇江ETN6068NPH");
        list.add("常州武警支队-溧阳武警四中队/ETN0001NP");
        list.add("南京徐州ETN0222NP(A)");
        list.add("南京苏州ETN6085NPH");
        list.add("南京镇江ETN6067NPH");
        list.add("南京常州ETN0280NP");
        list.add("南京淮安ETN0173NP(B)");
        list.add("南京南通ETN0250NP(A)");
        list.add("南京连云港ETN0204NP(B)");
        list.add("南京南通ETN0241NP(A)");
        list.add("MSTPJS1002574608");
        list.add("南京苏州ETN1785NP(B)");
        list.add("南京淮安ZQOTN00002");
        list.add("上海苏州ZQOTN00036");
        list.add("南京徐州ETN0218NP(A)");
        list.add("徐州徐州ETN6033NPH");
        list.add("市电视发射台-淮阴区码流ETN0001NP");
        list.add("IPRANJS1002441553");
        list.add("南京苏州ETN1763NP(A)");
        list.add("M14871291");
        list.add("南京张家港ETN0030NP(A)");
        list.add("南京常州ETN0215NP(A)");
        list.add("南京淮安ETN0184NP(A)");
        list.add("北京南京30N2602NP");
        list.add("镇江常州ETN0011NP(A)");
        list.add("上海南京ZQOTN00039");
        list.add("常州武警支队-溧阳武警三中队/ETN0001NP");
        list.add("电视台-江都电视台/ETN0002NP");
        list.add("盐城阜宁MSTP20M(M51066946)");
        list.add("MSTPJS1002573465");
        list.add("常州苏州ETN0024NP(A)");
        list.add("徐州电视台-沛县电视台/ETN0002NP");
        list.add("南京无锡ETN0256NP(A)");
        list.add("市电视台发射台-金湖数据ETN0001NP");
        list.add("南京徐州ETN6056NPH");
        list.add("MSTPJS1002575616");
        list.add("南京常州ETN0281NP");
        list.add("南京扬州ETN0241NP(B)");
        list.add("南京镇江ETN6061NPH");
        list.add("南京无锡ETN0267NP(A)");
        list.add("盐城盐城ETN6129NPH");
        list.add("南京苏州ETN1829(A)");
        list.add("南京无锡ETN0303NP(B)");
        list.add("南京常州ETN0204NP(B)");
        list.add("扬州扬州ETN6148NPH");
        list.add("南京盐城ETN0207NP(A)");
        list.add("无锡电视塔-江阴电视台发射台/ETN0002NP");
        list.add("南京宿迁ETN0141NP(B)");
        list.add("北京南京ETN2402NP(B)");
        list.add("南京淮安ETN0165NP(A)");
        list.add("南京南通30N0001NP(A)");
        list.add("南京徐州ETN0307NP(B)");
        list.add("盐城-滨海/ETN0042NP(M51061920)");
        list.add("南京连云港ETN6035NPH");
        list.add("南京南通ETN0105NP");
        list.add("南京扬州ETN6147NPH");
        list.add("MSTPJS1002572434");
        list.add("上海南通ZQOTN00016");
        list.add("江阴电视台-长泾电视台/ETN0001NP");
        list.add("上海南京ETN0482NP");
        list.add("南京镇江ETN0206NP(B)");
        list.add("南京连云港ETN6034NPH");
        list.add("南京泰州ETN0183NP(A)");
        list.add("扬州扬州ETN6146NPH");
        list.add("东吴电视台-吴江广播电视台/ETN0001NP");
        list.add("MSTPJS1002568232");
        list.add("南京南通ETN0685NP");
        list.add("南京苏州ETN6095NPH");
        list.add("无锡电视塔-无锡电视塔/ETN0001NP");
        list.add("南京常州ETN0229NP(B)");
        list.add("MSTPJS1003100980");
        list.add("南京泰州ETN6050NPH");
        list.add("南京镇江ETN0187NP(B)");
        list.add("南京淮安ETN0234NP(A)");
        list.add("盐城-射阳/ETN0094NP(M51066923)");
        list.add("南京镇江ETN0257NP(A)");
        list.add("常州武警支队-驻警教导组徐家村/ETN0001NP");
        list.add("武警支队-西林/ETN0001NP");
        list.add("MSTPJS1003040433");
        list.add("南京徐州30N0041NP(B)");
        list.add("南京镇江ETN0075NP");
        list.add("M00268709");
        list.add("成都苏州ZQOTN00004");
        list.add("通州广电-三余广电/ETN0002NP");
        list.add("南京无锡ETN6045NPH");
        list.add("徐州徐州ETN6035NPH");
        list.add("创元期货南施街IDC--创元期货科技城IDC/ETN0001NP");
        list.add("MSTPJS1002574215");
        list.add("南京苏州ETN6096NPH");
        list.add("南京淮安ETN0190NP(A)");
        list.add("南京苏州ZQOTN00028");
        list.add("南京扬州ZQOTN00010");
        list.add("宿迁宿迁ETN6090NPH");
        list.add("南京江阴ETN0042NP");
        list.add("南通银监局-海安银监局/ETN0001NP");
        list.add("南京常州ETN6079NPH");
        list.add("南京徐州ETN0205NP(A)");
        list.add("M14876285");
        list.add("南京宿迁ETN0145NP(B)");
        list.add("MSTPJS1002572866");
        list.add("南京扬州ETN0618NP");
        list.add("常州武警支队-溧阳武警五中队/ETN0001NP");
        list.add("南京苏州ETN1831NP(B)");
        list.add("花果山大道电视台大厦3层机房-灌南县新安镇人民西路文广体局发射台/ETN0002NP");
        list.add("南京福州S-4N0001NP");
        list.add("MSTPJS1002573961");
        list.add("南京镇江ETN0186NP(A)");
        list.add("无锡电视塔-江阴电视台发射台/ETN0001NP");
        list.add("南京扬州ETN0242NP(A)");
        list.add("徐州电视台-邳州电视台/ETN0001NP");
        list.add("南京淮安ETN6030NPH");
        list.add("M14676270");
        list.add("南京连云港ETN0170NP(B)");
        list.add("南京苏州ETN1764NP(A)");
        list.add("南京苏州ZQOTN00029");
        list.add("南京连云港ETN0199NP(B)");
        list.add("M00268717");
        list.add("南京盐城ZQOTN00004");
        list.add("南京南通ETN0254NP(B)");
        list.add("阜宁电视台-益林电视台100M(M51061930)");
        list.add("南京镇江ETN0186NP(B)");
        list.add("南京南京ZQOTN00009");
        list.add("苏州苏州STN047941-A");
        list.add("南京镇江ETN6066NPH");
        list.add("南京盐城ETN0160NP(B)");
        list.add("苏州苏州ETN6215NPH");
        list.add("M00268731");
        list.add("南京连云港ETN6032NPH");
        list.add("市电视台发射台-淮安区数据ETN0001NP");
        list.add("150012743");
        list.add("常州武警支队-徐家村驻警中队/ETN0001NP");
        list.add("南京淮安ETN0065NP");
        list.add("南京淮安30N0012NP(A)");
        list.add("南京徐州ETN0204NP(A)");
        list.add("南京苏州ETN1764NP(B)");
        list.add("南京徐州ETN6048NPH");
        list.add("南京无锡ETN0338NP");
        list.add("南京宿迁ETN6014NPH");
        list.add("泰州泰州ETN6199NPH");
        list.add("苏州苏州ETN6222NPH");
        list.add("南京镇江30N0012NP(B)");
        list.add("南京泰州ETN0229NP(B)");
        list.add("南京常州ETN0217NP(A)");
        list.add("北京南京ZQOTN00109");
        list.add("南京无锡ETN0226NP(B)");
        list.add("江阴电视台-长泾电视台/ETN0002NP");
        list.add("电视台-宝应电视台/ETN0001NP");
        list.add("南京宁波ZQOTN00006");
        list.add("扬州邯郸STN1986");
        list.add("南京江阴30N0027NP");
        list.add("北京南京30N2604NP");
        list.add("南京徐州ETN0190NP(A)");
        list.add("南京淮安ETN0176NP(B)");
        list.add("武警镇江支队-执勤一大队执勤一中队/ETN0001NP");
        list.add("南京淮安ETN0165NP(B)");
        list.add("东吴电视台-常熟广播电视台/ETN0001NP");
        list.add("南京盐城ETN0277NP");
        list.add("南京徐州ETN0204NP(B)");
        list.add("南京无锡ETN6048NPH");
        list.add("MSTPJS1002575617");
        list.add("南京泰州ETN0161NP(B)");
        list.add("南京扬州ETN0241NP(A)");
        list.add("南京宿迁ZQOTN00019");
        list.add("苏州苏州ETN6219NPH");
        list.add("南京宿迁ZQOTN00004");
        list.add("南京徐州ETN0284NP(B)");
        list.add("MSTPJS1002112130");
        list.add("北京苏州ZQOTN00032");
        list.add("南京无锡ETN0303NP(A)");
        list.add("南京连云港ETN0204NP(A)");
        list.add("南京扬州ETN6137NPH");
        list.add("M00289930/泰州广播发射塔-兴化广播电视台/ETN0001NP");
        list.add("北京南京ZQOTN00062");
        list.add("合肥苏州ZQOTN00005");
        list.add("北京苏州ZQOTN00016");
        list.add("苏州保定STN1764-A");
        list.add("南京连云港ETN0205NP(B)");
        list.add("南京镇江ETN0264NP");
        list.add("盐城盐城ETN6126NPH");
        list.add("南京无锡ETN0257NP(A)");
        list.add("南京镇江ETN0188NP(A)");
        list.add("北京南京30N2601NP");
        list.add("盐城-射阳/ETN0089NP（M51061922）");
        list.add("常州武警支队-溧阳市武警中队/ETN0001NP");
        list.add("南京南通ETN0223NP(A)");
        list.add("南京南通ETN0319NP(B)");
        list.add("南京连云港ETN6028NPH");
        list.add("南京苏州ZQOTN00068(A),南京苏州ZQOTN00068(B)");
        list.add("南京苏州ETN1829(B)");
        list.add("武警无锡支队-武警宜兴中队归径看守所1号/ETN0001NPH");
        list.add("南京盐城ETN0209NP(B)");
        list.add("徐州徐州ETN6038NPH");
        list.add("南京扬州ETN6139NPH");
        list.add("武警无锡支队-武警宜兴教导队原2师709团丁蜀镇查林村794号/ETN0001NPH");
        list.add("南京镇江ZQOTN00010");
        list.add("南京徐州ZQOTN00005");
        list.add("盐城盐城ETN6130NPH");
        list.add("南京扬州ETN0242NP(B)");
        list.add("MSTPJS1003103834");
        list.add("南京苏州ETN1760NP(B)");
        list.add("南京无锡ETN0235NP(A)");
        list.add("南京南通ETN0320NP");
        list.add("南京泰州ETN0181NP(A)");
        list.add("南京无锡ETN0310NP");
        list.add("南京镇江ETN0199NP(B)");
        list.add("南京苏州ZQOTN00075(A),南京苏州ZQOTN00075(B)");
        list.add("南京南通ETN6069NPH");
        list.add("上海仪征ETN0015NP(B)");
        list.add("南京无锡ETN0309NP");
        list.add("南京泰州ETN6051NPH");
        list.add("北京南京ETN2201NP");
        list.add("南京镇江ETN0280NP");
        list.add("常州武警支队-溧阳武警八中队/ETN0001NP");
        list.add("宿迁宿迁ETN6091NPH");
        list.add("南京泰州ETN0229NP(A)");
        list.add("北京南京ETN2221NP");
        list.add("南京盐城ETN0208NP(A)");
        list.add("徐州电视台-睢宁电视台/ETN0002NP");
        list.add("徐州电视台-新沂电视台/ETN0002NP");
        list.add("南京常州30N0013NP(B)");
        list.add("徐州电视台-丰县电视台/ETN0001NP");
        list.add("MSTPJS1002578762");
        list.add("南京苏州ETN1763NP(B)");
        list.add("北京南京ETN2321NP");
        list.add("南京徐州ETN0284NP(A)");
        list.add("南京扬州ETN6146NPH");
        list.add("南京连云港ETN0192NP(B)");
        list.add("南京泰州ETN0177NP(B)");
        list.add("南京泰州30N0010NP(A)");
        list.add("徐州徐州ETN6030NPH");
        list.add("南京连云港ETN0193NP(B)");
        list.add("南京淮安ETN6039NPH");
        list.add("徐州电视台-贾汪电视台/ETN0002NP");
        list.add("南京镇江ETN0188NP(B)");
        list.add("M00289924/泰州广播发射塔-靖江广播电视台/ETN0001NP");
        list.add("南京无锡ETN0093NP");
        list.add("南京呼市ETN6001NPH");
        list.add("南京扬州ETN0213NP(B)");
        list.add("南京常州ETN0237NP(B)");
        list.add("北京南京ZQOTN00067");
        list.add("苏州南通ZQOTN00006(A),苏州南通ZQOTN00006(B)");
        list.add("南京无锡ETN6041NPH");
        list.add("上海南京ZQOTN00021");
        list.add("南京镇江ETN0257NP(B)");
        list.add("M00289938/泰州广播发射塔-泰兴广播电视台/ETN0002NP");
        list.add("南京无锡ZQOTN00002");
        list.add("南京宿迁30N0001NP(B)");
        list.add("南通广播电视-如皋广播电视/ETN0002NP");
        list.add("南京苏州ETN1750NP(B)");
        list.add("徐州嘉兴ETN0003NP(A)");
        list.add("南京泰州30N0010NP(B)");
        list.add("MSTPJS1002568646");
        list.add("南京宿迁ETN0208NP");
        list.add("南京淮安ETN0175NP(A)");
        list.add("南京南通ETN0227NP(B)");
        list.add("南京苏州ETN1831NP(A)");
        list.add("南京徐州ETN0307NP(A)");
        list.add("南京南通ZQOTN00007");
        list.add("无锡深圳ZQOTN00006(A),无锡深圳ZQOTN00006(B)");
        list.add("南京苏州ETN1784NP(B)");
        list.add("南京常州ETN0275NP(A)");
        list.add("南京常州ETN0230NP(A)");
        list.add("南京苏州ZQOTN00043");
        list.add("南通南通ETN6309NPH");
        list.add("南京苏州ZQOTN00042");
        list.add("北京南京S-1N2093NP");
        list.add("南京连云港ETN0193NP(A)");
        list.add("南京无锡30N0011NP(A)");
        list.add("南京无锡ZQOTN00007");
        list.add("南京扬州ETN6148NPH");
        list.add("南京无锡ETN0308NP");
        list.add("市电视台发射台—涟水数据ETN0001NP");
        list.add("南京盐城ETN6058NPH");
        list.add("南京宿迁ETN0146NP(A)");
        list.add("南京宿迁ETN0164NP(B)");
        list.add("南通南通ETN6315NPH");
        list.add("徐州电视台-丰县电视台/ETN0002NP");
        list.add("南通南通ETN6311NPH");
        list.add("南通威海ZQOTN00008");
        list.add("南通银监局-海门银监局/ETN0001NP");
        list.add("MSTPJS1002580257");
        list.add("南京扬州ETN0201NP(A)");
        list.add("南京南通ETN0321NP");
        list.add("常州武警支队-金坛看守所/ETN0001NP");
        list.add("M00297824");
        list.add("南京宿迁ETN0145NP(A)");
        list.add("南通南通ETN6319NPH");
        list.add("南京徐州ETN0225NP(B)");
        list.add("南京宿迁ETN0054NP");
        list.add("南京盐城ETN0222NP(A)");
        list.add("南京盐城ETN0060NP");
        list.add("花果山大道电视台大厦3层机房-赣榆县青口镇环城北路广电局/ETN0002NP");
        list.add("盐城盐城ETN6124NPH");
        list.add("MSTPJS1002580267");
        list.add("苏州苏州ZQOTN00008");
        list.add("MSTPJS1002578232");
        list.add("南京泰州ETN0159NP(A)");
        list.add("M00172172");
        list.add("创元期货南施街IDC-创元期货科技城IDC(M65988164)/ETN0001NP");
        list.add("MSTPJS1002573466");
        list.add("北京南京ZQOTN00052");
        list.add("M00268708");
        list.add("南京南通ETN0250NP(B)");
        list.add("北京南京ZQOTN00022");
        list.add("南京盐城ETN0216NP(A)");
        list.add("市电视台发射台-金湖码流ETN0001NP");
        list.add("南京镇江ETN0266NP");
        list.add("南京无锡ETN0266NP(A)");
        list.add("南京常州ETN0233NP(B)");
        list.add("南京常州ZQOTN00009");
        list.add("南京常州30N0013NP(A)");
        list.add("南京扬州30N0004NP(A)");
        list.add("东吴电视台-吴江广播电视台/ETN0002NP");
        list.add("市电视台发射台-洪泽码流ETN0001NP");
        list.add("上海常熟ETN0055NP(A)");
        list.add("南京镇江ETN0281NP");
        list.add("无锡深圳ETN0054NP(B)");
        list.add("花果山大道电视台大厦3层机房-灌云县伊山镇振兴南路广电局/ETN0001NP");
        list.add("MSTPJS1002121131");
        list.add("南京扬州ETN6140NPH");
        list.add("东吴电视台-张家港广播电视台/ETN0002NP");
        list.add("泰州泰州ETN6205NPH");
        list.add("南京淮安ETN6032NPH");
        list.add("南京苏州ETN1631NP");
        list.add("M16021481");
        list.add("南京常州ZQOTN00005");
        list.add("南京宿迁ETN0237NP(B)");
        list.add("盐城广电发射部-响水ETN0002NP（20M）（M51066948）");
        list.add("扬州扬州ETN6145NPH");
        list.add("MSTPJS1002572435");
        list.add("南京镇江ETN0199NP(A)");
        list.add("南京淮安ETN0184NP(B)");
        list.add("南京常州ETN0204NP(A)");
        list.add("东台市广播电视台-三仓广播电视台20M(M51066956)");
        list.add("南京常州ETN0238NP(B)");
        list.add("M00289925/泰州广播发射塔-泰兴广播电视台/ETN0001NP");
        list.add("MSTPJS1002574607");
        list.add("武警镇江支队-执勤二大队执勤四中队/ETN0001NP");
        list.add("徐州徐州ETN6032NPH");
        list.add("南京徐州ETN6054NPH");
        list.add("南京扬州ETN0080NP");
        list.add("南京连云港ETN0181NP(B)");
        list.add("M00172168");
        list.add("南京江阴30N0026NP");
        list.add("南京泰州ETN0177NP(A)");
        list.add("南京镇江30N0012NP(A)");
        list.add("南京高邮30N0002NP");
        list.add("南京连云港ETN0184NP(B)");
        list.add("南京盐城ETN0160NP(A)");
        list.add("无锡深圳ETN0054NP(A)");
        list.add("南京南京ZQOTN00014");
        list.add("南京盐城ETN0207NP(B)");
        list.add("徐州电视台-邳州电视台/ETN0002NP");
        list.add("东吴电视台-太仓广播电视台/ETN0002NP");
        list.add("南京南通ZQOTN00002");
        list.add("镇江常州ETN0011NP(B)");
        list.add("贵阳苏州ZQOTN00007");
        list.add("宿迁宿迁ETN6088NPH");
        list.add("南京东莞ZQOTN00005");
        list.add("苏州苏州ETN6224NPH");
        list.add("上海苏州ZQOTN00051");
        list.add("南通南通ETN6317NPH");
        list.add("北京苏州ETN2117NP");
        list.add("苏州苏州ETN6223NPH");
        list.add("南京苏州ETN1784NP(A)");
        list.add("南京济南S-4N2001NP");
        list.add("南京连云港ETN0184NP(A)");
        list.add("南京苏州ETN6097NPH");
        list.add("M00268711");
        list.add("南通广播电视-海门广播电视/ETN0002NP");
        list.add("南京连云港ETN0271NP");
        list.add("北京南京ETN2155NP");
        list.add("通州广电-三余广电/ETN0001NP");
        list.add("呼市苏州ZQOTN00013");
        list.add("南京连云港ETN0170NP(A)");
        list.add("南京镇江ETN0207NP(B)");
        list.add("南京盐城ETN0191NP(A)");
        list.add("M16016328");
        list.add("M00289939/泰州广播发射塔-靖江广播电视台/ETN0002NP");
        list.add("南京扬州ETN0281NP(A)");
        list.add("上海江阴30N0009NP");
        list.add("南京盐城ETN0209NP(A)");
        list.add("南京无锡ETN0094NP");
        list.add("北京无锡ETN2108NP");
        list.add("南京盐城ETN0315NP");
        list.add("徐州电视台-泉山发射台/ETN0001NP");
        list.add("北京南京ETN2045NP");
        list.add("武警镇江支队-执勤二大队丹阳中队/ETN0001NP");
        list.add("南京扬州ETN0223NP(A)");
        list.add("市电视台发射台-淮安区码流ETN0001NP");
        list.add("南京泰州ETN0227NP(B)");
        list.add("北京南京30N2600NP");
        list.add("南京南通ETN6071NPH");
        list.add("南京常州ETN0233NP(A)");
        list.add("徐州电视台-沛县电视台/ETN0001NP");
        list.add("南京常州ETN0218NP(B)");
        list.add("南京泰州ETN0067NP");
        list.add("南京淮安ETN0189NP(A)");
        list.add("南京江阴ETN0059NP");
        list.add("MSTPJS1002574387");
        list.add("南京扬州ETN0238NP(B)");
        list.add("南京连云港ETN6027NPH");
        list.add("南京无锡ETN0235NP(B)");
        list.add("南京泰州ETN0171NP(A)");
        list.add("北京南京ZQOTN00099");
        list.add("扬州扬州ETN6142NPH");
        list.add("苏州东莞ZQOTN00002(A),苏州东莞ZQOTN00002(B)");
        list.add("南京徐州ETN6047NPH");
        list.add("苏州深圳ETN0003NP(A)");
        list.add("南京扬州ETN0213NP(A)");
        list.add("南京徐州ETN6045NPH");
        list.add("南京淮安ETN6042NPH");
        list.add("南京常州ETN0237NP(A)");
        list.add("南京泰州ETN0172NP(A)");
        list.add("北京南京ZQOTN00108");
        list.add("东吴电视台-太仓广播电视台/ETN0001NP");
        list.add("南京南通30N0001NP(B)");
        list.add("南京泰州ETN0162NP(A)");
        list.add("上海南京ETN0139NP");
        list.add("南京苏州ETN1869NP");
        list.add("南通广播电视-启东广播电视/ETN0002NP");
        list.add("南京连云港ETN0083NP");
        list.add("南京淮安ETN6033NPH");
        list.add("常州武警支队-溧阳武警六中队/ETN0001NP");
        list.add("南京盐城ZQOTN00002");
        list.add("南京扬州ETN0216NP(B)");
        list.add("花果山大道电视台大厦3层机房-连岛广播电视站/ETN0001NP");
        list.add("南京盐城ETN6051NPH");
        list.add("武警镇江支队-执勤二大队执勤二中队/ETN0001NP");
        list.add("南京盐城30N0005NP(B)");
        list.add("苏州苏州ETN6226NPH");
        list.add("南通广播电视-海门广播电视/ETN0001NP");
        list.add("南京宿迁ETN0165NP(B)");
        list.add("南京苏州ZQOTN00013");
        list.add("南京宿迁ETN0237NP(A)");
        list.add("南京盐城ETN6057NPH");
        list.add("宿迁宿迁ETN6089NPH");
        list.add("武警无锡支队-武警机动中队安镇街道胶南村/ETN0001NPH");
        list.add("南京南通ETN6060NPH");
        list.add("MSTPJS1002606093");
        list.add("MSTPJS1002580187");
        list.add("南京无锡ETN0257NP(B)");
        list.add("M16021471");
        list.add("盐城-滨海/ETN0043NP（M51066947）");
        list.add("南通南通ETN6314NPH");
        list.add("南京无锡ETN0238NP(B)");
        list.add("武汉苏州ZQOTN00122");
        list.add("IPRANJS1002450246");
        list.add("南京镇江ETN0207NP(A)");
        list.add("南京宿迁ETN0164NP(A)");
        list.add("南通广播电视-海安广播电视/ETN0001NP");
        list.add("南京徐州ETN0225NP(A)");
        list.add("北京南京ZQOTN00076");
        list.add("南京无锡ETN6040NPH");
        list.add("盐城-东台/ETN0123NP(M51066920)");
        list.add("南京徐州ETN0289NP");
        list.add("南京无锡ZQOTN00015");
        list.add("西安南京ZQOTN00002");
        list.add("北京南京ZQOTN00053");
        list.add("南京泰州ETN6048NPH");
        list.add("北京南京ZQOTN00081");
        list.add("苏州苏州ETN6238NPH");
        list.add("上海无锡ETN0252NP");
        list.add("南京镇江ETN0178NP(B)");
        list.add("合肥徐州ZQOTN00001");
        list.add("南京镇江ETN0187NP(A)");
        list.add("南京扬州ETN6136NPH");
        list.add("南京淮安ETN0173NP(A)");
        list.add("南京扬州30N0004NP(B)");
        list.add("徐州电视台-泉山发射台/ETN0002NP");
        list.add("南通南通ETN6310NPH");
        list.add("南京扬州ETN0238NP(A)");
        list.add("南京常州ETN0085NP");
        list.add("南京扬州ETN0223NP(B)");
        list.add("南京南通ETN0322NP");
        list.add("宿迁宿迁ETN6079NPH");
        list.add("北京南京ZQOTN00009");
        list.add("南京徐州ETN0190NP(B)");
        list.add("M00297823");
        list.add("南京扬州ETN0215NP(A)");
        list.add("南通广播电视-通州金沙广播电视/ETN0001NP");
        list.add("宿迁宿迁ETN6092NPH");
        list.add("南京宿迁ETN0146NP(B)");
        list.add("M00268715");
        list.add("南京苏州ETN1785NP(A)");
        list.add("南京南通ETN0253NP(B)");
        list.add("南京淮安ETN0190NP(B)");
        list.add("MSTPJS1002578704");
        list.add("南京泰州ETN0171NP(B)");
        list.add("南京连云港30N0007NP(A)");
        list.add("镇江扬中/ETN0002NP");
        list.add("上海常熟ETN0055NP(B)");
        list.add("电视台-宝应电视台/ETN0002NP");
        list.add("东吴电视台-张家港广播电视台/ETN0001NP");
        list.add("南京盐城ETN0191NP(B)");
        list.add("南京宿迁ETN0203NP(A)");
        list.add("苏州苏州ETN6237NPH");
        list.add("南京连云港ETN0346NP");
        list.add("花果山大道电视台大厦3层机房-赣榆县青口镇环城北路广电局/ETN0001NP");
        list.add("南通广播电视-通州金沙广播电视/ETN0002NP");
        list.add("南京苏州30N0026NP(A)");
        list.add("北京南京ZQOTN00114");
        list.add("南京连云港ETN0181NP(A)");
        list.add("南京扬州ETN0281NP(B)");
        list.add("南京盐城ETN0271NP(A)");
        list.add("北京无锡30N2072NP");
        list.add("南京镇江ETN6062NPH");
        list.add("徐州徐州ETN6031NPH");
        list.add("泰州泰州ETN6204NPH");
        list.add("北京南京30N2598NP");
        list.add("南京无锡ETN0241NP(B)");
        list.add("北京无锡ETN2130NP");
        list.add("北京南京ZQOTN00048");
        list.add("北京南京30N2595NP");
        list.add("南通南通ETN6316NPH");
        list.add("扬州扬州ETN6147NPH");
        list.add("南京徐州30N0041NP(A)");
        list.add("南京连云港ZQOTN00011");
        list.add("南京中卫ZQOTN00003");
        list.add("武警无锡支队-武警江阴中队西城路331号/ETN0001NPH");
        list.add("北京南京30N2597NP");
        list.add("南京南通ZQOTN00014");
        list.add("苏州苏州ETN6221NPH");
        list.add("盐城盐城ETN6125NPH");
        list.add("南通南通ETN6313NPH");
        list.add("南通深圳ZQOTN00001(A),南通深圳ZQOTN00001(B)");
        list.add("M00268729");
        list.add("南京南通ETN0245NP(A)");
        list.add("北京南京ZQOTN00025");
        list.add("盐城-建湖/ETN0055NP(M51066955)");
        list.add("南京镇江ETN0263NP");
        list.add("南京无锡ETN0238NP(A)");
        list.add("南京盐城ETN0223NP(B)");
        list.add("M00268728");
        list.add("南京宿迁ETN6013NPH");
        list.add("南京南通ETN6059NPH");
        list.add("武警镇江支队-执勤一大队扬中中队/ETN0001NP");
        list.add("南京南通ZQOTN00010");
        list.add("徐州嘉兴ETN0003NP(B)");
        list.add("南京连云港ETN0183NP(A)");
        list.add("苏州苏州ETN6218NPH");
        list.add("南京镇江ETN0178NP(A)");
        list.add("上海南京ETN0537NP");
        list.add("苏州昌吉ZQOTN00003");
        list.add("南京苏州ETN6094NPH");
        list.add("电视台-高邮电视台/ETN0002NP");
        list.add("南京常州ETN0230NP(B)");
        list.add("市电视发射台-淮阴区数据ETN0001NP");
        list.add("M00289935/泰兴电视台-泰兴黄桥电视台/ETN0002NP");
        list.add("南京连云港ZQOTN00017");
        list.add("无锡深圳ZQOTN00004(A),无锡深圳ZQOTN00004(B)");
        list.add("武警镇江支队-执勤二大队句容中队/ETN0001NP");
        list.add("东吴电视台-常熟广播电视台/ETN0002NP");
        list.add("南京淮安ETN6031NPH");
        list.add("通州广电-平潮广电/ETN0002NP");
        list.add("南京泰州ETN0161NP(A)");
        list.add("南京徐州ETN0201NP(B)");
        list.add("北京南京S-1N2059NP");
        list.add("南京南通ETN0241NP(B)");
        list.add("电视台-高邮电视台/ETN0001NP");
        list.add("扬州扬州ETN6144NPH");
        list.add("南通南通ETN6318NPH");
        list.add("MSTPJS1002578703");
        list.add("杭州无锡ZQOTN00001");
        list.add("上海无锡ZQOTN00015(A),上海无锡ZQOTN00015(B)");
        list.add("南京苏州ETN1779NP(B)");
        list.add("盐城-阜宁/ETN0040NP(M51061921)");
        list.add("南京苏州ETN6086NPH");
        list.add("苏州苏州STN000446");
        list.add("盐城广电发射部-响水ETN0001NP（100M）M51061927");
        list.add("南京淮安ETN0066NP");
        list.add("上海常州30N0114NP");
        list.add("徐州电视台-贾汪电视台/ETN0001NP");
        list.add("南京-南京30N0000NP");
        list.add("南京福州ZQOTN00002");
        list.add("北京苏州ZQOTN00024");
        list.add("南京盐城ETN0196NP(A)");
        list.add("南京盐城ETN0271NP(B)");
        list.add("南京扬州ETN6145NPH");
        list.add("电视台-仪征电视台/ETN0001NP");
        list.add("南京常熟ETN0028NP(B)");
        list.add("南京苏州ETN1760NP(A)");
        list.add("南京南通ETN0228NP(B)");
        list.add("南京徐州ETN0218NP(B)");
        list.add("常州武警支队-溧阳武警九中队/ETN0001NP");
        list.add("北京南京30N2599NP");
        list.add("电视台-仪征电视台/ETN0002NP");
        list.add("南京无锡ETN0262NP(A)");
        list.add("南京苏州ZQOTN00010");
        list.add("M00289937/泰州广播发射塔-兴化广播电视台/ETN0002NP");
        list.add("宿迁宿迁ETN6085NPH");
        list.add("南京宿迁ETN0158NP(A)");
        list.add("南京南通ETN0254NP(A)");
        list.add("无锡张家口ZQOTN00003(A),无锡张家口ZQOTN00003(B)");
        list.add("南通银监局-如皋银监局/ETN0001NP");
        list.add("常州武警支队-溧阳武警二中队/ETN0001NP");
        list.add("南京连云港ETN0269NP(A)");
        list.add("南京泰州ETN0159NP(B)");
        list.add("北京南京ETN2140NP");
        list.add("北京江阴30N2007NP");
        list.add("花果山大道电视台大厦3层机房-北固山路电视转播台/ETN0001NP");
        list.add("南京镇江ETN0203NP(B)");
        list.add("北京南京ZQOTN00080");
        list.add("天津苏州ETN2021NP");
        list.add("南京南通ETN0216NP(A)");
        list.add("南京连云港30N0007NP(B)");
        list.add("南通广播电视-启东广播电视/ETN0001NP");
        list.add("南京CTVPN52301E");
        list.add("南京CTVPN52301D");
        list.add("南通CTVPN80130O");
        list.add("南通CTVPN80130P");
        list.add("徐州CTVPN58363B");
        list.add("徐州CTVPN58363A");
        list.add("无锡CTVPN80001B");
        list.add("无锡CTVPN80001A");
        list.add("无锡CTVPN52325D");
        list.add("无锡CTVPN52325C");
        list.add("无锡CTVPN80002A");
        list.add("无锡CTVPN80002B");
        list.add("苏州CTVPN80001A");
        list.add("苏州CTVPN80001B");
        list.add("南京CTVPN80002A");
        list.add("南京CTVPN53094A");
        list.add("南通CTVPN80002A");
        list.add("南通CTVPN80002B");
        list.add("南京CTVPN80002B");
        list.add("苏州CTVPN56329B");
        list.add("苏州CTVPN56329C");
        list.add("南京CTVPN80001B");
        list.add("南京CTVPN80001A");
        list.add("苏州CTVPN53288A");
        list.add("苏州CTVPN53288B");
        list.add("南通CTVPN55605B");
        list.add("南通CTVPN55605A");
        list.add("宿迁CTVPN80130H");
        list.add("宿迁CTVPN80130G");
        list.add("南京CTVPN54845F");
        list.add("南京CTVPN53763B");
        list.add("苏州CTVPN53615A");
        list.add("苏州CTVPN53615B");
        list.add("南京CTVPN53094B");
        list.add("南京CTVPN80101A");
        list.add("南京CTVPN80101B");
        list.add("南京CTVPN81101B");
        list.add("南京CTVPN81101A");
        list.add("南京CTVPN54341A");
        list.add("南通CTVPN190B001");
        list.add("南通CTVPN190A001");
        list.add("南通CTVPN80019B");
        list.add("南通CTVPN80019A");
        list.add("扬州CTVPN52484F");
        list.add("南京CTVPN80019A");
        list.add("南京CTVPN80019B");
        list.add("南京CTVPN54682B");
        list.add("南京CTVPN54682A");
        list.add("苏州CTVPN53517D");
        list.add("苏州CTVPN53727D");
        list.add("苏州CTVPN53727C");
        list.add("苏州CTVPN54819A");
        list.add("苏州CTVPN54819B");
        list.add("南京CTVPN54819B");
        list.add("南京CTVPN54819A");
        list.add("苏州CTVPN80130B");
        list.add("苏州CTVPN80130A");
        list.add("苏州CTVPN80130V");
        list.add("苏州CTVPN80130Q");
        list.add("苏州CTVPN80130AD");
        list.add("苏州CTVPN80130D");
        list.add("苏州CTVPN80130G");
        list.add("苏州CTVPN80130AF");
        list.add("苏州CTVPN80130M");
        list.add("苏州CTVPN80130T");
        list.add("苏州CTVPN80130U");
        list.add("苏州CTVPN80130AE");
        list.add("苏州CTVPN80130R");
        list.add("苏州CTVPN80130Z");
        list.add("苏州CTVPN80130AB");
        list.add("苏州CTVPN80130L");
        list.add("苏州CTVPN80130N");
        list.add("苏州CTVPN80130K");
        list.add("苏州CTVPN80130F");
        list.add("苏州CTVPN80130P");
        list.add("苏州CTVPN80130E");
        list.add("苏州CTVPN80130AA");
        list.add("苏州CTVPN80130AG");
        list.add("苏州CTVPN80130I");
        list.add("苏州CTVPN80130AC");
        list.add("苏州CTVPN80130C");
        list.add("苏州CTVPN54845A");
        list.add("苏州CTVPN54845B");
        list.add("无锡CTVPN54452A");
        list.add("无锡CTVPN54452B");
        list.add("常州CTVPN80131B");
        list.add("常州CTVPN80130A");
        list.add("常州CTVPN80130B");
        list.add("常州CTVPN80131A");
        list.add("南京CTVPN80131G");
        list.add("南京CTVPN80131H");
        list.add("无锡CTVPN80131A");
        list.add("无锡CTVPN80131B");
        list.add("无锡CTVPN80130A");
        list.add("无锡CTVPN80130B");
        list.add("南京CTVPN80131A");
        list.add("南京CTVPN80131B");
        list.add("南通CTVPN80131B");
        list.add("连云港CTVPN80131G");
        list.add("连云港CTVPN80131H");
        list.add("南通CTVPN80131A");
        list.add("南京CTVPN80130B");
        list.add("南京CTVPN80130A");
        list.add("扬州CTVPN80131A");
        list.add("扬州CTVPN80131B");
        list.add("徐州CTVPN80131A");
        list.add("徐州CTVPN80131B");
        list.add("淮安CTVPN80131A");
        list.add("淮安CTVPN80131B");
        list.add("镇江CTVPN80131A");
        list.add("镇江CTVPN80131B");
        list.add("宿迁CTVPN80131D");
        list.add("宿迁CTVPN80131E");
        list.add("泰州CTVPN80131A");
        list.add("泰州CTVPN80131B");
        list.add("盐城CTVPN80131A");
        list.add("盐城CTVPN80131B");
        list.add("常州CTVPN80131G");
        list.add("常州CTVPN80131H");
        list.add("无锡CTVPN80130E");
        list.add("无锡CTVPN80130F");
        list.add("无锡CTVPN80130C");
        list.add("无锡CTVPN80130D");
        list.add("苏州CTVPN80130AH");
        list.add("苏州CTVPN80130AI");
        list.add("常州CTVPN54295A");
        list.add("南通CTVPN54295A");
        list.add("无锡CTVPN52409B");
        list.add("南京CTVPN54217E");
        list.add("南京CTVPN54217F");
        list.add("扬州CTVPN80130B");
        list.add("镇江CTVPN80130D");
        list.add("镇江CTVPN80130E");
        list.add("淮安CTVPN80130A");
        list.add("淮安CTVPN80130B");
        list.add("扬州CTVPN80130A");
        list.add("连云港CTVPN80130A");
        list.add("连云港CTVPN80130B");
        list.add("南通CTVPN80130E");
        list.add("盐城CTVPN80130B");
        list.add("南通CTVPN80130F");
        list.add("泰州CTVPN80130A");
        list.add("泰州CTVPN80130B");
        list.add("盐城CTVPN80130A");
        list.add("苏州CTVPN54217A");
        list.add("苏州CTVPN54217B");
        list.add("南京CTVPN54295C");
        list.add("南京CTVPN80011AT");
        list.add("南京CTVPN80011AU");
        list.add("苏州CTVPN80401I");
        list.add("苏州CTVPN80401J");
        list.add("南京CTVPN52031A");
        list.add("南京CTVPN80011AW");
        list.add("南京CTVPN80011AY");
        list.add("苏州CTVPN80001C");
        list.add("苏州CTVPN80001D");
        list.add("南京CTVPN80001C");
        list.add("南京CTVPN80001D");
        list.add("宿迁CTVPN80011M");
        list.add("宿迁CTVPN80011L");
        list.add("常州CTVPN55511B");
        list.add("常州CTVPN55511A");
        list.add("苏州CTVPN55623A");
        list.add("苏州CTVPN55623B");
        list.add("苏州CTVPN55747A");
        list.add("苏州CTVPN55747B");
        list.add("无锡CTVPN80019B");
        list.add("无锡CTVPN80019A");
        list.add("苏州CTVPN80019A");
        list.add("苏州CTVPN80019B");
        list.add("南京CTVPN80131M");
        list.add("泰州CTVPN52409A");
        list.add("泰州CTVPN52409B");
        list.add("南京CTVPN52484O");
        list.add("南京CTVPN52484P");
        list.add("常州CTVPN54509D");
        list.add("常州CTVPN54509C");
        list.add("苏州CTVPN55906A");
        list.add("苏州CTVPN55906B");
        list.add("南京CTVPN80441C");
        list.add("南京CTVPN80441E");
        list.add("南京CTVPN80011BI");
        list.add("南京CTVPN80011BH");
        list.add("宿迁CTVPN29136A");
        list.add("宿迁CTVPN29136B");
        list.add("常州CTVPN29136A");
        list.add("苏州CTVPN29136B");
        list.add("苏州CTVPN29136A");
        list.add("常州CTVPN29136B");
        list.add("苏州CTVPN80027A");
        list.add("苏州CTVPN80027B");
        list.add("南京CTVPN29136D");
        list.add("南京CTVPN29136C");
        list.add("苏州CTVPN52239B");
        list.add("苏州CTVPN52239A");
        list.add("南京CTVPN80130J");
        list.add("南京CTVPN80130I");
        list.add("苏州CTVPN80011AY");
        list.add("南京CTVPN80131O");
        list.add("苏州CTVPN53727L");
        list.add("苏州CTVPN53727K");
        list.add("常州CTVPN80019B");
        list.add("常州CTVPN80019A");
        list.add("苏州CTVPN52353P");
        list.add("南京CTVPN52353D");
        list.add("南京CTVPN52353C");
        list.add("苏州CTVPN52353N");
        list.add("苏州CTVPN52353S");
        list.add("苏州CTVPN52353T");
        list.add("苏州CTVPN52353V");
        list.add("苏州CTVPN80012AI");
        list.add("苏州CTVPN80012AH");
        list.add("南京CTVPN29146C");
        list.add("南京CTVPN29146B");
        list.add("扬州CTVPN29146A");
        list.add("扬州CTVPN29146B");
        list.add("南京CTVPN80102C");
        list.add("南京CTVPN80102D");
        list.add("南京CTVPN80412B");
        list.add("南京CTVPN80412A");
        list.add("南京CTVPN80012AC");
        list.add("南京CTVPN80012AB");
        list.add("扬州CTVPN80019A");
        list.add("扬州CTVPN80019B");
        list.add("徐州CTVPN81101A");
        list.add("苏州CTVPN56789B");
        list.add("苏州CTVPN56789A");
        list.add("徐州CTVPN81101B");
        list.add("徐州CTVPN80019A");
        list.add("徐州CTVPN80019B");
        list.add("苏州CTVPN80012AO");
        list.add("苏州CTVPN80012AN");
        list.add("无锡CTVPN53727H");
        list.add("无锡CTVPN53727I");
        list.add("苏州CTVPN54389A");
        list.add("苏州CTVPN54389B");
        list.add("南通CTVPN18921B");
        list.add("南通CTVPN18921A");
        list.add("南京CTVPN80000F");
        list.add("南京CTVPN80000G");
        list.add("苏州CTVPN53727M");
        list.add("苏州CTVPN53727N");
        list.add("南京CTVPN18921C");
        list.add("南京CTVPN18921B");
        list.add("无锡CTVPN18921B");
        list.add("无锡CTVPN18921C");
        list.add("苏州CTVPN57933B");
        list.add("苏州CTVPN57933A");
        list.add("宿迁CTVPN80130I");
        list.add("宿迁CTVPN80130J");
        list.add("苏州CTVPN54389C");
        list.add("苏州CTVPN54389D");
        list.add("泰州CTVPN57969A");
        list.add("泰州CTVPN57969B");
        list.add("苏州CTVPN57969A");
        list.add("苏州CTVPN57969B");
        list.add("徐州CTVPN80130N");
        list.add("徐州CTVPN80130M");
        list.add("苏州CTVPN53727P");
        list.add("苏州CTVPN53727O");
        list.add("苏州CTVPN80012AT");
        list.add("苏州CTVPN80012AU");
        list.add("无锡CTVPN80000B");
        list.add("无锡CTVPN80000A");
        list.add("南京CTVPN57818B");
        list.add("南京CTVPN57818A");
        list.add("苏州CTVPN57942A");
        list.add("苏州CTVPN57942B");
        list.add("苏州CTVPN53727S");
        list.add("苏州CTVPN53727T");
        list.add("扬州CTVPN57933B");
        list.add("扬州CTVPN57933A");
        list.add("南京CTVPN80012AK");
        list.add("盐城CTVPN55717C");
        list.add("盐城CTVPN55717D");
        list.add("无锡CTVPN53727J");
        list.add("无锡CTVPN53727K");
        list.add("苏州CTVPN57933C");
        list.add("苏州CTVPN57933D");
        list.add("苏州CTVPN53727Y");
        list.add("苏州CTVPN53727W");
        list.add("无锡CTVPN29202B");
        list.add("无锡CTVPN29202A");
        list.add("南京CTVPN57933D");
        list.add("南京CTVPN57933C");
        list.add("常州CTVPN57933A");
        list.add("常州CTVPN57933B");
        list.add("盐城CTVPN57922A");
        list.add("盐城CTVPN57922B");
        list.add("连云港CTVPN57933B");
        list.add("连云港CTVPN57933A");
        list.add("南通CTVPN57933B");
        list.add("南通CTVPN57933A");
        list.add("无锡CTVPN53615D");
        list.add("无锡CTVPN53615C");
        list.add("淮安CTVPN57933B");
        list.add("淮安CTVPN57933A");
        list.add("南京CTVPN57933A");
        list.add("南京CTVPN57933B");
        list.add("扬州CTVPN29136D");
        list.add("扬州CTVPN29136C");
        list.add("苏州CTVPN56105A");
        list.add("苏州CTVPN56105B");
        list.add("无锡CTVPN56399C");
        list.add("常州CTVPN29290B");
        list.add("常州CTVPN29290A");
        list.add("常州CTVPN29342C");
        list.add("常州CTVPN29342D");
        list.add("常州CTVPN29342B");
        list.add("苏州CTVPN29342A");
        list.add("常州CTVPN29342A");
        list.add("苏州CTVPN29342B");
        list.add("苏州CTVPN29342C");
        list.add("苏州CTVPN29342D");
        list.add("南京CTVPN58334B");
        list.add("南京CTVPN58334A");
        list.add("南通CTVPN58334B");
        list.add("南通CTVPN58334A");
        list.add("南京CTVPN29342B");
        list.add("南京CTVPN29342A");
        list.add("南京CTVPN29297A");
        list.add("南京CTVPN29297B");
        list.add("苏州CTVPN57964H");
        list.add("苏州CTVPN57964I");
        list.add("苏州YCTVPN58258E");
        list.add("苏州YCTVPN58258F");
        list.add("苏州YCTVPN58258G");
        list.add("无锡CTVPN57964B");
        list.add("无锡CTVPN57964C");
        String filePath = "D://1.txt";
        File file = new File(filePath);
        FileWriter writer = new FileWriter(file);
        for(int i =0;i<list.size();i++){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("circuit_no", list.get(i));
            writer.write(list.get(i).toString()+","+getInfo(headers, jsonObject, response).toString()+"/n");
        }
        writer.flush();
        writer.close();
    }
}
