package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.OptGroupService;
import com.telecom.nrm.service.DualRouteOperationLogService;
import com.telecom.nrm.service.DocumentExportService;
import com.telecom.nrm.dto.ExportResponseDTO;
import com.telecom.nrm.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.SneakyThrows;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/opt-group-api")
@Slf4j
public class OptGroupController {

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    OptGroupService optGroupService;

    @Autowired
    OptGroupDao optGroupDao;

    @Autowired
    DualRouteOperationLogService dualRouteOperationLogService;

    @Autowired
    RegionDao regionDao;

    @Autowired
    DocumentExportService documentExportService;

    @PostMapping("/deleteOptGroupRecord")
    public Integer deleteOptGroupRecord(@RequestBody JSONObject jsonObject, HttpServletRequest request) throws IOException {
        // 记录删除操作日志
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();

            // 优先使用前端传递的对象信息，否则查询获取
            String objectCode = jsonObject.getString("code");
            String objectName = jsonObject.getString("name");

            // 如果前端没有传递，则查询获取
            if (objectCode == null || objectName == null) {
                JSONObject queryParam = new JSONObject();
                queryParam.put("id", jsonObject.getLong("id"));
                queryParam.put("ds", jsonObject.getString("ds"));
                JSONObject groupInfo = optGroupService.queryOptGroupDetail(queryParam);

                if (objectCode == null) {
                    objectCode = groupInfo != null ? groupInfo.getString("code") : null;
                }
                if (objectName == null) {
                    objectName = groupInfo != null ? groupInfo.getString("name") : null;
                }
            }

            // 构建具体的删除描述
            String description = String.format("删除光路组：%s", objectName != null ? objectName : "未知光路组");

            dualRouteOperationLogService.recordOperationLogFull(
                "delete", "opt_group", jsonObject.getLong("id"),
                objectCode, objectName, description,
                null, null, jsonObject.getString("ds"),
                jwtUser.getId(), jwtUser.getUsername(), request
            );
        } catch (Exception e) {
            log.warn("记录删除日志失败", e);
        }

        return optGroupDao.pm_opt_road_group_delete(jsonObject,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/deleteOpt")
    public Integer deleteOptRecord(@RequestBody JSONObject jsonObject, HttpServletRequest request) throws IOException {
        // 设置请求体到request属性中，供日志记录使用
        request.setAttribute("REQUEST_BODY", jsonObject.toJSONString());

        // 记录删除光路元素操作日志
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();

            // 获取删除前数据
            JSONObject beforeData = new JSONObject();
            if (jsonObject.containsKey("before_data") && jsonObject.getJSONObject("before_data") != null) {
                beforeData = jsonObject.getJSONObject("before_data");
                log.info("🎯 使用前端传入的光路元素删除前数据: {}", beforeData.toJSONString());
            } else {
                log.info("🔍 前端未传入光路元素before_data，使用请求参数构建");
                beforeData.put("id", jsonObject.getLong("id"));
                beforeData.put("code", jsonObject.getString("code"));
                beforeData.put("name", jsonObject.getString("name"));
            }

            // 构建删除后数据（标记为已删除）
            JSONObject afterData = new JSONObject();
            afterData.put("status", "已删除");
            afterData.put("delete_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            String objectCode = jsonObject.getString("code");
            String objectName = jsonObject.getString("name");

            String description = String.format("删除光路元素：%s (%s)", objectName, objectCode);

            dualRouteOperationLogService.recordOperationLogFull(
                "delete", "opt_element", jsonObject.getLong("id"),
                objectCode, objectName, description,
                beforeData, afterData, jsonObject.getString("ds"),
                jwtUser.getId(), jwtUser.getUsername(), request
            );
        } catch (Exception e) {
            log.warn("记录删除光路元素日志失败", e);
        }

        return optGroupDao.pm_opt_road_group_element_delete(jsonObject,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/upload_opt_group")
    public void upload_opt_group(@RequestParam("file") MultipartFile file,
                                @RequestParam("ds") String ds,
                                @RequestParam(value = "operator_id", required = false) String frontendOperatorId,
                                @RequestParam(value = "operator_name", required = false) String frontendOperatorName,
                                HttpServletRequest request) throws IOException {
        // String ds = "无锡";

        // 导入统计信息 - 使用 AtomicInteger 解决 lambda 表达式中的变量引用问题
        final AtomicInteger totalRows = new AtomicInteger(0);
        final AtomicInteger successOptCount = new AtomicInteger(0);
        final AtomicInteger failOptCount = new AtomicInteger(0);
        final Set<String> successGroupCodes = new HashSet<>();
        final Set<String> failGroupCodes = new HashSet<>();
        final List<String> errorMessages = new ArrayList<>();

        // 记录导入操作日志
        String operatorId = null;
        String operatorName = null;

        try {
            String fileName = file.getOriginalFilename();

            // 获取用户信息，优先使用前端传递的参数

            // 1. 优先使用前端传递的用户信息（已经按照业务规则处理过）
            if (frontendOperatorId != null && !frontendOperatorId.isEmpty()) {
                operatorId = frontendOperatorId;
                log.info("🎯 使用前端传入的operator_id: {}", operatorId);
            }
            if (frontendOperatorName != null && !frontendOperatorName.isEmpty()) {
                operatorName = frontendOperatorName;
                log.info("🎯 使用前端传入的operator_name: {}", operatorName);
            }

            // 2. 如果前端没传，从JWT获取并按照业务规则处理
            if (operatorId == null || operatorName == null) {
                try {
                    JwtUser jwtUser = SecurityContext.getJwtUser();
                    log.info("🔍 JWT用户信息: {}", jwtUser);

                    if (jwtUser != null) {
                        if (operatorId == null) {
                            // 使用JWT中的用户ID，检查多个可能的字段
                            operatorId = jwtUser.getId();

                            log.info("🔍 从JWT获取operator_id: {}", operatorId);
                        }
                        if (operatorName == null) {
                            // 按照业务规则构建operator_name: username@@@地市名称
                            String username = jwtUser.getUsername();

                            String areaName = getAreaNameByCode(ds); // 根据ds参数获取地市名称
                            operatorName = username + "@@@" + areaName;
                            log.info("🔍 从JWT构建operator_name: username={}, areaName={}, operatorName={}",
                                    username, areaName, operatorName);
                        }
                    } else {
                        log.warn("⚠️ JWT用户信息为空");
                    }
                } catch (Exception jwtEx) {
                    log.warn("从JWT获取用户信息失败: {}", jwtEx.getMessage());
                }
            }

            // 3. 最后的降级处理
            if (operatorId == null || operatorId.isEmpty()) {
                operatorId = "UNKNOWN_USER";
                log.warn("⚠️ 无法获取用户ID，使用默认值: {}", operatorId);
            }
            if (operatorName == null || operatorName.isEmpty()) {
                operatorName = "UNKNOWN_USER";
                log.warn("⚠️ 无法获取用户名，使用默认值: {}", operatorName);
            }

            log.info("🎯 最终确定的导入操作用户信息: fileName={}, ds={}, operatorId={}, operatorName={}",
                    fileName, ds, operatorId, operatorName);

            // 先记录导入开始日志，后续会更新结果
            dualRouteOperationLogService.recordOperationLogFull(
                "import", "opt_group", null,
                fileName, fileName, "开始导入光路组文件：" + fileName,
                null, null, ds,
                operatorId, operatorName, request
            );
        } catch (Exception e) {
            log.warn("记录导入日志失败", e);
        }



        List<Map<String, String>> list = null;
        Sheet sheet = null;
        Row row = null;
        String cellData = null;
        List<String> keys = null;
        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            keys = new ArrayList<>();
            log.info("wb:" + rownum + "," + column);

            String areaName = "";
            String name = "";
            String code = "";

            List<JSONObject> dataList = new ArrayList<>();
            if(rownum >1){
                totalRows.set(rownum - 1); // 减去标题行
                log.info("🎯 开始处理导入数据，总行数: {}", totalRows.get());

                for (int i = 1; i < rownum; i++) {
                    try {
                        JSONObject current_data = new JSONObject();
                        for (int j = 0; j < column; j++) {
                            Cell cell = sheet.getRow(i).getCell(j);
                            String cellValue=null;
                            if(!Objects.isNull(cell) || ObjectUtil.isNotEmpty(cellValue)){
                                if(cell.getCellType().equals(CellType.STRING)){
                                    cellValue = cell.getStringCellValue();
                                }else if(cell.getCellType().equals(CellType.NUMERIC)){
                                    cellValue = String.valueOf(cell.getNumericCellValue());
                                }
                                if(j==0 ) {
                                    areaName = cellValue;
                                    current_data.put("area_name", areaName);
                                };
                                if(j==1) current_data.put("leaf_region",cellValue);
                                if (j==2) {
                                    name = cellValue;
                                    current_data.put("name", name);
                                }

                                if (j==3) {
                                    code = cellValue;
                                    current_data.put("code", code);

                                }
                                if(j==4)current_data.put("opt_code",cellValue);
                                if(j==5) current_data.put("router_no",cellValue);
                                if(j==6) current_data.put("speciality",cellValue );;
                                if(j==7) current_data.put("customer",cellValue);
                                if(j==8) current_data.put("maintainers",cellValue);

                                if(j==9) current_data.put("note",cellValue);
                            }else{
                                if(j==0 ) current_data.put("area_name", areaName);
                                if(j==1)  current_data.put("leaf_region",cellValue);

                                if (j==2) current_data.put("name", name);

                                if (j==3) current_data.put("code", code);
                            }
                            current_data.put("source_type_id", 0);
                            current_data.put("state_id",1);
                        }

                        // 验证必要字段
                        String groupCode = current_data.getString("code");
                        if (groupCode == null || groupCode.trim().isEmpty()) {
                            errorMessages.add("第" + (i+1) + "行：光路组编码不能为空");
                            failGroupCodes.add("第" + (i+1) + "行");
                            continue;
                        }

                        dataList.add(current_data);
                        log.info("✅ 第{}行数据解析成功: groupCode={}", i+1, groupCode);
                    } catch (Exception e) {
                        String errorMsg = "第" + (i+1) + "行数据解析失败: " + e.getMessage();
                        errorMessages.add(errorMsg);
                        failGroupCodes.add("第" + (i+1) + "行");
                        log.warn("❌ {}", errorMsg);
                    }
                }

                log.info("🎯 数据解析完成，有效数据: {} 条", dataList.size());

                // 执行数据库插入操作
                try {
                    log.info("🎯 开始插入光路组数据");
                    PageUtils.doSomethingByPage(v->{
                        try {
                            optGroupDao.pm_opt_road_group_insert(v, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                            // 统计成功的光路组
                            for (JSONObject data : v) {
                                String groupCode = data.getString("code");
                                if (groupCode != null && !groupCode.trim().isEmpty()) {
                                    successGroupCodes.add(groupCode);
                                }
                            }
                        } catch (Exception e) {
                            log.warn("❌ 光路组插入失败: {}", e.getMessage());
                            for (JSONObject data : v) {
                                String groupCode = data.getString("code");
                                if (groupCode != null && !groupCode.trim().isEmpty()) {
                                    failGroupCodes.add(groupCode);
                                    errorMessages.add("光路组 " + groupCode + " 插入失败: " + e.getMessage());
                                }
                            }
                        }
                    }, dataList, 500);

                    log.info("🎯 开始插入光路元素数据");
                    PageUtils.doSomethingByPage(v->{
                        try {
                            optGroupDao.pm_opt_road_group_element_insert(v, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                            // 统计成功的光路
                            for (JSONObject data : v) {
                                String optCode = data.getString("opt_code");
                                if (optCode != null && !optCode.trim().isEmpty()) {
                                    successOptCount.incrementAndGet();
                                }
                            }
                        } catch (Exception e) {
                            log.warn("❌ 光路元素插入失败: {}", e.getMessage());
                            for (JSONObject data : v) {
                                String optCode = data.getString("opt_code");
                                if (optCode != null && !optCode.trim().isEmpty()) {
                                    failOptCount.incrementAndGet();
                                    errorMessages.add("光路 " + optCode + " 插入失败: " + e.getMessage());
                                }
                            }
                        }
                    }, dataList, 500);

                } catch (Exception e) {
                    log.error("❌ 导入过程发生异常: {}", e.getMessage(), e);
                    errorMessages.add("导入过程发生异常: " + e.getMessage());
                }

                log.info("🎯 导入完成统计:");
                log.info("  - 总行数: {}", totalRows.get());
                log.info("  - 成功光路组数: {}", successGroupCodes.size());
                log.info("  - 失败光路组数: {}", failGroupCodes.size());
                log.info("  - 成功光路数: {}", successOptCount.get());
                log.info("  - 失败光路数: {}", failOptCount.get());
            }
        }

        // 导入完成后，记录最终结果日志
        try {
            fileName = file.getOriginalFilename();

            // 构建详细的导入结果描述
            StringBuilder resultDescription = new StringBuilder();
            resultDescription.append("导入光路组文件：").append(fileName);
            resultDescription.append("，总行数：").append(totalRows.get());
            resultDescription.append("，成功光路组：").append(successGroupCodes.size()).append("个");
            resultDescription.append("，失败光路组：").append(failGroupCodes.size()).append("个");
            resultDescription.append("，成功光路：").append(successOptCount.get()).append("条");
            resultDescription.append("，失败光路：").append(failOptCount.get()).append("条");

            if (!errorMessages.isEmpty()) {
                resultDescription.append("，错误信息：").append(String.join("; ", errorMessages.subList(0, Math.min(3, errorMessages.size()))));
                if (errorMessages.size() > 3) {
                    resultDescription.append("等").append(errorMessages.size()).append("个错误");
                }
            }

            // 判断导入是否成功
            boolean isSuccess = failGroupCodes.size() == 0 && failOptCount.get() == 0;
            String operationType = isSuccess ? "import_success" : "import_partial";

            log.info("🎯 记录最终导入结果: operationType={}, description={}", operationType, resultDescription.toString());

            dualRouteOperationLogService.recordOperationLogFull(
                operationType, "opt_group", null,
                fileName, fileName, resultDescription.toString(),
                null, null, ds,
                operatorId, operatorName, request
            );
        } catch (Exception e) {
            log.warn("记录导入结果日志失败", e);
        }
    }
    @PostMapping("/save_opt_group")
    public Integer saveOptGroup(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        // 设置请求体到request属性中，供日志记录使用
        request.setAttribute("REQUEST_BODY", jsonObject.toJSONString());

        // 调试：打印前端传入的完整参数
        log.info("🔍 接收到save_opt_group请求: {}", jsonObject.toJSONString());
        log.info("🔍 前端传入的关键字段: id={}, code={}, name={}, note={}, ds={}",
                jsonObject.getString("id"),
                jsonObject.getString("code"),
                jsonObject.getString("name"),
                jsonObject.getString("note"),
                jsonObject.getString("ds"));

        // jsonObject.put("area_name", jsonObject.getString("ds"));
        jsonObject.put("source_type_id",0);
        jsonObject.put("state_id",1);

        boolean isCreate = ObjectUtil.isEmpty(jsonObject.getString("id"));
        JSONObject beforeData = new JSONObject();

        // 如果是更新操作，先获取原数据
        if (!isCreate) {
            try {
                JSONObject queryParam = new JSONObject();
                queryParam.put("id", jsonObject.getString("id"));
                queryParam.put("ds", jsonObject.getString("ds"));
                // 这里应该调用查询接口获取原数据，暂时用空对象
                beforeData = new JSONObject();
                beforeData.put("id", jsonObject.getString("id"));
            } catch (Exception e) {
                log.warn("获取原数据失败", e);
            }
        }

        if (isCreate) {
            List<JSONObject> list = Arrays.asList(jsonObject);
            optGroupDao.pm_opt_road_group_insert(list, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));

            // 记录新增操作日志
            try {
                JwtUser jwtUser = SecurityContext.getJwtUser();

                String objectCode = jsonObject.getString("code");
                String objectName = jsonObject.getString("name");

                dualRouteOperationLogService.recordOperationLogFull(
                        "create", "opt_group", null,
                        objectCode, objectName, "新增光路组",
                        null, null, jsonObject.getString("ds"),
                        jwtUser.getId(), jwtUser.getUsername(), request
                );
            } catch (Exception e) {
                log.warn("记录新增日志失败", e);
            }
        } else {
            // 修改操作 - 优先使用前端传入的before_data，否则查询获取
            beforeData = new JSONObject();
            JSONObject afterData = new JSONObject();

            try {
                JwtUser jwtUser = SecurityContext.getJwtUser();

                // 1. 优先使用前端传入的before_data
                JSONObject currentData = null;
                if (jsonObject.containsKey("before_data") && jsonObject.getJSONObject("before_data") != null) {
                    beforeData = jsonObject.getJSONObject("before_data");
                    log.info("🎯 使用前端传入的修改前数据: {}", beforeData.toJSONString());
                } else {
                    // 2. 如果前端没传，则查询获取
                    log.info("🔍 前端未传入before_data，开始查询修改前数据: id={}, ds={}",
                            jsonObject.getLong("id"), jsonObject.getString("ds"));

                    JSONObject queryParam = new JSONObject();
                    queryParam.put("id", jsonObject.getLong("id"));
                    queryParam.put("ds", jsonObject.getString("ds"));
                    currentData = optGroupService.queryOptGroupDetail(queryParam);

                    if (currentData != null) {
                        log.info("🔍 查询到修改前数据: {}", currentData.toJSONString());

                        // 记录修改前的数据，使用安全的字段获取方式
                        String oldCode = currentData.getString("code");
                        String oldName = currentData.getString("name");
                        String oldNote = currentData.getString("note");
                        String oldUpdateTime = currentData.getString("update_time");
                        String oldAreaName = currentData.getString("area_name");
                        String oldLeafRegion = currentData.getString("leafRegion");
                        String oldSpeciality = currentData.getString("speciality");
                        String oldCustomer = currentData.getString("customer");
                        String oldMaintainers = currentData.getString("maintainers");

                        beforeData.put("code", oldCode != null ? oldCode : "");
                        beforeData.put("name", oldName != null ? oldName : "");
                        beforeData.put("note", oldNote != null ? oldNote : "");
                        beforeData.put("area_name", oldAreaName != null ? oldAreaName : "");
                        beforeData.put("leafRegion", oldLeafRegion != null ? oldLeafRegion : "");
                        beforeData.put("speciality", oldSpeciality != null ? oldSpeciality : "");
                        beforeData.put("customer", oldCustomer != null ? oldCustomer : "");
                        beforeData.put("maintainers", oldMaintainers != null ? oldMaintainers : "");
                        beforeData.put("last_update_time", oldUpdateTime != null ? oldUpdateTime : "");
                    } else {
                        log.warn("⚠️ 查询修改前数据失败，currentData为null: id={}, ds={}",
                                jsonObject.getLong("id"), jsonObject.getString("ds"));

                        // 即使查询失败，也要记录一些基本信息
                        beforeData.put("code", "查询失败");
                        beforeData.put("name", "查询失败");
                        beforeData.put("note", "查询失败");
                        beforeData.put("area_name", "查询失败");
                        beforeData.put("leafRegion", "查询失败");
                        beforeData.put("speciality", "查询失败");
                        beforeData.put("customer", "查询失败");
                        beforeData.put("maintainers", "查询失败");
                        beforeData.put("last_update_time", "查询失败");
                    }
                }

                // 获取修改前后的所有字段用于描述
                String oldCode = beforeData.getString("code");
                String newCode = jsonObject.getString("code");
                String oldName = beforeData.getString("name");
                String newName = jsonObject.getString("name");
                String oldNote = beforeData.getString("note");
                String newNote = jsonObject.getString("note");
                String oldAreaName = beforeData.getString("area_name");
                String newAreaName = jsonObject.getString("area_name");
                String oldLeafRegion = beforeData.getString("leafRegion");
                String newLeafRegion = jsonObject.getString("leafRegion");
                String oldSpeciality = beforeData.getString("speciality");
                String newSpeciality = jsonObject.getString("speciality");
                String oldCustomer = beforeData.getString("customer");
                String newCustomer = jsonObject.getString("customer");
                String oldMaintainers = beforeData.getString("maintainers");
                String newMaintainers = jsonObject.getString("maintainers");

                log.info("🎯 记录修改光路组操作: 保护组ID={}", jsonObject.getLong("id"));
                log.info("🔍 编码变化: [{}] → [{}]", oldCode, newCode);
                log.info("🔍 名称变化: [{}] → [{}]", oldName, newName);
                log.info("🔍 备注变化: [{}] → [{}]", oldNote, newNote);
                log.info("🔍 区域变化: [{}] → [{}]", oldAreaName, newAreaName);
                log.info("🔍 叶子区域变化: [{}] → [{}]", oldLeafRegion, newLeafRegion);
                log.info("🔍 专业变化: [{}] → [{}]", oldSpeciality, newSpeciality);
                log.info("🔍 关联客户变化: [{}] → [{}]", oldCustomer, newCustomer);
                log.info("🔍 维护人员变化: [{}] → [{}]", oldMaintainers, newMaintainers);

                // 执行修改操作
                optGroupDao.pm_opt_road_group_note_update(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));

                // 记录修改后的数据
                afterData.put("code", jsonObject.getString("code"));
                afterData.put("name", jsonObject.getString("name"));
                afterData.put("note", jsonObject.getString("note"));
                afterData.put("area_name", jsonObject.getString("area_name"));
                afterData.put("leafRegion", jsonObject.getString("leafRegion"));
                afterData.put("speciality", jsonObject.getString("speciality"));
                afterData.put("customer", jsonObject.getString("customer"));
                afterData.put("maintainers", jsonObject.getString("maintainers"));
                afterData.put("update_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                log.info("🔍 完整的afterData内容: {}", afterData.toJSONString());
                log.info("🔍 完整的beforeData内容: {}", beforeData.toJSONString());

                // 获取object_code和object_name，优先从前端传入，其次从查询结果，最后从请求参数
                String objectCode = null;
                String objectName = null;

                // 1. 优先从前端传入的参数获取
                if (jsonObject.getString("code") != null && !jsonObject.getString("code").isEmpty()) {
                    objectCode = jsonObject.getString("code");
                }
                if (jsonObject.getString("name") != null && !jsonObject.getString("name").isEmpty()) {
                    objectName = jsonObject.getString("name");
                }

                // 2. 如果前端没传，从查询结果获取
                if (objectCode == null && currentData != null) {
                    objectCode = currentData.getString("code");
                }
                if (objectName == null && currentData != null) {
                    objectName = currentData.getString("name");
                }

                // 3. 如果还是没有，使用默认值
                if (objectCode == null) {
                    objectCode = "未知编码";
                }
                if (objectName == null) {
                    objectName = "未知名称";
                }

                log.info("🏷️ 确定对象标识: objectCode={}, objectName={}", objectCode, objectName);

                // 构建详细的操作描述
                StringBuilder descriptionBuilder = new StringBuilder("修改光路组信息");

                // 检查所有字段的变化
                if (!Objects.equals(oldCode, newCode)) {
                    descriptionBuilder.append(String.format("，编码从[%s]修改为[%s]",
                        oldCode != null ? oldCode : "", newCode != null ? newCode : ""));
                }
                if (!Objects.equals(oldName, newName)) {
                    descriptionBuilder.append(String.format("，名称从[%s]修改为[%s]",
                        oldName != null ? oldName : "", newName != null ? newName : ""));
                }
                if (!Objects.equals(oldNote, newNote)) {
                    descriptionBuilder.append(String.format("，备注从[%s]修改为[%s]",
                        oldNote != null ? oldNote : "", newNote != null ? newNote : ""));
                }
                if (!Objects.equals(oldAreaName, newAreaName)) {
                    descriptionBuilder.append(String.format("，区域从[%s]修改为[%s]",
                        oldAreaName != null ? oldAreaName : "", newAreaName != null ? newAreaName : ""));
                }
                if (!Objects.equals(oldLeafRegion, newLeafRegion)) {
                    descriptionBuilder.append(String.format("，叶子区域从[%s]修改为[%s]",
                        oldLeafRegion != null ? oldLeafRegion : "", newLeafRegion != null ? newLeafRegion : ""));
                }
                if (!Objects.equals(oldSpeciality, newSpeciality)) {
                    descriptionBuilder.append(String.format("，专业从[%s]修改为[%s]",
                        oldSpeciality != null ? oldSpeciality : "", newSpeciality != null ? newSpeciality : ""));
                }
                if (!Objects.equals(oldCustomer, newCustomer)) {
                    descriptionBuilder.append(String.format("，关联客户从[%s]修改为[%s]",
                        oldCustomer != null ? oldCustomer : "", newCustomer != null ? newCustomer : ""));
                }
                if (!Objects.equals(oldMaintainers, newMaintainers)) {
                    descriptionBuilder.append(String.format("，维护人员从[%s]修改为[%s]",
                        oldMaintainers != null ? oldMaintainers : "", newMaintainers != null ? newMaintainers : ""));
                }

                String description = descriptionBuilder.toString();

                dualRouteOperationLogService.recordOperationLogFull(
                        "update", "opt_group", jsonObject.getLong("id"),
                        objectCode, objectName, description,
                        beforeData, afterData, jsonObject.getString("ds"),
                        jwtUser.getId(), jwtUser.getUsername(), request
                );
            } catch (Exception e) {
                log.warn("记录修改日志失败", e);
            }
        }

        return 1;
    }

    @PostMapping("/save_opt_group_element")
    public Integer saveOptGroupElement(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        // jsonObject.put("code", jsonObject.getString("opt_code"));
        // jsonObject.put("name", jsonObject.getString("opt_name"));

        boolean isCreate = ObjectUtil.isEmpty(jsonObject.getString("id"));

        if (isCreate) {
            List<JSONObject> list = Arrays.asList(jsonObject);
            optGroupDao.pm_opt_road_group_element_insert(list, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));

            // 记录新增光路元素日志
            try {
                JwtUser jwtUser = SecurityContext.getJwtUser();
                dualRouteOperationLogService.recordOperationLogFull(
                    "create", "opt_element", null,
                    jsonObject.getString("opt_code"), jsonObject.getString("opt_name"),
                    "新增光路元素", null, jsonObject, jsonObject.getString("ds"),
                    jwtUser.getId(), jwtUser.getUsername(), request
                );
            } catch (Exception e) {
                log.warn("记录新增光路元素日志失败", e);
            }
        } else {
            // 修改光路元素 - 处理before_data
            JSONObject beforeData = new JSONObject();
            JSONObject afterData = new JSONObject();

            try {
                JwtUser jwtUser = SecurityContext.getJwtUser();

                // 1. 优先使用前端传入的before_data
                if (jsonObject.containsKey("before_data") && jsonObject.getJSONObject("before_data") != null) {
                    beforeData = jsonObject.getJSONObject("before_data");
                    log.info("🎯 使用前端传入的光路元素修改前数据: {}", beforeData.toJSONString());
                } else {
                    log.info("🔍 前端未传入光路元素before_data，使用空对象");
                }

                // 执行修改操作
                optGroupDao.pm_opt_road_group_element_update(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));

                // 构建修改后数据
                afterData.put("id", jsonObject.getLong("id"));
                afterData.put("code", jsonObject.getString("code"));
                afterData.put("name", jsonObject.getString("name"));
                afterData.put("opt_code", jsonObject.getString("opt_code"));
                afterData.put("route_no", jsonObject.getString("route_no"));
                afterData.put("update_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                String objectCode = jsonObject.getString("code");
                String objectName = jsonObject.getString("name");

                // 构建详细的操作描述
                String oldCode = beforeData.getString("code");
                String newCode = jsonObject.getString("code");
                String description = String.format("修改光路元素，编码从[%s]修改为[%s]", oldCode, newCode);

                dualRouteOperationLogService.recordOperationLogFull(
                    "update", "opt_element", jsonObject.getLong("id"),
                    objectCode, objectName, description,
                    beforeData, afterData, jsonObject.getString("ds"),
                    jwtUser.getId(), jwtUser.getUsername(), request
                );
            } catch (Exception e) {
                log.warn("记录修改光路元素日志失败", e);
            }
        }

        return 1;
    }

    @PostMapping("/save_opt_group_element_sinlge")
    public Integer saveOptGroupElementSingle(@RequestBody JSONObject jsonObject) {
        // jsonObject.put("code", jsonObject.getString("opt_code"));
        // jsonObject.put("name", jsonObject.getString("opt_name"));
        if (ObjectUtil.isEmpty(jsonObject.getString("id"))) {
            List<JSONObject> list = Arrays.asList(jsonObject);
            optGroupDao.opt_group_element_single_insert(list, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        }else{
            optGroupDao.pm_opt_road_group_element_update(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        }
        return 1;
    }




    @GetMapping("")
    @LogAnnotation( interfaceName="光路保护组查询")
    public BiyiPageResult<JSONObject> pageQuery(@RequestParam(required = false) Map example,BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        return getList(jsonObject, pageable);
    }

    public BiyiPageResult<JSONObject> getList(JSONObject jsonObject, BiyiPageRequest pageable){
        //log.info("接口从这里进"+ jsonObject);
        List<JSONObject> optGroupList = new ArrayList<JSONObject>();
        jsonObject.put("object_type","optGroup");

        //读取创建人
        String createOp = jsonObject.getString("create_op");
        jsonObject.put("create_op", createOp);

        //读取创建时间范围（开始和结束）
        String createStartDate = jsonObject.getString("create_start_date");
        String createEndDate = jsonObject.getString("create_end_date");
        jsonObject.put("create_start_date", createStartDate);
        jsonObject.put("create_end_date", createEndDate);

        System.out.println(jsonObject+","+pageable);
        PageResponse<JSONObject> optGroupPage = optGroupDao.pm_opt_road_group_result_query(jsonObject, pageable.getSize(), pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        List<JSONObject> optGroupPageData = new ArrayList<>();
        if(optGroupPage!=null &&optGroupPage.getData()!=null && optGroupPage.getData().size() !=0){
            optGroupPageData = optGroupPage.getData();
            if(optGroupPageData !=null && optGroupPageData.size()!=0){
                JSONObjectUtil.convertBigNumberToString(optGroupPageData);
                optGroupList.addAll(optGroupPageData);
            }
        }
        List<String> ids = optGroupList.stream().map(optGroup -> optGroup.getString("id")).collect(Collectors.toList());
        JSONObject param_opt_group_member = new JSONObject();
        param_opt_group_member.put("ids", ids);
        List<JSONObject> opt_group_members = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(ids))opt_group_members = optGroupDao.pm_opt_road_group_element_query(param_opt_group_member, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        for(JSONObject optGroup: optGroupList) {
            optGroup.put("ds", jsonObject.getString("ds"));
            String check_result = optGroup.getString("checkResult");
            String exception_info = optGroup.getString("exceptionInfo");
            optGroup.put("result", !Objects.isNull(check_result)? check_result.equals("正常")? check_result : !Objects.isNull(exception_info)? check_result+"("+exception_info+")":check_result: "");
            if(ObjectUtil.isNotEmpty(opt_group_members)) optGroup.put("members", opt_group_members.stream().filter(item -> item.getString("group_id").equals(optGroup.getString("id"))).collect(Collectors.toList()));
        }
        BiyiPageResult<JSONObject> result =  new BiyiPageResult(optGroupList,optGroupPage.getPageInfo().getTotalCount() ,optGroupPage.getPageInfo().getPageSize());
        return result;
    }


    @PostMapping("getByIds")
    public List<JSONObject> pageQuery(@RequestBody JSONObject jsonObject) {
        List<JSONObject> optGroupList = new ArrayList<JSONObject>();
        jsonObject.put("object_type","optGroup");
        PageResponse<JSONObject> optGroupPage = optGroupDao.pm_opt_road_group_result_query(jsonObject, 1000000, 1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(optGroupPage!=null &&optGroupPage.getData()!=null && optGroupPage.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(optGroupPage.getData());
            optGroupList.addAll(optGroupPage.getData());
        }
        List<String> ids = optGroupList.stream().map(optGroup -> optGroup.getString("id")).collect(Collectors.toList());
        JSONObject param_opt_group_member = new JSONObject();
        param_opt_group_member.put("ids", ids);
        List<JSONObject> opt_group_members = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(ids))opt_group_members = optGroupDao.pm_opt_road_group_element_query(param_opt_group_member, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        for(JSONObject optGroup: optGroupList) {
            // String deal_result = optGroup.getString("dealResult");
            String check_result = optGroup.getString("checkResult");
            String exception_info = optGroup.getString("exceptionInfo");
            optGroup.put("result", !Objects.isNull(check_result)? check_result.equals("正常")? check_result : !Objects.isNull(exception_info)? check_result+"("+exception_info+")":check_result: "");
            if(ObjectUtil.isNotEmpty(opt_group_members)) optGroup.put("members", opt_group_members.stream().filter(item -> item.getString("group_id").equals(optGroup.getString("id"))).collect(Collectors.toList()));
        }
        return optGroupList;
    }



    @PostMapping("getoptGroupListByIds")
    public List<JSONObject> getoptGroupListByIds(@RequestBody JSONObject jsonObject) {
        List<JSONObject> optGroupList = new ArrayList<JSONObject>();
        jsonObject.put("object_type","optGroup");

        log.info("查询入参{}",jsonObject);
        PageResponse<JSONObject> optGroupPage = optGroupDao.pm_opt_road_group_result_query(jsonObject, 1000000, 1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(optGroupPage!=null &&optGroupPage.getData()!=null && optGroupPage.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(optGroupPage.getData());
            optGroupList.addAll(optGroupPage.getData());
        }
        for(JSONObject optGroup: optGroupList) {
            // String deal_result = optGroup.getString("dealResult");
            String check_result = optGroup.getString("checkResult");
            String exception_info = optGroup.getString("exceptionInfo");
            optGroup.put("result", !Objects.isNull(check_result)? check_result.equals("正常")? check_result : !Objects.isNull(exception_info)? check_result+"("+exception_info+")":check_result: "");
        }
        return optGroupList;
    }


    /**
     * 查看光路保护组详情 - 用于记录查看日志
     */
    @PostMapping("/detail")
    public ResponseEntity<JSONObject> getOptGroupDetail(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        // 设置请求体到request属性中，供日志记录使用
        request.setAttribute("REQUEST_BODY", jsonObject.toJSONString());

        try {
            log.info("🎯 接收到查看光路保护组详情请求: id={}, ds={}", jsonObject.getString("id"), jsonObject.getString("ds"));

            // 调用Service查询详情
            JSONObject result = optGroupService.queryOptGroupDetail(jsonObject);

            // 记录查看详情操作日志
            try {
                JwtUser jwtUser = SecurityContext.getJwtUser();
                log.info("🎯 记录查看光路保护组详情操作: 保护组ID={}, 操作人={}, 地市={}",
                        jsonObject.getString("id"), jwtUser.getUsername(), jsonObject.getString("ds"));

                // 获取object_code和object_name，优先使用前端传入的参数
                String objectCode = null;
                String objectName = null;

                // 1. 优先使用前端传入的参数
                if (jsonObject.getString("object_code") != null && !jsonObject.getString("object_code").isEmpty()) {
                    objectCode = jsonObject.getString("object_code");
                    log.info("🎯 使用前端传入的object_code: {}", objectCode);
                }
                if (jsonObject.getString("object_name") != null && !jsonObject.getString("object_name").isEmpty()) {
                    objectName = jsonObject.getString("object_name");
                    log.info("🎯 使用前端传入的object_name: {}", objectName);
                }

                // 2. 如果前端没传，从查询结果获取
                if (objectCode == null && result != null) {
                    objectCode = result.getString("code");
                    log.info("🔍 从查询结果获取object_code: {}", objectCode);
                }
                if (objectName == null && result != null) {
                    objectName = result.getString("name");
                    log.info("🔍 从查询结果获取object_name: {}", objectName);
                }

                // 3. 如果查询结果中也没有，尝试重新查询
                if (objectCode == null || objectName == null) {
                    log.warn("⚠️ 缺少对象信息，尝试重新查询: objectCode={}, objectName={}", objectCode, objectName);
                    try {
                        // 重新查询获取基本信息
                        JSONObject basicInfo = optGroupService.queryOptGroupDetail(jsonObject);
                        if (basicInfo != null) {
                            if (objectCode == null) {
                                objectCode = basicInfo.getString("code");
                                log.info("🔍 重新查询获取object_code: {}", objectCode);
                            }
                            if (objectName == null) {
                                objectName = basicInfo.getString("name");
                                log.info("🔍 重新查询获取object_name: {}", objectName);
                            }
                        }
                    } catch (Exception ex) {
                        log.warn("重新查询对象信息失败", ex);
                    }
                }

                // 4. 最后的降级处理
                if (objectCode == null) {
                    objectCode = "查询失败_ID" + jsonObject.getString("id");
                }
                if (objectName == null) {
                    objectName = "查询失败_保护组";
                }

                log.info("🏷️ 最终确定的对象标识: objectCode={}, objectName={}", objectCode, objectName);

                // 构建查看前数据
                JSONObject beforeData = new JSONObject();
                beforeData.put("view_status", "未查看");
                beforeData.put("last_view_time", null);
                beforeData.put("object_id", jsonObject.getString("id"));

                // 构建查看后数据
                JSONObject afterData = new JSONObject();
                afterData.put("view_status", "已查看");
                afterData.put("view_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                afterData.put("view_details", result != null ? "查看成功" : "查看失败");
                afterData.put("view_result_size", result != null ? "有数据" : "无数据");

                dualRouteOperationLogService.recordOperationLogFull(
                    "view", "opt_group", Long.valueOf(jsonObject.getString("id")),
                    objectCode, objectName,
                    "查看光路保护组详情",
                    beforeData, afterData, jsonObject.getString("ds"),
                    jwtUser.getId(), jwtUser.getUsername(), request
                );
            } catch (Exception e) {
                log.error("❌ 记录查看详情日志失败: 保护组ID={}", jsonObject.getString("id"), e);
            }

            return ResponseEntity.ok(result != null ? result : new JSONObject());

        } catch (Exception e) {
            log.error("查看光路保护组详情失败: id={}", jsonObject.getString("id"), e);
            return ResponseEntity.ok(new JSONObject());
        }
    }

    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> get_dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> regionList = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> cityList = new ArrayList<>();
        for(int i =0;i<regionList.size();i++){
            if(regionList.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                cityList.add(regionList.get(i));
            }
        }
        meta.put("cityList", cityList);

        String currentCityName = exampleJSON.getString("ds");
        if (ObjectUtil.isNotEmpty(currentCityName)) {
            Region currentCity = cityList.stream().filter(c->c.getName().equals(currentCityName)).findFirst().orElse(new Region());
            String currentCityId = currentCity.getId();
            List<Region> leafRegionList = regionList.stream().filter(r->ObjectUtil.isNotEmpty(r.getParentId()) && r.getParentId().equals(currentCityId)).collect(Collectors.toList());
            meta.put("leafRegionList", leafRegionList);
        }else {
            meta.put("leafRegionList", new ArrayList<Region>());
        }


        List<String> StateList = new ArrayList<>();
        StateList.add("正常");
        StateList.add("无光路");
        StateList.add("单光路");
        StateList.add("单路由");
        StateList.add("缺管道");
        StateList.add("同管道");
        StateList.add("同光缆");
        StateList.add("局内光路");
        StateList.add("同局前井");

        /*
        JSONObject lifeState = new JSONObject();
        lifeState.put("stateName", "同管道");
        StateList.add("lifeState");
        JSONObject lifeState1 = new JSONObject();
        lifeState1.put("stateName", "同光缆");
        StateList.add(lifeState1);
        JSONObject lifeState3 = new JSONObject();
        lifeState3.put("stateName", "正常");
        StateList.add(lifeState3);
        JSONObject lifeState4 = new JSONObject();
        lifeState4.put("stateName", "已忽略异常");
        StateList.add(lifeState4);

         */
        meta.put("StateList", StateList);
        meta.put("Statistic", optGroupService.optPair_statistic_dictionary(exampleJSON));
        JSONObject static_param = new JSONObject();
        static_param.put("static_type", "opt_group");
        meta.put("group_static",optGroupDao.pm_opt_road_group_static_query(static_param,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(exampleJSON.getString("ds"))));
        meta.put("speciality",optGroupDao.pm_opt_road_group_speciality_query(NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(exampleJSON.getString("ds"))));
        return ResponseEntity.ok().body(meta);
    }


    @GetMapping("/opt_pair_check")
    public void check(@RequestParam(required = false) Map example , BiyiPageRequest pageable){
        BiyiPageResult<JSONObject> multi_query = pageQuery(example, pageable);
        List<JSONObject> multi_query_list = multi_query.getData();
        for(JSONObject multi_opt : multi_query_list){
            JSONObject param = new JSONObject();
            param.put("ds",example.get("ds"));
            param.put("id", multi_opt.getString("id"));
            optGroupService.risk_analyze(param);
        }
    }

    @PostMapping("/note_update")
    public Integer note_update(@RequestBody JSONObject jsonObject, HttpServletRequest request){
        // 设置请求体到request属性中，供日志记录使用
        request.setAttribute("REQUEST_BODY", jsonObject.toJSONString());

        // 先查询修改前的备注信息
        JSONObject beforeData = new JSONObject();
        JSONObject afterData = new JSONObject();

        try {
            // 查询修改前的数据
            JSONObject queryParam = new JSONObject();
            queryParam.put("id", jsonObject.getLong("id"));
            queryParam.put("ds", jsonObject.getString("ds"));
            JSONObject currentData = optGroupService.queryOptGroupDetail(queryParam);

            String oldNote = currentData != null ? currentData.getString("note") : "";
            String newNote = jsonObject.getString("note") != null ? jsonObject.getString("note") : "";

            // 构建修改前后数据
            beforeData.put("note", oldNote);
            beforeData.put("last_update_time", currentData != null ? currentData.getString("update_time") : null);

            afterData.put("note", newNote);
            afterData.put("update_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            log.info("🎯 记录备注更新操作: 保护组ID={}, 修改前备注=[{}], 修改后备注=[{}]",
                    jsonObject.getLong("id"), oldNote, newNote);

        } catch (Exception e) {
            log.warn("查询修改前备注信息失败: {}", e.getMessage());
            beforeData.put("note", "查询失败");
            afterData.put("note", jsonObject.getString("note"));
        }

        // 执行更新操作
        Integer result = optGroupDao.pm_opt_road_group_note_update(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));

        // 记录详细的备注更新日志
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();
            String description = String.format("更新光路组备注信息，从[%s]修改为[%s]",
                    beforeData.getString("note"), afterData.getString("note"));

            dualRouteOperationLogService.recordOperationLogFull(
                "update", "opt_group", jsonObject.getLong("id"),
                jsonObject.getString("code"), jsonObject.getString("name"),
                description, beforeData, afterData, jsonObject.getString("ds"),
                jwtUser.getId(), jwtUser.getUsername(), request
            );
        } catch (Exception e) {
            log.warn("记录备注更新日志失败", e);
        }

        return result;
    }

    @PostMapping("/confirm_result")
    public Integer confirm_result(@RequestBody JSONObject jsonObject, HttpServletRequest request){
        jsonObject.put("object_type", "optGroup");
        jsonObject.put("confirm_status", "已确认");
        jsonObject.put("confirm_op", jsonObject.getString("userName"));

        // 记录确认操作日志
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();
            log.info("🎯 记录确认风险检测结果操作: 保护组ID={}, 操作人={}, 地市={}",
                    jsonObject.getLong("id"), jwtUser.getUsername(), jsonObject.getString("ds"));

            // 构建修改前数据（未确认状态）
            JSONObject beforeData = new JSONObject();
            beforeData.put("confirm_status", "未确认");
            beforeData.put("confirm_op", null);

            // 构建修改后数据（已确认状态）
            JSONObject afterData = new JSONObject();
            afterData.put("confirm_status", "已确认");
            afterData.put("confirm_op", jsonObject.getString("userName"));
            afterData.put("confirm_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            dualRouteOperationLogService.recordOperationLogFull(
                "confirm", "opt_group", jsonObject.getLong("id"),
                jsonObject.getString("code"), jsonObject.getString("name"), "确认风险检测结果",
                beforeData, afterData, jsonObject.getString("ds"),
                jwtUser.getId(), jwtUser.getUsername(), request
            );
        } catch (Exception e) {
            log.error("❌ 记录确认操作日志失败: 保护组ID={}", jsonObject.getLong("id"), e);
        }

        return optGroupDao.pm_risk_check_confirm(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/cancel_confirm_result")
    public Integer cancel_confirm_result(@RequestBody JSONObject jsonObject, HttpServletRequest request){
        jsonObject.put("object_type", "optGroup");
        jsonObject.put("confirm_status", "未确认");
        jsonObject.put("confirm_op", null);

        // 记录取消确认操作日志
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();
            log.info("🎯 记录取消确认风险检测结果操作: 保护组ID={}, 操作人={}, 地市={}",
                    jsonObject.getLong("id"), jwtUser.getUsername(), jsonObject.getString("ds"));

            // 构建修改前数据（已确认状态）
            JSONObject beforeData = new JSONObject();
            beforeData.put("confirm_status", "已确认");
            beforeData.put("confirm_op", jsonObject.getString("userName"));

            // 构建修改后数据（未确认状态）
            JSONObject afterData = new JSONObject();
            afterData.put("confirm_status", "未确认");
            afterData.put("confirm_op", null);
            afterData.put("cancel_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            dualRouteOperationLogService.recordOperationLogFull(
                "取消确认", "opt_group", jsonObject.getLong("id"),
                jsonObject.getString("code"), jsonObject.getString("name"), "取消确认风险检测结果",
                beforeData, afterData, jsonObject.getString("ds"),
                jwtUser.getId(), jwtUser.getUsername(), request
            );
        } catch (Exception e) {
            log.warn("记录取消确认操作日志失败", e);
        }

        return optGroupDao.pm_risk_check_confirm(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/risk-analyze")
    @LogAnnotation( interfaceName="光路保护组风险检测")
    public JSONObject riskAnalyze(@RequestBody JSONObject request, HttpServletRequest httpRequest){
        // 设置请求体到request属性中，供日志记录使用
        httpRequest.setAttribute("REQUEST_BODY", request.toJSONString());

        JSONObject result =  optGroupService.risk_analyze(request);

        // 记录风险检测操作日志
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();
            String checkResult = result != null ? result.getString("check_result") : "检测失败";
            String description = "风险检测，结果：" + checkResult;

            log.info("🎯 记录风险检测操作: 保护组ID={}, 保护组编码={}, 检测结果={}, 操作人={}, 地市={}",
                    request.getLong("id"), request.getString("code"), checkResult,
                    jwtUser.getUsername(), request.getString("ds"));

            // 构建检测前数据（检测前状态）
            JSONObject beforeData = new JSONObject();
            beforeData.put("check_status", "未检测");
            beforeData.put("last_check_time", null);

            // 构建检测后数据（检测结果）
            JSONObject afterData = new JSONObject();
            afterData.put("check_status", "已检测");
            afterData.put("check_result", checkResult);
            afterData.put("check_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            if (result != null) {
                // 只记录关键的检测结果信息，避免复杂对象序列化问题
                afterData.put("check_details", "检测完成，结果：" + checkResult);
            }

            dualRouteOperationLogService.recordOperationLogFull(
                "risk_detect", "opt_group", request.getLong("id"),
                request.getString("code"), request.getString("name"),
                description, beforeData, afterData, request.getString("ds"),
                jwtUser.getId(), jwtUser.getUsername(), httpRequest
            );
        } catch (Exception e) {
            log.error("❌ 记录风险检测日志失败: 保护组ID={}", request.getLong("id"), e);
        }

        return result;
    }
    @LogAnnotation( interfaceName="光路保护组风险检测")
    @PostMapping("/risk-analyze-previous")
    public JSONObject riskAnalyzePrevious(@RequestBody JSONObject request){
        JSONObject result =  optGroupService.risk_analyze_previous(request);
        return result;

    }

    @PostMapping("/risk-analyze-his")
    public JSONObject riskAnalyzeHis(@RequestBody JSONObject request, HttpServletResponse response){
        JSONObject result =  optGroupService.risk_analyze_his(request);
        return result;
    }

    /**
     * 临时光路组风险检测（用于隐患整改等场景）
     * 不保存到数据库，直接返回检测结果
     */
    @PostMapping("/risk-analyze-temp")
    @LogAnnotation(interfaceName="临时光路组风险检测")
    public JSONObject riskAnalyzeTemp(@RequestBody JSONObject request){
        log.info("临时光路组风险检测请求: {}", request);

        // 设置临时检测标识
        request.put("checkType", "hazard_remediation");
        request.put("tempGroup", true);

        // 调用风险分析服务
        JSONObject result = optGroupService.risk_analyze(request);

        log.info("临时光路组风险检测结果: {}", result);
        return result;
    }

    @PostMapping("/riskHisTimeList")
    public List<JSONObject> riskHisTimeList(@RequestBody JSONObject request, HttpServletResponse response){
        List<JSONObject> result =  optGroupService.pm_risk_check_his_time_list_query(request);
        return result;
    }


    @PostMapping("/white-insert")
    public Integer pair_circuit_input_fiber_result_corr_insert(@RequestBody JSONObject jsonObject){
        String type = jsonObject.getString("type");
        jsonObject.put("pipleline_code", jsonObject.getJSONObject("pipeSegment").getString("code"));
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_insert(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/white-delete")
    public Integer pipleline_white_delete(@RequestBody JSONObject jsonObject){
        String code = jsonObject.getJSONObject("pipeSegment").getString("code");
        if(code.contains("----已忽略")){
            code = code.substring(0, code.indexOf("----已忽略"));
        }
        jsonObject.put("pipleline_code", code.trim());
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_delete(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }


    @PostMapping("/opt_group_download")
    public ResponseEntity<ExportResponseDTO> opt_group_download(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("🎯 [光路组导出] 接收到光路组数据导出请求");
            log.info("📋 [光路组导出] 请求参数: {}", jsonObject);

            // 参数验证
            if (jsonObject == null || jsonObject.isEmpty()) {
                log.error("❌ [光路组导出] 请求参数为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("请求参数不能为空"));
            }

            String ds = jsonObject.getString("ds");
            log.info("📍 [光路组导出] 查询地市: {}", ds);

            if (ds == null || ds.trim().isEmpty()) {
                log.error("❌ [光路组导出] 地市参数为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("地市参数不能为空"));
            }

            // 记录导出操作日志
            try {
                JwtUser jwtUser = SecurityContext.getJwtUser();
                log.info("🎯 [光路组导出] 记录导出操作: 操作人={}, 地市={}", jwtUser.getUsername(), ds);

                // 构建导出前数据（导出请求）
                JSONObject beforeData = new JSONObject();
                beforeData.put("export_status", "准备导出");
                beforeData.put("query_conditions", jsonObject.toJSONString());

                // 构建导出后数据（导出完成）
                JSONObject afterData = new JSONObject();
                afterData.put("export_status", "导出完成");
                afterData.put("export_time", java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                afterData.put("export_format", "Excel");

                dualRouteOperationLogService.recordOperationLogFull(
                    "export", "opt_group", null,
                    null, null, "导出光路组数据",
                    beforeData, afterData, ds,
                    jwtUser.getId(), jwtUser.getUsername(), request
                );
            } catch (Exception e) {
                log.error("❌ [光路组导出] 记录导出日志失败: 地市={}", ds, e);
            }

            // 1. 查询光路组数据
            log.info("🔍 [光路组导出] 步骤1: 开始查询光路组数据");
            long queryStartTime = System.currentTimeMillis();
            BiyiPageRequest pageable = new BiyiPageRequest();
            pageable.setSize(1000000000);
            pageable.setPage(1);
            List<JSONObject> result = pageQuery(jsonObject, pageable).getData();
            long queryEndTime = System.currentTimeMillis();
            log.info("⏱️ [光路组导出] 步骤1完成: 数据查询耗时 {} ms", (queryEndTime - queryStartTime));

            if (result == null || result.isEmpty()) {
                log.warn("⚠️ [光路组导出] 查询结果为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("没有查询到光路组数据"));
            }

            log.info("📊 [光路组导出] 查询到数据量: {} 条", result.size());

            // 2. 转换数据格式
            log.info("🔄 [光路组导出] 步骤2: 开始转换数据格式");
            long convertStartTime = System.currentTimeMillis();
            List<Map<String, Object>> exportData = convertOptGroupData(result);
            long convertEndTime = System.currentTimeMillis();
            log.info("⏱️ [光路组导出] 步骤2完成: 数据转换耗时 {} ms", (convertEndTime - convertStartTime));
            log.info("📊 [光路组导出] 转换后数据量: {} 条记录", exportData.size());

            // 3. 定义列
            log.info("📝 [光路组导出] 步骤3: 定义Excel列结构");
            List<String> columns = Arrays.asList(
                "保护组编码:code",
                "保护组名称:name",
                "地市:area_name",
                "专业:speciality",
                "检测结果:result",
                "处理结果:deal_result",
                "创建时间:create_time",
                "更新时间:update_time",
                "备注:notes",
                "状态:status",
                "风险等级:risk_level",
                "成员数量:member_count",
                "光路编码:opt_code",
                "光路名称:opt_name"
            );
            log.info("📋 [光路组导出] 步骤3完成: 定义了 {} 个列", columns.size());

            // 4. 使用通用导出服务
            log.info("📤 [光路组导出] 步骤4: 开始调用文档导出服务");
            long exportStartTime = System.currentTimeMillis();
            ExportResponseDTO exportResult = documentExportService.exportToDocumentSecurity(
                exportData, columns, "光路组数据", "光路组数据导出",
                "光路组管理", "/api/opt-group/export", "光路组数据导出"
            );
            long exportEndTime = System.currentTimeMillis();
            log.info("⏱️ [光路组导出] 步骤4完成: 文档导出服务耗时 {} ms", (exportEndTime - exportStartTime));

            if (exportResult.isSuccess()) {
                exportResult.setDataCount(exportData.size());
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("✅ [光路组导出] 导出成功完成!");
                log.info("📁 [光路组导出] 文件名: {}", exportResult.getFileName());
                log.info("📊 [光路组导出] 数据量: {} 条", exportData.size());
                log.info("⏱️ [光路组导出] 总耗时: {} ms", totalTime);
                log.info("📈 [光路组导出] 性能统计 - 查询: {}ms, 转换: {}ms, 导出: {}ms",
                    (queryEndTime - queryStartTime), (convertEndTime - convertStartTime), (exportEndTime - exportStartTime));
            } else {
                log.error("❌ [光路组导出] 导出失败: {}", exportResult.getMessage());
            }

            return ResponseEntity.ok(exportResult);

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("💥 [光路组导出] 导出异常，总耗时: {} ms", totalTime);
            log.error("💥 [光路组导出] 异常详情: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    @PostMapping("/deal_result_update_ignore")
    public JSONObject updateDealResultIgnore(@RequestBody JSONObject jsonObject, HttpServletResponse response, HttpServletRequest request) throws IOException {
        // 设置请求体到request属性中，供日志记录使用
        request.setAttribute("REQUEST_BODY", jsonObject.toJSONString());

        // 调试：打印前端传入的完整参数
        log.info("🔍 接收到deal_result_update_ignore请求: {}", jsonObject.toJSONString());
        log.info("🔍 前端传入的关键字段: id={}, code={}, name={}, ds={}, areaName={}, userCode={}",
                jsonObject.getString("id"),
                jsonObject.getString("code"),
                jsonObject.getString("name"),
                jsonObject.getString("ds"),
                jsonObject.getString("areaName"),
                jsonObject.getString("userCode"));

        String deal_result = "忽略异常("+jsonObject.getString("userCode")+")";

        // 记录忽略风险操作日志
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();

            String objectCode = jsonObject.getString("code");
            String objectName = jsonObject.getString("name");
            String areaCode = jsonObject.getString("ds");

            log.info("🎯 忽略风险记录日志: objectCode={}, objectName={}, areaCode={}", objectCode, objectName, areaCode);

            dualRouteOperationLogService.recordOperationLogFull(
                "ignore_risk", "opt_group", jsonObject.getLong("id"),
                objectCode, objectName,
                "忽略风险异常", null, null, areaCode,
                jwtUser.getId(), jwtUser.getUsername(), request
            );
        } catch (Exception e) {
            log.warn("记录忽略风险日志失败", e);
        }

        return updateDealResult_common(jsonObject, response, deal_result);
    }

    @PostMapping("/deal_result_update")
    public JSONObject updateDealResult(@RequestBody JSONObject jsonObject, HttpServletResponse response, HttpServletRequest request) throws IOException {
        // 设置请求体到request属性中，供日志记录使用
        request.setAttribute("REQUEST_BODY", jsonObject.toJSONString());

        // 调试：打印前端传入的完整参数
        log.info("🔍 接收到deal_result_update请求: {}", jsonObject.toJSONString());
        log.info("🔍 前端传入的关键字段: id={}, code={}, name={}, ds={}, userCode={}",
                jsonObject.getString("id"),
                jsonObject.getString("code"),
                jsonObject.getString("name"),
                jsonObject.getString("ds"),
                jsonObject.getString("userCode"));

        String deal_result = "待整改("+jsonObject.getString("userCode")+")";

        // 记录恢复风险操作日志
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();

            // 获取object_code和object_name，优先使用前端传入的参数
            String objectCode = null;
            String objectName = null;

            // 1. 优先使用前端传入的参数
            if (jsonObject.getString("code") != null && !jsonObject.getString("code").isEmpty()) {
                objectCode = jsonObject.getString("code");
                log.info("🎯 恢复风险使用前端传入的object_code: {}", objectCode);
            }
            if (jsonObject.getString("name") != null && !jsonObject.getString("name").isEmpty()) {
                objectName = jsonObject.getString("name");
                log.info("🎯 恢复风险使用前端传入的object_name: {}", objectName);
            }

            // 2. 如果前端没传，则查询获取
            if (objectCode == null || objectName == null) {
                log.info("🔍 前端未传入完整对象信息，查询获取: objectCode={}, objectName={}", objectCode, objectName);
                JSONObject queryParam = new JSONObject();
                queryParam.put("id", jsonObject.getLong("id"));
                queryParam.put("ds", jsonObject.getString("ds"));
                JSONObject groupInfo = optGroupService.queryOptGroupDetail(queryParam);

                if (objectCode == null && groupInfo != null) {
                    objectCode = groupInfo.getString("code");
                    log.info("🔍 查询获取object_code: {}", objectCode);
                }
                if (objectName == null && groupInfo != null) {
                    objectName = groupInfo.getString("name");
                    log.info("🔍 查询获取object_name: {}", objectName);
                }
            }

            // 3. 最后的降级处理
            if (objectCode == null) {
                objectCode = "未知编码_ID" + jsonObject.getLong("id");
            }
            if (objectName == null) {
                objectName = "未知名称_保护组";
            }

            log.info("🏷️ 恢复风险最终对象标识: objectCode={}, objectName={}", objectCode, objectName);

            dualRouteOperationLogService.recordOperationLogFull(
                "restore_risk", "opt_group", jsonObject.getLong("id"),
                objectCode, objectName, "恢复风险状态",
                null, null, jsonObject.getString("ds"),
                jwtUser.getId(), jwtUser.getUsername(), request
            );
        } catch (Exception e) {
            log.warn("记录恢复风险日志失败", e);
        }

        return updateDealResult_common(jsonObject, response, deal_result);
    }

    public JSONObject updateDealResult_common(JSONObject jsonObject, HttpServletResponse response, String deal_result){
        jsonObject.put("ds",jsonObject.getString("areaName"));
        jsonObject.put("object_id",jsonObject.getString("id"));
        optGroupService.get_previous_risk_check_and_his_save(jsonObject);
        jsonObject.put("deal_result", deal_result);
        Integer result = optGroupService.updateOptRiskDealResult(jsonObject);
        JSONObject result_j = new JSONObject();
        if(result ==0){
            result_j.put("result","success");
        }else result_j.put("result","fail");
        return result_j;
    }

    @PostMapping("/optCheck")
    public void optCheck(@RequestBody JSONObject jsonObject,BiyiPageRequest pageable) throws IOException {
        new Thread(){
            @Override
            public void run(){
                BiyiPageResult<JSONObject> optGroupBiyi = getList(jsonObject, pageable);
                if (ObjectUtil.isNotNull(optGroupBiyi)) {
                    for (JSONObject optGroup : optGroupBiyi.getData()) {
                        // 创建一个模拟的HttpServletRequest，或者调用不需要HttpServletRequest的方法
                        try {
                            // 直接调用optGroupService的风险分析方法，避免需要HttpServletRequest参数
                            optGroupService.risk_analyze(optGroup);
                        } catch (Exception e) {
                            log.warn("批量风险检测失败: " + optGroup.getString("id"), e);
                        }
                    };
                }
            }
        }.start();
    }


    @PostMapping("/optCheck_ds")
    public void optCheck(@RequestBody JSONObject jsonObject) throws IOException {
        jsonObject.put("check_time","1");
        BiyiPageRequest pageRequest = new BiyiPageRequest();
        pageRequest.setSize(100000);
        pageRequest.setPage(1);
        new Thread(){
            @Override
            public void run(){
                BiyiPageResult<JSONObject> optGroupBiyi = getList(jsonObject, pageRequest);
                if (ObjectUtil.isNotNull(optGroupBiyi)) {
                    for (JSONObject optGroup : optGroupBiyi.getData()) {
                        // 直接调用optGroupService的风险分析方法，避免需要HttpServletRequest参数
                        try {
                            optGroupService.risk_analyze(optGroup);
                        } catch (Exception e) {
                            log.warn("批量风险检测失败: " + optGroup.getString("id"), e);
                        }
                    };
                }
            }
        }.start();
    }

    /**
     * 根据地市编码获取地市名称
     */
    private String getAreaNameByCode(String areaCode) {
        if ("js".equals(areaCode)) return "江苏";
        if ("wx".equals(areaCode)) return "无锡";
        if ("nj".equals(areaCode)) return "南京";
        if ("sz".equals(areaCode)) return "苏州";
        if ("xz".equals(areaCode)) return "徐州";
        if ("cz".equals(areaCode)) return "常州";
        if ("nt".equals(areaCode)) return "南通";
        if ("lyg".equals(areaCode)) return "连云港";
        if ("ha".equals(areaCode)) return "淮安";
        if ("yc".equals(areaCode)) return "盐城";
        if ("yz".equals(areaCode)) return "扬州";
        if ("zj".equals(areaCode)) return "镇江";
        if ("tz".equals(areaCode)) return "泰州";
        if ("sq".equals(areaCode)) return "宿迁";
        return areaCode != null ? areaCode : "未知地区";
    }

    /**
     * 获取标准化的操作人信息
     * @param areaCode 地市编码
     * @return [operatorId, operatorName] 数组
     */
    private String[] getStandardOperatorInfo(String areaCode) {
        try {
            JwtUser jwtUser = SecurityContext.getJwtUser();
            if (jwtUser != null) {
                String operatorId = jwtUser.getId();
                String username = jwtUser.getUsername();
                String areaName = getAreaNameByCode(areaCode);
                String operatorName = username + "@@@" + areaName;

                log.info("🎯 获取标准化操作人信息: operatorId={}, username={}, areaCode={}, areaName={}, operatorName={}",
                        operatorId, username, areaCode, areaName, operatorName);

                return new String[]{operatorId, operatorName};
            }
        } catch (Exception e) {
            log.warn("获取JWT用户信息失败: {}", e.getMessage());
        }

        // 降级处理
        String areaName = getAreaNameByCode(areaCode);
        return new String[]{"UNKNOWN_USER", "UNKNOWN_USER@@@" + areaName};
    }

    /**
     * 转换光路组数据格式
     */
    private List<Map<String, Object>> convertOptGroupData(List<JSONObject> result) {
        log.info("🔄 [数据转换] 开始转换光路组数据格式，输入数据量: {}", result.size());
        List<Map<String, Object>> data = new ArrayList<>();

        try {
            int processedCount = 0;
            int expandedCount = 0;

            for (JSONObject optGroup : result) {
                try {
                    // 获取光路组成员
                    JSONArray members = optGroup.getJSONArray("members");
                    int memberSize = members != null ? members.size() : 0;

                    if (memberSize == 0) {
                        // 没有成员的光路组，创建一行记录
                        Map<String, Object> row = createOptGroupRow(optGroup, null);
                        data.add(row);
                        expandedCount++;
                    } else {
                        // 有成员的光路组，为每个成员创建一行记录
                        for (int j = 0; j < memberSize; j++) {
                            try {
                                JSONObject member = members.getJSONObject(j);
                                Map<String, Object> row = createOptGroupRow(optGroup, member);
                                data.add(row);
                                expandedCount++;
                            } catch (Exception e) {
                                log.error("❌ [数据转换] 转换光路组成员时出错，光路组ID: {}, 成员索引: {}, 错误: {}",
                                    optGroup.getString("id"), j, e.getMessage());
                            }
                        }
                    }

                    processedCount++;

                    // 每处理100个光路组输出一次进度
                    if (processedCount % 100 == 0) {
                        log.info("📊 [数据转换] 已处理 {} / {} 个光路组，展开为 {} 条记录",
                            processedCount, result.size(), expandedCount);
                    }

                } catch (Exception e) {
                    log.error("❌ [数据转换] 转换第 {} 个光路组时出错: {}", processedCount + 1, e.getMessage());
                    log.error("❌ [数据转换] 出错的光路组数据: {}", optGroup);
                }
            }

            log.info("✅ [数据转换] 转换完成");
            log.info("📊 [数据转换] 成功处理光路组: {} 个", processedCount);
            log.info("📊 [数据转换] 展开为记录数: {} 条", expandedCount);

        } catch (Exception e) {
            log.error("💥 [数据转换] 数据转换过程中发生异常: {}", e.getMessage(), e);
        }

        log.info("🎯 [数据转换] 最终输出数据量: {}", data.size());
        return data;
    }

    /**
     * 创建光路组行数据
     */
    private Map<String, Object> createOptGroupRow(JSONObject optGroup, JSONObject member) {
        Map<String, Object> row = new HashMap<>();

        // 光路组基本信息
        row.put("code", optGroup.getString("code") != null ? optGroup.getString("code") : "");
        row.put("name", optGroup.getString("name") != null ? optGroup.getString("name") : "");
        row.put("area_name", optGroup.getString("area_name") != null ? optGroup.getString("area_name") : "");
        row.put("speciality", optGroup.getString("speciality") != null ? optGroup.getString("speciality") : "");
        row.put("result", optGroup.getString("result") != null ? optGroup.getString("result") : "");
        row.put("deal_result", optGroup.getString("deal_result") != null ? optGroup.getString("deal_result") : "");
        row.put("create_time", optGroup.getString("create_time") != null ? optGroup.getString("create_time") : "");
        row.put("update_time", optGroup.getString("update_time") != null ? optGroup.getString("update_time") : "");
        row.put("notes", optGroup.getString("notes") != null ? optGroup.getString("notes") : "");
        row.put("status", optGroup.getString("status") != null ? optGroup.getString("status") : "");
        row.put("risk_level", optGroup.getString("risk_level") != null ? optGroup.getString("risk_level") : "");

        // 成员数量
        JSONArray members = optGroup.getJSONArray("members");
        row.put("member_count", members != null ? members.size() : 0);

        // 光路成员信息（如果有）
        if (member != null) {
            row.put("opt_code", member.getString("code") != null ? member.getString("code") : "");
            row.put("opt_name", member.getString("name") != null ? member.getString("name") : "");
        } else {
            row.put("opt_code", "");
            row.put("opt_name", "");
        }

        return row;
    }

}
