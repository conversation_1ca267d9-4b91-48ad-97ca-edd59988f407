package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.nrm.dto.ApiCodeCountDTO;
import com.telecom.nrm.dto.ApiCodeCountResponse;
import com.telecom.nrm.dto.AppApiCountDTO;
import com.telecom.nrm.dto.AppApiCountResponse;
import com.telecom.nrm.service.DaasInterfaceEsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/DaasInterfaceEs")
@Slf4j
public class DaasInterfaceEsController {

    @Autowired
    DaasInterfaceEsService daasInterfaceEsService;

    @GetMapping("/api-code-counts")
    public BiyiPageResult<JSONObject> getApiCodeCallCounts(
            @RequestParam String startTime,
            @RequestParam String endTime,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {

        ApiCodeCountResponse apiCodeCountResponse = daasInterfaceEsService.getApiCodeCallCounts(startTime, endTime, page, size);


        return new BiyiPageResult(apiCodeCountResponse.getData(), apiCodeCountResponse.getTotalCount(),apiCodeCountResponse.getTotalCount());
    }

    @GetMapping("/app-api-counts")
    public BiyiPageResult<JSONObject> getAppApiCallCounts(
            @RequestParam String startTime,
            @RequestParam String endTime,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {

        AppApiCountResponse appApiCallCounts = daasInterfaceEsService.getAppApiCallCounts(startTime, endTime, page, size);


        return new BiyiPageResult(appApiCallCounts.getData(), appApiCallCounts.getTotalCount(),appApiCallCounts.getTotalCount());
    }

}
