package com.telecom.nrm.thread;

import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class Task implements Runnable {


    private String ds;

    private String au;

    public Task(String ds, String au) {
        this.ds = ds;
        this.au = au;
    }

    public String getDs() {
        return ds;
    }

    public void setDs(String ds) {
        this.ds = ds;
    }

    @Override
    public void run() {
        int jj = 1;
        System.out.println("当前地市为" + ds);
        String inputFile = "src/main/resources/test.xlsx";
        String outputFile = "src/main/resources/excel/"; // 新的输出文件
        try {
            FileInputStream fis = new FileInputStream(inputFile);
            Workbook workbook = new XSSFWorkbook(fis);
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

            // 创建一个新的工作簿来保存更新后的数据
            Workbook outputWorkbook = new XSSFWorkbook();
            Sheet outputSheet = outputWorkbook.createSheet();

            // 遍历工作表中的所有行和列,并将更新后的数据写入新的工作簿
            int rowIndex = 1;
            for (int i = 0; i < sheet.getLastRowNum(); i++) {
              /*  if (jj > 10) {
                    break;
                }*/

                Row row = sheet.getRow(i);
                Cell ds = row.getCell(0);
                // 填充第一列
                if (i == 0) {
                    Row firstout = outputSheet.createRow(0);
                    for (int j = 0; j < row.getLastCellNum(); j++) {
                        Cell cell = row.getCell(j);
                        //填充原来的内容
                        if (cell != null) {
                            firstout.createCell(j).setCellValue(cell.getStringCellValue());
                        }
                    }
                }
                if (ds != null && this.ds.equals(ds.getStringCellValue())) {
                    jj++;
                    Row copyRow = outputSheet.createRow(rowIndex++);
                    for (int j = 0; j < row.getLastCellNum(); j++) {
                        Cell cell = row.getCell(j);
                        //填充原来的内容
                        if (cell != null) {
                            copyRow.createCell(j).setCellValue(cell.getStringCellValue());
                        }
                    }
                    Cell circuit_code = row.getCell(4);
                    if (circuit_code != null && StringUtils.isNotEmpty(circuit_code.getStringCellValue())) {
                        String url = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/circuitsorderapi";
                        Map<String, String> headers = new HashMap<>();
                        headers.put("Authorization", this.au);
                        Map<String, String> params = new HashMap<>();
                        params.put("ds", this.ds);
                        params.put("circuit_code", circuit_code.getStringCellValue());
                        try {
                            String result = HttpRequestsUtils.sendGet(url, headers, params);
                            Cell cell0 = copyRow.getCell(0);
                            if (cell0 == null) {
                                copyRow.createCell(0).setCellValue(ds.getStringCellValue());
                            } else {
                                copyRow.getCell(0).setCellValue(ds.getStringCellValue());
                            }
                            Cell cell1 = copyRow.getCell(1);

                            if (cell1 == null) {
                                copyRow.createCell(1).setCellValue(circuit_code.getStringCellValue());
                            } else {
                                copyRow.getCell(1).setCellValue(circuit_code.getStringCellValue());
                            }

                            Cell cell2 = copyRow.getCell(2);
                            if (cell2 == null) {
                                copyRow.createCell(2).setCellValue(result);
                            } else {
                                copyRow.getCell(2).setCellValue(result);
                            }
                            System.out.println("当前地市为" + ds + "当前电路为" + circuit_code.getStringCellValue() +
                                    "结果为" + result);
                        } catch (Exception e) {
                            log.error("", e);
                            Cell cell1 = copyRow.getCell(1);

                            if (cell1 == null) {
                                copyRow.createCell(1).setCellValue(circuit_code.getStringCellValue());
                            } else {
                                copyRow.getCell(1).setCellValue(circuit_code.getStringCellValue());
                            }

                            Cell cell2 = copyRow.getCell(2);
                            if (cell2 == null) {
                                copyRow.createCell(2).setCellValue("接口异常");
                            } else {
                                copyRow.getCell(2).setCellValue("接口异常");
                            }
                        }
                    }


                }

            }
            // 将新的工作簿写入输出文件
            FileOutputStream fos = new FileOutputStream(outputFile + ds + ".xlsx");
            outputWorkbook.write(fos);
            fos.close();
            System.out.println("Excel file updated successfully.");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
