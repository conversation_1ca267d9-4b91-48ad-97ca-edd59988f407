package com.telecom.nrm.thread.audit;


import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.service.HunderRiverService;
import com.telecom.nrm.utils.BeanUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;

@Data
public class QueryCache implements Callable<List<JSONObject>> {

    private List<String> param;

    private  int start;
    private  int end;
    private  int totalSize;
    @Autowired
    HunderRiverService hunderRiverService;

    @Override
    public List<JSONObject> call() throws Exception {
        System.out.println(String.format("总数量为%d 当前线程为%s,起始行为%d,结束行为%d",this.getTotalSize(),
                Thread.currentThread().getName(), this.getStart(), this.getEnd()));
        JSONObject jsonObject =new JSONObject();
        List<JSONObject> result =new ArrayList<>();
        if (CollectionUtils.isNotEmpty(this.param)){
            for (String str : this.param) {

                jsonObject.put("accessCode", str);
                this.hunderRiverService = BeanUtils.getBean(HunderRiverService.class);
               JSONObject  jsonObject1= hunderRiverService .zd(jsonObject);
                if (null != jsonObject1) result.add(jsonObject1);

            }
        }
        return result;
    }
}
