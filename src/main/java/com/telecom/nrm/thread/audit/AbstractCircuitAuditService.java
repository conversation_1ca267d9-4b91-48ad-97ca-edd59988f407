package com.telecom.nrm.thread.audit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.DaClient;
import com.telecom.da.client.DaProxyFactory;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.PmNetWorkLogDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.utils.ExcelUtil;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Slf4j
@Data
public abstract class AbstractCircuitAuditService implements ConverseData, Callable<List<AuditExcelDTO>> {


    private PmNetWorkLogDao pmNetWorkLogDao;
    private static final String CIRCUIT_URL = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/circuitsorderapi";
    private static final String AUTHORIZATION = "Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhQGEuY29tIiwibmFtZSI6IkhYX1dBTkdZVUBAQOWNl-S6rCIsInJlbSI6MCwiaWQiOiIxODEwOTMyOTYxODU5NDMyNDUwIiwiYXV0aCI6IiIsImV4cCI6MTcyOTk5NDcwNn0.Tw1m8gJUcCD2ZLlJnY5CdTohsudxXHmH6O8uGD2Krkd-pJsX5_zNklzzvBFsk5WbOg-ReakvG5NZKb2r94BnIQ";
    private static final String CLOUD_2_URL = "http://**************:7070/queryConfigDevice";
    private static final String MSTP_URL = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/ipranView";
    private static final String MTP_URL = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/mstp";
    public static String inputFilePath = "src/main/resources/excel/电路明细20240829-0910.xlsx";
    public static String outFilePath = "src/main/resources/excel/";
    public static SimpleDateFormat simpleDate = new SimpleDateFormat("yyyyMMddHHmmss");
    public static String[] sheetNames = {"Sheet1", "Sheet2", "Sheet3", "Sheet4", "IP虚拟网及数字电路", "云专网", "精品专线"};
   // public static String[] sheetNames = {"Sheet1", "Sheet2"};
    //public static String[] sheetNames = {"IP虚拟网及数字电路", "云专网", "精品专线"};
    public static int threadNum = 20;
    public static ExecutorService executorService = Executors.newFixedThreadPool(threadNum);
    private List<JSONObject> query;
    private int start;
    private int end;
    private int totalSize;
    public JSONObject mstpQuery(JSONObject body) {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", AUTHORIZATION);
        try {
            String result = HttpRequestsUtils.sendPost(MTP_URL, header, body);
            return JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("getMstpResult error:{}", e);
            throw new RuntimeException("接口异常" + "getMstpResult error" + e.getMessage());
        }

    }
    public String getAccessCodeByCircuitCode(String circuitCode) {

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", AUTHORIZATION);
        Map<String, String> params = new HashMap<>();
        params.put("page", "1");
        params.put("size", "10");
        String region = getRegionByCircuitCode(circuitCode);
        if (StringUtils.isEmpty(region)) {
            log.warn("getAccessCodeByCircuitCode error: circuitCode is empty");
            return null;
        }
        params.put("ds", region);
        params.put("circuit_code", circuitCode);
        String result = null;
        try {
            String str = HttpRequestsUtils.sendGet(CIRCUIT_URL, header, params);
            JSONObject jsonObject = JSONObject.parseObject(str);
            JSONArray data = jsonObject.getJSONArray("data");
            if (data.size() > 0) {
                result = jsonObject.getJSONArray("data").getJSONObject(0).getString("access_code");
            }
        } catch (Exception e) {
            log.error("getAccessCodeByCircuitCode error:{}", e);
            throw new RuntimeException("接口异常" + "getAccessCodeByCircuitCode error" + e.getMessage());
        }
        return result;
    }

    public JSONObject getMstpResult(JSONObject body) {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", AUTHORIZATION);
        try {
            String result = HttpRequestsUtils.sendPost(MSTP_URL, header, body);
            return JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("getMstpResult error:{}", e);
            throw new RuntimeException("接口异常" + "getMstpResult error" + e.getMessage());
        }

    }


    public String getCloud2Data(String circuitCode) {
   /*     JSONObject body = new JSONObject();
        body.put("circuitCode", circuitCode);*/
        Map<String, String> body = new HashMap<>();
        body.put("access_code", circuitCode);
        String result = null;
        try {
            result = HttpRequestsUtils.sendGet(CLOUD_2_URL, null, body);
        } catch (Exception e) {
            log.error("getCloud2Data error:{}", e);
            throw new RuntimeException("接口异常" + "getCloud2Data error" + e.getMessage());
        }
        return result;
    }

    public String getRegionByCircuitCode(String circuitCode) {
        if (StringUtils.isEmpty(circuitCode)) {
            return null;
        }
        for (ShardingEnum value : ShardingEnum.values()) {
            if (circuitCode.contains(value.getRegionName())) {
                return value.getRegionName();
            }
        }
        return null;
    }


    @Override
    public void getData() {

    }


    @Override
    public <T> List<T> auditData(List<JSONObject> query, Class<T> resultTyoe) {
        return null;
    }


    public static void main(String[] args) {

        DaClient client = new DaClient("http://nrm.oss.telecomjs.com:39049/daas-gray-api/interface", "net_resource_integration");// 测试
        DaProxyFactory factory = new DaProxyFactory(client);
        PmNetWorkLogDao dao = (PmNetWorkLogDao) factory.getInstance(PmNetWorkLogDao.class);
        List<JSONObject> query = new ArrayList<>();
        for (String sheetName : AbstractCircuitAuditService.sheetNames) {
            Sheet sheet = ExcelUtil.getXSSFWorkbookSheet(AbstractCircuitAuditService.inputFilePath, sheetName);
            if (sheet != null) {
                Row row = sheet.getRow(0);
                int codeIndex = 0, typeIndex = 0;
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    String value = row.getCell(i).getStringCellValue();
                    if ("电路代号".equals(value)) {
                        codeIndex = i;

                    }
                    if ("电路类别".equals(value)) {
                        typeIndex = i;
                        break;
                    }
                }
                for (int i = 1; i <10 ; i++) {
                    Row row1 = sheet.getRow(i);
                    String circuitCode = row1.getCell(codeIndex).getStringCellValue();
                    String type = row1.getCell(typeIndex).getStringCellValue();
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("circuitCode", circuitCode);
                    jsonObject.put("type", type);
                    query.add(jsonObject);
                }
            }
        }
        List<AuditExcelDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(query)) {
            int totalSize = query.size();
            int threadNum = AbstractCircuitAuditService.threadNum;
            int batchSize = (totalSize + threadNum - 1) / threadNum; // 计算每批处理的数量
            List<Future<List<AuditExcelDTO>>> futures = new ArrayList<>(threadNum);
            // 提交任务
            for (int i = 0; i < totalSize; i += batchSize) {
                int end = Math.min(i + batchSize, totalSize);
                AbstractCircuitAuditService abstractCircuitAuditService = new Ipran();
                abstractCircuitAuditService.setQuery(query.subList(i, end));
                abstractCircuitAuditService.setStart(i);
                abstractCircuitAuditService.setEnd(end-1);
                abstractCircuitAuditService.setTotalSize(totalSize);
                abstractCircuitAuditService.setPmNetWorkLogDao(dao);
                Future<List<AuditExcelDTO>> future = AbstractCircuitAuditService.executorService.submit(abstractCircuitAuditService);
                futures.add(future);
            }
            // 收集结果
            for (Future<List<AuditExcelDTO>> future : futures) {
                try {
                    List<AuditExcelDTO> result = future.get();
                    if (CollectionUtils.isNotEmpty(result)) {
                        resultList.addAll(result);
                    }
                } catch (Exception e) {
                    // 处理异常
                    e.printStackTrace();
                }
            }
            AbstractCircuitAuditService.executorService.shutdown(); // 关闭线程池
        }
        if (CollectionUtils.isNotEmpty(resultList)) {
            ExcelUtil.writeExcel(AbstractCircuitAuditService.outFilePath +
                            "云专网2.0" + AbstractCircuitAuditService.simpleDate.format(new Date()) + ".xlsx",
                    "云专网2.0", AuditExcelDTO.class, resultList);
        }

    }

    //判断电路名称内有几个地市（省内）
    public List<String> getCitysFromCircuit(String circuitCode) {
        List<String> areaIds = new ArrayList<>();

        Set<String> set = new HashSet<>();
        for (String value : NRMConstants.AREA_NAME_MAP.values()) {
            if (circuitCode.contains(value)) {
                if (set.add(NRMConstants.AREA_NAME_TO_ID_MAP.get(value))) {
                    areaIds.add(NRMConstants.AREA_NAME_TO_ID_MAP.get(value));
                }
            }
        }

        return areaIds;
    }

    public void getLinkArrayRec(JSONArray links, JSONArray result) {
        for (Object link : links) {
            JSONObject father = (JSONObject) link;
            result.add(father);
            JSONArray children = father.getJSONArray("children");
            if (children != null) {
                getLinkArrayRec(children, result);
            }
        }
    }

}
