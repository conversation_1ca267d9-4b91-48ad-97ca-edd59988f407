package com.telecom.nrm.thread.audit;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface ConverseData {

    /**
     * 稽核数据
     */
    void getData();

    /**
     * 入口在IPRAN处，收集 ipran 场景 OTN 场景 数字电路场景 稽核的数据
     * @param query
     * @param resultTyoe
     * @param <T>
     * @return
     */
    <T> List<T> auditData(List<JSONObject> query, Class<T> resultTyoe);

    /**
     * 数据入库
     */
  //  void insertData();
}
