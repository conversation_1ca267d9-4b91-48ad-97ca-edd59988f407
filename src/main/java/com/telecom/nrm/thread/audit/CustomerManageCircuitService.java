package com.telecom.nrm.thread.audit;


import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.controller.CustViewMemberController;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.CustViewDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerManageCircuitService extends AbstractCircuitAuditService {
    @Autowired
    CustViewDao custViewDao;
    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;


    public void createCircuit(String circuitCode, String ds, Long id) {
        try {
            JSONObject custView = custViewDao.getById(id, NRMConstants.SHARDING_GRAPH_DB);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("codes", new String[]{circuitCode});
            PageResponse<JSONObject> pageResponse = custLinkInterfaceDao.getCustLink(jsonObject, 10, 1,
                    NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
            if (null != pageResponse && CollectionUtils.isNotEmpty(pageResponse.getData())) {
                List<JSONObject> data = pageResponse.getData();
                if (null == custView) {
                    throw new RuntimeException("noData");
                } else {
                    custView.put("scene_id", id);
                    custView.put("dataSource", data);
                    Set<String> keys = data.stream().map(e -> e.getString("id")).collect(Collectors.toSet());
                    custView.put("selectedRowKeys", keys);
                    CustViewMemberController custViewMemberController = BeanUtils.getBean(CustViewMemberController.class);
                    if (null != custViewMemberController) {
                        custViewMemberController.saveList(custView, null);
                    } else {
                        log.error("", "获取bean失败");
                    }

                }
            } else {
                throw new RuntimeException("noData");
            }
        } catch (Exception e) {
            log.error("",e);
            throw e;
        }

    }


    @Override
    public List<AuditExcelDTO> call() throws Exception {
        return null;
    }
}
