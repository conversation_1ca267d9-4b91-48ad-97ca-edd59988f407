package com.telecom.nrm.thread.audit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.nrm.constant.Constant;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.impl.CircuitAnalysisServiceImpl;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InternetSpecialtyLineService extends AbstractCircuitAuditService {


    @Autowired
    GraphApiService graphApiService;

    @Autowired
    CircuitAnalysisDao circuitAnalysisDao;

    @Autowired
    CircuitAnalysisServiceImpl circuitAnalysisService;

    @Autowired
    Otn otn;


    public boolean saveData(String regionName, String accessCode) {
        boolean flag = true;
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByRegionName(regionName);
        if (shardingEnum == null) {
            throw new RuntimeException("请输入汉字类型的地市");
        }
        try {
            GraphRequest graphRequest = new GraphRequest();
            graphRequest.getBusinessParam().setApiId(Constant.GraphApiList.QUERY_PON_TOPO);
            graphRequest.getBusinessParam().setShardingCode(shardingEnum.getBcShardingCode());
            GraphWhereBodyItem graphWhereBodyItem =
                    new GraphWhereBodyItem("cfs", "access_code", accessCode);
            List<GraphWhereBodyItem> whereBody = new ArrayList<>();
            whereBody.add(graphWhereBodyItem);
            graphRequest.setWhereBody(whereBody);
//            Graph graph = graphApiService.doApi(graphRequest);
            // 临时解决本地无法连接图库问题
            ObjectMapper objectMapper = new ObjectMapper();
            String res = HttpRequestsUtils.sendPost("http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/graph-api", null, JSONObject.parseObject(objectMapper.writeValueAsString(graphRequest)));
            Graph graph = JSONObject.parseObject(res).getObject("data",Graph.class);

            Node devices = graph.getNodes().stream().filter(e -> e.getId().equals("device")).collect(Collectors.toList()).get(0);
            Node link = graph.getNodes().stream().filter(e -> e.getId().equals("link")).collect(Collectors.toList()).get(0);
            Node cfs = graph.getNodes().stream().filter(e -> e.getId().equals("cfs")).collect(Collectors.toList()).get(0);
            Node route = graph.getNodes().stream().filter(e -> e.getId().equals("opt_route")).collect(Collectors.toList()).get(0);
            List<JSONObject> upperDevice = null;
            //Set<BigDecimal> deviceIds = null;
            if (null != devices && CollectionUtils.isNotEmpty(devices.getData())) {
                upperDevice = devices.getData().stream()
                        .filter(e -> "1028200001".equals(e.getString("spec_id"))).collect(Collectors.toList());
            //    deviceIds = devices.getData().stream().map(e -> e.getBigDecimal("id")).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(upperDevice)) {
                    upperDevice = devices.getData().stream()
                            .filter(e -> "1024600002".equals(e.getString("spec_id"))).collect(Collectors.toList());
                }


            }
            JSONArray oltFindUpperLink; // 实际为上层城域网信息，包括：PON接入，光纤接入
            JSONArray saveLink = new JSONArray();
            JSONObject apiQuery = new JSONObject();

            if (CollectionUtils.isNotEmpty(upperDevice)) {
                BigDecimal oltId = upperDevice.get(0).getBigDecimal("id");
                apiQuery.put("id", oltId);
                JSONObject upperLink = circuitAnalysisDao.queryUpperlinkByOlt(apiQuery, shardingEnum.getBcShardingCode());
                if (null != upperLink && CollectionUtils.isNotEmpty(upperLink.getJSONArray("links"))) {
                    oltFindUpperLink = upperLink.getJSONArray("links");
                    saveLink.addAll(oltFindUpperLink);
                }
            }

            if (null != link && CollectionUtils.isNotEmpty(link.getData())) {
                saveLink.addAll(link.getData());
            }
            if (null != route && CollectionUtils.isNotEmpty(route.getData())) {
                saveLink.addAll(route.getData());
            }
            //以前流程
            circuitAnalysisService.saveByLinks(shardingEnum, saveLink,accessCode);
            if (cfs != null && CollectionUtils.isNotEmpty(cfs.getData())){
                Set<BigDecimal>  cfsIds =cfs.getData().stream().map(e->e.getBigDecimal("id")).collect(Collectors.toSet());
                circuitAnalysisService.saveRmServiceAndRelation(cfsIds, shardingEnum);
            }
            // 兼容中博数据接口，构建上层链路
            saveCreateLinkRelation(graph,shardingEnum,accessCode);

        } catch (Exception e) {
            flag = false;
            e.printStackTrace();
            circuitAnalysisService.saveException(accessCode, e.getMessage(), "互联网专线", regionName, "失败");
            log.error("", e);
            throw  new RuntimeException("失败"+e);
        }
        return flag;
    }

    public void saveCreateLinkRelation(Graph graph, ShardingEnum shardingEnum,String accessCode) {
        Node access_link =
                graph.getNodes().stream().filter(e -> e.getId().equals("access_link")).collect(Collectors.toList()).get(0);
        Node pon_link =
                graph.getNodes().stream().filter(e -> e.getId().equals("pon_link")).collect(Collectors.toList()).get(0);
        Node devices =
                graph.getNodes().stream().filter(e -> e.getId().equals("device")).collect(Collectors.toList()).get(0);
        boolean oltExist = devices.getData().stream().anyMatch(e -> "1028200001".equals(e.getString("spec_id")));
        boolean switchExist = devices.getData().stream().anyMatch(e -> "1024600002".equals(e.getString("spec_id")));
        if (!oltExist && !switchExist) return;
        BigDecimal oltOrSwitchId = oltExist ? devices.getData().stream()
                .filter(e -> "1028200001".equals(e.getString("spec_id"))).collect(Collectors.toList()).get(0)
                .getBigDecimal("id") :
                devices.getData().stream().filter(e -> "1024600002".equals(e.getString("spec_id")))
                        .collect(Collectors.toList()).get(0).getBigDecimal("id");
        List<JSONObject> upperLinkAndPonOrAccessLink =new ArrayList<>();
        if (null !=oltOrSwitchId){
            JSONObject apiQuery = new JSONObject();
            apiQuery.put("id", oltOrSwitchId);
            JSONObject upperLink = circuitAnalysisDao.queryUpperlinkByOlt(apiQuery, shardingEnum.getBcShardingCode());
            if (null != upperLink && CollectionUtils.isNotEmpty(upperLink.getJSONArray("links"))) {
               JSONArray oltFindUpperLink = upperLink.getJSONArray("links");
                upperLinkAndPonOrAccessLink.addAll(JSONObjectUtil.jsonObjectArrayToList(oltFindUpperLink));
            }
        }
        if (oltExist){
            upperLinkAndPonOrAccessLink.addAll(pon_link.getData());
        }else if (null != access_link && CollectionUtils.isNotEmpty(access_link.getData())){
            upperLinkAndPonOrAccessLink.addAll(access_link.getData());
        }
        if (CollectionUtils.isNotEmpty(upperLinkAndPonOrAccessLink)){
            String upperId = String.valueOf(otn.getId());
            String accessTypeCode = "光纤接入";// 记录接入方式
            if (oltExist) {
                accessTypeCode = "PON接入";
            } else {
                return; // 光纤接入不添加 展示
            }
            JSONObject createLink= otn.createLinkObj(upperId,accessTypeCode,accessCode,1132100021L,shardingEnum.getO3(),
                    null,null,null,null,null);
            List<JSONObject> resLinkRelas =new ArrayList<>();
            for (JSONObject jsonObject : upperLinkAndPonOrAccessLink) {
                JSONObject linkRela = new JSONObject();
                linkRela.put("id", otn.getId());
                linkRela.put("code", null);
                linkRela.put("spec_id",1130311310000L);
                linkRela.put("upper_link_id", upperId);
                linkRela.put("upper_link_spec_id", 1132100021L);
                linkRela.put("lower_link_id",new BigDecimal(jsonObject.getString("id")));
                linkRela.put("lower_link_spec_id",new BigDecimal(jsonObject.getString("spec_id")));
                linkRela.put("circuit_code", accessCode);
                resLinkRelas.add(linkRela);
            }
            JSONObject inputJson = new JSONObject();
            JSONArray liknks = new JSONArray();
            liknks.add(createLink);
            JSONArray linkRelas = new JSONArray();
            linkRelas.addAll(resLinkRelas);
            inputJson.put("links", liknks);
            inputJson.put("linkRelas", linkRelas);
            circuitAnalysisDao.saveNetLinks(inputJson, NRMConstants.SHARDING_CODE);
        }

    }

    @Override
    public List<AuditExcelDTO> call() throws Exception {
        return null;
    }
}
