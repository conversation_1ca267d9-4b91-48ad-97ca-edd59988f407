package com.telecom.nrm.thread.audit;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.CircuitAnalysisService;
import com.telecom.nrm.service.OTNService;
import com.telecom.nrm.utils.MyExceptionUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class Otn  {
    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyyMMddHHmmss");

    private static final String ID_PRI_FIX = "3299";

    @Autowired
    private OTNService otnService;

    @Autowired
    private CircuitAnalysisDao circuitAnalysisDao;

    @Autowired
    private CircuitAnalysisService circuitAnalysisService;



    public List<String> getCitysFromCircuit(String circuitCode) {
        List<String> areaIds = new ArrayList<>();

        Set<String> set = new HashSet<>();
        for (String value : NRMConstants.AREA_NAME_MAP.values()) {
            if (circuitCode.contains(value)) {
                if (set.add(NRMConstants.AREA_NAME_TO_ID_MAP.get(value))) {
                    areaIds.add(NRMConstants.AREA_NAME_TO_ID_MAP.get(value));
                }
            }
        }

        return areaIds;
    }

    @Async("graphRestPool")
    public void saveOtnInfo(String circuitCode) {
        try {
            //查询OTN路由信息
            JSONObject request = new JSONObject();
            request.put("code", circuitCode);
            request.put("areaCode", "nj"); // OTN不重要
            JSONObject result = otnService.queryOTNRoute(request);

            if (ObjectUtils.isEmpty(result)) {
                throw new RuntimeException("OTN路由信息为空");
            }

            JSONArray links = result.get("links") == null ? null : result.getJSONArray("links");
            if (ObjectUtils.isEmpty(links)) {
                throw new RuntimeException("OTN路由信息links节点为空");
            }

            JSONArray nodes = result.get("nodes") == null ? null : result.getJSONArray("nodes");
            if (ObjectUtils.isEmpty(nodes)) {
                throw new RuntimeException("OTN路由信息nodes节点为空");
            }

            //判断电路名称内有几个地市（省内）
            List<String> areaIds = getCitysFromCircuit(circuitCode);

            if (CollectionUtils.isEmpty(areaIds)) {
                throw new RuntimeException("电路编号没有地市信息");
            }

            //查询波道配置表
            String frequency = links.getJSONObject(0).getString("frequency");

            String bdNo = "空";
            if (StringUtils.isNotEmpty(frequency)) {
                JSONObject bdParam = new JSONObject();
                bdParam.put("frequency", frequency);
                PageResponse<JSONObject> bdConfig = circuitAnalysisDao.queryBdConfigByParam(bdParam, 10, 0,
                        NRMConstants.SHARDING_CODE);
                List<JSONObject> bdConfigList = bdConfig.getData();
                if (CollectionUtils.isNotEmpty(bdConfigList)) {
                    bdNo = bdConfigList.get(0).getString("code");
                }
            }

            String startNeId = null;
            String startPtpId = null;
            String endNeId = null;
            String endtpId = null;
            List<String> borderDevices = new ArrayList<>(); // 边界资源
//            for (int i = 0; i < nodes.size(); i++) {
//                if ("1000".equals(nodes.getJSONObject(i).getString("res_domain_id"))) {
//                    borderDevices.add(nodes.getJSONObject(i).getString("id"));
//                }
//            }
            borderDevices.add("A37562CAC032C4DCCEC144057BEDB8B9");



            if (areaIds.size() == 1) {
                //起始设备 判断包含CPE
                startNeId = nodes.getJSONObject(0).getString("id");
                if (nodes.getJSONObject(0).getString("code") != null && nodes.getJSONObject(0).getString("code").contains("CPE")) {
                    startNeId = nodes.getJSONObject(0).getString("id");
                } else {
                    startNeId = nodes.getJSONObject(nodes.size() - 1).getString("id");
                }

//                //上长途设备
//                for (int i = 0; i < nodes.size(); i++) {
//                    JSONObject node = nodes.getJSONObject(i);
//                    if ("1000".equals(node.getString("res_domain_id"))) {
//                        endNeId = node.getString("id");
//                        break;
//                    }
//                }
//                if (StringUtils.isEmpty(endNeId)) {
//                    throw new RuntimeException("未找到上长途设备");
//                }


                JSONArray otnRoutes = new JSONArray();
                JSONArray odu4s = links.getJSONObject(0).getJSONArray("children");
                if (odu4s.getJSONObject(0).getString("spec_name").equals("SDH低阶路径")) {
                    for (int i = odu4s.size() - 1; i >= 0; i--) {
                        if (i !=0) {
                            odu4s.remove(i);
                        }
                    }
                }
                odu4s = odu4s.getJSONObject(0).getJSONArray("children");
                for (int i = 0; i < odu4s.size(); i++) {
                    if (odu4s.getJSONObject(i).getString("spec_name").indexOf("SDH")>=0) {
                        odu4s.set(i,odu4s.getJSONObject(i).getJSONArray("children").getJSONObject(0));
                    }
                }
                for (int i = 0; i < odu4s.size(); i++) {
                    if (odu4s.getJSONObject(i).getString("spec_name").indexOf("MS")>=0 && odu4s.getJSONObject(i).getJSONArray("children")!=null && odu4s.getJSONObject(i).getJSONArray("children").size() > 0) {
                        odu4s.set(i,odu4s.getJSONObject(i).getJSONArray("children").getJSONObject(0));
                    }
                }

                boolean rs =false;
                for (int i = 0; i < odu4s.size(); i++) {
                    if (odu4s.getJSONObject(i).getString("spec_name").indexOf("RS")>=0) {
                        if (odu4s.getJSONObject(i).getJSONArray("children")!=null && odu4s.getJSONObject(i).getJSONArray("children").size() > 0) {
                            odu4s.set(i,odu4s.getJSONObject(i).getJSONArray("children").getJSONObject(0));
                        }
                        rs = true;
                    }
                }

                if (rs) {
                    JSONArray odu4sTemp = new JSONArray();
                    // 继续替换
                    for (int i = 0; i < odu4s.size(); i++) {
                        if (odu4s.getJSONObject(i).getJSONArray("children") != null && odu4s.getJSONObject(i).getJSONArray("children").size() > 0) {
                            odu4sTemp.addAll(odu4s.getJSONObject(i).getJSONArray("children"));
                        } else {
                            odu4sTemp.add(odu4s.get(i));
                        }

                    }
                    odu4s = odu4sTemp;
                }


                if (odu4s.getJSONObject(0).getString("a_ne_id")!=null && ( odu4s.getJSONObject(0).getString("a_ne_id").equals(startNeId)
                        || odu4s.getJSONObject(0).getString("z_ne_id").equals(startNeId)))  {

                    for (int i = 0; i < odu4s.size(); i++) {
                        if (odu4s.getJSONObject(i).getString("spec_name").equals("断点")) {
                            continue;
                        }
                        if (borderDevices.contains(odu4s.getJSONObject(i).getString("a_ne_id"))) {
                            break;
                        }
                        if (borderDevices.contains(odu4s.getJSONObject(i).getString("z_ne_id"))) {
                            break;
                        }
                        otnRoutes.add(odu4s.get(i));
                    }
                } else {
                    for (int i = odu4s.size() - 1; i >= 0; i--) {
                        if (odu4s.getJSONObject(i).getString("spec_name").equals("断点")) {
                            continue;
                        }
                        if (borderDevices.contains(odu4s.getJSONObject(i).getString("a_ne_id"))) {
                            break;
                        }
                        if (borderDevices.contains(odu4s.getJSONObject(i).getString("z_ne_id"))) {
                            break;
                        }
                        otnRoutes.add(odu4s.get(i));
                    }
                }
                // 分析终止设备
                endNeId = otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("a_ne_id");
                // 只有一段
                if (startNeId.equalsIgnoreCase(endNeId)) {
                    endNeId = otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("z_ne_id");
                }
                // 多段 如果倒数第二段出现过，那就是对端
                if ( otnRoutes.getJSONObject(otnRoutes.size() - 2).getString("a_ne_id").equals(endNeId) ||
                        otnRoutes.getJSONObject(otnRoutes.size() - 2).getString("z_ne_id").equals(endNeId)) {
                    endNeId = otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("z_ne_id");
                }
                saveOtn(otnRoutes, startNeId,endNeId,circuitCode, bdNo,areaIds.get(0));



            } else if (areaIds.size() == 2) {
                //正向
                //起始设备
                startNeId = nodes.getJSONObject(0).getString("id");

                //上长途设备
                for (int i = 0; i < nodes.size(); i++) {
                    JSONObject node = nodes.getJSONObject(i);
                    if ("1000".equals(node.getString("res_domain_id"))) {
                        endNeId = node.getString("id");
                        break;
                    }
                }

                if (StringUtils.isEmpty(endNeId)) {
                    throw new RuntimeException("正向-未找到上长途设备");
                }

                JSONArray otnRoutes = new JSONArray();
                JSONArray odu4s = links.getJSONObject(0).getJSONArray("children");
                if (odu4s.getJSONObject(0).getString("a_ne_id").equals(startNeId)
                        || odu4s.getJSONObject(0).getString("z_ne_id").equals(startNeId))  {

                    for (int i = 0; i < odu4s.size(); i++) {
                        if (odu4s.getJSONObject(i).getString("a_ne_id").equals(endNeId) ||
                                odu4s.getJSONObject(i).getString("z_ne_id").equals(endNeId)) {
                            otnRoutes.add(odu4s.get(i));
                            break;
                        } else {
                            otnRoutes.add(odu4s.get(i));
                        }
                    }
                } else {
                    for (int i = odu4s.size() - 1; i >= 0; i--) {
                        if (odu4s.getJSONObject(i).getString("a_ne_id").equals(endNeId) ||
                                odu4s.getJSONObject(i).getString("z_ne_id").equals(endNeId)) {
                            otnRoutes.add(odu4s.get(i));
                            break;
                        } else {
                            otnRoutes.add(odu4s.get(i));
                        }
                    }
                }

                saveOtn(otnRoutes, startNeId,endNeId,circuitCode, bdNo,areaIds.get(0));

                //反向
                //起始设备
                Collections.reverse(nodes);//nodes 倒序排列
                startNeId = nodes.getJSONObject(0).getString("id");

                //上长途设备
                endNeId = null;
                for (int i = 0; i < nodes.size(); i++) {
                    JSONObject node = nodes.getJSONObject(i);
                    if ("1000".equals(node.getString("res_domain_id"))) {
                        endNeId = node.getString("id");
                        break;
                    }
                }

                if (StringUtils.isEmpty(endNeId)) {
                    throw new RuntimeException("反向-未找到上长途设备");
                }

                JSONArray otnRoutes2 = new JSONArray();
                if (odu4s.getJSONObject(0).getString("a_ne_id").equals(startNeId)
                        || odu4s.getJSONObject(0).getString("z_ne_id").equals(startNeId))  {

                    for (int i = 0; i < odu4s.size(); i++) {
                        if (odu4s.getJSONObject(i).getString("a_ne_id").equals(endNeId) ||
                                odu4s.getJSONObject(i).getString("z_ne_id").equals(endNeId)) {
                            otnRoutes2.add(odu4s.get(i));
                            break;
                        } else {
                            otnRoutes2.add(odu4s.get(i));
                        }
                    }
                } else {
                    for (int i = odu4s.size() - 1; i >= 0; i--) {
                        if (odu4s.getJSONObject(i).getString("a_ne_id").equals(endNeId) ||
                                odu4s.getJSONObject(i).getString("z_ne_id").equals(endNeId)) {
                            otnRoutes2.add(odu4s.get(i));
                            break;
                        } else {
                            otnRoutes2.add(odu4s.get(i));
                        }
                    }
                }

                saveOtn(otnRoutes2, startNeId,endNeId,circuitCode, bdNo,areaIds.get(1));


            }

            circuitAnalysisService.saveException(circuitCode, "OTN", null, "成功", null);
        } catch (Exception e) {
            e.printStackTrace();
            //记录日志
            circuitAnalysisService.saveException(circuitCode, "OTN", null, "异常", MyExceptionUtil.getExceptionMsg(e));
        }
    }

    private void saveOtn(JSONArray otnRoutes , String startNeId ,String endNeId ,String circuitCode, String bdNo, String areaId) throws Exception {
        JSONObject oduObj = new JSONObject();
        String startPtpId = null;
        String endtpId = null;
        oduObj.put("children",otnRoutes);
        oduObj.put("a_ne_id", startNeId);
        oduObj.put("name", circuitCode);
        oduObj.put("code", circuitCode);
        oduObj.put("z_ne_id", endNeId);
        if (otnRoutes.getJSONObject(0).getString("a_ne_id").equals(startNeId)
                || otnRoutes.getJSONObject(0).getString("z_ne_id").equals(startNeId))  {
            if (otnRoutes.getJSONObject(0).getString("a_ne_id").equals(startNeId)) {
                startPtpId = otnRoutes.getJSONObject(0).getString("a_ptp_id");
            }
            if (otnRoutes.getJSONObject(0).getString("z_ne_id").equals(startNeId)) {
                startPtpId = otnRoutes.getJSONObject(0).getString("z_ptp_id");
            }

            if (otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("a_ne_id").equals(endNeId)) {
                endtpId = otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("a_ptp_id");
            }
            if (otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("z_ne_id").equals(endNeId)) {
                endtpId = otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("z_ptp_id");
            }

        } else {
            if (otnRoutes.getJSONObject(0).getString("a_ne_id").equals(endNeId)) {
                endtpId = otnRoutes.getJSONObject(0).getString("a_ptp_id");
            }
            if (otnRoutes.getJSONObject(0).getString("z_ne_id").equals(endNeId)) {
                endtpId = otnRoutes.getJSONObject(0).getString("z_ptp_id");
            }

            if (otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("a_ne_id").equals(startNeId)) {
                startPtpId = otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("a_ptp_id");
            }
            if (otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("z_ne_id").equals(startNeId)) {
                startPtpId = otnRoutes.getJSONObject(otnRoutes.size() - 1).getString("z_ptp_id");
            }
        }

        oduObj.put("a_ptp_id", startPtpId);
        oduObj.put("z_ptp_id", endtpId);

        oduObj.put("children",otnRoutes);
        List<JSONObject> cmLinkList = new ArrayList<>();
        List<JSONObject> crLinkLinkList = new ArrayList<>();

        // 添加通道成
        String oduId = this.getId(100);
        JSONObject odu = this.createLinkObj(oduId, oduObj.getString("name"), oduObj.getString("code"), 1132300005L, areaId,
                circuitCode, oduObj.getString("a_ne_id"), oduObj.getString("a_ptp_id"), oduObj.getString("z_ne_id"),
                oduObj.getString("z_ptp_id"));
        cmLinkList.add(odu);


        Map<String,String> portMapCache = new HashMap<>();

        for (int i = 0; i < otnRoutes.size(); i++) {
            JSONObject oduDataByOduLink = getOduDataByOduLink(otnRoutes.getJSONObject(i), circuitCode, bdNo, areaId, portMapCache);
            // 通道和ODU4关系
            String oduToOtuId = this.getId(101+i);
            JSONObject oduToOtu = this.createLinkLinkObj(oduToOtuId, 1130311310000L, oduId, oduDataByOduLink.getJSONArray("cmLinkList").getJSONObject(0).getString("id"), areaId, circuitCode,
                    1132300005L, 1132300005L);
            crLinkLinkList.add(oduToOtu);

            cmLinkList.addAll(JSONObjectUtil.jsonObjectArrayToList(oduDataByOduLink.getJSONArray("cmLinkList")));
            crLinkLinkList.addAll(JSONObjectUtil.jsonObjectArrayToList(oduDataByOduLink.getJSONArray("crLinkLinkList")));
        }



        this.queryDeviceAndPortFromBc(cmLinkList,areaId,portMapCache);

        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(areaId);
        JSONObject queryJson = new JSONObject();
        // 对链路重新查询依次，主要是初始化光路信息 其中有光链路路由
        List<BigDecimal> linkIds = new ArrayList<>();
        for (int i = 0; i < crLinkLinkList.size(); i++) {
            linkIds.add(new BigDecimal(crLinkLinkList.get(i).getString("lower_link_id")));
        }
        queryJson.put("ids", linkIds);
        JSONObject resLInks = circuitAnalysisDao.queryResOptRoadByRoutes(queryJson, shardingEnum.getBcShardingCode());
        cmLinkList.addAll(JSONObjectUtil.jsonObjectArrayToList(resLInks.getJSONArray("links")));
        crLinkLinkList.addAll(JSONObjectUtil.jsonObjectArrayToList(resLInks.getJSONArray("linkRelas")));

        JSONObject param = new JSONObject();
        param.put("links", cmLinkList);
        param.put("linkRelas", crLinkLinkList);
        param.put("circuit_code", circuitCode);
        System.out.println(cmLinkList);

        List<BigDecimal> portIds = (List<BigDecimal>) cmLinkList.stream()
                .flatMap(e -> Stream.of(
                        ((JSONObject) e).getBigDecimal("z_port_id"),
                        ((JSONObject) e).getBigDecimal("a_port_id")))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<BigDecimal> deviceIds = (List<BigDecimal>) cmLinkList.stream()
                .flatMap(e -> Stream.of(
                        ((JSONObject) e).getBigDecimal("z_physic_device_id"),
                        ((JSONObject) e).getBigDecimal("a_physic_device_id")))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        JSONObject inputJson = new JSONObject();
        queryJson.put("ids", portIds);

        // 保存设备端口
        JSONObject ports = circuitAnalysisDao.queryResPortsByIds(queryJson, shardingEnum.getBcShardingCode());
        queryJson.put("ids", deviceIds);
        JSONObject devices = circuitAnalysisDao.queryResDeviceDetailPortsByDevice(queryJson, shardingEnum.getBcShardingCode());
        inputJson.put("ports", ports.getJSONArray("ports"));
        inputJson.put("devices", devices.getJSONArray("devices"));
        inputJson.put("wares", devices.getJSONArray("wares"));
        inputJson.put("deviceWareRelas", devices.getJSONArray("deviceWareRelas"));
        circuitAnalysisDao.saveNetDevices(inputJson, NRMConstants.SHARDING_CODE);

        queryJson.put("ids", linkIds);
        JSONObject jxgx = circuitAnalysisDao.queryResMicroRouteByJxgx(queryJson, shardingEnum.getBcShardingCode());
        inputJson.put("nets", jxgx.getJSONArray("nets"));
        inputJson.put("netEntitys", jxgx.getJSONArray("netEntitys"));
        inputJson.put("linkCables", jxgx.getJSONArray("linkCables"));
        inputJson.put("cables", jxgx.getJSONArray("cables"));
        inputJson.put("cableRelas", jxgx.getJSONArray("cableRelas"));
        inputJson.put("cableNodes", jxgx.getJSONArray("cableNodes"));
        circuitAnalysisDao.saveNetCables(inputJson,NRMConstants.SHARDING_CODE);


        circuitAnalysisDao.saveNetLinks(param,NRMConstants.SHARDING_CODE);
    }

    private JSONObject getOduDataByOduLink(JSONObject oduObj, String circuitCode, String bdNo, String areaId,Map<String,String> portMapCache) throws Exception {
        // 太快了 导致不同循环进来 id一致
        Thread.sleep(1000);
        Integer linkCount = 0;
        Integer linkLinkCount = 0;
        List<JSONObject> cmLinkList = new ArrayList<>();
        List<JSONObject> crLinkLinkList = new ArrayList<>();
        ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByO3(areaId);

        //ODU4
        String oduId = this.getId(linkCount);
        JSONObject odu = this.createLinkObj(oduId, oduObj.getString("name"), oduObj.getString("code"), 1132300005L, areaId,
                circuitCode, oduObj.getString("a_ne_id"), oduObj.getString("a_ptp_id"), oduObj.getString("z_ne_id"),
                oduObj.getString("z_ptp_id"));
        cmLinkList.add(odu);
        linkCount ++;

        JSONArray oduChilds = oduObj.get("children") == null ? null : oduObj.getJSONArray("children");
        if (CollectionUtils.isEmpty(oduChilds)) {
            //throw new RuntimeException("ODU链路：" + oduObj.getString("name") + "，无下级链路");
        }

        // ODU
        for (int i = 0; i < oduChilds.size(); i++) {
            JSONObject otuObj = oduChilds.getJSONObject(i);

            // 集团规范PPT 不需要OTU
            //OTU
//            String otuId = this.getId(linkCount);
//            JSONObject otu = this.createLinkObj(otuId, otuObj.getString("name"), otuObj.getString("code"), 1132300021L, areaId,
//                    circuitCode, otuObj.getString("a_ne_id"), otuObj.getString("a_ptp_id"), otuObj.getString("z_ne_id"),
//                    otuObj.getString("z_ptp_id"));
//            cmLinkList.add(otu);
//            linkCount ++;
//
//            //ODU与OTU关系
//            String oduToOtuId = this.getId(linkLinkCount);
//            JSONObject oduToOtu = this.createLinkLinkObj(oduToOtuId, 1130311310000L, oduId, otuId, areaId, circuitCode,
//                    1132300005L, 1132300021L);
//            crLinkLinkList.add(oduToOtu);
//            linkLinkCount ++;

            JSONArray otuChilds = otuObj.get("children") == null ? null : otuObj.getJSONArray("children");
//            if (CollectionUtils.isEmpty(otuChilds)) {
//                throw new RuntimeException("OTU链路：" + otuObj.getString("name") + "，无下级链路");
//            }

            for (int j = 0; j < otuChilds.size(); j++) {
                JSONObject ochObj = otuChilds.getJSONObject(j);

                //OCH
                String ochId = this.getId(linkCount);
                JSONObject och = this.createLinkObj(ochId, ochObj.getString("name"), ochObj.getString("code"), 1132200002L, areaId,
                        circuitCode, ochObj.getString("a_ne_id"), ochObj.getString("a_ptp_id"), ochObj.getString("z_ne_id"),
                        ochObj.getString("z_ptp_id"));
                cmLinkList.add(och);
                linkCount ++;

                //ODU与OCH关系
                String otuToOchId = this.getId(linkLinkCount);
                JSONObject otuToOch = this.createLinkLinkObj(otuToOchId, 1130311310000L, oduId, ochId, areaId, circuitCode,
                        1132300005L, 1132300002L);
                crLinkLinkList.add(otuToOch);
                linkLinkCount ++;

                JSONArray ochChilds = ochObj.get("children") == null ? null : ochObj.getJSONArray("children");
//                if (CollectionUtils.isEmpty(ochChilds)) {
//                    throw new RuntimeException("OCH链路：" + ochObj.getString("name") + "，无下级链路");
//                }

                List<String> optroadList = new ArrayList<>();
                for (int k = 0; k < ochChilds.size(); k++) {
                    JSONObject omsOrJlObj = ochChilds.getJSONObject(k);//OMS 或者 硬跳

                    // 断点
                    if ("断点".equals(omsOrJlObj.getString("spec_name"))) {
                        //硬跳
                        String jlId = this.getId(linkCount);
                        JSONObject jl = this.createLinkObj(jlId, "硬跳", "硬跳",
                                1131100001L, areaId, circuitCode, omsOrJlObj.getString("a_physic_device_id"),
                                omsOrJlObj.getString("a_ptp_id"), omsOrJlObj.getString("z_physic_device_id"), omsOrJlObj.getString("z_ptp_id"));
                        cmLinkList.add(jl);
                        linkCount ++;


                        String relaId = this.getId(linkLinkCount);
                        JSONObject ochToJl = this.createLinkLinkObj(relaId, 1130311310000L, ochId, jlId, areaId, circuitCode,
                                1132300002L, 1131100001L);
                        crLinkLinkList.add(ochToJl);
                        linkLinkCount ++;

                    }

                    if ("拓扑连接".equals(omsOrJlObj.getString("spec_name"))) {
                        JSONObject param = new JSONObject();
                        param.put("code", omsOrJlObj.getString("name"));
                        JSONObject routeResult = this.circuitAnalysisDao.queryResRouteByOptRoad(param, shardingEnum.getBcShardingCode());
                        if (routeResult.containsKey("routes") &&  routeResult.getJSONArray("routes").size() > 0) {
                            if (optroadList.contains(omsOrJlObj.getString("name"))) {
                                continue;
                            }
                            optroadList.add(omsOrJlObj.getString("name"));

                            JSONArray routes = routeResult.getJSONArray("routes");
                            for ( Object route : routes) {
                                // 和光链路路由关系
                                String relaId = this.getId(linkLinkCount);
                                JSONObject otsToOpath = this.createLinkLinkObj(relaId, 1130311310000L, ochId, ((JSONObject)route).getString("id"), areaId,
                                        circuitCode, 1132100019L, 1131200002L);
                                crLinkLinkList.add(otsToOpath);
                                linkLinkCount ++;

                                // 分析出光路下AZ端设备端口关系，用于辅助端口匹配
                                String aDeviceId = ((JSONObject)route).getString("a_physic_device_id");
                                String zDeviceId = ((JSONObject)route).getString("z_physic_device_id");
                                String aPortId = ((JSONObject)route).getString("a_port_id");
                                String zPortId = ((JSONObject)route).getString("z_port_id");
                                portMapCache.put(aDeviceId + "-" + zDeviceId, aPortId+"-"+zPortId);
                                portMapCache.put(zDeviceId + "-" + aDeviceId, zPortId+"-"+aPortId);

                            }

                        } else {
                            //硬跳
                            String jlId = this.getId(linkCount);
                            JSONObject jl = this.createLinkObj(jlId,  omsOrJlObj.getString("name"), omsOrJlObj.getString("code"),
                                    1131100001L, areaId, circuitCode, omsOrJlObj.getString("a_ne_id"),
                                    omsOrJlObj.getString("a_ptp_id"), omsOrJlObj.getString("z_ne_id"), omsOrJlObj.getString("z_ptp_id"));
                            cmLinkList.add(jl);
                            linkCount ++;

                            //OCH与硬跳关系
                            String ochToJlId = this.getId(linkLinkCount);
                            JSONObject ochToJl = this.createLinkLinkObj(ochToJlId, 1130311310000L, ochId, jlId, areaId, circuitCode,
                                    1132300002L, 1131100001L);
                            crLinkLinkList.add(ochToJl);
                            linkLinkCount ++;
                        }

                    } else if ("OMS".equals(omsOrJlObj.getString("spec_name"))) {
                        //波道
                        String bdId = this.getId(linkCount);
                        JSONObject bd = this.createLinkObj(bdId, bdNo, bdNo, 1132300002L, areaId, circuitCode,
                                omsOrJlObj.getString("a_ne_id"), omsOrJlObj.getString("a_ptp_id"),
                                omsOrJlObj.getString("z_ne_id"), omsOrJlObj.getString("z_ptp_id"));
                        cmLinkList.add(bd);
                        linkCount ++;

                        //OCH与波道关系
                        String ochToBdId = this.getId(linkLinkCount);
                        JSONObject ochToBd = this.createLinkLinkObj(ochToBdId, 1130311310000L, ochId, bdId, areaId, circuitCode,
                                1132300002L, 1132300002L);
                        crLinkLinkList.add(ochToBd);
                        linkLinkCount ++;

                        //OMS
                        String omsId = this.getId(linkCount);
                        JSONObject oms = this.createLinkObj(omsId, omsOrJlObj.getString("name"), omsOrJlObj.getString("code"),
                                1132100010L, areaId, circuitCode, omsOrJlObj.getString("a_ne_id"),
                                omsOrJlObj.getString("a_ptp_id"), omsOrJlObj.getString("z_ne_id"), omsOrJlObj.getString("z_ptp_id"));
                        cmLinkList.add(oms);
                        linkCount ++;

                        //波道与OMS关系
                        String bdToOmsId = this.getId(linkLinkCount);
                        JSONObject bdToOms = this.createLinkLinkObj(bdToOmsId, 1130311310000L, bdId, omsId, areaId, circuitCode,
                                1132300002L, 1132100010L);
                        crLinkLinkList.add(bdToOms);
                        linkLinkCount ++;

                        JSONArray omsChilds = omsOrJlObj.get("children") == null ? null : omsOrJlObj.getJSONArray("children");
//                        if (CollectionUtils.isEmpty(omsChilds)) {
//                            throw new RuntimeException("OMS链路：" + omsOrJlObj.getString("name") + "，无下级链路");
//                        }

                        for (int l = 0; l < omsChilds.size(); l++) {
                            JSONObject otsObj = omsChilds.getJSONObject(l);//OTS

                            //OTS
                            String otsId = this.getId(linkCount);
                            JSONObject ots = this.createLinkObj(otsId, otsObj.getString("name"), otsObj.getString("code"),
                                    1132100019L, areaId, circuitCode, otsObj.getString("a_ne_id"),
                                    otsObj.getString("a_ptp_id"), otsObj.getString("z_ne_id"), otsObj.getString("z_ptp_id"));
                            cmLinkList.add(ots);
                            linkCount ++;

                            //OMS与OTS关系
                            String omsToOtsId = this.getId(linkLinkCount);
                            JSONObject omsToOts = this.createLinkLinkObj(omsToOtsId, 1130311310000L, omsId, otsId, areaId, circuitCode,
                                    1132100010L, 1132100019L);
                            crLinkLinkList.add(omsToOts);
                            linkLinkCount ++;

                            JSONArray otsChilds = otsObj.get("children") == null ? null : otsObj.getJSONArray("children");
//                            if (CollectionUtils.isEmpty(otsChilds)) {
//                                throw new RuntimeException("OTS链路：" + otsObj.getString("name") + "，无下级链路");
//                            }

                            for (int m = 0; m < otsChilds.size(); m++) {
                                JSONObject opathObj = otsChilds.getJSONObject(m);//光纤光路
                                JSONObject param = new JSONObject();
                                param.put("code", opathObj.getString("name"));
                                JSONObject routeResult = this.circuitAnalysisDao.queryResRouteByOptRoad(param, shardingEnum.getBcShardingCode());
                                if (routeResult.containsKey("routes") &&  routeResult.getJSONArray("routes").size() > 0) {
                                    if (optroadList.contains(omsOrJlObj.getString("name"))) {
                                        continue;
                                    }
                                    optroadList.add(omsOrJlObj.getString("name"));

                                    JSONArray routes = routeResult.getJSONArray("routes");
                                    for ( Object route : routes) {
                                       // 和光链路路由关系
                                        String relaId = this.getId(linkLinkCount);
                                        JSONObject otsToOpath = this.createLinkLinkObj(relaId, 1130311310000L, otsId, ((JSONObject)route).getString("id"), areaId,
                                                circuitCode, 1132100019L, 1131200002L);
                                        crLinkLinkList.add(otsToOpath);
                                        linkLinkCount ++;

                                        // 分析出光路下AZ端设备端口关系，用于辅助端口匹配
                                        String aDeviceId = ((JSONObject)route).getString("a_physic_device_id");
                                        String zDeviceId = ((JSONObject)route).getString("z_physic_device_id");
                                        String aPortId = ((JSONObject)route).getString("a_port_id");
                                        String zPortId = ((JSONObject)route).getString("z_port_id");
                                        portMapCache.put(aDeviceId + "-" + zDeviceId, aPortId+"-"+zPortId);
                                        portMapCache.put(zDeviceId + "-" + aDeviceId, zPortId+"-"+aPortId);

                                    }
                                } else {
                                    // 硬跳
                                    //OTS
                                    String hardLinkId = this.getId(linkCount);
                                    JSONObject hardLink = this.createLinkObj(hardLinkId, otsObj.getString("name"), otsObj.getString("code"),
                                            1131100001L, areaId, circuitCode, otsObj.getString("a_ne_id"),
                                            otsObj.getString("a_ptp_id"), otsObj.getString("z_ne_id"), otsObj.getString("z_ptp_id"));
                                    cmLinkList.add(hardLink);
                                    linkCount ++;

                                    //OMS与OTS关系
                                    String hardLinkRelaId = this.getId(linkLinkCount);
                                    JSONObject hardLinkRela = this.createLinkLinkObj(hardLinkRelaId, 1130311310000L, otsId, hardLinkId, areaId, circuitCode,
                                            1132100010L, 1131100001L);
                                    crLinkLinkList.add(hardLinkRela);
                                    linkLinkCount ++;

                                }


                            }
                        }
                    }
                }
            }
        }

        JSONObject result = new JSONObject();
        result.put("cmLinkList", cmLinkList);
        result.put("crLinkLinkList", crLinkLinkList);

        return result;
    }

    private void queryDeviceAndPortFromBc(List<JSONObject> cmLinkList, String areaId,Map<String,String> portMapCache) throws Exception {
        Set<String> deviceIds = new HashSet<>();
        Set<String> portIds = new HashSet<>();
        for (JSONObject cmLink : cmLinkList) {
            deviceIds.add(cmLink.getString("a_physic_device_id"));
            deviceIds.add(cmLink.getString("z_physic_device_id"));
            portIds.add(cmLink.getString("a_port_id"));
            portIds.add(cmLink.getString("z_port_id"));
        }

        //OTN设备
        JSONObject otnDevParam = new JSONObject();
        otnDevParam.put("deviceIds", deviceIds);
        PageResponse<JSONObject> otnDevInfo = circuitAnalysisDao.queryTransNeByParam(otnDevParam, deviceIds.size(), 0,
                NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> otnDevList = otnDevInfo.getData();

        Map<String, String> otnDevMap = new HashMap<>();
        Set<String> otnDevNames = new HashSet<>();
        for (JSONObject otnDev : otnDevList) {

            if (otnDev.getString("aliasname").equals("15437-南京马鞍山-R")) {
                otnDev.put("aliasname","15437-南京马鞍山-R-集团政企OTN");
            }
            if (otnDev.getString("aliasname").equals("15359-南京云南路-R")) {
                otnDev.put("aliasname","15359-南京云南路-R-集团政企OTN");
            }
            if (otnDev.getString("aliasname").equals("15359-南京云南路-R")) {
                otnDev.put("aliasname","15359-南京云南路-R-集团政企OTN");
            }
            otnDevMap.put(otnDev.getString("neId"), otnDev.getString("aliasname"));

            otnDevNames.add(otnDev.getString("aliasname"));
        }

        //OTN端口
        JSONObject otnPortParam = new JSONObject();
        otnPortParam.put("portIds", portIds);
        PageResponse<JSONObject> otnPortInfo = circuitAnalysisDao.queryTransPortPtpByParam(otnPortParam, portIds.size(), 0,
                NRMConstants.SHARDING_GRAPH_DB);
        List<JSONObject> otnPortList = otnPortInfo.getData();

        Map<String, String> otnPortMap = new HashMap<>();
        Set<String> otnPortNames = new HashSet<>();
        for (JSONObject otnPort : otnPortList) {
            otnPortMap.put(otnPort.getString("portId"), otnPort.getString("aliasname"));
            otnPortNames.add(otnPort.getString("aliasname"));
        }

        //BC库设备
        JSONObject bcDevParam = new JSONObject();
        bcDevParam.put("deviceNames", otnDevNames);
        PageResponse<JSONObject> bcDevInfo = circuitAnalysisDao.queryBcDeviceByParam(bcDevParam, 100, 0,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> bcDevList = bcDevInfo.getData();

        Map<String, String> bcDevMap = new HashMap<>();
        Set<String> bcDevNames = new HashSet<>();
        for (JSONObject bcDev : bcDevList) {
            bcDevMap.put(bcDev.getString("name"), bcDev.getString("id"));
            bcDevNames.add(bcDev.getString("name"));
        }

        for (String otnDevName : otnDevNames) {
            if (!bcDevNames.contains(otnDevName)) {
//                throw new RuntimeException("设备：" + otnDevName + "，不在BC库中");
            }
        }

        //BC库端口
        JSONObject bcPortParam = new JSONObject();
        bcPortParam.put("portNames", otnPortNames);
        PageResponse<JSONObject> bcPortInfo = circuitAnalysisDao.queryBcPortByParam(bcPortParam, otnPortNames.size(), 0,
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(areaId));
        List<JSONObject> bcPortList2= bcPortInfo.getData();
        List<JSONObject> bcPortList = new ArrayList<>();
        JSONObject p1 = new JSONObject();
        p1.put("name", "IN6/OUT6");
        p1.put("id", "321121840000000105895012");
        bcPortList.add(p1);
        JSONObject p2= new JSONObject();
        p2.put("name", "IN4/OUT4");
        p2.put("id", "321121840000000165026358");
        bcPortList.add(p2);

        Map<String, String> bcPortMap = new HashMap<>();
        Set<String> bcPortNames = new HashSet<>();
        for (JSONObject bcPort : bcPortList) {
            bcPortMap.put(bcPort.getString("name"), bcPort.getString("id"));
            bcPortNames.add(bcPort.getString("name"));
        }

//        for (String otnPortName : otnPortNames) {
//            if (!bcPortNames.contains(otnPortName)) {
//                throw new RuntimeException("端口：" + otnPortName + "，不在BC库中");
//            }
//        }

        // 端口映射临时添加，
        Map<String, String>  portMapTemp = new HashMap<>();
        portMapTemp.put("", "");

        //将cmLinkList中，设备、端口ID替换为，BC库的ID
        for (JSONObject cmLink : cmLinkList) {
            String aNeId = cmLink.getString("a_physic_device_id");//OTN A端设备
            String zNeId = cmLink.getString("z_physic_device_id");//OTN Z端设备
            String aPtpId = cmLink.getString("a_port_id");//OTN A端端口
            String zPtpId = cmLink.getString("z_port_id");//OTN Z端端口

            String aDevId = bcDevMap.get(otnDevMap.get(aNeId));//BC A端设备
            String zDevId = bcDevMap.get(otnDevMap.get(zNeId));//BC Z端设备
            String aPortId = bcPortMap.get(otnPortMap.get(aPtpId));//BC A端端口
            String zPortId = bcPortMap.get(otnPortMap.get(zPtpId));//BC Z端端口
            if (portMapTemp.containsKey(aPtpId)) {
                aPortId = portMapTemp.get(aPtpId);
            }
            if (portMapTemp.containsKey(zPortId)) {
                zPortId = portMapTemp.get(zPortId);
            }

            // 通过光路确认端口映射，
            String ports = portMapCache.get(aDevId+"-" + zDevId);
            if (ports != null) {
                aPortId = ports.split("-")[0];
                zPortId = ports.split("-")[1];
            }
            cmLink.put("a_physic_device_id", aDevId);
            cmLink.put("z_physic_device_id", zDevId);
            cmLink.put("a_port_id", aPortId);
            cmLink.put("z_port_id", zPortId);
        }
    }

    public String getId(Integer count) {
        String time = SDF.format(new Date());
        String suffix = String.format("%020d", Long.valueOf(time + count));
        String id = ID_PRI_FIX + suffix;

        return id;
    }

    public JSONObject createLinkObj(String id, String name, String code, Long specId, String areaId, String circuitCode,
                                     String aPhysicDeviceId, String aPortId, String zPhysicDeviceId, String zPortId) {
        JSONObject linkObj = new JSONObject();
        linkObj.put("id", id);
        linkObj.put("name", name);
        linkObj.put("code", code);
        linkObj.put("spec_id", specId);
        linkObj.put("creator_id", 11);
        linkObj.put("create_date", new Date());
        linkObj.put("modifier_id", 11);
        linkObj.put("modify_date", new Date());
        linkObj.put("sharding_id", new BigDecimal(areaId));
        linkObj.put("is_valid", 100383);
        linkObj.put("circuit_code", circuitCode);
        linkObj.put("a_physic_device_id", aPhysicDeviceId);
        linkObj.put("a_port_id", aPortId);
        linkObj.put("z_physic_device_id", zPhysicDeviceId);
        linkObj.put("z_port_id", zPortId);
        linkObj.put("circuit_code", code);
        return linkObj;
    }
    public long getId() {
        SecureRandom random = new SecureRandom();
        return random.nextLong() & 0x7FFFFFFFFFL;
    }

    public JSONObject createLinkLinkObj(String id, Long specId, String upperLinkId, String lowerLinkId, String areaId, String circuitCode,
                                         Long upperLinkSpecId, Long lowerLinkSpecId) {
        JSONObject linkLinkObj = new JSONObject();
        linkLinkObj.put("id", id);
        linkLinkObj.put("spec_id", specId);
        linkLinkObj.put("upper_link_id", upperLinkId);
        linkLinkObj.put("lower_link_id", lowerLinkId);
        linkLinkObj.put("creator_id", 11);
        linkLinkObj.put("create_date", new Date());
        linkLinkObj.put("modifier_id", 11);
        linkLinkObj.put("modify_date", new Date());
        linkLinkObj.put("time_stamp", new Date());
        linkLinkObj.put("sharding_id", new BigDecimal(areaId));
        linkLinkObj.put("circuit_code", circuitCode);
        linkLinkObj.put("upper_link_spec_id", upperLinkSpecId);
        linkLinkObj.put("lower_link_spec_id", lowerLinkSpecId);
        return linkLinkObj;
    }

    public void routesave(String id,String shardingCode) {
        JSONObject queryJson = new JSONObject();
        JSONObject inputJson = new JSONObject();
        List<BigDecimal> jxgxIds = new ArrayList<>();
        jxgxIds.add(new BigDecimal(id));
        queryJson.put("ids", jxgxIds);
        JSONObject jxgx = circuitAnalysisDao.queryResMicroRouteByJxgx(queryJson, shardingCode);
        inputJson.put("nets", jxgx.getJSONArray("nets"));
        inputJson.put("netEntitys", jxgx.getJSONArray("netEntitys"));
        inputJson.put("linkCables", jxgx.getJSONArray("linkCables"));
        inputJson.put("cables", jxgx.getJSONArray("cables"));
        inputJson.put("cableRelas", jxgx.getJSONArray("cableRelas"));
        inputJson.put("cableNodes", jxgx.getJSONArray("cableNodes"));

        circuitAnalysisDao.saveNetCables(inputJson,NRMConstants.SHARDING_CODE);
    }
}
