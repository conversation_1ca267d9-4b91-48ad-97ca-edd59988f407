package com.telecom.nrm.thread.audit;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public class DigitCircuit extends  AbstractCircuitAuditService{
    @Override
    public void getData() {

    }

    @Override
    public <T> List<T> auditData(List<JSONObject> query, Class<T> resultTyoe) {
        return null;
    }

    @Override
    public List<AuditExcelDTO> call() throws Exception {
        return null;
    }
}
