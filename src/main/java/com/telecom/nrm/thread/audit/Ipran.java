package com.telecom.nrm.thread.audit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.service.impl.IpranServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@Slf4j
public class Ipran extends ClouldNetwork2 {


    public boolean ifContainsUorADevice(JSONArray nodes) {
        if (CollectionUtils.isNotEmpty(nodes)) {
            for (Object device : nodes) {
                JSONObject jsonObject = (JSONObject) device;
                String type = jsonObject.getString("network_role_id");
                if (StringUtils.isNotEmpty(type) && IpranServiceImpl.IPRAN_TYPE.contains(type)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public <T> List<T> auditData(List<JSONObject> ipranQueryParam, Class<T> resultTyoe) {
        List auditExcelDTOList = new ArrayList<>();
        for (JSONObject query : ipranQueryParam) {
            AuditExcelDTO auditExcelDTO = new AuditExcelDTO();
            String circuitCode = query.getString("circuitCode");
            String type = query.getString("type");
            auditExcelDTO.setCircuitCode(circuitCode);
            auditExcelDTO.setBusinessType(type);
            String regionName = getRegionByCircuitCode(circuitCode);
            auditExcelDTO.setRegionName(regionName);
            String accessCode;
            try {
                accessCode = super.getAccessCodeByCircuitCode(circuitCode);
            } catch (Exception exception) {
                log.error("", exception);
                auditExcelDTO.setResult(exception.getMessage());
                auditExcelDTOList.add(auditExcelDTO);
                continue;
            }
            if (StringUtils.isNotEmpty(accessCode)) {
                JSONObject mstpQuery = new JSONObject();
                mstpQuery.put("access_code", accessCode);
                String areaCode = ShardingEnum.getShardingEnumByRegionName(regionName).toString().toLowerCase(Locale.ROOT);
                mstpQuery.put("areaCode", areaCode);
                mstpQuery.put("code", circuitCode);
                auditExcelDTO.setAccessCode(accessCode);
                JSONObject mstpResult = null;
                try {
                    mstpResult = super.getMstpResult(mstpQuery);
                } catch (Exception exception) {
                    log.error("", exception);
                    auditExcelDTO.setResult(exception.getMessage());
                    auditExcelDTOList.add(auditExcelDTO);
                }
                if (null != mstpResult && mstpResult.size() > 0 && mstpResult.getJSONArray("nodes") != null
                        && mstpResult.getJSONArray("nodes").size() > 0) {
                    if (ifContainsUorADevice(mstpResult.getJSONArray("nodes"))) {
                        List<JSONObject> requery = new ArrayList<>();
                        requery.add(query);
                        List<AuditExcelDTO> auditExcelDTOS =
                                super.auditData(requery, AuditExcelDTO.class);
                        if (CollectionUtils.isNotEmpty(auditExcelDTOS)) {
                            auditExcelDTOList.addAll(auditExcelDTOS);
                        }
                    } else if (StringUtils.isNotEmpty(type) && type.toLowerCase(Locale.ROOT).contains("otn") &&
                            type.contains("精品")) {
                        auditExcelDTO.setResult("OTN跳过");
                        auditExcelDTOList.add(auditExcelDTO);
                    } else if (StringUtils.isNotEmpty(type) && type.toLowerCase(Locale.ROOT).contains("数字")) {
                 /*   AbstractCircuitAuditService digitCircuit = new DigitCircuit();
                    //  to do  数字电路逻辑
                    List list= digitCircuit.auditData(new ArrayList<>((Collection<? extends JSONObject>) query),Object.class);
                    if (CollectionUtils.isNotEmpty(list)){
                        auditExcelDTOList.addAll(list);
                    }*/
                        auditExcelDTO.setResult("nm_code 暂时无逻辑");
                        auditExcelDTOList.add(auditExcelDTO);
                    } else {
                        auditExcelDTO.setResult(String.format("类型为 %s ,逻辑待补充", type));
                        auditExcelDTOList.add(auditExcelDTO);
                    }
                } else {
                    auditExcelDTO.setRouteExist("否");
                    auditExcelDTOList.add(auditExcelDTO);
                }

            } else {
                auditExcelDTO.setAccessCode("未查询到接入号信息");
                auditExcelDTO.setRouteExist("");
                auditExcelDTOList.add(auditExcelDTO);
            }

        }
        return auditExcelDTOList;
    }

    @Override
    public List<AuditExcelDTO> call() throws Exception {
        System.out.println(String.format("总数量为%s 当前线程为%s,起始行为%s,结束行为%s", super.getTotalSize(),
                Thread.currentThread().getName(), super.getStart(), super.getEnd()));
        return this.auditData(super.getQuery(), AuditExcelDTO.class);
    }

    @Override
    public void getData() {
    }
}
