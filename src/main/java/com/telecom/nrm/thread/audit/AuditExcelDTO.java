package com.telecom.nrm.thread.audit;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AuditExcelDTO {

    @ExcelProperty("地市")
    @ColumnWidth(30)
    private String  regionName;

    @ExcelProperty("电路代号")
    @ColumnWidth(30)
    private String circuitCode;

    @ExcelProperty("接入号")
    @ColumnWidth(30)
    private String accessCode;

    @ExcelProperty("业务类型")
    @ColumnWidth(30)
    private String businessType;

    @ExcelProperty("路由存在（是/否）")
    @ColumnWidth(30)
    private String routeExist = "是";

    @ExcelProperty("稽核结果")
    @ColumnWidth(30)
    private String result;

    @ExcelProperty("资源校验结果")
    @ColumnWidth(30)
    private String resResult;

}
