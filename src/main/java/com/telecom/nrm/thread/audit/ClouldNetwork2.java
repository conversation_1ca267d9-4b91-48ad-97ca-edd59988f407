package com.telecom.nrm.thread.audit;


import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.DaClient;
import com.telecom.da.client.DaProxyFactory;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.PmNetWorkLogDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.utils.ExcelUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.*;


@Slf4j
@Data
public class ClouldNetwork2 extends AbstractCircuitAuditService {



    private List<JSONObject> queryParam;

    public static void main(String[] args) {
        //dev分支的后台api
        DaClient client = new DaClient("http://nrm.oss.telecomjs.com:39049/daas-gray-api/interface", "net_resource_integration");// 测试
        DaProxyFactory factory = new DaProxyFactory(client);
        PmNetWorkLogDao dao = (PmNetWorkLogDao) factory.getInstance(PmNetWorkLogDao.class);
        Sheet sheet = ExcelUtil.getXSSFWorkbookSheet(AbstractCircuitAuditService.inputFilePath, AbstractCircuitAuditService.sheetNames[6]);
        if (sheet != null) {
            Row row = sheet.getRow(0);
            int codeIndex = 0, typeIndex = 0;
            for (int i = 0; i < row.getLastCellNum(); i++) {
                String value = row.getCell(i).getStringCellValue();
                if ("电路代号".equals(value)) {
                    codeIndex = i;

                }
                if ("电路类别".equals(value)) {
                    typeIndex = i;
                    break;
                }
            }
            List<JSONObject> query = new ArrayList<>();
            for (int i = 1; i < sheet.getLastRowNum(); i++) {
                Row row1 = sheet.getRow(i);
                String circuitCode = row1.getCell(codeIndex).getStringCellValue();
                String type = row1.getCell(typeIndex).getStringCellValue();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("circuitCode", circuitCode);
                jsonObject.put("type", type);
                query.add(jsonObject);
            }
            ClouldNetwork2 clouldNetwork2 = new ClouldNetwork2();
            clouldNetwork2.setQueryParam(query);
            clouldNetwork2.setPmNetWorkLogDao(dao);
            clouldNetwork2.getData();
        }

    }


    /**
     * {
     * "source_m_pw": "pw31013539",
     * "b1_bussiness_ip": "*************",
     * "source_ip": "**********",
     * "b2_bussiness_ip": "*************",
     * "b1_ip": "***********",
     * "s_vcid": "32013539",
     * "b2_ip": "***********",
     * "source_vlan": null,
     * "terminal_business_ip": "*************",
     * "terminal_ip": "***********",
     * "a1_ip": "**********",
     * "source_s_pw": "pw32013539",
     * "m_vcid": "31013539",
     * "terminal_vlan": 1035,
     * "source_business_ip": "**********",
     * "source_port": "eth 1/1",
     * "terminal_m_pw": null,
     * "a1_bussiness_ip": "**********",
     * "terminal_port": "GigabitEthernet1/0/1",
     * "source_type": "U",
     * "terminal_s_pw": null,
     * "terminal_type": "ASBR"
     * }
     */


    @Override
    public void getData() {
        List<AuditExcelDTO> auditExcelDTOList = new ArrayList<>();
        // 生成中间表可能要用到稽核的数据,暂时全扔map中
        for (JSONObject query : queryParam) {
            Map<String, Object> result = new HashMap<>();
            AuditExcelDTO auditExcelDTO = new AuditExcelDTO();
            String circuitCode = query.getString("circuitCode");
            String type = query.getString("type");
            auditExcelDTO.setCircuitCode(circuitCode);
            auditExcelDTO.setBusinessType(type);
            String regionName = getRegionByCircuitCode(circuitCode);
            auditExcelDTO.setRegionName(regionName);
            String accessCode;
            try {
                accessCode = super.getAccessCodeByCircuitCode(circuitCode);
            } catch (Exception exception) {
                auditExcelDTO.setResult(exception.getMessage());
                auditExcelDTOList.add(auditExcelDTO);
                continue;
            }
            if (StringUtils.isNotEmpty(accessCode)) {
                auditExcelDTO.setAccessCode(accessCode);
                result.put("accessCode", accessCode);
                String cloud2Data;
                try {
                    cloud2Data = super.getCloud2Data(accessCode);
                } catch (Exception exception) {
                    auditExcelDTO.setResult(exception.getMessage());
                    auditExcelDTOList.add(auditExcelDTO);
                    continue;
                }
                if (StringUtils.isNotEmpty(cloud2Data)) {
                    result.put("cloud2Data", cloud2Data);
                    JSONObject jsonObject = JSONObject.parseObject(cloud2Data);
                    if (jsonObject != null) {
                        if (jsonObject.containsKey("error")) {
                            String errorMessage = jsonObject.getString("error");
                            auditExcelDTO.setResult(errorMessage);
                            log.error("cloud2Data error");
                        } else {
                            NetworkConfiguration2 networkConfiguration2 = new NetworkConfiguration2(jsonObject);
                            Map<String, String> ips = networkConfiguration2.getIps();
                            Map<String, String> ports = networkConfiguration2.getPorts();
                            result.put("ips", ips);
                            result.put("ports", ports);
                            String stringResult = getResult(ips, ports);
                            auditExcelDTO.setResult(stringResult);
                            String stringResultRes = getResultRes(ips, ports, "ds_bc_o3_"+
                                    ShardingEnum.getShardingEnumByRegionName(regionName).toString().toLowerCase(Locale.ROOT));
                            auditExcelDTO.setResResult(stringResultRes);
                        }
                    }
                }
            } else {
                auditExcelDTO.setRouteExist("");
                auditExcelDTO.setAccessCode("未查询到接入号信息");
            }
            auditExcelDTOList.add(auditExcelDTO);
        }
        if (CollectionUtils.isNotEmpty(auditExcelDTOList)) {
            ExcelUtil.writeExcel(AbstractCircuitAuditService.outFilePath +
                            "数据稽核" + AbstractCircuitAuditService.simpleDate.format(new Date()) + ".xlsx",
                    "数据稽核", AuditExcelDTO.class, auditExcelDTOList);
        }
    }

    @Override
    public <T> List<T> auditData(List<JSONObject> queryParam, Class<T> resultTyoe) {
        List<AuditExcelDTO> auditExcelDTOList = new ArrayList<>();
        // 生成中间表可能要用到稽核的数据,暂时全扔map中
        for (JSONObject query : queryParam) {
            Map<String, Object> result = new HashMap<>();
            AuditExcelDTO auditExcelDTO = new AuditExcelDTO();
            String circuitCode = query.getString("circuitCode");
            String type = query.getString("type");
            auditExcelDTO.setCircuitCode(circuitCode);
            auditExcelDTO.setBusinessType(type);
            String regionName = getRegionByCircuitCode(circuitCode);
            auditExcelDTO.setRegionName(regionName);
            String accessCode;
            try {
                accessCode = super.getAccessCodeByCircuitCode(circuitCode);
            } catch (Exception exception) {
                log.error("",exception);
                auditExcelDTO.setResult(exception.getMessage());
                auditExcelDTOList.add(auditExcelDTO);
                continue;
            }
            if (StringUtils.isNotEmpty(accessCode)) {
                auditExcelDTO.setAccessCode(accessCode);
                result.put("accessCode", accessCode);
                String cloud2Data;
                try {
                    cloud2Data = super.getCloud2Data(accessCode);
                } catch (Exception exception) {
                    log.error("",exception);
                    auditExcelDTO.setResult(exception.getMessage());
                    auditExcelDTOList.add(auditExcelDTO);
                    continue;
                }
                if (StringUtils.isNotEmpty(cloud2Data)) {
                    result.put("cloud2Data", cloud2Data);
                    JSONObject jsonObject = JSONObject.parseObject(cloud2Data);
                    if (jsonObject != null) {
                        if (jsonObject.containsKey("error")) {
                            String errorMessage = jsonObject.getString("error");
                            auditExcelDTO.setResult(errorMessage);
                            log.error("cloud2Data error");
                        } else {
                            NetworkConfiguration2 networkConfiguration2 = new NetworkConfiguration2(jsonObject);
                            Map<String, String> ips = networkConfiguration2.getIps();
                            Map<String, String> ports = networkConfiguration2.getPorts();
                            result.put("ips", ips);
                            result.put("ports", ports);
                            String stringResult = getResult(ips, ports);
                            auditExcelDTO.setResult(stringResult);
                            String stringResultRes = getResultRes(ips, ports, "ds_bc_o3_"+
                                    ShardingEnum.getShardingEnumByRegionName(regionName).toString().toLowerCase(Locale.ROOT));
                            auditExcelDTO.setResResult(stringResultRes);
                        }
                    }
                }
            } else {
                auditExcelDTO.setAccessCode("deviceConfig之前未获取到接入号");
            }
            auditExcelDTOList.add(auditExcelDTO);
        }
      return (List<T>) auditExcelDTOList;
    }


    public String getResultRes(Map<String, String> ips, Map<String, String> ports, String shardingCode) {
        StringBuilder deviceBuilder = new StringBuilder();
        StringBuilder portBuilder = new StringBuilder();
        StringBuilder lastCheckBuilder = new StringBuilder();
        lastCheckBuilder.append("设备、端口、设备和端口关系是否校验通过:");
        deviceBuilder.append("设备校验结果").append('\n');
        portBuilder.append("端口校验结果").append('\n');
        boolean flag = true;
        for (Map.Entry<String, String> entry : ips.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            JSONObject ipQueryJson = new JSONObject();
            ipQueryJson.put("manageIp", value);

            JSONObject ipJson = super.getPmNetWorkLogDao().auditBcIpQueryDevice(ipQueryJson, shardingCode);
            deviceBuilder.append(key).append('(').append(value).append(')');
            if (ipJson != null && ipJson.containsKey("id")) {
                String deviceId = ipJson.getString("id");
                JSONObject portQueryJson = new JSONObject();
                portQueryJson.put("id", deviceId);
                List<JSONObject> portJson = super.getPmNetWorkLogDao().auditDevieQueryPort(portQueryJson, shardingCode);
                if (CollectionUtils.isNotEmpty(portJson)) {
                    deviceBuilder.append("此IP下有设备,设备有端口");
                    if ("source_business_ip".equals(key)) {
                        portBuilder.append("source_business_ip").append('(').append(value).append(')')
                                .append("此IP下有设备,设备有端口");
                        Optional<JSONObject> isSourcePort = portJson.stream()
                                .filter(e -> e.containsValue(ports.get("source_port"))).findAny();
                        if (isSourcePort.isPresent()) {
                            String portCode = isSourcePort.get().getString("code");
                            if (StringUtils.isNotEmpty(ports.get("source_port")) && portCode.equals(ports.get("source_port"))) {
                                portBuilder.append("设备和端口对应").append("  ");
                            } else {
                                flag = false;
                                portBuilder.append("设备和端口不对应").append("  ");
                            }
                        }

                    }
                    if ("terminal_ip".equals(key)) {
                        portBuilder.append("terminal_ip").append('(').append(value).append(')')
                                .append("此IP下有设备,设备有端口");
                        Optional<JSONObject> isSourcePort = portJson.stream()
                                .filter(e -> e.containsValue(ports.get("terminal_port"))).findAny();
                        if (isSourcePort.isPresent()) {
                            String portCode = isSourcePort.get().getString("code");
                            if (StringUtils.isNotEmpty(ports.get("terminal_port")) && portCode.equals(ports.get("terminal_port"))) {
                                portBuilder.append("设备和端口对应").append("  ");
                            } else {
                                flag = false;
                                portBuilder.append("设备和端口不对应").append("  ");
                            }
                        }

                    }


                } else {
                    flag = false;
                    deviceBuilder.append("此IP下有设备,设备无端口");
                    if ("source_business_ip".equals(key)) {
                        portBuilder.append("source_business_ip").append('(').append(value).append(')')
                                .append("此IP下有设备,设备无端口");
                    }
                    if ("terminal_ip".equals(key)) {
                        portBuilder.append("terminal_ip").append('(').append(value).append(')')
                                .append("此IP下有设备,设备无端口");
                    }
                }
            } else {
                flag = false;
                deviceBuilder.append("此IP未查询到设备信息").append("    ");
                if ("source_business_ip".equals(key)) {
                    portBuilder.append("source_business_ip").append('(').append(value).append(')')
                            .append("此IP未查询到设备信息");
                }
                if ("terminal_ip".equals(key)) {
                    portBuilder.append("terminal_ip").append('(').append(value).append(')')
                            .append("此IP未查询到设备信息");
                }
            }
        }
        deviceBuilder.append(portBuilder);
        lastCheckBuilder.append(flag ? "是" : "否").append('\n').append(deviceBuilder);
        return lastCheckBuilder.toString();

    }


    public String getResult(Map<String, String> ips, Map<String, String> ports) {
        StringBuilder deviceBuilder = new StringBuilder();
        StringBuilder portBuilder = new StringBuilder();
        StringBuilder lastCheckBuilder = new StringBuilder();
        lastCheckBuilder.append("设备、端口、设备和端口关系是否校验通过:");
        deviceBuilder.append("设备校验结果").append('\n');
        portBuilder.append("端口校验结果").append('\n');
        boolean flag = true;
        for (Map.Entry<String, String> entry : ips.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            JSONObject ipQueryJson = new JSONObject();
            ipQueryJson.put("manageIp", value);
            JSONObject ipJson = super.getPmNetWorkLogDao().auditIpQueryDevice(ipQueryJson, NRMConstants.SHARDING_CODE);
            deviceBuilder.append(key).append('(').append(value).append(')');
            if (ipJson != null && ipJson.containsKey("id")) {
                String deviceId = ipJson.getString("id");
                JSONObject portQueryJson = new JSONObject();
                portQueryJson.put("deviceId", deviceId);
                JSONObject portJson = super.getPmNetWorkLogDao().auditIpQueryDevice(portQueryJson, NRMConstants.SHARDING_CODE);
                if (portJson != null && portJson.containsKey("code")) {
                    deviceBuilder.append("此IP下有设备,设备有端口");
                    if ("source_business_ip".equals(key)) {
                        portBuilder.append("source_business_ip").append('(').append(value).append(')')
                                .append("此IP下有设备,设备有端口");
                        if (StringUtils.isNotEmpty(ports.get("source_port")) && portJson.getString("code").equals
                                (ports.get("source_port"))) {
                            portBuilder.append("设备和端口对应").append("  ");
                        } else {
                            flag = false;
                            portBuilder.append("设备和端口不对应").append("  ");
                        }
                    }
                    if ("terminal_ip".equals(key)) {
                        portBuilder.append("terminal_ip").append('(').append(value).append(')')
                                .append("此IP下有设备,设备有端口");
                        if (StringUtils.isNotEmpty(ports.get("terminal_port")) && portJson.getString("code").equals
                                (ports.get("terminal_port"))) {
                            portBuilder.append("设备和端口对应").append("  ");
                        } else {
                            flag = false;
                            portBuilder.append("设备和端口不对应").append("  ");
                        }
                    }

                } else {
                    flag = false;
                    deviceBuilder.append("此IP下有设备,设备无端口");
                    if ("source_business_ip".equals(key)) {
                        portBuilder.append("source_business_ip").append('(').append(value).append(')')
                                .append("此IP下有设备,设备无端口");
                    }
                    if ("terminal_ip".equals(key)) {
                        portBuilder.append("terminal_ip").append('(').append(value).append(')')
                                .append("此IP下有设备,设备无端口");
                    }
                }
            } else {
                flag = false;
                deviceBuilder.append("此IP未查询到设备信息").append("    ");
                if ("source_business_ip".equals(key)) {
                    portBuilder.append("source_business_ip").append('(').append(value).append(')')
                            .append("此IP未查询到设备信息");
                }
                if ("terminal_ip".equals(key)) {
                    portBuilder.append("terminal_ip").append('(').append(value).append(')')
                            .append("此IP未查询到设备信息");
                }
            }
        }
        deviceBuilder.append(portBuilder);
        lastCheckBuilder.append(flag ? "是" : "否").append('\n').append(deviceBuilder);
        return lastCheckBuilder.toString();
    }

    @Override
    public List<AuditExcelDTO> call() throws Exception {
        return null;
    }

    @Data
    static class NetworkConfiguration2 {
        private final String source_m_pw;
        private final String b1_bussiness_ip;
        private final String source_ip;
        private final String b2_bussiness_ip;
        private final String b1_ip;
        private final String s_vcid;
        private final String b2_ip;
        private final String source_vlan;
        private final String terminal_business_ip;
        private final String terminal_ip;
        private final String a1_ip;
        private final String source_s_pw;
        private final String m_vcid;
        private final Integer terminal_vlan;
        private final String source_business_ip;
        private final String source_port;
        private final String terminal_m_pw;
        private final String a1_bussiness_ip;
        private final String terminal_port;
        private final String source_type;
        private final String terminal_s_pw;
        private final String terminal_type;


        public NetworkConfiguration2(JSONObject jsonObject) {
            this.source_m_pw = jsonObject.getString("source_m_pw");
            this.b1_bussiness_ip = jsonObject.getString("b1_bussiness_ip");
            this.source_ip = jsonObject.getString("source_ip");
            this.b2_bussiness_ip = jsonObject.getString("b2_bussiness_ip");
            this.b1_ip = jsonObject.getString("b1_ip");
            this.s_vcid = jsonObject.getString("s_vcid");
            this.b2_ip = jsonObject.getString("b2_ip");
            this.source_vlan = jsonObject.getString("source_vlan");
            this.terminal_business_ip = jsonObject.getString("terminal_business_ip");
            this.terminal_ip = jsonObject.getString("terminal_ip");
            this.a1_ip = jsonObject.getString("a1_ip");
            this.source_s_pw = jsonObject.getString("source_s_pw");
            this.m_vcid = jsonObject.getString("m_vcid");
            this.terminal_vlan = jsonObject.getInteger("terminal_vlan");
            this.source_business_ip = jsonObject.getString("source_business_ip");
            this.source_port = jsonObject.getString("source_port");
            this.terminal_m_pw = jsonObject.getString("terminal_m_pw");
            this.a1_bussiness_ip = jsonObject.getString("a1_bussiness_ip");
            this.terminal_port = jsonObject.getString("terminal_port");
            this.source_type = jsonObject.getString("source_type");
            this.terminal_s_pw = jsonObject.getString("terminal_s_pw");
            this.terminal_type = jsonObject.getString("terminal_type");
        }

        public Map<String, String> getIps() {
            Map<String, String> ips = new HashMap<>();
            ips.put("b1_bussiness_ip", b1_bussiness_ip);
            ips.put("source_ip", source_ip);
            ips.put("b2_bussiness_ip", b2_bussiness_ip);
            ips.put("b1_ip", b1_ip);
            ips.put("b2_ip", b2_ip);
            ips.put("terminal_business_ip", terminal_business_ip);
            ips.put("terminal_ip", terminal_ip);
            ips.put("a1_ip", a1_ip);
            ips.put("source_business_ip", source_business_ip);
            ips.put("a1_bussiness_ip", a1_bussiness_ip);
            return ips;
        }

        public Map<String, String> getPorts() {
            Map<String, String> ports = new HashMap<>();
            ports.put("source_port", source_port);
            ports.put("terminal_port", terminal_port);
            return ports;
        }

    }

}
