package com.telecom.nrm.thread;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class Main {



    public static void main(String[] args) {

        int threadNum = 8;
        String codeUrl = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/circuitsorderapi";
        String ipranUrl = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/ipranView";
        String au = "Bearer eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************.CKaY9J9uxunhPMwk8fncAN9uI5XDCCwKwYuSWSyg7BOSYOaqLWhe0s1x8WImNFAquEnwDQtUODLXkAka_wrr4w";

      //  String inputFile = "src/main/resources/excel/电路明细20240829-1.xlsx";
        String inputFile = "src/main/resources/excel/849465416465.xlsx";
        String outputFile = "src/main/resources/excel/";

        try {
            FileInputStream fis = new FileInputStream(inputFile);
            Workbook workbook = new XSSFWorkbook(fis);

            Workbook outputWorkbook = new XSSFWorkbook();
            Sheet outputSheet = outputWorkbook.createSheet();
            String[] sheet2title = {"电路代号", "电路类别", "地市", "电路是存在", "链路模型", "是否存在跨机房软跳"};
            String[] arr = {"Sheet1"};
           // String[] arr = {"Sheet1", "Sheet2", "Sheet3", "Sheet4"};

            Row row = outputSheet.createRow(0);
            for (int i = 0; i < sheet2title.length; i++) {
                row.createCell(i).setCellValue(sheet2title[i]);
            }
            // 创建线程池
            ExecutorService executorService = Executors.newFixedThreadPool(threadNum);
            List<Future<List<IpranTast2.Line>>> futures = new ArrayList<>();
            int nextStartRow = 0;
            // 遍历每个 Sheet 并提交任务到线程池
            for (String s : arr) {
                Sheet sheet = workbook.getSheet(s);
                int rowCount = sheet.getLastRowNum() - sheet.getFirstRowNum();
                int taskSize = Math.max(1, (rowCount+threadNum-1) / threadNum); // 每个线程处理的行数
                for (int i = 0; i < threadNum; i++) {
                    int startRow = i * taskSize + 1;
                    int endRow = Math.min(sheet.getLastRowNum(), startRow + taskSize - 1);
                    IpranTast2 ipranTast2 = new IpranTast2();
                    ipranTast2.setInputSheet(sheet);
                    ipranTast2.setOutputSheet(outputSheet);
                    ipranTast2.setStartRow(startRow);
                    ipranTast2.setEndRow(endRow);
                    nextStartRow += taskSize * 2 +1;
                    ipranTast2.setOutPutStartRow(i == 0 ? endRow * 2 : nextStartRow);
                    Future<List<IpranTast2.Line>> future = executorService.submit(ipranTast2);
                    futures.add(future);
                }
            }
            int outRow = 1;
            List<IpranTast2.Line> lines = new ArrayList<>();
            // 等待所有任务完成并获取结果
            for (Future<List<IpranTast2.Line>>future : futures) {
                List<IpranTast2.Line> result = future.get();
                if (CollectionUtils.isNotEmpty(result)){
                    lines.addAll(result);
                }
            }
            // 关闭线程池
;            executorService.shutdown();
            if (CollectionUtils.isNotEmpty(lines)){
                for (int i = 0; i < lines.size(); i++) {
                    IpranTast2.Line line=  lines.get(i);
                    Row row1= outputSheet.createRow(outRow++);
                    for (int j = 0; j < line.getTitle().length; j++) {
                        row1.createCell(j).setCellValue(line.getTitle()[j]);
                    }

                }
            }
            SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMddhhmmss");
            String fileName=simpleDateFormat.format(new Date());
            // 将新的工作簿写入输出文件
            FileOutputStream fos = new FileOutputStream(outputFile +"电路明细" + fileName  + ".xlsx");
            outputWorkbook.write(fos);
            fos.close();
            System.out.println("Excel file updated successfully.");
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
    }


}
