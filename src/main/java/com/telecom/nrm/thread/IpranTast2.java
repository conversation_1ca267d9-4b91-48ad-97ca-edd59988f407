package com.telecom.nrm.thread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.locks.ReentrantLock;

@Data
public class IpranTast2 implements Callable<List<IpranTast2.Line>> {

    private ReentrantLock reentrantLock;

    private Sheet inputSheet;

    volatile int start = 0;

    private int startRow;

    private int endRow;

    private Sheet outputSheet;

    private int outPutStartRow;

    private String codeUrl = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/circuitsorderapi";

    private String ipranUrl = "http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/customer-topology/ipranView";

    private String au = "Bearer eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************.CKaY9J9uxunhPMwk8fncAN9uI5XDCCwKwYuSWSyg7BOSYOaqLWhe0s1x8WImNFAquEnwDQtUODLXkAka_wrr4w";


    @Override
    public List<Line> call() {
        List<Line> list = new LinkedList<>();
        System.out.println(String.format("当前sheet为%s,当前线程为%s,起始行为%s,结束行为%s 输入的输入行为%s", inputSheet.getSheetName(), Thread.currentThread().getName(),
                this.startRow, this.endRow, this.outPutStartRow));
        int code = 0, type = 0;
        Row row0 = inputSheet.getRow(0);
        for (int i = 0; i < row0.getLastCellNum(); i++) {
            Cell cell = row0.getCell(i);
            if (cell.getStringCellValue().equals("电路代号")) {
                code = i;
            } else if (cell.getStringCellValue().equals("电路类别")) {
                type = i;
                break;
            }
        }
        for (int i = startRow; i <= endRow; i++) {
            Row row1 = inputSheet.getRow(i);
            String codeId = row1.getCell(code).getStringCellValue();
            String typeId = row1.getCell(type).getStringCellValue();
            extracted(list, codeId, typeId);

        }
        return list;
    }

    public void extracted(List<Line> list, String codeId, String typeId) {
        for (int i1 = 0; i1 < ShardingEnum.values().length; i1++) {
            ShardingEnum shardingEnum = ShardingEnum.values()[i1];
            String region = shardingEnum.getRegionName();
            if (codeId.contains(region)) {
                Line line = new Line();
                list.add(line);
                line.setFirst(codeId);
                line.setSecond(typeId);
                line.setThird(region);
                Map<String, String> header = new HashMap<>();
                header.put("Authorization", au);
                Map<String, String> params = new HashMap<>();
                params.put("page", "1");
                params.put("size", "10");
                params.put("ds", region);
                params.put("circuit_code", codeId);
                String codeResult = null;
                try {
                    codeResult = HttpRequestsUtils.sendGet(codeUrl, header, params);
                } catch (IOException e) {
                    e.printStackTrace();
                    line.setFourth("接口异常");
                    continue;
                }
                if (StringUtils.isEmpty(codeResult)) {
                    line.setFourth("否");
                    line.setFifth("");
                    line.setSixth("否");
                } else {
                    JSONObject jsonObject = JSONObject.parseObject(codeResult);
                    JSONArray data = jsonObject.getJSONArray("data");
                    if (data.size() > 0 && StringUtils.isNotEmpty(jsonObject.getJSONArray("data").getJSONObject(0).getString("access_code"))) {
                        String access_code = jsonObject.getJSONArray("data").getJSONObject(0).getString("access_code");
                        JSONObject body = new JSONObject();
                        body.put("access_code", access_code);
                        body.put("areaCode", shardingEnum.toString().toLowerCase(Locale.ROOT));
                        body.put("code", codeId);
                        String ipranResult = null;
                        try {
                            ipranResult = HttpRequestsUtils.sendPost(ipranUrl, header, body);
                        } catch (IOException e) {
                            e.printStackTrace();
                            line.setFourth("接口异常");
                            continue;
                        }
                        if (StringUtils.isEmpty(ipranResult)) {
                            line.setFifth("");
                            line.setSixth("否");
                        } else {
                            try {
                                JSONObject request = JSONObject.parseObject(ipranResult);
                                Set<IpranTask.Link> linkSet = new LinkedHashSet<>();
                                IpranTask.recGetLinks(request.getJSONArray("links"), linkSet);
                                List<IpranTask.Node> nodeList = IpranTask.Node.jsonarryToList(request.getJSONArray("nodes"));
                                String getLink2LinkSegment = IpranTask.getLink2LinkSegment(linkSet, nodeList);
                                boolean falg = IpranTask.checkLinkAcrossFactory(linkSet, nodeList);
                                if ("数量小于1".equals(getLink2LinkSegment)) {
                                    line.setFourth("否");
                                    line.setFifth("");
                                    line.setSixth("否");
                                } else {
                                    line.setFourth("是");
                                    line.setFifth(getLink2LinkSegment);
                                    line.setSixth(falg ? "是" : "否");
                                }
                            } catch (Exception e) {
                                line.setFourth("接口异常" + e.getMessage());
                                continue;
                            }

                        }

                    } else {
                        line.setFourth("否");
                        line.setFifth("");
                        line.setSixth("否");

                    }
                }


            }
        }
    }

    @Data
    static class Line {
        private String first;
        private String second;
        private String third;
        private String fourth;
        private String fifth;
        private String sixth;

        public String[] getTitle() {
            return new String[]{first, second, third, fourth, fifth, sixth};
        }
    }
}
