package com.telecom.nrm.agent.chart.model;

import java.util.List;
import java.util.Map;

/**
 * 图表数据模型类
 * 用于表示生成图表所需的数据
 */
public class ChartData {

    /**
     * 图表类型
     */
    private String type;

    /**
     * 图表标题
     */
    private String title;

    /**
     * X轴标签
     */
    private String xAxisLabel;

    /**
     * Y轴标签
     */
    private String yAxisLabel;

    /**
     * 图表数据系列
     */
    private List<Series> series;

    /**
     * 图表宽度
     */
    private int width = 600;

    /**
     * 图表高度
     */
    private int height = 400;

    /**
     * 其他配置选项
     */
    private Map<String, Object> options;

    /**
     * 数据系列类
     */
    public static class Series {
        /**
         * 系列名称
         */
        private String name;

        /**
         * 系列数据
         */
        private List<Number> data;

        /**
         * 系列标签
         */
        private List<String> labels;

        /**
         * 系列颜色
         */
        private String color;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Number> getData() {
            return data;
        }

        public void setData(List<Number> data) {
            this.data = data;
        }

        public List<String> getLabels() {
            return labels;
        }

        public void setLabels(List<String> labels) {
            this.labels = labels;
        }

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getxAxisLabel() {
        return xAxisLabel;
    }

    public void setxAxisLabel(String xAxisLabel) {
        this.xAxisLabel = xAxisLabel;
    }

    public String getyAxisLabel() {
        return yAxisLabel;
    }

    public void setyAxisLabel(String yAxisLabel) {
        this.yAxisLabel = yAxisLabel;
    }

    public List<Series> getSeries() {
        return series;
    }

    public void setSeries(List<Series> series) {
        this.series = series;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public Map<String, Object> getOptions() {
        return options;
    }

    public void setOptions(Map<String, Object> options) {
        this.options = options;
    }
}
