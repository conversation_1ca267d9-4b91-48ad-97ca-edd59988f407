package com.telecom.nrm.agent.chart.service.impl;

import com.alibaba.fastjson.JSON;
import com.telecom.nrm.agent.chart.model.ChartData;
import com.telecom.nrm.agent.chart.service.ChartService;
import org.apache.commons.codec.binary.Base64;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.CategoryAxis;
import org.jfree.chart.axis.NumberAxis;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PiePlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.plot.XYPlot;
import org.jfree.chart.renderer.category.BarRenderer;
import org.jfree.chart.renderer.category.LineAndShapeRenderer;
import org.jfree.chart.renderer.category.StandardBarPainter;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.xy.XYSeries;
import org.jfree.data.xy.XYSeriesCollection;
import org.jfree.graphics2d.svg.SVGGraphics2D;
import org.jfree.graphics2d.svg.FontMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.List;
import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;

/**
 * 图表服务实现类
 */
@Service
public class ChartServiceImpl implements ChartService {

    private static final Logger logger = LoggerFactory.getLogger(ChartServiceImpl.class);

    @Override
    public BufferedImage generateChart(ChartData chartData) {
        if (chartData == null) {
            throw new IllegalArgumentException("图表数据不能为空");
        }

        JFreeChart chart;

        switch (chartData.getType().toLowerCase()) {
            case "bar":
                chart = createBarChart(chartData);
                break;
            case "line":
                chart = createLineChart(chartData);
                break;
            case "pie":
                chart = createPieChart(chartData);
                break;
            case "scatter":
                chart = createScatterChart(chartData);
                break;
            default:
                throw new IllegalArgumentException("不支持的图表类型: " + chartData.getType());
        }

        // 设置图表背景（支持透明背景）
        setChartBackground(chart, chartData);

        // 启用抗锯齿渲染
        chart.setAntiAlias(true);
        chart.setTextAntiAlias(true);

        // 设置更高的渲染质量
        chart.setRenderingHints(new RenderingHints(
                RenderingHints.KEY_ANTIALIASING,
                RenderingHints.VALUE_ANTIALIAS_ON));
        chart.setRenderingHints(new RenderingHints(
                RenderingHints.KEY_TEXT_ANTIALIASING,
                RenderingHints.VALUE_TEXT_ANTIALIAS_ON));
        chart.setRenderingHints(new RenderingHints(
                RenderingHints.KEY_RENDERING,
                RenderingHints.VALUE_RENDER_QUALITY));
        chart.setRenderingHints(new RenderingHints(
                RenderingHints.KEY_INTERPOLATION,
                RenderingHints.VALUE_INTERPOLATION_BICUBIC));
        chart.setRenderingHints(new RenderingHints(
                RenderingHints.KEY_STROKE_CONTROL,
                RenderingHints.VALUE_STROKE_PURE));

        // 计算实际尺寸，考虑DPI
        int width = chartData.getWidth();
        int height = chartData.getHeight();

        // 如果用户指定了更高的DPI，则按比例增加图像尺寸
        double scale = 1.0;
        if (chartData.getOptions() != null && chartData.getOptions().containsKey("dpi")) {
            Object dpiObj = chartData.getOptions().get("dpi");
            if (dpiObj instanceof Number) {
                double dpi = ((Number) dpiObj).doubleValue();
                // 默认DPI为72，计算缩放比例
                scale = dpi / 72.0;
                width = (int) (width * scale);
                height = (int) (height * scale);
            }
        } else {
            // 默认使用标准DPI (72)来保持图片尺寸较小
            scale = 1.0; // 使用原始尺寸
            // 不进行缩放
        }

        // 创建高清晰度图表图像
        return chart.createBufferedImage(width, height, BufferedImage.TYPE_INT_ARGB, null);
    }

    @Override
    public String generateChartSVG(ChartData chartData) {
        if (chartData == null) {
            throw new IllegalArgumentException("图表数据不能为空");
        }

        JFreeChart chart;

        switch (chartData.getType().toLowerCase()) {
            case "bar":
                chart = createBarChart(chartData);
                break;
            case "line":
                chart = createLineChart(chartData);
                break;
            case "pie":
                chart = createPieChart(chartData);
                break;
            case "scatter":
                chart = createScatterChart(chartData);
                break;
            default:
                throw new IllegalArgumentException("不支持的图表类型: " + chartData.getType());
        }

        // 设置图表背景（支持透明背景）
        setChartBackground(chart, chartData);

        // 设置支持中文的字体
        Font chineseFont = getChineseFont();

        // 设置图表标题字体
        if (chart.getTitle() != null) {
            chart.getTitle().setFont(chineseFont.deriveFont(Font.BOLD, 16f));
        }

        // 设置图例字体
        if (chart.getLegend() != null) {
            chart.getLegend().setItemFont(chineseFont.deriveFont(12f));
        }

        // 根据图表类型设置轴标签字体
        setAxisFonts(chart, chineseFont);

        // 获取图表尺寸
        int width = chartData.getWidth();
        int height = chartData.getHeight();

        // 创建SVG图形上下文
        SVGGraphics2D svgGraphics = new SVGGraphics2D(width, height);

        // 设置字体映射，确保中文字体正确渲染
        try {
            svgGraphics.setFontMapper(new ChineseFontMapper());
        } catch (Exception e) {
            logger.warn("设置字体映射失败，使用默认设置: {}", e.getMessage());
        }

        // 设置渲染提示以改善字体渲染
        svgGraphics.setRenderingHint(java.awt.RenderingHints.KEY_TEXT_ANTIALIASING,
                                   java.awt.RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        svgGraphics.setRenderingHint(java.awt.RenderingHints.KEY_FRACTIONALMETRICS,
                                   java.awt.RenderingHints.VALUE_FRACTIONALMETRICS_ON);

        // 绘制图表到SVG
        chart.draw(svgGraphics, new java.awt.Rectangle(0, 0, width, height));

        // 获取SVG字符串并进行后处理
        String svgContent = svgGraphics.getSVGElement();

        // 手动添加中文字体支持到SVG
        svgContent = addChineseFontSupport(svgContent);

        // 处理SVG背景设置
        svgContent = processSVGBackground(svgContent, chartData);

        return svgContent;
    }

    @Override
    public ChartData parseBase64ChartData(String base64Data) {
        if (base64Data == null || base64Data.isEmpty()) {
            throw new IllegalArgumentException("Base64数据不能为空");
        }

        try {
            // 尝试使用Java 8内置的Base64解码器
            byte[] decodedBytes;
            try {
                decodedBytes = java.util.Base64.getDecoder().decode(base64Data);
            } catch (IllegalArgumentException e) {
                throw e;
            }

            String jsonData = new String(decodedBytes, StandardCharsets.UTF_8);
            logger.debug("解码后的JSON数据: {}", jsonData);

            // 解析JSON数据为ChartData对象
            return JSON.parseObject(jsonData, ChartData.class);
        } catch (Exception e) {
            logger.error("解析Base64图表数据失败: {}", e.getMessage(), e);
            throw new IllegalArgumentException("无效的Base64图表数据: " + e.getMessage());
        }
    }


    /**
     * 创建柱状图
     */
    private JFreeChart createBarChart(ChartData chartData) {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();

        // 填充数据集
        for (ChartData.Series series : chartData.getSeries()) {
            List<Number> data = series.getData();
            List<String> labels = series.getLabels();

            for (int i = 0; i < data.size(); i++) {
                String category = (labels != null && i < labels.size()) ? labels.get(i) : "类别" + (i + 1);
                dataset.addValue(data.get(i), series.getName(), category);
            }
        }

        // 创建柱状图
        JFreeChart chart = ChartFactory.createBarChart(
                chartData.getTitle(),      // 图表标题
                chartData.getxAxisLabel(), // X轴标签
                chartData.getyAxisLabel(), // Y轴标签
                dataset,                   // 数据集
                PlotOrientation.VERTICAL,  // 图表方向
                true,                      // 是否显示图例
                true,                      // 是否生成工具提示
                false                      // 是否生成URL链接
        );

        // 自定义柱状图样式
        CategoryPlot plot = (CategoryPlot) chart.getPlot();
        BarRenderer renderer = (BarRenderer) plot.getRenderer();

        // 设置为平面样式，移除所有光线和渐变效果
        // 禁用阴影
        renderer.setShadowVisible(false);
        // 禁用渐变填充
        renderer.setBarPainter(new StandardBarPainter());
        // 设置柱子边框颜色为透明
        renderer.setDrawBarOutline(false);
        // 设置柱子间距
        renderer.setItemMargin(0.1);

        // 设置柱子颜色
        int seriesCount = chartData.getSeries().size();
        for (int i = 0; i < seriesCount; i++) {
            ChartData.Series series = chartData.getSeries().get(i);
            if (series.getColor() != null && !series.getColor().isEmpty()) {
                try {
                    Color color = Color.decode(series.getColor());
                    renderer.setSeriesPaint(i, color);
                    // 设置相同的边框颜色，增强平面效果
                    renderer.setSeriesOutlinePaint(i, color);
                } catch (NumberFormatException e) {
                    logger.warn("无效的颜色代码: {}", series.getColor());
                }
            }
        }

        // 设置背景和网格线
        plot.setBackgroundPaint(Color.WHITE);
        plot.setRangeGridlinePaint(new Color(230, 230, 230)); // 浅灰色网格线
        plot.setDomainGridlinesVisible(false); // 隐藏垂直网格线

        // 设置轴样式
        CategoryAxis domainAxis = plot.getDomainAxis();
        domainAxis.setCategoryLabelPositions(org.jfree.chart.axis.CategoryLabelPositions.UP_45);
        domainAxis.setLowerMargin(0.03); // 减少边缘空白
        domainAxis.setUpperMargin(0.03); // 减少边缘空白

        // 设置数值轴样式
        NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
        rangeAxis.setStandardTickUnits(NumberAxis.createIntegerTickUnits()); // 使用整数刻度
        rangeAxis.setLowerMargin(0.05); // 设置轴的下边距

        return chart;
    }

    /**
     * 创建折线图
     */
    private JFreeChart createLineChart(ChartData chartData) {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();

        // 填充数据集
        for (ChartData.Series series : chartData.getSeries()) {
            List<Number> data = series.getData();
            List<String> labels = series.getLabels();

            for (int i = 0; i < data.size(); i++) {
                String category = (labels != null && i < labels.size()) ? labels.get(i) : "类别" + (i + 1);
                dataset.addValue(data.get(i), series.getName(), category);
            }
        }

        // 创建折线图
        JFreeChart chart = ChartFactory.createLineChart(
                chartData.getTitle(),      // 图表标题
                chartData.getxAxisLabel(), // X轴标签
                chartData.getyAxisLabel(), // Y轴标签
                dataset,                   // 数据集
                PlotOrientation.VERTICAL,  // 图表方向
                true,                      // 是否显示图例
                true,                      // 是否生成工具提示
                false                      // 是否生成URL链接
        );

        // 自定义折线图样式
        CategoryPlot plot = (CategoryPlot) chart.getPlot();
        LineAndShapeRenderer renderer = (LineAndShapeRenderer) plot.getRenderer();

        // 设置线条颜色和显示数据点
        int seriesCount = chartData.getSeries().size();
        for (int i = 0; i < seriesCount; i++) {
            ChartData.Series series = chartData.getSeries().get(i);
            renderer.setSeriesShapesVisible(i, true);

            if (series.getColor() != null && !series.getColor().isEmpty()) {
                try {
                    Color color = Color.decode(series.getColor());
                    renderer.setSeriesPaint(i, color);
                } catch (NumberFormatException e) {
                    logger.warn("无效的颜色代码: {}", series.getColor());
                }
            }
        }

        return chart;
    }

    /**
     * 创建饼图
     */
    private JFreeChart createPieChart(ChartData chartData) {
        DefaultPieDataset dataset = new DefaultPieDataset();

        // 饼图只使用第一个系列的数据
        if (!chartData.getSeries().isEmpty()) {
            ChartData.Series series = chartData.getSeries().get(0);
            List<Number> data = series.getData();
            List<String> labels = series.getLabels();

            for (int i = 0; i < data.size(); i++) {
                String category = (labels != null && i < labels.size()) ? labels.get(i) : "类别" + (i + 1);
                dataset.setValue(category, data.get(i));
            }
        }

        // 创建饼图
        JFreeChart chart = ChartFactory.createPieChart(
                chartData.getTitle(), // 图表标题
                dataset,              // 数据集
                true,                 // 是否显示图例
                true,                 // 是否生成工具提示
                false                 // 是否生成URL链接
        );

        // 自定义饼图样式
        PiePlot plot = (PiePlot) chart.getPlot();
        plot.setLabelFont(new Font("SansSerif", Font.PLAIN, 12));
        plot.setNoDataMessage("没有可用数据");
        plot.setCircular(true);
        plot.setLabelGap(0.02);

        return chart;
    }

    /**
     * 创建散点图
     */
    private JFreeChart createScatterChart(ChartData chartData) {
        XYSeriesCollection dataset = new XYSeriesCollection();

        // 填充数据集
        for (ChartData.Series series : chartData.getSeries()) {
            XYSeries xySeries = new XYSeries(series.getName());
            List<Number> data = series.getData();
            List<String> labels = series.getLabels();

            // 对于散点图，我们假设数据是成对的 [x1, y1, x2, y2, ...]
            for (int i = 0; i < data.size() - 1; i += 2) {
                xySeries.add(data.get(i).doubleValue(), data.get(i + 1).doubleValue());
            }

            dataset.addSeries(xySeries);
        }

        // 创建散点图
        JFreeChart chart = ChartFactory.createScatterPlot(
                chartData.getTitle(),      // 图表标题
                chartData.getxAxisLabel(), // X轴标签
                chartData.getyAxisLabel(), // Y轴标签
                dataset,                   // 数据集
                PlotOrientation.VERTICAL,  // 图表方向
                true,                      // 是否显示图例
                true,                      // 是否生成工具提示
                false                      // 是否生成URL链接
        );

        // 自定义散点图样式
        XYPlot plot = (XYPlot) chart.getPlot();
        plot.setDomainCrosshairVisible(true);
        plot.setRangeCrosshairVisible(true);

        // 设置点的颜色
        int seriesCount = chartData.getSeries().size();
        for (int i = 0; i < seriesCount; i++) {
            ChartData.Series series = chartData.getSeries().get(i);
            if (series.getColor() != null && !series.getColor().isEmpty()) {
                try {
                    Color color = Color.decode(series.getColor());
                    plot.getRenderer().setSeriesPaint(i, color);
                } catch (NumberFormatException e) {
                    logger.warn("无效的颜色代码: {}", series.getColor());
                }
            }
        }

        return chart;
    }

    /**
     * 获取支持中文的字体
     */
    private Font getChineseFont() {
        // 尝试获取系统中支持中文的字体
        String[] fontNames = {
            "Microsoft YaHei",     // Windows 微软雅黑
            "SimHei",              // Windows 黑体
            "SimSun",              // Windows 宋体
            "PingFang SC",         // macOS 苹方
            "Hiragino Sans GB",    // macOS 冬青黑体
            "STHeiti",             // macOS 华文黑体
            "WenQuanYi Micro Hei", // Linux 文泉驿微米黑
            "Noto Sans CJK SC",    // Linux Noto字体
            "DejaVu Sans"          // 通用字体
        };

        for (String fontName : fontNames) {
            Font font = new Font(fontName, Font.PLAIN, 12);
            if (font.canDisplay('中') && font.canDisplay('文')) {
                logger.debug("使用字体: {}", fontName);
                return font;
            }
        }

        // 如果没有找到合适的字体，使用默认字体
        logger.warn("未找到支持中文的字体，使用默认字体");
        return new Font(Font.SANS_SERIF, Font.PLAIN, 12);
    }

    /**
     * 设置图表背景颜色
     */
    private void setChartBackground(JFreeChart chart, ChartData chartData) {
        // 检查是否指定了背景颜色
        String backgroundColor = "white"; // 默认白色

        if (chartData.getOptions() != null && chartData.getOptions().containsKey("backgroundColor")) {
            backgroundColor = chartData.getOptions().get("backgroundColor").toString().toLowerCase();
        }

        // 设置图表背景
        switch (backgroundColor) {
            case "transparent":
            case "none":
                // 设置透明背景
                chart.setBackgroundPaint(null);
                chart.getPlot().setBackgroundPaint(null);

                // 对于不同类型的图表，设置透明背景
                if (chart.getPlot() instanceof CategoryPlot) {
                    CategoryPlot plot = (CategoryPlot) chart.getPlot();
                    plot.setBackgroundPaint(null);
                    plot.setOutlineVisible(false);
                } else if (chart.getPlot() instanceof XYPlot) {
                    XYPlot plot = (XYPlot) chart.getPlot();
                    plot.setBackgroundPaint(null);
                    plot.setOutlineVisible(false);
                } else if (chart.getPlot() instanceof PiePlot) {
                    PiePlot plot = (PiePlot) chart.getPlot();
                    plot.setBackgroundPaint(null);
                    plot.setOutlineVisible(false);
                }
                break;

            case "white":
            default:
                // 设置白色背景
                chart.setBackgroundPaint(Color.WHITE);

                if (chart.getPlot() instanceof CategoryPlot) {
                    CategoryPlot plot = (CategoryPlot) chart.getPlot();
                    plot.setBackgroundPaint(Color.WHITE);
                } else if (chart.getPlot() instanceof XYPlot) {
                    XYPlot plot = (XYPlot) chart.getPlot();
                    plot.setBackgroundPaint(Color.WHITE);
                } else if (chart.getPlot() instanceof PiePlot) {
                    PiePlot plot = (PiePlot) chart.getPlot();
                    plot.setBackgroundPaint(Color.WHITE);
                }
                break;
        }
    }

    /**
     * 设置图表轴标签字体
     */
    private void setAxisFonts(JFreeChart chart, Font font) {
        if (chart.getPlot() instanceof CategoryPlot) {
            CategoryPlot plot = (CategoryPlot) chart.getPlot();

            // 设置分类轴字体
            if (plot.getDomainAxis() != null) {
                plot.getDomainAxis().setLabelFont(font.deriveFont(12f));
                plot.getDomainAxis().setTickLabelFont(font.deriveFont(10f));
            }

            // 设置数值轴字体
            if (plot.getRangeAxis() != null) {
                plot.getRangeAxis().setLabelFont(font.deriveFont(12f));
                plot.getRangeAxis().setTickLabelFont(font.deriveFont(10f));
            }
        } else if (chart.getPlot() instanceof XYPlot) {
            XYPlot plot = (XYPlot) chart.getPlot();

            // 设置X轴字体
            if (plot.getDomainAxis() != null) {
                plot.getDomainAxis().setLabelFont(font.deriveFont(12f));
                plot.getDomainAxis().setTickLabelFont(font.deriveFont(10f));
            }

            // 设置Y轴字体
            if (plot.getRangeAxis() != null) {
                plot.getRangeAxis().setLabelFont(font.deriveFont(12f));
                plot.getRangeAxis().setTickLabelFont(font.deriveFont(10f));
            }
        } else if (chart.getPlot() instanceof PiePlot) {
            PiePlot plot = (PiePlot) chart.getPlot();
            plot.setLabelFont(font.deriveFont(10f));
        }
    }

    /**
     * 手动添加中文字体支持到SVG内容
     */
    private String addChineseFontSupport(String svgContent) {
        if (svgContent == null || svgContent.isEmpty()) {
            return svgContent;
        }

        // 定义中文字体CSS样式 - 使用更强制性的设置
        String fontStyle = "<style type=\"text/css\"><![CDATA[\n" +
                "  text {\n" +
                "    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimHei', 'SimSun', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Arial Unicode MS', sans-serif !important;\n" +
                "  }\n" +
                "  * {\n" +
                "    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimHei', 'SimSun', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Arial Unicode MS', sans-serif !important;\n" +
                "  }\n" +
                "]]></style>\n";

        // 在SVG开始标签后插入样式
        if (svgContent.contains("<svg")) {
            int insertPos = svgContent.indexOf(">", svgContent.indexOf("<svg")) + 1;
            svgContent = svgContent.substring(0, insertPos) + "\n" + fontStyle + svgContent.substring(insertPos);
        }

        // 替换所有字体引用 - 更强制性的替换
        svgContent = svgContent.replaceAll("font-family=\"[^\"]*\"",
            "font-family=\"Microsoft YaHei, PingFang SC, Hiragino Sans GB, SimHei, SimSun, Arial Unicode MS, sans-serif\"");

        // 确保SVG有正确的编码声明
        if (!svgContent.contains("encoding")) {
            svgContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" + svgContent;
        }

        // 清理可能的重复 style 属性
        svgContent = cleanupDuplicateStyles(svgContent);

        return svgContent;
    }

    /**
     * 处理SVG背景设置
     */
    private String processSVGBackground(String svgContent, ChartData chartData) {
        if (svgContent == null || svgContent.isEmpty()) {
            return svgContent;
        }

        // 检查背景颜色设置
        String backgroundColor = "white"; // 默认白色
        if (chartData.getOptions() != null && chartData.getOptions().containsKey("backgroundColor")) {
            backgroundColor = chartData.getOptions().get("backgroundColor").toString().toLowerCase();
        }

        if ("transparent".equals(backgroundColor) || "none".equals(backgroundColor)) {
            // 移除所有白色背景填充
            svgContent = svgContent.replaceAll("fill=\"rgb\\(255,255,255\\)\"", "fill=\"none\"");
            svgContent = svgContent.replaceAll("fill=\"white\"", "fill=\"none\"");
            svgContent = svgContent.replaceAll("fill=\"#ffffff\"", "fill=\"none\"");
            svgContent = svgContent.replaceAll("fill=\"#FFFFFF\"", "fill=\"none\"");

            // 移除背景矩形
            svgContent = svgContent.replaceAll("<rect[^>]*fill=\"none\"[^>]*></rect>", "");

            // 确保SVG根元素没有背景色
            if (svgContent.contains("<svg")) {
                svgContent = svgContent.replaceAll("<svg([^>]*?)style=\"([^\"]*?)background[^;]*;([^\"]*?)\"",
                    "<svg$1style=\"$2$3\"");
            }
        } else {
            // 确保白色背景
            svgContent = svgContent.replaceAll("fill=\"none\"", "fill=\"white\"");
        }

        return svgContent;
    }

    /**
     * 清理重复的 style 属性
     */
    private String cleanupDuplicateStyles(String svgContent) {
        // 移除可能重复的 style 属性
        // 匹配有多个 style 属性的元素并只保留第一个
        svgContent = svgContent.replaceAll("(style=\"[^\"]*\")([^>]*?)(style=\"[^\"]*\")", "$1$2");

        return svgContent;
    }

    /**
     * 中文字体映射器，用于SVG渲染
     */
    private static class ChineseFontMapper implements org.jfree.graphics2d.svg.FontMapper {
        @Override
        public String mapFont(String family) {
            // 将Java字体映射到SVG字体
            if (family.contains("YaHei") || family.contains("SimHei") || family.contains("SimSun")) {
                return "Microsoft YaHei, SimHei, SimSun, sans-serif";
            } else if (family.contains("PingFang") || family.contains("Hiragino")) {
                return "PingFang SC, Hiragino Sans GB, sans-serif";
            } else if (family.contains("WenQuanYi") || family.contains("Noto")) {
                return "WenQuanYi Micro Hei, Noto Sans CJK SC, sans-serif";
            }

            // 默认使用通用的中文字体回退
            return "Microsoft YaHei, PingFang SC, Hiragino Sans GB, SimHei, sans-serif";
        }
    }
}
