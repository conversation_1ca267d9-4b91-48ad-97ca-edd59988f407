package com.telecom.nrm.agent.weather;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

@Component
public class WeatherToolsService {

    public JSONObject weather_query(String city) {
        JSONObject result = new JSONObject();
        result.put("location", city);
        result.put("condition","晴");
        result.put("humidity","60%");
        return result;

    }

    public String weather_query_suggestion(String humidity) {
        return "有点冷,多穿点衣服.";

    }

    /**
     *   {
     *                 "type": "function",
     *                 "function": {
     *                     "name": "weather_query",
     *                     "description": "查询指定城市天气信息,包括：天气情况、湿度",
     *                     "parameters": {
     *                         "type": "object",
     *                         "properties": {
     *                             "location": {"type": "string"}
     *                         },
     *                         "required": ["location"]
     *                     }
     *                 }
     *             },
     * @return
     */
    public JSONObject weather_query_tool_config() {
        JSONObject tool = new JSONObject();
        tool.put("type","function");
        JSONObject function = new JSONObject();
        tool.put("function", function);
        function.put("name", "weather_query");
        function.put("description", "查询指定城市天气信息");
        JSONObject parameters = new JSONObject();
        function.put("parameters", parameters);
        parameters.put("type", "object");
        JSONObject properties = new JSONObject();
        parameters.put("properties", properties);
        properties.put("location", new JSONObject());
        properties.getJSONObject("location").put("type","string");
        parameters.put("required", new JSONArray());
        parameters.getJSONArray("required").add("location");
        return tool;
    }

    public JSONObject weather_query_suggestion_tool_config() {
        JSONObject tool = new JSONObject();
        tool.put("type","function");
        JSONObject function = new JSONObject();
        tool.put("function", function);
        function.put("name", "weather_query_suggestion");
        function.put("description", "根据城市的适度给出出行建议");
        JSONObject parameters = new JSONObject();
        function.put("parameters", parameters);
        parameters.put("type", "object");
        JSONObject properties = new JSONObject();
        parameters.put("properties", properties);
        properties.put("humidity", new JSONObject());
        properties.getJSONObject("humidity").put("type","string");
        parameters.put("required", new JSONArray());
        parameters.getJSONArray("required").add("humidity");
        return tool;
    }


    public JSONArray generateToolsConfig() {
        JSONArray tools = new JSONArray();
        tools.add(this.weather_query_tool_config());
        tools.add(this.weather_query_suggestion_tool_config());
        return tools;
    }

    public String call_tool(JSONObject action) {
        String name = action.getString("name");
        JSONObject input = action.getJSONObject("input");
        if ("weather_query".equalsIgnoreCase(name)) {
            return this.weather_query(input.getString("city")).toJSONString();
        }
        if ("weather_query_suggestion".equalsIgnoreCase(name)) {
            return this.weather_query_suggestion(input.getString("city"));
        }
        return "无法识别的工具"+name;
    }
}
