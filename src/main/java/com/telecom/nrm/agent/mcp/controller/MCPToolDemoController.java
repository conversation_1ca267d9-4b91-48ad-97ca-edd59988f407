package com.telecom.nrm.agent.mcp.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.mcp.exception.MCPToolException;
import com.telecom.nrm.agent.mcp.service.IMCPToolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * Demo controller for MCP Tool operations.
 */
@RestController
@RequestMapping("/api/mcp-demo")
public class MCPToolDemoController {

    private static final Logger logger = LoggerFactory.getLogger(MCPToolDemoController.class);

    @Autowired
    @Qualifier("customMCPToolService")
    private IMCPToolService mcpToolService;

    /**
     * Get a list of all MCP Tools.
     * @return a JSON object with the list of tools
     */
    @GetMapping("/list-tools")
    public JSONObject listTools() {
        JSONObject response = new JSONObject();
        response.put("tools", mcpToolService.getToolsAsJsonArray());
        return response;
    }

    /**
     * Demo endpoint to test the echo tool.
     * @param input the input string
     * @return a JSON object with the result
     */
    @GetMapping("/echo")
    public JSONObject echo(@RequestParam String input) {
        JSONObject response = new JSONObject();

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("input", input);

            Object result = mcpToolService.invokeTool("echo", params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error invoking echo tool", e);
            response.put("success", false);
            response.put("message", "Error invoking echo tool: " + e.getMessage());

            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());

            return response;
        }
    }

    /**
     * Demo endpoint to test the add tool.
     * @param a the first number
     * @param b the second number
     * @return a JSON object with the result
     */
    @GetMapping("/add")
    public JSONObject add(@RequestParam int a, @RequestParam int b) {
        JSONObject response = new JSONObject();

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("a", a);
            params.put("b", b);

            Object result = mcpToolService.invokeTool("add", params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error invoking add tool", e);
            response.put("success", false);
            response.put("message", "Error invoking add tool: " + e.getMessage());

            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());

            return response;
        }
    }

    /**
     * Demo endpoint to test the getItems tool.
     * @param count the number of items to return
     * @return a JSON object with the result
     */
    @GetMapping("/get-items")
    public JSONObject getItems(@RequestParam int count) {
        JSONObject response = new JSONObject();

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("count", count);

            Object result = mcpToolService.invokeTool("getItems", params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error invoking getItems tool", e);
            response.put("success", false);
            response.put("message", "Error invoking getItems tool: " + e.getMessage());

            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());

            return response;
        }
    }
}
