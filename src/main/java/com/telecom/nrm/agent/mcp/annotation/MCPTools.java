package com.telecom.nrm.agent.mcp.annotation;

import java.lang.annotation.*;

/**
 * Annotation to mark a class as an MCP Tools provider.
 * Classes annotated with @MCPTools will be scanned by the MCPToolService.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MCPTools {
    
    /**
     * Name of the MCP Tools provider.
     * @return the name of the MCP Tools provider
     */
    String name() default "";
    
    /**
     * Description of the MCP Tools provider.
     * @return the description of the MCP Tools provider
     */
    String description() default "";
}
