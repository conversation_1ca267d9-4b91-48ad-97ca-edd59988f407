package com.telecom.nrm.agent.mcp.service;

import com.alibaba.fastjson.JSONArray;
import com.telecom.nrm.agent.mcp.exception.MCPToolException;
import com.telecom.nrm.agent.mcp.model.MCPToolInfo;

import java.util.List;
import java.util.Map;

/**
 * Interface for MCP Tool Service.
 * Defines methods for managing and invoking MCP Tools.
 */
public interface IMCPToolService {
    
    /**
     * Get all MCP Tools.
     * @return a list of all MCP Tools
     */
    List<MCPToolInfo> getAllTools();
    
    /**
     * Get an MCP Tool by name.
     * @param toolName the name of the tool
     * @return the tool, or null if not found
     */
    MCPToolInfo getTool(String toolName);
    
    /**
     * Invoke an MCP Tool by name with the given parameters.
     * @param toolName the name of the tool
     * @param params the parameters to pass to the tool
     * @return the result of the tool invocation
     * @throws MCPToolException if the tool cannot be invoked
     */
    Object invokeTool(String toolName, Map<String, Object> params) throws MCPToolException;
    
    /**
     * Get a JSON array describing all MCP Tools.
     * @return a JSON array describing all MCP Tools
     */
    JSONArray getToolsAsJsonArray();
    
    /**
     * Get a JSON array describing all MCP Tools providers.
     * @return a JSON array describing all MCP Tools providers
     */
    JSONArray getProvidersAsJsonArray();
}
