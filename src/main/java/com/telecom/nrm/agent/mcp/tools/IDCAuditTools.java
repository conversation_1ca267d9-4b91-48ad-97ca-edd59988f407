package com.telecom.nrm.agent.mcp.tools;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.mcp.annotation.MCPTool;
import com.telecom.nrm.agent.mcp.annotation.MCPTools;
import com.telecom.nrm.dao.IdcAuditDao;
import org.apache.commons.codec.binary.Base64;
import org.apache.cxf.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.text.SimpleDateFormat;

/**
 * IDC Audit Tools for data center auditing operations.
 */
@Component
@MCPTools(name = "IDCAuditTools", description = "Tools for data center auditing operations")
public class IDCAuditTools {

    private static final Logger logger = LoggerFactory.getLogger(IDCAuditTools.class);

    @Autowired
    private IdcAuditDao idcAuditDao;

    // 不再需要 ChartService 依赖

    /**
     * 执行SQL查询并返回结果集
     *
     * 该方法连接PostgreSQL数据库并执行指定的SQL查询语句。
     * 查询结果将以JSONArray格式返回，每个元素代表结果集中的一行数据。
     *
     * 注意事项：
     * 1. 此方法仅支持查询操作（SELECT语句），不支持数据修改操作（INSERT、UPDATE、DELETE等）
     * 2. 为了安全起见，应避免在SQL中包含敏感信息
     * 3. SQL注入风险：确保传入的SQL已经过适当验证和清理
     * 4. 查询超时默认为30秒，大型查询可能会导致超时
     * 5. 结果集默认限制为10000行，超过此限制的结果将被截断
     *
     * 使用示例：
     * JSONArray results = sql_query("SELECT * FROM idc_servers WHERE status = 'active' LIMIT 100");
     *
     * @param sql 要执行的SQL查询语句
     * @return 包含查询结果的JSONArray，如果SQL为空或发生错误则返回空数组
     */
    @MCPTool(description = "连接PostgreSQL数据库执行SQL查询并返回结果集")
    public JSONArray sql_query(String sql) {
        if (StringUtils.isEmpty(sql)) {
            logger.warn("SQL query is empty, returning empty result");
            return new JSONArray();
        }

        logger.info("Executing SQL query: {}", sql);
        try {
            JSONObject param = new JSONObject();
            param.put("pbSql", sql);
            JSONArray result = idcAuditDao.idcDynamicQuery(param, "ds_idc_js").getJSONArray("data");
            logger.info("SQL query executed successfully, returned {} rows", result.size());
            return result;
        } catch (Exception e) {
            logger.error("Error executing SQL query: {}", e.getMessage(), e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 将图表数据对象转换为Base64编码的图表图像
     *
     * 该方法接收一个JSONObject对象，将其转换为ChartData对象，
     * 然后调用ChartService的generateChartBase64方法生成Base64编码的图表图像。
     *
     * 图表类型支持：
     * - bar: 柱状图
     * - line: 折线图
     * - pie: 饼图
     * - scatter: 散点图
     *
     * 示例JSON格式：
     * {
     *   "type": "bar",
     *   "title": "示例柱状图",
     *   "xAxisLabel": "类别",
     *   "yAxisLabel": "数值",
     *   "width": 800,
     *   "height": 600,
     *   "series": [
     *     {
     *       "name": "系列1",
     *       "data": [10, 20, 30, 40, 50],
     *       "labels": ["A", "B", "C", "D", "E"],
     *       "color": "#FF4500"
     *     },
     *     {
     *       "name": "系列2",
     *       "data": [15, 25, 35, 45, 55],
     *       "labels": ["A", "B", "C", "D", "E"],
     *       "color": "#4169E1"
     *     }
     *   ]
     * }
     *
     * @param chartData 包含图表配置和数据的JSONObject对象
     * @return Base64编码的图表图像字符串，可直接在HTML中使用
     */
    @MCPTool(description = "将图表数据对象转换为Base64编码字符串\n\n"
            + "该工具将JSON格式的图表数据直接转换为Base64编码的字符串，可用于图表生成服务的输入。\n\n"
            + "支持的图表类型：\n"
            + "- bar: 柱状图\n"
            + "- line: 折线图\n"
            + "- pie: 饼图\n"
            + "- scatter: 散点图\n\n"
            + "参数格式：\n"
            + "```json\n"
            + "{\n"
            + "  \"type\": \"bar\",  // 图表类型：bar, line, pie, scatter\n"
            + "  \"title\": \"图表标题\",  // 图表标题\n"
            + "  \"xAxisLabel\": \"X轴标签\",  // X轴标签\n"
            + "  \"yAxisLabel\": \"Y轴标签\",  // Y轴标签\n"
            + "  \"width\": 600,  // 图表宽度（像素），默认值\n"
            + "  \"height\": 400,  // 图表高度（像素），默认值\n"
            + "  \"options\": {  // 可选的高级配置\n"
            + "    \"dpi\": 300  // 图表DPI，控制清晰度，默认为72\n"
            + "  },\n"
            + "  \"series\": [  // 数据系列数组\n"
            + "    {\n"
            + "      \"name\": \"系列名称\",  // 系列名称\n"
            + "      \"data\": [10, 20, 30, 40, 50],  // 数据点数组\n"
            + "      \"labels\": [\"A\", \"B\", \"C\", \"D\", \"E\"],  // 标签数组，与数据点一一对应\n"
            + "      \"color\": \"#FF4500\"  // 可选，系列颜色，十六进制色值\n"
            + "    }\n"
            + "  ]\n"
            + "}\n"
            + "```\n\n"
            + "返回值：\n"
            + "返回一个经过URL编码的Base64字符串（等同于JavaScript的encodeURIComponent），可直接作为图表生成服务的URL参数。\n\n"
            + "使用方式：\n"
            + "1. 调用此工具生成URL编码的Base64图表数据\n"
            + "2. 将生成的字符串直接作为URL参数传递给图表生成服务\n"
            + "3. 例如：/graph-rest-api/api/chart/generate?data={encodedData}\n\n"
            + "控制图表尺寸和清晰度：\n"
            + "1. 默认生成小尺寸图表（600x400像素）\n"
            + "2. 如需更高清晰度，可设置 \"options\": { \"dpi\": 300 }\n"
            + "3. 可自定义尺寸，如 \"width\": 800, \"height\": 600\n"
            + "4. 使用PNG格式可获得更清晰的图表，但文件较大\n\n"
            + "图表样式说明：\n"
            + "1. 柱状图采用现代平面设计，没有光线和3D效果\n"
            + "2. 所有图表使用简洁清晰的设计风格\n"
            + "3. 可以通过color参数自定义柱子颜色\n\n"
            + "示例用法：\n"
            + "1. 创建一个高清晰度柱状图，用于显示各地区的销售数据\n"
            + "2. 创建一个高清晰度饼图，用于显示各产品的市场份额\n"
            + "3. 创建一个高清晰度折线图，用于显示一段时间内的趋势变化")
    public String chat_bar(JSONObject chartData) {
        if (chartData == null) {
            logger.warn("Chart data is null, returning empty result");
            return "";
        }

        logger.debug("Converting chart data to JSON: {}", chartData);

        // 直接将JSONObject序列化为Base64字符串，然后进行URL编码
        try {
            // 将JSONObject转换为JSON字符串
            String json = chartData.toJSONString();

            // 将JSON字符串转换为Base64编码
            String base64Result = java.util.Base64.getEncoder().encodeToString(json.getBytes(StandardCharsets.UTF_8));
            logger.debug("Successfully encoded chart data to Base64");

            // 对Base64编码的字符串进行URL编码（等同于JavaScript的encodeURIComponent）
            String encodedResult = URLEncoder.encode(base64Result, StandardCharsets.UTF_8.toString());
            logger.info("Successfully URL-encoded Base64 chart data");

            return encodedResult;
        } catch (UnsupportedEncodingException e) {
            logger.error("Failed to URL-encode Base64 chart data: {}", e.getMessage(), e);
            throw new RuntimeException("将Base64图表数据进行URL编码失败: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Failed to encode chart data: {}", e.getMessage(), e);
            throw new RuntimeException("将图表数据编码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据输入参数生成审计查询JSON报文
     *
     * 该方法接收各种审计查询参数，生成标准化的JSON报文格式。
     * 返回的JSON包含中英文字段名并存，便于不同系统对接和多语言界面显示。
     *
     * @param deviceIp 设备IP地址
     * @param portName 端口名称
     * @param userName 用户名称
     * @param startDate 开始时间（格式：yyyy-MM-dd HH:mm:ss 或 yyyy-MM-dd）
     * @param endDate 结束时间（格式：yyyy-MM-dd HH:mm:ss 或 yyyy-MM-dd）
     * @param regionNames 地市名称列表，多个地市用逗号分隔
     * @return 包含中英文字段名的标准化JSON报文字符串
     */
    @MCPTool(description = "根据输入参数生成审计查询JSON报文\n\n"
            + "该工具接收各种审计查询参数，生成标准化的JSON报文格式，返回包含中英文字段名并存的JSON。\n\n"
            + "参数说明：\n"
            + "- deviceIp: 设备IP地址（可选）\n"
            + "- portName: 端口名称（可选）\n"
            + "- userName: 用户名称（可选）\n"
            + "- startDate: 开始时间，支持格式 yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss（可选）\n"
            + "- endDate: 结束时间，支持格式 yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss（可选）\n"
            + "- regionNames: 地市名称，多个地市用逗号分隔，如\"南京,苏州,无锡\"（可选）\n\n"
            + "输出格式（包含中英文字段名）：\n"
            + "```json\n"
            + "{\n"
            + "  \"deviceIp\": \"*************\",\n"
            + "  \"设备IP\": \"*************\",\n"
            + "  \"portName\": \"GE0/0/1\",\n"
            + "  \"端口名称\": \"GE0/0/1\",\n"
            + "  \"userName\": \"张三\",\n"
            + "  \"用户名\": \"张三\",\n"
            + "  \"startDate\": \"2025-05-06 00:00:00\",\n"
            + "  \"开始时间\": \"2025-05-06 00:00:00\",\n"
            + "  \"endDate\": \"2025-06-06 00:00:00\",\n"
            + "  \"截至时间\": \"2025-06-06 00:00:00\",\n"
            + "  \"regionNames\": [\"南京\", \"苏州\"],\n"
            + "  \"地市\": [\"南京\", \"苏州\"],\n"
            + "  \"code\": \"0\"\n"
            + "}\n"
            + "```\n\n"
            + "特殊处理：\n"
            + "1. 空参数会被设置为空字符串\n"
            + "2. 时间格式会自动标准化为 yyyy-MM-dd HH:mm:ss\n"
            + "3. 如果未指定时间，使用默认时间范围（最近一个月）\n"
            + "4. 如果未指定地市，返回江苏省所有地市列表：[南京、镇江、苏州、常州、无锡、徐州、连云港、淮安、盐城、宿迁、泰州、扬州、南通]\n"
            + "5. 地市名称支持逗号分隔的多个值\n"
            + "6. 返回的JSON同时包含中英文字段名，值完全一致")
    public String generateAuditQueryJson(String deviceIp, String portName, String userName,
                                       String startDate, String endDate, String regionNames) {
        logger.info("Generating audit query JSON with parameters: deviceIp={}, portName={}, userName={}, startDate={}, endDate={}, regionNames={}",
                   deviceIp, portName, userName, startDate, endDate, regionNames);

        JSONObject result = new JSONObject();

        try {
            // 处理设备IP
            String finalDeviceIp = (deviceIp != null) ? deviceIp.trim() : "";
            result.put("deviceIp", finalDeviceIp);
            result.put("设备IP", finalDeviceIp);

            // 处理端口名称
            String finalPortName = (portName != null) ? portName.trim() : "";
            result.put("portName", finalPortName);
            result.put("端口名称", finalPortName);

            // 处理用户名称
            String finalUserName = (userName != null) ? userName.trim() : "";
            result.put("userName", finalUserName);
            result.put("用户名", finalUserName);

            // 处理时间范围
            Map<String, String> timeRange = processTimeRange(startDate, endDate);
            result.put("startDate", timeRange.get("startDate"));
            result.put("开始时间", timeRange.get("startDate"));
            result.put("endDate", timeRange.get("endDate"));
            result.put("截至时间", timeRange.get("endDate"));

            // 处理地市信息
            List<String> regions = processRegionNames(regionNames);
            if (ObjectUtil.isEmpty(regions)) {
                regions = Arrays.asList("南京", "镇江", "苏州", "常州", "无锡", "徐州", "连云港", "淮安", "盐城", "宿迁", "泰州", "扬州", "南通");
            }
            result.put("regionNames", regions);
            result.put("地市", new ArrayList<>(regions)); // 创建新的列表避免引用问题

            // 添加状态码
            result.put("code", "0");

            String jsonResult = result.toJSONString();
            logger.info("Successfully generated audit query JSON: {}", jsonResult);
            return jsonResult;

        } catch (Exception e) {
            logger.error("Error generating audit query JSON: {}", e.getMessage(), e);
            return generateDefaultJson();
        }
    }

    /**
     * 处理时间范围参数
     */
    private Map<String, String> processTimeRange(String startDate, String endDate) {
        Map<String, String> timeRange = new HashMap<>();
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            // 处理开始时间
            if (startDate != null && !startDate.trim().isEmpty()) {
                String normalizedStartDate = normalizeInputDate(startDate.trim());
                timeRange.put("startDate", normalizedStartDate);
            } else {
                // 默认开始时间：一个月前
                Calendar cal = Calendar.getInstance();
                cal.add(Calendar.MONTH, -1);
                timeRange.put("startDate", outputFormat.format(cal.getTime()));
            }

            // 处理结束时间
            if (endDate != null && !endDate.trim().isEmpty()) {
                String normalizedEndDate = normalizeInputDate(endDate.trim());
                timeRange.put("endDate", normalizedEndDate);
            } else {
                // 默认结束时间：当前时间
                timeRange.put("endDate", outputFormat.format(new Date()));
            }

        } catch (Exception e) {
            logger.warn("Error processing time range, using default values: {}", e.getMessage());
            // 使用默认时间范围
            Calendar cal = Calendar.getInstance();
            timeRange.put("endDate", outputFormat.format(cal.getTime()));
            cal.add(Calendar.MONTH, -1);
            timeRange.put("startDate", outputFormat.format(cal.getTime()));
        }

        return timeRange;
    }

    /**
     * 标准化输入的日期格式
     */
    private String normalizeInputDate(String dateStr) {
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            // 尝试解析完整的日期时间格式
            if (dateStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return dateStr; // 已经是标准格式
            }

            // 尝试解析日期格式，添加默认时间
            if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return dateStr + " 00:00:00";
            }

            // 处理其他可能的格式
            dateStr = dateStr.replaceAll("[年月]", "-").replaceAll("日", "");
            if (dateStr.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
                String[] parts = dateStr.split("-");
                String normalized = String.format("%s-%02d-%02d", parts[0], Integer.parseInt(parts[1]), Integer.parseInt(parts[2]));
                return normalized + " 00:00:00";
            }

        } catch (Exception e) {
            logger.warn("Error normalizing date: {}, using current time", dateStr);
        }

        // 如果无法解析，返回当前时间
        return outputFormat.format(new Date());
    }

    /**
     * 处理地市名称参数
     */
    private List<String> processRegionNames(String regionNames) {
        List<String> regions = new ArrayList<>();
        String[] allRegions = {"南京", "徐州", "无锡", "连云港", "宿迁", "苏州", "常州", "镇江", "盐城", "淮安", "扬州", "泰州", "南通"};

        if (regionNames != null && !regionNames.trim().isEmpty()) {
            // 分割地市名称（支持逗号、分号、空格等分隔符）
            String[] inputRegions = regionNames.trim().split("[,，;；\\s]+");

            for (String region : inputRegions) {
                String trimmedRegion = region.trim();
                if (!trimmedRegion.isEmpty()) {
                    // 验证地市名称是否在支持列表中
                    for (String validRegion : allRegions) {
                        if (trimmedRegion.contains(validRegion) || validRegion.contains(trimmedRegion)) {
                            if (!regions.contains(validRegion)) {
                                regions.add(validRegion);
                            }
                            break;
                        }
                    }
                }
            }
        }

        // 如果没有找到有效的地市，返回所有地市
        if (regions.isEmpty()) {
            for (String region : allRegions) {
                regions.add(region);
            }
        }

        logger.debug("Processed regions: {}", regions);
        return regions;
    }

    /**
     * 生成默认JSON
     */
    private String generateDefaultJson() {
        JSONObject defaultJson = new JSONObject();

        // 默认时间范围（最近一个月）
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String endDate = sdf.format(cal.getTime());
        cal.add(Calendar.MONTH, -1);
        String startDate = sdf.format(cal.getTime());

        // 默认地市列表
        String[] defaultRegions = {"南京", "徐州", "无锡", "连云港", "宿迁", "苏州", "常州", "镇江", "盐城"};

        defaultJson.put("deviceIp", "");
        defaultJson.put("设备IP", "");
        defaultJson.put("portName", "");
        defaultJson.put("端口名称", "");
        defaultJson.put("userName", "");
        defaultJson.put("用户名", "");
        defaultJson.put("startDate", startDate);
        defaultJson.put("开始时间", startDate);
        defaultJson.put("endDate", endDate);
        defaultJson.put("截至时间", endDate);
        defaultJson.put("regionNames", defaultRegions);
        defaultJson.put("地市", defaultRegions);
        defaultJson.put("code", "0");

        return defaultJson.toJSONString();
    }


}
