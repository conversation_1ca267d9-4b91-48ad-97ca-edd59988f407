package com.telecom.nrm.agent.mcp.tools;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.mcp.annotation.MCPTool;
import com.telecom.nrm.agent.mcp.annotation.MCPTools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Sample implementation of MCP Tools.
 */
@Component
@MCPTools(name = "SampleTools", description = "Sample MCP Tools for demonstration")
public class SampleMCPTools {
    
    private static final Logger logger = LoggerFactory.getLogger(SampleMCPTools.class);
    
    /**
     * Echo the input string.
     * @param input the input string
     * @return the input string
     */
    @MCPTool(description = "Echo the input string")
    public String echo(String input) {
        logger.info("Echo: {}", input);
        return input;
    }
    
    /**
     * Add two numbers.
     * @param a the first number
     * @param b the second number
     * @return the sum of the two numbers
     */
    @MCPTool(name = "add", description = "Add two numbers")
    public int addNumbers(int a, int b) {
        logger.info("Adding {} and {}", a, b);
        return a + b;
    }
    
    /**
     * Get a list of items.
     * @param count the number of items to return
     * @return a list of items
     */
    @MCPTool(description = "Get a list of items")
    public List<String> getItems(int count) {
        logger.info("Getting {} items", count);
        List<String> items = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            items.add("Item " + (i + 1));
        }
        return items;
    }
    
    /**
     * Process a JSON object.
     * @param jsonObject the JSON object to process
     * @return a processed JSON object
     */
    @MCPTool(description = "Process a JSON object")
    public JSONObject processJson(JSONObject jsonObject) {
        logger.info("Processing JSON: {}", jsonObject);
        JSONObject result = new JSONObject();
        result.put("processed", true);
        result.put("original", jsonObject);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * Process a JSON array.
     * @param jsonArray the JSON array to process
     * @return a processed JSON array
     */
    @MCPTool(description = "Process a JSON array")
    public JSONArray processJsonArray(JSONArray jsonArray) {
        logger.info("Processing JSON array: {}", jsonArray);
        JSONArray result = new JSONArray();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = new JSONObject();
            item.put("index", i);
            item.put("value", jsonArray.get(i));
            result.add(item);
        }
        return result;
    }
    
    /**
     * This method is not annotated with @MCPTool, so it will not be registered.
     */
    public void notAnMCPTool() {
        logger.info("This method is not an MCP Tool");
    }
}
