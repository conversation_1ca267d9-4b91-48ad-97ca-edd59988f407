package com.telecom.nrm.agent.mcp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.mcp.exception.MCPToolException;
import com.telecom.nrm.agent.mcp.service.IMCPToolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for IDC Audit Tools operations.
 */
@RestController
@RequestMapping("/api/idc-audit")
public class IDCAuditToolsController {
    
    private static final Logger logger = LoggerFactory.getLogger(IDCAuditToolsController.class);
    
    @Autowired
    @Qualifier("idcAuditAgentService")
    private IMCPToolService mcpToolService;
    
    /**
     * Get a list of all IDC Audit Tools.
     * @return a JSON array of all IDC Audit Tools
     */
    @GetMapping("/tools")
    public JSONArray getTools() {
        return mcpToolService.getToolsAsJsonArray();
    }
    
    /**
     * Audit server configuration.
     * @param serverId the server ID
     * @return a JSON object with the audit result
     */
    @GetMapping("/server-config")
    public JSONObject auditServerConfig(@RequestParam String serverId) {
        JSONObject response = new JSONObject();
        
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("serverId", serverId);
            
            Object result = mcpToolService.invokeTool("auditServerConfig", params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error auditing server configuration", e);
            response.put("success", false);
            response.put("message", "Error auditing server configuration: " + e.getMessage());
            
            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());
            
            return response;
        }
    }
    
    /**
     * Audit network configuration.
     * @param networkId the network ID
     * @return a JSON object with the audit result
     */
    @GetMapping("/network-config")
    public JSONObject auditNetworkConfig(@RequestParam String networkId) {
        JSONObject response = new JSONObject();
        
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("networkId", networkId);
            
            Object result = mcpToolService.invokeTool("auditNetworkConfig", params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error auditing network configuration", e);
            response.put("success", false);
            response.put("message", "Error auditing network configuration: " + e.getMessage());
            
            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());
            
            return response;
        }
    }
    
    /**
     * Audit data center compliance.
     * @param dataCenterId the data center ID
     * @param complianceStandard the compliance standard to audit against
     * @return a JSON object with the audit result
     */
    @GetMapping("/compliance")
    public JSONObject auditCompliance(@RequestParam String dataCenterId, 
                                     @RequestParam String complianceStandard) {
        JSONObject response = new JSONObject();
        
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dataCenterId", dataCenterId);
            params.put("complianceStandard", complianceStandard);
            
            Object result = mcpToolService.invokeTool("auditCompliance", params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error auditing compliance", e);
            response.put("success", false);
            response.put("message", "Error auditing compliance: " + e.getMessage());
            
            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());
            
            return response;
        }
    }
    
    /**
     * Generate audit report.
     * @param auditIds list of audit IDs to include in the report
     * @param format report format (json, pdf, html)
     * @return a JSON object with the report generation result
     */
    @PostMapping("/generate-report")
    public JSONObject generateAuditReport(@RequestBody List<String> auditIds, 
                                         @RequestParam String format) {
        JSONObject response = new JSONObject();
        
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("auditIds", auditIds);
            params.put("format", format);
            
            Object result = mcpToolService.invokeTool("generateAuditReport", params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error generating audit report", e);
            response.put("success", false);
            response.put("message", "Error generating audit report: " + e.getMessage());
            
            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());
            
            return response;
        }
    }
    
    /**
     * Schedule periodic audit.
     * @param resourceId the resource ID to audit
     * @param resourceType the resource type (server, network, datacenter)
     * @param schedule the schedule in cron format
     * @return a JSON object with the scheduling result
     */
    @PostMapping("/schedule")
    public JSONObject scheduleAudit(@RequestParam String resourceId, 
                                   @RequestParam String resourceType, 
                                   @RequestParam String schedule) {
        JSONObject response = new JSONObject();
        
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("resourceId", resourceId);
            params.put("resourceType", resourceType);
            params.put("schedule", schedule);
            
            Object result = mcpToolService.invokeTool("scheduleAudit", params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error scheduling audit", e);
            response.put("success", false);
            response.put("message", "Error scheduling audit: " + e.getMessage());
            
            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());
            
            return response;
        }
    }
}
