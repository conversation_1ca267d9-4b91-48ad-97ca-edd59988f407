package com.telecom.nrm.agent.mcp.service;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.Utils;
import com.telecom.nrm.agent.mcp.exception.MCPToolException;
import com.telecom.nrm.agent.mcp.tools.IDCAuditTools;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@Slf4j
@Qualifier("idcAuditAgentService")
public class IDCAuditAgentService extends AbstractMCPToolService {

    private static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");



    public String  execute(WebSocketSession session,String userMessage){
        JSONArray message = new JSONArray();
        String pt = prompt().toString();
        System.out.println("<UNK>"+pt);
        if (StringUtils.isNotEmpty(pt)) {
            message.add(generateMessage("system", prompt().toString()));
        }
        message.add(generateMessage("user", userMessage));
        JSONObject react = new JSONObject();
        for (int i = 0; i < 10; i++) {
            log.info(i +"问答===============================================================================================>");
            react = Utils.RequestR1React(message, session,true);
            if (react.containsKey("action") && StringUtils.isNotEmpty(react.getString("action"))) {
                String msg = react.getString("message");
                if (msg.contains("<answer>")) {
                    msg = msg.substring(0,msg.indexOf("<answer>"));
                }
                message.add(generateMessage("assistant", msg));
                JSONArray actions = new JSONArray();
                try{
                    actions = react.getJSONArray("action");
                }catch(Exception ex) {
                    log.warn(ex.getMessage(),ex);
                }

                JSONObject content = new JSONObject();
                content.put("content","开始按照sql执行取数...");
                JSONObject next = new JSONObject();
                next.put("content","<next>"); // 告诉页面 本次结束继续下一步分析
                try {
                    if (session != null) {
                        session.sendMessage(new TextMessage(next.toJSONString()));
                        session.sendMessage(new TextMessage(content.toJSONString()));
                    }
                } catch (IOException e) {
                    JSONObject errorResponse = new JSONObject();
                    errorResponse.put("message", "发送WebSocket消息失败");
                    errorResponse.put("error", e.getMessage());
                    System.err.println(errorResponse.toJSONString());
                }
                for (int actionIndex = 0; actionIndex < actions.size(); actionIndex++) {
                    try {
                        JSONObject actionObj = actions.getJSONObject(actionIndex);
                        String toolName = actionObj.getString("name");
                        Map<String, Object> params = new HashMap<>();
                        if (actionObj.containsKey("input")) {
                            JSONObject inputValue = actionObj.getJSONObject("input");

                            for (String key : inputValue.keySet()) {
                                params.put(key, inputValue.get(key));
                            }
                        }
                        String call = this.invokeTool(toolName, params).toString();
                        message.add(generateMessage("user", "[观察]" + actionObj.getString("code") + "调用结果:" + call));
                    } catch (MCPToolException e) {
                        JSONObject errorResponse = new JSONObject();
                        errorResponse.put("message", "工具调用失败");
                        errorResponse.put("error", e.getMessage() + "\n" + e.getStackTrace()[0]);
                        System.err.println(errorResponse.toJSONString());
                        message.add(generateMessage("user", "[观察]" + actions.getJSONObject(actionIndex).getString("code") + "调用异常:" + e.getMessage()));
                    } catch (Exception e) {
                        JSONObject errorResponse = new JSONObject();
                        errorResponse.put("message", "工具调用失败");
                        errorResponse.put("error", e.getMessage() + "\n" + e.getStackTrace()[0]);
                        System.err.println(errorResponse.toJSONString());
                        message.add(generateMessage("user", "[观察]" + actions.getJSONObject(actionIndex).getString("code") + "调用异常:" + e.getMessage()));
                    }
                }

            } else {
                break;
            }
        }
        if (session != null) {
            // 检查最终消息是否包含detail标签，如果包含则提取JSON作为askParam
            String finalMessage = react.getString("message");
            JSONObject extractResult = extractDetailFromMessage(finalMessage);

            try {
                JSONObject content = new JSONObject();
                content.put("content", "<end>");

                // 检查提取结果的code字段
                if ("0".equals(extractResult.getString("code"))) {
                    // 解析成功，直接使用extractResult作为askParam
                    content.put("askParam", extractResult.toString());
                    log.info("Extracted askParam from detail: {}", extractResult.toJSONString());
                } else {
                    // 解析失败，记录日志但不影响正常流程
                    log.debug("Detail extraction failed: {}", extractResult.getString("msg"));
                }

                session.sendMessage(new TextMessage(content.toJSONString()));

            } catch (IOException e) {
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("message", "发送WebSocket结束消息失败");
                errorResponse.put("error", e.getMessage());
                System.err.println(errorResponse.toJSONString());
            }
        }
        return react.toJSONString();

    }


    private JSONObject generateMessage(String role, String msg) {
        JSONObject message = new JSONObject();
        message.put("role", role);
        message.put("content", msg);
        return message;

    }

    /**
     * 从消息中提取detail标签内的JSON内容
     *
     * @param message 包含可能包含detail标签的消息
     * @return 返回包含所有字段的JSONObject，解析成功时返回detail中的JSON内容并确保包含code="0"，失败时返回code="-1"和msg字段
     */
    private JSONObject extractDetailFromMessage(String message) {
        if (StringUtils.isEmpty(message)) {
            JSONObject result = new JSONObject();
            result.put("code", "-1");
            result.put("msg", "消息为空");
            return result;
        }

        try {
            // 查找<detail>和</detail>标签
            int startIndex = message.indexOf("<detail>");
            int endIndex = message.indexOf("</detail>");

            if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
                // 提取detail标签之间的内容
                String detailContent = message.substring(startIndex + 8, endIndex).trim(); // 8是"<detail>"的长度

                log.debug("Found detail content: {}", detailContent);

                // 尝试解析为JSON
                if (StringUtils.isNotEmpty(detailContent)) {
                    JSONObject detailJson = JSONObject.parseObject(detailContent);
                    log.info("Successfully parsed detail JSON: {}", detailJson.toJSONString());

                    // 解析成功，确保返回的JSON包含code="0"
                    detailJson.put("code", "0");
                    return detailJson;
                } else {
                    // detail标签内容为空
                    JSONObject result = new JSONObject();
                    result.put("code", "-1");
                    result.put("msg", "detail标签内容为空");
                    return result;
                }
            } else {
                // 未找到完整的detail标签
                JSONObject result = new JSONObject();
                result.put("code", "-1");
                result.put("msg", "未找到完整的detail标签");
                return result;
            }
        } catch (Exception e) {
            log.warn("Failed to extract detail from message: {}", e.getMessage());
            log.debug("Original message: {}", message);

            // 解析失败
            JSONObject result = new JSONObject();
            result.put("code", "-1");
            result.put("msg", "JSON解析失败: " + e.getMessage());
            return result;
        }
    }

    private StringBuffer prompt() {
        try {
            // 初始化FreeMarker配置
            Configuration cfg = new Configuration();
            cfg.setClassForTemplateLoading(IDCAuditAgentService.class, "/templates");

            // 加载模板
            Template template = cfg.getTemplate("idc_audit_prompt.txt");

            // 准备数据模型
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("tools", this.getToolsAsJsonArray().toString());
            dataModel.put("currentTime", format.format(new Date()));

            // 处理模板
            Writer out = new StringWriter();
            template.process(dataModel, out);

            return new StringBuffer(out.toString());
        } catch (IOException | TemplateException e) {
            // 如果模板处理失败，返回一个错误信息
            StringBuffer errorContent = new StringBuffer();
            errorContent.append("模板加载失败，错误信息: ").append(e.getMessage());
            log.warn(e.getMessage(),e);
            return errorContent;
        }
    }


    @Override
    protected List<Class<?>> getMCPToolClass() {
        return Arrays.asList(
                IDCAuditTools.class
        );
    }
}
