package com.telecom.nrm.agent.mcp.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Class representing an MCP Tools provider.
 */
public class MCPToolsProvider {
    
    private String name;
    private String description;
    private Class<?> targetClass;
    private List<MCPToolInfo> tools;
    
    public MCPToolsProvider(String name, String description, Class<?> targetClass) {
        this.name = name;
        this.description = description;
        this.targetClass = targetClass;
        this.tools = new ArrayList<>();
    }
    
    /**
     * Add a tool to this provider.
     * @param tool the tool to add
     */
    public void addTool(MCPToolInfo tool) {
        tools.add(tool);
    }
    
    /**
     * Convert this MCPToolsProvider to a JSON-compatible Map.
     * @return a Map representation of this MCPToolsProvider
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("description", description);
        map.put("className", targetClass.getName());
        
        List<Map<String, Object>> toolMaps = new ArrayList<>();
        for (MCPToolInfo tool : tools) {
            toolMaps.add(tool.toMap());
        }
        
        map.put("tools", toolMaps);
        
        return map;
    }
    
    // Getters and setters
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Class<?> getTargetClass() {
        return targetClass;
    }
    
    public void setTargetClass(Class<?> targetClass) {
        this.targetClass = targetClass;
    }
    
    public List<MCPToolInfo> getTools() {
        return tools;
    }
    
    public void setTools(List<MCPToolInfo> tools) {
        this.tools = tools;
    }
}
