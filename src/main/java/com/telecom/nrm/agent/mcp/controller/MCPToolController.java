package com.telecom.nrm.agent.mcp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.mcp.exception.MCPToolException;
import com.telecom.nrm.agent.mcp.service.IMCPToolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;

/**
 * Controller for MCP Tool operations.
 */
@RestController
@RequestMapping("/api/mcp")
public class MCPToolController {

    private static final Logger logger = LoggerFactory.getLogger(MCPToolController.class);

    @Autowired
    @Qualifier("sampleMCPToolService")
    private IMCPToolService mcpToolService;

    /**
     * Get a list of all MCP Tools.
     * @return a JSON array of all MCP Tools
     */
    @GetMapping("/tools")
    public JSONArray getTools() {
        return mcpToolService.getToolsAsJsonArray();
    }

    /**
     * Get a list of all MCP Tools providers.
     * @return a JSON array of all MCP Tools providers
     */
    @GetMapping("/providers")
    public JSONArray getProviders() {
        return mcpToolService.getProvidersAsJsonArray();
    }

    /**
     * Invoke an MCP Tool.
     * @param toolName the name of the tool to invoke
     * @param params the parameters to pass to the tool
     * @return the result of the tool invocation
     */
    @PostMapping("/invoke/{toolName}")
    public JSONObject invokeTool(@PathVariable String toolName, @RequestBody Map<String, Object> params) {
        JSONObject response = new JSONObject();

        try {
            Object result = mcpToolService.invokeTool(toolName, params);
            response.put("success", true);
            response.put("result", result);
            return response;
        } catch (MCPToolException e) {
            logger.error("Error invoking tool: {}", toolName, e);
            response.put("success", false);
            response.put("message", "Error invoking tool: " + e.getMessage());

            // Capture full stack trace
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            response.put("error", sw.toString());

            return response;
        }
    }
}
