package com.telecom.nrm.agent;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.mcp.service.IDCAuditAgentService;
import com.telecom.nrm.agent.weather.WeatherReactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/agent")
@Slf4j
public class AgentController {

    @Autowired
    private WeatherReactService weatherReactService;

    @Autowired
    private IDCAuditAgentService portReacteSrvice;


    @PostMapping("/weather")
    public String weather(@RequestBody JSONObject params) {
        String message = params.getString("message");
        return weatherReactService.execute( message);

    }

    @PostMapping("/port")
    public String port(@RequestBody JSONObject params) {
        String message = params.getString("message");
        return portReacteSrvice.execute(null, message);

    }
}
