package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy
public interface ProductDao {
    @DaAPI(apiCode = "access_code_prod_query", version = "V20240619224011098", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> accessCodeProdQuery(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryProdByCircuit", version = "V20240807154635347", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryProdByCircuit(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
}
