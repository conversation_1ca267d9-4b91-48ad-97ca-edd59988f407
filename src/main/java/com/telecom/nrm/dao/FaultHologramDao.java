package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;

@DaClientProxy

public interface FaultHologramDao {



    //插入ak记录
    @DaAPI(apiCode = "insert_into_tb_fault_hologram_log", version = "V20240726184044997", returnTypes = {Integer.class})
    public Integer insert_into_tb_fault_hologram_log(@DaParam JSONObject param, @DaShardingCode String shardingCode);





    //批量插入群障障碍单
    @DaAPI(apiCode = "tb_zd_fault_main_bill_batch_insert", version = "V20240911102933999", returnTypes = {JSONObject.class})
    public JSONObject tb_zd_fault_main_bill_batch_insert(@DaParam JSONObject request, @DaShardingCode String shardingCode);




    //批量插入根据障碍单查到的告警
    @DaAPI(apiCode = "tb_alarm_data_all_batch_insert", version = "V20240911160552548", returnTypes = {JSONObject.class})
    public JSONObject tb_alarm_data_all_batch_insert(@DaParam JSONObject request, @DaShardingCode String shardingCode);





    //查询在途障碍单
    @DaAPI(apiCode = "query_processing_faultorder", version = "V20240911165711828", returnTypes = {JSONObject.class})
    public JSONObject query_processing_faultorder(@DaParam JSONObject request, @DaShardingCode String shardingCode);





    //批量插入群障障碍单
    @DaAPI(apiCode = "insert_into_tb_complaints", version = "V20240925111947644", returnTypes = {JSONObject.class})
    public JSONObject insert_into_tb_complaints(@DaParam JSONObject request, @DaShardingCode String shardingCode);






    //批量插入群障障碍单
    @DaAPI(apiCode = "query_zd_fault_main_bill", version = "V20241009202923153", returnTypes = {JSONObject.class})
    public JSONObject query_zd_fault_main_bill(@DaParam JSONObject request, @DaShardingCode String shardingCode);




    @DaAPI(apiCode = "query_alarm_data_all", version = "V20241012114922352", returnTypes = {JSONObject.class})
    public JSONObject query_alarm_data_all(@DaParam JSONObject request, @DaShardingCode String shardingCode);




    //批量插入群障障碍单
    @DaAPI(apiCode = "batch_insert_tb_rr_alarm_accs_nbr", version = "V20241013130911158", returnTypes = {JSONObject.class})
    public JSONObject batch_insert_tb_rr_alarm_accs_nbr(@DaParam JSONObject request, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "query_cache_tb_complaints", version = "V20241016090812071", returnTypes = {JSONObject.class})
    public JSONObject query_cache_tb_complaints(@DaParam JSONObject request, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "query_olt_position", version = "V20241111153605295", returnTypes = {JSONObject.class})
    public JSONObject query_olt_position(@DaParam JSONObject request, @DaShardingCode String shardingCode);






}
