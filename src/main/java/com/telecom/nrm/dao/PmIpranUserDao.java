package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.PmIpranTopo;
import com.telecom.nrm.domain.PmIpranUser;

@DaClientProxy
public interface PmIpranUserDao {

    @DaAPI(apiCode = "net_resource_integration.get_pm_ipran_user", version = "V20241017102628622", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getPmIpranUserInfo(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                     @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String sharSdingCode);

    @DaAPI(apiCode = "net_resource_integration.save_ipran_user",version = "V20241030163010358",returnTypes = {JSONObject.class})
    JSONObject savePmIpranUser(@DaParam PmIpranUser entity, @DaShardingCode String shardingCode);

}
