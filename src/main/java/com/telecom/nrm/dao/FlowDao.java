package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;

@DaClientProxy
public interface FlowDao {

    @DaAPI(apiCode = "pm_flow_instance_insert", version = "V20250116143148426", returnTypes = {JSONObject.class})
    public JSONObject pm_flow_instance_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_task_instance_insert", version = "V20250116144211262", returnTypes = {JSONObject.class})
    public JSONObject pm_task_instance_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_rela_flow_task_insert", version = "V20250116144413535", returnTypes = {JSONObject.class})
    public JSONObject pm_rela_flow_task_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_resource_process_insert", version = "V20250116144648029", returnTypes = {JSONObject.class})
    public JSONObject pm_resource_process_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_rela_flow_resource_insert", version = "V20250116144834287", returnTypes = {JSONObject.class})
    public JSONObject pm_rela_flow_resource_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_flow_instance_update", version = "V20250116175643670", returnTypes = {JSONObject.class})
    public JSONObject pm_flow_instance_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_flow_instance_query", version = "V20250116201800236", returnTypes = {JSONObject.class})
    public JSONObject pm_flow_instance_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_flow_resource_query", version = "V20250120124713196", returnTypes = {JSONObject.class})
    public JSONObject pm_flow_resource_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_task_instance_query", version = "V20250211202451383", returnTypes = {JSONObject.class})
    public JSONObject pm_task_instance_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_flow_instance_task_query", version = "V20250218112413843", returnTypes = {JSONObject.class})
    public JSONObject pm_flow_instance_task_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_resource_process_delete", version = "V20250121164312010", returnTypes = {Integer.class})
    public Integer pm_resource_process_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
