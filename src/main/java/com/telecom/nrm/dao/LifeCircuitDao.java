package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;

import java.util.List;

@DaClientProxy
public interface LifeCircuitDao {

    @DaAPI(apiCode = "pm_busi_user_scene_member_circuit_code_update", version = "V20240620211207211", returnTypes = {Integer.class})
    public Integer pm_busi_user_scene_member_circuit_code_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_busi_user_scene_member_service_type_name_query", version = "V20240622171104096", returnTypes = {List.class})
    public List<JSONObject> pm_busi_user_scene_member_service_type_name_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "pm_busi_user_scene_member_state_statistic_query", version = "V20240623153118185", returnTypes = {Integer.class})
    public Integer pm_busi_user_scene_member_state_statistic_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_busi_user_scene_member_statistic_his_query", version = "V20240627152107060", returnTypes = {Integer.class})
    public Integer pm_busi_user_scene_member_statistic_his_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_busi_user_scene_member_result_deal_update", version = "V20240628190337824", returnTypes = {Integer.class})
    public Integer pm_busi_user_scene_member_result_deal_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_pipesegment_white_insert", version = "V20240628220613991", returnTypes = {Integer.class})
    public Integer pm_pipesegment_white_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_pipesegment_white_delete", version = "V20240628223415992", returnTypes = {Integer.class})
    public Integer pm_pipesegment_white_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_pipesegment_white_query", version = "V20240628225837311", returnTypes = {List.class})
    public List<JSONObject> pm_pipesegment_white_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "cm_link_query", version = "V20240628093714948", returnTypes = {JSONObject.class})
    public JSONObject cm_link_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_member_note_update", version = "V20240705140836909", returnTypes = {Integer.class})
    public Integer pm_busi_user_scene_member_note_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);

}
