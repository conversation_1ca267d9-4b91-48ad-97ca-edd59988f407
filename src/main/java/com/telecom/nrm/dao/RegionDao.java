package com.telecom.nrm.dao;

import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.nrm.domain.Region;


import java.util.List;

@DaClientProxy
public interface RegionDao {


    @DaAPI(apiCode = "net_resource_integration.query_region",version = "V20220519082908833",returnTypes = {List.class, Region.class})
    public List<Region> listQuery(@DaParam Region region, @DaShardingCode String shardingCode);
}
