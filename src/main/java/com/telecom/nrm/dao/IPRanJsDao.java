package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;


@DaClientProxy
public interface IPRanJsDao {

    @DaAPI(apiCode = "getgraphnodes",version = "V20230610115411301",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodes(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphrelations",version = "V20230610140545758",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphrelations(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphlinks",version = "V20230613104231826",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphlinks(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "PSRgetgraphnodes",version = "V20230619124142729",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodesPSR(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "PSRgetgraphrelations",version = "V20230619124230974",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphrelationsPSR(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "PSRgetgraphlinks",version = "V20230619124319518",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphlinksPSR(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.query_product_link_analysis_cjtype",version = "V20250122103931336",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> queryProductLinkAnalysisCjtype(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                            @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.query_product_link_analysis_data",version = "V20250122111424410",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> queryProductLinkAnalysisData(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "queryTopoRelay",version = "V20230612174315428",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> queryTopoRelay(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphlinkrelations",version = "V20230629172040278",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphlinkrelations(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "getgraphnodesport",version = "V20230630111252523",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodesport(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "getgraphnodesdevice",version = "V20230701123412555",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodesdevice(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "getgraphnodesfacility",version = "V20230701123535471",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodesfacility(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphrelationsdevicearea",version = "V20230807124756748",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphrelationsdevicearea(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphrelationsdevicefacility",version = "V20230914164443859",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphrelationsdevicefacility(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphrelationsdevicenumber",version = "V20230807125130933",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphrelationsdevicenumber(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphnodesarea",version = "V20230807120948928",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodesarea(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                   @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphnodesservice",version = "V20230807121246728",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodesservice(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "insertgraphcoderesult",version = "V20230816110930864",returnTypes = {Object.class})
    Object recordgraphresults(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphresults",version = "V20230816142957534",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphresults(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphcodeall",version = "V20230816182939815",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphcodeall(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphcodeallLocal",version = "V20231208112157559",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphcodeallLocal(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphcodeall2",version = "V20231208114151542",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphcodeall2(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphcodeall3",version = "V20231208140324231",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphcodeall3(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "get5GCnodes",version = "V20231121195315196",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> get5GCnodes(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "get5GCrelations",version = "V20231123090847499",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> get5GCrelations(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphcodeexists",version = "V20231127152355333",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphcodeexists(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphnodesware",version = "V20240105091530631",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodesware(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphnodescable",version = "V20240226202306758",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphnodescable(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphrelationscable",version = "V20240227142829810",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphrelationscable(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getgraphlinkscable",version = "V20240227152612576",returnTypes = {PageResponse.class})
    PageResponse<JSONObject> getgraphlinkscable(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.ipran_pon_city_net_work_query", version = "V20241115145402876", returnTypes = {JSONObject.class})
    JSONObject ipranPonCityNetWorkQuery(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
