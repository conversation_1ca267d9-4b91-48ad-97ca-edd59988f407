package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;

import java.math.BigDecimal;

@DaClientProxy
public interface NetConfigDao {
    @DaAPI(apiCode = "get_ip_man_config_file",version = "V20220505104703883",returnTypes = {JSONObject.class})
    public JSONObject getNetConfigFile(@DaParam(name="id") BigDecimal id, @DaShardingCode String shardingCode) ;


}
