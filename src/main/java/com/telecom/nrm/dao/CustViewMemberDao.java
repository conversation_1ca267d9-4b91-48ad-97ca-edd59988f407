package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy
public interface CustViewMemberDao {

    @DaAPI(apiCode = "pm_busi_user_scene_member_query", version = "V20240121155915921", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryList(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_member_graph", version = "V20240121235715215", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryGraph(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_member_node_neighbor_query", version = "V20240128154553957", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryNodeNeighbors(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                   @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_member_link_neighbor_query", version = "V20240130091716281", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryLinkNeighbors(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                       @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);




    @DaAPI(apiCode = "pm_busi_user_scene_member_insert", version = "V20240121162310337", returnTypes = {Integer.class})
    public Integer save(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_member_update", version = "V20240121234102627", returnTypes = {Integer.class})
    public Integer update(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_member_delete", version = "V20240121163518662", returnTypes = {Integer.class})
    public Integer delete(@DaParam(name="id")String id, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.query_scene_city_topo", version = "V20241021130605324", returnTypes = {JSONObject.class})
    public JSONObject queryCityGraph(@DaParam JSONObject jsonObject, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_scene_city_statistic", version = "V20241021132219845", returnTypes = {JSONObject.class})
    JSONObject queryCityStatistic(Long scene_id, String shardingGraphDb);
}
