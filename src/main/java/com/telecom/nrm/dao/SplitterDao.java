package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;

/**
 * 分光器数据访问层
 * 使用@DaClientProxy注解，框架会自动生成实现类
 */
@DaClientProxy
public interface SplitterDao {
    
    /**
     * 查询分光器设备列表
     * 实际调用: obd_port_alert API
     */
    @DaAPI(apiCode = "obd_port_alert", version = "V20250731165928869", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> querySplitterDevices(
            @DaParam JSONObject param, 
            @DaParam(name = "pageSize") Integer pageSize,
            @DaParam(name = "currentPage") Integer currentPage, 
            @DaShardingCode String shardingCode
    );
    
    /**
     * 查询分光器设备统计
     * 实际调用: obd_port_alert_statistic API
     */
    @DaAPI(apiCode = "obd_port_alert_statistic", version = "V20250731165928869", returnTypes = {Integer.class})
    Integer querySplitterDeviceCount(
            @DaParam JSONObject param, 
            @DaShardingCode String shardingCode
    );
}