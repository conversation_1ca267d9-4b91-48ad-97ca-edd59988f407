package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;

/**
 * 分光器数据访问层
 * 使用@DaClientProxy注解，框架会自动生成实现类
 */
@DaClientProxy
public interface SplitterDao {
    
    /**
     * 查询分光器设备列表
     * 实际调用: obd_port_alert API
     * 注意：API返回的是数组格式，不是分页格式
     */
    @DaAPI(apiCode = "obd_port_alert", version = "V20250731165928869", returnTypes = {List.class, JSONObject.class})
    List<JSONObject> querySplitterDevices(
            @DaParam JSONObject param,
            @DaShardingCode String shardingCode
    );
}