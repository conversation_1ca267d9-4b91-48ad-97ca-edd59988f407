package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.math.BigDecimal;
import java.util.List;

@DaClientProxy
public interface CutoverDao {

    @DaAPI(apiCode = "nrm.saveCutoverOrder", version = "V20240621103210048", returnTypes = {JSONObject.class})
    public JSONObject saveCutoverOrder(@DaParam JSONObject param, @DaShardingCode String shardingCode);

//    @DaAPI(apiCode = "nrm.queryCutoverOrder", version = "V20240621155815592", returnTypes = {JSONObject.class})
//    public JSONObject queryCutoverOrder(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.queryCutoverOrder", version = "V20240621155815592", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryCutoverOrder(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.getCutoverResource", version = "V20240626163606621", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getCutoverResource(@DaParam(name = "project_id") String projectId, @DaParam(name = "pageSize") Integer pageSize,
                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.getInfluenceScopeByProjectId", version = "V20240626170206818", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getInfluenceScopeByProjectId(@DaParam(name = "batch_no") String batch_no, @DaParam(name = "pageSize") Integer pageSize,
                                                       @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getInfluenceProdIDByProjectId", version = "V20240626175401761", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getInfluenceProdIDByProjectId(@DaParam(name = "project_id") String projectId, @DaParam(name = "pageSize") Integer pageSize,
                                                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.getRelatePonByProdId", version = "V20240627094332797", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getRelatePonByProdId(@DaParam(name = "prod_ids") List prodIDS, @DaParam(name = "pageSize") Integer pageSize,
                                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.insertPrProjectService", version = "V20240626165443011", returnTypes = {List.class})
    public List insertPrProjectService(@DaParam(name = "prod_info") List param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.insert_pr_project_opt_group", version = "V20240730204052945", returnTypes = {List.class})
    public List insertPrProjectOptGroup(@DaParam(name = "list") List param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "updatePrProjectServiceRowStatus", version = "V20240626174457367", returnTypes = {Integer.class})
    public Integer updatePrProjectServiceRowStatusInvalid(@DaParam(name = "project_id") String projectId, @DaParam(name = "action") String action, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.deleteFailPonCheck", version = "V20240710171041359", returnTypes = {Integer.class})
    public Integer deleteFailPonCheck(@DaParam(name = "project_id") String projectId, @DaParam(name = "action") String action, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.delete_offline_pon_check", version = "V20240710215626628", returnTypes = {Integer.class})
    public Integer deleteOfflineDupPonCheck(@DaParam(name = "project_id") String projectId, @DaParam(name = "action") String action, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.insertPoncheckResult", version = "V20240627161724019", returnTypes = {List.class})
    public List insertPoncheckResult(@DaParam(name = "pon_check_info") List param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.insertPrProjectServiceAction", version = "V20240626171113129", returnTypes = {Integer.class})
    public Integer insertActionStatus(@DaParam JSONObject params, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.updateActionStatus", version = "V20240627142639337", returnTypes = {Integer.class})
    public Integer updateActionStatus(@DaParam(name = "batch_no") String batchNo, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "nrm.pe_project_cutover_insert", version = "V20240626083113524", returnTypes = {JSONObject.class})
    public JSONObject saveCutOverProject(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.pm_project_insert", version = "V20240626080942456", returnTypes = {JSONObject.class})
    public JSONObject saveProject(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_pe_project_cutover", version = "V20240626093014045", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryCutOverProject(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);




    @DaAPI(apiCode = "nrm.get_cutover_project", version = "V20240626143239714", returnTypes = {JSONObject.class})
    public JSONObject getCutOverProject(@DaParam(name="id") String id, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.save_project_res", version = "V20240628200035033", returnTypes = {JSONObject.class})
    public JSONObject saveProjectRes(@DaParam JSONObject param,  @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "nrm.delete_project_res", version = "V20240628224808573", returnTypes = {JSONObject.class})
    public JSONObject deleteProjectRes(@DaParam JSONObject param,  @DaShardingCode String shardingCode);




    @DaAPI(apiCode = "nrm.list_project_res", version = "V20240628204217638", returnTypes = {List.class,JSONObject.class})
    public List<JSONObject> listProjectRes(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_project_service", version = "V20240629192346120", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryProjectService(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_process_pon_check", version = "V20240706091239137", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryProcessPONCheck(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.list_success_pon_port", version = "V20240710191335420", returnTypes = {List.class,JSONObject.class})
    public List<JSONObject> listSuccessPonPort(@DaParam(name="project_id") String projectId, @DaParam(name="action") String action, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.summary_project_service", version = "V20240629214246878", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> summaryProjectService(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                          @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.summary_project_pon_check", version = "V20240702212630393", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> summaryProjectPonCheck(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.summary_process_pon_check", version = "V20240706183229203", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> summaryProcessPonCheck(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "nrm.list_project_running_action", version = "V20240630094907960", returnTypes = {List.class,JSONObject.class})
    public List<JSONObject> listProjectRunningAction(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_pr_project_route", version = "V20240722141121513", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> getInfluenceRouteByCableCode(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_pr_project_route_by_cable_net", version = "V20240723155534108", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> getInfluenceRouteByCableNetCode(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.getInfluenceRouteByOBD", version = "V20240726141756328", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> getInfluenceRouteByOBD(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                    @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.insertPrProjectRoute", version = "V20240722161324237", returnTypes = {List.class})
    public List insertPrProjectRoute(@DaParam(name = "route_info") List param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.deletePrProjectRoute", version = "V20240723145812475", returnTypes = {Integer.class})
    public Integer deletePrProjectRoute(@DaParam(name="project_id") String projectId,  @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.deletePrProjectOptGroup", version = "V20240730214611338", returnTypes = {Integer.class})
    public Integer deletePrProjectOptGroup(@DaParam(name="project_id") String projectId,  @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_pr_project_route", version = "V20240722163320116", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> queryPrProjectRoute(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_pr_project_opt_group", version = "V20240730220537391", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> queryPrProjectOptGroup(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "insert_pm_opt_road_carry_cfs", version = "V20240724115617910", returnTypes = {List.class,Integer.class})
    public List<Integer> insertPmOptRoadCarryCFS(@DaParam(name="carry_list") List<JSONObject> carryList,  @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "delete_pm_opt_road_carry_cfs", version = "V20240724124321146", returnTypes = {List.class,Integer.class})
    public List<Integer> deletePmOptRoadCarryCFS(@DaParam(name="route_list") List<String> routeList,  @DaShardingCode String shardingCode);

}
