package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.*;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.Device;
import com.telecom.nrm.domain.Fiber;
import com.telecom.nrm.domain.Port;
import com.telecom.nrm.entity.IdcAuditEmailReport;
import com.telecom.nrm.entity.PmPortNetworkOperationAudit;
import com.telecom.nrm.entity.PmProductLinkAnalysis;



@DaClientProxy
public interface OTNDao {

    @DaAPI(apiCode = "nrm.get_device_by_nm_device_code",version = "V20240320172011841",returnTypes = {Device.class})
    public Device getDeviceByNMDeviceCode(@DaParam(name="code") String code, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_port_by_nm_port_code",version = "V20240402143233956",returnTypes = { Port.class})
    public Port getPortByNMPort(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_fiber_by_port",version = "V20240320173153309",returnTypes = { Fiber.class})
    public Fiber getFiberByPort(@DaParam JSONObject param,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_fiber_by_jt_fiber",version = "V20240402144007249",returnTypes = { Fiber.class})
    public Fiber getFiberByJTFiber(@DaParam(name="code") String code,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_device_region_by_device_code",version = "V20240402144613857",returnTypes = { String.class})
    public String getDeviceRegion(@DaParam(name="code") String code,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_nmport_code_by_port_id",version = "V20240402152523224",returnTypes = { JSONObject.class})
    public JSONObject getNmportByID(@DaParam(name="port_id") String code,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.save_device_without_region",version = "V20240402145033827",returnTypes = { JSONObject.class})
    public JSONObject saveDeviceWithoutRegion(@DaParam(name="code") String code, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.update_device_port_intf",version = "V20240321144030977",returnTypes = { JSONObject.class})
    public JSONObject saveDevicePortINTF(@DaParam JSONObject param,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.check_circuit_exist",version = "V20240403102700173",returnTypes = { JSONObject.class})
    public JSONObject checkCircuitExist(@DaParam(name="line_no") String line_no,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.check_city_exist",version = "V20240403154810279",returnTypes = { JSONObject.class})
    public JSONObject checkCityExist(@DaParam(name="city") String city,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.del_device_port_intf",version = "V20240402155405682",returnTypes = { String.class})
    public String delDevicePortINTF(@DaParam(name="line_no") String line_no, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_otn_check_result",version = "V20240402200956042",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getCheckResult(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                   @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.update_pm_otn_circuits_status",version = "V20240321173700510",returnTypes = { JSONObject.class})
    public JSONObject updateOTNStatus(@DaParam(name="code") String code,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.save_per_circuit_check_res",version = "V20240410163343249",returnTypes = { JSONObject.class})
    public JSONObject savePerCircuitCheckRes(@DaParam JSONObject param,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_nm_device_res_domain",version = "V20240422164855560",returnTypes = { String.class})
    public String getDeviceResDomain(@DaParam(name="ne_id") String id,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_pm_intf_otn_kb_invalid",version = "V20240321144412862",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQueryInvalid(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                     @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_pm_intf_otn_kb",version = "V20240402183414027",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQuery(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_per_circuit_check_res",version = "V20240410164906904",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQueryPerCircuitCheckRes(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_check_result_by_city",version = "V20240408163748605",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getCheckResultByCity(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                       @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_per_circuit_check_res_info",version = "V20240415142408011",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getPerCircuitCheckRes(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    /**
     * 根据当前编码(IPPTJ...)查询完整的OTN电路编号
     * @param code
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "nrm.get_circuit_by_code",version = "V20240711101732030",returnTypes = {JSONObject.class})
    public JSONObject getCircuitByCode(@DaParam(name="code") String code, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.select_all_audit_order",version = "V20250121160514341",returnTypes = {PageResponse.class, PmProductLinkAnalysis.class})
    public PageResponse<PmProductLinkAnalysis> select_all_audit_order(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.save_batch_pm_product_link_analysis",version = "V20250121163308320",returnTypes = {JSONObject.class})
    public JSONObject save_batch_pm_product_link_analysis(@DaParam JSONObject param , @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.select_pm_product_link_analysis_by_condition",version = "V20250122101005787",returnTypes = {PageResponse.class, PmProductLinkAnalysis.class})
    public PageResponse<PmProductLinkAnalysis> select_pm_product_link_analysis_by_condition(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.export_pm_product_link_analysis_by_condition",version = "V20250721154158160",returnTypes = {PageResponse.class, PmProductLinkAnalysis.class})
    public PageResponse<PmProductLinkAnalysis> export_pm_product_link_analysis_by_condition(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                                            @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryOperationAuditInfo",version = "V20250317185716817",returnTypes = {PageResponse.class, PmPortNetworkOperationAudit.class})
    public PageResponse<PmPortNetworkOperationAudit> queryOperationAuditInfo(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "queryRiskDescrition",version = "V20250320202157118",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryRiskDescrition(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryAuditRiskDictvaluePage",version = "V20250331141226007",returnTypes = {JSONObject.class})
    public JSONObject queryAuditRiskDictvaluePage(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryRiskRuleById",version = "V20250331144402579",returnTypes = {JSONObject.class})
    public JSONObject queryRiskRuleById(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "insertAuditEmailReport",version = "V20250410102142594",returnTypes = {JSONObject.class})
    public JSONObject insertAuditEmailReport(@DaParam IdcAuditEmailReport param , @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryDispatchTimeOutCount",version = "V20250627101513589",returnTypes = {PageResponse.class, PmPortNetworkOperationAudit.class})
    public PageResponse<PmPortNetworkOperationAudit> queryDispatchTimeOutCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryReviewRiskInfo",version = "V20250627141548067",returnTypes = {PageResponse.class, PmPortNetworkOperationAudit.class})
    public PageResponse<PmPortNetworkOperationAudit> queryReviewRiskInfo(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
