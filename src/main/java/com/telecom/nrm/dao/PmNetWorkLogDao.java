package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;

import java.util.List;

@DaClientProxy
public interface PmNetWorkLogDao {

    @DaAPI(apiCode = "pm_interface_log_save",version = "V20240131200139558",returnTypes = {JSONObject.class})
     JSONObject pmInterfaceLogSave(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "audit_query_device", version = "V20240912161946151", returnTypes = {JSONObject.class})
    JSONObject auditQueryDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "audit_ip_query_device", version = "V20240912155531529", returnTypes = {JSONObject.class})
    JSONObject auditIpQueryDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "audit_devie_query_port", version = "V20240913175535384", returnTypes = {List.class})
    List<JSONObject> auditDevieQueryPort(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "audit_bc_ip_query_device", version = "V20240913174003986", returnTypes = {JSONObject.class})
    JSONObject auditBcIpQueryDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
