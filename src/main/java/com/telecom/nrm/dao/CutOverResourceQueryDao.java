package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy
public interface CutOverResourceQueryDao {

    @DaAPI(apiCode = "nrm.query_cutover_cable_segment", version = "V20240627194135934", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> queryCableSegment(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_cutover_cable", version = "V20240702095607823", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> queryCable(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_olt_pon", version = "V20240702170908994", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> queryPon(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_olt_info", version = "V20240703103040672", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> queryOLT(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_obd_info", version = "V20240725163310377", returnTypes = {PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> queryOBD(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
}
