package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

/**
 * 隐患整改DAO接口
 * 使用DataQL进行数据库操作
 */
@DaClientProxy
public interface HazardRemediationDao {

    /**
     * 创建隐患整改单
     * @param param 隐患整改单信息
     * @param shardingCode 分片代码
     * @return 创建结果，包含新生成的ID
     */
    @DaAPI(apiCode = "hazard_remediation_create", version = "V20250606162142624", returnTypes = {JSONObject.class})
    JSONObject createHazardRemediation(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询隐患整改单列表（分页）
     * @param param 查询条件
     * @param pageSize 页大小
     * @param currentPage 当前页
     * @param shardingCode 分片代码
     * @return 分页查询结果
     */
    @DaAPI(apiCode = "hazard_remediation_query", version = "V20250606162251306", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryHazardRemediationList(@DaParam JSONObject param,
                                                        @DaParam(name = "pageSize") Integer pageSize,
                                                        @DaParam(name = "currentPage") Integer currentPage,
                                                        @DaShardingCode String shardingCode);

    /**
     * 查询隐患整改单详情
     * @param param 查询参数，包含id
     * @param shardingCode 分片代码
     * @return 隐患整改单详情，包含光路和附件信息
     */
    @DaAPI(apiCode = "hazard_remediation_get", version = "V20250606162412436", returnTypes = {JSONObject.class})
    JSONObject getHazardRemediationDetail(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 更新隐患整改单
     * @param param 更新参数
     * @param shardingCode 分片代码
     * @return 更新结果
     */
    @DaAPI(apiCode = "hazard_remediation_update", version = "V20250606162508349", returnTypes = {JSONObject.class})
    JSONObject updateHazardRemediation(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 删除隐患整改单
     * @param param 删除参数，包含id
     * @param shardingCode 分片代码
     * @return 删除结果
     */
    @DaAPI(apiCode = "hazard_remediation_delete", version = "V20250606162553579", returnTypes = {JSONObject.class})
    JSONObject deleteHazardRemediation(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 批量插入隐患整改光路
     * @param param 光路信息，包含remediationId和opticalPaths数组
     * @param shardingCode 分片代码
     * @return 插入结果
     */
    @DaAPI(apiCode = "hazard_remediation_optical_path_batch_insert", version = "V20250606162633796", returnTypes = {JSONObject.class})
    JSONObject batchInsertOpticalPaths(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 删除隐患整改光路
     * @param param 删除参数，包含remediationId
     * @param shardingCode 分片代码
     * @return 删除结果
     */
    @DaAPI(apiCode = "hazard_remediation_optical_path_delete", version = "V20250608105430688", returnTypes = {JSONObject.class})
    JSONObject deleteOpticalPaths(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询隐患整改光路列表
     * @param param 查询参数，包含remediationId
     * @param shardingCode 分片代码
     * @return 光路列表
     */
    @DaAPI(apiCode = "hazard_remediation_optical_path_query", version = "V20250606162735865", returnTypes = {JSONObject.class})
    JSONObject queryOpticalPaths(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 插入隐患整改附件记录
     * @param param 附件信息
     * @param shardingCode 分片代码
     * @return 插入结果，包含新生成的ID
     */
    @DaAPI(apiCode = "hazard_remediation_attachment_insert", version = "V20250606162836980", returnTypes = {JSONObject.class})
    JSONObject insertAttachment(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询隐患整改附件列表
     * @param param 查询参数，包含remediationId
     * @param shardingCode 分片代码
     * @return 附件列表
     */
    @DaAPI(apiCode = "hazard_remediation_attachment_query", version = "V20250606173330031", returnTypes = {JSONObject.class})
    JSONObject queryAttachments(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // ==================== 风险检测相关接口 ====================

    /**
     * 保存风险检测记录
     * @param param 检测记录参数
     * @param shardingCode 分片代码
     * @return 保存结果
     */
    @DaAPI(apiCode = "hazard_remediation_risk_detection_save", version = "V20250608221208377", returnTypes = {JSONObject.class})
    JSONObject saveRiskDetection(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询风险检测记录
     * @param param 查询参数，包含remediationId
     * @param shardingCode 分片代码
     * @return 检测记录列表
     */
    @DaAPI(apiCode = "hazard_remediation_risk_detection_query", version = "V20250608221232725", returnTypes = {JSONObject.class})
    JSONObject queryRiskDetections(@DaParam JSONObject param, @DaShardingCode String shardingCode);





    /**
     * 查询检测时间线
     * @param param 查询参数，包含remediationId
     * @param shardingCode 分片代码
     * @return 时间线记录列表
     */
    @DaAPI(apiCode = "hazard_remediation_detection_timeline_query", version = "V20250608221639110", returnTypes = {JSONObject.class})
    JSONObject queryDetectionTimeline(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 更新隐患整改单检测信息
     * @param param 更新参数
     * @param shardingCode 分片代码
     * @return 更新结果
     */
    @DaAPI(apiCode = "hazard_remediation_update_detection_info", version = "V20250608221705120", returnTypes = {JSONObject.class})
    JSONObject updateDetectionInfo(@DaParam JSONObject param, @DaShardingCode String shardingCode);





    /**
     * 删除隐患整改附件记录
     * @param param 删除参数，包含id
     * @param shardingCode 分片代码
     * @return 删除结果，包含附件信息用于删除S3文件
     */
    @DaAPI(apiCode = "hazard_remediation_attachment_delete", version = "V20250606173411939", returnTypes = {JSONObject.class})
    JSONObject deleteAttachment(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 隐患整改统计查询
     * @param param 统计参数
     * @param shardingCode 分片代码
     * @return 统计结果
     */
    @DaAPI(apiCode = "hazard_remediation_statistics", version = "V20250606173459118", returnTypes = {JSONObject.class})
    JSONObject getStatistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
