package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;


@DaClientProxy
public interface UserDao {

    @DaAPI(apiCode = "nrm.query_user", version = "V20240731195142378", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> queryUsers(@DaParam(name="list") List userNames, @DaParam(name = "pageSize") Integer pageSize,
                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "query_user", version = "V20250114153635538", returnTypes = {PageResponse.class})
    public PageResponse<JSONObject> userPageQuery(@DaParam JSONObject jsonObject, @DaParam(name = "pageSize") Integer size,
                                       @DaParam(name = "currentPage") Integer page, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "query_rm_area", version = "V20241021203242674", returnTypes = {JSONObject.class})
    public JSONObject query_rm_area(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryRestInterfaceInvokeCount", version = "V20241107144001542", returnTypes = {JSONObject.class})
    public JSONObject queryRestInterfaceInvokeCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryRestDataCount", version = "V20241107120957619", returnTypes = {JSONObject.class})
    public JSONObject queryRestDataCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
