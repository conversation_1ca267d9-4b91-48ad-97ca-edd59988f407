package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONArray;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;

import java.util.Set;

public interface GraphDao {

    public Graph getNeighbors(Node node);

    public Node getNode(String id);

    public Set<Node> reloadNodes(Set<Node> nodes);

    public Set<Edge> reloadEdges(Set<Edge> edges);

    public JSONArray runGQL(String GQL);
}
