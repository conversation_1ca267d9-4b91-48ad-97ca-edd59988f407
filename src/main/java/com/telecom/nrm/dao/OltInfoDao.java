package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.QueryConditionVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@DaClientProxy
public interface OltInfoDao {

    @DaAPI(apiCode = "graph.query_res_olt_by_condition", version = "V20241104101757208", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryResOltInfo(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                    @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String sharSdingCode);


    @DaAPI(apiCode = "graph.query_res_olt_by_id", version = "V20241105144210767", returnTypes = {List.class, JSONObject.class})
    List<JSONObject> queryResOltByIds(@DaParam(name = "ids") Set<BigDecimal>  ids, @DaShardingCode String sharSdingCode);


    @DaAPI(apiCode = "graph.query_res_olt_rela_link", version = "V20241105145048257", returnTypes = {List.class, JSONObject.class})
    List<JSONObject> queryResOltRelaLink(@DaParam(name = "olt_id") BigDecimal oltId, @DaShardingCode String sharSdingCode);

    @DaAPI(apiCode = "graph.select_all_olt", version = "V20241107141809385", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> selectAllOlt(@DaParam QueryConditionVO queryConditionVO, @DaShardingCode String sharSdingCode);
}
