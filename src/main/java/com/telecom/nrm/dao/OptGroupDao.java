package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;

@DaClientProxy
public interface OptGroupDao {

    @DaAPI(apiCode = "pm_opt_road_group_query", version = "V20240625095428444", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pm_opt_road_group_query(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_result_query", version = "V20240617150533407", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pm_opt_road_group_result_query(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                            @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_risk_check_query", version = "V20240820194335044", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pm_risk_check_query(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                   @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_risk_check_his_query", version = "V20241015101244485", returnTypes = { JSONObject.class})
    public JSONObject pm_risk_check_his_query(@DaParam JSONObject param,  @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_risk_check_his_time_list_query", version = "V20241015113531235", returnTypes = {List.class})
    public List<JSONObject> pm_risk_check_his_time_list_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_element_query", version = "V20240617160223637", returnTypes = {List.class})
    public List<JSONObject> pm_opt_road_group_element_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_speciality_query", version = "V20241031085716329", returnTypes = {List.class})
    public List<JSONObject> pm_opt_road_group_speciality_query(@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_insert", version = "V20240717153110307", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_road_group_insert(@DaParam(name="list") List<JSONObject> param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_element_insert", version = "V20240717192828291", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_road_group_element_insert(@DaParam(name="list") List<JSONObject> param, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "opt_group_element_single_insert", version = "V20240821153815566", returnTypes = {JSONObject.class})
    public JSONObject opt_group_element_single_insert(@DaParam(name="list") List<JSONObject> param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_element_update", version = "V20240729211520748", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_element_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_note_update", version = "V20240718104903713", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_note_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_risk_check_insert", version = "V20240718152054209", returnTypes = {Integer.class})
    public Integer pm_risk_check_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_risk_check_his_insert", version = "V20240820182531233", returnTypes = {Integer.class})
    public Integer pm_risk_check_his_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_delete", version = "V20240718155239443", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_state_statistic_query", version = "V20240722140345183", returnTypes = {List.class,JSONObject.class})
    public List<JSONObject> pm_opt_road_group_state_statistic_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_static_query", version = "V20241101115958821", returnTypes = {List.class,JSONObject.class})
    public List<JSONObject> pm_opt_road_group_static_query(@DaParam JSONObject param,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_risk_check_confirm", version = "V20240723173255955", returnTypes = {Integer.class})
    public Integer pm_risk_check_confirm(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_element_delete", version = "V20240724142553938", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_element_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_opt_road_group_count", version = "V20240822213601908", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_count(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_risk_check_deal_update", version = "V20250207144917291", returnTypes = {Integer.class})
    public Integer pm_risk_check_deal_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 光路对比数据操作日志
     * {
     *   "staff_number": "1",//用户工号
     *   "name": "1", //模块名称
     *   "in_parameter": "1",//输入参数
     *   "result": "1"//结果
     * }
     */
    @DaAPI(apiCode = "fiber_opt_operation_log", version = "V20241023141859437", returnTypes = {Integer.class})
    public Integer fiber_opt_operation_log(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 添加光路组数据
     */
    @DaAPI(apiCode = "pm_opt_road_group_insert_v2", version = "V20241024161047430", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_insert_v2(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 根据F号查询两端设备
     * code:""
     */
    @DaAPI(apiCode = "query_az_end_odf_frame", version = "V20241107163205064", returnTypes = {List.class})
    public List<JSONObject> pm_opt_road_group_element_query_by_code(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 根据F号查询两端设备v2
     */
    @DaAPI(apiCode = "query_az_end_odf_frame_v2", version = "V20241220151012065", returnTypes = {List.class})
    public List<JSONObject> pm_opt_road_group_element_query_by_code_v2(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    /**
     * 根据 两端设备查询路径方案
     *{
     *   "beginNodeId": 1,
     *   "endNodeId": 1,
     *   "num": 5 :路径数量
     * }
     */
    @DaAPI(apiCode = "calculate_az_end_reachable_path", version = "V20241107163948458", returnTypes = {List.class})
    public List<JSONObject> query_path_scheme_by_az(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询光路保护组详情 - 单条记录查询，用于查看详情并记录日志
     *
     * @param param 查询参数（包含id）
     * @param shardingCode 分库编码
     * @return 保护组详情
     */
    @DaAPI(apiCode = "pm_opt_road_group_result_query", version = "V20240617150533407", returnTypes = {JSONObject.class})
    JSONObject queryOptGroupDetailById(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 光路组检测资源问题清单表插入API
     */
    @DaAPI(apiCode = "pm_opt_group_resource_problem_insert", version = "V20250801174639872", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_group_resource_problem_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 光路组检测资源问题清单表批量插入API
     */
    @DaAPI(apiCode = "pm_opt_group_resource_problem_batch_insert", version = "V20250801175054159", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_group_resource_problem_batch_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 光路组检测资源问题清单表查询API
     */
    @DaAPI(apiCode = "pm_opt_group_resource_problem_query", version = "V20250801174955366", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pm_opt_group_resource_problem_query(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    /**
     * 光路组检测资源问题清单表批量失效API
     */
    @DaAPI(apiCode = "pm_opt_group_resource_problem_batch_inactive", version = "V20250801180642279", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_group_resource_problem_batch_inactive(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 光路组检测资源问题清单表关联整治单API
     */
    @DaAPI(apiCode = "pm_opt_group_resource_problem_bind_rectification", version = "V20250801195337757", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_group_resource_problem_bind_rectification(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 光路组检测资源问题清单表删除API
     */
    @DaAPI(apiCode = "pm_opt_group_resource_problem_delete", version = "V20250801195536037", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_group_resource_problem_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 光路组检测资源问题清单表设置豁免API
     */
    @DaAPI(apiCode = "pm_opt_group_resource_problem_set_exemption", version = "V20250801195759859", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_group_resource_problem_set_exemption(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 光路组检测资源问题清单表更新API
     */
    @DaAPI(apiCode = "pm_opt_group_resource_problem_update", version = "V20250801195947144", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_group_resource_problem_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);

}
