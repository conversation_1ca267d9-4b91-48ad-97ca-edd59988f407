package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;

@DaClientProxy
public interface ProtCircuitCheckDao {

    @DaAPI(apiCode = "graph_pm_interface_log_insert", version = "V20240424173339169", returnTypes = {Integer.class})
    public Integer graph_pm_interface_log_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

}
