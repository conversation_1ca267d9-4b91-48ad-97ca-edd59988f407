package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy

public interface CableViewDao {





    //通过支撑设施，找到相关的支撑段的全部内容
    @DaAPI(apiCode = "cableview_room_info",version = "V20240318213654725",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> cableViewGetRoom(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    //通过支撑设施，找到相关的支撑段的全部内容
    @DaAPI(apiCode = "cableview_station_info",version = "V20240319115217031",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> cableViewGetStation(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                     @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    //通过支撑设施，找到相关的支撑段的全部内容
    @DaAPI(apiCode = "cableview_station_to_cable_sects",version = "V20240321144055710",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> cableViewGetStationCables(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);




}
