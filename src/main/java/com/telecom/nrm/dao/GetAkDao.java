package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy
public interface GetAkDao {



    //插入ak记录
    @DaAPI(apiCode = "insert_into_tb_ak", version = "V20240724095241953", returnTypes = {Integer.class})
    public Integer insert_into_tb_ak(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    //查询ak
    @DaAPI(apiCode = "query_tb_ak", version = "V20240724100646540", returnTypes = {JSONObject.class})
    public JSONObject query_tb_ak(@DaParam JSONObject param, @DaShardingCode String shardingCode);




}


