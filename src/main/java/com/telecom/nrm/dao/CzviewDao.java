package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
@DaClientProxy
public interface CzviewDao {
    @DaAPI(apiCode = "network_scale_wuxian_count", version = "V20250408162029219", returnTypes = {JSONObject.class})
    public JSONObject network_scale_wuxian_count(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "cz_rru4G_statistics", version = "V20250409093121004", returnTypes = {JSONObject.class})
    public JSONObject cz_rru4G_statistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "cz_rru5G_statistics", version = "V20250409094920736", returnTypes = {JSONObject.class})
    public JSONObject cz_rru5G_statistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "mrcoveragecount", version = "V20250409100702849", returnTypes = {JSONObject.class})
    public JSONObject mrcoveragecount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "cloud_cpu_usage_count", version = "V20250409115352409", returnTypes = {JSONObject.class})
    public JSONObject cloud_cpu_usage_count(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "cloudmemorycount", version = "V20250409140555149", returnTypes = {JSONObject.class})
    public JSONObject cloudmemorycount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "cloudstatscount", version = "V20250409144728160", returnTypes = {JSONObject.class})
    public JSONObject cloudstatscount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "cloudtopuserseach", version = "V20250409154436276", returnTypes = {JSONObject.class})
    public JSONObject cloudtopuserseach(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "IDCdataseach", version = "V20250410110227711", returnTypes = {JSONObject.class})
    public JSONObject IDCdataseach(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "czjieruywcount", version = "V20250410102257458", returnTypes = {JSONObject.class})
    public JSONObject czjieruywcount(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "czotncount", version = "V20250410101121585", returnTypes = {JSONObject.class})
    public JSONObject czotncount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "czmsapcount", version = "V20250410100734577", returnTypes = {JSONObject.class})
    public JSONObject czmsapcount(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "cywcn2dkcount", version = "V20250410092825127", returnTypes = {JSONObject.class})
    public JSONObject cywcn2dkcount(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "cyw163dkcount", version = "V20250410092331576", returnTypes = {JSONObject.class})
    public JSONObject cyw163dkcount(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "czjtotncount", version = "V20250410091758082", returnTypes = {JSONObject.class})
    public JSONObject czjtotncount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "czcsbf10gcount", version = "V20250410091456495", returnTypes = {JSONObject.class})
    public JSONObject czcsbf10gcount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "czjhjdkcount", version = "V20250410090858832", returnTypes = {JSONObject.class})
    public JSONObject czjhjdkcount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "czcsbf100gcount", version = "V20250410085252991", returnTypes = {JSONObject.class})
    public JSONObject czcsbf100gcount(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "czoltponcount", version = "V20250410083659210", returnTypes = {JSONObject.class})
    public JSONObject czoltponcount(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "czcarrieraggregationdata", version = "V20250415090944212", returnTypes = {JSONObject.class})
    public JSONObject czcarrieraggregationdata(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
