package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy
public interface ARingDao {

    //通过支撑设施，找到相关的支撑段的全部内容
    @DaAPI(apiCode = "NOC_gl_A_s_query",version = "V20240606084557476",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> NOC_gl_A_s_query(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                     @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    //通过支撑设施，找到相关的支撑段的全部内容
    @DaAPI(apiCode = "NOC_gl_A_s_all_query",version = "V20240606093424423",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> NOC_gl_A_s_all_query(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                     @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


}
