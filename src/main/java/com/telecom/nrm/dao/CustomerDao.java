package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;

@DaClientProxy
public interface CustomerDao {
    @DaAPI(apiCode = "queryCustomerByCircuit", version = "V20240808101148783", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryCustomerByCircuit(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    /**
     * 客户健康档案
     */
    @DaAPI(apiCode = "query_customer_health_record", version = "V20241122172250420", returnTypes = {PageResponse.class, JSONObject
            .class})
    PageResponse<JSONObject> queryCustomerHealthRecord(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                       @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "query_customer_health_group_record", version = "V20241212151400669", returnTypes = {PageResponse.class, JSONObject
            .class})
    PageResponse<JSONObject> queryCustomerHealthGroupRecord(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                       @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    /**
     * 客户档案新增
     */
    @DaAPI(apiCode = "insert_customer_health_record", version = "V20241125154142636", returnTypes = {Integer.class})
    public Integer saveCustviewsave(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 客户档案删除
     * @param id
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "del_customer_health_record", version = "V20241125154642308", returnTypes = {Integer.class})
    Integer deleteCustview(@DaParam(name="id")String id, @DaShardingCode String shardingCode);

    /**
     * 客户档案基础数据查询
     * @param param
     * @param shardingGraphDb
     * @return
     */
    @DaAPI(apiCode = "get_customer_health_record", version = "V20241125165308199", returnTypes = {JSONObject.class})
    JSONObject getById(@DaParam JSONObject param, @DaShardingCode String shardingGraphDb);

    /**
     * 客户档案基础数据查询2
     * @param param
     * @param shardingGraphDb
     * @return
     */
    @DaAPI(apiCode = "get_customer_health_recordv2", version = "V20241212162259466", returnTypes = {JSONObject.class})
    JSONObject getById2(@DaParam JSONObject param, @DaShardingCode String shardingGraphDb);

    /**
     * 查询客户光缆数据get_customer_cables_record
     */
    @DaAPI(apiCode = "get_customer_cables_record", version = "V20241126090147939", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> queryCustomerCable(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询客户设备资源
     */
    @DaAPI(apiCode = "get_customer_devices_record", version = "V20241126100020504", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> queryCustomerDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询客户安全信息
     */
    @DaAPI(apiCode = "get_customer_security_record", version = "V20241126100802517", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> getCustomerSecurity(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询资源光缆
     */
    @DaAPI(apiCode = "get_resource_cables_record", version = "V20241127090200192", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> getResourceCables(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询资源设备
     */
    @DaAPI(apiCode = "get_resource_devices_record", version = "V20241127090451917", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> getResourceDevices(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询安全设备总量
     */
    @DaAPI(apiCode = "get_security_device_count", version = "V20241127171100158", returnTypes = {Integer.class})
    public Integer getSecurityDeviceCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询符合双路由的安全设备
     */
    @DaAPI(apiCode = "get_security_device_double_route_count", version = "V20241127172902390", returnTypes = {Integer.class})
    public Integer getSecurityDeviceDoubleRoute(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询客户光缆列表
     */
    @DaAPI(apiCode = "get_customer_cables_list", version = "V20241203133803158", returnTypes = {List.class, JSONObject
            .class})
    public List<JSONObject> getCustomerCablesList(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询客户光缆空闲数据
     */
    @DaAPI(apiCode = "get_customer_cables_free", version = "V20241203135105797", returnTypes = {List.class, JSONObject
            .class})
    public List<JSONObject> getCustomerCablesFree(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询客户双路由保护数据
     */
    @DaAPI(apiCode = "get_customer_double_route_count", version = "V20241204175801865", returnTypes = {Integer.class})
    public Integer getCustomerDoubleRouteCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询客户光缆总算
     */
    @DaAPI(apiCode = "get_customer_cables_count", version = "V20241204180019739", returnTypes = {Integer.class})
    public Integer getCustomerCablesCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    /**
     * 搜索用户信息
     * @param jsonObject
     * @param ds
     * @return
     */
    @DaAPI(apiCode = "search_customer_list", version = "V20241230112627916", returnTypes = {List.class, JSONObject
            .class})
    List<JSONObject> searchCustomer(@DaParam JSONObject jsonObject, @DaShardingCode String ds);

    /**
     * 搜索设备信息
     * @param jsonObject
     * @param ds
     * @return
     */
    @DaAPI(apiCode = "search_device_list", version = "V20241230122600934", returnTypes = {List.class, JSONObject
            .class})
    List<JSONObject> searchDevice(@DaParam JSONObject jsonObject, @DaShardingCode String ds);

    /**
     * 查询设备信息
     * @param param
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "get_customer_devices_record2", version = "V20250106180509725", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> queryCustomerDevice2(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询业务安全拓扑图数据
     */
    @DaAPI(apiCode = "get_business_security_topology", version = "V20250107134952899", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> getBusinessSecurityTopology(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 根据设备id查询设备属性
     */
    @DaAPI(apiCode = "get_device_property_by_id", version = "V20250107162127966", returnTypes = {List.class, JSONObject.class})
    public List<JSONObject> getDevicePropertyById(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.graph_query_link_cout_by_custId", version = "V20250320175635648", returnTypes = {JSONObject.class})
    JSONObject graph_query_link_cout_by_custId(@DaParam JSONObject param, @DaShardingCode String shardingGraphDb);
}
