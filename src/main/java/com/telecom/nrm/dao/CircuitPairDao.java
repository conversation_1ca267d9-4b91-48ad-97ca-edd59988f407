package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;

@DaClientProxy
public interface CircuitPairDao {

    @DaAPI(apiCode = "pm_circuit_pair_note_update", version = "V20240821141015271", returnTypes = {Integer.class})
    public Integer pm_circuit_pair_note_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "tmp_cm_link_service_customer_1_delete", version = "V20250418145421255", returnTypes = {Integer.class})
    public Integer tmp_cm_link_service_customer_1_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);


}
