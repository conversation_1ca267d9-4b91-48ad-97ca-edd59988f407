package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.*;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.Circuit2Fiber;
import com.telecom.nrm.domain.CircuitPair2FiberPair;
import com.telecom.nrm.domain.ServiceType;

import java.util.List;


@DaClientProxy
public interface CustLinkInterfaceDao {

    @DaAPI(apiCode = "circuit_customer_addr_query", version = "V20231225000852626", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getCustLink(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "circuit_customer_addr_order_query", version = "V20240409103110518", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getCircuitsOrder(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
    @DaAPI(apiCode = "query_pm_pub_res_type", version = "V20240410154857870", returnTypes = {JSONArray.class})
    public List<ServiceType> getServiceTypeDict(@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "circuit_2_fiber_query", version = "V20240520152122499", returnTypes = {List.class,Circuit2Fiber.class})
    public List<Circuit2Fiber> getFiberByCircuit(@DaShardingCode String shardingCode, @DaParam(name = "circuit_code") String circuit_code);

    @DaAPI(apiCode = "insert_circuit_pair_2_fiber_pair", version = "V20240522164013630", returnTypes = {Integer.class})
    public Integer insertCircuitPair2FiberPair( @DaShardingCode String shardingCode,@DaParam CircuitPair2FiberPair param);

    @DaAPI(apiCode = "net_resource_integration.query_result", version = "V20240105141602606", returnTypes = {JSONArray.class})
    public JSONArray getCustLink_1(@DaParam JSONObject param,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "basic_info_query", version = "V20240107162157176", returnTypes = {JSONObject.class})
    public JSONObject getCustLink_2(@DaParam JSONObject param,   @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "basic_info_query_circuit_no", version = "V20240110085722051", returnTypes = {JSONArray.class})
    public JSONArray getCustLink_by_circuit(@DaParam JSONObject param,   @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "ct_cust_link_save", version = "V20231227141236748", returnTypes = {Integer.class})
    public Integer saveCustLink(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "circuit_pair_query_f", version = "V20240411142359006", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getCircuit_pair_f(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                    @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "circuit_pair_query", version = "V20240218145352910", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getCircuit_pair(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "circuit_pair_query_view", version = "V20240326161650804", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getCircuit_pair_view(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                    @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "shuangluyou_result_query", version = "V20240218151606778", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> shuangluyou_result_query(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                    @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "tmp_result_shuangluyou_insert", version = "V20240222112932394", returnTypes = {Integer.class})
    public Integer tmp_result_shuangluyou_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "tmp_circuits_result_trucate", version = "V20240222170650651", returnTypes = {Integer.class})
    public Integer tmp_circuits_result_trucate(@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "tmp_cm_link_service_customer_1_update", version = "V20240229103828154", returnTypes = {Integer.class})
    public Integer tmp_cm_link_service_customer_1_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_busi_user_scene_member_result_insert", version = "V20240308110212984", returnTypes = {Integer.class})
    public Integer pm_busi_user_scene_member_result_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.pm_busi_user_scene_member_result_get", version = "V20241026130521544", returnTypes = {JSONObject.class})
    public JSONObject pm_busi_user_scene_member_result_get(@DaParam(name="member_id") String id, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pair_circuit_input_insert", version = "V20240318000427831", returnTypes = {Integer.class})
    public Integer pair_circuit_input_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pair_circuit_input_insert_access_code", version = "V20250316201746233", returnTypes = {Integer.class})
    public Integer pair_circuit_input_insert_access_code(@DaParam JSONObject param,  @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "fiber_pair_m_upload_insert", version = "V20240325191418465", returnTypes = {Integer.class})
    public Integer fiber_pair_m_upload_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "fiber_pair_multi_insert", version = "V20240515144206117", returnTypes = {Integer.class})
    public Integer fiber_pair_multi_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "fiber_pair_m_upload_query", version = "V20240326100633527", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> fiber_pair_m_upload_query(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "fiber_pair_m_upload_update", version = "V20240327174547672", returnTypes = {Integer.class})
    public Integer fiber_pair_m_upload_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "fiber_pair_m_upload_delete",version = "V20240328090404511",returnTypes = { Integer.class})
    public Integer fiber_pair_m_upload_delete(@DaParam(name="id") Long id,@DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pair_circuit_input_result_corr_insert", version = "V20240329145402391", returnTypes = {Integer.class})
    public Integer pair_circuit_input_result_corr_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_member_result_corr_insert", version = "V20240329145227674", returnTypes = {Integer.class})
    public Integer pm_busi_user_scene_member_result_corr_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pair_circuit_input_result_corr_delete",version = "V20240401141541789",returnTypes = { Integer.class})
    public Integer pair_circuit_input_result_corr_delete(@DaParam JSONObject param,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_busi_user_scene_member_result_corr_delete",version = "V20240401145625462",returnTypes = { Integer.class})
    public Integer pm_busi_user_scene_member_result_corr_delete(@DaParam JSONObject param,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "fiber_pair_multi_delete",version = "V20240515145420877",returnTypes = { Integer.class})
    public Integer fiber_pair_multi_delete(@DaParam JSONObject param,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pair_circuit_input_fiber_result_corr_insert", version = "V20240401161540996", returnTypes = {Integer.class})
    public Integer pair_circuit_input_fiber_result_corr_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pair_circuit_input_fiber_result_corr_delete",version = "V20240401162524524",returnTypes = { Integer.class})
    public Integer pair_circuit_input_fiber_result_corr_delete(@DaParam JSONObject param,@DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pair_circuit_input_fiber_result_corr_query", version = "V20240401172508010", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pair_circuit_input_fiber_result_corr_query(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "cm_Link_code_query", version = "V20240506205333177", returnTypes = {JSONObject.class})
    public JSONObject cm_Link_code_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "rm_service_query", version = "V20240823111643370", returnTypes = {JSONObject.class})
    public JSONObject rm_service_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pair_circuit_input_insert_jt", version = "V20240508171731929", returnTypes = {Integer.class})
    public Integer pair_circuit_input_insert_jt(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "fiber_pair_multi_query", version = "V20240515100828605", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> fiber_pair_multi_query(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "net_resource_integration.obd_acc_num", version = "V20230728141138322", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> obd_acc_num(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "fiber_pair_multi_group_query", version = "V20240522105518734", returnTypes = {JSONObject.class})
    public JSONObject fiber_pair_multi_group_query(@DaShardingCode String shardingCode);


    @DaAPI(apiCode = "fiber_pair_multi_result_insert", version = "V20240522131448827", returnTypes = {Integer.class})
    public Integer fiber_pair_multi_result_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "code_opt_query", version = "V20240606173026395", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> code_opt_query(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryCircuitByCondition", version = "V20240806105243766", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryCircuitByCondition(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                     @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

}
