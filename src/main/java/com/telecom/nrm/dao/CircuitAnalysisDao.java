package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.entity.PmProductLinkAnalysis;
import com.telecom.nrm.entity.PmProductLinkAnalysisRule;

import java.util.List;
import java.util.Map;

@DaClientProxy
public interface CircuitAnalysisDao {

    // 批量查询资源端口(ports) {"ids":[321162060000000023013520]}
    @DaAPI(apiCode = "graph.query_res_ports_by_ids", version = "V20240923130630981", returnTypes = JSONObject.class)
    JSONObject queryResPortsByIds(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.query_res_port_by_ids", version = "V20250123141830550", returnTypes = JSONObject.class)
    JSONObject queryResPortByIds(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 批量查询资源设备详情信息(设备、列框、插槽、板卡，硬件关系)(devices,wares,deviceWareRelas)
    @DaAPI(apiCode = "graph.query_res_device_detail_by_deviceIds", version = "V20240923133241387", returnTypes = {JSONObject.class})
    JSONObject queryResDeviceDetailPortsByDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_node_room", version = "V20230814231554848", returnTypes = {JSONArray.class})
    JSONArray queryNodeRoom(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.query_res_device_facility_by_id", version = "V20241027134314961", returnTypes = {JSONObject.class})
    JSONObject queryResFacilityByDeviceId(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    // 批量查询资源设备位置信息（机房、局站、机房和局站关系）(facilitys,areas)
    @DaAPI(apiCode = "graph.query_res_stationInfos_by_deviceIds", version = "V20240923135405202", returnTypes = {JSONObject.class})
    JSONObject queryResStationInfosByDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 批量查询资源链路，包括依赖链路(返回链路实体、向上链路依赖)(links, linkRelas)
    @DaAPI(apiCode = "graph.query_res_links_by_ids", version = "V20240923144338501", returnTypes = {JSONObject.class})
    JSONObject queryResLinksByIds(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 根据光链路路由查询光路信息(光路、局向光纤、硬跳、路由和组成关系)(links,linkRelas)
    @DaAPI(apiCode = "graph.query_res_opt_rolad_by_routeIds", version = "V20240923150631925", returnTypes = {JSONObject.class})
    JSONObject queryResOptRoadByRoutes(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 根据局向光纤查询微观路由(纤芯列表、光缆段列表、光缆列表,接续列表，成端列表等)(linkCables,cables,nets,netEntitys,cableRelas,cableNodes)
    @DaAPI(apiCode = "graph.query_res_microroute_by_jxgx", version = "V20240923153503952", returnTypes = {JSONObject.class})
    JSONObject queryResMicroRouteByJxgx(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.query_res_optroad_by_enddevices", version = "V20241023230625689", returnTypes = {JSONObject.class})
    JSONObject queryResRouteByEndDevices(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_device_otn", version = "V20241024120315295", returnTypes = {JSONObject.class})
    JSONObject queryResDeviceOTN(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 查询光路
    @DaAPI(apiCode = "graph.query_road_by_route", version = "V20241027113356733", returnTypes = {JSONObject.class})
    JSONObject queryResOptRoadByRoute(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 局站
     *
     * @param param
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "graph.query_station_node", version = "V20241030130223561", returnTypes = {JSONObject.class})
    JSONObject queryStationNode(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.save_code_result", version = "V20241030132914745", returnTypes = {JSONObject.class})
    JSONObject saveCodeResult(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 查询光路
    @DaAPI(apiCode = "graph.query_res_route_by_optroad", version = "V20240927220610176", returnTypes = {JSONObject.class})
    JSONObject queryResRouteByOptRoad(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 批量保存设备组成信息到网资（设备、列框、插槽、板卡，硬件关系、端口）（devices,wares,deviceWareRelas,ports）
    @DaAPI(apiCode = "graph.save_net_devices", version = "V20240923165408511", returnTypes = {JSONObject.class})
    JSONObject saveNetDevices(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 批量保存（链路、链路依赖关系）到网资源(links,linkRelas)
    @DaAPI(apiCode = "graph.save_net_links", version = "V20240923174813303", returnTypes = {JSONObject.class})
    JSONObject saveNetLinks(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //  批量保存缆线信息(纤芯列表、光缆段列表,缆线关系、接续信息、成段信息)(linkCables,cables,nets,netEntitys,cableRelas,cableNodes)
    @DaAPI(apiCode = "graph.save_net_cables", version = "V20240923180321368", returnTypes = {JSONObject.class})
    JSONObject saveNetCables(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // 批量保存位置信息（机房、局站、）到网资源(facilitys,areas)
    @DaAPI(apiCode = "graph.save_net_stationInfos", version = "V20240923182331967", returnTypes = {JSONObject.class})
    JSONObject saveNetStationInfos(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.save_cjtype", version = "V20241018204308348", returnTypes = {JSONObject.class})
    JSONObject saveCjType(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.add_code_results", version = "V20250615233327529", returnTypes = {JSONObject.class})
    JSONObject addCodeResults(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "circuit_analyses_report", version = "V20240925140919177", returnTypes = {JSONObject.class})
    JSONObject circuitAnalysesReport(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //查询波道配置表
    @DaAPI(apiCode = "graph.query_bd_config_by_param", version = "V20240925143701807", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryBdConfigByParam(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    //查询OTN设备信息
    @DaAPI(apiCode = "graph.query_trans_ne_by_param", version = "V20240925094432667", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryTransNeByParam(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    //查询OTN端口信息
    @DaAPI(apiCode = "graph.query_trans_port_ptp_by_param", version = "V20240925152457780", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryTransPortPtpByParam(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    //查询BC库设备信息
    @DaAPI(apiCode = "graph.query_bc_device_by_param", version = "V20240925162309728", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryBcDeviceByParam(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    //查询BC库端口信息
    @DaAPI(apiCode = "graph.query_bc_port_by_param", version = "V20240925162536778", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryBcPortByParam(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                       @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_upperlink_by_olt", version = "V20241010124249070", returnTypes = {JSONObject.class})
    JSONObject queryUpperlinkByOlt(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_number_by_deviceIds", version = "V20241010124959198", returnTypes = {JSONObject.class})
    JSONObject queryResNumberByDeviceIds(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_service_by_circuit", version = "V20241105231800930", returnTypes = {JSONObject.class})
    JSONObject queryResServiceByCircuit(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_service_by_circuitId", version = "V20240924125717822", returnTypes = {JSONObject.class})
    JSONObject queryResServiceByCircuitId(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.save_net_numbers_by_device", version = "V20241010130402922", returnTypes = {JSONObject.class})
    JSONObject saveNetNumbersByDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_service_Info", version = "V20241011170054181", returnTypes = {JSONObject.class})
    JSONObject queryResServiceInfo(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_all_link_by_code", version = "V20241106120151433", returnTypes = {JSONObject.class})
    JSONObject queryResAllLinkByCode(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_all_link_by_top_id", version = "V20250123100603961", returnTypes = {JSONObject.class})
    JSONObject queryResAllLinkByTopId(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.save_net_service_Info", version = "V20241011171437437", returnTypes = {JSONObject.class})
    JSONObject saveNetServiceInfo(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.save_net_ipran_view_cache", version = "V20241018161719563", returnTypes = {JSONObject.class})
    JSONObject saveIpranViewCache(@DaParam Map param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.save_clear_access_code_cache", version = "V20241026235608870", returnTypes = {JSONObject.class})
    JSONObject clearAccessCodeCache(@DaParam Map param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.query_cache_by_access_code", version = "V20241018162650872", returnTypes = {JSONObject.class})
    JSONObject queryCacheByAccessCode(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.get_circuit_cjtype", version = "V20241023211830105", returnTypes = {JSONObject.class})
    JSONObject queryCjtype(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.group_check_circuit_query_station", version = "V20241025140807647", returnTypes = {JSONObject.class})
    JSONObject queryStationByCircuitCode(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.save_sync_alarm", version = "V20241026131200731", returnTypes = {JSONObject.class})
    void saveSyncAlarm(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.save_net_fault", version = "V20241026173526710", returnTypes = {JSONObject.class})
    void saveNetFault(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_pm_interface_log", version = "V20241025174230040", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryPmInterfaceLog(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.queryAlarmInfoByDeviceCode", version = "V20241026163456823", returnTypes = {JSONObject.class})
    JSONObject queryAlarmInfoByDeviceCode(@DaParam Map<String, String> param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.cust_fault_query", version = "V20241027160816779", returnTypes = {JSONObject.class})
    JSONObject custFaultQuery(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.queryAlarmInfoByIp", version = "V20241215180423602", returnTypes = {JSONObject.class})
    JSONObject queryAlarmInfoByIp(@DaParam Map<String, String> param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_device_by_deviceIds", version = "V20250123122833121", returnTypes = {JSONObject.class})
    JSONObject queryResDeviceByIds(@DaParam JSONObject param, @DaShardingCode String bcShardingCode);


    @DaAPI(apiCode = "graph.query_res_room_by_ids", version = "V20250123150006846", returnTypes = {JSONObject.class})
    JSONObject queryResRoomsByIds(@DaParam JSONObject param, @DaShardingCode String bcShardingCode);

    @DaAPI(apiCode = "graph.query_res_tml_by_ids", version = "V20250123150655925", returnTypes = {JSONObject.class})
    JSONObject queryResTmlByIds(@DaParam JSONObject param, @DaShardingCode String bcShardingCode);

    @DaAPI(apiCode = "query_res_ip_by_deviceIds", version = "V20250124000201755", returnTypes = {JSONObject.class})
    JSONObject queryResIpByDeviceIds(@DaParam JSONObject param, @DaShardingCode String bcShardingCode);

    @DaAPI(apiCode = "graph.query_res_cfs_by_id", version = "V20250123151532745", returnTypes = {JSONObject.class})
    JSONObject queryCfsByServiceId(@DaParam JSONObject param, @DaShardingCode String bcShardingCode);

    @DaAPI(apiCode = "graph.query_res_address_by_ids", version = "V20250123152152957", returnTypes = {JSONObject.class})
    JSONObject queryResAddressByIds(@DaParam JSONObject param, @DaShardingCode String bcShardingCode);


    @DaAPI(apiCode = "graph.save_link_analisys_data", version = "V20250123173317116", returnTypes = {JSONObject.class})
    JSONObject saveLinkAnalysisData(@DaParam PmProductLinkAnalysis param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.save_link_analisys_province_success_date", version = "V20250415225111977", returnTypes = {JSONObject.class})
    JSONObject saveProvinceSuccessCheckDate(@DaParam PmProductLinkAnalysis param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.save_link_analisys_basic_success_date", version = "V20250415225324340", returnTypes = {JSONObject.class})
    JSONObject saveBasicSuccessCheckDate(@DaParam PmProductLinkAnalysis param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph.query_res_device_by_ip", version = "V20250123232547331", returnTypes = {JSONObject.class})
    JSONObject queryDeviceByIp(@DaParam JSONObject param, @DaShardingCode String bcShardingCode);

    @DaAPI(apiCode = "graph.refresh_link_analisys_data", version = "V20250330004943715", returnTypes = {JSONObject.class})
    JSONObject refreshLinkAnalysisData(@DaParam PmProductLinkAnalysis param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.select_analysis_product_info_by_accesscde", version = "V20250330004036681", returnTypes = {PageResponse.class, PmProductLinkAnalysis.class})
    PageResponse<PmProductLinkAnalysis>  selectAnalysisInfoByAccessCode(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph.reset_audit_check_state", version = "V20250408144404488", returnTypes = {JSONObject.class})
    JSONObject reset_audit_check_state(@DaParam JSONObject param, @DaShardingCode String bcShardingCode);

    @DaAPI(apiCode = "graph.select_analysis_rule_by_prod_cjtype", version = "V20250424143315906", returnTypes = {PageResponse.class, PmProductLinkAnalysisRule.class})
    PageResponse<PmProductLinkAnalysisRule>  selectAnalysisRuleByProductCjtype(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph_query_cjtype_by_code_region",version = "V20250609084004415",returnTypes = {PageResponse.class,JSONObject.class})
    PageResponse<JSONObject> graph_query_cjtype_by_code_region(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "graph_query_cjtype_by_type_region",version = "V20250609085336245",returnTypes = {PageResponse.class,JSONObject.class})
    PageResponse<JSONObject> graph_query_cjtype_by_type_region(@DaParam JSONObject param,@DaParam(name = "pageSize") Integer pageSize,
                                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "graph_query_results_by_type_region",version = "V20250609085810817",returnTypes = {PageResponse.class,JSONObject.class})
    PageResponse<JSONObject> graph_query_results_by_type_region(@DaParam JSONObject param,@DaParam(name = "pageSize") Integer pageSize,
                                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



}
