package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.PortAbnormalRateDto;
import com.telecom.nrm.domain.PortAbnormalTrafficDto;
import com.telecom.nrm.entity.PortRateOverrunDto;

import java.util.List;

@DaClientProxy
public interface IdcAuditDao {

    @DaAPI(apiCode = "qryIdcAuditStatistics",version = "V20250317215524021",returnTypes = {JSONObject.class})
    public JSONObject qryIdcAuditStatistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "queryOperationAuditContent",version = "V20250331150136216",returnTypes = {JSONObject.class})
    public JSONObject queryOperationAuditContent(@DaParam JSONObject param,  @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "queryExistsRegions",version = "V20250408094526590",returnTypes = {JSONObject.class})
    public JSONObject queryExistsRegions(@DaParam JSONObject param,  @DaShardingCode String shardingCode);

    // 查询无风险的数量
    @DaAPI(apiCode = "queryOperationAuditNoRiskCount",version = "V20250407112516004",returnTypes = {JSONObject.class})
    public JSONObject queryOperationAuditNoRiskCount(@DaParam JSONObject param,  @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "queryOperationExistsCitys",version = "V20250331165559458",returnTypes = {JSONObject.class})
    public JSONObject queryOperationExistsCitys(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryAllOperationAuditCount",version = "V20250410173514888",returnTypes = {JSONObject.class})
    public JSONObject queryAllOperationAuditCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryIDCReviewOperationAuditCount",version = "V20250410184903183",returnTypes = {JSONObject.class})
    public JSONObject queryIDCReviewOperationAuditCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryIDCPortAbnormalTrafficCount",version = "V20250416195644384",returnTypes = {JSONObject.class})
    public JSONObject queryIDCPortAbnormalTrafficCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryIDCPortAbnormalRateCount",version = "V20250416202434936",returnTypes = {JSONObject.class})
    public JSONObject queryIDCPortAbnormalRateCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getPortAbnormalTraffic",version = "V20250417141311002",returnTypes = { PageResponse.class, PortAbnormalTrafficDto.class})
    public PageResponse<PortAbnormalTrafficDto> getPortAbnormalTraffic(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getPortRateAbnormalTraffic",version = "V20250417151722037",returnTypes = { PageResponse.class, PortAbnormalRateDto.class})
    public PageResponse<PortAbnormalRateDto> getPortRateAbnormalTraffic(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "idc_query_dynamic_sql",version = "V20250513093228083",returnTypes = {JSONObject.class})
    public JSONObject idcDynamicQuery(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "qry_idc_whole_config",version = "V20250623141041402",returnTypes = {JSONObject.class})
    public JSONObject qryIDCWholeConfig(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "qry_idc_network_device",version = "V20250625181853787",returnTypes = {JSONObject.class})
    public JSONObject qryIDCNetworkDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "delete_pm_device_rate_template_by_device_ip",version = "V20250626093000672",returnTypes = {JSONObject.class})
    public void deletePmDeviceRateTemplateByDeviceIp(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "insert_pm_device_rate_template",version = "V20250626104427997",returnTypes = {JSONObject.class})
    public void insertPmDeviceRateTemplate(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "delete_pm_gather_port_by_device_ip",version = "V20250627111442726",returnTypes = {JSONObject.class})
    public void deletePmGatherPortByDeviceIp(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "insert_pm_gather_port",version = "V20250627112145091",returnTypes = {JSONObject.class})
    public void insertPmGatherPort(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "insert_pm_booksize",version = "V20250707161928504",returnTypes = {JSONObject.class})
    public void insertPmBooksize(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "insert_init_gather_port",version = "V20250711143919641",returnTypes = {JSONObject.class})
    public void insertInitGatherPort(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "qry_idc_need_analysis_device",version = "V20250711161226153",returnTypes = {JSONObject.class})
    public JSONObject qryIdcNeedAnalysisDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "update_init_gather_port",version = "V20250711165506364",returnTypes = {JSONObject.class})
    public void updateInitGatherPort(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "insert_pm_port_gather_lock",version = "V20250714185641339",returnTypes = {JSONObject.class})
    public void insertPmPortGatherLock(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "qry_pm_port_gather_lock",version = "V20250714190006335",returnTypes = {JSONObject.class})
    public JSONObject qryPmPortGatherLock(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryIDCPortRateOverrunAuditInfo",version = "V20250721152435899",returnTypes = { PageResponse.class, PortRateOverrunDto.class})
    public PageResponse<PortRateOverrunDto> queryIDCPortRateOverrunAuditInfo(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "batchInsertPmPortRateOverrun",version = "V20250721175309709",returnTypes = {JSONObject.class})
    public void batchInsertPmPortRateOverrun(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryIDCPortRateOverrunCount",version = "V20250722165851437",returnTypes = {JSONObject.class})
    public JSONObject queryIDCPortRateOverrunCount(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "getPortRateOverrunInfo",version = "V20250722175530068",returnTypes = { PageResponse.class, PortAbnormalRateDto.class})
    public PageResponse<PortAbnormalRateDto> getPortRateOverrunInfo(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
