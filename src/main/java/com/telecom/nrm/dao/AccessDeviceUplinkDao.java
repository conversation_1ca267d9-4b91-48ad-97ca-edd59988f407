package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;

@DaClientProxy
public interface AccessDeviceUplinkDao {

//    @DaAPI(apiCode = "accessdevice_uplink_optroute", version = "V20240717172831390", returnTypes = {PageResponse.class, JSONObject.class})
//    public PageResponse<JSONObject> getInfo(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
//                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
//
//    @DaAPI(apiCode = "net_resource_integration.query_region",version = "V20220519082908833",returnTypes = {List.class, Region.class})
//    public List<Region> listQuery(@DaParam Region region, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_query_device", version = "V20240731095131356", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pm_opt_road_group_query_device(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                            @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_result_query_device", version = "V20240731095859964", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pm_opt_road_group_result_query_device(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                   @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
//以下四个没改
    @DaAPI(apiCode = "pm_opt_road_group_element_query_details", version = "V20240902145131828", returnTypes = {List.class})
    public List<JSONObject> pm_opt_road_group_element_query_details(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_insert", version = "V20240717153110307", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_road_group_insert(@DaParam(name="list") List<JSONObject> param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_element_insert", version = "V20240717192828291", returnTypes = {JSONObject.class})
    public JSONObject pm_opt_road_group_element_insert(@DaParam(name="list") List<JSONObject> param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_element_update", version = "V20240729211520748", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_element_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);
//以上四个没改
    @DaAPI(apiCode = "pm_opt_road_group_note_update_device", version = "V20240731100801632", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_note_update_device(@DaParam JSONObject param, @DaShardingCode String shardingCode);
//以下五个没改
    @DaAPI(apiCode = "pm_risk_check_insert", version = "V20240718152054209", returnTypes = {Integer.class})
    public Integer pm_risk_check_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_delete", version = "V20240718155239443", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_state_statistic_query_device", version = "V20240801141733830", returnTypes = {List.class,JSONObject.class})
    public List<JSONObject> pm_opt_road_group_state_statistic_query_device(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_risk_check_confirm", version = "V20240723173255955", returnTypes = {Integer.class})
    public Integer pm_risk_check_confirm(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_element_delete", version = "V20240724142553938", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_element_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_opt_road_group_count_device", version = "V20240913154233887", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_count_device(@DaParam JSONObject param, @DaShardingCode String shardingCode);


}
