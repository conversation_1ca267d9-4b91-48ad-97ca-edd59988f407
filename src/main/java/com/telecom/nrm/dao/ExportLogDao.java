package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

/**
 * 导出日志DAO
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@DaClientProxy
public interface ExportLogDao {
    
    /**
     * 保存导出日志
     *
     * @param param 日志参数
     * @param shardingCode 分库编码
     * @return 影响行数
     */
    @DaAPI(apiCode = "insert_yuanfeng_file_export_log", version = "V20250708111728469", returnTypes = {Integer.class})
    Integer saveExportLog(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询导出日志
     *
     * @param param 查询参数
     * @param pageSize 页大小
     * @param currentPage 当前页
     * @param shardingCode 分库编码
     * @return 查询结果
     */
    @DaAPI(apiCode = "query_yuanfeng_file_export_log", version = "V20250708112051873", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryExportLog(@DaParam JSONObject param,
                                           @DaParam(name = "pageSize") Integer pageSize,
                                           @DaParam(name = "currentPage") Integer currentPage,
                                           @DaShardingCode String shardingCode);
}
