package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;


@DaClientProxy
public interface CableSectionDao {

    /**
     * 光缆段查产品电路
     * @param param
     * @param
     * @param
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "sect_prod_circuit_query",version = "V20240619133608914",returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> sectProdCircuitQuery(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryCircuitByCable",version = "V20240807090923707",returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryCircuitByCable(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    //route   //  id
    @DaAPI(apiCode = "nrm.graph_query_route_by_cable_id", version = "V20250312144042747", returnTypes = {JSONObject.class})
    JSONObject graph_query_route_by_cable_id(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //   exist       // linkIds
    @DaAPI(apiCode = "nrm.graph_check_links_exist_rr_service_entity", version = "V20250312144824937", returnTypes = {JSONObject.class})
    JSONObject graph_check_links_exist_rr_service_entity(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    // upperLinks   //   lowerLinkIds
    @DaAPI(apiCode = "nrm.graph_query_upper_links_by_lower_link_ids", version = "V20250312145416892", returnTypes = {JSONObject.class})
    JSONObject graph_query_upper_links_by_lower_link_ids(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.graph_query_cable_by_condition",version = "V20250321112921760",returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> graph_query_cable_by_condition(@DaParam JSONObject param, @DaShardingCode String shardingCode);


}
