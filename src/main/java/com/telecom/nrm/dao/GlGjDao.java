package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy
public interface GlGjDao {




    @DaAPI(apiCode = "gl_gj_fiber_pair_m_query", version = "V20240320164717884", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> gl_gj_fiber_pair_m_query(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "gl_gj_fiber_pair_m_insert", version = "V20240320165023351", returnTypes = {Integer.class})
    public Integer gl_gj_fiber_pair_m_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

}
