package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy
public interface CircuitDetailDao {

    /**
     * 链路id查详情包含客户
     * @param param
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "circuit_detail_query", version = "V20240619224404788", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> circuitDetailQuery(@DaParam JSONObject param, @DaShardingCode String shardingCode);

}
