package com.telecom.nrm.dao;

import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy

public interface GroupFaultRapidPositioningDao {



    @DaAPI(apiCode = "Group_fault_rapid_positioning_interface",version = "V20240111184045269",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQueryOltObd(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                     @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "Group_fault_cbl_sect_by_phy_port",version = "V20240115105116074",returnTypes = { JSONObject.class})
    public JSONObject getSectByPhyPort(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "Group_fault_get_accs_by_segments",version = "V20240119152028899",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getAllAccCodesByFiberCableSegments(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                       @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    //通过接入号，从oLT到底层支撑段全都查出来
    @DaAPI(apiCode = "Group_fault_rapid_positioning_interfaceV2",version = "V20240118143414570",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQueryFromOltToBaseSect(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                    @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);




    //查资源的IP对应的网管的IP是多少
    @DaAPI(apiCode = "net_resource_integration.pagequery_pon_manage_ip", version = "V20240228192822943", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQueryPONManageIP(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String sharSdingCode);








    //查询资源的IP对应的厂商
    @DaAPI(apiCode = "nrm.query_pon_manufactor", version = "V20240813223939134", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQueryPONManuFactor(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String sharSdingCode);





    //通过支撑设施，找到相关的支撑段的全部内容
    @DaAPI(apiCode = "bse_eqp_to_bse_sect",version = "V20240312215155111",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQueryBseEqptoBseSect(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                               @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);





    @DaAPI(apiCode = "get_accs_code_by_port_id",version = "V20240407110201502",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject>  pageQueryGetAccsCode(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                             @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);




    //生成群障
    @DaAPI(apiCode = "tb_comm_cause_insert", version = "V20240410113746988", returnTypes = {Integer.class})
    public Integer tb_comm_cause_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //生成群障关联的号码

    @DaAPI(apiCode = "tb_cause_number_batch_insert", version = "V20240410144949900", returnTypes = {JSONObject.class})
    public JSONObject tb_cause_number_batch_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);





    @DaAPI(apiCode = "queryCause",version = "V20240411114110862",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject>  pageQueryqueryCause(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                          @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);




    @DaAPI(apiCode = "get_start_end_point_info",version = "V20240522200200264",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject>  get_start_end_point_info(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "get_bse_sect_info_by_cbl_sect",version = "V20240522202501523",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject>  get_bse_sect_info_by_cbl_sect(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "updatetb_comm_cause_isrecoveried",version = "V20240415212446017",returnTypes = { JSONObject.class})
    public JSONObject updatetb_comm_cause_isrecoveried(@DaParam(name="causeid") String code,@DaShardingCode String shardingCode);




    //生成群障查询记录
    @DaAPI(apiCode = "tb_groupfault_log_insert", version = "V20240709210303889", returnTypes = {Integer.class})
    public Integer tb_groupfault_log_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);



    //根据申告汇聚的接入号信息，查询OLT、光路、局站、机房等信息
    @DaAPI(apiCode = "query_oltip_by_accs_nbr_no",version = "V20240910162417160",returnTypes = {JSONObject.class})
    public JSONObject query_oltip_by_accs_nbr_no(@DaParam JSONObject param, @DaShardingCode String shardingCode);





    //查障碍保存位置
    @DaAPI(apiCode = "query_tb_groupfault_log_postion",version = "V20241017142729619",returnTypes = {JSONObject.class})
    public JSONObject query_tb_groupfault_log_postion(@DaParam JSONObject param, @DaShardingCode String shardingCode);




    // query_prodInfo_by_cblsect  V20250616122717377  ds_odso_nt
    @DaAPI(apiCode = "query_prodInfo_by_cblsect",version = "V20250616122717377",returnTypes = {JSONObject.class})
    public JSONObject query_prodInfo_by_cblsect(@DaParam JSONObject param, @DaShardingCode String shardingCode);



}
