package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

/**
 * 双路由光路检测运营统计数据访问接口
 * 
 * <AUTHOR> Generated
 * @date 2025-06-16
 */
@DaClientProxy
public interface DualRouteStatisticsDao {

    /**
     * 双路由月度运营统计查询
     * 查询指定时间范围内的月度运营统计数据，包括整改数量、确认数量、变好数量、变差数量等指标
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (可选，默认最近3个月)
     *              - endDate: 结束时间 (可选，默认当前时间)
     *              - areaCode: 地区代码 (可选)
     * @param shardingCode 分库代码
     * @return 月度统计数据，包含汇总信息、月度明细、状态分布、地区统计等
     */
    @DaAPI(apiCode = "dual_route_monthly_statistics", version = "V20250617090933641", returnTypes = {JSONObject.class})
    JSONObject getDualRouteMonthlyStatistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 双路由历史趋势分析查询
     * 基于历史数据分析检测频次、状态变化趋势、问题持续时间等指标
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (可选，默认最近6个月)
     *              - endDate: 结束时间 (可选，默认当前时间)
     *              - areaCode: 地区代码 (可选)
     * @param shardingCode 分库代码
     * @return 趋势分析数据，包含月度趋势、状态变化趋势、问题持续时间分析、风险对象识别等
     */
    @DaAPI(apiCode = "dual_route_trend_analysis", version = "V20250617095617673", returnTypes = {JSONObject.class})
    JSONObject getDualRouteTrendAnalysis(@DaParam JSONObject param, @DaShardingCode String shardingCode);



    /**
     * 双路由变化统计查询
     * 查询光路组的检测历史记录，分析隐患整改情况
     *
     * @param param 查询参数，包含：
     *              - startDate: 开始时间 (可选，格式: YYYY-MM-DD HH:mm:ss)
     *              - endDate: 结束时间 (可选，格式: YYYY-MM-DD HH:mm:ss)
     *              - groupId: 光路组ID (可选，用于查询单个光路组)
     *              - objectType: 保护组类型 (可选，optGroup=光路保护组, CircuitPair=电路保护组)
     *              - historyLimit: 历史记录条数限制 (可选，默认2，可放宽到20查看更多历史)
     *              - pageSize: 每页大小 (可选，默认20)
     *              - currentPage: 当前页码 (可选，默认1)
     *              - fromStatus: 源状态筛选 (可选，如：正常、单路由、同管道等)
     *              - toStatus: 目标状态筛选 (可选，如：正常、单路由、同管道等)
     *              - transitionType: 转移类型筛选 (可选，improved=改善, deteriorated=恶化, maintained=保持, changed=变化)
     * @param shardingCode 分库代码
     * @return 变化统计数据，包含分页信息
     */
    @DaAPI(apiCode = "dual_route_change_statistics", version = "V20250618102229686", returnTypes = {JSONObject.class})
    JSONObject getDualRouteChangeStatistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 双路由风险对象查询
     * 查询高风险的光路对象，用于重点监控和预警
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间
     *              - endDate: 结束时间
     *              - riskLevel: 风险等级 (可选): 'high_risk', 'medium_risk', 'low_risk'
     *              - areaCode: 地区代码 (可选)
     * @param shardingCode 分库代码
     * @return 风险对象列表
     */
    @DaAPI(apiCode = "dual_route_risk_objects", version = "V20250616000004", returnTypes = {JSONObject.class})
    JSONObject getDualRouteRiskObjects(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 双路由运营指标汇总
     * 获取关键运营指标的汇总数据，用于仪表板展示
     * 
     * @param param 查询参数，包含：
     *              - timeRange: 时间范围 ('1m', '3m', '6m', '1y')
     *              - areaCode: 地区代码 (可选)
     * @param shardingCode 分库代码
     * @return 运营指标汇总数据
     */
    @DaAPI(apiCode = "dual_route_kpi_summary", version = "V20250616000005", returnTypes = {JSONObject.class})
    JSONObject getDualRouteKpiSummary(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 双路由地区对比分析
     * 按地区维度对比各项运营指标，用于地区间绩效对比
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间
     *              - endDate: 结束时间
     *              - areaCodes: 地区代码列表 (可选，为空则查询所有地区)
     * @param shardingCode 分库代码
     * @return 地区对比分析数据
     */
    @DaAPI(apiCode = "dual_route_area_comparison", version = "V20250616000006", returnTypes = {JSONObject.class})
    JSONObject getDualRouteAreaComparison(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 双路由问题类型分析
     * 按问题类型统计分析，了解各类问题的分布和变化趋势
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间
     *              - endDate: 结束时间
     *              - areaCode: 地区代码 (可选)
     *              - problemTypes: 问题类型列表 (可选)
     * @param shardingCode 分库代码
     * @return 问题类型分析数据
     */
    @DaAPI(apiCode = "dual_route_problem_analysis", version = "V20250616000007", returnTypes = {JSONObject.class})
    JSONObject getDualRouteProblemAnalysis(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 双路由运营报表导出数据
     * 获取用于导出Excel报表的详细数据
     * 
     * @param param 查询参数，包含：
     *              - startDate: 开始时间
     *              - endDate: 结束时间
     *              - areaCode: 地区代码 (可选)
     *              - reportType: 报表类型 ('monthly', 'trend', 'detail', 'summary')
     * @param shardingCode 分库代码
     * @return 报表导出数据
     */
    @DaAPI(apiCode = "dual_route_export_data", version = "V20250616000008", returnTypes = {JSONObject.class})
    JSONObject getDualRouteExportData(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
