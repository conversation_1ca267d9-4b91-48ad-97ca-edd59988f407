package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.entity.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@DaClientProxy
public interface KeyCustTransferDao {

    @DaAPI(apiCode = "kct_port_query_by_page", version = "V20250623105307724", returnTypes = {PageResponse.class, CmPort.class})
    PageResponse<CmPort> queryCmPortByPage(@DaParam PortQueryDTO portQueryDTO
            , @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "kct_device_query_by_page", version = "V20250623164331147", returnTypes = {PageResponse.class, CmDevice.class})
    PageResponse<CmDevice> queryCmDeviceList(@DaParam EntityQueryDTO entityQueryDTO
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_wavelength_channel", version = "V20250624172728680", returnTypes = {List.class, WavelengthChannel.class})
    List<WavelengthChannel> queryWavelengthChannel(@DaParam WaveQueryDTO waveQueryDTO
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_cm_link", version = "V20250626101927752", returnTypes = {List.class, CmLink.class})
    List<CmLink> queryCmLink(@DaParam CmLink cmLink
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_optRoad_by_optCableName", version = "V20250626152106484", returnTypes = {List.class, CmLink.class})
    List<CmLink> queryOptRoadByOptCableName(@DaParam Map<String, String> optCableNameMap
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_crossConnect_by_optRoadId", version = "V20250626154535902", returnTypes = {List.class, CmLink.class})
    List<CmLink> queryCrossConnectByOptRoadId(@DaParam Map<String, BigDecimal> optRoadIdMap
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_optCable_by_optFiber", version = "V20250627091843762", returnTypes = {List.class, CmLink.class})
    List<CmLink> queryOptCableByOptFiber(@DaParam Map<String, BigDecimal> optFiberIdMap
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_device_by_ids", version = "V20250630150718541", returnTypes = {List.class, CmDevice.class})
    List<CmDevice> queryDeviceByIds(@DaParam Map<String, List<BigDecimal>> deviceIdsMap
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_optRoad_by_multiSegId", version = "V20250701152413303", returnTypes = {List.class, CmLink.class})
    List<CmLink> queryOptRoadByMultiSegId(@DaParam Map<String, String> optCableNameMap
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_upperLink_by_lowerLink", version = "V20250702104017004", returnTypes = {List.class, CmLink.class})
    List<CmLink> queryUpperLinkByLowerLink(@DaParam Map<String, Object> LowerLinkMap
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_query_lowerLink_by_upperLink", version = "V20250702104951339", returnTypes = {List.class, CmLink.class})
    List<CmLink> queryLowerLinkByUpperLink(@DaParam Map<String, String> upperLinkMap
            , @DaShardingCode  String bcShardingCode);

    @DaAPI(apiCode = "kct_save_occupy_res_info", version = "V20250710151505238", returnTypes = {Integer.class})
    Integer occupyResInfo(@DaParam Map<String, List> occupyResInfoDOMap, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "kct_query_occupy_res_info", version = "V20250716111434615", returnTypes = {List.class, OccupyResInfoDO.class})
    List<OccupyResInfoDO> queryBcInfo(@DaParam OccupyResInfoDO occupyResInfoDO
            , @DaShardingCode String dsGraphJs);
}
