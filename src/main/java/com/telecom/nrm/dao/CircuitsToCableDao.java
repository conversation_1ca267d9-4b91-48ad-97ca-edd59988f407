package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.*;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;


@DaClientProxy
public interface CircuitsToCableDao {

    @DaAPI(apiCode = "get_az_posxy_by_circuits_code",version = "V20231213211535903",returnTypes = { JSONObject.class})
    public JSONObject CircuitsToCable(@DaParam JSONObject param, @DaShardingCode String shardingCode);




    @DaAPI(apiCode = "get_facility_posxy_by_circuits_code",version = "V20231227165838438",returnTypes = { List.class,JSONObject.class})
    public List<JSONObject> CircuitsToFacility(@DaParam JSONObject param, @DaShardingCode String shardingCode);


}
