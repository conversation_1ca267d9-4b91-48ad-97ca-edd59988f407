package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.*;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.Device;
import com.telecom.nrm.domain.FiberCable;
import com.telecom.nrm.domain.Link;

@DaClientProxy
public interface ObsLocationDao {

    @DaAPI(apiCode = "nrm.query_pon_line_by_access_code",version = "V20240108145316085",returnTypes = {Link.class})
    public Link queryPonLine(@DaParam(name="access_code") String param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_route_by_link_id",version = "V20240108173418812",returnTypes = {PageResponse.class, Link.class})
    public PageResponse<Link> queryRoute(@DaParam(name="link_id") String param, @DaParam(name = "pageSize") Integer pageSize,
                                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_fiber_by_route",version = "V20240108173911333",returnTypes = {PageResponse.class, Link.class})
    public PageResponse<Link> queryFiber(@DaParam(name="link_id") String param, @DaParam(name = "pageSize") Integer pageSize,
                                          @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_fiber_segment",version = "V20240108174130786",returnTypes = {PageResponse.class, FiberCable.class})
    public PageResponse<FiberCable> queryFiberCableSegment(@DaParam(name="link_id") String param, @DaParam(name = "pageSize") Integer pageSize,
                                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_fiber_cable",version = "V20240108180831473",returnTypes = {FiberCable.class})
    public FiberCable queryFiberCable(@DaParam(name="link_id") String param, @DaShardingCode String shardingCode);

    //根据device_id和port_id查设备及端口
    @DaAPI(apiCode = "nrm.query_device_port_by_id",version = "V20240109093319297",returnTypes = {Device.class})
    public Device queryDevice(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //根据device_id查所属光设施
    @DaAPI(apiCode = "nrm.query_optical_facility_by_id",version = "V20240109094517080",returnTypes = {Device.class})
    public Device queryOpticalFacility(@DaParam(name="device_id") String param, @DaShardingCode String shardingCode);

    //查以太网链路
    @DaAPI(apiCode = "nrm.query_ethernet_line",version = "V20240506160340416",returnTypes = {Link.class})
    public Link queryEthernetLine(@DaParam(name="access_code") String param, @DaShardingCode String shardingCode);

    //查双纤光链路路由
    @DaAPI(apiCode = "nrm.get_route_by_duplex_access_code",version = "V20240506160930208",returnTypes = {PageResponse.class, Link.class})
    public PageResponse<Link> getRouteByDuplexAccessCode(@DaParam(name="link_id") String param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    //查光电转换器的硬跳
    @DaAPI(apiCode = "nrm.query_optical_transform",version = "V20240506161547821",returnTypes = {Link.class})
    public Link queryOpticalTransform(@DaParam(name="device_id") String param, @DaShardingCode String shardingCode);

    //查光电转换器连接的光配线架
    @DaAPI(apiCode = "nrm.query_duplex_optical_facility",version = "V20240506163616051",returnTypes = {Device.class})
    public Device queryDuplexOpticalFacility(@DaParam(name="device_id") String param, @DaShardingCode String shardingCode);

    //查接入号的业务类型
    @DaAPI(apiCode = "nrm.query_product_info",version = "V20240508111231245",returnTypes = {JSONObject.class})
    public JSONObject queryProductInfo(@DaParam(name="access_code") String param, @DaShardingCode String shardingCode);

    //查数字电路的光缆段
    @DaAPI(apiCode = "nrm.get_digital_circuit_fiber_segment",version = "V20240508112147164",returnTypes = {PageResponse.class, FiberCable.class})
    public PageResponse<FiberCable> queryDigitalCircuitFiberCableSegment(@DaParam(name="crm_product_id") String param, @DaParam(name = "pageSize") Integer pageSize,
                                                           @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_start_pc",version = "V20240508151921091",returnTypes = {String.class})
    public String getStartPC(@DaParam Link param, @DaShardingCode String shardingCode);
}
