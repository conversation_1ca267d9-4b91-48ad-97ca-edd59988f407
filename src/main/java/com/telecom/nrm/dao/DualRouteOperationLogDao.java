package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;

/**
 * 双路由管理操作日志DAO接口
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@DaClientProxy
public interface DualRouteOperationLogDao {

    /**
     * 插入操作日志记录
     *
     * @param param 日志参数
     * @param shardingCode 分库编码
     * @return 插入结果
     */
    @DaAPI(apiCode = "pm_dual_route_operation_log_insert", version = "V20250626155821973", returnTypes = {Integer.class})
    Integer insertOperationLog(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 分页查询操作日志记录
     *
     * @param param 查询参数
     * @param pageSize 页大小
     * @param currentPage 当前页
     * @param shardingCode 分库编码
     * @return 分页查询结果
     */
    @DaAPI(apiCode = "pm_dual_route_operation_log_query", version = "V20250626155934976", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryOperationLog(@DaParam JSONObject param,
                                             @DaParam(name = "pageSize") Integer pageSize,
                                             @DaParam(name = "currentPage") Integer currentPage,
                                             @DaShardingCode String shardingCode);

    /**
     * 查询操作日志详情
     *
     * @param param 查询参数（包含id）
     * @param shardingCode 分库编码
     * @return 日志记录详情
     */
    @DaAPI(apiCode = "pm_dual_route_operation_log_detail", version = "V20250626160830332", returnTypes = {JSONObject.class})
    JSONObject queryOperationLogDetail(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询操作日志统计信息
     *
     * @param param 查询参数
     * @param shardingCode 分库编码
     * @return 统计结果
     */
    @DaAPI(apiCode = "pm_dual_route_operation_log_statistics", version = "V20250626160917266", returnTypes = {List.class, JSONObject.class})
    List<JSONObject> queryOperationLogStatistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 批量插入操作日志记录
     *
     * @param param 日志参数列表
     * @param shardingCode 分库编码
     * @return 插入结果
     */
    @DaAPI(apiCode = "pm_dual_route_operation_log_batch_insert", version = "V20250626160951709", returnTypes = {Integer.class})
    Integer batchInsertOperationLog(@DaParam(name = "list") List<JSONObject> param, @DaShardingCode String shardingCode);

    /**
     * 清理过期日志记录
     *
     * @param param 清理参数（包含保留天数）
     * @param shardingCode 分库编码
     * @return 清理结果
     */
    @DaAPI(apiCode = "pm_dual_route_operation_log_cleanup", version = "V20250626161020229", returnTypes = {Integer.class})
    Integer cleanupExpiredLogs(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询光路保护组详情 - 用于记录查看日志
     *
     * @param param 查询参数（包含id）
     * @param shardingCode 分库编码
     * @return 保护组详情
     */
    @DaAPI(apiCode = "pm_opt_road_group_result_query", version = "V20240617150533407", returnTypes = {JSONObject.class})
    JSONObject queryOptGroupDetail(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}