package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.*;
import com.telecom.da.client.domain.PageResponse;


@DaClientProxy
public interface DSJJDao {

    @DaAPI(apiCode = "nrm.query_pm_intf_dsjj_kb",version = "V20231210041800829",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQuery(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.query_pm_intf_dsjj_kb_invalid",version = "V20231210050204104",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pageQueryInvalid(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.update_pm_intf_dsjj_kb_result",version = "V20231210045226376",returnTypes = { JSONObject.class})
    public JSONObject updateResult(@DaParam JSONObject param,@DaShardingCode String shardingCode,@DaXid String xid);

    @DaAPI(apiCode = "nrm.get_circuit_code_by_access_code",version = "V20231211154455782",returnTypes = { String.class})
    public String getCircuitCodeByAccessCode(@DaParam(name="access_code") String accessCode,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_circuit_code_by_linkid",version = "V20231215111421898",returnTypes = { JSONObject.class})
    public JSONObject getCircuitCodeByLinkId(@DaParam(name="link_id") String accessCode,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_rate_date_by_accesscode",version = "V20231218150427085",returnTypes = { JSONObject.class})
    public JSONObject getDateRateByAccessCode(@DaParam(name="access_code") String accessCode,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_circuit_by_circuit_code",version = "V20231211151908059",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryCircuitByCircuitCode(@DaParam(name="link_id") String linkID, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_room_info_by_port",version = "V20231219094015311",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getRoomInfoByPort(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_line_type_by_access_code",version = "V20231228144507666",returnTypes = { JSONObject.class})
    public JSONObject getLineTypeByAccessCode(@DaParam(name="access_code") String accessCode,@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_all_sublinks_by_linkid",version = "V20231228141103275",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getSubLinksByLinkID(@DaParam(name="link_id") String linkID, @DaParam(name = "pageSize") Integer pageSize,
                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_pon_link_by_linkid",version = "V20231228153809208",returnTypes = {JSONObject.class})
    public JSONObject getPonLinksByLinkID(@DaParam(name="link_id") String linkID, @DaShardingCode String shardingCode);

}
