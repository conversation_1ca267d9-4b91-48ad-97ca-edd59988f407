package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.Link;


import java.math.BigDecimal;

@DaClientProxy
public interface ToDoDao {


    @DaAPI(apiCode = "nrm.get_user_id_by_user_phone", version = "V20240531162604402", returnTypes = {JSONObject.class})
    public JSONObject getUserIDByUserPhone(@DaParam(name="phone") String phone, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_orders_by_user_id", version = "V20240531163001267", returnTypes = {String.class})
    public String getOrdersByUserID(@DaParam(name="user_id")String id, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.update_status_by_user_id", version = "V20240604110947444", returnTypes = {Integer.class})
    public Integer updateStatusByUserID(@DaParam JSONObject params, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.query_todo_by_user_id",version = "V20240604110602541",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryToDoByUserId(@DaParam(name="user_id") String param, @DaParam(name = "pageSize") Integer pageSize,
                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

}
