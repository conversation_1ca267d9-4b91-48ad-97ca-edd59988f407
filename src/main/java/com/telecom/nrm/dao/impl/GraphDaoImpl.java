package com.telecom.nrm.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.GraphDao;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GraphDaoImpl extends BaseGraphDaoImpl implements GraphDao {

    public JSONArray runGQL(String GQL) {
        return super.executeJson(GQL);

    }

    public Node getNode(String id) {
        String gql = "fetch prop on node \"%s\" yield vertex as n";
        gql = String.format(gql,id);
        JSONArray results = super.executeJson(gql);
        JSONObject result=results.getJSONObject(0);
        JSONArray dataList = result.getJSONArray("data");
        Node node = null;
        if (dataList.size()>0) {
            JSONObject data = dataList.getJSONObject(0);
            JSONObject meta = data.getJSONArray("meta").getJSONObject(0);
            JSONObject row = data.getJSONArray("row").getJSONObject(0);
            node = convertToNode(meta,row);
        }
        return node;
    }


    public Graph getNeighbors(Node node) {

        String gql = "go 1 step from \"%s\"  over * BIDIRECT  yield $^ as src,type(edge) as type,src(edge) as source_id," +
                "dst(edge) as target_id,$$ as dst, properties(edge)";
        gql = String.format(gql,node.getId());
        JSONArray results = super.executeJson(gql);
        JSONObject result=results.getJSONObject(0);
        JSONArray dataList = result.getJSONArray("data");
        int size = dataList.size();
        Graph graph = new Graph();
        for (int i=0;i<size;i++) {
            JSONObject data = dataList.getJSONObject(i);
            JSONArray dataMeta = data.getJSONArray("meta");
            JSONArray dataRow = data.getJSONArray("row");
            JSONObject dataMetaANode = dataMeta.getJSONObject(0);
            JSONObject dataMetaBNode = dataMeta.getJSONObject(4);
            JSONObject dataANode = dataRow.getJSONObject(0);
            JSONObject dataZNode = dataRow.getJSONObject(4);
            String edgeType = dataRow.getString(1);
            String aId=dataRow.getString(2);
            String bId=dataRow.getString(3);
            JSONObject edgeProperties=dataRow.getJSONObject(5);
            String apiCode = edgeProperties.getString("api_code");
            String apiVersion=edgeProperties.getString("api_version");
            // String zApiCode=edgeProperties.getString("z_api_code");
            // String zApiVersion = edgeProperties.getString("z_api_version");
            Node aNode = convertToNode(dataMetaANode, dataANode);
            Node bNode = convertToNode(dataMetaBNode, dataZNode);
            Edge edge = convertEdge(aNode,bNode,aId,bId,edgeType);
            edge.setApiCode(apiCode);
            edge.setApiVersion(apiVersion);

            graph.addNode(aNode);
            graph.addNode(bNode);
            graph.addEdge(edge);
        }

        return graph;
    }

    public Set<Node> reloadNodes(Set<Node> nodes) {
        if (ObjectUtil.isEmpty(nodes)){
            return new HashSet<Node>();
        }
        HashMap<String,Node> nodeMap = new HashMap<>();
        for (Node n: nodes) {
            nodeMap.put(n.getId(), n);
        }
        String ids=nodes.stream().map(n->"\""+n.getId()+"\"").collect(Collectors.joining(","));
        String gql = "fetch prop on node %s yield vertex as n";
        gql = String.format(gql,ids);
        JSONArray results = super.executeJson(gql);
        JSONObject result=results.getJSONObject(0);
        JSONArray dataList = result.getJSONArray("data");
        Node node = null;
        int size =dataList.size();
        Set<Node> resultSet =new HashSet<>();
        for (int i=0;i<size;i++) {
            JSONObject data = dataList.getJSONObject(i);
            JSONObject meta = data.getJSONArray("meta").getJSONObject(0);
            JSONObject row = data.getJSONArray("row").getJSONObject(0);
            node = convertToNode(meta,row);
            node.setX(nodeMap.get(node.getId()).getX());
            node.setY(nodeMap.get(node.getId()).getY());
            node.setMethod(nodeMap.get(node.getId()).getMethod());
            if (ObjectUtil.isEmpty(node.getMethod())) node.setMethod("union");
            resultSet.add(node);
        }
        return resultSet;
    }

    public Set<Edge> reloadEdges(Set<Edge> edges) {

        Set<Edge> resultEdgeSet = new HashSet<>();
        HashMap<String,Edge> edgeMap = new HashMap<>();
        for (Edge e: edges) {
            edgeMap.put(e.getId(),e);
            edgeMap.put(e.getReverseId(),e);
        }
        Map<String, List<Edge>> edgeGroup= edges.stream().collect(Collectors.groupingBy(Edge::getType, Collectors.toList()));
        Set<String> typeSet = edgeGroup.keySet();
        for (String type: typeSet) {
            List<Edge> edgeList = edgeGroup.get(type);

            List<Edge> doubleEdgeList = new ArrayList<>(); // 双份的边列表
            for (Edge e: edgeList) {
                doubleEdgeList.add(new Edge(e.getAId(),e.getBId(),e.getType()));
                doubleEdgeList.add(new Edge(e.getBId(),e.getAId(),e.getType()));
            }

            String ids = doubleEdgeList.stream().map(n->"\""+n.getAId()+"\"->\""+n.getBId()+"\"").collect(Collectors.joining(","));
            String gql = "fetch prop on %s %s yield edge as e";
            gql = String.format(gql,type,ids);
            JSONArray results = super.executeJson(gql);
            JSONObject result=results.getJSONObject(0);
            JSONArray dataList = result.getJSONArray("data");
            int size= dataList.size();

            for (int i=0;i<size;i++ ) {
                Edge edge = new Edge();
                JSONObject data = dataList.getJSONObject(i);
                JSONObject meta = data.getJSONArray("meta").getJSONObject(0).getJSONObject("id");
                JSONObject row = data.getJSONArray("row").getJSONObject(0);
                edge.setAId(meta.getString("src"));
                edge.setBId(meta.getString("dst"));
                edge.setType(meta.getString("name"));
                String edgeId=edge.getId();
                edge.setSource(edgeMap.get(edgeId).getSource());
                edge.setTarget(edgeMap.get(edgeId).getTarget());
                edge.setApiCode(row.getString("api_code"));
                edge.setApiVersion(row.getString("api_version"));
                edge.setDataSourceType(row.getString("data_source_type"));
                // edge.setZApiCode(row.getString("z_api_code"));
                // edge.setZApiVersion(row.getString("z_api_version"));
                resultEdgeSet.add(edge);
            }
            // log.info("dataList, {}", dataList);
        }
        return resultEdgeSet;
    }





}
