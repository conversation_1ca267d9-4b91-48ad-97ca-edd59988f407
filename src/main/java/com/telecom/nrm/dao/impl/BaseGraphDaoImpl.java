package com.telecom.nrm.dao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.config.NebulaGraphProperties;
import com.telecom.nrm.config.SessionPool;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Node;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.net.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j

public class BaseGraphDaoImpl {



    @Autowired
    RestTemplate restTemplate;

    @Autowired
    private NebulaGraphProperties nebulaGraphProperties;





    public JSONArray executeJson(String gql) {
        JSONObject param = new JSONObject();
        param.put("GQL", gql);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(param, headers);

        ResponseEntity<JSONObject> responseEntity= restTemplate.postForEntity(nebulaGraphProperties.getUrl()+"/runGQL",request, JSONObject.class);
        return responseEntity.getBody().getJSONArray("result");


        // return result.getJSONArray("result");
    }

    Node convertToNode(JSONObject dataMetaNode, JSONObject dataNode) {
        Node node = new Node();
        node.setId(dataMetaNode.getString("id"));
        node.setName(dataNode.getString("node.name"));
        node.setCode(dataNode.getString("node.code"));
        node.setApiCode(dataNode.getString("node.api_code"));
        node.setApiVersion(dataNode.getString("node.api_version"));
        node.setLabels(dataNode.getString("node.labels"));
        node.setDataSourceType(dataNode.getString("node.data_source_type"));
        return node;
    }

    Edge convertEdge(Node aNode, Node bNode, String srcId, String dstId, String edgeType) {
        Edge edge = new Edge();
        edge.setAId(srcId);
        edge.setBId(dstId);
        edge.setSource(aNode.getId());
        edge.setTarget(bNode.getId());
        edge.setType(edgeType);
        return edge;
    }
}
