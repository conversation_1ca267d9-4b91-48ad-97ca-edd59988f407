package com.telecom.nrm.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.GraphDao;
import com.telecom.nrm.dao.GraphPathDao;
import com.telecom.nrm.domain.graph.Node;
import com.telecom.nrm.domain.graph.Edge;
import com.telecom.nrm.domain.graph.Graph;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GraphPathDaoImpl extends BaseGraphDaoImpl implements GraphPathDao {

    @Autowired
    GraphDao graphDao;

    /**
     * 解析路径,如果路径中的节点个数是奇数,则返回,否则返回为空
     * @param paths
     * @return
     */
    public String[] parse(String paths) {
        String[] ids= paths.split("->");
        if (ids.length %2 == 0 ) {
            return new String[]{};
        }else{
            return ids;
        }
    }

    public List<String> getNodeIds(String[] ids) {
        List<String> nodeIds = new ArrayList<String>();
        for (int i = 0; i < ids.length; i++) {
            String id = ids[i];
            if (i%2==0) {
                nodeIds.add(id);
            }
        }
        return nodeIds;
    }

    public List<String> getEdgeIds(String[] ids) {
        List<String> edgeIds = new ArrayList<>();
        for (int i = 0; i < ids.length; i++) {
            String id = ids[i];
            if (i%2==1) {
                edgeIds.add(id);
            }
        }
        return edgeIds;
    }

    public List<Node> getNodes(String[] ids) {
        List<Node> nodes = new ArrayList<>();
        List<String> nodeIds = getNodeIds(ids);

        for (String id : nodeIds) {
            Node n = new Node();
            n.setId(id);
            nodes.add(n);
        }
        return nodes;
    }

    public Edge buildEdge(String sourceId, String targetId, String type) {
        Edge edge = new Edge();
        edge.setSource(sourceId);
        edge.setTarget(targetId);

        edge.setAId(sourceId);
        edge.setBId(targetId);

        edge.setType(type);
        return edge;
    }

    public List<Edge> getEdges(String[] ids) {
        List<String> nodeIds = getNodeIds(ids);
        List<String> edgeIds = getEdgeIds(ids);
        List<Edge> edgeList =new ArrayList<>();
        int i =0;
        for (String edgeId : edgeIds) {
            int edgeIndex =  i * 2+1;
            int sourceNodeIndex = (edgeIndex - 1)/2;
            int targetNodeIndex = (edgeIndex + 1)/2 ;
            String sourceNodeId = nodeIds.get(sourceNodeIndex);
            String targetNodeId = nodeIds.get(targetNodeIndex);
            Edge edge = buildEdge(sourceNodeId, targetNodeId, edgeId);
            edgeList.add(edge);
            i++;
        }

        return edgeList;


    }

    public Graph parseGraph(String[] paths) {

        Graph g = new Graph();

        Set<Node> nodes = new HashSet<>();
        Set<Edge> edges = new HashSet<>();
        // 解析路径中的标志服
        for (String path : paths) {
            String[] ids = parse(path);
            // 从路径中加载节点
            List<Node> pathNodes = getNodes(ids);
            nodes.addAll(pathNodes);
            // 从路径中加载边
            List<Edge> pathEdges = getEdges(ids);
            edges.addAll(pathEdges);

        }

        Set<Node> resultNodes = graphDao.reloadNodes(nodes);
        g.setNodes(resultNodes);

        Set<Edge> resultEdges = graphDao.reloadEdges(edges);
        g.setEdges(resultEdges);

        log.info("nodes: {}" , resultNodes);
        log.info("edges: {}" , resultEdges);

        return g;
    }


}
