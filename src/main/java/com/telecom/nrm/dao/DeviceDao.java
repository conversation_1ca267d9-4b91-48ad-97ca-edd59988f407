package com.telecom.nrm.dao;


import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.DeviceBO;
import com.telecom.nrm.domain.Row;

/**
 * <AUTHOR>
 */
@DaClientProxy
public interface DeviceDao {

    /**
     * 根据 设备信息 地市、局站、机房、 查询电路 支持分页
     * @param param
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "device_prod_circuit_query", version = "V20240619134438122", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryCircuitByDeviceInfo(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryCircuitByDevice", version = "V20240807100517260", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryCircuitByDevice(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "queryResInfoByIds", version = "V20240809153530858", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryResInfoByIds(@DaParam JSONObject param,   @DaParam(name = "pageSize") Integer pageSize,
                                                  @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "device_detail_query", version = "V20240620174027301", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> deviceDetailQuery(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 质差查询
     * @param param
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "quality_difference_query", version = "V20240711175415056", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> qualityDifferenceQuery(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 根据IP查设备详情
     * @param param
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "nrm.query_node_device_by_ip", version = "V20240708143525380", returnTypes = {JSONObject.class})
    JSONObject queryNodeDeviceByIp(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.graph_query_devices_ports_by_ids", version = "V20250311142704238", returnTypes = {JSONObject.class})
    JSONObject graph_query_devices_ports_by_ids(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    // linkIds 后续这个当作根据设备查光路详情   circuitCode  电路代号  custId    客户id也作为查询条件
    @DaAPI(apiCode = "nrm.graph_query_link_by_device_port_ware_id", version = "V20250311143908592", returnTypes = {PageResponse.class, Row.class})
    PageResponse<Row> graph_query_link_by_device_port_ware_id(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    // linkIds 后续这个当作根据设备查光路详情   circuitCode  电路代号  custId    客户id也作为查询条件
    @DaAPI(apiCode = "nrm.graph_device_by_condition", version = "V20250320154627069", returnTypes = {PageResponse.class, DeviceBO.class})
    PageResponse<DeviceBO> graph_device_by_condition(@DaParam JSONObject param, @DaShardingCode String shardingCode);



}
