package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

@DaClientProxy
public interface CustViewNodeDao {

    @DaAPI(apiCode = "pm_busi_user_scene_node_query", version = "V20240204144827861", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryList(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_busi_user_scene_node_insert", version = "V20240204145644109", returnTypes = {Integer.class})
    public Integer save(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "pm_busi_user_scene_node_delete", version = "V20240204150437882", returnTypes = {Integer.class})
    public Integer delete(@DaParam(name="id")String id, @DaShardingCode String shardingCode);


}
