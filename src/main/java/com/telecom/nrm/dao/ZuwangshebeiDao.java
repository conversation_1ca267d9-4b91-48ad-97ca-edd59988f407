package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;

@DaClientProxy
public interface ZuwangshebeiDao {
    //组网设备光路组查询（新建API）
    @DaAPI(apiCode = "pm_opt_road_group_query_zuwang", version = "V20240729170611631", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pm_opt_road_group_query_zuwang(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                            @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
    //组网设备稽核结果查询（新建API）
    @DaAPI(apiCode = "pm_opt_road_group_result_query_zuwang", version = "V20240729172320801", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> pm_opt_road_group_result_query_zuwang(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                   @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);
    //直接调用光路组元素
    @DaAPI(apiCode = "pm_opt_road_group_element_query", version = "V20240617160223637", returnTypes = {List.class})
    public List<JSONObject> pm_opt_road_group_element_query(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //插入（API新建存在报错）
    @DaAPI(apiCode = "pm_opt_road_group_insert", version = "V20240717153110307", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //直接调用插入光路组元素表
    @DaAPI(apiCode = "pm_opt_road_group_element_insert", version = "V20240717192828291", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_element_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //更新光路组备注（新建API）
    @DaAPI(apiCode = "pm_opt_road_group_note_update_zuwang", version = "V20240730093311078", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_note_update_zuwang(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //直接调用光路隐患核查API
    @DaAPI(apiCode = "pm_risk_check_insert", version = "V20240718152054209", returnTypes = {Integer.class})
    public Integer pm_risk_check_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //直接调用光路组删除API
    @DaAPI(apiCode = "pm_opt_road_group_delete", version = "V20240718155239443", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //创建组网设备光路组状态统计查询API
    @DaAPI(apiCode = "pm_opt_road_group_state_statistic_query_zuwang", version = "V20240730095522344", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_state_statistic_query_zuwang(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //直接调用人工确认API
    @DaAPI(apiCode = "pm_risk_check_confirm", version = "V20240723173255955", returnTypes = {Integer.class})
    public Integer pm_risk_check_confirm(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //直接调用光路组元素删除API
    @DaAPI(apiCode = "pm_opt_road_group_element_delete", version = "V20240724142553938", returnTypes = {Integer.class})
    public Integer pm_opt_road_group_element_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 组网设备-逻辑图-中继
     */
    @DaAPI(apiCode = "logic_zuwang", version = "V20240827100649364", returnTypes = {List.class})
    public List<JSONObject> pm_opt_road_group_query_zuwang_zj(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 重要设备ipran列表查询
     */
    @DaAPI(apiCode = "important_device_ipran_list", version = "V20241219170831623", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject>  queryImportantDeviceIpranList(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                   @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    /**
     * 重要设备ipran 光缆详情列表
     */
    @DaAPI(apiCode = "important_device_ipran_cable_detail", version = "V20241219171412847", returnTypes = {List.class})
    public List<JSONObject>  queryImportantDeviceIpranCableDetail(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 重要设备ipran 拓扑图数据
     */
    @DaAPI(apiCode = "important_device_ipran_topo", version = "V20241223093103773", returnTypes = {List.class})
    public List<JSONObject>  queryImportantDeviceIpranTopo(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
