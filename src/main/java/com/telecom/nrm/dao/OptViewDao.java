package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;

import java.util.List;

@DaClientProxy
public interface OptViewDao {

    /**
     * 查询光缆数据
     * @param param
     * @param shardingCode
     * @return
     */
    @DaAPI(apiCode = "fiber_opt_net_station", version = "V20240823105331994", returnTypes = {List.class})
    public List<JSONObject> fiber_opt_net(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询光缆连接数据
     */
    @DaAPI(apiCode = "fiber_opt_net_cable", version = "V20240827104936837", returnTypes = {List.class})
    public List<JSONObject> fiber_opt_link(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 获得光缆连接数据方案
     */
    @DaAPI(apiCode = "fiber_opt_net_shortest_node", version = "V20240905103304898", returnTypes = {List.class})
    public List<JSONObject> fiber_opt_link_scheme(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 根据光缆对比ID_ID查询结果数据
     */
    @DaAPI(apiCode = "fiber_opt_net_compare", version = "V20240905103304898", returnTypes = {List.class})
    public JSONObject fiber_opt_net_compare(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 根据光缆对比ID_ID 插入数据
     */
    @DaAPI(apiCode = "fiber_opt_net_compare_insert", version = "V20240905103304898", returnTypes = {List.class})
    public JSONObject fiber_opt_net_compare_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    /**
     * 根据光缆对比ID_ID 更新数据
     */
    @DaAPI(apiCode = "fiber_opt_net_compare_update", version = "V20240905103304898", returnTypes = {List.class})
    public JSONObject fiber_opt_net_compare_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 批量查询双路由结果数据
     */
    @DaAPI(apiCode = "pm_opt_check_result", version = "V20240910172749525", returnTypes = {List.class})
    public List<JSONObject> fiber_opt_net_double_route(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询区县光缆数据
     */
    @DaAPI(apiCode = "fiber_opt_net_cable_area", version = "V20241016163802257", returnTypes = {List.class})
    public List<JSONObject> fiber_opt_net_area(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    /**
     * 根据光缆编号查询管道数据
     */
    @DaAPI(apiCode = "fiber_opt_net_pipe", version = "V20241203100540244", returnTypes = {List.class})
    public List<JSONObject> fiber_opt_net_pipe(@DaParam JSONObject param, @DaShardingCode String shardingCode);

}
