package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.*;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.PmIpranTopo;

@DaClientProxy
public interface PmIpranTopoDao {

    @DaAPI(apiCode = "net_resource_integration.get_pm_ipran_topo", version = "V20241017164146256", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> getPmIpranTopoInfo(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                     @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String sharSdingCode);

    @DaAPI(apiCode = "net_resource_integration.save_ipran_topo",version = "V20241017170426317",returnTypes = {JSONObject.class})
    JSONObject savePmIpranTopo(@DaParam PmIpranTopo entity, @DaShardingCode String shardingCode);


}
