package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.graph.GraphScene;

@DaClientProxy
public interface CustViewDao {




    @DaAPI(apiCode = "pm_busi_user_scene_get",version = "V20240121102058401",returnTypes = { JSONObject.class})
    public JSONObject getById(@DaParam(name="id") Long id,@DaShardingCode String shardingCode);



    @DaAPI(apiCode = "pm_busi_user_scene_query", version = "V20240116215011606", returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> queryCustview(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_insert", version = "V20240117091326810", returnTypes = {Integer.class})
    public Integer saveCustviewsave(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "pm_busi_user_scene_delete", version = "V20240117202248119", returnTypes = {Integer.class})
    public Integer deleteCustview(@DaParam(name="id")String id, @DaShardingCode String shardingCode);

}
