package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.*;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.graph.GraphScene;

@DaClientProxy
public interface GraphSceneDao {

    @DaAPI(apiCode = "nrm.query_pm_graph_scene",version = "V20230723182516656",returnTypes = {PageResponse.class, GraphScene.class})
    public PageResponse<GraphScene> pageQuery(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                              @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "nrm.get_pm_graph_scene",version = "V20230723183236305",returnTypes = { GraphScene.class})
    public GraphScene get(@DaParam(name="id") Long id,@DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.get_pm_graph_scene_by_code",version = "V20230807135800735",returnTypes = { GraphScene.class})
    public GraphScene getByCode(@DaParam(name="code") String id,@DaShardingCode String shardingCode);


    @DaAPI(apiCode = "nrm.save_pm_graph_scene",version = "V20230723200701783",returnTypes = { GraphScene.class})
    public GraphScene save(@DaParam GraphScene scene,@DaShardingCode String shardingCode,@DaXid String xid);

    @DaAPI(apiCode = "nrm.delete_pm_graph_scene",version = "V20230723201902388",returnTypes = { Integer.class})
    public Integer delete(@DaParam(name="id") Long id,@DaShardingCode String shardingCode,@DaXid String xid);

    @DaAPI(apiCode = "Rs_ResourceConsistency",version = "V20231127091613717",returnTypes = { JSONObject.class})
    public JSONObject getRs(@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "IP_NMSDataConsistency",version = "V20231124170331544",returnTypes = { JSONObject.class})
    public JSONObject getIP(@DaShardingCode String shardingCode);

    @DaAPI(apiCode = "year_month",version = "V20231127152001997",returnTypes = { JSONObject.class})
    public JSONObject getYear(@DaShardingCode String shardingCode);
}
