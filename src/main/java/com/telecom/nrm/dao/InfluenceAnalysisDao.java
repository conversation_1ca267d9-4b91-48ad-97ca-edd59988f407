package com.telecom.nrm.dao;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.*;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.Device;
import com.telecom.nrm.domain.FiberCable;
import com.telecom.nrm.domain.Link;

import java.util.List;

@DaClientProxy
public interface InfluenceAnalysisDao {

    @DaAPI(apiCode = "query85bastoolt",version = "V20240416154146663",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject> query85bastoolt(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);




    //查找影响分析的所有场景
    @DaAPI(apiCode = "pageQueryquerySceneGraph",version = "V20240423114742324",returnTypes = {PageResponse.class, JSONObject.class})
    public PageResponse<JSONObject>  pageQueryquerySceneGraph(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                         @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


//    query_cfs_influence_by_device_code_v2     V20240429092620833
//
//    query_cfs_influence_by_cbl_sect_code      V20240429103218930
//
//    query_cfs_influence_by_fiber_busi_node    V20240429141124434
//
    //查找影响分析的所有场景
    @DaAPI(apiCode = "query_cfs_influence_by_device_code_v2",version = "V20240429092620833",returnTypes = { JSONObject.class})
    public JSONObject  query_cfs_influence_by_device_code_v2(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "query_cfs_influence_by_pon_code",version = "V20240808090715765",returnTypes = { JSONObject.class})
    public JSONObject  query_cfs_influence_by_pon_code(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "query_cfs_influence_by_cbl_sect_code",version = "V20240429103218930",returnTypes = { JSONObject.class})
    public JSONObject  query_cfs_influence_by_cbl_sect_code(@DaParam JSONObject param, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "query_cfs_influence_by_cm_net",version = "V20240711162010393",returnTypes = { JSONObject.class})
    public JSONObject  query_cfs_influence_by_cm_net(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "query_cfs_influence_by_fiber_busi_node",version = "V20240429141124434",returnTypes = { JSONObject.class})
    public JSONObject  query_cfs_influence_by_fiber_busi_node(@DaParam JSONObject param, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "query_cfs_influence_by_port_id",version = "V20240520211818321",returnTypes = { JSONObject.class})
    public JSONObject  query_cfs_influence_by_port_id(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "PageQueryPUB_STAFF_ORG_INFO",version = "V20240430201841922",returnTypes = { PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject>  PageQueryPUB_STAFF_ORG_INFO(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    @DaAPI(apiCode = "Query_dim_station_within_cicle",version = "V20240504204637925",returnTypes = { PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject>  Query_dim_station_within_cicle(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                   @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "Query_dim_station_within_polygon",version = "V20240504202549199",returnTypes = { PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject>  Query_dim_station_within_polygon(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "Query_bse_sect_within_circle",version = "V20240506184058025",returnTypes = { PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject>  Query_bse_sect_within_circle(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                      @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "Query_bse_sect_within_polygon",version = "V20240506183238159",returnTypes = { PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject>  Query_bse_sect_within_polygon(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                        @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);





    //存储外力点区域
    @DaAPI(apiCode = "tb_ExternalForcePointConstructionArea_insert", version = "V20240607115838512", returnTypes = {Integer.class})
    public Integer tb_ExternalForcePointConstructionArea_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    //删除网资库外力点施工区域
    @DaAPI(apiCode = "tb_ExternalForcePointConstructionArea_delete", version = "V20240711172222168", returnTypes = {Integer.class})
    public Integer tb_ExternalForcePointConstructionArea_delete(@DaParam JSONObject param, @DaShardingCode String shardingCode);



    //查询网资库外力点施工区域
    @DaAPI(apiCode = "tb_ExternalForcePointConstructionArea_query",version = "V20240712102137702",returnTypes = { PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject>  tb_ExternalForcePointConstructionArea_query(@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                                 @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);


    //修改网资库外力点施工区域
    @DaAPI(apiCode = "tb_ExternalForce_update", version = "V20240715163604891", returnTypes = {Integer.class})
    public Integer tb_ExternalForce_update(@DaParam JSONObject param, @DaShardingCode String shardingCode);
    //销项网资库外力点施工区域
    @DaAPI(apiCode = "tb_ExternalForce_invalide", version = "V20240717155734076", returnTypes = {Integer.class})
    public Integer tb_ExternalForce_invalide(@DaParam JSONObject param, @DaShardingCode String shardingCode);


    // 查询生命线业务接入号
    @DaAPI(apiCode = "nrm.query_life_service_access_code",version = "V20240731123951733",returnTypes = { PageResponse.class,JSONObject.class})
    public PageResponse<JSONObject> query_life_service_access_code (@DaParam JSONObject param, @DaParam(name = "pageSize") Integer pageSize,
                                                                    @DaParam(name = "currentPage") Integer currentPage, @DaShardingCode String shardingCode);



    @DaAPI(apiCode = "query_cbl_sect_by_bse_sect_ids",version = "V20240731232455011",returnTypes = { JSONObject.class})
    public JSONObject  query_cbl_sect_by_bse_sect_ids(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    @DaAPI(apiCode = "query_cbl_by_bse_sect_ids",version = "V20240826214440724",returnTypes = { JSONObject.class})
    public JSONObject  query_cbl_by_bse_sect_ids(@DaParam JSONObject param, @DaShardingCode String shardingCode);





    @DaAPI(apiCode = "nrm.query_customer_manager",version = "V20240806235841245",returnTypes = { List.class,JSONObject.class})
    public List<JSONObject> queryProductCustomerManager (@DaParam(name="crmProductIds") List<String> crmProductIds, @DaShardingCode String shardingCode); ;

    @DaAPI(apiCode = "nrm.query_circuit_code_by_access_code",version = "V20250301152220888",returnTypes = { List.class,JSONObject.class})
    public List<JSONObject> queryCircuitCodeByAccessCode (@DaParam(name="access_code_list") List<String> accessCodeList, @DaShardingCode String shardingCode); ;


    @DaAPI(apiCode = "nrm.get_30_cutover_times",version = "V20250117113045608",returnTypes = { List.class,JSONObject.class})
    public List<JSONObject> query30DCutOverTimes (@DaParam(name="crmProductIds") List<String> crmProductIds, @DaShardingCode String shardingCode); ;



    //根据ID，计算外力点影响的管线，并插入到表中
    @DaAPI(apiCode = "insert_into_externalforce_pipes", version = "V20240903101006596", returnTypes = {Integer.class})
    public Integer insert_into_externalforce_pipes(@DaParam JSONObject param, @DaShardingCode String shardingCode);






    //查询在途障碍单
    @DaAPI(apiCode = "query_tb_externalforce_influence_pipes", version = "V20240919205358583", returnTypes = {JSONObject.class})
    public JSONObject query_tb_externalforce_influence_pipes(@DaParam JSONObject request, @DaShardingCode String shardingCode);





    //插入外力点影响管道的记录到graph sch下面
    @DaAPI(apiCode = "insert_tb_externalforce_influence_pipes", version = "V20240919211021075", returnTypes = {JSONObject.class})
    public JSONObject insert_tb_externalforce_influence_pipes(@DaParam JSONObject param, @DaShardingCode String shardingCode);



    //truncate 表
    @DaAPI(apiCode = "truncate_tb_externalforce_influence_pipes", version = "V20240919215139474", returnTypes = {JSONObject.class})
    public JSONObject truncate_tb_externalforce_influence_pipes(@DaParam JSONObject param, @DaShardingCode String shardingCode);



    //查询在途障碍单
    @DaAPI(apiCode = "query_external_force_daily_summary", version = "V20241008170708780", returnTypes = {JSONObject.class})
    public JSONObject query_external_force_daily_summary(@DaParam JSONObject request, @DaShardingCode String shardingCode);







}
