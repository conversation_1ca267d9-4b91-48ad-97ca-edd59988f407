package com.telecom.nrm.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Configuration
@ConfigurationProperties(prefix = "nebula")
@Data
@RefreshScope
public class NebulaGraphProperties {
    /**
     * 数据共享平台地址
     */
    private String url;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密码
     */
    private String password;
    /**
     * 格式：ip:prot
     */
    private List<String> hostAddresses;
    /**
     * 最小连接数
     */
    private int minConnsSize;
    /**
     * 最大连接数
     */
    private int maxConnSize;
    /**
     * 超时时间
     */
    private int timeout;
    /**
     * 空闲时间
     */
    private int idleTime;
    /**
     * 使用图数据库space
     */
    private String space;

}
