package com.telecom.nrm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * SFTP配置属性类
 * 从Nacos读取SFTP服务器配置信息
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Configuration
@ConfigurationProperties(prefix = "wdaq.sftp")
@Data
@RefreshScope
public class SftpProperties {
    
    /**
     * SFTP服务器地址
     */
    private String sftpserver;
    
    /**
     * SFTP服务器端口
     */
    private Integer sftpport = 22;
    
    /**
     * SFTP用户名
     */
    private String sftpuser;
    
    /**
     * SFTP密码
     */
    private String sftppassword;
    
    /**
     * SFTP输出路径
     */
    private String outputpath;
}
