package com.telecom.nrm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 文档安全平台配置属性类
 * 从Nacos读取文档安全平台配置信息
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Configuration
@ConfigurationProperties(prefix = "wdaq")
@Data
@RefreshScope
public class DocumentSecurityProperties {
    
    /**
     * 客户端ID
     */
    private String client_id;
    
    /**
     * 重定向URL（文档安全平台跳转地址）
     */
    private String redirect_url;
}
