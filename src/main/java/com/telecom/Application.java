package com.telecom;


import com.alibaba.fastjson2.JSON;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

import java.net.ProxySelector;
import java.util.TimeZone;

import static com.alibaba.fastjson2.JSONWriter.Feature.LargeObject;


@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties
@ConfigurationPropertiesScan(basePackages = "com.telecom.*")
@EnableGlobalMethodSecurity(jsr250Enabled = true,prePostEnabled = true)

public class Application {
    public static void main(String[] args) {

        JSON.config(LargeObject, true);
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
//        ProxySelector.setDefault(null);
        // System.setProperty("javax.net.ssl.trustStore", "./elastic.jks");
        // System.setProperty("javax.net.ssl.trustStorePassword", "changeit");
        SpringApplication.run(Application.class, args);
    }
}
