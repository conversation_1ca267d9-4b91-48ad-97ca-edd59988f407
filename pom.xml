<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.telecom.net-resource-integration</groupId>
	<artifactId>nrm-graph-rest</artifactId>
	<version>0.0.1</version>
	<name>nrm-graph-rest</name>
	<packaging>jar</packaging>


	<properties>
		<!-- <java.version>1.8</java.version> -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<start-class>com.telecom.Application</start-class>
		<mysql.version>5.1.47</mysql.version>
		<curator.version>2.9.1</curator.version>
		<ctg-mq.verson>2.4.1_P2</ctg-mq.verson>
		<nacos-client.version>1.4.3</nacos-client.version>

	</properties>

	<parent>

		<groupId>org.springframework.boot</groupId>

		<artifactId>spring-boot-starter-parent</artifactId>

		<version>2.2.2.RELEASE</version>

		<relativePath /> <!-- lookup parent from repository -->



	</parent>

	<dependencies>



		<dependency>
			<groupId>org.commonmark</groupId>
			<artifactId>commonmark</artifactId>
			<version>0.21.0</version>
		</dependency>
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>html2pdf</artifactId>
			<version>4.0.5</version>
		</dependency>
		<!-- JFreeChart 依赖 -->
		<dependency>
			<groupId>org.jfree</groupId>
			<artifactId>jfreechart</artifactId>
			<version>1.5.3</version>
		</dependency>
		<!-- JFreeSVG 依赖 - 用于生成SVG矢量图 -->
		<dependency>
			<groupId>org.jfree</groupId>
			<artifactId>jfreesvg</artifactId>
			<version>3.4</version>
		</dependency>
		<!-- Base64 处理 -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.15</version>
		</dependency>
		<dependency>
			<groupId>org.commonmark</groupId>
			<artifactId>commonmark-ext-gfm-tables</artifactId>
			<version>0.21.0</version>
		</dependency>

		<!-- AWS S3 SDK -->
		<dependency>
			<groupId>com.emc.vipr</groupId>
			<artifactId>aws-java-sdk</artifactId>
			<version>1.7.5</version>
		</dependency>



		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.6.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
			<version>${curator.version}</version>
		</dependency>
		<!-- 引入aop支持 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>



		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.10.1</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
			<version>2.10.1</version>
		</dependency>



		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxws</artifactId>
			<version>3.1.6</version>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>javax.xml.messaging</groupId>-->
<!--			<artifactId>javax.xml.messaging-api</artifactId>-->
<!--			<version>1.0.3</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http</artifactId>
			<version>3.1.6</version>
		</dependency>


		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>

			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.searchbox/jest -->
		<!--springBoot 默认使用SpringData ElasticSearch模块进行操作-->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>

			<scope>test</scope>
		</dependency>

<!--
		<dependency> <groupId>org.slf4j</groupId> <artifactId>slf4j-api</artifactId>
			</dependency> <dependency> <groupId>org.slf4j</groupId> <artifactId>slf4j-log4j12</artifactId>
			</dependency>
-->

		<!-- okhttp3依赖 -->
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>3.9.0</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.24</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-engine-core</artifactId>
			<version>2.1</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>freemarker</groupId>
			<artifactId>freemarker</artifactId>
			<version>2.3.6</version>
		</dependency>




		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>
		</dependency>
<!--
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-log4j</artifactId>
                    <version>1.3.8.RELEASE</version>
                </dependency>
-->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>

		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>

		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
			<version>2.2.4.RELEASE</version>
		</dependency>

		<!-- <dependency> <groupId>commons-pool</groupId> <artifactId>commons-pool</artifactId>
			</dependency> -->

		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4</version>
			<classifier>jdk15</classifier>
		</dependency>





		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
			<version>2.3.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>


		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>2.0.16</version>
		</dependency>

		<dependency>
			<groupId>com.github.penggle</groupId>
			<artifactId>kaptcha</artifactId>
			<version>2.3.2</version>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.3.10</version>
		</dependency>

		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.1.0</version>
		</dependency>



		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-properties-migrator</artifactId>
		</dependency>

<!--
去掉swagger,提升加载效率
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.8.0</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.8.0</version>
		</dependency>
-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
			<version>2.2.2.RELEASE</version>
			<exclusions>

				<exclusion>
					<groupId>com.alibaba.nacos</groupId>
					<artifactId>nacos-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.alibaba.nacos</groupId>
			<artifactId>nacos-client</artifactId>
			<version>1.4.3</version>
		</dependency>



		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>



		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.9.0</version>
		</dependency>

		<!--

		-->
		<dependency>
			<groupId>ctg-mq</groupId>
			<artifactId>ctg-mq-api</artifactId>
			<version>${ctg-mq.verson}</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/ctg-mq/ctg-mq-api-2.4.1_P2.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>ctg-mq</groupId>
			<artifactId>ctg-mq-client</artifactId>
			<version>${ctg-mq.verson}</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/ctg-mq/ctg-mq-client-2.4.1_P2.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>ctg-mq</groupId>
			<artifactId>ctg-mq-common</artifactId>
			<version>${ctg-mq.verson}</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/ctg-mq/ctg-mq-common-2.4.1_P2.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>ctg-mq</groupId>
			<artifactId>ctg-mq-remoting</artifactId>
			<version>${ctg-mq.verson}</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/ctg-mq/ctg-mq-remoting-2.4.1_P2.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>ctg-mq</groupId>
			<artifactId>ctg-mq-srvutil</artifactId>
			<version>${ctg-mq.verson}</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/ctg-mq/ctg-mq-srvutil-2.4.1_P2.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>ctg-mq</groupId>
			<artifactId>ctg-mq-tools</artifactId>
			<version>${ctg-mq.verson}</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/ctg-mq/ctg-mq-tools-2.4.1_P2.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>ctg-da</groupId>
			<artifactId>ctg-da-client</artifactId>
			<version>0.0.1-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/da-client/da-client-0.0.1-SNAPSHOT.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>com.emc.vipr</groupId>
			<artifactId>aws-java-sdk</artifactId>
			<version>1.7.5</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.0.1</version>
		</dependency>

		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-client</artifactId>
		</dependency>

		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
		</dependency>
        <dependency>
            <groupId>com.luxsuen</groupId>
            <artifactId>jsonutil</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>

		<dependency>
			<groupId>com.vesoft</groupId>
			<artifactId>client</artifactId>
			<version>3.0.0</version>
		</dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
            <version>5.2.2.RELEASE</version>
        </dependency>

		<!-- EASY-EXCEL -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.3.3</version>
		</dependency>

		<!-- JSch SFTP客户端 -->
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>

		<dependency>
			<groupId>org.jfree</groupId>
			<artifactId>jfreechart</artifactId>
			<version>1.5.3</version>
		</dependency>
	</dependencies>


	<dependencyManagement>
		<dependencies>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>Finchley.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<!--
        <repositories>

            <repository>
                <id>spring-milestones</id>
                <name>Spring Milestones</name>
                <url>https://repo.spring.io/milestone</url>
                <snapshots>
                    <enabled>false</enabled>
                </snapshots>
            </repository>
        </repositories>
    -->
	<build>

		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>

				</includes>
				<filtering>true</filtering>
			</resource>

			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
			</resource>

			<resource>
				<directory>libs</directory>
				<includes>
					<include>**/*.jar</include>

				</includes>
				<filtering>false</filtering>
			</resource>
		</resources>


		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>8</source>
					<target>8</target>
					<encoding>utf8</encoding>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.24</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>

				<configuration>
					<layout>ZIP</layout>
					<fork>true</fork>
					<includeSystemScope>true</includeSystemScope>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>

			</plugin>

		</plugins>
	</build>
</project>
