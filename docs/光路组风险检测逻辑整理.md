# 光路组风险检测逻辑整理文档

## 概述

本文档详细整理了光路组风险检测系统中的所有检测逻辑，特别关注除了同光缆、同管道检测之外的特殊检测逻辑。

## 一、基础检测逻辑

### 1.1 光路组验证
- **无光路检测**：光路组为空时，结果为"无光路"
- **单光路检测**：光路组只有一条光路时，结果为"单光路"
- **单路由检测**：所有光路属于同一路由时，结果为"单路由"

### 1.2 基础问题检测
- **缺管道检测**：检测光路是否缺少管道段
- **同光缆检测**：检测多条光路是否共用同一光缆
- **同管道检测**：检测多条光路是否共用同一管道段

## 二、特殊检测逻辑

### 2.1 局内光路豁免检测

#### 检测条件
- 光路的起终点在同一局站（`a_site_id`等于`z_site_id`）
- 主要用于局内光路的风险豁免

#### 检测逻辑
```java
// 局内光路检测
for (JSONObject optRoad : optRoads) {
    String aSiteId = optRoad.getString("a_site_id");
    String zSiteId = optRoad.getString("z_site_id");
    
    if (ObjectUtil.isNotEmpty(aSiteId) && ObjectUtil.isNotEmpty(zSiteId) && 
        aSiteId.equals(zSiteId)) {
        indoorOpticalPaths.add(optRoad.getString("code"));
    }
}
```

#### 豁免规则
- 局内光路可以豁免缺管道问题
- 豁免后结果中会标记"局内光路"标签

### 2.2 局前井豁免检测

#### 检测条件
- 管道段与设备的距离小于等于500米
- 局前井数量不超过10个

#### 检测逻辑
```java
// 局前井检测算法
public void computeFrontHole(List<JSONObject> pipeSegmentList, List<JSONObject> deviceList) {
    for (JSONObject pipeSegment : pipeSegmentList) {
        Double a_pos_x = pipeSegment.getDouble("a_pos_x");
        Double a_pos_y = pipeSegment.getDouble("a_pos_y");
        Double z_pos_x = pipeSegment.getDouble("z_pos_x");
        Double z_pos_y = pipeSegment.getDouble("z_pos_y");
        
        boolean isFront = false;
        for (JSONObject device : deviceList) {
            // 计算管道段A端到设备的距离
            if (calculateDistance(a_pos_x, a_pos_y, device) <= 500) {
                isFront = true;
                break;
            }
            
            // 计算管道段Z端到设备的距离
            if (calculateDistance(z_pos_x, z_pos_y, device) <= 500) {
                isFront = true;
                break;
            }
        }
        
        pipeSegment.put("is_front", isFront);
    }
}
```

#### 豁免规则
- 局前井数量≤10个时，可以豁免同管道问题
- 豁免后结果中会标记"同局前井"标签
- 超过10个局前井不享受豁免

### 2.3 局内光缆豁免检测（待实现）

#### 检测条件
- 同光缆的起终点在同一局站内
- 主要用于局内光缆的风险豁免

#### 检测逻辑
```java
// TODO: 实现局内光缆豁免判断逻辑
// 需要判断同光缆是否在同一局站内
private String analyzeIndoorCableExemption(JSONObject topologyData) {
    // 待实现
    return null;
}
```

#### 豁免规则
- 局内光缆可以豁免同光缆问题
- 豁免后结果中会标记"局内光缆"标签

### 2.4 光路对路由号自动分配

#### 触发条件
- 专业为"数据"或"视频"
- 光路组元素数量大于2个

#### 分配逻辑
```java
// 路由号自动分配
if ((speciality.equals("数据")||speciality.equals("视频")) && 
    optGroupElements.size()>2) {
    optRoadPairService.opt_pair_route_no_analyse(optGroupElements,ds);
}
```

#### 分配规则
- 基于光路对分析结果自动分配路由号
- 保留用户手动设置的路由号
- 对未手动设置的光路进行自动分配
- 避免同一路由的光路生成光路对

### 2.5 隐患整改临时检测

#### 检测场景
- 用于隐患整改场景下的临时风险检测
- 不保存到数据库，直接返回结果

#### 检测逻辑
```java
// 构建临时光路组元素
private List<JSONObject> buildTempOptGroupElements(JSONObject request) {
    List<JSONObject> tempElements = new ArrayList<>();
    
    // 从请求中获取光路编码列表
    Object opticalPathsObj = request.get("opticalPaths");
    if (opticalPathsObj instanceof List) {
        List<?> opticalPaths = (List<?>) opticalPathsObj;
        
        for (int i = 0; i < opticalPaths.size(); i++) {
            // 构建临时光路组元素
            JSONObject element = new JSONObject();
            element.put("code", pathCode.trim());
            element.put("name", pathCode.trim());
            element.put("route_no", i + 1);
            element.put("group_id", request.getString("id"));
            element.put("attribute", "数据");
            element.put("state", "在用");
            tempElements.add(element);
        }
    }
    
    return tempElements;
}
```

#### 特点
- 使用传入的光路编码构建临时光路组
- 不查询历史记录
- 不保存检测结果
- 专门用于隐患整改场景

### 2.6 白名单机制

#### 白名单类型
- 管道段白名单：标记为忽略的管道段
- 光缆白名单：标记为忽略的光缆

#### 检测逻辑
```java
// 获取白名单
private Set<String> get_white_codes(JSONObject request){
    PageResponse<JSONObject> pageResponse = 
        custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_query(
            new JSONObject(), 100000, 1, 
            NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(request.getString("ds")));
    
    return pageResponse.getData().stream()
        .map(d -> d.getString("code"))
        .collect(Collectors.toSet());
}
```

#### 应用规则
- 白名单中的资源会被标记为"已忽略"
- 忽略的资源不计入风险统计

### 2.7 MSTP电路重复检测

#### 检测条件
- 同一光路上承载多个MSTP电路
- 主要用于检测资源复用情况

#### 检测逻辑
```java
// 检测承载多个MSTP电路的光路
List<JSONObject> sameoptRoads = optRoadList.stream()
    .filter(optRoad -> ObjectUtil.isNotEmpty(optRoad.getJSONArray("mstp_circuits")) && 
                     optRoad.getJSONArray("mstp_circuits").size() > 1)
    .collect(Collectors.toList());
```

#### 结果处理
- 标记存在资源复用的光路
- 用于资源管理和容量规划

### 2.8 局向光纤检测

#### 检测内容
- 检测局向光纤的占用情况
- 计算局向光纤的位置信息

#### 检测逻辑
```java
// 局向光纤检测
List<JSONObject> jxgxList = graphService.filterNodeByLabel(data, "局向光纤");
setupJXGQPos(jxgxList, deviceList);
jxgxList = jxgxList.stream()
    .filter(jx -> jx.getBoolean("empty"))
    .collect(Collectors.toList());
```

#### 应用场景
- 局向光纤资源管理
- 网络拓扑分析

## 三、豁免优先级

### 3.1 豁免优先级顺序
1. **局内光路豁免**（最高优先级）
2. **局前井豁免**
3. **局内光缆豁免**（待实现）

### 3.2 豁免应用规则
```java
// 豁免优先级处理
private String applyExemptions() {
    // 1. 局内光路豁免（最高优先级）
    if (!indoorOpticalPaths.isEmpty()) {
        return "局内光路";
    }
    
    // 2. 局前井豁免
    if (isFrontWell && count_frontPipeSegmentList <= 10) {
        return "局前井数量" + count_frontPipeSegmentList + "个，在豁免范围内(≤10)";
    }
    
    // 3. 局内光缆豁免（待实现）
    if (hasIndoorCableExemption) {
        return "局内光缆";
    }
    
    return null;
}
```

## 四、结果计算逻辑

### 4.1 结果类型定义
- **正常**：无任何风险问题
- **单光路**：只有一条光路
- **单路由**：所有光路同路由
- **缺管道**：光路缺少管道段
- **同光缆**：多条光路共用光缆
- **同管道**：多条光路共用管道段
- **局内光路**：起终点在同一局站
- **同局前井**：管道段在局前井范围内

### 4.2 结果组合规则
- 多个问题用"+"连接（如"缺管道+同光缆"）
- 豁免标签单独显示
- 优先显示基础问题，再显示豁免标签

### 4.3 风险等级映射
```java
// 风险等级映射
public String getRiskLevel(String result) {
    if (result.contains("同管道") || result.contains("同光缆")) {
        return "high";
    } else if (result.contains("缺管道")) {
        return "medium";
    } else if (result.contains("单路由")) {
        return "low";
    } else {
        return "none";
    }
}
```

## 五、特殊场景处理

### 5.1 地市代码转换
- 支持地市代码到中文地市名称的转换
- 用于处理不同地区的风险检测

### 5.2 路由号处理
- 支持用户手动设置路由号
- 自动分配路由号时保留手动设置
- 清空未在路由分析结果中的光路的路由号

### 5.3 历史记录处理
- 保存风险检测结果快照
- 记录检测结果变化历史
- 支持整改完成后的状态更新

## 六、性能优化

### 6.1 结果缓存
- 支持风险检测结果的缓存
- 缓存时间30分钟
- 避免重复计算

### 6.2 批量处理
- 支持批量光路对分析
- 分批处理避免内存溢出
- 并行处理提升性能

## 七、原子化问题明细

### 7.1 问题明细结构
```java
{
    "problem_type": "SAME_PIPE",
    "resource_type": "PIPE_SEGMENT", 
    "resource_id": "pipe_id",
    "resource_code": "pipe_code",
    "resource_name": "pipe_name",
    "optical_paths": "[\"opt1\", \"opt2\"]",
    "severity": 1,
    "description": "光路1和光路2共用管道段",
    "is_front_well": false,
    "is_indoor_cable": false,
    "is_indoor_optical": false,
    "front_well_count": 0,
    "exemption_reason": null
}
```

### 7.2 明细生成规则
- 每个问题生成独立的明细记录
- 包含完整的豁免信息
- 支持问题分类和统计

## 八、总结

光路组风险检测系统的特殊检测逻辑主要包括：

1. **局内光路豁免**：起终点在同一局站的光路可豁免缺管道问题
2. **局前井豁免**：距离设备500米内且数量≤10个的管道段可豁免同管道问题
3. **局内光缆豁免**：同一局站内的光缆可豁免同光缆问题（待实现）
4. **路由号自动分配**：数据/视频专业且光路>2个时自动分配路由号
5. **隐患整改临时检测**：支持临时光路组的风险检测
6. **白名单机制**：支持管道段和光缆的忽略处理
7. **MSTP电路重复检测**：检测同一光路上的多电路承载
8. **局向光纤检测**：检测局向光纤占用情况

这些特殊检测逻辑使系统能够更准确地进行风险分析，避免误报，提供更精准的风险评估结果。









路由号相同的几个光路是什么处理逻辑?

⏺ 根据代码分析，路由号相同的光路处理逻辑如下：

  🔄 路由号相同的处理逻辑

  1. 光路对生成时的处理

  在 OptRoadPairServiceImpl.java:275 中：
  if (aRouteNo.equals(zRouteNo)) continue; // 如果2条光路属于同一个路由

  处理规则：
  - 路由号相同的光路不会生成光路对
  - 避免同路由的光路进行重复性风险检测

  2. 路由号自动分配逻辑

  在 OptRoadPairServiceImpl.java:505-579 中的 opt_pair_route_no_analyse 方法：

  触发条件

  - 专业为"数据"或"视频"
  - 光路组元素数量大于2个

  分配规则

  // 1. 记录用户手动设置的路由号
  Map<String, String> userDefinedRouteNos = new HashMap<>();
  if (ObjectUtil.isNotEmpty(routeNo) && !routeNo.equals(code)) {
      userDefinedRouteNos.put(code, routeNo);
  }

  // 2. 基于网络拓扑分析结果分配路由号
  List<List<JSONObject>> route_no_result = getRoute_Num(
      result.getJSONArray("samePipeSegments_d"),
      result.getJSONArray("sameCables_d"),
      result.getJSONArray("optRoads")
  );

  // 3. 为每个路由组分配序号
  for(int i = 0; i < route_no_result.size(); i++){
      for(JSONObject route : route_no_result.get(i)){
          if (userDefinedRouteNos.containsKey(code)) {
              // 保留用户手动设置的路由号
              currentRoute.put("route_no", userRouteNo);
          } else {
              // 自动分配路由号：i+1
              currentRoute.put("route_no", i+1);
          }
      }
  }

  3. 路由号相同的影响

  风险检测方面

  - 不生成光路对：同路由光路间不进行风险检测
  - 避免重复检测：减少不必要的计算量
  - 结果准确性：确保只对不同路由的光路进行风险分析

  网络拓扑分析

  - 路由分组：基于同管道、同光缆关系将光路分组
  - 路由数量：最终的路由数量影响风险等级判断
  - 单路由检测：如果所有光路最终归为同一路由，结果为"单路由"

  4. 路由号分配的核心算法

  在 getRoute_Num 方法中（OptRoadPairServiceImpl.java:401-409）：
  public List<List<JSONObject>> getRoute_Num(List<JSONObject> samePipeSegments_d,
                                           List<JSONObject> sameCables_d,
                                           List<JSONObject> optList){
      List<List<JSONObject>> result = new ArrayList<>();

      // 1. 基于同管道关系分组
      intoR_common(samePipeSegments_d, result);
    
      // 2. 基于同光缆关系分组
      intoR_common(sameCables_d, result);
    
      return result;
  }

  分组逻辑：
  - 如果两个光路共用同一段管道，则归为同一路由
  - 如果两个光路共用同一根光缆，则归为同一路由
  - 通过传递性关系，将相关联的光路归为同一路由组

  5. 实际应用场景

  示例：
  - 光路A和光路B共用管道段1 → 同路由
  - 光路B和光路C共用光缆X → 同路由
  - 最终结果：光路A、B、C归为同一路由组，分配相同的路由号

  业务意义：
  - 路由号相同表示这些光路在物理路径上存在共享资源
  - 共享资源的风险需要在路由层面进行统一管理
  - 避免同一路由内的光路被误判为"双路由"保护