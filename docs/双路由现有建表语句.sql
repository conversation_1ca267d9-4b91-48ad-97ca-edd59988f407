
准实时库
pm_opt_road_group --组
pm_opt_road_group_element --元素
pm_risk_check --结果
pm_risk_check_his --历史结果


建表语句：
CREATE TABLE "wlyy_nj_sch"."pm_opt_road_group" (
"id" numeric(24) NOT NULL DEFAULT nextval('fiber_pair_m_upload_id_seq'::regclass),
"name" varchar(1000) COLLATE "pg_catalog"."default",
"code" varchar(1000) COLLATE "pg_catalog"."default" NOT NULL,
"create_date" timestamp(6),
"create_op" varchar(1000) COLLATE "pg_catalog"."default",
"area_name" varchar(1000) COLLATE "pg_catalog"."default",
"note" varchar(10000) COLLATE "pg_catalog"."default",
"source_type_id" numeric(24),
"state_id" numeric(24),
"source_id" numeric(24),
"route_no" varchar(200) COLLATE "pg_catalog"."default",
"leaf_region" varchar(1000) COLLATE "pg_catalog"."default",
"speciality" varchar(1000) COLLATE "pg_catalog"."default",
"customer" varchar(1000) COLLATE "pg_catalog"."default",
"maintainers" varchar(1000) COLLATE "pg_catalog"."default",
"routerid" varchar(1000) COLLATE "pg_catalog"."default",
CONSTRAINT "pm_opt_road_group_pkey" PRIMARY KEY ("id"),
CONSTRAINT "code" UNIQUE ("code")
)
;
ALTER TABLE "wlyy_nj_sch"."pm_opt_road_group"
OWNER TO "wlyy_nj";
CREATE INDEX "idx_pm_opt_road_group_code" ON "wlyy_nj_sch"."pm_opt_road_group" USING btree (
"code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_pm_opt_road_group_name" ON "wlyy_nj_sch"."pm_opt_road_group" USING btree (
"name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);


CREATE TABLE "wlyy_nj_sch"."pm_opt_road_group_element" (
"id" numeric(24) NOT NULL,
"group_id" numeric(24),
"code" varchar(1000) COLLATE "pg_catalog"."default",
"name" varchar(1000) COLLATE "pg_catalog"."default",
"route_no" varchar(200) COLLATE "pg_catalog"."default",
"attribute" varchar(10000) COLLATE "pg_catalog"."default",
CONSTRAINT "pm_opt_road_group_element_pkey" PRIMARY KEY ("id")
)
;
ALTER TABLE "wlyy_nj_sch"."pm_opt_road_group_element"
OWNER TO "wlyy_nj";
CREATE INDEX "idx_pm_opt_road_group_element_group" ON "wlyy_nj_sch"."pm_opt_road_group_element" USING btree (
"group_id" "pg_catalog"."numeric_ops" ASC NULLS LAST
);
CREATE UNIQUE INDEX "uidx_pm_opt_road_group_element" ON "wlyy_nj_sch"."pm_opt_road_group_element" USING btree (
"code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
"group_id" "pg_catalog"."numeric_ops" ASC NULLS LAST,
"route_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);



CREATE TABLE "wlyy_nj_sch"."pm_risk_check" (
"id" numeric(24),
"object_type" varchar(1000) COLLATE "pg_catalog"."default",
"check_result" varchar(10000) COLLATE "pg_catalog"."default",
"previous_check_result" varchar(10000) COLLATE "pg_catalog"."default",
"deal_result" varchar(10000) COLLATE "pg_catalog"."default",
"exception_info" text COLLATE "pg_catalog"."default",
"create_time" timestamp(6),
"deal_time" timestamp(6),
"confirm_status" varchar(10000) COLLATE "pg_catalog"."default",
"confirm_op" varchar(10000) COLLATE "pg_catalog"."default",
"confirm_time" timestamp(6),
"snapshot" text COLLATE "pg_catalog"."default",
"object_id" numeric(24),
"check_time" timestamp(6),
CONSTRAINT "obj" UNIQUE ("object_type", "object_id"),
CONSTRAINT "object" UNIQUE ("object_id", "object_type")
)
;
ALTER TABLE "wlyy_nj_sch"."pm_risk_check"
OWNER TO "wlyy_nj";
CREATE INDEX "idx_pm_risk_check_id" ON "wlyy_nj_sch"."pm_risk_check" USING btree (
"id" "pg_catalog"."numeric_ops" ASC NULLS LAST
);
CREATE INDEX "pm_risk_check_his_obj" ON "wlyy_nj_sch"."pm_risk_check" USING btree (
"object_id" "pg_catalog"."numeric_ops" ASC NULLS LAST,
"object_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "pm_risk_check_obj" ON "wlyy_nj_sch"."pm_risk_check" USING btree (
"object_id" "pg_catalog"."numeric_ops" ASC NULLS LAST,
"object_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);


CREATE TABLE "wlyy_nj_sch"."pm_risk_check_his" (
"id" numeric(24),
"object_type" varchar(1000) COLLATE "pg_catalog"."default",
"check_result" varchar(10000) COLLATE "pg_catalog"."default",
"previous_check_result" varchar(10000) COLLATE "pg_catalog"."default",
"deal_result" varchar(10000) COLLATE "pg_catalog"."default",
"exception_info" text COLLATE "pg_catalog"."default",
"create_time" timestamp(6),
"deal_time" timestamp(6),
"confirm_status" varchar(10000) COLLATE "pg_catalog"."default",
"confirm_op" varchar(10000) COLLATE "pg_catalog"."default",
"confirm_time" timestamp(6),
"snapshot" text COLLATE "pg_catalog"."default",
"his_date" timestamp(6),
"object_id" numeric(24)
)
;
ALTER TABLE "wlyy_nj_sch"."pm_risk_check_his"
OWNER TO "wlyy_nj";
CREATE INDEX "idx_pm_risk_check_his_id" ON "wlyy_nj_sch"."pm_risk_check_his" USING btree (
"id" "pg_catalog"."numeric_ops" ASC NULLS LAST
);

