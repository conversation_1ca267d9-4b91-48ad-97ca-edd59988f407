-- 光路组检测资源问题清单表设计
-- 作者：Claude AI
-- 创建时间：2025-08-01
-- 描述：用于存储光路组风险检测中发现的具体资源问题，支持豁免管理和操作追踪

-- ================================
-- 光路组检测资源问题清单表
-- ================================
CREATE TABLE "pm_opt_group_resource_problem" (
    -- 主键和基本信息
    "id" numeric(24) NOT NULL DEFAULT nextval('fiber_pair_m_upload_id_seq'::regclass),
    "group_id" numeric(24) NOT NULL,                    -- 关联光路组ID
    "check_id" numeric(24),                           -- 关联检测记录ID (pm_risk_check.id)
    
    -- 资源信息
    "resource_type" varchar(50) NOT NULL,             -- 资源类型：光路/光缆/管道段
    "resource_id" numeric(24),                        -- 资源ID
    "resource_code" varchar(1000) NOT NULL,           -- 资源编码
    "resource_name" varchar(1000),                    -- 资源名称
    
    -- 问题信息
    "problem_type" varchar(50) NOT NULL,              -- 问题类型：缺管道/同光缆/同管道/同光路号
    "problem_description" text,                        -- 问题描述
    
    -- 关联光路信息（用于记录与哪个光路形成问题）
    "related_optical_path_id" numeric(24),            -- 关联光路ID
    "related_optical_path_code" varchar(1000),       -- 关联光路编码
    "related_optical_paths" text,                    -- 关联光路列表JSON格式，存储多个关联光路
    
    -- 豁免信息
    "is_exemption" boolean DEFAULT false,             -- 是否豁免
    "exemption_type" varchar(50),                     -- 豁免类型：局内光路/局内光缆/局前井/白名单
    "exemption_reason" varchar(2000),                 -- 豁免原因描述
    
    -- 白名单关联
    "white_list_id" numeric(24),                      -- 关联白名单记录ID
    "white_list_code" varchar(1000),                  -- 白名单编码
    
    -- 忽略标记
    "is_ignore" boolean DEFAULT false,                 -- 是否忽略
    "ignore_reason" varchar(2000),                     -- 忽略原因
    
    -- 处理状态
    "deal_status" varchar(50) DEFAULT '待处理',      -- 处理状态：待处理/处理中/已解决/已忽略
    "deal_result" varchar(1000),                      -- 处理结果
    "deal_remark" varchar(5000),                      -- 处理备注
    
    -- 问题生效状态
    "is_active" boolean DEFAULT true,                 -- 问题是否生效
    "inactive_reason" varchar(1000),                 -- 失效原因：重新检测后不存在/已整治完成/人工失效
    
    -- 整治单关联
    "rectification_id" numeric(24),                   -- 关联整治单ID
    "rectification_code" varchar(1000),               -- 整治单编码
    "rectification_status" varchar(50),               -- 整治单状态
    
    -- 操作信息
    "create_op" varchar(1000),                        -- 创建人
    "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "update_op" varchar(1000),                        -- 更新人
    "update_time" timestamp(6),                       -- 更新时间
    "confirm_op" varchar(1000),                       -- 确认人
    "confirm_time" timestamp(6),                      -- 确认时间
    
    -- 扩展字段
    "is_front_well" boolean DEFAULT false,            -- 是否局前井（管道段专用）
    "is_indoor_optical" boolean DEFAULT false,        -- 是否局内光路（光路专用）
    "is_indoor_cable" boolean DEFAULT false,           -- 是否局内光缆（光缆专用）
    
    -- 约束
    CONSTRAINT "pm_opt_group_resource_problem_pkey" PRIMARY KEY ("id")
);

-- ================================
-- 表注释
-- ================================
COMMENT ON TABLE "pm_opt_group_resource_problem" IS '光路组检测资源问题清单表';
COMMENT ON COLUMN "pm_opt_group_resource_problem"."group_id" IS '关联光路组ID';
COMMENT ON COLUMN "pm_opt_group_resource_problem"."check_id" IS '关联检测记录ID';
COMMENT ON COLUMN "pm_opt_group_resource_problem"."resource_type" IS '资源类型：光路/光缆/管道段';
COMMENT ON COLUMN "pm_opt_group_resource_problem"."problem_type" IS '问题类型：缺管道/同光缆/同管道/同光路号';
COMMENT ON COLUMN "pm_opt_group_resource_problem"."is_exemption" IS '是否豁免';
COMMENT ON COLUMN "pm_opt_group_resource_problem"."exemption_type" IS '豁免类型：局内光路/局内光缆/局前井/白名单';
COMMENT ON COLUMN "pm_opt_group_resource_problem"."deal_status" IS '处理状态：待处理/处理中/已解决/已忽略';

-- ================================
-- 索引设计
-- ================================

-- 主键索引
CREATE INDEX "idx_pm_opt_group_resource_problem_id" ON "pm_opt_group_resource_problem" USING btree (
    "id" "pg_catalog"."numeric_ops" ASC NULLS LAST
);

-- 光路组关联索引
CREATE INDEX "idx_pm_opt_group_resource_problem_group" ON "pm_opt_group_resource_problem" USING btree (
    "group_id" "pg_catalog"."numeric_ops" ASC NULLS LAST
);

-- 检测记录关联索引
CREATE INDEX "idx_pm_opt_group_resource_problem_check" ON "pm_opt_group_resource_problem" USING btree (
    "check_id" "pg_catalog"."numeric_ops" ASC NULLS LAST
);

-- 资源类型和编码索引
CREATE INDEX "idx_pm_opt_group_resource_problem_resource" ON "pm_opt_group_resource_problem" USING btree (
    "resource_type" "pg_catalog"."text_ops" ASC NULLS LAST,
    "resource_code" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- 问题类型索引
CREATE INDEX "idx_pm_opt_group_resource_problem_type" ON "pm_opt_group_resource_problem" USING btree (
    "problem_type" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- 豁免状态索引
CREATE INDEX "idx_pm_opt_group_resource_problem_exemption" ON "pm_opt_group_resource_problem" USING btree (
    "is_exemption" "pg_catalog"."bool_ops" ASC NULLS LAST,
    "exemption_type" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- 处理状态索引
CREATE INDEX "idx_pm_opt_group_resource_problem_status" ON "pm_opt_group_resource_problem" USING btree (
    "deal_status" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- 创建时间索引
CREATE INDEX "idx_pm_opt_group_resource_problem_create_time" ON "pm_opt_group_resource_problem" USING btree (
    "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- 复合索引：光路组+问题类型
CREATE INDEX "idx_pm_opt_group_resource_problem_group_type" ON "pm_opt_group_resource_problem" USING btree (
    "group_id" "pg_catalog"."numeric_ops" ASC NULLS LAST,
    "problem_type" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- 复合索引：资源类型+豁免状态
CREATE INDEX "idx_pm_opt_group_resource_problem_resource_exemption" ON "pm_opt_group_resource_problem" USING btree (
    "resource_type" "pg_catalog"."text_ops" ASC NULLS LAST,
    "is_exemption" "pg_catalog"."bool_ops" ASC NULLS LAST
);

-- ================================
-- 问题类型枚举说明
-- ================================

-- 问题类型 (problem_type):
-- - 缺管道：光路缺少管道段
-- - 同光缆：多条光路共用同一光缆
-- - 同管道：多条光路共用同一管道段
-- - 同光路号：多条光路使用相同的光路号

-- 资源类型 (resource_type):
-- - 光路：光路资源
-- - 光缆：光缆资源
-- - 管道段：管道段资源

-- 豁免类型 (exemption_type):
-- - 局内光路：起终点在同一局站的光路豁免
-- - 局内光缆：同一局站内的光缆豁免
-- - 局前井：距离设备500米内的管道段豁免
-- - 白名单：白名单中的资源豁免

-- 处理状态 (deal_status):
-- - 待处理：新发现的问题，等待处理
-- - 处理中：正在处理中的问题
-- - 已解决：已处理完成的问题
-- - 已忽略：已标记忽略的问题

-- 失效原因 (inactive_reason):
-- - 重新检测后不存在：重新检测后问题已消失
-- - 已整治完成：通过整治单整治完成
-- - 人工失效：人工标记为失效

-- 整治单状态 (rectification_status):
-- - 待派单：整治单待派发
-- - 已派单：整治单已派发
-- - 整治中：正在整治中
-- - 已完成：整治已完成
-- - 已关闭：整治单已关闭

-- ================================
-- 问题生效状态索引
-- ================================

-- 问题生效状态索引
CREATE INDEX "idx_pm_opt_group_resource_problem_active" ON "pm_opt_group_resource_problem" USING btree (
    "is_active" "pg_catalog"."bool_ops" ASC NULLS LAST,
    "inactive_reason" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- 整治单关联索引
CREATE INDEX "idx_pm_opt_group_resource_problem_rectification" ON "pm_opt_group_resource_problem" USING btree (
    "rectification_id" "pg_catalog"."numeric_ops" ASC NULLS LAST,
    "rectification_status" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ================================
-- 问题类型枚举说明
-- ================================

-- 问题类型 (problem_type):
-- - MISSING_PIPE: 缺管道（光路问题）
-- - SAME_CABLE: 同光缆（光缆问题）
-- - SAME_PIPE: 同管道（管道段问题）
-- - SAME_OPTICAL_CODE: 同光路号（光路问题）

-- 资源类型 (resource_type):
-- - OPTICAL_PATH: 光路
-- - CABLE: 光缆
-- - PIPE_SEGMENT: 管道段

-- 豁免类型 (exemption_type):
-- - INDOOR_OPTICAL: 局内光路豁免
-- - INDOOR_CABLE: 局内光缆豁免
-- - FRONT_WELL: 局前井豁免
-- - WHITE_LIST: 白名单豁免

-- 处理状态 (deal_status):
-- - pending: 待处理
-- - processing: 处理中
-- - resolved: 已解决
-- - ignored: 已忽略