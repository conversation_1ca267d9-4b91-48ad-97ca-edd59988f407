# 光路路由号保留修复说明

## 问题描述

在光路保护组管理页面 `/nrm/res-app/risk-manage/opt_road_group_management` 中，用户手动修改光路的路由号后，虽然可以修改成功，但当对这个光路进行单独的风险检测后，路由号会还原回之前的值。

## 问题根因分析

### 触发条件
当满足以下条件时，风险检测会自动重新分配路由号：
1. 光路保护组的专业类型（speciality）为"数据"或"视频"
2. 光路组中的光路数量大于2条

### 问题代码位置
`OptGroupServiceImpl.opt_group_elements_analyze()` 方法中：
```java
String speciality = Optional.ofNullable(optGroup.getString("speciality")).orElse("");
if((speciality.equals("数据")||speciality.equals("视频")) && optGroupElements.size()>2){
    optRoadPairService.opt_pair_route_no_analyse(optGroupElements,ds);  // 这里会重新分配路由号
}
```

### 核心问题
`OptRoadPairServiceImpl.opt_pair_route_no_analyse()` 方法会根据光路的物理路径分析结果，自动重新分配路由号（从1开始递增），完全忽略了用户手动设置的路由号。

原始问题代码：
```java
for(int i =0;i<route_no_result.size();i++){
    for(JSONObject route : route_no_result.get(i)){
        JSONObject currentRoute = optGroupElementsMap.get(route.getString("code"));
        if(ObjectUtil.isNotNull(currentRoute)){
            String code = currentRoute.getString("code");
            currentRoute.put("route_no", i+1);  // 直接覆盖，不考虑用户手动设置
            currentRoute.put("opt_code", code);
            optGroupDao.pm_opt_road_group_element_update(currentRoute,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
        }
    }
}
```

## 修复方案

### 解决思路
在自动分配路由号之前，先识别和保存用户手动设置的路由号，然后在分配过程中跳过这些已手动设置的光路。

### 用户手动设置路由号的识别规则
路由号被认为是用户手动设置的条件：
1. 路由号不为空（不是null或空字符串）
2. 路由号不等于光路编码（系统默认会将路由号设置为光路编码）

### 修复后的逻辑流程
1. **识别阶段**：遍历所有光路元素，识别用户手动设置的路由号并记录
2. **分析阶段**：进行正常的路径分析和路由分组
3. **分配阶段**：
   - 对于有用户手动设置路由号的光路：保留原有路由号
   - 对于未手动设置的光路：进行自动分配
4. **更新阶段**：将结果更新到数据库

## 修复代码

### 主要修改文件
`nrm-graph-rest/src/main/java/com/telecom/nrm/service/impl/OptRoadPairServiceImpl.java`

### 关键修改点

1. **添加用户手动路由号识别逻辑**：
```java
// 记录用户手动设置的路由号，避免被自动分配覆盖
Map<String, String> userDefinedRouteNos = new HashMap<>();
optGroupElements.forEach(element -> {
    String code = element.getString("code");
    String routeNo = element.getString("route_no");
    // 如果路由号不为空且不等于光路编码，则认为是用户手动设置的
    if (ObjectUtil.isNotEmpty(routeNo) && !routeNo.equals(code)) {
        userDefinedRouteNos.put(code, routeNo);
        log.info("检测到用户手动设置的路由号: 光路编码={}, 路由号={}", code, routeNo);
    }
});
```

2. **修改路由号分配逻辑**：
```java
// 检查是否有用户手动设置的路由号
if (userDefinedRouteNos.containsKey(code)) {
    // 保留用户手动设置的路由号，不进行自动分配
    String userRouteNo = userDefinedRouteNos.get(code);
    currentRoute.put("route_no", userRouteNo);
    log.info("保留用户手动设置的路由号: 光路编码={}, 路由号={}", code, userRouteNo);
} else {
    // 只对未手动设置的光路进行自动路由号分配
    currentRoute.put("route_no", i+1);
    log.info("自动分配路由号: 光路编码={}, 路由号={}", code, i+1);
}
```

3. **处理未在分析结果中的光路**：
```java
// 对于未在路由分析结果中的光路，也要检查是否有用户手动设置的路由号
if (userDefinedRouteNos.containsKey(k)) {
    // 保留用户手动设置的路由号
    String userRouteNo = userDefinedRouteNos.get(k);
    v.put("route_no", userRouteNo);
    log.info("保留未在路由分析结果中的光路的用户手动路由号: 光路编码={}, 路由号={}", k, userRouteNo);
} else {
    // 对于未手动设置的光路，设置为null
    v.put("route_no", null);
    log.info("清空未在路由分析结果中的光路的自动路由号: 光路编码={}", k);
}
```

## 测试验证

### 测试场景
1. **保留用户手动设置的路由号**：用户将某光路的路由号手动设置为"5"，风险检测后应该保持为"5"
2. **自动分配未设置的路由号**：未设置路由号的光路应该按照分析结果自动分配
3. **处理默认路由号**：路由号等于光路编码的情况应该被视为未设置，进行自动分配

### 测试文件
创建了测试文件 `OptRoadPairServiceTest.java` 来验证修复逻辑。

## 影响范围

### 直接影响
- 光路保护组管理页面的风险检测功能
- 专业类型为"数据"或"视频"且光路数量>2的保护组

### 间接影响
- 提升用户体验，避免手动设置被意外覆盖
- 保持数据一致性，用户设置的路由号得到保留

## 部署注意事项

1. **备份数据**：建议在部署前备份相关数据表
2. **测试验证**：在测试环境充分验证修复效果
3. **日志监控**：部署后关注相关日志，确认修复正常工作
4. **用户通知**：可以通知用户该问题已修复

## 后续优化建议

1. **增加用户界面提示**：在前端界面提示用户哪些路由号是手动设置的
2. **添加路由号锁定功能**：允许用户明确锁定某些路由号，防止任何自动修改
3. **增加操作日志**：记录路由号的修改历史，便于问题追踪
