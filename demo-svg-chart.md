# SVG 矢量图表功能演示

## 功能概述

现在 `ChartController` 的 `generateChart` 方法已经支持生成 SVG 矢量图，具有以下特点：

1. **默认格式改为 SVG**: 现在默认生成 SVG 矢量图而不是 PNG 位图
2. **矢量图优势**: SVG 图表可以无损缩放，文件更小，支持文本选择
3. **向后兼容**: 仍然支持 PNG、JPG、GIF 等位图格式

## API 使用方法

### 生成 SVG 矢量图（默认）
```
GET /api/chart/generate?data={base64_encoded_chart_data}
GET /api/chart/generate?data={base64_encoded_chart_data}&format=svg
```

### 生成位图格式
```
GET /api/chart/generate?data={base64_encoded_chart_data}&format=png
GET /api/chart/generate?data={base64_encoded_chart_data}&format=jpg
GET /api/chart/generate?data={base64_encoded_chart_data}&format=gif
```

## 响应格式

- **SVG 格式**: 返回 `image/svg+xml` 类型的字符串内容
- **位图格式**: 返回对应的 `image/png`、`image/jpeg`、`image/gif` 类型的字节数组

## 示例图表数据

```json
{
  "type": "bar",
  "title": "示例柱状图",
  "xAxisLabel": "类别",
  "yAxisLabel": "数值",
  "width": 800,
  "height": 600,
  "series": [
    {
      "name": "系列1",
      "data": [10, 20, 30, 40, 50],
      "labels": ["A", "B", "C", "D", "E"],
      "color": "#FF4500"
    },
    {
      "name": "系列2",
      "data": [15, 25, 35, 45, 55],
      "labels": ["A", "B", "C", "D", "E"],
      "color": "#4169E1"
    }
  ]
}
```

## 技术实现

1. **依赖库**: 添加了 JFreeSVG 3.4 依赖
2. **服务层**: 在 `ChartService` 接口中添加了 `generateChartSVG` 方法
3. **实现层**: 在 `ChartServiceImpl` 中使用 `SVGGraphics2D` 生成 SVG 内容
4. **控制器层**: 修改 `ChartController` 支持根据 format 参数返回不同格式

## 测试覆盖

已添加完整的单元测试，包括：
- SVG 柱状图生成测试
- SVG 饼图生成测试
- SVG 折线图生成测试
- 异常情况测试

所有测试均通过验证。

## 中文字体支持

### 问题描述
在生成 SVG 图表时，中文字符可能显示为问号（?），这是因为 SVG 渲染时没有正确的中文字体支持。

### 解决方案
已实现完整的中文字体支持机制：

#### 1. 智能字体检测
系统会自动检测并使用系统中可用的中文字体：
- **Windows**: Microsoft YaHei, SimHei, SimSun
- **macOS**: PingFang SC, Hiragino Sans GB, STHeiti
- **Linux**: WenQuanYi Micro Hei, Noto Sans CJK SC

#### 2. 字体映射器
实现了 `ChineseFontMapper` 类，将 Java 字体正确映射到 SVG 字体：
```java
private static class ChineseFontMapper implements FontMapper {
    @Override
    public String mapFont(String family) {
        // 提供字体回退链，确保中文正确显示
        return family + ", Microsoft YaHei, PingFang SC, Hiragino Sans GB, SimHei, sans-serif";
    }
}
```

#### 3. 全面字体设置
为图表的所有文本元素设置中文字体：
- 图表标题
- 轴标签
- 刻度标签
- 图例文字
- 数据标签

### 测试验证
添加了专门的中文字体测试用例 `testChineseFontInSVG()`，验证：
- 中文字符正确显示
- SVG 包含正确的字体回退设置
- 所有中文文本元素都有字体支持

### 使用示例
现在可以正常使用包含中文的图表数据：
```json
{
  "type": "line",
  "title": "2025年1-4月首要污染物连接趋势",
  "xAxisLabel": "月份",
  "yAxisLabel": "连接率(%)",
  "series": [{
    "name": "连接率",
    "data": [33.61, 62.04, 19.13, 32.08],
    "labels": ["2025-01", "2025-02", "2025-03", "2025-04"]
  }]
}
```

生成的 SVG 将正确显示所有中文字符，不再出现问号问题。
